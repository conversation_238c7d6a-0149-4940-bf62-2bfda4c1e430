<?php

$html = '';
$subnav = '';
$mblnav = '';

//Display awards
if(!empty($awards)){
	
	//Awards navigation
	$mblnav .= '<select class="select" id="nav-menu" onchange="window.location=\'' .$_sitepages['award-winners']['page_url']. '\'+this.value;">
	<option value="">' .$award_year. ' Award Winners</option>';
	$subnav .= '<div id="award_winners">';
	$subnav .= '<nav id="sub-navigation" class="f_left">
		<h4>Award Winners</h4>
	
		<ul>';
			foreach($awards as $nav){
				$mblnav .= '<option value="' .$nav['page'].'-'.$nav['award_id']. '/"' .(isset($award_id) && $award_id == $nav['award_id'] ? ' selected' : ''). '>' .$nav['name']. '</option>';
				$subnav .= '<li><a href="' .$_sitepages['award-winners']['page_url'].$nav['page'].'-'.$nav['award_id']. '/">' .$nav['name']. '</a></li>';
			}
		$mblnav .= '</select><br>';
		$subnav .= '</ul>
	</nav>';
	
	//Winners panel
	// $page['page_panels']['winners'] = array(
	// 	'panel_id' => 'winners',
	// 	'panel_type' => 'standard',
	// 	'title' => '',
	// 	'show_title' => true,
	// 	'content' => (isset($award_id) && $award_id == '1' ? '' : $subnav)
	// 	'subnav' => (isset($award_id) && $award_id == '1' ? '' : $subnav) //Don't display subnav on lifetime achievement page
	// );

	// $page['page_panels'][$panel_id]['content'] = $subnav;

	//Current winners page
	if(PAGE_ID == $_sitepages['award-winners']['page_id']){
		
		$html .= '<div class="center">';
		$html .= '<h3>' .$award_year. ' Award Winners</h3>';
		$html .= '<div class="winners_list">';
		foreach($awards as $award){
			if(array_key_exists($award['award_id'], $current_winners) && !empty($current_winners[$award['award_id']])){
				foreach($current_winners[$award['award_id']] as $winner){
					// echo'<pre>';
					// print_r($winner);
					$html .= format_personnel($winner['first_name'], $winner['last_name'], $award['name'], $page['page_url'].$award['page'].'-'.$award['award_id'].'/', $winner['image']);
				}
			}
			if(!empty($award['sub_categories'])){
				foreach($award['sub_categories'] as $subcat){
					if(array_key_exists($subcat['award_id'], $current_winners) && !empty($current_winners[$subcat['award_id']])){
						foreach($current_winners[$subcat['award_id']] as $winner){
							$html .= format_personnel($winner['first_name'], $winner['last_name'], $award['name'].' - '.$subcat['name'], $page['page_url'].$award['page'].'-'.$award['award_id'].'/', $winner['image']);
						}
					}
				}
			}
		}
		$html .= '</div>';
		$html .= '</div>';
	}
	

	//Selected award page
	if(PARENT_ID == $_sitepages['award-winners']['page_id'] && PAGE_ID == '' && !empty($award)){
		$has_winners = false;
		$curr_winners = (array_key_exists($award['award_id'], $current_winners) ? $current_winners[$award['award_id']] : NULL);
		$past_winners = (array_key_exists($award['award_id'], $award_winners) ? $award_winners[$award['award_id']] : NULL);
		
		$html .= '<div class="center">';
		if(!empty($curr_winners) || !empty($past_winners)){
			$html .= '<h3>' .$award['name']. '</h3>';
		}
		$html .= '<div class="winners_list">';
		
		
		//Current winners
		if(!empty($curr_winners)){
			$has_winners = true;
			foreach($curr_winners as $winner){
				if(trim($winner['description']) != ''){
					$html .= '<table cellpadding="10" cellspacing="0" border="0" class="personnel-tbl">
					<tr>';
						$html .= '<td valign="top" class="nobg center" width="250px">' .format_personnel($winner['first_name'], $winner['last_name'], $winner['year'].' '.$winner['type'], $winner['profile_url'], $winner['image']). '</td>';
						$html .= '<td valign="middle" class="left description toggle">';
							if(strlen($winner['description']) > 1500){
								$html .= '<div class="expandable">
									<div class="inner">'
										.nl2br($winner['description']). '
									</div>
								</div>
								<a class="expander">...Read More</a>';
							}else{
								$html .= nl2br($winner['description']);
							} 
						$html .= '</td>';
					$html .= '</tr>
					</table>';
				}else{
					$html .= format_personnel($winner['first_name'], $winner['last_name'], $winner['year'].' '.$winner['type'], $winner['profile_url'], $winner['image']);
				}
			}
		}
		$html .= '</div>';
		//Past winners
		if(!empty($past_winners)){
			$has_winners = true;
			if(!empty($curr_winners)){
				$html .= '<br class="clear" /><br /><h3>Past Winners</h3>';
				$html .= '<div class="winners_list">';
			}
			foreach($past_winners as $winner){
				if(trim($winner['description']) != ''){
					$html .= '<table cellpadding="10" cellspacing="0" border="0" class="personnel-tbl">
					<tr>';
						$html .= '<td valign="top" class="nobg center" width="250px">' .format_personnel($winner['first_name'], $winner['last_name'], $winner['year'].' '.$winner['type'], $winner['profile_url'], $winner['image']). '</td>';
						$html .= '<td valign="middle" class="left description toggle">';
							if(strlen($winner['description']) > 1500){
								$html .= '<div class="expandable">
									<div class="inner">'
										.nl2br($winner['description']). '
									</div>
								</div>
								<a class="expander">...Read More</a>';
							}else{
								$html .= nl2br($winner['description']);
							} 
						$html .= '</td>';
					$html .= '</tr>
					</table>';
				}else{
					$html .= format_personnel($winner['first_name'], $winner['last_name'], $winner['year'].' '.$winner['type'], $winner['profile_url'], $winner['image']);
				}
			}
			$html .= '</div>';
		}
		
		//Subcategory winners
		if(!empty($award['sub_categories'])){
			foreach($award['sub_categories'] as $subcat){
				
				$curr_winners = ($current_winners[$subcat['award_id']] ?? array());
				$past_winners = ($award_winners[$subcat['award_id']] ?? array());
				$subcat_winners = array_merge($curr_winners, $past_winners);
				
				if(!empty($subcat_winners)){
					$has_winners = true;
					$html .= '<h3>' .$award['name'].' - '.$subcat['name']. '</h3>';
					$html .= $subcat['description'];
					$html .= '<div class="winners_list">';
					foreach($subcat_winners as $winner){
						if(trim($winner['description']) != ''){
							$html .= '<table cellpadding="10" cellspacing="0" border="0" class="personnel-tbl">
							<tr>';
								$html .= '<td valign="top" class="nobg center" width="250px">' .format_personnel($winner['first_name'], $winner['last_name'], $winner['year'].' '.$winner['type'], $winner['profile_url'], $winner['image']). '</td>';
								$html .= '<td valign="middle" class="left description toggle">';
									if(strlen($winner['description']) > 1500){
										$html .= '<div class="expandable">
											<div class="inner">'
												.nl2br($winner['description']). '
											</div>
										</div>
										<a class="expander">...Read More</a>';
									}else{
										$html .= nl2br($winner['description']);
									} 
								$html .= '</td>';
							$html .= '</tr>
							</table>';
						}else{
							$html .= format_personnel($winner['first_name'], $winner['last_name'], $winner['year'].' '.$winner['type'], $winner['profile_url'], $winner['image']);
						}
					}
					$html .= '</div>';
				}
				
			}
			
		}
		
		//No winners
		if(!$has_winners){
			$html .= '<h3>' .$award['name']. '</h3>';
			$html .= '<p>Currently no winners to display.</p>';
		}
		
		$html .= '</div>';
		
	}
	$html .= '</div>';
	//Set panel content
	$page['page_panels'][$panel_id]['content'] = $mblnav.$page['page_panels'][$panel_id]['content'];
	$page['page_panels'][$panel_id]['content'] = $mblnav.$subnav.$html;
}

//Page panels
include("includes/pagepanels.php");

?>