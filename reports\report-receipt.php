<?php

//Config
include("config.php");

//Get vars
$id = (isset($_GET['id']) ? $_GET['id'] : NULL);
$year = (isset($_GET['year']) ? $_GET['year'] : date('Y')); //Default this year
$start_date = $year.'-01-01';
$end_date = $year.'-12-31';
if($year == date('Y')){
	$end_date = $year.'-'.date('m-d'); //only go up to today if viewing this year
}

//Set data array
$records = array();

//Check permissions
if(!empty($id) && ($Account->profile_id == $id || (USER_LOGGED_IN && $Account->account_has_role(1)))){
		
	$params = array('Registered', $start_date, $end_date, $id);
	$where = "";
	if(!empty($categories)){
		$where .= "&& (";
		$count = 0;
		foreach($categories as $category){
			$count++;
			$params[] = $category;
			$where .= "`reg_event_categories`.`category_id` = ?".($count < count($categories) ? " || " : "");
		}
		$where .= ") ";
	}

	$query = $db->query("SELECT ".
		"`reg_tournament_field`.*, ".
		"`reg_attendees`.`first_name`, `reg_attendees`.`last_name`, `reg_attendees`.`account_id`, `reg_attendees`.`partner_id`, `reg_attendees`.`ticket_price`, ".
		"`reg_registrations`.`gst_rate`, `reg_registrations`.`pst_rate`, `reg_registrations`.`hst_rate`, `reg_registrations`.`paid`, ".
		"`account_profiles`.`profile_id`, ".
		"`facilities`.`facility_name`, ".
		"`reg_event_categories`.`category_id`, ".
		"`reg_events`.`name` AS `event_name`, ".
		"`reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`, ".
		"IFNULL(`reg_tournament_field`.`prize`, 0) AS `prize` ".
	"FROM `reg_attendees` ".
	"LEFT JOIN `reg_registrations` ON `reg_attendees`.`registration_id` = `reg_registrations`.`registration_id` ".
	"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
	"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
	"LEFT JOIN `reg_event_categories` ON `reg_attendees`.`event_id` = `reg_event_categories`.`event_id` ".
	"LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ".
	"LEFT JOIN `reg_occurrences` ON `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` ".
	"LEFT JOIN `reg_tournament_field` ON `reg_attendees`.`attendee_id` = `reg_tournament_field`.`attendee_id` ".
	"WHERE `reg_attendees`.`reg_status` = ? && `reg_attendees`.`account_id` IS NOT NULL && `reg_events`.`role_id` != 8 && (`reg_occurrences`.`start_date` >= ? && `reg_occurrences`.`end_date` <= ?) && `account_profiles`.`profile_id` = ? ".$where.
	"ORDER BY `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){

			//If attendee was a partner, get team prize from partner entry
			if(!empty($row['partner_id'])){
				$params = array($row['partner_id']);
				$query = $db->query("SELECT IFNULL(`team_prize`, 0) AS `team_prize` FROM `reg_tournament_field` WHERE `attendee_id` = ?", $params);
				if($query && !$db->error()){
					$partner = $db->fetch_array();
					$row['team_prize'] = $partner[0]['team_prize'];
				}
			}

			//Team prize amount should be divided between team members, except for Pro-Ams
			if($row['category_id'] != 8){
				$row['team_prize'] = ($row['team_prize']/2);
			}else{
				//Partner does not get team money for Pro-Ams
				if(!empty($row['partner_id'])){
					$row['team_prize'] = 0;
				}
			}
			
			//Ticket amount before taxes
			if($row['ticket_price'] > 0){
				$row['ticket_gst'] = (($row['ticket_price']*$row['gst_rate'])/($row['gst_rate']+100));
				$row['ticket_pst'] = (($row['ticket_price']*$row['pst_rate'])/($row['pst_rate']+100));
				$row['ticket_hst'] = (($row['ticket_price']*$row['hst_rate'])/($row['hst_rate']+100));
				$row['ticket_subtotal'] = $row['ticket_price']-$row['ticket_gst']-$row['ticket_pst']-$row['ticket_hst'];
			}
			
			//Total prize money
			$row['total_prize'] = number_format($row['prize']+$row['team_prize'], 2, '.', '');

			//Push to records if there is a value
			if($row['total_prize'] > 0 || $row['ticket_price'] > 0){
				$records[] = $row;
			}

		}
	}

	//Set html
	$html = '<table cellpadding="5" cellspacing="0" width="100%" border="0">
		<tr>
			<td align="center" colspan="2"><img src="' .$logo. '" /></td>
		</tr>
		<tr>
			<td align="center" colspan="2"><strong>' .$global['full_address']. '<br />' .$global['contact_phone']. ' / ' .$global['contact_toll_free']. '</strong></td>
		</tr>
		<tr>
			<td valign="top"><strong>' .$records[0]['first_name'].' '.$records[0]['last_name']. '<br />' .$records[0]['facility_name']. '</strong></td>
			<td valign="top" align="right"><strong>Date: ' .date('F j, Y'). '</strong></td>
		</tr>
		<tr>
			<td align="center" colspan="2">
				<h2>Re: Tournaments for ' .$year. '</h2>
			</td>
		</tr>
	</table><br />';

	$html .= '<table cellpadding="5" cellspacing="1" width="100%" border="0"><tr>
			<th style="' .$css['th']. '">Description</th>
			<th style="' .$css['th']. '" align="center">Tournament Start Date</th>
			<th style="' .$css['th']. '" width="100px">Amount</th>
			<th style="' .$css['th']. '" width="100px">Winnings</th>
		</tr>';
	
		$subtotal = 0;
		$total_gst = 0;
		$total_pst = 0;
		$total_hst = 0;
		$total_amount = 0;
		$total_prize = 0;
	
		$count = 0;
		foreach($records as $record){
			
			$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
			$html .= '<tr>
				<td style="' .$css_td. '">' .$record['event_name']. '</td>
				<td style="' .$css_td. '" align="center">' .date('Y-m-d', strtotime($record['start_date'])). '</td>
				<td style="' .$css_td. '" align="right">$' .number_format($record['ticket_subtotal'], 2). '</td>
				<td style="' .$css_td. '" align="right">$' .number_format($record['total_prize'], 2). '</td>
			</tr>';
			$count++;
			
			$subtotal += $record['ticket_subtotal'];
			$total_gst += $record['ticket_gst'];
			$total_pst += $record['ticket_pst'];
			$total_hst += $record['ticket_hst'];
			$total_amount += $record['ticket_price'];
			$total_prize += $record['total_prize'];
			
		}
		if($count == 0){
			$html .= '<tr>
				<td colspan="4">No records found.</td>
			</tr>';
		}
		$html .= '<tr>
			<td rowspan="3" valign="top"><strong>GST# 83024 0750 RT0001</strong></td>
			<td align="right"><strong>Subtotal:</strong></td>
			<td align="right"><strong>$' .number_format($subtotal, 2). '</strong></td>
			<td align="right"><strong>$' .number_format($total_prize, 2). '</strong></td>
		</tr>
		<tr>
			<td align="right"><strong>GST (' .number_format($records[0]['gst_rate'], 2). '%):</strong></td>
			<td align="right"><strong>$' .number_format($total_gst, 2). '</strong></td>
			<td>&nbsp;</td>
		</tr>';
		if($total_pst > 0){
			$html .= '<tr>
				<td align="right"><strong>PST (' .number_format($records[0]['pst_rate'], 2). '%):</strong></td>
				<td align="right"><strong>$' .number_format($total_pst, 2). '</strong></td>
				<td>&nbsp;</td>
			</tr>';
		}
		if($total_hst > 0){
			$html .= '<tr>
				<td align="right"><strong>PST (' .number_format($records[0]['hst_rate'], 2). '%):</strong></td>
				<td align="right"><strong>$' .number_format($total_hst, 2). '</strong></td>
				<td>&nbsp;</td>
			</tr>';
		}
		$html .= '<tr>
			<td align="right"><strong>Total Amount Paid:</strong></td>
			<td align="right"><strong>$' .number_format($total_amount, 2). '</strong></td>
			<td>&nbsp;</td>
		</tr>
	</table>';

	//Generate pdf and send to browser
	$mpdf->WriteHTML($html, 2);
	$mpdf->Output('Tournaments Receipt '.$year.'.pdf','I');
		
		
//Invalid request
}else{
	die('Access Denied.');
}

?>