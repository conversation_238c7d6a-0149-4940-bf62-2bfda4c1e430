<?php  

if(SECTION_ID == $_cmssections['form_submissions'] && USER_LOGGED_IN){

	//Get records 
	$filter_records = implode(",", array_fill_keys(array_keys($records_arr ?? []), "?"));
	$record_ids = array_keys($records_arr);

	$table_columns = [];
	$records_arr = [];

	if($filter_records){
		$db->query("SELECT `form_submission_fields`.`field_id`, 
				`form_submission_fields`.`label`, `form_submission_fields`.`value`, 
				`$record_db`.`$record_id`, `$record_db`.`subject`, `$record_db`.`timestamp` 
			FROM `form_submission_fields` 
			LEFT JOIN `$record_db` ON `form_submission_fields`.`$record_id` = `$record_db`.`$record_id` 
			WHERE `$record_db`.`$record_id` IN ($filter_records)
			GROUP BY `form_submission_fields`.`field_id` ORDER BY `$record_db`.`timestamp` DESC"
		, $record_ids);

		if(!$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			
			//For grouping table headers by subject
			$subject = $result[0]['subject'];
			$submission_id = $result[0]['submission_id'];
			
			//Add subject and timestamp columns to start
			$table_columns[$subject] = array();
			$table_columns[$subject][] = 'Subject';
			$table_columns[$subject][] = 'Date Submitted';
			
			foreach($result as $row){
				
				//If subject has changed, assume new form
				if($subject != $row['subject']){
					$subject = $row['subject'];
					$submission_id = $row['submission_id'];
					
					$table_columns[$subject] = array();
					$table_columns[$subject][] = 'Subject';
					$table_columns[$subject][] = 'Date Submitted';
				}
				
				//Compile headers, only one set per form
				if(!in_array($row['label'], $table_columns[$subject]) || $row['submission_id'] == $submission_id){
					$table_columns[$subject][] = html_entity_decode($row['label']);
				}
				
				//Compile records
				$records_arr[$subject][$row[$record_id]]['subject'] = html_entity_decode($row['subject']);
				$records_arr[$subject][$row[$record_id]]['timestamp'] = date('M j, Y g:iA', strtotime($row['timestamp']));

				// Format option values with bullet points and newlines
				$val = str_replace('<br/>', "\n", $row['value']);
				$val = str_replace('&nbsp;', "", $val);
				$val = str_replace('&#10003;', "• ", $val);
				$val = strip_tags(trim($val));

				$records_arr[$subject][$row[$record_id]][$row['field_id']] = html_entity_decode($val);
			}
		}
	}
	
	//Output CSV
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=form-submissions-".date("Ymd-His").".csv");
	header("Content-Transfer-Encoding: binary ");

	$fp = fopen('php://output', 'w');
	
	$count = 0;
	foreach($table_columns as $key=>$columns){		
		if($count){
			fputcsv($fp, array());
		}

		fputcsv($fp, $columns);
		
		foreach($records_arr[$key] as $row) {
			fputcsv($fp, $row);
		}

		$count++;
	}
	
	fclose($fp);

	exit();

} 
?>