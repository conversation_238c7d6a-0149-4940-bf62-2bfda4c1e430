@charset "utf-8";
/* 
	regstyles.css
	Project: PACMS v3.0
*/

/*------ regsystem styles ------*/
.relative{position:relative;}
.absolute{position:absolute;}

.block{display:block;}
.inlineblock{display:inline-block;}

.v_top{vertical-align:top;}
.v_middle{vertical-align:middle;}
.v_bottom{vertical-align:bottom;}

.text-underlined{text-decoration:underline;}
.text-caps{text-transform:uppercase;}

label.inlineblock{display:inline-block;}


/*------ listings ------*/
#listings-extra-buttons .button{margin-left:10px; margin-bottom:20px;}


/*------ search ------*/
#search-form{
	.select{height:50px; margin-bottom:20px; background-position-y:0;}
	&.multiple-search{
		.input, .select{margin-right:10px;}
	}
}
#advanced-search-form{
	#search-fields{max-width:1090px;}
	.buttons-wrapper{margin-top:20px; .full; .f_left; .clear;}
	.column{float:left;}
	h3{font-weight:600;}
} 

.panel-content.autoscroll{overflow:auto;}

.reset, a.reset{.delete; 
	i{background:@color-light; color:@color-dark;}
	&:hover{background:@color-darkest; 
		i{color:#fff; background:lighten(@color-darkest, 10%);}
	}
}

.scrollbox{display:block; max-height:220px; width:260px; overflow-y:scroll; padding:10px; border:1px solid @color-light;}


/*------ showhide toggle ------*/
.panel-switch-lg{margin-bottom:20px; 
	label:first-child{display:block; float:left; height:auto; padding:0 10px 0 0; line-height:40px; text-transform:uppercase; font-weight:800;}
	.onoffswitch{width:100px;
		.inner{
			&:before, &:after{height:40px; line-height:40px;}
			&:before{padding-left:19px;}
			&:after{padding-right:19px;}
		}
		.switch{width:50px; right:50px;}
	}
	
}


/*------ footer ------*/
#cms-footer .button{margin-left:10px;}


/*------ rows ------*/
.instructions-text{padding:20px 20px 0;}
.rows-wrapper{
	.row{padding:20px 20px 10px; border-bottom:1px solid @color-lightest;
		&:nth-child(2n){background:@color-lightest;}
	}
}

.remove-row-btn{margin-bottom:10px;}
.add-row-btn{display:inline-block; margin:20px;}


/*------ notifications ------*/
.pre-notification-panel label{min-width:200px;}
.post-notification-panel label{min-width:250px;}

.notification-days-wrapper{ 
	label{margin-top:10px;}
	a{display:inline-block; margin-bottom:20px;}
}


/*------ hierachry ------*/
.hierarchy-table{
	tr.push{
		td.show-lvl:before{content:"\f3bf"; display:inline-block; margin-right:10px; font-family:"Font Awesome 5 Free"; font-size:12px; color:@color-theme1; .rotate(@deg:90deg);}
		&.lvl2 td.show-lvl{padding-left:25px;}
		&.lvl3 td.show-lvl{padding-left:50px;}
		&.lvl4 td.show-lvl{padding-left:75px;}
		&.lvl5 td.show-lvl{padding-left:100px;}
	}
}


/*------ steps nav ------*/
.steps-nav{margin-bottom:19px; 
	ul{list-style-type:none; margin:0; padding:0; 
		li{display:inline-block; padding-right:1px; padding-bottom:1px; 
			&:last-child{padding-right:0;}
			a{
				display:inline-block; 
				position:relative;
				padding:15px;
				background:@color-dark;
				.center; 
				color:#fff; 
				font-size:16px; 
				font-weight:700; 
				text-transform:uppercase; 
				text-decoration:none; 
				white-space:nowrap; 
				// .trans();
				
				&:hover, &.active{background:@color-theme1; 
					small{color:@color-theme2;}
				}
				&.disabled{background:@color-light; color:@color-lightest; cursor:not-allowed;
					small{color:@color-lightest;}
				}
			}
		}
	}
}


/*------ qr code ------*/
.generate-qr-code{margin-bottom:20px;}


/*------ multiselect ------*/
.multiselect-field{margin-right:10px;}
.multiselect{ 
	width:260px; 
	height:115px; 
	margin-bottom:20px; 
	padding:0;
	border:1px solid @color-light;
	border-radius:0;
	background:#fff;
	outline:none;
	-webkit-appearance:none; 
	-moz-appearance:none; 
	appearance:none; 
	-webkit-border-radius:0; 
	-moz-border-radius:0;  
	// .trans(background-color); 
	text-overflow:""; 
	font-size:16px; 
	line-height:30px; 
	font-family:@font-base; 
	color:@color-dark; 
	font-weight:400; 
	
	option{padding:4px 8px 4px 4px;}
}
.multiselect-arrows{margin:5px 20px 0 10px;}
.multiselect-arrow, a.multiselect-arrow{display:block; margin:0 0 1px 0; padding:0 20px;}


/*------ tag editor ------*/
.tag-editor{.textarea; list-style-type:none; margin:0; overflow:auto; cursor:text; 
	li{position:relative; display:inline-block; margin:3px 7px 0 0; overflow:hidden; line-height:1.5;}
	li[style="width:1px"]{position:absolute; margin-right:0;}
	div{float:left; padding:0 4px;}
	input{ 
		padding:0; 
		margin:0; 
		border:0; 
		vertical-align:inherit; 
		outline:none; 
		cursor:text; 
		box-shadow:none; 
		background:none; 
		font-family:inherit; 
		font-weight:inherit; 
		font-size:inherit; 
		font-style:inherit; 
	}
	.placeholder{padding:0 8px; color:@color-darkest;}
	.tag-editor-spacer{display:none;}
	.tag-editor-tag{padding-left:5px; padding-right:25px; background:@color-lightest; overflow:hidden; cursor:pointer; white-space:nowrap; 
		&.active{background:none !important; 
			+.tag-editor-delete, +.tag-editor-delete i{visibility:hidden; cursor:text;}
		}
	}
	.tag-editor-delete{position:absolute; top:0; bottom:0; right:0; width:17px; background:@color-lightest; cursor:pointer; text-align:center; 
		i:before{content:"\00d7"; display:inline-block; line-height:20px; font-size:16px; font-style:normal;}
		&:hover i:before{color:@color-medium;}
	}
}
.tag-editor-hidden-src{position:absolute !important; left:-99999px;}
.tag-editor ::-ms-clear{display:none;} 


/*------ gallery ------*/
.gallery-img{display:inline-block; width:160px; height:160px; margin:0 10px 10px 0; border:1px solid @color-light; overflow:hidden;}


/*------ attendee info fields ------*/
.attendee-fields-wrapper .row .select + .radio + label{margin-left:20px;}

#occ_extra_buttons{margin-bottom:15px; 
	.button-sm{margin-right:10px; background:@color-theme1;}
	.button-sm:hover{background:@color-theme2;}
}


/*------ addons and options ------*/
#addons-wrapper{ 
	.addon-options{padding-top:20px; padding-left:50px; border-bottom:1px solid @color-light; background:url('../images/gradient_10_down.png') repeat-x @color-lightest;}
}
.add-option-btn{margin:0 0 10px 0;}


/*------ restrictions ------*/
.restriction-template{display:block; .clear; width:100%;}
#add-restriction-btn{.clear; .f_left;}


/*------ autocomplete ------*/
.ui-front{position:relative; z-index:100;}
.ui-helper-hidden-accessible{display:none;}
.ui-autocomplete.ui-menu{list-style:none; margin:0; padding:0; max-width:256px; max-height:160px; border:1px solid @color-light; overflow-y:auto; position:absolute !important;
	li{display:block; 
		.ui-menu-item-wrapper{display:block; background:#fff; display:block; padding:4px 8px; cursor:pointer;
			&:hover{background:@color-light;}
		}
	}
}


/*------ add attendees ------*/
#add-basic-attendee{margin-top:25px;}

#attendee_list{ 
	td{vertical-align:top;}
	.push td{border-bottom:1px solid @color-light; 
		&:first-child{padding-left:90px;}
	}
	.extra-field{display:inline-block; padding-right:20px;}
	.toggle-extra-fields{margin-top:13px; color:@color-theme1; cursor:pointer; 
		&:hover{color:@color-theme2;}
	}
}


/*------ registration ------*/
#registration-totals #ordertotal td{text-transform:uppercase; font-weight:800; font-size:18px;}


/*------ attendee stats ------*/
#attendee-charts .span_3{width:33.33%;}
.donut-chart{width:100%; height:200px;}
#chart-attendee-locations{max-width:100% !important; height:200px; padding-bottom:20px; overflow-y:hidden; overflow-x:auto;}