<?php
	
//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

echo "<form action='' method='post' enctype='multipart/form-data'>";

	//Settings
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Hole In One Fees
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";

		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0'>";
				echo "<tr>
					<td width='150px'><label class='nopadding'>Service Fee ".$CMSBuilder->tooltip('Service Fee', 'Administrative fee to be added to all online credit card purchases exceeding the maximum online payment amount. May be a flat rate, or a percentage (example: 5.00 <b>or</b> 2.3). Please be sure to select the correct type below. If nothing is set, the default system service fee will be used instead.')."</label></td>
					<td><input type='text' name='admin_fee' value='".(isset($settings['admin_fee']) ? number_format($settings['admin_fee'], 2, '.', '') : '')."' class='input input_sm decimal nomargin' /></td>
				</tr>";
				echo "<tr>
					<td width='150px'><label class='nopadding'>Service Fee Type ".$CMSBuilder->tooltip('Service Fee Type', 'Select if the Service Fee (if applicable) will be calculated as a percentage, or a flat fee added to all online credit card purchases. This setting can be overwritten for individual events.')."</label></td>
					<td>
						<select name='admin_fee_type' class='select nomargin'>
							<option value='Dollar'".(isset($settings['admin_fee_type']) && $settings['admin_fee_type'] == 'Dollar' ? " selected" : "").">Dollar Amount</option>
							<option value='Percent'".(isset($settings['admin_fee_type']) && $settings['admin_fee_type'] == 'Percent' ? " selected" : "").">Percentage</option>
						</select>
					</td>
				</tr>";
			echo "</table>";
		echo "</div>";
	echo "</div>"; 

	//Rates
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Hole In One Premiums
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";

		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' id='hio-rates-tbl'>
				<thead>
					<tr>
						<th width='180px'>Prize</th>
						<th width='180px'>Premium <small>32-99 Golfers</small></th>
						<th>Premium <small>100-199 Golfers</small></th>
					</tr>
				</thead>
				<tbody>";
				if(!empty($rates)){
					$index=0;
					foreach($rates as $rate){
						echo "<tr class='hio_template'>
							<td>$<input type='text' name='prize[".$index."]' value='".(isset($_POST['prize'][$index]) ? $_POST['prize'][$index] : number_format($rate['prize_total'], 2, '.', ''))."' class='input input_sm number nomargin" .(in_array('prize_'.$index, $required) ? ' required' : ''). "' /></td>
							<td>$<input type='text' name='premium[".$index."]' value='".(isset($_POST['premium'][$index]) ? $_POST['premium'][$index] : number_format($rate['premium'], 2, '.', ''))."' class='input input_sm number nomargin" .(in_array('premium_'.$index, $required) ? ' required' : ''). "' /></td>
							<td>$<input type='text' name='premium_100[".$index."]' value='".(isset($_POST['premium_100'][$index]) ? $_POST['premium_100'][$index] : number_format($rate['premium_100'], 2, '.', ''))."' class='input input_sm number nomargin" .(in_array('premium_100_'.$index, $required) ? ' required' : ''). "' /></td>
						</tr>";
						$index++;
					}
				}else{
					echo "<tr class='hio_template'>
						<td>$<input type='text' name='prize[0]' value='' class='input input_sm number nomargin' /></td>
						<td>$<input type='text' name='premium[0]' value='' class='input input_sm number nomargin' /></td>
						<td>$<input type='text' name='premium_100[0]' value='' class='input input_sm number nomargin' /></td>
					</tr>";
				}
				echo "</tbody>
			</table>";

			echo "<a id='add-hio-btn' class='add-row-btn button-sm' href='#' onclick='addNewHIO(); return false;'>+ Add New</a>";	

		echo "</div>";
	echo "</div>"; 
	
	//Sticky footer
	echo "<footer id='cms-footer' class='resize'>";
	echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
	echo "</footer>";

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid']. "'/>";	
echo "</form>";

?>