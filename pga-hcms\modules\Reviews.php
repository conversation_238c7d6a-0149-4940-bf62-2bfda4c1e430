<?php

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('reviews');
	$CMSBuilder->set_widget($_cmssections['reviews'], 'Total Reviews', $total_records);
}

if(SECTION_ID == $_cmssections['reviews']){

	//Define vars
	$record_db = 'reviews';
	$record_id = 'review_id';
	$record_name = 'Review';

	//Validation
	$errors = false;
	$required = [];
	$required_fields = ['rating'];

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.client",
		"$record_db.company"
	];


	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;

	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	$db->query("SELECT * FROM `$record_db` $where ORDER BY `ordering`, `$record_id`", $params);
	$records_arr = $db->fetch_assoc($record_id);
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);
	}


	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];
		}
	}


	//Delete item
	if(isset($_POST['delete'])){

		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);
		}
		header("Location: " .PAGE_URL);
		exit();


	//Save item
	}else if(isset($_POST['save'])){
		$_POST['showhide'] = +!isset($_POST['showhide']);

		// Overwrite POST data with record data if not editable
		$editable = ($records_arr[ITEM_ID]['type'] ?? '') != 'Google Review';
		if(!$editable){
			$_POST['client']  = $records_arr[ITEM_ID]['client'];
			$_POST['company'] = $records_arr[ITEM_ID]['company'];
			$_POST['content'] = $records_arr[ITEM_ID]['content'];
			$_POST['rating']  = $records_arr[ITEM_ID]['rating'];
		}

		//Required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		if(!$errors){
			$params = array(
				ITEM_ID,
				$_POST['client'],
				$_POST['company'],
				$_POST['content'],
				$_POST['rating'],
				$_POST['ordering'],
				$_POST['showhide'],

				$_POST['client'],
				$_POST['company'],
				$_POST['content'],
				$_POST['rating'],
				$_POST['ordering'],
				$_POST['showhide']
			);
			$db->query("INSERT INTO `$record_db` (`$record_id`, `client`, `company`, `content`, `rating`, `ordering`, `showhide`) VALUES (?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `client` = ?, `company` = ?, `content` = ?, `rating` = ?, `ordering` = ?, `showhide` = ?", $params);
			if(!$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();

			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}
		}
	}
}

?>