<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('pages_cta');
	$CMSBuilder->set_widget($_cmssections['ctas'], 'Total Call To Actions', $total_records);
}

if(SECTION_ID == $_cmssections['ctas']){
	define('CTA_IMAGE', false); // Enable CTA images

	//Define vars
	$record_db 	 = 'pages_cta';
	$record_id 	 = 'cta_id';
	$record_name = 'Call To Action';
	
	//Validation
	$errors 	= false;
	$required 	= [];
	$required_fields = ['title'];

	//Image Uploader
	$imagedir = '../images/cta/';
	$CMSUploader = new CMSUploader('cta', $imagedir);

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.title",
		"$record_db.subtitle"
	];	
	
	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get records
	$db->query("SELECT * FROM $record_db $where", $params);
	$records_arr = $db->fetch_assoc($record_id);
	foreach($records_arr as &$record){
		$record['image']        = check_file($record['image'], $imagedir);
		$record['image_mobile'] = check_file($record['image_mobile'], $imagedir);
		unset($record);
	}
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}

	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){
			$row = $records_arr[ITEM_ID];
			
		//Not found	
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}
	

	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()){
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}

		header("Location: " .PAGE_URL);
		exit();
		

	//Save item
	}else if(isset($_POST['save'])){

		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);
		
		//Required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Image validation
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large. Cannot exceed ' .$_max_filesize['megabytes']. '.';
		}

		if(!$errors){
			
			//Format slug
			$pagename = clean_url($_POST['title']);
			
			//Delete images
			if(isset($_POST['deleteimage']) || isset($_POST['deleteimage_mobile'])){
				$CMSUploader->bulk_delete([
					'image' => isset($_POST['deleteimage']) ? $records_arr[ITEM_ID]['image'] : false,
					'image_mobile' => isset($_POST['deleteimage_mobile']) ? $records_arr[ITEM_ID]['image_mobile'] : false
				]);
			}
			
			//Upload new images
			try{
				$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []); 
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			//Insert to db
			$params = [
				ITEM_ID, 
				$_POST['title'], 
				$_POST['subtitle'], 
				$images['image'] ?? NULL,
				$images['image_mobile'] ?? NULL,
				$_POST['url'], 
				$_POST['url_target'], 
				$_POST['url_text'], 
				$_POST['url2'], 
				$_POST['url_target2'], 
				$_POST['url_text2'], 
				$_POST['showhide'],
				
				$_POST['title'], 
				$_POST['subtitle'], 
				$images['image'] ?? NULL,
				$images['image_mobile'] ?? NULL,
				$_POST['url'], 
				$_POST['url_target'], 
				$_POST['url_text'], 
				$_POST['url2'], 
				$_POST['url_target2'], 
				$_POST['url_text2'], 
				$_POST['showhide']
			];
			$db->query("INSERT INTO $record_db ($record_id, title, subtitle, image, image_mobile, url, url_target, url_text, url2, url_target2, url_text2, showhide) VALUES (?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE title = ?, subtitle = ?, image = ?, image_mobile = ?, url = ?, url_target = ?, url_text = ?, url2 = ?, url_target2 = ?, url_text2 = ?, showhide = ?", $params);
			if(!$db->error()){
				$item_id = ITEM_ID ?: $db->insert_id(); //For image positioning
				
				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}
			}else{
				$CMSBuilder->set_system_alert('Unable to update record. ', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}
		

	//Handle images
	}else{
		include('modules/CropImages.php');
	}
}

?>