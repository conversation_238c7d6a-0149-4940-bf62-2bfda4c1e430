<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}


//Display all
if(ACTION == '' || (ACTION == 'edit' && empty($hio))){

	//Add new top button
	// $html .= '<p><a href="' .$page['page_url']. '?action=add" class="button solid inline nomargin action-btn">Create New Event</a></p>';

	//Add new table button
	$html .= '<a href="' .$page['page_url']. '?action=add" class="button f_right solid inline action-btn button primary red">Create New Event<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>';

	//Search form
	// $html .= '<form name="search-form" id="directory-search-bar" action="" method="get" class="clearfix directory-search-form">
	//  <div class="search-input-container">
	// 	<input type="text" name="search" class="input" value="' .(isset($_GET['search']) ? $_GET['search'] : ''). '" placeholder="Search" />
	// 	<button type="submit" class="button search-icon-btn"><i class="fas fa-search"></i></button>
	// 	' .(isset($_GET['search']) && trim($_GET['search']) != '' ? '<a href="' .$page['page_url']. '" class="fa fa-times-circle"></a>' : ''). '
	// </form>';

	$html .= '<form name="search-form" id="directory-search-bar" 
        action="" method="get" class="clearfix directory-search-form">

        <div class="search-input-container">
            <input type="text" name="search" class="input directory-search-input"
                   value="'.htmlspecialchars($_GET["search"] ?? '', ENT_QUOTES, 'UTF-8').'"
                   placeholder="Search" />';

            if (!empty($_GET["search"] ?? '')): 
              $html .=  '<a href="'.htmlspecialchars($_sitepages["hole-in-one"]["page_url"], ENT_QUOTES, 'UTF-8').'"  title="Clear Search" class="clear-search-btn">×</a>
			  ';
             endif; 

            $html .= '<button type="submit" class="search-icon-btn" title="Search">
                <i class="fa fa-search"></i>
            </button>
        </div>
        <button type="submit" class="button primary visually-hidden">Search</button>
    </form>';

	//Hole in ones
	$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="hio-list">';
	if(!empty($hios)){
		$html .= '<tr>
			<th align="left">Event Name</th>
			<th align="left">Invoice No.</th>
			<th align="left">Prize</th>
			<th align="left">Premium</th>
			<th width="120px">Action</th>
		</tr>';
		foreach($hios as $hio){
			$html .= '<tr>
				<td><a href="' .$page['page_url']. '?action=edit&id=' .$hio['hio_id']. '">' .$hio['event_name']. '</a>
					<small class="dblock">' .format_date_range($hio['start_date'], $hio['end_date']). '</small>
				</td>
				<td><a href="' .$_sitepages['invoices']['page_url']. '?action=edit&id='.$hio['invoice_id']. '">'.$hio['invoice_number'].'</a>'.
					(!$hio['approved'] ? '<small class="dblock color-red"><i class="fa fa-exclamation-triangle"></i> Pending Payment</small>' : '').'
				</td>
				<td>$' .number_format($hio['prize_total'], 2). '</td>
				<td>$' .number_format($hio['premium_total'], 2). '</td>
				<td><a href="' .$page['page_url']. '?action=edit&id=' .$hio['hio_id']. '" class="button simple"><i class="fa fa-eye"></i></a></td>
			</tr>';
		}
	}else{
		$html .= '<tr>
			<td class="nobg" colspan="5">No records found' .(isset($_GET['search']) && trim($_GET['search']) != '' ? ' matching `<strong>' .$_GET['search']. '</strong>`' : ''). '.</td>
		</tr>';
	}

	//Pager
	if($totalresults > 0){
		$searchterm = (isset($_GET['search']) ? $_GET['search'] : '');
		$html .= '<tr>
			<td class="pager" colspan="5">
				<small>';
					$html .= 'Displaying '.($pg == 'all' ? '1 - '.$totalresults : (1+($limit*($pg-1))).' - '.(count($hios)+($limit*($pg-1)))).' (of '.$totalresults.' Total)<br />';
					if($totalresults > $limit && $pg != 'all'){
						$tagend = round($totalresults % $limit, 0);
						$splits = round(($totalresults - $tagend)/$limit, 0);
						$num_pages = ($tagend == 0 ? $splits : $splits+1);
						$pos = $pg;
						$startpos = ($pos*$limit)-$limit;

						$html .= ($pos > 1 ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos-1).'">&lsaquo; Prev</a> ' : '');
						for($i=1; $i<=$num_pages; $i++){
							$html .= ($i != $pos ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.$i.'">'.$i.'</a> ' : '<span><u>'.$i.'</u></span> ');
						}
						$html .= ($pos < $num_pages ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos+1).'">Next &rsaquo;</a> ' : '');
					}
				$html .= '</small>
			</td>
		</tr>';
	}

	$html .= '</table>';


//Add new
}else if(ACTION == 'add'){

	//Event information
	if(!$confirm){

		//Panel title
		$page['page_panels'][$panel_id]['title'] = 'Create Hole In One Event';
		$page['page_panels'][$panel_id]['show_title'] = true;

		//Load from session
		if(isset($_SESSION['hio'])){
			$row = $_SESSION['hio'];
			foreach($_SESSION['hio']['courses'] as $c=>$course){
				$row['course_name'][$c] = $course['course_name'];
				foreach($course['holes'] as $h=>$hole){
					$row['yards_m_'.$h][$c] = $hole['yards_men'];
					$row['yards_w_'.$h][$c] = $hole['yards_women'];
					$row['prize_'.$h][$c] = $hole['prize'];
				}
			}
		}

		//Display form
		$html .= '<form method="post" action="" class="" id="hio-form" enctype="multipart/form-data">
			<p><small>Required Fields</small> <strong class="color-red">*</strong></p>';

			//Event
			$html .= '<h4>Event Information</h4>
			<fieldset class="form-grid">
				<div class="form-column f_left">
					<label>Event Name <strong class="color-red">*</strong></label>
					<input type="text" name="event_name" class="input' .(in_array('event_name', $required) ? ' required' : ''). '" value="' .(isset($row['event_name']) ? $row['event_name'] : ''). '" />
				</div>
				<div class="form-column f_right">
					<label>Number of Golfers <strong class="color-red">*</strong></label>
					<input type="text" name="field" class="input half number golfers' .(in_array('field', $required) ? ' required' : ''). '" value="' .(isset($row['field']) ? $row['field'] : ''). '" />
				</div>
				<label class="clear">Event Date <strong class="color-red">*</strong>
					<small class="dblock">Note: If it is a recurring event with all the same characteristics, you may enter all the dates now to create multiple policies at once.</small>
				</label>
				<div class="form-column clear">';
					//Dates
					if(isset($row['dates']) && !empty($row['dates'])){
						foreach($row['dates'] as $date){
							$html .= '<input type="text" name="dates[]" class="input clear futuredatepicker ' .(in_array('dates', $required) || in_array($date, $required) ? ' required' : ''). '" value="' .$date. '" autocomplete="off" />';
						}
					}else{
						$html .= '<input type="text" name="dates[]" class="start input clear futuredatepicker ' .(in_array('dates', $required) ? ' required' : ''). '" value="" autocomplete="off" />';
					}
					$html .= '<a onclick="hioDate();" id="hio-date" class="clear">+ Add Another Date</a>
				</div>
			</fieldset>';

			//Courses
			$html .= '<h4>Sponsored Holes</h4>';
			if(isset($row['courses']) && !empty($row['courses'])){
				foreach($row['courses'] as $c=>$course){

					$html .= '<fieldset ' .($c==0 ? 'id="hio-template"' : ''). 'class="hio-course">
						<div class="form-column">
							<label>Course Name</label>
							<p><input type="text" name="course_name[]" class="input' .(in_array('course_name_'.$c, $required) ? ' required' : ''). '" value="' .(isset($row['course_name'][$c]) ? $row['course_name'][$c] : ''). '" /></p>
						</div>
						<div class="hio-headers clearfix">
							<label>Hole</label>
							<label>Men <small>Yds</small></label>
							<label>Women <small>Yds</small></label>
							<label>Prize <small>$</small></label>
						</div>';
						for($h=1; $h<=18; $h++){
							$html .= '<div class="hio-column clearfix">
								<label>' .$h. '</label>
								<div><input type="text" name="yards_m_'.$h.'[]" class="input number' .(in_array('yards_m_'.$h.'_'.$c, $required) ? ' required' : ''). '" value="' .(isset($row['yards_m_'.$h][$c]) ? $row['yards_m_'.$h][$c] : ''). '" /></div>
								<div><input type="text" name="yards_w_'.$h.'[]" class="input number' .(in_array('yards_w_'.$h.'_'.$c, $required) ? ' required' : ''). '" value="' .(isset($row['yards_w_'.$h][$c]) ? $row['yards_w_'.$h][$c] : ''). '" /></div>
								<div><input type="text" name="prize_'.$h.'[]" class="input number sum' .(in_array('prize_'.$h.'_'.$c, $required) ? ' required' : ''). '" value="' .(isset($row['prize_'.$h][$c]) ? $row['prize_'.$h][$c] : ''). '" /></div>
							</div>';
						}
					$html .= '</fieldset>';
				}

			//Display template
			}else{
				$html .= '<fieldset id="hio-template" class="hio-course">
					<div class="form-column">
						<label>Course Name</label>
						<p><input type="text" name="course_name[]" class="input" value="' .$facility['facility_name']. '" /></p>
					</div>
					<div class="hio-headers clearfix">
						<label>Hole</label>
						<label>Men <small>Yds</small></label>
						<label>Women <small>Yds</small></label>
						<label>Prize <small>$</small></label>
					</div>';
					for($h=1; $h<=18; $h++){
						$html .= '<div class="hio-column clearfix">
							<label>' .$h. '</label>
							<div><input type="text" name="yards_m_'.$h.'[]" class="input number" value="" /></div>
							<div><input type="text" name="yards_w_'.$h.'[]" class="input number" value="" /></div>
							<div><input type="text" name="prize_'.$h.'[]" class="input number sum" value="" /></div>
						</div>';
					}
				$html .= '</fieldset>';
			}

			$html .= '<p id="hio-add"><a onclick="hioTemplate();">+ Add Another Course</a></p>';

			//Calculations
			$html .= '<fieldset>
				<table cellpadding="0" cellspacing="0" border="0" width="100%" class="nobgs noborder nomargin noresponsive">
					<tr>
						<td width="250px">Total Prize Amount:</td>
						<td><span id="prize_total">$' .(isset($row['prize_total']) ? number_format($row['prize_total'], 2) : '0.00'). '</span></td>
					</tr>
					<tr>
						<td>Premium Per Date:</td>
						<td><span id="premium_per_date">$' .(isset($row['premium_total']) ? number_format($row['premium_total'], 2) : '0.00'). '</span></td>
					</tr>
					<tr>
						<td><strong>Total Premium:</strong></td>
						<td><strong id="premium_total">$' .(isset($row['premium_total']) ? number_format($row['premium_total']*count($row['dates']), 2) : '0.00'). '</strong></td>
					</tr>
				</table>
			</fieldset>';

			//Comments
			$html .= '<fieldset>
				<label>Comments</label>
				<textarea name="comments" class="textarea' .(in_array('comments', $required) ? ' required' : ''). '">' .(isset($row['comments']) ? $row['comments'] : ''). '</textarea>
			</fieldset>';

			//Terms
			if(!empty($waiver_forms)){
				$html .= '<h4>Terms &amp; Conditions</h4>
				<fieldset class="clearfix">';
					$count=0;
					foreach($waiver_forms as $waiver){
						$count++;
						$html .= '<span class="' .(in_array('waiver-' .$waiver['waiver_id'], $required) ? 'color-red' : ''). '">' .$waiver['title']. '</span>'.
						($waiver['required'] ? ' <strong class="color-red">*</strong>' : ''). '<br />
						<input type="checkbox" class="checkbox" name="waiver-' .$waiver['waiver_id']. '" id="waiver-' .$waiver['waiver_id']. '" value="1"' .(isset($row['waivers'][$waiver['waiver_id']]) && $row['waivers'][$waiver['waiver_id']] == true ? ' checked' : ''). ' />
						<label for="waiver-' .$waiver['waiver_id']. '"><small>I agree/consent to the conditions outlined below:</small></label>
						<div class="terms-box' .($count == count($waiver_forms) ? ' nomargin' : ''). '">';
							if($waiver['file_name'] != '' && file_exists('uploads/files/'.$waiver['file_name'])){
								$html .= '<a href="' .$path.'uploads/files/'.$waiver['file_name']. '" target="_blank"><i class="fa fa-file-pdf-o"></i>&nbsp; Download ' .$waiver['title']. '</a><br />';
							}
							$html .= nl2br($waiver['description']).'
						</div>' .($count < count($waiver_forms) ? '<hr />' : '');
					}
				$html .= '</fieldset>';
			}

			//Form buttons
			$html .= '<div class="form-buttons">
				<button type="submit" name="continue" value="continue" class="button solid f_right primary red">Continue<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
				<a href="' .$page['page_url']. '" class="previous f_right primary black button">Cancel<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
			</div>

			<input type="hidden" name="xid" value="'.$_COOKIE['xid'].'" />
		</form>';


	//Confirmation
	}else{

		//Panel title
		$page['page_panels'][$panel_id]['title'] = 'Review &amp; Submit';
		$page['page_panels'][$panel_id]['show_title'] = true;

		$html = '';

		//Payment alert for hio accounts
		if(HIO_ACCESS){
			$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> Immediate payment is required to complete this registration. You will be prompted for payment details once you submit your registration.');
		}

		//Display details
		$html .= '<h4>Event Information</h4>
		<table cellpadding="0" cellspacing="0" border="0" width="100%" class="nobgs noborder noresponsive">
			<tr>
				<td valign="top" width="100px">Name:</td>
				<td>' .$_SESSION['hio']['event_name']. '</td>
			</tr>
			<tr>
				<td valign="top">Field:</td>
				<td>' .$_SESSION['hio']['field']. ' Golfers</td>
			</tr>
			<tr>
				<td valign="top">Date' .(count($_SESSION['hio']['dates']) > 1 ? 's' : ''). ':</td>
				<td>' .implode(';&nbsp; ', $_SESSION['hio']['dates']). '</td>
			</tr>
		</table>';

		if(trim($_SESSION['hio']['comments']) != ''){
			$html .= '<p>' .nl2br($_SESSION['hio']['comments']). '</p>';
		}

		foreach($_SESSION['hio']['courses'] as $course){
			$html .= '<h4>' .$course['course_name']. '</h4>
			<table cellpadding="10" cellspacing="0" border="0" width="100%" class="header-container">
				<tr class="header-row">
					<th align="left">Hole</th>
					<th align="left">Men</th>
					<th align="left">Women</th>
					<th align="right">Prize</th>
					<th align="right" width="200px">Premium</th>
				</tr>';
				foreach($course['holes'] as $hole=>$info){
					$html .= '<tr>
						<td>' .$hole. '</td>
						<td>' .(!empty($info['yards_men']) ? $info['yards_men'].' Yards' : ''). '</td>
						<td>' .(!empty($info['yards_women']) ? $info['yards_women'].' Yards' : ''). '</td>
						<td align="right">$' .number_format($info['prize'], 2). '</td>
						<td align="right">$' .number_format($info['premium'], 2). '</td>
					</tr>';
				}
			$html .= '</table>';
		}

		//Totals
		$html .= '<h4>Event Totals</h4>
		<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive amount-container">
			<tr class="amount-row">
				<td align="right">Total Prize Amount:</td>
				<td class="right" width="200px">$' .number_format($_SESSION['hio']['prize_total'], 2). '</td>
			</tr>
			<tr class="amount-row">
				<td align="right" valign="top">Premium Per Date:</td>
				<td class="right" valign="top">$' .number_format($_SESSION['hio']['premium_total'], 2). '</td>
			</tr>
			<tr class="amount-row">
				<td align="right" valign="top"><h6>Total Premium:</h6></td>
				<td class="right" valign="top"><h6>$' .number_format($_SESSION['hio']['premium_total']*count($_SESSION['hio']['dates']), 2). '</h6></td>
			</tr>
		</table>';

		//Submit registration
		$html .= '<form name="confirm-form" class="hidden-recaptcha" action="" method="post" data-recaptcha="#recaptcha-confirm">
			<div class="form-buttons">
				<button type="button" name="button" class="button solid f_right  button primary red">Submit Registration<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
				<a href="' .$page['page_url'].'?action='.ACTION. '" class="previous f_right  button primary black light">Go Back<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
			</div>
			<div class="hidden">
				<div id="recaptcha-modal" class="hidden-modal" title="Verify You&rsquo;re Not a Robot">
					<div class="recaptcha-wrapper">
						<div id="recaptcha-confirm" class="g-recaptcha" data-sitekey="'.$global['recaptcha_key'].'"></div>
					</div>
				</div>
			</div>
			<input type="hidden" name="g-recaptcha-response" value="" />
			<input type="hidden" name="process" value="1" />
			<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
		</form>';

	}


//Selected record
}else if(ACTION == 'edit'){

	//Panel title
	$page['page_panels'][$panel_id]['title'] = 'Hole In One';
	$page['page_panels'][$panel_id]['show_title'] = true;

	if(!$hio['approved']){
		$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> Hole in one event was successfully submitted and will be approved upon receipt of payment. &nbsp; <a href="' .$_sitepages['payments']['page_url']. '?id=i'.$hio['invoice_id']. '">Pay Now &rsaquo;</a>');
	}

	//Event information
	$html .= '<div style="border: 1px solid #ccc;" ><h4 style="margin-left: 15px;">Event Information</h4>
	<table cellpadding="0" cellspacing="0" border="0" width="100%" class="nobgs noresponsive noborder">
		<tr>
			<td width="150px">Name:</td>
			<td>' .$hio['event_name']. '</td>
		</tr>
		<tr>
			<td>Field:</td>
			<td>' .$hio['field']. ' Golfers</td>
		</tr>
		<tr>
			<td valign="top">Date' .(count($hio['event_dates']) > 1 ? 's' : ''). ':</td>
			<td>' .implode(';&nbsp; ', $hio['event_dates']). '</td>
		</tr>
		<tr>
			<td valign="top">Invoice:&nbsp;</td>
			<td>' .(!empty($hio['invoice_number']) ? '<a href="' .$_sitepages['invoices']['page_url']. '?action=edit&id=' .$hio['invoice_id']. '">'.$hio['invoice_number'].'</a>' : 'Pending'). '</td>
		</tr>
	</table></div>';

	//Comments
	if(trim($hio['comments']) != ''){
		$html .= '<p style="margin-top: 25px;">' .nl2br($hio['comments']). '</p>';
	}

	//Courses
	foreach($hio['courses'] as $course){
		$html .= '<h4>' .$course['course_name']. '</h4>';
		foreach($course['holes'] as $hole){
			$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noheaders nobgs reg-table">
				<tr>
					<td>Hole ' .$hole['hole']. '<br />'.
						(!empty($hole['yards_men']) ? '<small class="dblock">Men: ' .$hole['yards_men']. ' Yards</small>' : '').
						(!empty($hole['yards_women']) ? '<small class="dblock">Women: ' .$hole['yards_women']. ' Yards</small>' : '').'
					</td>
					<td align="right">$' .number_format($hole['prize'], 2). '<small class="dblock">Prize Amount</small></td>
					<td align="right" width="150px">$' .number_format($hole['premium'], 2). '<small class="dblock">Premium Per Date</small></td>
				</tr>
			</table>';
		}
	}

	//Display totals
	$html .= '<br /><table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive">
		<tr>
			<td align="right">Total Prize Amount:</td>
			<td class="right" width="150px">$' .number_format($hio['prize_total'], 2). '</td>
		</tr>
		<tr>
			<td align="right">Premium Per Date:</td>
			<td class="right">$' .number_format($hio['premium_total']/count($hio['event_dates']), 2). '</td>
		</tr>
		<tr>
			<td align="right"><h6>Total Premium:</h6></td>
			<td class="right"><h6>$' .number_format($hio['premium_total'], 2). '</h6></td>
		</tr>
	</table>';

	if(!empty($hio['invoice_id'])){
		$html .= '<p class="right"><a href="' .$_sitepages['invoices']['page_url']. '?action=edit&id=' .$hio['invoice_id']. '" class="button solid nomargin primary red">View Invoice<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a></p>';
	}
}

//Set panel content
// $page['page_panels'][$panel_id]['content'] = $html;
// $page['page_panels'][$panel_id]['append_content'] = $html;

?>