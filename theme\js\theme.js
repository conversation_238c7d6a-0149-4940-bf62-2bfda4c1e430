/*** NOTE - Append this to script.js ***/

// Slideshow swiper options
extendSlideshowOpts = {
	effect: 'fade',

	// force autoheight to update on resize when based on viewport height
	on: {
		afterInit: swiper => {
			$(window).on('resize-end', () => {
				swiper.updateAutoHeight();
			});
		}
	}
};

// Gallery scrollbar
$(function () {
	let $galleries = $('.panel.gallery:not(.gallery-listings) .light-gallery');

	observeOnce($galleries, () => loadScript(['swiper', 'lightgallery'], () => {
		$galleries.each(function () {
			let $this = $(this);

			$this.addClass('swiper');
			$this.children().wrap(`<div class="swiper-slide"/>`);
			$this.children().wrapAll(`<div class="swiper-wrapper"/>`);
			$this.append(`<div class="swiper-scrollbar"/>`);

			const swiper = new Swiper(this, {
				spaceBetween: 1,
				slidesPerView: 'auto',
				watchSlidesProgress: true, // fix right click moving slideshow
				centerInsufficientSlides: true,
				freeMode: true,

				scrollbar: {
					el: ".swiper-scrollbar",
					draggable: true,
					hide: false,
					dragSize: 150,
				},

				mousewheel: {
					forceToAxis: true
				},

				breakpoints: {
					769: {
						scrollbar: {
							dragSize: 'auto',
						}
					}
				}
			});

			$this.data({swiper});
		});
	}));
});