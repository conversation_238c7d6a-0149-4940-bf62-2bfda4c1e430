<?php

//Global functions
include($_SERVER['DOCUMENT_ROOT'].$root."core/functions.php");

//Generate timezone list
if(!function_exists('timezone_list')){
	function timezone_list(){
	    static $timezones = null;

	    if ($timezones === null){
	        $timezones = [];
	        $offsets = [];
	        $now = new DateTime();

	        foreach (DateTimeZone::listIdentifiers() as $timezone){
	            $now->setTimezone(new DateTimeZone($timezone));
	            $offsets[] = $offset = $now->getOffset();
	            $timezones[$timezone] = '(' . format_GMT_offset($offset) . ') ' . format_timezone_name($timezone);
	        }

	        array_multisort($offsets, $timezones);
	    }

	    return $timezones;
	}
}
if(!function_exists('format_GMT_offset')){
	function format_GMT_offset($offset){
	    $hours = intval($offset / 3600);
	    $minutes = abs(intval($offset % 3600 / 60));
	    return 'GMT' . ($offset ? sprintf('%+03d:%02d', $hours, $minutes) : '');
	}
}
if(!function_exists('format_timezone_name')){
	function format_timezone_name($name){
	    $name = str_replace('/', ', ', $name);
	    $name = str_replace('_', ' ', $name);
	    $name = str_replace('St ', 'St. ', $name);
	    return $name;
	}
}

//Delete directory (recursive)
if(!function_exists('delete_directory')){
	function delete_directory($dirname){
		if (is_dir($dirname))
			chmod($dirname, 0777);
			$dir_handle = opendir($dirname);
		if (!$dir_handle)
			return false;
		while($file = readdir($dir_handle)){
			if ($file != "." && $file != ".."){
				chmod($dirname."/".$file, 0777);
				if (!is_dir($dirname."/".$file))
				unlink($dirname."/".$file);
				else
				delete_directory($dirname.'/'.$file);
			}
		}
		chmod($dir_handle, 0775);
		closedir($dir_handle);
		rmdir($dirname);
	}
}

//Generate clean query string label from string
if(!function_exists('clean_query_string')){
	function clean_query_string($str){
		$clean = trim($str);
		$clean = str_replace("&rsquo;", "", $clean);
		$clean = str_replace("&quot;", "", $clean);
		$clean = preg_replace("/[^0-9a-zA-Z\$\-_.+!*'(),]/", '', $clean);
		$clean = strtolower($clean);

		return $clean;
	}
}

//Check for special characters and spaces
if(!function_exists('check_special_chars')){
	function check_special_chars($string){
		if(!preg_match("#^[-A-Za-z\&0-9\&\_;' .]*$#",$string)){
		return true;
		}
		if(strstr($string, " ")){
			return true;
		}
		return false;
	}
}

//Gravatar image
if(!function_exists('render_gravatar')){
	function render_gravatar($image, $link = false, $name = false){
		global $path;
		$img = is_file($image) ? '<span class="gravatar"><img src="'.$path.$image.'" style="height: 100%; width: 100%; object-fit: cover;" /></span>' : '';
		$img = is_file($link) ? '<a href="'.$path.$link.'" class="gravatar-link light-gallery" title="'.$name.'" target="_blank">'.$img.'</a>' : $img;
		return $img;
	}
}

//Get site pages (including dynamic pages)
if(!function_exists('get_site_pages')){
	function get_site_pages($nested = true, $include_external_links = false, $relative_urls = false) {
		global $db;
		global $siteurl;
		global $root;
		global $_sitepages;

		$db->query("SELECT * FROM pages WHERE page_id > 2 ORDER BY ordering");

		if(!$db->error()) {
			$result = $db->fetch_array();
			$pages = array();
			$sitemap = array(); // separate array for non-nested (to prevent infinite looping in the foreach below)

			foreach($result as $row){
				$pages[$row['page_id']] = $row;
				$pages[$row['page_id']]['sub_pages'] = array();
				$pages[$row['page_id']]['page_url'] = (!$relative_urls ? $siteurl : '') . $root . ($row['slug'] != NULL ? $row['slug'] : $row['page']) . '/';
			}

			$pages = build_hierarchy($pages, 'page_id');

			foreach($pages as $page_id => &$page){
				//If parent showhide = 2, make sure all children (recursive) showhide = 2 as well
				if($page['parent_id'] && array_key_exists($page['page_id'], $pages)){
					if($pages[$page['parent_id']]['showhide'] == 2){
						$page['showhide'] = 2;
					}
				}

				//Display only if page and parent are not disabled
				if($page['showhide'] != 2 && (!$page['parent_id'] || $pages[$page['parent_id']]['showhide'] != 2)){
					// Add sub pages under their parent page
					if($page['parent_id'] && array_key_exists($page['parent_id'], $pages)){
						$pages[$page['parent_id']]['sub_pages'][$page_id] = &$page;
						$pages[$page['parent_id']]['sub_pages'][$page_id]['page_url'] = $pages[$page['parent_id']]['page_url'].($page['slug'] != NULL ? $page['slug'] : $page['page']).'/';
						$pages[$page['parent_id']]['sub_pages'][$page_id]['meta_title'] = $page['meta_title'].' | '.$pages[$page['parent_id']]['meta_title'];

						// Set tinyMCE link_list menu
						$pages[$page['parent_id']]['menu'] = $pages[$page['parent_id']]['sub_pages'];
						array_unshift($pages[$page['parent_id']]['menu'], array(
								'title' => $pages[$page['parent_id']]['name'],
								'value' => ($pages[$page['parent_id']]['type'] == 1 ? $pages[$page['parent_id']]['url'] : $pages[$page['parent_id']]['page_url'])
							)
						);
					}

					if(!$nested) {
						$page['url'] = ($page['type'] == 1 ? $page['url'] : $page['page_url']);
						$page['lastmod'] = date('c',strtotime($page['last_modified']));
						$page['priority'] = ($page['parent_id'] ? "0.64" : "0.80");

						if($page['page_id'] == $_sitepages['home']){
							$page['url'] = ($page['slug'] != '' ? $page['url'] : $siteurl);
							$page['priority'] = "1.00";
						}

						if($page['type'] != 1 || $include_external_links){
							$sitemap[] = $page;
						}
					}

					//DYNAMIC PAGES
					//Galleries
					if($page['page_id'] == $_sitepages['gallery']){
						$db->query("SELECT * FROM galleries WHERE showhide != 2");
						$galleries = $db->fetch_array();

						foreach($galleries as $gallery){
							$gallery['page_url'] = $page['page_url'] . $gallery['page'] . "-" . $gallery['gallery_id'] . "/";
							$gallery['url'] = $gallery['page_url'];
							$gallery['lastmod'] = date('c',strtotime($gallery['last_updated']));
							$gallery['priority'] = "0.64";
							$gallery['type'] = 0;
							$gallery['name'] = $gallery['name'];
							$gallery['lvl'] = $page['lvl'] + 1;
							$gallery['sub_pages'] = array();

							// Set tinyMCE link_list properties
							$gallery['title'] = $gallery['name'];
							$gallery['value'] = $gallery['page_url'];

							$page['sub_pages'][] = $gallery;
							$sitemap[] = $gallery;
						}
					} //Galleries

					// Blog pages
					if($page['page_id'] == $_sitepages['blog']){

						// Check blog settings
						$db->query("SELECT show_author, empty_categories FROM blog_settings");
						$show_empty   = !!($db->fetch_array()[0]['empty_categories'] ?? false);
						$show_author  = !!($db->fetch_array()[0]['show_author'] ?? false);


						// Load Recent Entries page into sitemap
						$recent['url']      = $page['page_url']."recent/";
						$recent['lastmod']  = date('c',strtotime($page['last_modified']));
						$recent['priority'] = "0.64";
						$recent['type']     = 0;
						$recent['name']     = 'Most Recent';
						$recent['page_url'] = $recent['url'];
						$recent['lvl']      = $page['lvl'] + 1;

						// Set tinyMCE link_list properties
						$recent['title']    = $recent['name'];
						$recent['value']    = $recent['url'];

						$page['sub_pages'][] = $recent;
						$sitemap[] = $recent;


						// Load Popular Entries page into sitemap
						$popular['url']      = $page['page_url']."popular/";
						$popular['lastmod']  = date('c',strtotime($page['last_modified']));
						$popular['priority'] = "0.64";
						$popular['type']     = 0;
						$popular['name']     = 'Most Popular';
						$popular['page_url'] = $popular['url'];
						$popular['lvl']      = $page['lvl'] + 1;

						// Set tinyMCE link_list properties
						$popular['title']    = $popular['name'];
						$popular['value']    = $popular['url'];

						$page['sub_pages'][] = $popular;
						$sitemap[] = $popular;


						// Get categories
						$db->query("SELECT `blog_categories`.*, count(`blog_entries`.`entry_id`) as count FROM `blog_categories`
						LEFT JOIN `blog_entries` ON `blog_entries`.`category_id` = `blog_categories`.`category_id` AND `blog_entries`.`showhide` = 0
						WHERE `blog_categories`.`showhide` = 0
						GROUP BY `blog_categories`.`category_id`
						ORDER BY `blog_categories`.`ordering`, `blog_categories`.`name`");
						$blog_categories = $db->fetch_assoc('category_id');
						foreach($blog_categories as $category_id => $category){

							// Category has entries or empty categories are being displayed
							if ($category['count'] || $show_empty) {

								// Format category for sitemap
								$category_page['url']      = $page['page_url'].$category['page']."-".$category_id."/";
								$category_page['lastmod']  = date('c',strtotime($category['last_modified']));
								$category_page['priority'] = "0.64";
								$category_page['type']     = 0;
								$category_page['name']     = $category['name'];
								$category_page['page_url'] = $category_page['url'];
								$category_page['lvl']      = $page['lvl'] + 1;

								// Set tinyMCE link_list properties
								$category_page['title']    = $category_page['name'];
								$category_page['value']    = $category_page['url'];

								// Add category to page subpages and load into sitemap
								$page['sub_pages'][] = $category_page;
								$sitemap[] = $category_page;


								// Load blog entries for sitemap
								$db->query("SELECT `blog_entries`.* FROM `blog_entries`
								LEFT JOIN `blog_authors` ON `blog_authors`.`author_id` = `blog_entries`.`author_id`
								WHERE
									`blog_authors`.`showhide` = 0
									AND `blog_entries`.`post_date` <= CURDATE()
									AND `blog_entries`.`showhide` = 0
									AND `blog_entries`.`category_id` = ?
								ORDER BY `blog_entries`.`post_date` DESC", [$category_id]);
								$blog_entries = $db->fetch_assoc('entry_id');
								foreach($blog_entries as $entry_id => $entry){
									$entry_page['url']      = $category_page['url'].$entry['page']."-".$entry['entry_id']."/";
									$entry_page['lastmod']  = date('c', strtotime($entry['last_modified']));
									$entry_page['priority'] = "0.64";
									$entry_page['type']     = 0;
									$entry_page['name']     = $entry['title'];
									$entry_page['page_url'] = $entry_page['url'];
									$entry_page['lvl']      = $page['lvl'] + 2;

									$sitemap[] = $entry_page;
								}
							}
						}

						// Get authors
						if ($show_author) {
							$db->query("SELECT `blog_authors`.*, count(`blog_entries`.`entry_id`) as count FROM `blog_authors`
							LEFT JOIN `blog_entries` ON `blog_entries`.`author_id` = `blog_authors`.`author_id` AND `blog_entries`.`showhide` = 0
							WHERE `blog_authors`.`showhide` = 0 AND `blog_entries`.`post_date` <= CURDATE()
							GROUP BY `blog_authors`.`author_id`
							ORDER BY `blog_authors`.`ordering`, `blog_authors`.`name`");
							$blog_authors = $db->fetch_assoc('author_id');
							foreach($blog_authors as $author_id => $author){

								// Author has entries or empty categories are being displayed
								if ($author['count'] || $show_empty) {

									// Format category for sitemap
									$author_page['url']      = $page['page_url'].$author['page']."-".$author_id."/";
									$author_page['lastmod']  = date('c',strtotime($author['last_updated']));
									$author_page['priority'] = "0.64";
									$author_page['type']     = 0;
									$author_page['name']     = $author['name'];
									$author_page['page_url'] = $author_page['url'];
									$author_page['lvl']      = $page['lvl'] + 1;

									// Set tinyMCE link_list properties
									$author_page['title']    = $author_page['name'];
									$author_page['value']    = $author_page['url'];

									// Add author to page subpages and load into sitemap
									$page['sub_pages'][] = $author_page;
									$sitemap[] = $author_page;
								}
							}
						}


						// Get archives
						if ($show_empty) {
							// Load all archive data regardless of entries
							$i = new DateTime('first day of this month');
							$last_year = clone $i;
							$last_year->modify('-11 months');
							while ($i >= $last_year) {
								$blog_archives[] = $i->format('mY');
								$i->modify('-1 month');
							}

						// Query DB for past year of entry data
						} else {

							// Grab distinct months from entries posted within the last 12 months
							$db->query("SELECT DISTINCT DATE_FORMAT(`post_date`, ?) as archive FROM `blog_entries` WHERE `showhide` = 0 AND `post_date` <= CURDATE() AND `post_date` >= ".
							// The last day of the current date's month, minus 1 month and plus 1 day, which results in the FIRST DAY of the this month
							// Additionally subtract 11 more months, because we're only checking for entries in the last 12 months.
							"DATE_ADD(DATE_ADD(LAST_DAY(CURDATE()), INTERVAL 1 DAY), INTERVAL -12 MONTH)", ['%m%Y']);
							$blog_archives = array_column($db->fetch_array(), 'archive');
						}

						// Load archives data into sitemap
						foreach ($blog_archives ?? [] as $month_year) {
							$date = DateTime::createFromFormat('mY', $month_year);

							// Format category for sitemap
							$archive['url']      = $page['page_url'].$month_year."/";
							$archive['lastmod']  = date('c', strtotime($page['last_modified']));
							$archive['priority'] = "0.64";
							$archive['type']     = 0;
							$archive['name']     = $date->format('F Y');
							$archive['page_url'] = $archive['url'];
							$archive['lvl']      = $page['lvl'] + 1;

							// Set tinyMCE link_list properties
							$archive['title']    = $archive['name'];
							$archive['value']    = $archive['url'];

							// Add category to page subpages and load into sitemap
							$page['sub_pages'][] = $archive;
							$sitemap[] = $archive;
						}
					} // <-- ADD BLOG PAGES TO SITEMAP
				}

				//Set tinyMCE link_list properties
				$page['title'] = $page['name'];
				$page['value'] = ($page['type'] == 1 ? $page['url'] : $page['page_url']);

				if(!empty($page['sub_pages'])) {

					// Add links with title and value to tinyMCE menu
					foreach ($page['sub_pages'] as $key => $subpage) {
						if (isset($subpage['value']) && isset($subpage['title'])) {
							$page['menu'][$key] = $subpage;
						}
					}

					// Prepend current page to tinyMCE menu
					array_unshift($page['menu'], [
						'title' => $page['title'],
						'value' => $page['value']
					]);
				}

				unset($page);
			}

			//Unset the sub pages (since they were already added to sub_pages of their parent page)
			foreach($pages as $page_id => &$page){
				if(!empty($page['parent_id'])){
					unset($pages[$page_id]);
				}

				unset($page);
			}
		}

		// $pages preserves the nested structure of the sitemap. $sitemap does not preserve the nested structure and does not include pages that are linked to other pages ($type must be 0).
		return ($nested ? $pages : $sitemap);
	}
}

//Sitemap xml
if(!function_exists('sitemap_XML')){
	function sitemap_XML(){
		global $root;

		$sitepages = get_site_pages(false);

		$filename = $_SERVER['DOCUMENT_ROOT'].$root."sitemap.xml";
		$doc = new DOMDocument('1.0','UTF-8');
		$doc->formatOutput = true;

		$segment = $doc->createElement("urlset");
		$segment = $doc->appendChild($segment);
		$segment->setAttribute("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9");
		$segment->setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
		$segment->setAttribute("xsi:schemaLocation", "http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd");

		//Build xml
		foreach($sitepages as $s){
			$url = $doc->createElement("url");
			$url = $segment->appendChild($url);

			$loc = $doc->createElement("loc");
			$loc = $url->appendChild($loc);
			$text = $doc->createTextNode($s['url']);
			$text = $loc->appendChild($text);

			$lastmod = $doc->createElement("lastmod");
			$lastmod = $url->appendChild($lastmod);
			$text = $doc->createTextNode($s['lastmod']);
			$text = $lastmod->appendChild($text);

			$changefreq = $doc->createElement("changefreq");
			$changefreq = $url->appendChild($changefreq);
			$text = $doc->createTextNode("weekly");
			$text = $changefreq->appendChild($text);

			$priority = $doc->createElement("priority");
			$priority = $url->appendChild($priority);
			$text = $doc->createTextNode($s['priority']);
			$text = $priority->appendChild($text);
		}

		$doc->save($filename);
	}
}

//sitemap HTML
if(!function_exists('sitemap_HTML')){
	function sitemap_HTML($sitepages = array(), $table_format = false, $display_url = false, $copy_link = false, $view_link = false) {
		global $siteurl;

		$sitemap_html = '';

		if(!$table_format) {
			$sitepages = $sitepages ?: get_site_pages(false, true);

			foreach($sitepages as $nav){
				$sitemap_html .= '<li>';
					if($nav['type'] == "1"){
						$nav['anchor'] = '<a href="' .$nav['url']. '" target="_blank">' .$nav['name']. '</a>';
					}else{
						$nav['anchor'] = '<a href="' .$nav['page_url']. '" target="_blank">' .$nav['name']. '</a>';
					}

					$sitemap_html .= $nav['anchor'];
					$sitemap_html .= ($display_url ? ' - <small>' . ($nav['type'] == "1" ? $nav['url'] : $nav['page_url']) . '</small>' : '');
					$sitemap_html .= ($copy_link ? ' <small><a class="copy-to-clipboard button-xsm" href="#" data-copy="' . ($nav['type'] == "1" ? $nav['url'] : $nav['page_url']) . '"><span class="fas fa-clipboard"></span>&nbsp;Copy Link</a></small>' : '');
					$sitemap_html .= ($view_link ? ' <small class=""><a class="view-page button-xsm" href="' . ($nav['type'] == "1" ? $nav['url'] : $nav['page_url']) . '" target="_blank">View Page&nbsp;<span class="fas fa-external-link-alt"></span></a></small>' : '');

					//Sub Pages
					if(is_array($nav['sub_pages']) && count($nav['sub_pages']) > 0){
						$sitemap_html .= '<ul>';
							sitemap_HTML($nav['sub_pages'], false, $display_url, $copy_link, $view_link);
						$sitemap_html .= '</ul>';
					}
				$sitemap_html .= '</li>';
			} // close foreach($pages as $nav)
		} // close if(!$table_format)
		else {
			$sitepages = $sitepages ?: get_site_pages(false, true, true);

			foreach($sitepages as $nav){
				if($nav['type'] == "1"){
					$nav['anchor'] = '<a href="' .$nav['url']. '" target="_blank">' .$nav['name']. '</a>';
				}else{
					$nav['anchor'] = '<a href="' .$nav['page_url']. '" target="_blank">' .$nav['name']. '</a>';
				}

				//Determine child level/parent status of page
				$sitemap_html .= "<tr class='level-" . $nav['lvl'] . "'>";

					$sitemap_html .= "<td class='page-name'>" . $nav['anchor'] . "</td>";
					$sitemap_html .= ($display_url ? '<td><small>' . ($nav['type'] == "1" ? $nav['url'] : $nav['page_url']) . '</small></td>' : '');
					$sitemap_html .= "<td class='right'>";
						$sitemap_html .= ($copy_link ? ' <a class="copy-to-clipboard sitemap-button button-sm" title="Copy Link" href="#" data-copy="' . ($nav['type'] == "1" ? $nav['url'] : $nav['page_url']) . '"><span class="fas fa-clipboard"></span></a>' : '');
						$sitemap_html .= ($view_link ? ' <a class="view-page sitemap-button button-sm" title="View Page" href="' . ($nav['type'] == "1" ? $nav['url'] : $nav['page_url']) . '" target="_blank"><span class="fas fa-external-link-alt"></span></a>' : '');
					$sitemap_html .= "</td>";

				$sitemap_html .= "</tr>";
			} // close foreach($pages as $nav)
		} // close else (if $table_format)

		return $sitemap_html;
	}
}

//Limit characters in a string
if(!function_exists('str_limit_characters')){
	function str_limit_characters($string, $maxlength=140){
		if(trim(strip_tags($string)) != ''){
			$string = strip_tags($string);
			if(strlen($string) > $maxlength){
			    $string = substr($string, 0, $maxlength); // truncate string
			    $string = substr($string, 0, strrpos($string, ' ')).' &hellip;'; // make sure it ends in a word
			}
		}else{
			$string = '';
		}

		return $string;
	}
}

//Generate SEO title from page ID and optional custom title
if(!function_exists('generate_seo_title')){
	function generate_seo_title(int $page_id, $title = ''){
		global $db;

		$db->query("SELECT IF(meta_title, meta_title, page_title) AS seo_title FROM pages WHERE page_id = ?", [$page_id]);
		$seo_title = $db->fetch_array()[0]['seo_title'] ?? '';

		return implode(" | ", array_filter([$title, $seo_title]));
	}
}


//Get page url
if(!function_exists('get_page_url')){
	function get_page_url($page_id, $page_url=''){
		global $db;

		$db->query("SELECT page_id, parent_id, page, slug, type, url FROM pages WHERE page_id = ?", [$page_id]);
		if(!$db->error() && $db->num_rows() > 0){
			$result = $db->fetch_array();
			$row = $result[0];

			$page_type = 'page';

			//External link, get url
			if($row['type'] == 1){
				$page_type = 'url';
			}

			//Getting url of parent, always get the page
			if(trim($page_url) != ''){
				$page_type = 'page';
			}else{
				if($page_type == 'url'){
					$row['parent_id'] = '';
				}
			}

			//Normal Page
			if($page_type == 'page'){
				$page_url = (trim($row['slug']) != '' ? $row['slug'] : $row['page']).'/';
			//External Link
			} else if($page_type == 'url'){
				$page_url = $row['url'];
			}

			//No Parent
			if($row['parent_id'] == '' || $row['parent_id'] == 0){
				return $page_url;
			//Has Parent
			}else{
				return get_page_url($row['parent_id'], $page_url).$page_url;
			}
		}else{
			return $page_url;
		}
	}
}

//Extract youtube video id from url
if(!function_exists('youtube_id_from_url')){
	function youtube_id_from_url($url){
		$pattern =
			'%^# Match any youtube URL
			(?:https?://)?  # Optional scheme. Either http or https
			(?:www\.)?      # Optional www subdomain
			(?:             # Group host alternatives
			  youtu\.be/    # Either youtu.be,
			| youtube\.com  # or youtube.com
			  (?:           # Group path alternatives
				/embed/     # Either /embed/
			  | /v/         # or /v/
			  | /watch\?v=  # or /watch\?v=
			  )             # End path alternatives.
			)               # End host alternatives.
			([\w-]{10,12})  # Allow 10-12 for 11 char youtube id.
			$%x'
			;
		$result = preg_match($pattern, $url, $matches);
		if($result){
			return $matches[1];
		}
		return false;
	}
}

//Create XML feed for Indeed
if(!function_exists('careers_XML')){
	function careers_XML($page_id){

		global $db;
		global $siteurl;
		global $root;
		global $global;

		$careers_page['page_url'] = get_page_url($page_id);

		$filename = $_SERVER['DOCUMENT_ROOT'].$root."indeed.xml";
		$fh = fopen($filename, 'w') or die("can't open file");

		$rss_txt = '<?xml version="1.0" encoding="utf-8"?>';
		$rss_txt .= '<source>';
		$rss_txt .= '<publisher>'.htmlentities($global['company_name']).'</publisher>
		    <publisherurl>'.$siteurl.'</publisherurl>
		    <lastBuildDate>'.date(DATE_RSS,strtotime("now")).'</lastBuildDate>'.PHP_EOL;

			$db->query("SELECT `careers`.*, `locations`.`city`, `locations`.`province`, `locations`.`country` FROM `careers` INNER JOIN `locations` ON `careers`.`location_id` = `locations`.`location_id` WHERE `careers`.`showhide` = 0 AND `locations`.`showhide` = 0 AND (`careers`.`date_closing` IS NULL || `careers`.`date_closing` > ?) ORDER BY `careers`.`date_posting` DESC", array(date('Y-m-d')));
			$careers = $db->fetch_array();
			foreach($careers as $career) {
				$rss_txt .= '<job>'.PHP_EOL;

				$rss_txt .= '<title><![CDATA['.$career['title'].']]></title>'.PHP_EOL;
				$rss_txt .= '<date>'.date("D, j M Y G:i:s T",strtotime($career['date_posting'])).'</date>'.PHP_EOL;
				$rss_txt .= '<referencenumber><![CDATA[JOB-'.date("y",strtotime($career['date_posting'])).$career['career_id'].']]></referencenumber>'.PHP_EOL;
				$rss_txt .= '<url><![CDATA['.$siteurl.$root.$careers_page['page_url'].$career['page']."-".$career['career_id'].'/]]></url>';
				$rss_txt .= "<company><![CDATA[".$global['company_name']."]]></company>".PHP_EOL;
				$rss_txt .= '<city><![CDATA['.$career['city'].']]></city>'.PHP_EOL;
				$rss_txt .= '<state><![CDATA['.$career['province'].']]></state>'.PHP_EOL;
				$rss_txt .= '<country><![CDATA['.$career['country'].']]></country>'.PHP_EOL;
				$rss_txt .= '<description><![CDATA[
				'.str_replace("'","&rsquo;",$career['content']).'
				]]></description>'.PHP_EOL;
				if($career['salary'] != "" && $career['salary'] > 0){
					$rss_txt .= '<salary><![CDATA[$'.$career['salary'].' '.$career['salary_delimination'].']]></salary>'.PHP_EOL;
				}
				if($career['job_type'] != ""){
					$rss_txt .= '<jobtype><![CDATA['.$career['job_type'].']]></jobtype>'.PHP_EOL;
				}
				$rss_txt .= '</job>'.PHP_EOL;
			}

		$rss_txt .= '</source>';

		fwrite($fh, $rss_txt);
		fclose($fh);
	}
}

// Generate RSS Feed
if(!function_exists('blog_rss')) {
	function blog_rss($filepath = false) {
		global $root;
		global $db;
		global $siteurl;
		global $_sitepages;

		$success  = false;
		$filepath = $filepath ?: $_SERVER['DOCUMENT_ROOT'].$root."blog-rss.xml";

		// Load blog page data
		$db->query("SELECT * FROM pages WHERE page_id = ?", [$_sitepages['blog']]);
		$page = $db->fetch_array();
		$page = reset($page);

		if ($page) {

			$fh = fopen($filepath, 'w');
			$page['page_url'] = get_page_url($_sitepages['blog']);

			$rss = '<?xml version="1.0" encoding="utf-8"?>'.PHP_EOL;
			$rss .= '<rss version="2.0">'.PHP_EOL;
				$rss .= "\t<channel>".PHP_EOL;
					$rss .= "\t\t<title>".htmlspecialchars(html_entity_decode($page['name']))."</title>".PHP_EOL;
					$rss .= "\t\t<description>".htmlspecialchars(html_entity_decode($page['description'] ?: $page['meta_description'] ?: 'Blog Posts from '.$siteurl))."</description>".PHP_EOL;
					$rss .= "\t\t<link>".$siteurl.$root.$page['page_url']."recent/</link>".PHP_EOL;
					$rss .= "\t\t<lastBuildDate>".date(DATE_RSS, strtotime("now"))."</lastBuildDate>".PHP_EOL;


			// Query all valid blog entries
			$db->query("SELECT blog_entries.*, blog_categories.page as catpage FROM blog_entries
			LEFT JOIN blog_categories ON blog_entries.category_id = blog_categories.category_id
			LEFT JOIN blog_authors ON blog_authors.author_id = blog_entries.author_id
			WHERE
				(blog_authors.showhide = 0 || blog_authors.showhide IS NULL)
				AND (blog_categories.showhide = 0 || blog_categories.showhide IS NULL)
				AND blog_entries.showhide = 0
				AND blog_entries.post_date <= CURDATE()
			GROUP BY blog_entries.entry_id
			ORDER BY blog_entries.post_date DESC");
			$entries = $db->fetch_assoc('entry_id');

			foreach($entries as $entry_id => $entry) {
				$entry['page_url'] = $siteurl.$root.$page['page_url'];
				$entry['page_url'] .= !empty($entry['category_id']) ? $entry['catpage'].'-'.$entry['category_id']."/" : '';
				$entry['page_url'] .= $entry['page']."-".$entry['entry_id']."/";

				// RSS Code
				$rss .= "\t\t<item>".PHP_EOL;
					$rss .= "\t\t\t<title>".htmlspecialchars(html_entity_decode($entry['title']))."</title>".PHP_EOL;
					$rss .= "\t\t\t<link>".$entry['page_url']."</link>".PHP_EOL;
					$rss .= "\t\t\t<guid>".$entry['page_url']."</guid>".PHP_EOL;
					$rss .= "\t\t\t<pubDate>".date(DATE_RSS, strtotime($entry['post_date']))."</pubDate>".PHP_EOL;
					$rss .= "\t\t\t<description>".htmlspecialchars(html_entity_decode($entry['description']))."</description>".PHP_EOL;
				$rss .= "\t\t</item>".PHP_EOL;
			}

				$rss .= "\t</channel>".PHP_EOL;
			$rss .= "</rss>".PHP_EOL;

			// Write to RSS file
			$success = fwrite($fh, $rss);
			fclose($fh);
		}

		return $success;
	}
}

//Strip special chars from phone numbers
if(!function_exists('stripPhoneNumber')){
	function stripPhoneNumber($number){
		$return = str_replace(array('+','-','.',')','(',' '), "", stripslashes($number));		
		return trim($return);
	}
}
//Format phone number for display
if(!function_exists('formatPhoneNumber')){
	function formatPhoneNumber($number){
			
		//get ext
		$ext = trim(substr(strrchr($number, 'ext'), 4));
		
		//remove ext. if exists
		if($ext != ""){
			$number = strstr($number, 'ext', true);
		}
				
		//format number for blank canvas
		$number = stripPhoneNumber($number);
		
		//check if number starts with 1
		if(substr($number, 0, 1) == '1'){
			//remove it
			$number = substr($number, 1);
		}
		
		//grab last 7 digits (for canada/us numbers)
		$number = substr($number, 0, 3)."-".substr($number, 3, 3)."-".substr($number, 6, 4).($ext != "" ? ";ext=".$ext : "");
		
		return trim($number);
	}
}

//Format file size
if(!function_exists('formatBytes')){
	function formatBytes($size){ 
		$base = log($size) / log(1024);
		$suffix = array("", "KB", "MB", "GB", "TB");
		$f_base = floor($base);
		return round(pow(1024, $base - floor($base)), 1).' '.$suffix[$f_base];
	} 
}

//Format date range
if(!function_exists('format_date_range')){
	function format_date_range($start_date, $end_date, $format_m='F', $format_d='j', $format_y='Y'){
		
		if(trim($end_date) == ''){ $end_date = $start_date; }
		
		$start_day = date("d", strtotime($start_date));
		$start_month = date("m", strtotime($start_date));
		$start_year = date("Y", strtotime($start_date));
		
		$end_day = date("d", strtotime($end_date));
		$end_month = date("m", strtotime($end_date));
		$end_year = date("Y", strtotime($end_date));
		
		if($format_y != ''){
			$format_y = ', '.$format_y;
		}
		
		//if same day
		if($start_date == $end_date){
			return date("$format_m $format_d$format_y", strtotime($start_date));
		}
		
		//if same month/year
		if($start_month == $end_month && $start_year == $end_year){
			return date("$format_m $format_d", strtotime($start_date)). ' - ' .date("$format_d$format_y", strtotime($end_date));
		}
		
		//if same year
		if($start_year == $end_year){
			return date("$format_m $format_d", strtotime($start_date)). ' - ' .date("$format_m $format_d$format_y", strtotime($end_date));
		}
		
		//basic format
		return date("$format_m $format_d$format_y", strtotime($start_date)). ' - ' .date("$format_m $format_d$format_y", strtotime($end_date));
		
	}
}

//Gravatar image
if(!function_exists('renderGravatar')){
	function renderGravatar($image){
		global $path;
		if(is_file($image)){
			$dims = getimagesize($image);
			if ($dims[0]>=$dims[1]){
				$img = "<img src='".$path.$image."' style='max-height:40px' />";						
			}else{					
				$img = "<img src='".$path.$image."' style='max-width: 40px;' />";
			}		
		}else{
			$img = '';
		}
		return "<span class='gravatar'>" .$img. "</span>";
	}	
}

//Detect if string is a phone number
if(!function_exists('detectPhone')){
	function detectPhone($string) {
		if(!preg_match("/[a-z]/i", $string)){
			preg_match("/\+?[0-9][0-9()\-\s+]{4,20}[0-9]/i", $string, $matches);
			if(isset($matches) && !empty($matches)){
				return $matches[0];
			}
		}
		return false;
	}
}

//Detect credit card type from number
if(!function_exists('get_card_type')){
	function get_card_type($str, $format='string'){
		if(empty($str)){
			return false;
		}
		$matchingPatterns = array(
			'VISA' => '/^4[0-9]{0,}$/',																			
			'MC' => '/^(5[1-5]|222[1-9]|22[3-9]|2[3-6]|27[01]|2720)[0-9]{0,}$/',
			'AMEX' => '/^3[47][0-9]{0,}$/', 
			'Diners' => '/^3(?:0[0-59]{1}|[689])[0-9]{0,}$/',
			'Discover' => '/^(6011|65|64[4-9]|62212[6-9]|6221[3-9]|622[2-8]|6229[01]|62292[0-5])[0-9]{0,}$/',
			'JCB' => '/^(?:2131|1800|35)[0-9]{0,}$/',
			'Maestro' => '/^(5[06789]|6)[0-9]{0,}$/'
		);
		$ctr = 1;
		foreach($matchingPatterns as $key=>$pattern){
			if(preg_match($pattern, $str)){
				return $format == 'string' ? $key : $ctr;
			}
			$ctr++;
		}
		
		//No match
		return ($format == 'string' ? 'Unknown' : false);
	}
}

//Get database enum values
if(!function_exists('get_enum_vals')){
	function get_enum_vals($record_db, $record_id){
		global $db;

		$response = array();
		$get_enumopts = $db->query("SHOW COLUMNS FROM `".$record_db."` WHERE FIELD = ?", array($record_id));
		if($get_enumopts) {
			$opts = $db->fetch_array();
			$opts = explode("','",preg_replace("/(enum|set)\('(.+?)'\)/","\\2", $opts[0]['Type']));
			foreach($opts as $inquiry_type){
				$response[] = $inquiry_type;
			}
		}

		return $response;
	}
}

//Return unique value for db
if(!function_exists('get_unique_db_val')){
	function get_unique_db_val($table, $field, $length=32) {
		global $db;
		
		$unique_id = md5(rand());

		$query = $db->query("SELECT COUNT(*) AS `total` FROM `$table`");
		if($query && !$db->error()) {
			$result = $db->fetch_array();
			$total = $result[0]['total'];

			// has rows, create unique id
			if($total > 0) {
				$query = $db->query("SELECT MD5(RAND()) AS rand_id FROM `$table` WHERE ? NOT IN (SELECT `$field` FROM `$table` WHERE `$field` IS NOT NULL AND `$field` <> ?) LIMIT 1", array('rand_id', ''));
				if($query && !$db->error()) {
					$result = $db->fetch_array();
					$unique_id = $result[0]['rand_id'];
				}
			} 
		}

		return substr($unique_id,0,$length);
	}
}
?>