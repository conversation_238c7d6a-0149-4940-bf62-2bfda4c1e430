@charset "utf-8";
/*
	global.less

*/
/*------ imports ------*/
/*
	core.less

*/
/*------ responsive ------*/
/*------ imports ------*/
/* 
	mixins.less

*/
/*------ utilities ------*/
/*------ typography ------*/
.light {
  font-weight: 300;
}
.regular {
  font-weight: 400;
}
.medium {
  font-weight: 500;
}
.semibold {
  font-weight: 600;
}
.bold {
  font-weight: 700;
}
.extrabold {
  font-weight: 800;
}
.black {
  font-weight: 900;
}
.uppercase {
  text-transform: uppercase;
}
.capitalize {
  text-transform: capitalize;
}
.strikethrough {
  text-decoration: line-through;
}
.underline {
  text-decoration: underline;
}
/*------ forms ------*/
/*------ display ------*/
.full {
  width: 100%;
}
.half {
  width: 50%;
}
.auto {
  width: auto;
}
.auto-width {
  width: auto !important;
}
.auto-height {
  height: auto !important;
}
.sr-only {
  position: absolute;
  margin: 0;
  padding: 0;
  border: 0;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  background: none;
  overflow: hidden;
}
.f_right {
  float: right;
  display: block;
}
.f_left {
  float: left;
  display: block;
}
.clear {
  display: block;
  clear: both;
}
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}
.right {
  text-align: right;
}
.left {
  text-align: left;
}
.center {
  text-align: center;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.block {
  display: block !important;
}
.inline-block {
  display: inline-block !important;
}
.inline {
  display: inline !important;
}
.hidden {
  display: none !important;
}
@media all and (max-width: 480px) {
  .show-tablet-p {
    display: none !important;
  }
}
@media all and (max-width: 768px) {
  .show-tablet-l {
    display: none !important;
  }
}
@media all and (max-width: 1024px) {
  .show-notebook {
    display: none !important;
  }
}
@media all and (max-width: 1366px) {
  .show-desktop {
    display: none !important;
  }
}
@media all and (max-width: 1920px) {
  .show-widescreen {
    display: none !important;
  }
}
@media all and (min-width: 481px) {
  .hide-tablet-p {
    display: none !important;
  }
}
@media all and (min-width: 769px) {
  .hide-tablet-l {
    display: none !important;
  }
}
@media all and (min-width: 1025px) {
  .hide-notebook {
    display: none !important;
  }
}
@media all and (min-width: 1367px) {
  .hide-desktop {
    display: none !important;
  }
}
@media all and (min-width: 1921px) {
  .hide-widescreen {
    display: none !important;
  }
}
.noborder {
  border: 0 !important;
}
.nobg {
  background: none !important;
}
.nomargin {
  margin: 0 !important;
}
.nomargin-v,
.nomargin-t {
  margin-top: 0 !important;
}
.nomargin-h,
.nomargin-r {
  margin-right: 0 !important;
}
.nomargin-v,
.nomargin-b {
  margin-bottom: 0 !important;
}
.nomargin-h,
.nomargin-l {
  margin-left: 0 !important;
}
.nopadding {
  padding: 0 !important;
}
.nopadding-v,
.nopadding-t {
  padding-top: 0 !important;
}
.nopadding-h,
.nopadding-r {
  padding-right: 0 !important;
}
.nopadding-v,
.nopadding-b {
  padding-bottom: 0 !important;
}
.nopadding-h,
.nopadding-l {
  padding-left: 0 !important;
}
/*------ gradients ------*/
/*------ flexbox ------*/
/*------ columns ------*/
/* For elements within a .multi-column element. Prevents elements from breaking into multiple columns */
/*------ filters ------*/
/*------ transformations ------*/
/*------ animations ------*/
/*------ reset ------*/
* {
  margin: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
body,
html {
  width: 100%;
  height: 100%;
  text-align: left;
}
main,
article,
aside,
details,
figcaption,
figure,
picture,
footer,
header,
hgroup,
menu,
nav,
section,
label {
  display: block;
}
input,
select,
textarea,
button {
  color: inherit;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
input,
select,
textarea,
button,
th,
td {
  font-size: inherit;
  font-family: inherit;
  line-height: normal;
  letter-spacing: inherit;
}
th,
td {
  text-align: inherit;
  line-height: inherit;
}
button {
  background: none;
  border: 0;
  padding: 0;
  cursor: pointer;
  -webkit-transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, color 0.3s ease 0s, opacity 0.3s ease 0s;
  transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, color 0.3s ease 0s, opacity 0.3s ease 0s;
}
select {
  background: none;
  text-overflow: ellipsis;
}
fieldset {
  padding: 0;
  border: 0;
}
/*------ typography ------*/
body,
input,
select,
textarea,
button,
th,
td {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
p,
ul,
ol {
  padding: 0 0 20px;
  margin: 0;
}
ul,
ol {
  margin: 0 0 0 40px;
}
ul ul,
ul ol,
ol ul,
ol ol {
  padding-bottom: 0;
}
ol ol {
  list-style-type: lower-alpha;
}
ol ol ol {
  list-style-type: lower-roman;
}
a {
  text-decoration: none;
  cursor: pointer;
  -webkit-transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, color 0.3s ease 0s, opacity 0.3s ease 0s;
  transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, color 0.3s ease 0s, opacity 0.3s ease 0s;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  padding: 0;
}
h1 {
  margin: 0;
}
h2 {
  margin: 0 0 30px;
}
h3 {
  margin: 0 0 25px;
}
h4 {
  margin: 0 0 20px;
}
h5 {
  margin: 0 0 15px;
}
h6 {
  margin: 0 0 10px;
}
p + h2,
ul + h2,
ol + h2,
table + h2,
blockquote + h2 {
  margin-top: 20px;
}
p + h3,
ul + h3,
ol + h3,
table + h3,
blockquote + h3 {
  margin-top: 15px;
}
p + h4,
ul + h4,
ol + h4,
table + h4,
blockquote + h4 {
  margin-top: 10px;
}
p + h5,
ul + h5,
ol + h5,
table + h5,
blockquote + h5 {
  margin-top: 5px;
}
small {
  display: inline-block;
}
blockquote p {
  padding: 0;
}
/*------ interface ------*/
img,
a.embed-media {
  display: inline-block;
  border: 0;
  max-width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
img:where([width][height]) {
  height: auto;
}
iframe {
  max-width: 100%;
}
hr {
  height: 0;
  margin: 0 0 20px 0;
  padding: 0;
  border: 1px solid;
  border-width: 1px 0 0;
}
table {
  border-collapse: collapse;
  border-style: solid;
  margin: 10px 0 30px;
}
table tbody,
table thead,
table tr,
table th,
table td {
  text-align: inherit;
  border-color: inherit;
  border-style: inherit;
  border-collapse: inherit;
  border-width: inherit;
}
table:where(:not([border])) {
  border-width: 1px;
}
table.responsive .table-header {
  display: none;
  margin: 0;
}
table.column {
  width: calc(100% + 20px) !important;
  margin: 0;
  padding: 0;
  table-layout: fixed;
}
table.column,
table.column td,
table.column th {
  background-color: transparent !important;
  height: auto !important;
}
table.column:not(.mce-item-table) td,
table.column:not(.mce-item-table) th,
table.column {
  border: none !important;
}
table.column td {
  padding: 0;
  vertical-align: top;
}
@media all and (min-width: 769px) {
  table.column:not(.mce-item-table) {
    margin: 0 -10px;
  }
  table.column td {
    padding: 0 10px;
  }
}
@media all and (max-width: 768px) {
  table.column ul:only-child,
  table.column ol:only-child {
    padding-bottom: 0px;
  }
  table.column td:last-child ul:only-child,
  table.column td:last-child ol:only-child {
    padding-bottom: 20px;
  }
  table {
    width: 100% !important;
    border: 1px solid;
  }
  table.responsive tr.header-row,
  table.responsive th {
    display: none;
  }
  table.responsive tr:not(:last-child) td:last-child,
  table.responsive tr:not(:last-child) th:last-child {
    border-bottom-width: 0px;
  }
  table.responsive td {
    display: block;
    width: auto !important;
    text-align: left;
  }
  table.responsive .table-header {
    display: inline-block;
  }
}
/*---- embed media ----*/
a.embed-media {
  position: relative;
  display: inline-block;
  max-width: 100%;
}
a.embed-media img {
  display: block;
}
a.embed-media .play {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 36px;
  font-weight: normal;
  font-style: normal;
  text-align: center;
  line-height: 1;
  z-index: 1;
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
  -webkit-transition: background-color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
a.embed-media .play::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f04b";
  text-shadow: 0 0 8px rgba(0, 0, 0, 0.16);
}
a.embed-media:hover .play {
  background-color: rgba(0, 0, 0, 0.5);
}
iframe.embed-media {
  display: inline-block;
  background-color: #0f0f0f;
}
/*---- dialog ----*/
.ui-dialog {
  position: absolute;
  visibility: hidden;
  overflow: hidden;
  top: 0;
  left: 0;
}
/*---- light gallery ----*/
.lg-outer img {
  -o-object-fit: cover;
     object-fit: cover;
}
/*---- page structure ----*/
#page-wrapper {
  position: relative;
  width: 100%;
  height: auto;
}
#seo-wrapper {
  position: relative;
  z-index: 1;
}
#sitemap .menu-header {
  display: none;
}
.panel-text:after {
  content: "";
  display: table;
  clear: both;
}
.panel-text > :where(:last-child) {
  margin-bottom: 0;
  padding-bottom: 0;
}
/*
	definitions.less

*/
:root {
  --container-width: 930px;
  --container-width-lg: 1240px;
  --container-width-xl: 1560px;
  --container-margin: max(0px, (100% - var(--container-width) - var(--container-padding)*2) / 2);
  --container-margin-lg: max(0px, (100% - var(--container-width-lg) - var(--container-padding)*2) / 2);
  --container-margin-xl: max(0px, (100% - var(--container-width-xl) - var(--container-padding)*2) / 2);
  --container-padding: 20px;
  --container-padding: clamp(20px, 3.90625vw - 10px, 30px);
  --font-h1: 48px;
  --font-h1: clamp(48px, 2.17028vw + 31.33222px, 61px);
  --font-h2: 40px;
  --font-h2: clamp(40px, 1.5025vw + 28.46077px, 49px);
  --font-h3: 33px;
  --font-h3: clamp(33px, 1.00167vw + 25.30718px, 39px);
  --font-h4: 28px;
  --font-h4: clamp(28px, 0.50083vw + 24.15359px, 31px);
  --font-h5: 23px;
  --font-h5: clamp(23px, 0.33389vw + 20.43573px, 25px);
  --font-h6: 19px;
  --font-h6: clamp(19px, 0.16694vw + 17.71786px, 20px);
  --font-blockquote: 22px;
  --font-blockquote: clamp(22px, 0.66778vw + 16.87145px, 26px);
  --font-paragraph: 16px;
  --font-paragraph: clamp(16px, 0vw + 16px, 16px);
  --font-caption: 13px;
  --font-caption: clamp(13px, 0vw + 13px, 13px);
  --font-footnote: 11px;
  --line-height-thin: 1.2;
  --line-height-normal: 1.4;
  --line-height-thick: 1.7;
}
/*------ typography ------*/
body {
  font-family: "Poppins", sans-serif;
  color: #020202;
  line-height: var(--line-height-thick);
  letter-spacing: -0.02em;
  background: #FFFFFF;
}
.font-footnote {
  font-size: var(--font-footnote);
}
.font-caption,
small,
.button.simple {
  font-size: var(--font-caption);
}
.font-paragraph,
body,
.button {
  font-size: var(--font-paragraph);
}
.font-blockquote,
blockquote {
  font-size: var(--font-blockquote);
}
.font-h6,
h6 {
  font-size: var(--font-h6);
}
.font-h5,
h5 {
  font-size: var(--font-h5);
}
.font-h4,
h4 {
  font-size: var(--font-h4);
}
.font-h3,
h3 {
  font-size: var(--font-h3);
}
.font-h2,
h2 {
  font-size: var(--font-h2);
}
.font-h1,
h1 {
  font-size: var(--font-h1);
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Lora", sans-serif;
  word-break: break-word;
}
h1 {
  font-weight: 800;
  line-height: var(--line-height-thin);
  letter-spacing: -0.05em;
}
h2 {
  margin-bottom: 20px;
  font-weight: 700;
  line-height: var(--line-height-thin);
  letter-spacing: -0.05em;
}
h3 {
  font-weight: 700;
  color: #16212F;
  line-height: var(--line-height-thin);
  letter-spacing: -0.05em;
}
h4 {
  font-weight: 700;
  color: #EF3E34;
  line-height: var(--line-height-normal);
  letter-spacing: -0.05em;
}
h5 {
  font-weight: 700;
  color: #16212F;
  line-height: var(--line-height-normal);
  letter-spacing: -0.02em;
}
h6 {
  font-weight: 700;
  color: #16212F;
  line-height: var(--line-height-normal);
  letter-spacing: -0.02em;
}
small {
  font-family: "Lora", sans-serif;
  color: #999999;
}
/*------ interface ------*/
:target {
  scroll-margin-top: 100px;
}
::-moz-selection {
  color: #EF3E34;
}
::selection {
  color: #EF3E34;
}
hr {
  border-color: #DDDDDD;
}
a {
  word-break: break-word;
}
blockquote {
  position: relative;
  margin-block: 20px;
  margin-block: clamp(20px, 6.6778vw - 31.28548px, 60px);
  padding-left: 0px;
  padding-left: clamp(0px, 8.34725vw - 64.10684px, 50px);
  color: #16212F;
  line-height: var(--line-height-thick);
  letter-spacing: -0.02em;
  font-family: 'contralto-big';
}
blockquote::before {
  content: '';
  background-image: url("../../images/quote.png");
  background-repeat: no-repeat;
  position: absolute;
  top: -10px;
  left: 5px;
  left: clamp(5px, 0.83472vw - 1.41068px, 10px);
  font-size: 1250px;
  font-style: normal;
  font-weight: 700;
  color: #B3C6BB;
  line-height: 0.6;
  letter-spacing: -0.05em;
  z-index: 2;
  width: 74px;
  height: 150px;
}
blockquote small {
  font-weight: 400;
  font-style: normal;
}
p + blockquote {
  margin-top: 0px;
  margin-top: clamp(0px, 6.6778vw - 51.28548px, 40px);
}
table th,
table td {
  color: #020202;
  padding: 10px 20px;
}
table.responsive label,
table th {
  color: #16212F;
  font-weight: 700;
}
@-webkit-keyframes slideInTopBorder {
  0% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@keyframes slideInTopBorder {
  0% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@-webkit-keyframes slideInBottomBorderDouble {
  0% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  40% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  41% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@keyframes slideInBottomBorderDouble {
  0% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  40% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  41% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@-webkit-keyframes slideInTopBorderContinue {
  0% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@keyframes slideInTopBorderContinue {
  0% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@-webkit-keyframes slideInBottomBorderDoubleContinue {
  0% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  40% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  41% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@keyframes slideInBottomBorderDoubleContinue {
  0% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  40% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  41% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
/* Left border animation (bottom to top, twice) */
@-webkit-keyframes leftBorderAnimation {
  0% {
    height: 0%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  40% {
    height: 100%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  41% {
    height: 0%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  100% {
    height: 100%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}
@keyframes leftBorderAnimation {
  0% {
    height: 0%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  40% {
    height: 100%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  41% {
    height: 0%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  100% {
    height: 100%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}
/* Right border animation (top to bottom, once) */
@-webkit-keyframes rightBorderAnimation {
  0% {
    height: 0%;
    top: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  100% {
    height: 100%;
    top: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}
@keyframes rightBorderAnimation {
  0% {
    height: 0%;
    top: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  100% {
    height: 100%;
    top: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}
@-webkit-keyframes leftBorderSlideUp {
  60% {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  40% {
    height: 100%;
    bottom: 0;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  41% {
    height: 0%;
    bottom: 0;
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes leftBorderSlideUp {
  60% {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  40% {
    height: 100%;
    bottom: 0;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  41% {
    height: 0%;
    bottom: 0;
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@-webkit-keyframes rightBorderSlideDown {
  0% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes rightBorderSlideDown {
  0% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
.button {
  --text-hover: #FFFFFF;
  --bg: #EF3E34;
  --bg-hover: #16212F;
  --border: var(--bg);
  --border-hover: var(--bg-hover);
  display: inline-block;
  width: auto;
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: var(--line-height-thin);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  overflow: hidden;
  padding: 18px 30px;
  -webkit-box-shadow: none;
  box-shadow: none;
  text-shadow: none;
  -webkit-transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, opacity 0.3s ease 0s, color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, opacity 0.3s ease 0s, color 0.3s ease 0s;
  --text: #EF3E34;
  --border-color: #000000;
  --border-color-light: #FFFFFF;
  --border-color-dark: #000000;
  position: relative;
  padding: 15px 30px 12px;
  background: transparent;
  border: none;
  color: var(--text);
  text-transform: uppercase;
}
.button .fa,
.button .far,
.button .fab,
.button .fas,
.button span.mailto::before,
.button span.phone::before {
  line-height: 1;
  margin-right: 8px;
}
.button span.phone::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f3cd";
}
.button span.mailto::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f0e0";
}
.button.small {
  padding-block: 13px;
  padding-block: clamp(13px, 0.33389vw + 10.43573px, 15px);
  padding-inline: 15px;
  padding-inline: clamp(15px, 0.83472vw + 8.58932px, 20px);
}
.button.simple {
  --border: #B3C6BB;
  --bg: #FFFFFF;
  --text: #EF3E34;
  --bg-hover: #EF3E34;
  --text-hover: #FFFFFF;
  font-weight: 700;
  padding-block: 13px;
  padding-block: clamp(13px, 0.33389vw + 10.43573px, 15px);
  padding-inline: 15px;
  padding-inline: clamp(15px, 0.83472vw + 8.58932px, 20px);
  text-transform: none;
}
.button.light {
  --bg: #FFFFFF;
  --text: #72AA32;
}
.button.outline {
  --border: #B3C6BB;
  --bg: transparent;
  --text: #FFFFFF;
}
.button.hover-light {
  --bg-hover: #FFFFFF;
  --text-hover: #72AA32;
}
.button.hover-theme4 {
  --bg-hover: #72AA32;
  --text-hover: #FFFFFF;
}
.button:disabled {
  background-color: #EEEEEE;
  border-color: #EEEEEE;
  color: #CCCCCC;
  cursor: default;
}
.button.normal {
  border: 3px solid var(--border-color);
}
.button.primary {
  --border-width: 3px;
  position: relative;
  color: #000000;
  border-left: none;
  border-right: none;
}
.button.primary .left-border,
.button.primary .right-border {
  position: absolute;
  top: 0;
  bottom: 0;
  width: var(--border-width);
  background-color: var(--border-color);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.button.primary .left-border {
  left: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
}
.button.primary .right-border {
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
}
.button.primary .left-border,
.button.primary .right-border {
  background: none;
  border-left: var(--border-width) solid var(--border-color);
}
.button.primary::before,
.button.primary::after {
  content: '';
  position: absolute;
  width: 10%;
  height: var(--border-width);
  background-color: var(--border-color);
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
  will-change: width, transform;
}
.button.primary::before {
  top: 0;
  left: 0;
  -webkit-transform-origin: left;
          transform-origin: left;
}
.button.primary::after {
  bottom: 0;
  right: 0;
  -webkit-transform-origin: right;
          transform-origin: right;
}
.button.primary:hover,
.button.primary:focus {
  --border-color: #EF3E34;
  color: #EF3E34;
}
.button.primary:hover::before,
.button.primary:focus::before {
  width: 0%;
  -webkit-animation: slideInTopBorderContinue 0.3s ease 0.3s forwards;
          animation: slideInTopBorderContinue 0.3s ease 0.3s forwards;
}
.button.primary:hover::after,
.button.primary:focus::after {
  width: 0%;
  -webkit-animation: slideInBottomBorderDoubleContinue 0.3s ease 0.3s forwards;
          animation: slideInBottomBorderDoubleContinue 0.3s ease 0.3s forwards;
}
.button.primary:not(:hover):not(:focus)::before,
.button.primary:not(:hover):not(:focus)::after {
  -webkit-animation: none;
          animation: none;
  width: 10%;
  -webkit-transition: width 0.3s ease;
  transition: width 0.3s ease;
}
.button.primary.light {
  --border-color-light: #FFFFFF;
  --border-color: #FFFFFF;
  color: #FFFFFF;
}
.button.primary.light .left-border,
.button.primary.light .right-border {
  background-color: #FFFFFF;
  color: #FFFFFF;
}
.button.primary.black {
  --border-color-light: #000000;
  --border-color: #000000;
  color: #000000;
}
.button.primary.black .left-border,
.button.primary.black .right-border {
  background-color: #000000;
  color: #000000;
}
.button.primary.red {
  --border-color-light: #EF3E34;
  --border-color: #EF3E34;
  color: #EF3E34;
}
.button.primary.red .left-border,
.button.primary.red .right-border {
  background-color: #000000;
  color: #000000;
}
.button.secondary {
  --text-color: #EF3E34;
  padding: 10px 20px 10px 0;
  font-size: 18px;
  letter-spacing: -20;
  border: none;
  background: transparent;
  color: var(--text-color);
  position: relative;
  display: inline-block;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  border-left: none;
  border-right: none;
}
.button.secondary::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10px;
  width: 20px;
  height: 3px;
  background-color: var(--text-color);
  -webkit-transition: width 0.2s ease-in-out;
  transition: width 0.2s ease-in-out;
}
.button.secondary::after {
  content: '>';
  display: inline-block;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  opacity: 0;
  font-size: 18px;
  font-family: "Poppins", sans-serif;
  line-height: 1;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.button.secondary:hover {
  -webkit-transform: translateX(-8px);
          transform: translateX(-8px);
}
.button.secondary:hover::before {
  width: calc(100% - 31px);
}
.button.secondary:hover::after {
  opacity: 1;
  right: 0;
}
.button.secondary.light {
  --text-color: #FFFFFF;
}
.button.ternary {
  --border-width: 3px;
  position: relative;
  color: #000000;
  border-left: none;
  border-right: none;
}
.button.ternary .top-border,
.button.ternary .bottom-border {
  position: absolute;
  width: var(--border-width);
  background-color: var(--border-color-dark);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  opacity: 0;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.button.ternary .top-border {
  top: 0;
  left: 0;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.button.ternary .bottom-border {
  bottom: 0;
  right: 0;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.button.ternary .top-border,
.button.ternary .bottom-border {
  background: none;
  border-left: var(--border-width) solid var(--border-color-dark);
}
.button.ternary .left-border,
.button.ternary .right-border {
  position: absolute;
  width: var(--border-width);
  height: 100%;
  background-color: var(--border-color-dark);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  visibility: hidden;
}
.button.ternary .left-border {
  top: 0;
  left: 0;
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
}
.button.ternary .right-border {
  top: 0;
  right: 0;
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
}
.button.ternary::before,
.button.ternary::after {
  content: '';
  position: absolute;
  width: 40%;
  height: var(--border-width);
  background-color: var(--border-color-dark);
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
  will-change: width, transform;
}
.button.ternary::before {
  top: 0;
  left: 0;
  -webkit-transform-origin: left;
          transform-origin: left;
}
.button.ternary::after {
  bottom: 0;
  right: 0;
  -webkit-transform-origin: right;
          transform-origin: right;
}
.button.ternary:hover,
.button.ternary:focus {
  --border-color-light: #000;
  color: #000;
}
.button.ternary:hover::before,
.button.ternary:focus::before {
  width: 65%;
  -webkit-animation: slideInTopBorderContinue 0.1s ease 0.1s forwards;
          animation: slideInTopBorderContinue 0.1s ease 0.1s forwards;
}
.button.ternary:hover::after,
.button.ternary:focus::after {
  width: 65%;
  -webkit-animation: slideInBottomBorderDoubleContinue 0.2s ease 0.2s forwards;
          animation: slideInBottomBorderDoubleContinue 0.2s ease 0.2s forwards;
}
.button.ternary:hover .left-border,
.button.ternary:focus .left-border {
  opacity: 1;
  visibility: visible;
  -webkit-animation: leftBorderSlideUp 0.3s ease forwards;
          animation: leftBorderSlideUp 0.3s ease forwards;
}
.button.ternary:hover .right-border,
.button.ternary:focus .right-border {
  opacity: 1;
  visibility: visible;
  -webkit-animation: rightBorderSlideDown 0.3s ease forwards;
          animation: rightBorderSlideDown 0.3s ease forwards;
}
.button.ternary:not(:hover):not(:focus) {
  border-left: none;
  border-right: none;
  border-color: #000;
}
.button.ternary:not(:hover):not(:focus)::before,
.button.ternary:not(:hover):not(:focus)::after {
  -webkit-animation: none;
          animation: none;
  width: 65%;
  -webkit-transition: width 0.3s ease;
  transition: width 0.3s ease;
}
.button.ternary:not(:hover):not(:focus) .left-border,
.button.ternary:not(:hover):not(:focus) .right-border {
  opacity: 0;
  visibility: hidden;
  -webkit-transition: opacity 0.3s ease, visibility 0s 0.3s;
  transition: opacity 0.3s ease, visibility 0s 0.3s;
}
.button.ternary.light {
  --border-color-light: #FFFFFF;
  --border-color: #FFFFFF;
  color: #FFFFFF;
}
.button.ternary.light .left-border,
.button.ternary.light .right-border {
  background-color: #FFFFFF;
  color: #FFFFFF;
}
.button.ternary.light .top-border,
.button.ternary.light .bottom-border {
  border-color: var(--border-color-light);
  background-color: var(--border-color-light);
}
.button.ternary.light:not(:hover):not(:focus)::before,
.button.ternary.light:not(:hover):not(:focus)::after {
  background-color: var(--border-color-light);
}
.button.ternary.light:hover,
.button.ternary.light:focus {
  border-color: var(--border-color-light);
}
.button.ternary.light:hover::before,
.button.ternary.light:focus::before,
.button.ternary.light:hover::after,
.button.ternary.light:focus::after {
  border-color: var(--border-color-light);
  background-color: var(--border-color-light);
}
.panel-text {
  word-break: break-word;
  max-width: var(--text-wrap, 100%);
}
.panel-text .button {
  margin: 0 10px 10px 0;
}
.panel-text [style*="text-align: center;"] .button,
.panel-text .center .button {
  margin-inline: 5px;
}
.panel-text [style*="text-align: right;"] .button,
.panel-text .right .button {
  margin-right: 0;
  margin-left: 10px;
}
.gradient-text {
  color: #16212F;
  background-clip: text;
  background-size: 200% auto;
  -webkit-background-clip: text;
  text-fill-color: transparent;
  -webkit-text-fill-color: transparent;
  background-image: -webkit-gradient(linear, left top, right top, from(#16212F), color-stop(#EF3E34), to(#16212F));
  background-image: linear-gradient(to right, #16212F, #EF3E34, #16212F);
  padding-bottom: 0.15ch;
}
/*------ email template ------*/
body.email-template {
  background: #EEEEEE;
  max-width: none;
}
body.email-template #email-wrapper {
  margin: 0 auto;
}
body.email-template #email-wrapper #email-header,
body.email-template #email-wrapper #email-content,
body.email-template #email-wrapper #email-footer {
  border: 0;
}
body.email-template #email-wrapper #email-header {
  padding: 30px 0;
  background: none;
}
body.email-template #email-wrapper #email-header img {
  display: block;
}
body.email-template #email-wrapper #email-content {
  padding: 0 0 30px;
  background: none;
}
body.email-template #email-wrapper #email-content #email-content-inner {
  background: #FFFFFF;
  padding: 30px 30px 10px;
  border-radius: 10px;
}
body.email-template #email-wrapper #email-footer {
  padding: 20px 0;
  background: none;
  border-top: 1px solid #CCCCCC;
  text-align: center;
}
