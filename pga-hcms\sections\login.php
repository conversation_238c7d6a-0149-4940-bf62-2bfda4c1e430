<!--open login-wrapper-->
<div id="login-wrapper">

	<!--open login-form-->
	<div id="login-form">
		<header id="login-header"><img src="<?php echo $path; ?>images/logo.svg" alt="Honeycomb CMS" /></header>
		<form name="login-form" action="<?php echo $section['page_url']; ?>" method="post">
			<?php 
			$system_alert = $CMSBuilder->system_alert();
			echo (!is_null($system_alert) ? '<div id="login-alert"'.($system_alert[count($system_alert)-1]['status'] == true ? " class='success'": "").'>' .$system_alert[count($system_alert)-1]['message']. '</div>' : '');
			?>
			<div class="form-field">
				<label>Username or Email</label>
				<input type="text" name="user_login" class="input" tabindex="1" />
			</div>
			<div class="form-field">
				<label>Password <a href="<?php echo $path; ?>reset/">Forgot Password?</a></label>
				<input type="password" name="user_password" class="input" tabindex="2" autocomplete="off" />
			</div>

			<div id="login-buttons">
				<input type="checkbox" class="checkbox" name="user_reme" id="reme" value="1"<?php echo (!empty($_COOKIE['auth']['reme_id']) ? ' checked' : ''); ?> /> 
				<label for="reme">Remember Me</label>
				<button type="submit" name="login" class="button" tabindex="3"><i class="fas fa-lock"></i>Login</button>
			</div>

			<input type="hidden" name="xssid" value="<?php echo $_COOKIE['xssid']; ?>" />
		</form>
	</div><!--close login-form-->
    
</div><!--close login-wrapper-->