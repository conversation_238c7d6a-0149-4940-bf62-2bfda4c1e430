<?php  

if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();

}else{

	//Dashboard widget
	if(SECTION_ID == $_cmssections['dashboard']){
		$total_records = $db->get_record_count('gl_accounts');
		$CMSBuilder->set_widget(94, 'Total GL Accounts', $total_records);
	}

	if(SECTION_ID == $_cmssections['gl-accounts']){
		//Define vars
		$record_db = 'gl_accounts';
		$record_id = 'gl_id';
		$record_name = 'Account';

		$errors = false;
		$required = array();
		
		//GL categories
		$categories = array(
			1 => 'Events',
			2 => 'Tournaments',
			3 => 'Hole in One',
			4 => 'Invoices'
		);

		//Get Records
		$records_arr = array();
		$params = array();

		$_GET['search'] = $CMSBuilder->system_search(SECTION_ID);
		if(isset($_GET['search'])){
			$params[] = '%' .$_GET['search']. '%';
			$params[] = '%' .$_GET['search']. '%';
		}
		$query = $db->query("SELECT * FROM `$record_db`" .(!empty($_GET['search']) != "" ? " WHERE `gl_name` LIKE ? OR `gl_number` LIKE ?" : ""). " ORDER BY `gl_name` ASC", $params);
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				$records_arr[$row[$record_id]] = $row;
			}
		}else{
			$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
		}

		//Not found
		if(ACTION == 'edit'){
			if(!array_key_exists(ITEM_ID, $records_arr)){
				$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
				header('Location:' .PAGE_URL);
				exit();
			}else{
				$row = $records_arr[ITEM_ID];	
			}
		}

		//Delete item
		if(isset($_POST['delete'])){
			
			$delete = $db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
			if(!$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			}else{
				$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
			}
			header("Location: " .PAGE_URL);
			exit();
		
		//Save item
		}else if(isset($_POST['save'])){

			//Validate
			if(trim($_POST['gl_name']) == ''){
				array_push($required, 'gl_name');
				$errors[0] = 'Please fill out all the required fields (*).';
			}
			if(trim($_POST['gl_number']) == ''){
				array_push($required, 'gl_number');
				$errors[0] = 'Please fill out all the required fields (*).';
			}

			if(!$errors){
				
				$db->new_transaction();
				
				//Insert to db
				$params = array(
					ITEM_ID, 
					$_POST['gl_name'], 
					$_POST['gl_number'],
					(!empty($_POST['item_no']) ? $_POST['item_no'] : NULL),
					date("Y-m-d H:i:s"),
					date("Y-m-d H:i:s"),
					$_POST['gl_name'], 
					$_POST['gl_number'],
					(!empty($_POST['item_no']) ? $_POST['item_no'] : NULL),
					date("Y-m-d H:i:s")
				);
				$insert = $db->query("INSERT INTO `$record_db` (`$record_id`, `gl_name`, `gl_number`, `item_no`, `date_added`, `last_updated`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `gl_name` = ?, `gl_number` = ?, `item_no` = ?, `last_updated` = ?", $params);
				if($insert && !$db->error()){
					$item_id = (ITEM_ID == '' ? $db->insert_id() : ITEM_ID);
					
					//Unset other accounts with same category
					if(!empty($_POST['item_no'])){
						$query = $db->query("UPDATE `$record_db` SET `item_no` = ? WHERE `item_no` = ? && `$record_id` != ?", array(NULL, $_POST['item_no'], $item_id));
					}

				}
				
				if(!$db->error()){
					$db->commit();
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}else{
					$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), true);
				}

			}else{
				$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
				foreach($_POST AS $key=>$data){
					$row[$key] = $data;
				}	
			}
		}
	}

}

?>