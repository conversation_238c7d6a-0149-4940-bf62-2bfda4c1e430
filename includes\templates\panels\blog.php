<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?>">
	<div class="container container-xl">
		<div class="panel-content blog-content">
		<?php
		if($panel['title'] && $panel['show_title'] || !$panelcount && SLIDESHOW){
			echo '<header class="panel-header">'.
				(!$panelcount && SLIDESHOW ? '<h1>'.fancy_text($page['page_title']).'</h1>' : '').
				($panel['title'] && $panel['show_title'] ? '<div class="panel-title"><h2 class="font-h3">' .$panel['title']. '</h2></div>' : '').
			'</header>';
		}
		?>
		</div>

<?php
	$imagedir  = 'images/blog/entries/thumbs/';
    $bannerdir = 'images/heroes/';
    $pagebits  = $SiteBuilder->get_pagebits($_sitepages['blog']['slug'] ?: $_sitepages['blog']['page']);

	if(count($panel['blog_entries']) > 0){
		// Prepare entries with necessary data
		$entries = [];
		foreach ($panel['blog_entries'] as $index => $entry) {
			$entry['url'] = $_sitepages['blog']['page_url'] . $entry['page'] . '-' . $entry['category_id'] . '/' . $entry['page'] . '-' . $entry['entry_id'] . '/';
			$entry['image'] = $path.$imagedir.$entry['image'] ??  'default.jpg';
			$entry['image_alt'] = $entry['image_alt'] ?: $entry['title'];
			$entries[] = $entry;
		}

		// Start building the custom slider HTML
		$html = '<div class="blog-custom-slider-wrapper">
			<div class="blog-custom-slider swiper-container">
				<div class="swiper-wrapper">';

		// Generate slides with different layouts based on position
		for ($i = 0; $i < count($entries); $i++) {
			$entry = $entries[$i];
			// Determine slide type based on position in the slider
			$slideType = ($i % 4 == 0) ? 'small-left' :
						 (($i % 4 == 1) ? 'large-center' :
						 (($i % 4 == 2) ? 'small-top-right' : 'small-bottom-right'));

			// Set appropriate class for each entry based on its position
			$entry['class'] = 'blog-entry-' . $slideType;

			// For the third slide position (i % 4 == 2), we need to create a container for the two stacked entries
			if ($slideType == 'small-top-right') {
				$html .= '<div class="swiper-slide blog-slide-stacked">';

				// First entry in the stacked container
				ob_start();
				include('includes/templates/blog-box.php');
				$html .= ob_get_clean();

				// Check if we have another entry for the bottom position
				if (isset($entries[$i + 1])) {
					$nextEntry = $entries[$i + 1];
					$nextEntry['class'] = 'blog-entry-small-bottom-right';
					$entry = $nextEntry;

					ob_start();
					include('includes/templates/blog-box.php');
					$html .= ob_get_clean();
				}

				$html .= '</div>';
				// Skip the next entry as we've already used it
				$i++;
			} else {
				// Regular slide
				$html .= '<div class="swiper-slide blog-slide-' . $slideType . '">';
				ob_start();
				include('includes/templates/blog-box.php');
				$html .= ob_get_clean();
				$html .= '</div>';
			}
		}

		// Close the swiper structure and add navigation
		// <div class="blog-swiper-pagination"></div>

		$html .= '</div>
				<!-- Add Pagination -->
			</div>
			<!-- Add Navigation -->
			<div class="blog-swiper-button-prev location-scroll-arrow location-scroll-left"></div>
    		<div class="blog-swiper-button-next location-scroll-arrow location-scroll-right"></div>
		</div>';

		echo $html;
	}
?>
	</div>
</section>