<?php

$form = '';

if (!USER_LOGGED_IN) {
    $form .= '<div class="alert">
        <p><strong>Important!</strong> You are not logged in. If you are a registered user, <a href="' . $_sitepages['login']['page_url'] . '?redirect=' . urlencode($page['page_url']) . '">login here</a>. If you are not a registered user and would like to make changes to a previous job posting, please <a href="' . $_sitepages['contact']['page_url'] . '">contact us</a>. All job postings will require admin approval before being publicly available.</p>
    </div>';
} else {
    $form .= '<div class="alert">
        <p>Looking to get more traction for your recent job posting? Contact <PERSON><PERSON> at the PGA office for more information (<EMAIL>).</p>
        <p><strong class="color-red">Important!</strong> All job postings will require admin approval before being publicly available.</p>
    </div>';
}

// $form .= (isset($alert) ? $alert : '');
// --- Display Messages ---
	if (!empty($success)) {
		$form .= '<div class="alert alert-success" style="background-color: #dff0d8; border: 1px solid #d6e9c6; color: #3c763d; padding: 15px; margin-bottom: 20px;">' . $success . '</div>';
	}
	if (!empty($alert)) {
		$form .= '<div class="alert alert-danger" style="background-color: #f2dede; border: 1px solid #ebccd1; color: #a94442; padding: 15px; margin-bottom: 20px;">' . $alert . '</div>';
	}


$form .= '<form method="post" class="form job-posting-form ' . (!USER_LOGGED_IN ? 'hidden-recaptcha' : '') . '" id="job-posting" enctype="multipart/form-data" data-recaptcha="#recaptcha-job-posting">
    <h4>Job Details</h4>
    <div class="form-grid">

        <div class="form-field">
            <label>Title <strong class="color-red">*</strong></label>
            <input type="text" name="title" class="input jsvalidate' . (in_array('title', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['title'] ?? '', ENT_QUOTES) . '" />
        </div>

        <div class="form-field">
            <label>Category <strong class="color-red">*</strong></label>
            <select class="select' . (in_array('category_id', $required) ? ' required' : '') . '" name="category_id" id="category_id">
                <option value="">- Select a Category -</option>';
foreach ($categories as $category_id => $category) {
    $form .= '<option ' . ((isset($_POST['category_id']) && $_POST['category_id'] == $category_id) ? 'selected' : '') . ' value="' . $category_id . '">' . htmlspecialchars($category['category_name']) . '</option>';
}
$form .= '</select>
        </div>

        <div class="form-field">
            <label>Posted Date <strong class="color-red">*</strong></label>
            <input type="date" name="posted_date" class="input' . (in_array('posted_date', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['posted_date'] ?? '', ENT_QUOTES) . '"  />
        </div>

        <div class="form-field">
            <label>Closing Date <strong class="color-red">*</strong></label>
            <input type="date" name="closing_date" class="input' . (in_array('closing_date', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['closing_date'] ?? '', ENT_QUOTES) . '" />
        </div>

        <div class="form-field">
            <label>Facility <strong class="color-red">*</strong></label>
            <select class="select' . (in_array('facility_id', $required) ? ' required' : '') . '" name="facility_id" id="facility_id">
                <option value="">- Select a Facility -</option>';
foreach ($facilities as $facility_id => $facility) {
    $selected = (isset($_POST['facility_id']) && $_POST['facility_id'] == $facility_id) || (!empty($account['facility_id']) && $facility_id == $account['facility_id']) ? 'selected' : '';
    $form .= '<option ' . $selected . ' value="' . $facility_id . '">' . htmlspecialchars($facility['facility_name']) . '</option>';
}
$form .= '</select>
        </div>

        <div class="form-field">
            <label>Monthly Salary Range <strong class="color-red">*</strong></label>
            <input type="text" name="salary" class="input jsvalidate' . (in_array('salary', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['salary'] ?? '', ENT_QUOTES) . '" />
        </div>

        <div class="form-field">
            <label>Duration <strong class="color-red">*</strong></label>
            <input type="text" name="duration" class="input jsvalidate' . (in_array('duration', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['duration'] ?? '', ENT_QUOTES) . '" />
        </div>

        <div class="form-field">
            <label>Attach File <small>(PDF Document, 2MB maximum)</small></label>
            <div class="input-file-container">
                <input class="input-file" id="file" name="file" type="file" />
                <label tabindex="0" for="file" class="input-file-trigger' . (in_array('file', $required) ? ' required' : '') . '"><i class="fa fa-upload"></i>Select a file...</label>
            </div>
        </div>';

if (USER_LOGGED_IN) {
    $form .= '<div class="form-field full-width">
        <label>Who Can Apply? <strong class="color-red">*</strong></label>
        <select class="select' . (in_array('public', $required) ? ' required' : '') . '" name="public" id="public">
            <option value="0"' . (isset($_POST['public']) && $_POST['public'] == 0 ? ' selected' : '') . '>Registered Members Only</option>
            <option value="1"' . (isset($_POST['public']) && $_POST['public'] == 1 ? ' selected' : '') . '>Anyone</option>
        </select>
    </div>';
}

$form .= '</div>
<h4>Member Classification</h4>
<div class="form-grid member_classification">';
foreach ($classes as $class) {
    $checked = isset($_POST['class_id']) && in_array($class['class_id'], $_POST['class_id']) ? ' checked' : '';
    $form .= '<input type="checkbox" name="class_id[]" id="class' . $class['class_id'] . '" class="checkbox" value="' . $class['class_id'] . '"' . $checked . ' />
    <label for="class' . $class['class_id'] . '">' . htmlspecialchars($class['class_name']) . '</label>';
}
$form .= '</div>
<h4>Contact Information</h4>
<div class="form-grid">

    <div class="form-field">
        <label>First Name <strong class="color-red">*</strong></label>
        <input type="text" name="first_name" class="input jsvalidate' . (in_array('first_name', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['first_name'] ?? ($Account->first_name ?? ''), ENT_QUOTES) . '"/>
    </div>
    <div class="form-field">
        <label>Last Name <strong class="color-red">*</strong></label>
        <input type="text" name="last_name" class="input jsvalidate' . (in_array('last_name', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['last_name'] ?? ($Account->last_name ?? ''), ENT_QUOTES) . '"/>
    </div>
    <div class="form-field">
        <label>Email <strong class="color-red">*</strong></label>
        <input type="email" name="email" class="input jsvalidate' . (in_array('email', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['email'] ?? ($Account->email ?? ''), ENT_QUOTES) . '"/>
    </div>
    <div class="form-field">
        <label>Phone Number <strong class="color-red">*</strong></label>
        <input type="text" name="phone" class="input phone jsvalidate' . (in_array('phone', $required) ? ' required' : '') . '" value="' . htmlspecialchars($_POST['phone'] ?? ($Account->phone ?? ''), ENT_QUOTES) . '"/>
    </div>
</div>

<h4>Job Description</h4>
<div class="form-grid job_des">
<textarea name="texteditor" id="texteditor" class="textarea texteditor">' . htmlspecialchars($_POST['texteditor'] ?? '', ENT_QUOTES) . '</textarea>
</div>
<div class="form-buttons">
    <button type="' . (!USER_LOGGED_IN ? 'button' : 'submit') . '" class="button primary black back-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span>Post Job</button>
    <a href="' . $_sitepages['job-postings']['page_url'] . '" class="button primary red red-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Cancel</a>
</div>

<div class="hidden">
    <div id="recaptcha-modal" class="hidden-modal" title="Verify You&rsquo;re Not a Robot">
        <div class="recaptcha-wrapper">
            <div id="recaptcha-job-posting" class="g-recaptcha" data-sitekey="' . $global['recaptcha_key'] . '"></div>
        </div>	
    </div>


<input type="hidden" name="xid" value="' . htmlspecialchars($_COOKIE['xid'] ?? '', ENT_QUOTES) . '" />
<input type="hidden" name="post" value="post" />
<input type="hidden" name="g-recaptcha-response" value="" />
</form>';

// Set panel content
$page['page_panels'][$panel_id]['content'] .= $form;

// Page panels
include('includes/pagepanels.php');

?>
