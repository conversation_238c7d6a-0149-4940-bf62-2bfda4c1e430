<?php

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">'.$records_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">

				<thead>
				<th width="1px" data-sorter="false"></th>
				<th>Committee Name</th>
				<th width="1px" data-sorter="false"></th>
				</thead>

				<tbody>';

				foreach($records_arr as $row){
				echo '<tr data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-id="'.$row[$record_id].'">
				<td class="handle"><i class="fas fa-arrows-alt"></i></td>
				<td>'.$row['name'].'</td>
				<td><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
				</tr>';
				}

				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager(50);

		echo '</div>
	</div>';


//Display form
}else{
	$data = $records_arr[ITEM_ID] ?? [];
	$row  = !isset($_POST['save']) ? $data : $_POST;

	echo '<form action="" method="post" enctype="multipart/form-data">';

		// Committee details
		echo '<div class="panel">
			<div class="panel-header">Committee Details
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content flex-container">
				<div class="form-field" style="flex: 2;">
				<label>Committee Name <span class="required">*</span></label>
				<input type="text" name="name" value="'.($row['name'] ?? '').'" class="input'.(in_array('name', $required) ? ' required' : '').'" />
				</div>

				<div class="form-field">
				<label>Numerical Order'.$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.').'</label>
				<select name="ordering" class="select">
				<option value="101">Default</option>';

				for($i = 1; $i < 101; $i++){
				echo '<option value="'.$i.'"'.(($row['ordering'] ?? false) == $i ? ' selected' : '').'>'.$i.'</option>';
				}

				echo '</select>
				</div>
			</div>
		</div>';

		//Sticky footer
		include('includes/widgets/formbuttons.php');

		echo '<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'].'" />
	</form>';

}

?>