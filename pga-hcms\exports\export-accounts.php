<?php  

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../includes/utils.php");

if(isset($_POST) && USER_LOGGED_IN){

	//Define vars
	$record_db = 'accounts';
	$record_id = 'account_id';
	$record_name = 'User';
	$records_arr = array();
	$params = array();

	//Set defaults
	$db_columns = array('accounts.account_id', 'accounts.status', 'account_roles.role_id', 'accounts.email');
	$alias_columns = array('account_id', 'status', 'role_id', 'email');
	$table_columns = array('Account No.', 'Status', 'Type', 'Email');
	$table_headers = array();

	$querytxt = "";
	$wheretxt = "";
	
	//Export with selected columns/filters
	if(isset($_GET['advanced_search'])) {
		
		$db_columns = array();
		$table_columns = array();
		$alias_columns = array();
				
		//Columns
		$ccount = 0;
		if(isset($_GET['column']) && !empty($_GET['column'])){
			foreach($_GET['column'] as $db_name => $columns){
				foreach($columns as $column => $value){
					if($value != ''){
						if($column == 'address'){
							$db_columns[$ccount] = 'account_profiles.address1';
							$table_columns[$ccount] = 'Street Address';
							$alias_columns[$ccount] = 'address1';
							$ccount++;
							
							$db_columns[$ccount] = 'account_profiles.address2';
							$table_columns[$ccount] = 'Unit No.';
							$alias_columns[$ccount] = 'address2';
							$ccount++;
							
							$db_columns[$ccount] = 'account_profiles.city';
							$table_columns[$ccount] = 'City';
							$alias_columns[$ccount] = 'city';
							$ccount++;
							
							$db_columns[$ccount] = 'account_profiles.province';
							$table_columns[$ccount] = 'Province';
							$alias_columns[$ccount] = 'province';
							$ccount++;
							
							$db_columns[$ccount] = 'account_profiles.postal_code';
							$table_columns[$ccount] = 'Postal Code';
							$alias_columns[$ccount] = 'postal_code';
							$ccount++;
							
							$db_columns[$ccount] = 'account_profiles.country';
							$table_columns[$ccount] = 'Country';
							$alias_columns[$ccount] = 'country';
							$ccount++;
						}else{
							$db_columns[$ccount] = $db_name.'.'.$column;
							$table_columns[$ccount] = $value;
							$alias_columns[$ccount] = $column;
						}
						
						$ccount++;
					}
				}
			}
		}
		
		//Search/filters
		if(isset($_GET['filter']) && !empty($_GET['filter'])){
			foreach($_GET['filter'] as $db_name => $filters){				
				foreach($filters as $filter => $value){
					if(trim($value) != ''){

						$wheretxt .= ($wheretxt != "" ? " AND " : " WHERE ")."$db_name.$filter";

						if($filter == 'role_id' || $filter == 'status' || $filter == 'group_id'){
							$wheretxt .= " = ?";
							$params[] = $value;
						}else{
							$wheretxt .= " LIKE ?";
							$params[] = '%' .$value. '%';
						}

					}
				}
			}
		}
		
	}
		
	//Create query
	$querytxt = "SELECT `account_profiles`.`account_id`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `facilities`.`facility_name`, `membership_classes`.`class_name`, `membership_types`.`membership_name`, `membership_categories`.`name` AS `category_name`, `account_groups`.`group_name`".
	(!empty($db_columns) ? ",".implode(", ", $db_columns)." " : " ").
	",(SELECT `account_permissions`.`role_id` FROM `account_permissions` WHERE `account_permissions`.`account_id` = `account_profiles`.`account_id` && `account_permissions`.`role_id` = 6) AS `staff_member` ".
	"FROM `accounts` ".
	"LEFT JOIN `account_profiles` ON `accounts`.`account_id` = `account_profiles`.`account_id` ".
	"LEFT JOIN `account_permissions` ON `accounts`.`account_id` = `account_permissions`.`account_id` ".
	"LEFT JOIN `account_roles` ON `account_permissions`.`role_id` = `account_roles`.`role_id` ".
	"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
	"LEFT JOIN `membership_classes` ON `account_profiles`.`class_id` = `membership_classes`.`class_id` ".
	"LEFT JOIN `membership_types` ON `account_profiles`.`membership_id` = `membership_types`.`membership_id` ".
	"LEFT JOIN `membership_categories` ON `account_profiles`.`category_id` = `membership_categories`.`category_id` ".
	"LEFT JOIN `account_groups` ON `account_profiles`.`group_id` = `account_groups`.`group_id`".
	$wheretxt.
	" GROUP BY `accounts`.`account_id` ORDER BY `accounts`.`account_id`";
	$query = $db->query($querytxt, $params);
	if($query && !$db->error()){
		$records_arr = $db->fetch_array();
	}
	
	//Output csv
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=accounts-".date("Ymd").".csv");
	header("Content-Transfer-Encoding: binary ");
	
	$fp = fopen('php://output', 'w');
	
	//Compile headers
	if(!empty($table_columns)) {
		foreach($table_columns as $key => $column) {
			if($column == 'Account No.' || $column == 'Type' || $column == 'Status' || $column == 'Group'){
				$table_headers[] = $column;
			}
		}
		$table_headers[] = 'First Name';
		$table_headers[] = 'Last Name';
		$table_headers[] = 'Facility';
		foreach($table_columns as $key => $column) {
			if($column != 'Account No.' && $column != 'Type' && $column != 'Status' && $column != 'Group'){
				$table_headers[] = $column;
			}
		}
	}
	fputcsv($fp, str_replace("&rsquo;", "'", $table_headers));

	//Compile records
	$count = 0;
	foreach($records_arr as $row) {
		$data = array();
		
		foreach($alias_columns as $key => $column) {
			if($column == 'account_id' || $column == 'role_id' || $column == 'status' || $column == 'group_id') {
				
				//Type
				if($column == 'role_id'){
					$roles = array(1=>'Admin', 2=>'Member', 6=>'Staff', 7=>'Non-Member');
					$data[] = (empty($row['staff_member']) ? $roles[$row[$column]] : $roles[$row['staff_member']]);

				//Group
				}else if($column == 'group_id'){
					$data[] = $row['group_name'];

				}else{
					$data[] = $row[$column];
				}
			}
		}
		$data[] = $row['first_name'];
		$data[] = $row['last_name'];
		$data[] = $row['facility_name'];
		foreach($alias_columns as $key => $column) {
			if($column != 'account_id' && $column != 'role_id' && $column != 'status' && $column != 'group_id'){
				
				//Classification
				if($column == 'class_id'){
					$data[] = $row['class_name'];

				//Membership
				}else if($column == 'membership_id'){
					$data[] = $row['membership_name'];

				//Category
				}else if($column == 'category_id'){
					$data[] = $row['category_name'];	

				//Dates
				}else if($column == 'pga_member_since' || $column == 'dob' || $column == 'rewards_updated') {
					$data[] = ($row[$column] != '' && $row[$column] != '0000-00-00' && $row[$column] != '0000-00-00 00:00:00' ? date('Y-m-d', strtotime($row[$column])) : "");

				//Standard
				}else{
					$data[] = $row[$column];
				}
			}
		}
		
		fputcsv($fp, str_replace("&rsquo;", "'", $data));
	}

	//Close
	fclose($fp);

}
?>