<?php

// $directory_list = [];
$facility_detail = null;
$is_facility_list_page = false;
$is_facility_detail_page = false;
$facility_id_from_url = null;

$GLOBALS['total_facilities'] = 0;
$GLOBALS['current_page'] = 1;
$GLOBALS['limit'] = 4; // Items per page (change to 20 for production)

//Get golf facilities
if(PAGE_ID == $_sitepages['facilities']['page_id'] || (PARENT_ID == $_sitepages['facilities']['page_id'] && PAGE_ID == '')){

	// (PARENT_ID == $_sitepages['facilities']['page_id'] && PAGE_ID == '')
	$all_matching_facilities = array(); // Temporary array to hold all facilities BEFORE pagination
	$facilities = array();
	$params = array('Active');
	$searchqry = "";
	$letter_filter_where_clause = "";

	// $total_facilities_for_pagination = 0; // For pagination calculation
	// $current_page_for_pagination = 1;
	// $limit_for_pagination = 2; // Items per page (change to 20 later)
	// $offset_for_pagination = 0;

    // // --- 1. Determine Current Page for Pagination ---
	// if (isset($_GET['pg']) && is_numeric($_GET['pg']) && $_GET['pg'] > 0) {
	// 	$current_page_for_pagination = (int)$_GET['pg'];
	// }
	// $offset_for_pagination = ($current_page_for_pagination - 1) * $limit_for_pagination;

	if(isset($_GET['search']) && trim($_GET['search']) != ''){
		$params[] = '%'.$_GET['search'].'%';
		$params[] = '%'.$_GET['search'].'%';
		$params[] = '%'.$_GET['search'].'%';
		$searchqry .= "&& (`facility_name` LIKE ? OR `region` LIKE ? OR `email` LIKE ?) ";
	}

	// --- A-Z Letter Filter ---
    if (isset($_GET['letter']) && !empty(trim($_GET['letter']))) {
        $selected_letter = strtoupper(trim($_GET['letter']));
        if (strlen($selected_letter) == 1 && ctype_alpha($selected_letter)) { // Ensure it's a single letter
            array_push($params, $selected_letter . '%'); // Add param for letter (e.g., 'A%')
            $letter_filter_where_clause = "AND `facility_name` LIKE ? ";
        }
    }

	$query = $db->query("SELECT `facilities`.*, (SELECT COUNT(`account_profiles`.`facility_id`) FROM `account_profiles` LEFT JOIN `accounts` ON `account_profiles`.`account_id` = `accounts`.`account_id` WHERE `account_profiles`.`facility_id` = `facilities`.`facility_id` && `accounts`.`status` = ?) AS `facility_members` FROM `facilities` WHERE `showhide` = 0 " .$searchqry.' '. $letter_filter_where_clause. "ORDER BY `facility_name` ASC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			if($row['facility_members'] > 0){
				// $facilities[$row['facility_id']] = $row;
				$all_matching_facilities[$row['facility_id']] = $row;
			}
		}
	}

	//
	 // --- 2. Set Pagination Variables based on the filtered array ---
    $GLOBALS['total_facilities'] = count($all_matching_facilities);

    if (isset($_GET['pg']) && is_numeric($_GET['pg']) && $_GET['pg'] > 0) {
        $GLOBALS['current_page'] = (int)$_GET['pg'];
    }
    // $GLOBALS['limit'] is already set at the top

    $offset_for_array_slice = ($GLOBALS['current_page'] - 1) * $GLOBALS['limit'];

    // --- 3. Paginate the PHP array `$all_matching_facilities` ---
    // The $facilities variable will now hold only the items for the current page.
    if ($GLOBALS['total_facilities'] > 0) {
        $facilities = array_slice($all_matching_facilities, $offset_for_array_slice, $GLOBALS['limit'], true); // true to preserve keys
        error_log("Facilities Module - Sliced " . count($facilities) . " facilities for page " . $GLOBALS['current_page']);
    } else {
        $facilities = []; // Ensure it's an empty array if no facilities found
    }
	//
	
}

//Get selected facility
if(PARENT_ID == $_sitepages['facilities']['page_id'] && PAGE_ID == ''){
    
	// $pagebits =  get_pagebits((trim($_sitepages['facilities']['slug']) != '' ? $_sitepages['facilities']['slug'] : $_sitepages['facilities']['page']));

     $pagebits = $SiteBuilder->get_pagebits($_sitepages['facilites']['page'] ?? 'find-facility'); // Use actual page name/slug
	
	// if(!isset($pagebits[2]) || $pagebits[2] == ''){
    if (isset($pagebits[2]) && !empty($pagebits[2])) { // Expecting slug in the 3rd segment (index 2)

		$facility_bits = explode("-", $pagebits[2]);
		$facility_id = $facility_bits[count($facility_bits)-1];

		if(!is_numeric($facility_id)){
			$facility_id = NULL;
		}

		//
		$slug_parts = explode("-", $pagebits[2]);
        $facility_id_from_url = (int)end($slug_parts); // Get the last part, assume it's the ID

		// echo 'facility_id_from_url - '.$facility_id_from_url;

		if ($facility_id_from_url > 0) {
            $is_facility_detail_page = true;
            $error404 = false;    
		}	
		//
		
		$facility = array();		
		if(array_key_exists($facility_id_from_url, $facilities)){
			$error404 = false;
			$facility = $facilities[$facility_id_from_url];

			// echo "<pre>all facilites";
			// print_r($facilities);
			// echo '</pre>';
			// echo "<pre> facility to show";
			// print_r($facility);
			// echo '</pre>';
			
			$facility['page_url'] = $_sitepages['facilities']['page_url'].$facility['page'].'-'.$facility_id.'/';
			
			//Correct path
			if(clean_url($facility['page'].'-'.$facility_id) != $pagebits[2]){
				header("HTTP/1.1 301 Moved Permanently");
				header('Location: ' .$facility['page_url']);
				exit();
			}
			
			//Set page vars
			$parent = $SiteBuilder->get_page_content(PARENT_ID);
			$page['page_title'] = $facility['facility_name'];
			$page['description'] = $facility['city'].', '.$facility['province'];
			$page['content'] = '';
			$page['meta_canonical'] = $facility['page_url'];
			$page['meta_title'] = ($facility['meta_title'] != '' ? $facility['meta_title'] : $facility['facility_name'].' | '.$parent['meta_title']);
			$page['meta_description'] = ($facility['meta_description'] != '' ? $facility['meta_description'] : $parent['meta_description']);
			if($facility['logo'] != '' && file_exists('images/logos/'.$facility['logo'])){
				$page['logo'] = $path.'images/logos/'.$facility['logo'];
			}
			if($facility['image'] != '' && file_exists('images/heroes/1920/'.$facility['image'])){
				$page['banner_image'] = $facility['image'];
				$page['banner_image_alt'] = (trim($facility['image_alt']) != '' ? $facility['image_alt'] : $facility['facility_name']);
			}else{
				$page['banner_image'] = $parent['banner_image'];
				$page['banner_image_alt'] = $parent['banner_image'];
			}			
			$page['page_panels'] = array();
			// $page['page_panels'][28] = $parent['page_panels'][28]; //Utilize parent panel
			
			//Breadcrumb
			array_pop($breadcrumbs);
			array_push($breadcrumbs, array('name'=>$page['page_title'], 'url'=>$facility['page_url']));
			
			//Format address
			$facility['full_address'] = array();
			if(trim($facility['address2']) != '') {
				$facility['full_address'][] = $facility['address2'];
			}
			if(trim($facility['address1']) != '') {
				$facility['full_address'][] = $facility['address1'];
			}
			if(trim($facility['city']) != '') {
				$facility['full_address'][] = $facility['city'];
			}
			if(trim($facility['province']) != '') {
				$facility['full_address'][] = $facility['province'];
			}
			$facility['full_address'] = implode(', ', $facility['full_address']);
			$facility['full_address'] .= (trim($facility['postal_code']) != '' ? ' '.$facility['postal_code'] : '');
			
			//Facility members
			$query = $db->query("SELECT `account_profiles`.`first_name`, `account_profiles`.`last_name`, `account_profiles`.`profile_id`, `account_profiles`.`photo`, `membership_classes`.`class_name` FROM `account_profiles` ".
			"INNER JOIN `account_permissions` ON `account_profiles`.`account_id` = `account_permissions`.`account_id` && `account_permissions`.`role_id` = 2 ".
			"LEFT JOIN `accounts` ON `account_profiles`.`account_id` = `accounts`.`account_id` ".
			"LEFT JOIN `membership_classes` ON `account_profiles`.`class_id` = `membership_classes`.`class_id` ".
			"WHERE `account_profiles`.`facility_id` = ? && `accounts`.`status` = ? && `accounts`.`showhide` = 0 ".
			"ORDER BY `membership_classes`.`ordering`, `account_profiles`.`last_name`, `account_profiles`.`first_name`", array($facility_id, 'Active'));
			if($query && !$db->error() && $db->num_rows()){
				$facility['members'] = $db->fetch_array();
				// echo "<pre> facility members";
				// print_r($facility['members']);
				// echo '</pre>';

				$page['page_panels']['members'] = array(
					'panel_id' => 'members',
					'panel_type' => 'standard',
					'title' => '',
					'show_title' => 0,
					'content' => ''
				);
			}
			
			// //Attached gallery
			// if(!empty($facility['gallery_id'])){
			// 	$gallery = $SiteBuilder->get_attached_gallery($facility['gallery_id']);
			// 	$page['page_panels']['gallery'] = array(
			// 		'panel_id' => 'gallery',
			// 		'panel_type' => 'gallery',
			// 		'title' => $gallery['name'],
			// 		'show_title' => true,
			// 		'gallery_id' => $facility['gallery_id'],
			// 		'gallery_limit' => '',
			// 		'gallery' => $gallery
			// 	);
			// }
			
			// //Upcoming events
			// $page['page_panels']['events'] = array(
			// 	'panel_id' => 'events',
			// 	'panel_type' => 'events',
			// 	'title' => 'Upcoming Events',
			// 	'show_title' => 1,
			// 	'content' => ''
			// );
			
		}else{
			unset($facility_id);
		}
	}
}

?>