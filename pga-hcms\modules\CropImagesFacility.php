<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Re-Crop, copy original image to cropping directories and add to crop queue
if(isset($_POST['recrop']) && ($_POST['item_id'] ?? ITEM_ID)){

	$fieldname   = $_POST['recrop'];
	$item_id    = $_POST['item_id'] ?? ITEM_ID;
	$old_image   = $records_arr[$item_id][$fieldname] ?? false;
	$image_title = $records_arr[$item_id]['image_name'] ?? $records_arr[$item_id]['name'] ?? $records_arr[$item_id]['title'] ?? '';

	// Determine which uploader to use based on fieldname
	$uploader = ($fieldname == 'logo') ? $CMSUploaderLogo : $CMSUploaderBanner;

	//Upload re-cropped image with a new name and update the DB
	if($old_image){
		try{
			$newname = $uploader->upload_image($fieldname, $image_title, $old_image, true);
			$db->query("UPDATE $record_db SET $fieldname = ? WHERE $record_id = ?", [$newname, $item_id]);

			//Error - Remove image and attempt to set NULL
			if($db->error()){
				$uploader->bulk_delete([$fieldname => $old_image]);
				$db->query("UPDATE $record_db SET $fieldname = ? WHERE $record_id = ?", [NULL, $item_id]);

				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: ".($_POST['redirect'] ?? false ?: PAGE_URL));
				exit();
			}

		}catch(Exception $e){
			$CMSBuilder->set_system_alert($e->getMessage(), false);
		}
	}else{
		$CMSBuilder->set_system_alert('Image does not exist.', false);
	}


//Crop images
}else if(isset($_POST['crop'])){
	$item_id = $_POST['item_id'] ?? ITEM_ID;

	//Do crop
	include("includes/jcropimagesFacility.php");

	// After cropping, check if more crops are needed
	$logo_crops_remaining = $CMSUploaderLogo->crop_queue();
	$banner_crops_remaining = $CMSUploaderBanner->crop_queue();

	// If there are still crops to process, redirect back to continue cropping
	if(!empty($logo_crops_remaining) || !empty($banner_crops_remaining)){
		// Redirect back to the same page to show next crop queue
		header("Location: " . PAGE_URL . "?action=edit&item_id=" . $item_id);
		exit();
	}

	// No more crops needed, check position step and finish
	$CMSUploaderLogo->set_position_params($records_arr[$item_id]);
	$CMSUploaderBanner->set_position_params($records_arr[$item_id]);

	if(empty($CMSUploaderLogo->position_queue()) && empty($CMSUploaderBanner->position_queue())){
		$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
		header("Location: ".($_POST['redirect'] ?? false ?: PAGE_URL));
		exit();
	}


//Position images
}else if(isset($_POST['position'])){
	$item_id = $_POST['item_id'] ?? ITEM_ID;

	//Save position selection
	$db->new_transaction();
	foreach($_POST['image_position'] as $name => $position){
		$db->query("UPDATE `$record_db` SET `$name` = ? WHERE `$record_id` = ?", [$position, $item_id]);
	}

	if(!$db->error()){
		$db->commit();
		$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
		header("Location: ".($_POST['redirect'] ?? false ?: PAGE_URL));
		exit();

	}else{
		$db->rollback();
		$CMSBuilder->set_system_alert('Unable to update position.', false);
	}

// Display crop form
}else{
	include("includes/jcropimagesFacility.php");
}

?>