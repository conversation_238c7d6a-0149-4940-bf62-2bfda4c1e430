<?php
//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Restrict to members
if(!defined('MEMBER_ACCESS') || !MEMBER_ACCESS){
	exit();
}

//Manage profile
if(ACTION == 'add' || (ACTION == 'edit' && array_key_exists(ITEM_ID, $profiles))){

	//Panel title
	$page['page_panels'][$panel_id]['title'] = (ACTION == 'add' ? 'Add' : 'Edit').' Billing Profile';
	$page['page_panels'][$panel_id]['show_title'] = true;
	


	//Display form
	$html .= '<form method="post" action="">
		<p><small>Required Fields!!!</small> <strong class="color-red">*</strong></p>

		<h4>Billing Information</h4>
		<div class="form-grid">
			<div class="form-field">
				<label for="bill_address1">Street Address <strong class="color-red">*</strong></label>
				<input type="text" name="bill_address1" id="bill_address1" class="input' .(in_array('bill_address1', $required) ? ' required' : ''). '" value="' .htmlspecialchars($profile['bill_address1'] ?? $Account->address1 ?? '', ENT_QUOTES, 'UTF-8'). '" />
			</div>';
			// <div class="form-field">
			// 	<label for="bill_province">Province/State <strong class="color-red">*</strong></label>';
			// 	// <select name="bill_province" id="bill_province" class="select' .(in_array('bill_province', $required) ? ' required' : ''). '">
			// 	// 	<option value="">- Select -</option>';
				// 	$selected_province = $profile['bill_province'] ?? $Account->province ?? '';
				// 	if (isset($provinces) && is_array($provinces)) {
				// 		$html .= '<optgroup label="Canada">';
				// 		foreach ($provinces as $province) {
				// 			$name = htmlspecialchars($province[0], ENT_QUOTES, 'UTF-8');
				// 			$code = htmlspecialchars($province[1], ENT_QUOTES, 'UTF-8');
				// 			$selected = ($selected_province == $code ? ' selected' : '');
				// 			$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
				// 		}
				// 		$html .= '</optgroup>';
				// 	}
				// 	if (isset($states) && is_array($states)) {
				// 		$html .= '<optgroup label="United States">';
				// 		foreach ($states as $state) {
				// 			$name = htmlspecialchars($state[0], ENT_QUOTES, 'UTF-8');
				// 			$code = htmlspecialchars($state[1], ENT_QUOTES, 'UTF-8');
				// 			$selected = ($selected_province == $code ? ' selected' : '');
				// 			$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
				// 		}
				// 		$html .= '</optgroup>';
				// 	}
				// $html .= '</select>';


				//
				$html .= '<div class="form-field">
				<label for="bill_province">Province/State <strong class="color-red">*</strong></label>
				<select name="bill_province" id="bill_province" class="select' .(in_array('bill_province', $required) ? ' required' : ''). '">
				<option value="">- Select -</option>';
				$selected_province = $profile['bill_province'] ?? $Account->province ?? '';
				if(isset($provinces)&&is_array($provinces)):
					$html .= '<optgroup label="Canada">';
					foreach($provinces as $code=>$name){
						// $html .= "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['province']??null,$Account->province??'',$code);
						$selected = ($selected_province == $code ? ' selected' : '');
						$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
						// echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";
					}
					$html .= '</optgroup>';
				endif;
				if(isset($states)&&is_array($states)):
					$html .= '<optgroup label="United States">';
					foreach($states as $code=>$name){
						//  $html .= "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['province']??null,$Account->province??'',$code);echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";
						$selected = ($selected_province == $code ? ' selected' : '');
						$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
					}
					$html .= '</optgroup>';
				endif;
				$html .= '</select></div>';


			// $html .= '</div>';
			$html .= '<div class="form-field">
				<label for="bill_address2">Unit No.</label>
				<input type="text" name="bill_address2" id="bill_address2" class="input' .(in_array('bill_address2', $required) ? ' required' : ''). '" value="' .htmlspecialchars($profile['bill_address2'] ?? $Account->address2 ?? '', ENT_QUOTES, 'UTF-8'). '" />
			</div>';
			$html .= '<div class="form-field">
				<label for="bill_postalcode">Postal Code <strong class="color-red">*</strong></label>
				<input type="text" name="bill_postalcode" id="bill_postalcode" class="input' .(in_array('bill_postalcode', $required) ? ' required' : ''). '" value="' .htmlspecialchars($profile['bill_postalcode'] ?? $Account->postal_code ?? '', ENT_QUOTES, 'UTF-8'). '" />
			</div>
			<div class="form-field">
				<label for="bill_city">City/Town <strong class="color-red">*</strong></label>
				<input type="text" name="bill_city" id="bill_city" class="input' .(in_array('bill_city', $required) ? ' required' : ''). '" value="' .htmlspecialchars($profile['bill_city'] ?? $Account->city ?? '', ENT_QUOTES, 'UTF-8'). '" />
			</div>
			<div class="form-field">
				<label for="bill_country">Country <strong class="color-red">*</strong></label>
				<select name="bill_country" id="bill_country" class="select country' .(in_array('bill_country', $required) ? ' required' : ''). '">
					<option value="">- Select -</option>';
					$selected_country = $profile['bill_country'] ?? $Account->country ?? '';
					if (isset($countries) && is_array($countries)) {
						foreach ($countries as $code => $name) {
							$code_esc = htmlspecialchars($code, ENT_QUOTES, 'UTF-8');
							$name_esc = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
							$selected = ($selected_country == $code ? ' selected' : '');
							$html .= '<option value="' .$code_esc. '"' .$selected. '>' .$name_esc. '</option>';
						}
					}
				$html .= '</select>
			</div>
		</div>

		<hr style="grid-column: 1 / -1; margin: 20px 0;" />

		<h4>Credit Card Information</h4>
		<div class="form-grid">
			<div class="form-field">
				<label for="ccname">Cardholder Name <strong class="color-red">*</strong></label>
				<input type="text" name="ccname" id="ccname" class="input' .(in_array('ccname', $required) ? ' required' : ''). '" value="' .htmlspecialchars($_POST['ccname'] ?? $profile['ccname'] ?? '', ENT_QUOTES, 'UTF-8'). '" />
			</div>
			<div class="form-field expiry-date-fields" style="display: grid; grid-template-columns: 1fr 1fr; column-gap: 10px;">
				<div>
					<label for="exp_month">Expiry Date <strong class="color-red">*</strong></label>
					<select name="exp_month" id="exp_month" class="select' .(in_array('exp_month', $required) ? ' required' : ''). '">
					<option value="">MM</option>';
					$selected_month = $_POST['exp_month'] ?? $profile['exp_month'] ?? '';
					foreach($months as $key=>$value){
						$key_padded = str_pad((string)$key, 2, '0', STR_PAD_LEFT);
						$selected = ($selected_month == $key_padded ? ' selected' : '');
						$html .= '<option value="' .$key_padded. '"' .$selected. '>' .$key_padded. '</option>';
					}
					$html .= '</select>
				</div>
				<div>
					<label for="exp_year" style="visibility: hidden;">Expiry Year</label>
					<select name="exp_year" id="exp_year" class="select' .(in_array('exp_year', $required) ? ' required' : ''). '">
					<option value="">YYYY</option>';
					$selected_year = $_POST['exp_year'] ?? $profile['exp_year'] ?? '';
					for($y=date('Y'); $y<=(date('Y')+20); $y++){
						$year_short = substr((string)$y, -2);
						$selected = ($selected_year == $year_short ? ' selected' : '');
						$html .= '<option value="' .$year_short. '"' .$selected. '>' .$y. '</option>';
					}
					$html .= '</select>
				</div>
			</div>
			<div class="form-field">
				<label for="ccnumber">Card Number <strong class="color-red">*</strong> &nbsp; ';
				foreach($payment_options as $payopt){
					$cctype = ($payopt['type'] == 'MC' ? 'mastercard' : strtolower($payopt['type']));
					$html .= '<i class="fa fa-cc-' .$cctype. ' fa-lg" title="' .$payopt['name']. '"></i> ';
				}
				$html .= '</label>
				<input type="text" name="ccnumber" id="ccnumber" class="input number' .(in_array('ccnumber', $required) ? ' required' : ''). '" value="' .(isset($_POST['ccnumber']) ? htmlspecialchars($_POST['ccnumber'], ENT_QUOTES, 'UTF-8') : (isset($profile['ccnumber']) ? '**** **** **** '.$profile['ccnumber'] : '')). '" maxlength="16" />
			</div>';
			// <div class="form-field">
			// 	<label for="bill_postalcode_2">Postal Code</label>
			// 	<input type="text" name="bill_postalcode_2" id="bill_postalcode_2" class="input" value="' .htmlspecialchars($_POST['bill_postalcode_2'] ?? $profile['bill_postalcode'] ?? $Account->postal_code ?? '', ENT_QUOTES, 'UTF-8'). '" />
			// </div>
		echo '</div>

			<div class="form-actions" style="margin-top: 30px; display: flex; justify-content: space-between; align-items: center;">
			<a href="' .$page['page_url']. '" class="button primary red back-button light" style="font-weight: bold; text-transform: uppercase;">Back to My Account<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
			<div style="display: flex; align-items: center;">
				'.(ACTION == 'edit' ? '<button type="button" id="delete-button" class="button delete-button confirm black solid primary" style="margin-right: 15px;"><span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span>DELETE</button>' : '').'
				<button type="submit" name="save" value="save" class="button solid primary red save-button">SAVE PROFILE<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
			</div>
		</div>

		<input type="hidden" name="xid" value="'.$_COOKIE['xid'].'" />
	</form>';


//View profiles
}else{

    // echo "billing-profiles pages";exit;


	$html .= $page['page_panels'][$panel_id]['content'];

	$html .= '<div class="billing-profiles-list">';
	$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%">';
	if(!empty($profiles)){
		$html .= '<tr>
			<th class="left">Cardholder </th>
			<th class="left">Credit Card </th>
			<th width="130px" class="left">Action </th>
		</tr>';

		foreach($profiles as $profile){

			$html .= '<tr>
				<td>' .$profile['ccname']. '</td>
				<td>' .$profile['cctype'].' **** **** **** ' .$profile['ccnumber']. ' &nbsp; ' .substr($profile['ccexpiry'], 0, 2).'/'.substr($profile['ccexpiry'], -2, 2).($profile['expired'] ? ' &nbsp; <small class="color-red"><i class="fa fa-exclamation-triangle"></i> Credit card is expired</small>' : ''). '</td>
				<td align="center" ><a href="' .$page['page_url'].'?action=edit&id='.$profile['billing_id'].'" class="button simple"><i class="fas fa-edit style="font-size:50px; padding:5px;"></i></a></td>
			</tr>';
		}

	}else{
		$html .= '<tr><td class="nobg">No billing profiles found' .(isset($_GET['search']) ? ' matching <strong>`'.$_GET['search'].'`</strong>' : ''). '.</td></tr>';

	}
	$html .= '</table>';
	$html .= '</div>';

	//TODO CHECK FOR PRODUCTION
	if(USER_LOGGED_IN != 10213){ //Public account restricted
		// $html .= '<a href="'.$page['page_url'].'?action=add" class="button f_right solid primary">Add Billing Profile<span> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>';
		$html .= '<a href="'.$page['page_url'].'?action=add"  class="button primary red f_right">ADD BILLING PROFILE<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
		';
	}
}

//Set panel content
// $page['page_panels'][$panel_id]['content'] = $html;

?>