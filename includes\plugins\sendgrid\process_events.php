<?php
$include_path = "/home/<USER>/public_html/"; //REPLACE_ME (change this to your web root)

header('HTTP/1.1 200 OK',true,200);

include $include_path.'includes/config.php';
include $include_path.'includes/database.php';

$query = $db->query("SELECT `sendgrid_key` FROM `global_settings`");
$result = $db->fetch_array();
if(isset($result[0]['sendgrid_key']) && !empty($result[0]['sendgrid_key'])){
	require_once($include_path."includes/plugins/sendgrid/sendgrid-php.php");
	require_once($include_path."modules/classes/SendGrid.class.php");
	$sendgrid = new MassMail($result[0]['sendgrid_key']);
}

//parse data to array
$json = file_get_contents("php://input");
$data = json_decode($json, true);

//write received data to log
$fh = fopen($include_path.'logs/sendgrid.log', 'a+'); //create this file manually if you want to keep logs
fwrite($fh, $json."\n");
fclose($fh);

//get each event sent
foreach ($data as $event){
	
	$event_id = "";
	if(isset($event['sg_event_id'])){
		$event_id = $event['sg_event_id'];	
	}
	
	if(isset($event['marketing_campaign_id'])){
		$newsletter_id = $event['marketing_campaign_id'];	
	}
	if(isset($event['item_id'])){
		$item_id = $event['item_id'];	
	}
	
	if(isset($event['category']) && is_array($event['category'])){
		$category = implode(",",$event['category']);
	} else {
		$category = $event['category'];
	}
	
	$params = array(
		$event_id,
		@$event['event'],
		@$event['email'],
		@$newsletter_id,
		@$item_id,
		@$event['status'],
		str_replace("'", "&rsquo;", @$event['reason']),
		str_replace("'", "&rsquo;", @$event['response']),
		@$event['type'],
		@$event['attempt'],
		@$event['url'],
		@$category,
		date("Y-m-d H:i:s")
	);
	
	//insert event log
	$query = $db->query("INSERT INTO `email_events` (`sg_event_id`, `event`, `email`, `newsletter_id`, `item_id`, `status`, `reason`, `response`, `type`, `attempt`, `url`, `category`, `date`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)",$params);
		
	if($query && !$db->error()){
		// If second time this email has bounced, automatically unsubscribe
		if(@$event['event'] == "bounce"){
			$params = array("bounce",@$event['email']);
			$query = $db->query("SELECT COUNT(`event_id`) AS `total` FROM `email_events` WHERE `event` = ? AND `email` = ?",$params);
			if($query && !$db->error()){
				$result = $db->fetch_array();
				$total_bounces = $result[0];
				$total_bounces = number_format($total_bounces['total'],0);
			}			
			if($total_bounces > 1){
				// add email to global unsubscribe list
				$unsubscribe = $sendgrid->global_unsubscribe(array(@$event['email']));
			}
		}//bounced
	}
}

?>