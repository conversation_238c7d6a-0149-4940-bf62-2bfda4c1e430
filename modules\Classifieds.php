<?php 

//All Classified Listings
if(PAGE_ID == $_sitepages['classifields']['page_id']){
	$panel_id = 60; //ID of the Classifieds Panel
	
	//Pagination
	$pg = (isset($_GET['pg']) ? $_GET['pg'] : 1);
	$limit = 20;

	$andor = false; //Filter query for either or both the checklist and the searchbar ()
	$search = false; //Confirm whether that the search bar is being utilized (for highlighted)

	//Initilize data vars
	$classifieds = array();
	$categories = array();
	$facilities = array();
	$params = array();  //Query parameters
	$searchable_params = array(
	//Columns to search
		'title',
		'description',
		'city',
		'province',
		'facility_name'
	);

	if(isset($classified_id)){
		unset($classified_id);
	}

	//Build filtered query
	$q = "SELECT `classifieds`.*, `facilities`.`facility_name`, `facilities`.`city`, `facilities`.`province` FROM `classifieds` ". 
	"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `classifieds`.`facility_id` ".
	"WHERE `classifieds`.`showhide` = 0".(!USER_LOGGED_IN ? " AND `classifieds`.`public` = 1" : "");	
	// echo $q;
	// exit;

	//Filter by facility
	if(isset($_GET['facility_ids']) && !empty($_GET['facility_ids'])){
		$andor = true;
		$q .= " AND `classifieds`.`facility_id` IN (";
		foreach ($_GET['facility_ids'] as $value){
			$q .= "?,";
			$params[] = $value;
		}
		$q = substr($q, 0, -1).")";

	}

	//Filter by location
	if(isset($_GET['province']) && !empty($_GET['province'])){
		$andor = true;
		$q .= " AND `facilities`.`province` IN (";
		foreach ($_GET['province'] as $value){
			if($value == 'Outside'){
				foreach($provinces as $prov){
					if($prov[1] != 'AB'){
						$q .= "?,";
						$params[] = $prov[1];
					}
				}
			}else{
				$q .= "?,";
				$params[] = $value;
			}
		}
		$q = substr($q, 0, -1).")";

	}

	//Text search
	if(isset($_GET['filter_search']) && $_GET['filter_search']){
		$search = true;
		$q .= ($andor ? " OR (" : " AND (");
		foreach ($searchable_params as $value){
			$q .= $value . " LIKE ? OR ";
			$params[] = '%'.$_GET['filter_search'].'%';
		}
		$q = substr($q, 0, -4). ")"; //Remove excess " OR "

	}
	
	$q .= " ORDER BY `classifieds`.`date_added` DESC, `classifieds`.`classified_id` DESC;"; //Close query
	
	$classified_query = $db->query($q, $params);
	if($classified_query && !$db->error()){
		$total_classifieds = $db->num_rows();
		if($total_classifieds > 0){
			$classifieds = $db->fetch_array();
			
			//Pagination
			if($pg != 'all'){
				$start = (($pg-1)*$limit);
				$end = $limit;
			}else{
				$start = 0;
				$end = $total_classifieds;
			}
			$classifieds = array_slice($classifieds, $start, $end);
			
		}
		if($search){
			
			//Highlight searched text
			foreach ($classifieds as $key => $classified){
				foreach ($classified as $key2 => $value){
					if(in_array($key2, $searchable_params)){
						//Search for text
						$pos = strpos(strtolower($value), strtolower($_GET['filter_search']));

						if($pos !== false){
							//Surround found text in a span
							$classifieds[$key][$key2] = substr_replace($value, '<span class="found-text">'.substr($value, $pos, strlen($_GET['filter_search'])).'</span>', $pos, strlen($_GET['filter_search']));
						}
					}
				}
			}
		}
	}

	//Load facility data
	$facility_query = $db->query("SELECT * FROM `facilities` WHERE `showhide` = 0 ORDER BY `facility_name`");
	if($facility_query && !$db->error()){
		if($db->num_rows() > 0){
			$facilities = $db->fetch_array();
		}
	}

//Single Classified Display
}else if(PARENT_ID == $_sitepages['classifields']['page_id'] && PAGE_ID == ''){

	$pagebits = $SiteBuilder->get_pagebits((trim($_sitepages['classifields']['slug']) != '' ? $_sitepages['classifields']['slug'] : $_sitepages['classifields']['page']));

	if(!isset($pagebits[3]) || $pagebits[3] == ''){
		
		//Grab classified ID from URL
		$classified_bits = explode("-", $pagebits[2]);
		$classified_id = $classified_bits[count($classified_bits)-1];

		//Grab classified data
		$query = $db->query("SELECT `classifieds`.*, `facilities`.`facility_name`, `facilities`.`city`, `facilities`.`province` FROM `classifieds` ".
		"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `classifieds`.`facility_id` ".
		"WHERE `classifieds`.`showhide` = 0 AND `classified_id` = ?", array($classified_id));
		if($query && !$db->error()){
			if($db->num_rows() > 0){

				//Load classified data
				$classified = $db->fetch_array();
				$classified = $classified[0];
				
				if (USER_LOGGED_IN || $classified['public'] == 1) {
					$error404 = false;
					$parent = $SiteBuilder->get_page_content($page['parent_id']); //Grab main page content
					$page['page_title'] = $parent['name'];
					$page['name'] = $classified['title'];
					$last_path_bit = array_pop($breadcrumbs);
					$last_path_bit['name'] = $classified['title'];
					$breadcrumbs[] = $last_path_bit;
					$page['content'] = '';
					$page['meta_canonical'] = $parent['page_url'] .$classified['page']. '-' .$classified['classified_id']. '/';
					$page['meta_title'] = $classified['meta_title'] ?: ($classified['title']. ' | ' .$parent['page_title']);
					$page['meta_description'] = $classified['meta_description'] ?: $parent['meta_description'];
					$page['banner_image'] = $parent['banner_image'];
					$page['banner_image_alt'] = $parent['banner_image_alt'];
					$page['page_panels'] = array();
					
					//Send to correct path
					if(($classified['page']. '-' .$classified['classified_id']) != $pagebits[2]){
						header("HTTP/1.1 301 Moved Permanently");
						header('Location: ' .$parent['page_url'] .$classified['page']. '-' .$classified['classified_id']. '/');
						exit();
					}
				}


			}else{
				unset($classified_id);
			}
		}else{
			unset($classified_id);
		}

	}
}
?>