@import (reference) "../../../core/less/core.less";
@import (reference) "definitions.less";

body.landing-page{
	#page-hero{
		--content-gap: 40px;
		--column-width: calc( (100% - var(--content-gap)) / 2 );

		position: relative;
		background: var(--theme-bg);
		text-align: left;

		.page-hero-wrapper{
			.flexbox(column; center; flex-start);
			gap: var(--content-gap);
			padding-block: 30px;
			text-align: inherit;
		}

		#page-hero-image{
			.position();
			z-index: -1;
			.overlay{
				height: 100%;
				max-height: none;
				mask-image: none;
				opacity: .5;
			}
		}

		#page-header{
			.page-subtitle{
				p:last-child{padding-bottom: 0;}
			}
		}

		#landing-form{
			--form-columns: 1;
			--field-border: @color-gray-light;
			--field-border-hover: @color-gray-light;
			--field-border-width: 1px;
			--field-placeholder: transparent;
			width: 100%;
			max-width: 480px;
			margin: 0 auto;
			padding: 20px;
			background: @color-light;

			label{
				text-align: left;
			}

			.landing-form-title{
				&:extend(.font-h3);
				margin: 0;
			}

			.landing-form-title,
			.landing-form-description{
				+ fieldset{
					margin-top: 20px;
				}
			}
		}
	}
}

@media @tablet-l{
	body.landing-page{
		#page-hero{
			#landing-form{
				padding: 30px;
			}
		}
	}
}

@media @notebook{
	body.landing-page{
		#page-hero{
			.page-hero-wrapper{.flexbox(row wrap; flex-start; center);}

			#page-logo{
				width: 100%;
			}

			#page-header,
			#landing-form{
				width: var(--column-width);
				.flex(1 0 auto);
			}

			#landing-form{
				max-width: 600px;
			}
		}
	}
}