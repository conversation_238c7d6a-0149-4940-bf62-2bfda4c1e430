<?php if (!LANDING) { ?>
<!--page navigation-->
<div id="page-navbar" class="<?php echo (PAGE_ID == $_sitepages['home']['page_id'] ? 'theme-transparent' : ''); ?>">
	<div class="container">
		<div id="page-contact-top">
			<?php // include(include_path("includes/templates/contact-info.php")); ?>
			<?php
			echo '<ul class="page-contact">
				<li><a href="/make-donation" class="link">MAKE A DONATION</a></li>
				<li>';
			//inside li code <i class="fas fa-search search"></i>
			 if($_sitepages['search']['showhide'] < 2){
				echo '<form name="site-search" id="site-search" action="'.$_sitepages["search"]["page_url"].'" method="get" class="f_left">
						<button type="button" name="button" class="search-btn f_left" ><i class="fa fa-search"></i></button>
						<input type="text" name="search" class="input f_left search_input" value="" placeholder="Search..." autocomplete="off" />
					</form>';
				} 
			echo "</li>";
				if(!USER_LOGGED_IN){ 
				echo '<li><a href="' .$path. 'login/"  class="button ternary light" href="#"> <span class="left-border"></span> <span class="right-border"></span> Sign In</a></li>';
				}
				if(USER_LOGGED_IN){ 
					echo '<li><a href="' .$path. 'modules/account/Logout.php" class="button ternary light" id="logout-btn">
					<span class="top-border"></span>
					<span class="bottom-border"></span>
					<span class="left-border"></span>
					<span class="right-border"></span>
					Logout</a></li>';
				}
			echo '</ul>';
			?>
		</div>

		<div class="navbar-wrapper">
			<a href="<?php echo $path; ?>" id="page-logo" title="<?php echo $global['company_name']; ?>">
				<?php load_svg('logo'); ?>
			</a>

			<div class="navbar-menu">
				<nav id="main-navigation" class="nav-menu">
					<ul><?php echo NAVIGATION; ?><li id="more-link" style="display:none;"><span tabindex="0" class="more-icon" aria-label="Other Links" aria-role="Button"></span><ul></ul></li></ul>
				</nav>
			</div>

			<span tabindex="0" title="Open Menu" id="menu-toggle" class="open">
				<span></span>
				<span></span>
				<span></span>
			</span>
		</div>
	</div>
</div>
<?php } ?>

<?php include(include_path("includes/templates/slideshow.php")); ?>
<?php include(include_path("includes/templates/banner.php")); ?>
<?php include(include_path("includes/templates/landing/landing-page.php")); ?>
<?php include(include_path("includes/templates/breadcrumbs.php")); ?>