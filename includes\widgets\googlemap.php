<?php

if(GOOGLE_MAP){
	if(!empty($map_locations)){
		$init_location = $map_locations[0];
		
		//Location buttons
		if(count($map_locations) > 1){
			$map_html .= '<div class="hidden">
				<div id="switch-location-modal" class="hidden-modal" title="Choose a Location">';
				foreach($map_locations as $loc){
					$map_html .= '<a href="#location-'.$loc['location_id'].'" class="switch-location-btn'.($loc['location_id'] == $init_location['location_id'] ? ' active' : '').'" data-address="'.$loc['address'].'" data-name="'.$loc['name'].'" data-gpslat="'.$loc['gpslat'].'" data-gpslong="'.$loc['gpslong'].'" data-zoom="'.$loc['zoom'].'"><i class="fa fa-map-marker"></i> '.$loc['name'].'</a><br />';
				}
				$map_html .= '</div>
			</div>';
			$map_html .= '<a id="map-locations">Change Location</a>';
		}

		//Init first location map
		$map_html = '<div id="google-map">
			<div id="map-buttons" class="right">
				<a href="http://maps.google.ca/maps?q='.urlencode($init_location['name'].' '.$init_location['address']). '" target="_blank" id="google-btn">Open in Google Maps</a>
			</div>';

			//Map container
			$map_html .= '<div id="load-map" data-address="' .$init_location['address']. '" data-name="' .$init_location['name']. '" data-gpslat="' .$init_location['gpslat']. '" data-gpslong="' .$init_location['gpslong']. '" data-zoom="' .$init_location['zoom']. '"></div>
		</div>';

		//Contact map
		if(PAGE_ID == $_sitepages['contact']['page_id']){
			$google_map = $map_html;

		//Footer map
		}else{
			$footer_map = $map_html;
			echo $footer_map;
		}
	}
}

?>