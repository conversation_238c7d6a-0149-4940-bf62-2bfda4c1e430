/*! lightgallery - v1.2.19 - 2016-05-17
* http://sachinchoolur.github.io/lightGallery/
* Copyright (c) 2016 Sachin N; Licensed Apache 2.0 */
.lg-outer.fb-comments .lg-img-wrap {
  padding-right: 400px !important; }
.lg-outer.fb-comments .fb-comments {
  height: 100%;
  overflow-y: auto;
  position: absolute;
  right: 0;
  top: 0;
  width: 420px;
  z-index: 99999;
  background: #fff url("../img/loading.gif") no-repeat scroll center center; }
  .lg-outer.fb-comments .fb-comments.fb_iframe_widget {
    background-image: none; }
    .lg-outer.fb-comments .fb-comments.fb_iframe_widget.fb_iframe_widget_loader {
      background: #fff url("../img/loading.gif") no-repeat scroll center center; }
.lg-outer.fb-comments .lg-toolbar {
  right: 420px;
  width: auto; }
.lg-outer.fb-comments .lg-actions .lg-next {
  right: 420px; }
.lg-outer.fb-comments .lg-item {
  background-image: none; }
  .lg-outer.fb-comments .lg-item.lg-complete .lg-img-wrap {
    background-image: none; }
.lg-outer.fb-comments .lg-img-wrap {
  background: url(../img/loading.gif) no-repeat scroll center center transparent; }
.lg-outer.fb-comments .lg-sub-html {
  padding: 0;
  position: static; }

/*# sourceMappingURL=lg-fb-comment-box.css.map */
