<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

$row  = !isset($_POST['save']) ? ($row ?? []) : $_POST;

echo '<form action="" method="post" enctype="multipart/form-data">';
//var_dump(isset($row['send_copy']) && $row['send_copy']);exit;
//Field details
echo '<div class="panel">
	<div class="panel-header">'.$record_name.' Details
		<span class="panel-toggle fas fa-chevron-up"></span>
	</div>
	<div class="panel-content">
		<div class="flex-container">
			<div class="form-field">
				<label>Field Label'.(in_array('label', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
				<input type="text" name="label" value="'.($row['label'] ?? '').'" class="input'.(in_array('label', $required) ? ' required' : '').'" />
			</div>

			<div class="form-field">
				<div class="flex-container">
				<label>Field Type'.(in_array('type', $required_fields) ? ' <span class="required">*</span>' : '').'</label>

				<span id="send-copy" class="" style="' .(isset($row['type']) && $row['type'] == 'email' ? 'display:block;' : 'display:none;'). '">
					<input type="checkbox" name="send_copy" id="send_copy" value="1" class="checkbox" ' .(isset($row['send_copy']) && $row['send_copy'] ? ' checked' : ''). ' />
					<label for="send_copy"><small>Send copy of submission</small></label>
				</span>

				<span id="notify-facility" class="" style="' .(isset($row['type']) && $row['type'] == 'facilities' ? 'display:block;' : 'display:none;'). '">
					<input type="checkbox" name="send_copy_facility" id="send_copy_facility" value="1" class="checkbox" ' .(isset($row['send_copy_facility']) && $row['send_copy_facility'] ? ' checked' : ''). ' />
					<label for="send_copy_facility"><small>Send to Head/Exec. Pros</small></label>
				</span>
				</div>
				<select name="type" id="field-type" class="select animation-control'.(in_array('type', $required) ? ' required' : '').'"  onchange="fieldOptions();">
					<option value="">- Select -</option>';
					foreach($field_types as $type){
						if($type == 'fieldset') continue;
						echo '<option value="'.$type.'"'.(in_array($type, $field_has_options) ? ' data-toggle=".field-options"' : '').(($row['type'] ?? '') == $type ? ' selected' : '').'>'.ucwords($type).'</option>';
					}
				echo '</select>
			</div>
			<div id="max-chars" class="form-field" style="' .(isset($row['type']) && $row['type'] == "textarea" ? "display:block;" : "display:none;"). '">
				<label for="max-chars1">Maximum Words ' .$CMSBuilder->tooltip('Maximum Words', 'Maximum number of words that can be entered into this field. Leave blank for no limit.'). '</label>
				<input type="number" name="max_chars" value="' .(isset($row['max_chars']) ? $row['max_chars'] : ''). '" class="input' .(in_array('max_chars', $required) ? ' required' : ''). '" />
			</div>
			<div class="form-field">
				<label>Fieldset'.(in_array('parent_id', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Fieldset', 'Display field underneath this group. Fields not assigned to a fieldset will always appear first.').'</label>
				<select name="parent_id" class="select">
					<option value="">None</option>';
					foreach($fieldsets as $fieldset){
						echo '<option value="'.$fieldset[$record_id].'"'.(($row[$record_id] ?? '') == $fieldset[$record_id] ? ' selected' : '').'>'.$fieldset['label'].'</option>';
					}
				echo '</select>
			</div>

			<div class="form-field">
				<label>Required Field'.(in_array('required', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
				<select name="required" class="select'.(in_array('required', $required) ? ' required' : '').'">
					<option value="0">No</option>
					<option value="1"'.(($row['required'] ?? '') == '1' ? ' selected' : '').'>Yes</option>
				</select>
			</div>

			<div class="form-field">
				<label>Description'.(in_array('description', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
				<input type="text" name="description" value="'.($row['description'] ?? '').'" class="input'.(in_array('description', $required) ? ' required' : '').'" />
			</div>

			<div class="form-field">
				<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
				<select name="ordering" class="select">
					<option value="101">Default</option>';
					for($i=1; $i<101; $i++){
						echo '<option value="' .$i. '"' .(($row['ordering'] ?? '') == $i ? ' selected' : ''). '>' .$i. '</option>';
					}
				echo '</select>
			</div>
		</div>
	</div>
</div>';

//Field options
echo '<div class="panel field-options"'.(!in_array(($row['type'] ?? ''), $field_has_options) ? ' style="display:none;"' : '').'>
	<div class="panel-header">Field Options
		<span class="panel-toggle fas fa-chevron-up"></span>
	</div>
	<div class="panel-content nopadding">
		<table id="field-options-table">
			<tbody>';
				$ocount = 0;
				foreach(($row['field_options'] ?? []) as $option){
					echo '<tr class="field-option-row">
						<td class="nopadding-b">
							<div class="flex-container">
								<div class="form-field">
									<label>Option Name <span class="required">*</span></label>
									<input type="text" name="option_name[]" value="'.($option['option_name'] ?? '').'" class="input'.(in_array('option_name_'.$ocount, $required) ? ' required' : '').'" />
								</div>

								<div class="form-field">
									<label>Numerical Order</label>
									<select name="option_ordering[]" class="select">
										<option value="101">Default</option>';
										for($i=1; $i<101; $i++){
											echo '<option value="' .$i. '"' .(($option['ordering'] ?? '') == $i ? ' selected' : ''). '>' .$i. '</option>';
										}
									echo '</select>
								</div>

								<div class="flex-column align-end"><button type="button" class="button-sm delete-template-btn"><i class="fas fa-trash-alt nomargin"></i></button></div>
								<input type="hidden" name="option_id[]" value="'.($option['option_id'] ?? '').'" />
							</div>
						</td>
					</tr>';

					$ocount++;
				}
				echo '<tr class="copy-btn-row">
					<td class="right">
						<button type="button" class="copy-template-btn button-sm"><i class="fas fa-plus"></i>Add Option</button>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>';

//Sticky footer
echo '<footer id="cms-footer">
	<div class="flex-container">
		<div class="flex-column right">
			<button type="submit" class="button" name="save" value="save"><i class="fas fa-check"></i>Save Changes</button>
		</div>
		<div class="flex-column left">';
			if(ITEM_ID != ""){
				echo '<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn"' .(!($row['deletable'] ?? true) ? ' disabled' : ''). '><i class="fas fa-trash-alt"></i>Delete</button>';
			}
			echo '<a href="' .$formpage['page_url']. '?action=edit&item_id=' .FORM_ID. '" class="cancel">Cancel</a>
		</div>
	</div>
</footer>';

echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
</form>';

//Template
echo '<table class="hidden">
	<tr id="field-option-template" class="field-option-row">
		<td class="nopadding-b">
			<div class="flex-container">
				<div class="form-field">
					<label>Option Name <span class="required">*</span></label>
					<input type="text" name="option_name[]" value="" class="input'.(in_array('option_name_'.$ocount, $required) ? ' required' : '').'" />
				</div>

				<div class="form-field">
					<label>Numerical Order</label>
					<select name="option_ordering[]" class="select">
						<option value="101">Default</option>';
						for($i=1; $i<101; $i++){
							echo '<option value="' .$i. '">' .$i. '</option>';
						}
					echo '</select>
				</div>

				<div class="flex-column align-end"><button type="button" class="button-sm delete-template-btn"><i class="fas fa-trash-alt nomargin"></i></button></div>
				<input type="hidden" name="option_id[]" value="" />
			</div>
		</td>
	</tr>
</table>';

?>