<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";

	echo "<div class='panel'>";
		echo "<div class='panel-header'>GL Accounts  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

			echo "<thead>";
			echo "<th width='350px'>Account Name</th>";
			echo "<th width='350px'>Account No.</th>";
			echo "<th>Category</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";

			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .$row['gl_name']. "</td>";
					echo "<td>" .$row['gl_number']. "</td>";
					echo "<td>" .$categories[$row['item_no']]. "</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo "</div>";	
	echo "</div>";
	
}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){	
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		//Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Account Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Account Name <span class='required'>*</span></label>
					<input type='text' name='gl_name' value='" .(isset($row['gl_name']) ? $row['gl_name'] : ''). "' class='input" .(in_array('gl_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Account No. <span class='required'>*</span></label>
					<input type='text' name='gl_number' value='" .(isset($row['gl_number']) ? $row['gl_number'] : ''). "' class='input" .(in_array('gl_number', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Default Category " .$CMSBuilder->tooltip('Default Category', 'All payments that fall within this category will default to this account. If necessary, you can override this setting on an individual basis for events and invoices. Only one account can be assigned to each category.'). "</label>
					<select name='item_no' class='select" .(in_array('item_no', $required) ? ' required' : ''). "'>
						<option value=''>- None -</option>";
						foreach($categories as $no=>$name){
							echo "<option value='" .$no. "'" .(isset($row['item_no']) && $row['item_no'] == $no ? ' selected' : ''). ">" .$name. "</option>";
						}
					echo "</select>
				</div>";
			echo "</div>";
		echo "</div>";

		//Sticky footer
		include("includes/widgets/formbuttons.php");
		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	
	echo "</form>";

}

?>