<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('faqs');
	$CMSBuilder->set_widget($_cmssections['faqs'], 'Total FAQs', $total_records);
}

if(SECTION_ID == $_cmssections['faqs']){
	
	//Define vars
	$record_db 	 = 'faqs';
	$record_id 	 = 'faq_id';
	$record_name = 'Question';
	
	//Validation
	$errors 	= false;
	$required 	= [];
	$required_fields = [
		'question', 
		'answer', 
		'category_id'
	];

	//Get categories
	$db->query("SELECT * FROM `faq_categories` ORDER BY `name`");
	$categories = $db->fetch_assoc('category_id');
	
	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.question",
		"faq_categories.name"
	];

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}
	
	//Get records
	$db->query("SELECT `$record_db`.*, `faq_categories`.`name` AS `category` FROM `$record_db` LEFT JOIN `faq_categories` ON `faq_categories`.`category_id` = `$record_db`.`category_id` $where GROUP BY `$record_db`.`$record_id` ORDER BY `faq_categories`.`ordering`, `$record_db`.`ordering`, `$record_db`.`$record_id`", $params);
	$records_arr = $db->fetch_assoc($record_id);
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}

	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){		
			$row = $records_arr[ITEM_ID];
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();	
		}
	}

	
	//Delete item
	if(isset($_POST['delete'])){

		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}
		header("Location: " .PAGE_URL);
		exit();

		
	//Save item
	}else if(isset($_POST['save'])){

		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);
		
		//Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		if(!$errors){

			//Insert to db
			$params = array(
				
				//Insert
				ITEM_ID, 
				$_POST['category_id'], 
				$_POST['question'], 
				str_replace("<p>&nbsp;</p>", "", $_POST['answer']),
				$_POST['showhide'], 
				$_POST['ordering'],
				date("Y-m-d H:i:s"), 
				
				//Update
				$_POST['category_id'], 
				$_POST['question'], 
				str_replace("<p>&nbsp;</p>", "", $_POST['answer']),
				$_POST['showhide'], 
				$_POST['ordering'],
				date("Y-m-d H:i:s")
			);

			$db->query("INSERT INTO `$record_db`(`$record_id`, `category_id`, `question`, `answer`, `showhide`, `ordering`, `last_modified`) VALUES(?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `category_id` = ?, `question` = ?, `answer` = ?, `showhide` = ?, `ordering` = ?, `last_modified` = ?", $params);
			if(!$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}

	}

}

?>