<?php  


//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Edit job post
if(ACTION == 'edit' && array_key_exists(ITEM_ID, $careers)){
	
	//Panel title
	$page['page_panels'][$panel_id]['title'] = 'Edit Job Posting';
	$page['page_panels'][$panel_id]['show_title'] = true;
	// $required = [];
	
	//Display form
	$form = '<form method="post" action="" class="" id="job-posting" enctype="multipart/form-data">
	
		<h4>Job Details</h4>
		<div class="form-grid">

			<div class="form-field">
				<label>Title <strong class="color-red">*</strong></label>
				<input type="text" name="title" class="input' .(in_array('title', $required) ? ' required' : ''). '" value="' .(isset($_POST['title']) ? $_POST['title'] : (isset($career['title']) ? $career['title'] : '')). '" />

				<label>Category <strong class="color-red">*</strong></label>
				<select class="select' .(in_array('category_id', $required) ? ' required' : ''). '" name="category_id" id="category_id">
					<option value="">- Please Choose -</option>'; 
					foreach ($categories as $category) {
						$form .= '<option '.(isset($_POST['category_id']) ? ($_POST['category_id'] == $category['category_id'] ? 'selected' : '') : (isset($career['category_id']) && $career['category_id'] == $category['category_id'] ? 'selected' : '')).' value="'.$category['category_id'].'">'.$category['category_name'].'</option>';
					}
				$form .= '</select>

				<label>Posted Date <strong class="color-red">*</strong></label>
				<input type="text" name="posted_date" class="input datepicker' .(in_array('posted_date', $required) ? ' required' : ''). '" value="' .(isset($_POST['posted_date']) ? $_POST['posted_date'] : (isset($career['posted_date']) ? date('F j, Y', strtotime($career['posted_date'])) : '')). '" readonly />

				<label>Closing Date <strong class="color-red">*</strong></label>
				<input type="text" name="closing_date" class="input datepicker' .(in_array('closing_date', $required) ? ' required' : ''). '" value="' .(isset($_POST['closing_date']) ? $_POST['closing_date'] : (isset($career['closing_date']) ? date('F j, Y', strtotime($career['closing_date'])) : '')). '" readonly />
			</div>

			<div class="form-field ">
				<label>Facility <strong class="color-red">*</strong></label>
				<select class="select' .(in_array('facility_id', $required) ? ' required' : ''). '" name="facility_id" id="facility_id">
					<option value="">- Please Choose -</option>'; 
					foreach ($facilities as $facility) {
						$form .= '<option '.(isset($_POST['facility_id']) ? ($_POST['facility_id'] == $facility['facility_id'] ? 'selected' : '') : (isset($career['facility_id']) && $career['facility_id'] == $facility['facility_id'] ? 'selected' : '')).' value="'.$facility['facility_id'].'">'.$facility['facility_name'].'</option>';
					}
				$form .= '</select>

				<label>Monthly Salary Range <strong class="color-red">*</strong></label>
				<input type="text" name="salary" class="input' .(in_array('salary', $required) ? ' required' : ''). '" value="' .(isset($_POST['salary']) ? $_POST['salary'] : (isset($career['salary']) ? $career['salary'] : '')). '" />

				<label>Duration <strong class="color-red">*</strong></label>
				<input type="text" name="duration" class="input' .(in_array('duration', $required) ? ' required' : ''). '" value="' .(isset($_POST['duration']) ? $_POST['duration'] : (isset($career['duration']) ? $career['duration'] : '')). '" />

				<label>Attach File <small>(PDF Document, 2MB maximum)</small></label>
				<div class="input-file-container">  
					<input class="input-file" id="file" name="file" type="file" value="" />
					<label tabindex="0" for="file" class="input-file-trigger' .(in_array('file', $required) ? ' required' : ''). '"><i class="fa fa-upload"></i>Select a file...</label>
				</div>';

				if(isset($file) && $file != '' && file_exists($filedir.$file)){
					$form .= '<input type="checkbox" class="checkbox" name="deletefile" id="deletefile" value="1">
					<label for="deletefile"><small>Delete Current File</small></label>';
					$form .= '<small><a href="' .$path.$filedir.$file. '" target="_blank"><i class="fa fa-file-pdf-o"></i> View Current File</a></small>';
					$form .= '<input type="hidden" name="old_file" value="' .$file. '" />';
				}else{
					$form .= '<input type="hidden" name="old_file" value="" />';
				}

			$form .= '</div>

			<div class="form-field">
				<label>Who Can Apply? <strong class="color-red">*</strong></label>
				<select class="select' .(in_array('public', $required) ? ' required' : ''). '" name="public" id="public">
					<option value="0"'.(isset($_POST['post']) ? (isset($_POST['public']) && $_POST['public'] == 0 ? ' selected' : '') : (isset($career['public']) && $career['public'] == 0 ? ' selected' : '')).'>Registered Members Only</option>
					<option value="1"'.(isset($_POST['post']) ? (isset($_POST['public']) && $_POST['public'] == 1 ? ' selected' : '') : (isset($career['public']) && $career['public'] == 1 ? ' selected' : '')).'>Anyone</option>
				</select>

				<input type="checkbox" class="checkbox" id="showhide" value="1" name="showhide" '.(isset($_POST['post']) ? (isset($_POST['showhide']) ? 'checked' : '') : (isset($career['showhide']) && $career['showhide'] == '1' ? 'checked' : '')).'>
				<label for="showhide"><small>Hide Job Posting</small></label>
			</div>
		</div>

		<h4>Member Classification</h4>
		<div class="form-grid member_classification">';
			foreach($classes as $class){
				$form .= '<input type="checkbox" name="class_id[]" id="class'.$class['class_id'].'" class="checkbox" value="'.$class['class_id'].'"'.(in_array($class['class_id'], ($_POST['class_id'] ?? $career['class_id'])) ? ' checked' : '').' />
				<label for="class'.$class['class_id'].'">'.$class['class_name'].'</label>';
			}
		$form .= '</div>

		<h4>Contact Information</h4>
		<div class="form-grid">
			<div class="form-field">
				<label>First Name <strong class="color-red">*</strong></label>
				<input type="text" name="first_name" class="input' .(in_array('first_name', $required) ? ' required' : ''). '" value="'. (isset($_POST['first_name']) ? $_POST['first_name'] : (isset($career['first_name']) ? $career['first_name'] : '') ). '"/>
			</div>
			<div class="form-field">
				<label>Last Name <strong class="color-red">*</strong></label>
				<input type="text" name="last_name" class="input' .(in_array('last_name', $required) ? ' required' : ''). '" value="' .(isset($_POST['last_name']) ? $_POST['last_name'] : (isset($career['last_name']) ? $career['last_name'] : '') ). '"/>
			</div>
			<div class="form-field">
				<label>Email <strong class="color-red">*</strong></label>
				<input type="email" name="email" class="input' .(in_array('email', $required) ? ' required' : ''). '" value="' .(isset($_POST['email']) ? $_POST['email'] : (isset($career['email']) ? $career['email']: '') ). '"/>
			</div>
			<div class="form-field">
				<label>Phone Number <strong class="color-red">*</strong></label>
				<input type="text" name="phone" class="input phone' .(in_array('phone', $required) ? ' required' : ''). '" value="' .(isset($_POST['phone']) ? $_POST['phone'] : (isset($career['phone']) ? formatPhoneNumber($career['phone']) : '') ). '"/>
			</div>
		</div>';


		$form .= '<h4>Job Description <strong class="color-red">*</strong></h4>
		<fieldset  class="' .(in_array('texteditor', $required) ? 'required' : ''). '" class="clearfix">
			<textarea name="texteditor" id="content" class="textarea texteditor' .(in_array('texteditor', $required) ? ' required' : ''). '">' .(isset($_POST['texteditor']) ? $_POST['texteditor'] : (isset($career['content']) ? $career['content'] : '') ). '</textarea>
		</div>';

		$form .= '<div class="form-buttons">
			<button type="submit" name="post" value="post" class="button primary black back-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Save Changes</button>
			<button type="button" id="delete-button" class="button primary red back-button confirm delete-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Delete</button>
			<a href="' .$_sitepages['job-postings']['page_url']. '" class="button primary black back-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Cancel</a>
		</div>

		<input type="hidden" name="xid" value="'.$_COOKIE['xid'].'" />
	</form>';

	

	$html .= $form;
			//Set panel content
// $page['page_panels'][$panel_id]['content'] .= $form;
//Job postings	
}else{
		
	// $html .= $page['page_panels'][$panel_id]['content'];
	
	//Add button
	$html .= '<a href="'.$_sitepages['post-job']['page_url'].'" class="button f_right solid inline action-btn">Post a Job</a>';

	//Search form

	$html .= '<form name="search-form" id="directory-search-bar" action="" method="get" class="clearfix directory-search-form">
		<div class="search-input-container">
			<input type="text" name="search" class="input directory-search-input" value="' . htmlspecialchars((isset($_GET['search']) ? $_GET['search'] : ''), ENT_QUOTES, 'UTF-8') . '" placeholder="Search" />

			' . (isset($_GET['search']) && trim($_GET['search']) != '' ? '
				<a href="' . htmlspecialchars($_sitepages['my-job-postings']['page_url'], ENT_QUOTES, 'UTF-8') . '" title="Clear Search" class="clear-search-btn">×</a>
			' : '') . '

			<button type="submit" class="button search-icon-btn" title="Search">
				<i class="fa fa-search"></i>
			</button>
		</div>

		<button type="submit" class="button primary visually-hidden">Search</button>
	</form>';


	$html .= '<table id="job-postings-table" cellpadding="10" cellspacing="0" border="0" width="100%" class="directory-table">';
	if(!empty($careers)){
		$html .= '<tr>
			<th class="left">Title</th>
			<th width="140px" class="left">Closing</th>
			<th width="140px" class="left">Availability</th>
			<th width="100px" class="left">Status</th>
			<th width="60px" class="left">&nbsp;</th>
		</tr>';

		foreach($careers as $career){
			$html .= '<tr>
				<td>'
					.($career['showhide'] ? '' : '<a href="'.$_sitepages['job-postings']['page_url'] .$career['page']. '-' .$career['career_id']. '/'.'">')
					.$career['title']
					.($career['showhide'] ? ' <small>(Hidden)</small>' : '</a>').'
				</td>
				<td>'.date('M j, Y', strtotime($career['closing_date'])).'</td>
				<td>'.($career['public'] ? 'Public' : 'Members Only').'</td>
				<td>'.($career['approved'] ? (date('Ymd', strtotime($career['closing_date'])) >= date('Ymd') ? 'Open' : 'Closed') : 'Pending Approval').'</td>
				<td align="center" ><a href="' .$_sitepages['my-job-postings']['page_url'].'?action=edit&id='.$career['career_id'].'" class="button"><i class="fa fa-edit"></i></a></td>
			</tr>';
		}

	} else {
		$html .= '<tr><td class="nobg" colspan="5">No job postings found' .(isset($_GET['search']) && trim($_GET['search']) != '' ? ' matching <strong>`'.$_GET['search'].'`</strong>' : ''). '.</td></tr>';

	}
	
	//Pager
	if($totalresults > 0){
		$searchterm = (isset($_GET['search']) ? $_GET['search'] : '');
		$html .= '<tr>
			<td class="pager" colspan="5">
				<small>';
					$html .= 'Displaying '.($pg == 'all' ? '1 - '.$totalresults : (1+($limit*($pg-1))).' - '.(count($careers)+($limit*($pg-1)))).' (of '.$totalresults.' Total)<br />';
					if($totalresults > $limit && $pg != 'all'){
						$tagend = round($totalresults % $limit, 0);
						$splits = round(($totalresults - $tagend)/$limit, 0);
						$num_pages = ($tagend == 0 ? $splits : $splits+1);	
						$pos = $pg;
						$startpos = ($pos*$limit)-$limit;

						$html .= ($pos > 1 ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos-1).'">&lsaquo; Prev</a> ' : '');
						for($i=1; $i<=$num_pages; $i++){
							$html .= ($i != $pos ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.$i.'">'.$i.'</a> ' : '<span><u>'.$i.'</u></span> ');
						}
						$html .= ($pos < $num_pages ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos+1).'">Next &rsaquo;</a> ' : '');
					}
				$html .= '</small>
			</td>
		</tr>';
	}
	
	$html .= '</table>';

}
// $page['page_panels'][$panel_id]['content'] .= $html;

	
?>