<?php  

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");
require_once("../../../core/classes/ImageUpload.class.php");

//Define vars
$response = [
	'errors'  => false, 
	'content' => ''
];

//Dynamic vars
$item_id   = $_POST['item_id'] ?? NULL;
$record_db = $_POST['record_db'] ?? 'galleries_photos';
$record_id = $_POST['record_id'] ?? 'photo_id';
$imagedir  = '../../../'.($_POST['imagedir'] ?? 'images/galleries/');
$crop_type = $_POST['crop_type'] ?? 'gallery-photo';

if(USER_LOGGED_IN){
	if($_POST['xssid'] == $_COOKIE['xssid']){
		if(!empty($item_id)){

			//Get image
			$db->query("SELECT * FROM `$record_db` WHERE `$record_id` = ?", [$item_id]);
			if(!$db->error() && $db->num_rows() > 0){
				$photo = $db->fetch_array()[0];

				//Delete from DB
				$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", [$item_id]);
				if(!$db->error()){
					$response['content'] = $CMSBuilder->mini_alert("<p>Photo was successfully deleted.</p>", true);

					$CMSUploader = new CMSUploader($crop_type, $imagedir);
					$CMSUploader->bulk_delete($photo);

				}else{
					$response['errors'][] = 'Unable to delete photo.';
				}

			}else{
				$response['errors'][] = 'Unable to retrieve photo.';
			}

		}else{
			$response['errors'][] = 'No item to delete.';
		}

	}else{
		$response['errors'][] = 'Invalid session.';
	}

}else{
	$response['errors'][] = 'You must be logged in to proceed.';
}

if($response['errors']){
	$response['content'] = $CMSBuilder->mini_alert("<p>".implode("<br/>", $response['errors'])."</p>", false);
}

print json_encode($response);

?>