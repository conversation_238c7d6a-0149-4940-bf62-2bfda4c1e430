<?php
$html ='';
//Application Form
// $html = (isset($alert) ? $alert : '');
if (!empty($success)) {
		$html .= '<div class="alert alert-success" style="background-color: #dff0d8; border: 1px solid #d6e9c6; color: #3c763d; padding: 15px; margin-bottom: 20px;">' . $success . '</div>';
	}
	if (!empty($alert)) {
		$html .= '<div class="alert alert-danger" style="background-color: #f2dede; border: 1px solid #ebccd1; color: #a94442; padding: 15px; margin-bottom: 20px;">' . $alert . '</div>';
	}

$html .= '<form id="application-form" name="application-form" class="clearfix'.(!USER_LOGGED_IN ? ' hidden-recaptcha' : '').'" action="" method="post" enctype="multipart/form-data" data-recaptcha="#recaptcha-apply">
	
	<h4>'.$career['title'].'</h4>
	<table cellpadding="0" cellspacing="0" border="0" width="100%" class="noborder nomargin">
		<tr>
			<td class="nobg" width="150px">Facility:</td>
			<td class="nobg">'.$career['facility_name'].'</td>
		</tr>
		<tr>
			<td class="nobg">Location:</td>
			<td class="nobg">'.$career['city'].', '.$career['province'].'</td>
		</tr>';
		if(!empty($career['category_name'])){
			$html .= '<tr>
				<td class="nobg">Category:</td>
				<td class="nobg">'.$career['category_name'].'</td>
			</tr>';
		}
	$html .= '</table>
	<p><small>Wrong Job? <a href="'.$_sitepages['job-postings']['page_url'].'">Go Back</a>.</small></p>

	<p><small>Required Fields</small> <strong class="color-red">*</strong></p>

	<div class="form-grid">';

	// First Name
	$html .= '<div class="form-field">
		<label>First Name <span class="req">*</span></label>
		<input type="text" name="first_name" class="input jsvalidate'.(in_array('first_name', $required) ? ' required' : '').'" value="'.($row['first_name'] ?? '').'" />
	</div>';

	// Last Name
	$html .= '<div class="form-field">
		<label>Last Name <span class="req">*</span></label>
		<input type="text" name="last_name" class="input jsvalidate'.(in_array('last_name', $required) ? ' required' : '').'" value="'.($row['last_name'] ?? '').'" />
	</div>';

	// Email
	$html .= '<div class="form-field">
		<label>Email <span class="req">*</span></label>
		<input type="email" name="email" class="input jsvalidate'.(in_array('email', $required) ? ' required' : '').'" value="'.($row['email'] ?? '').'" />
	</div>';

	// Phone
	$html .= '<div class="form-field">
		<label>Phone Number <span class="req">*</span></label>
		<input type="text" name="phone" class="input jsvalidate'.(in_array('phone', $required) ? ' required' : '').'" value="'.($row['phone'] ?? '').'" />
	</div>';

	// Resume Upload
	$html .= '<div class="form-field">
		<label>Attach Resume <small>(PDF Document, 2MB maximum)</small></label>
		<div class="input-file-container">
			<input class="input-file" id="file" name="resume" type="file" />
			<label tabindex="0" for="file" class="input-file-trigger'.(in_array('resume', $required) ? ' required' : '').'"><i class="fa fa-upload"></i>Select a file...</label>
		</div>
	</div>';

	$html .= '</div>'; // Close form-grid

	// Recaptcha
	$html .= '<div class="hidden">
		<div id="recaptcha-modal" class="hidden-modal" title="Verify You&rsquo;re Not a Robot">
			<div class="recaptcha-wrapper">
				<div id="recaptcha-apply" class="g-recaptcha" data-sitekey="'.$global['recaptcha_key'].'"></div>
			</div>
		</div>
	</div>';

	// Submit button
	$html .= '<div class="form-field button-wrapper">
		<button type="'.(!USER_LOGGED_IN ? 'button' : 'submit').'" name="button" class="button primary red">
			Send Application
			<span class="top-border"></span>
			<span class="bottom-border"></span>
			<span class="left-border"></span>
			<span class="right-border"></span>
		</button>
	</div>

	<input type="hidden" name="apply" value="true" />
	<input type="hidden" name="g-recaptcha-response" value="" />
	<input type="hidden" name="xid" value="'.$_COOKIE['xid'].'" />
</form>';

//Set panel content
$page['page_panels'][55]['content'] .= ($page['page_panels'][55]['content'] != '' ? '<hr />' : '').$html;

//Panels
include("includes/pagepanels.php");

?>
