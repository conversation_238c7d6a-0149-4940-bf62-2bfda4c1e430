<?php

// Define labels and panel header per section
if(SECTION_ID == $_cmssections['inquiries']){
	$header = 'Inquiries';
	$labels = $db->get_enum_vals($record_db, 'inquiry');
}

if(SECTION_ID == $_cmssections['form_submissions']){
	$header = 'Form Submissions';
	$labels = ['Submissions'];
}

// Fetch all dataset labels
$datasets = array_fill_keys($labels, []);
$dates    = [];

// Get last 30 days, fill each dataset with dates
$period = new DatePeriod(new DateTime('-29 days'), new DateInterval('P1D'), new DateTime('+1 day'));
foreach ($period as $i => $date) $dates[] = $date->format('m/d');
foreach ($datasets as $label => $stats) $datasets[$label] = array_fill_keys($dates, 0);

// Fetch inquiries
if(SECTION_ID == $_cmssections['inquiries']) {
	$params = [date("Y-m-d", strtotime("-29 days")), date("Y-m-d")];
	$db->query("SELECT * FROM inquiries WHERE DATE(timestamp) BETWEEN ? AND ?", $params);
	$result = $db->fetch_array();
	foreach ($result as $stat) {
		$date = date('m/d', strtotime($stat['timestamp']));
		$datasets[$stat['inquiry']][$date]++;
	}
}

// Fetch form submissions
if(SECTION_ID == $_cmssections['form_submissions']) {
	$params = array(date("Y-m-d",strtotime("-29 days")),date("Y-m-d"));
	$query = $db->query("SELECT * FROM form_submissions WHERE DATE(timestamp) BETWEEN ? AND ?", $params);
	$result = $db->fetch_array();
	foreach($result as $stat){
		$datasets['Submissions'][date("m/d",strtotime($stat['timestamp']))] += 1;
	}
}

?>
<div class="panel">
	<div class="panel-header"><?php echo $header; ?> in Last 30 Days
		<a class="panel-toggle fas fa-chevron-up"></a>
	</div>
	<div class="panel-content chart">
		<table class="chart-stats" cellspacing="0" cellpadding="15" border="0">
			<tbody>
				<tr>

				<?php
				// Display label plurals
				foreach ($datasets as $label => $dataset) {
					$label = substr($label, -1) == 's' ? $label : (substr($label, -1) == 'y' ? substr($label, 0, -1).'ies' : $label.'s');
					echo '<th class="center"><div>Total<br/> '.$label.' <b>'.array_sum($dataset).'</b></div></th>';
				}
				?>

				</tr>
			</tbody>
		</table>

		<div class="chart-container">
			<canvas id="dashboard-chart" height="200" data-source='<?php echo json_encode($datasets); ?>'></canvas>
		</div>
	</div>
</div>

<script src="<?php echo $path; ?>includes/plugins/chartjs/dist/chart.min.js"></script>