$(function () {
	let $galleries = $('.panel.partners:not(.gallery-listings) .light-gallery');

	observeOnce($galleries, () => loadScript(['swiper', 'lightgallery'], () => {
		$galleries.each(function () {
			let $this = $(this);

			$this.addClass('swiper');
			$this.children().wrap(`<div class="swiper-slide"/>`);
			$this.children().wrapAll(`<div class="swiper-wrapper"/>`);
			$this.append(`<div class="swiper-scrollbar"/>`);

			const swiper = new Swiper(this, {
				spaceBetween: 1,
				slidesPerView: 'auto',
				watchSlidesProgress: true, // fix right click moving slideshow
				centerInsufficientSlides: true,
				freeMode: true,

				scrollbar: {
					el: ".swiper-scrollbar",
					draggable: true,
					hide: false,
					dragSize: 150,
				},

				mousewheel: {
					forceToAxis: true
				},

				breakpoints: {
					769: {
						scrollbar: {
							dragSize: 'auto',
						}
					}
				}
			});

			$this.data({swiper});
		});
	}));
});

// Gallery images
$(function(){
	observeOnce('.partner .gallery-listings .light-gallery', () => {
		loadScript(['savvior', 'lightgallery'], () => {
			window.addEventListener('savvior:redraw', () => {
				$('.light-gallery').lightGallery({selector: '.gal-item'});
			});

			// Init plugin
			savvior.init('.gallery-listings .light-gallery', {
				"screen and (max-width: 480px)":  { columns: 2 },
				"screen and (min-width: 481px) and (max-width: 769px)":  { columns: 3 },
				"screen and (min-width: 769px) and (max-width: 1024px)": { columns: 6 },
				"screen and (min-width: 1025px)": { columns: 8 }
			});
		});
	});
});

//Job search
$(function(){

	$(document).on('click', '.dropdown-checkbox-wrapper > .side', function() {
		$(this).siblings('.checklist').stop().slideToggle(300);
		$arrow = ($(this).children('i').length) ? $(this).children('i') : $(this).next().children('i')
		if($arrow.attr('style')){
			$arrow.removeAttr('style');
		}else{
			$arrow.css('transform', 'scaleY(-1)');
			$arrow.css('top', '0');
		}
	});

	$('.dropdown-checkbox-wrapper input[type="checkbox"]').on('change', function() {
		var $this = $(this),
			$dropdown = $this.closest('.dropdown-checkbox-wrapper'),
			$checked = $dropdown.find(':checked'),
			$title = $dropdown.find('.title'),
			count = $checked.length,
			additions = []; 
		
		if(count){
			$checked.each(function () {
				additions.push($this.next().text()); 

			});

			keyword = $title.data('default').split(' ').pop()
			switch(keyword.substr(-1)){
				case 'y':
					keyword = keyword.substring(0, keyword.length - 1) + 'ies';
				break;
				case 's':
				break;
				default:
					keyword = keyword + 's';
				break;
			}				

			$title.text('Selected ' + keyword + ': ' + additions.length);

		}else{
			$title.text($title.data('default')); 
		}

	});
	$('.dropdown-checkbox-wrapper input[type="checkbox"]').trigger('change');
});
//Delete confirm
$(function(){
	$('button.delete').bind('click', function(){
		var formel = this.form;
		$('<div id="dialog-box"></div>').appendTo('body')
		.html('Are you sure you want to permanently delete this item?')
		.dialog({
			modal: true, 
			title: 'Confirm',
			autoOpen: true,
			width: 300,
			resizable: false,
			closeOnEscape: true,
			closeText: "<i class='fa fa-close'></i>",
			buttons: {
				"Confirm": function() {
					$(this).dialog("close");
					$(formel).submit();
				},
				Cancel: function() {
					$(this).dialog("close");
				}
			 },
			show:{effect:"drop", direction:"up", duration:200},
			hide:{effect:"drop", direction:"up", duration:200}
		});	
	});
	$('.delete-button.confirm').on('click', function () {
		var _this = this;

		$('<div id="dialog-box"></div>').appendTo('body')
		.html('Are you sure you want to permanently delete this item?')
		.dialog({
			modal: true,
			title: 'Confirm',
			autoOpen: true,
			width: 300,
			resizable: false,
			closeOnEscape: true,
			closeText: "",
			buttons: {
				"Confirm": function() {
					$(this).dialog("close");
					$('<input type="hidden" name="delete" value="delete">').appendTo($(_this).closest('form'));
					$(_this).closest('form').submit();
				},
				Cancel: function() {
					$(this).dialog("close");
				}
			},
			show:{effect:"drop", direction:"up", duration:200},
			hide:{effect:"drop", direction:"up", duration:200}
		});
	});
});

//Standard form submission with hidden recaptcha
function recaptchaForm(form_id){
	var this_form = $("#" + form_id);
	var validated = true;
	var errormsg = '';
	
	//Check for draft
	if(this_form.find('input[name="form_action"]').val() != 'draft'){
	
		//Validate fields
		this_form.find('.jsvalidate').each(function(index, el) {
			if($(el).find('input.radio, input.checkbox').length !== 0){
				var ischecked = false;
				$(el).find('input.radio, input.checkbox').each(function(){
					if($(this).is(':checked')){
						ischecked = true;
					}
				});
				if(!ischecked){
					validated = false;
					$(el).addClass('required');
					errormsg = 'Please fill out all the required fields.<br />';
				}
			}else{
				if(!$(el).hasClass('texteditor') && $.trim($(el).val()) == '') {
					validated = false;
					$(el).addClass('required');
					errormsg = 'Please fill out all the required fields.<br />';
				}
			}
		});
		this_form.find('input[type="email"]').each(function(index, el) {
			if($.trim($(el).val()) != '' && !checkmail($(el).val())) {
				validated = false;
				$(el).addClass('required');
				errormsg += 'Please enter a valid email address.<br />';
			}
		});
	}

	if(validated) {
		$('#recaptcha-modal').dialog('open');
	}else{
		dialogAlert('Error!', errormsg, 'error');
	}
}


//Forms
$(function() {

	//Contact form
	$("#contact-form").on('submit', function() {
		submitForm('contact-form', 'js/ajax/contactform.php', function() {});
		return false;
	});

	//payment form
	$("#payment-form .button").on("click", function () {
		console.log('payment form submitted 2');
		submitFormPayment2("payment-form", "", function () {
			$("#payment-form").submit();
		});
		return false;
	});
	//
	
	//Dynamic javascript forms
	$('form.dynamic-form .button').on('click', function() {
		var form_id = $(this).parents('form').attr('id');
		var button_value = $(this).val();
		$('#'+form_id).find('input[name="form_action"]').val(button_value);
	});
	$('form.dynamic-form').on('submit', function() {
		var form_id = $(this).attr('id');
		submitForm(form_id, 'js/ajax/submitform.php', function() {});
		return false;
	});
	
	//Hidden recaptcha forms
	$('form.hidden-recaptcha .button').on('click', function() {
		var form_id = $(this).parents('form').attr('id');
		
		//Set form submission type
		var button_value = $(this).val();
		$('#'+form_id).find('input[name="form_action"]').val(button_value);
		
		recaptchaForm(form_id);
		return false;
	});
});

$(function() {
  $('#recaptcha-modal').dialog({
    autoOpen: false,
    modal: true,
    width: 400
  });
});
 function showTab(index) {
    const tabs = document.querySelectorAll('.tab-content');
    const buttons = document.querySelectorAll('.tab-buttons div');
    tabs.forEach((tab, i) => {
      tab.classList.toggle('active', i === index);
      buttons[i].classList.toggle('active', i === index);
    });
  }

  //Content Tabs
$(function(){
	$('.content-tabs').each(function(){
		if(!$(this).hasClass('static-tabs')){
		  $(this).tabs({
				show: {effect: "fadeIn", duration: 300},
				hide: {effect: "fadeOut", duration: 300},
			    activate: function () {
					//Trigger responsive items
					responsiveItems();
				}
			}).each(function(){
				var el = $(this);
				var limit = $(el).data('tabs-limit');
				if($(el).find('.ui-tabs-anchor').length > limit){
					var opts = '';
					$(el).find('.ui-tabs-anchor').each(function(){
						opts += '<option value="'+$(this).data('index')+'">'+$(this).text()+'</option>';
					});
					$(el).addClass('responsive-tabs').prepend('<select name="content-tabs-select" class="select ui-tabs-select">'+opts+'</select>').find('.ui-tabs-select').bind('change', function(){
						$(el).tabs('option', 'active', $(this).val());
					});
				}
				$(this).find('.decorative').css('left', ($(this).find('.ui-tabs-nav').width()+20));
			}); 
		}else{
			$(this).find('.decorative').css('left', ($(this).find('.ui-tabs-nav').width()+20));
		}
	});	
});
$(window).resize(function(){
	$('.content-tabs').each(function(){
		$(this).find('.decorative').css('left', ($(this).find('.ui-tabs-nav').width()+20));
	});
});

$(function() {
  $('.slider').each(function() {
    const $slider = $(this);
    const min = parseInt($slider.data('min')) || 0;
    const max = parseInt($slider.data('max')) || 100;
    const value = parseInt($slider.data('value')) || 0;
    const $input = $slider.closest('.slider-container').find('input[type="hidden"]');

    $slider.slider({
      min: min,
      max: max,
      value: value,
      create: function(event, ui) {
        // Show initial value in the handle
        $(this).find('.ui-slider-handle').text(value);
      },
      slide: function(event, ui) {
        // Update hidden input and handle text
        $input.val(ui.value);
        $(this).find('.ui-slider-handle').text(ui.value);
      }
    });
  });
});