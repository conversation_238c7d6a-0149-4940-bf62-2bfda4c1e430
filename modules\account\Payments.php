<?php



//Make a payment
if(PAGE_ID == $_sitepages['payments']['page_id']){

		//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
		exit();
	}

	//Define vars
	$panel_id = 71;
	$required = array();
	$errors = array();
	$record = array();
	$confirm = false;
	$admin_fee = $reg_settings['admin_fee'];
	$admin_fee_type = $reg_settings['admin_fee_type'];
	$payment_options = $Registration->get_payment_options();

	//Set record type
	define('REG_ID', (substr(ITEM_ID, 0, 1) == 'r' ? substr(ITEM_ID, 1) : NULL));
	define('INVOICE_ID', (substr(ITEM_ID, 0, 1) == 'i' ? substr(ITEM_ID, 1) : NULL));

	//Registration
	if(!is_null(REG_ID)){

		//Get registration details
		$record = $Registration->get_registration(REG_ID);
		if(empty($record) || $record['account_id'] != USER_LOGGED_IN){ //Only registrant can pay
			header('Location: ' .$sitemap[60]['page_url'].'?action=edit&id=null');
			exit();
		}

		//Determine balance owing
		if($record['paid'] == '1'){
			$record['payment_amount'] = 0;
		}else{
			$record['payment_amount'] = $record['registration_total']-$registration['total_paid']-$registration['total_refunded'];
		}

		//Check for specific service fee
		foreach($record['events'] as $event_id=>$event){
			$get_event = $Registration->get_occurrence($event['occurrence_id']);
			if(!is_null($get_event['admin_fee'])){
				$admin_fee = $get_event['admin_fee'];
				$admin_fee_type = $get_event['admin_fee_type'];
			}
		}

	//Invoice
	}else if(!is_null(INVOICE_ID)){

		//Get invoice details
		$query = $db->query("SELECT `invoices`.*, `hio`.`hio_id`, `hio`.`approved` FROM `invoices` LEFT JOIN `hio` ON `invoices`.`invoice_id` = `hio`.`invoice_id` WHERE `invoices`.`status` = ? && `invoices`.`account_id` = ? && `invoices`.`invoice_id` = ?", array('Active', USER_LOGGED_IN, INVOICE_ID));
		if(!$query || $db->error() || !$db->num_rows()){
			header('Location: ' .$sitemap[76]['page_url'].'?action=edit&id=null');
			exit();
		}else{
			$result = $db->fetch_array();
			$record = $result[0];

			//Determine balance owing
			$paid = 0;
			$record['payment_amount'] = 0;
			$query = $db->query("SELECT SUM(`amount`) AS `total` FROM `payments` WHERE `status` = ? && `invoice_id` = ? ", array('1', INVOICE_ID));
			if($query && !$db->error() && $db->num_rows() > 0){
				$result = $db->fetch_array();
				$paid = $result[0]['total'];
			}
			$record['payment_amount'] = $record['invoice_total']-$paid;

			//Check for hio service fee
			if(!empty($record['hio_id']) && $record['payment_amount'] > $reg_settings['max_payment_amount']){

				$db->query("SELECT * FROM `hio_settings` WHERE `id`=1");
				if($db->num_rows()){
					$hiofees = $db->fetch_array()[0];
					if(!is_null($hiofees['admin_fee'])){
						$admin_fee = $hiofees['admin_fee'];
						$admin_fee_type = $hiofees['admin_fee_type'];
					}
				}

			}

		}

	}

	//Calculate service fee percentage
	if($admin_fee_type == 'Percent'){
		$admin_fee = number_format($record['payment_amount']*($admin_fee/100), 2, '.', '');
	}

	//If balance owing, and less than allowed maximum online payment amount OR paying a service fee
	if(!empty($record) && $record['payment_amount'] > 0 && ($record['payment_amount'] <= $reg_settings['max_payment_amount'] || empty($reg_settings['max_payment_amount']) || $admin_fee > 0)){

		//Fetch billing profiles
		$billing_profiles = $Account->get_account_billing_profiles(USER_LOGGED_IN);
		if(isset($record['billing_id']) && array_key_exists($record['billing_id'], $billing_profiles) && !isset($_SESSION['payment']['billing_id'])){
			$_SESSION['payment']['billing_id'] = $record['billing_id'];
		}

		//Fetch terms of use
		$termsofuse = NULL;
		if($_sitepages['terms']['showhide'] < 2){
			$termspanels = $SiteBuilder->get_page_panels($_sitepages['terms']['page_id']);
			if(isset($termspanels[80])){
				$termsofuse = (trim($termspanels[80]['title']) != '' && $termspanels[80]['show_title'] ? strtoupper($termspanels[80]['title']).':<br />' : '').strip_tags($termspanels[80]['content']);
			}
		}

		//Validate payment
		if(isset($_POST['continue'])){

			//Validation
			$required_fields = array('first_name', 'last_name', 'email', 'phone');
			if(!isset($_POST['billing_id']) || empty($_POST['billing_id'])){
				$required_fields = array_merge($required_fields, array('address1', 'city', 'province', 'postal_code', 'country', 'ccname', 'ccnumber', 'exp_month', 'exp_year', 'cvv'));
			}else{
				$required_fields[] = 'billing_id';
			}
			if(!empty($termsofuse)){
				$required_fields[] = 'terms';
			}
			foreach($required_fields as $field){
				if(!isset($_POST[$field]) || $_POST[$field] == ''){
					$errors[0] = 'Please fill out all the required fields.';
					$required[] = $field;
				}
				if($field == 'email' && !checkmail($_POST[$field])) {
					$errors[] = 'Please enter a valid email address.';
					$required[] = $field;
				}
				if($field == 'phone'){
					if(!detectPhone($_POST[$field])){
						$errors[] = 'Please enter a valid phone number.';
						$required[] = $field;
					}else{
						$_POST['phone'] = formatPhoneNumber($_POST['phone']);
					}
				}
				if($field == 'ccnumber' && $_POST[$field] != ''){
					$valid_card = false;
					$accepted_cards = array();
					foreach($payment_options as $payopt){
						$accepted_cards[] = $payopt['name'];
						if(get_card_type($_POST[$field]) == $payopt['type']){
							$valid_card = true;
						}
					}
					if(!$valid_card){
						$errors[] = 'Invalid card type. We currently only accept '.implode(' &amp; ', $accepted_cards).'.';
						$required[] = $field;
						$alert = $Account->alert(implode('<br />', $errors) , false);
					}
				}
			}

			//Save session vars
			$_SESSION['payment']['first_name'] = $_POST['first_name'];
			$_SESSION['payment']['last_name'] = $_POST['last_name'];
			$_SESSION['payment']['email'] = $_POST['email'];
			$_SESSION['payment']['phone'] = $_POST['phone'];
			$_SESSION['payment']['terms'] = ($_POST['terms'] ? true : false);

			//Set billing
			if(!isset($_POST['billing_id']) || empty($_POST['billing_id'])){
				$_SESSION['payment']['billing_id'] = '';
				$_SESSION['payment']['ref_number'] = '';
				$_SESSION['payment']['bill_address1'] = $_POST['address1'];
				$_SESSION['payment']['bill_address2'] = $_POST['address2'];
				$_SESSION['payment']['bill_city'] = $_POST['city'];
				$_SESSION['payment']['bill_province'] = $_POST['province'];
				$_SESSION['payment']['bill_postalcode'] = $_POST['postal_code'];
				$_SESSION['payment']['bill_country'] = $_POST['country'];
				$_SESSION['payment']['ccname'] = $_POST['ccname'];
				$_SESSION['payment']['cctype'] = get_card_type($_POST['ccnumber']);
				$_SESSION['payment']['ccnumber'] = NULL;
				$_SESSION['payment']['exp_month'] = $_POST['exp_month'];
				$_SESSION['payment']['exp_year'] = $_POST['exp_year'];
				$_SESSION['payment']['ccsave'] = (isset($_POST['ccsave']) ? 1 : 0);

			//Use profile
			}else{
				$profile = $billing_profiles[$_POST['billing_id']];

				$_SESSION['payment']['billing_id'] = $_POST['billing_id'];
				$_SESSION['payment']['ref_number'] = $profile['ref_number'];
				$_SESSION['payment']['bill_address1'] = $profile['bill_address1'];
				$_SESSION['payment']['bill_address2'] = $profile['bill_address2'];
				$_SESSION['payment']['bill_city'] = $profile['bill_city'];
				$_SESSION['payment']['bill_province'] = $profile['bill_province'];
				$_SESSION['payment']['bill_postalcode'] = $profile['bill_postalcode'];
				$_SESSION['payment']['bill_country'] = $profile['bill_country'];
				$_SESSION['payment']['ccname'] = $profile['ccname'];
				$_SESSION['payment']['cctype'] = $profile['cctype'];
				$_SESSION['payment']['ccnumber'] = $profile['ccnumber']; //Last 4 digits only
				$_SESSION['payment']['exp_month'] = substr($profile['ccexpiry'], 0, 2);
				$_SESSION['payment']['exp_year'] = substr($profile['ccexpiry'], -2, 2);
				$_SESSION['payment']['ccsave'] = 0;
			}

			//// for debugging only
			// if(!empty($_POST)){
			// 	echo "post not empty";
			// 	echo "<br>";
			// 	echo "reg id : ".REG_ID;
			// 	echo "<br>";
			// 	echo "invoice id : ".INVOICE_ID;

			// 	echo "<pre>";
			// 	print_r($_POST);
			// 	print_r($_SESSION['payment']);
			// 	echo "</pre>";
			// 	echo "<p style='margin-bottom:100px;'></p>";
			// 	exit;
			// }

			////

			//Send to confirm page
			if(empty($errors)){
				$confirm = true;
			}

			if($errors){
				$alert = $Account->alert(implode('<br />', $errors), false);
			}

		}

		//Retrive sensitive info
		$ccnumber = (isset($_POST['ccnumber']) ? $_POST['ccnumber'] : '');
		$cvv = (isset($_POST['cvv']) ? $_POST['cvv'] : '');
		if(!empty($_SESSION['payment']['billing_id']) && !empty($_SESSION['payment']['ccnumber'])){
			$ccnumber = $_SESSION['payment']['ccnumber']; //Last 4 digits only
		}

		//Process
		if(isset($_POST['process'])){
			$success = false;

			//Double check there is a balance
			if($record['payment_amount'] > 0){
				$ordertotal = number_format($record['payment_amount']+$admin_fee, 2, '.', '');

				//Insert payment data
				$params = array(
					(!is_null(REG_ID) ? REG_ID : INVOICE_ID),
					$_SESSION['payment']['bill_address1'],
					$_SESSION['payment']['bill_address2'],
					$_SESSION['payment']['bill_city'],
					$_SESSION['payment']['bill_province'],
					$_SESSION['payment']['bill_postalcode'],
					$_SESSION['payment']['bill_country'],
					$_SESSION['payment']['ccname'],
					$_SESSION['payment']['cctype'],
					substr($ccnumber, -4, 4),
					$_SESSION['payment']['exp_month'].$_SESSION['payment']['exp_year'],
					$admin_fee,
					$record['payment_amount'],
					USER_LOGGED_IN,
					date("Y-m-d H:i:s")
				);
				$query = $query = $db->query("INSERT INTO `payments`(" .(!is_null(REG_ID) ? "`registration_id`" : "`invoice_id`"). ", `bill_address1`, `bill_address2`, `bill_city`, `bill_province`, `bill_postalcode`, `bill_country`, `ccname`, `cctype`, `ccnumber`, `ccexpiry`, `admin_fee`, `amount`, `processed_by`, `payment_date`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
				if($query && !$db->error()){
					$order_id = $db->insert_id();
					$ordernum = 'A'.date("ymd").'-'.str_pad($order_id, 5, '0', STR_PAD_LEFT);

					//Format request using billing profile
					if(!empty($_SESSION['payment']['billing_id'])){
						$request = array(
							'type' => 'newOrderRequest',
							'ordernum' => $ordernum,
							'ordertotal' => $ordertotal,
							'taxes' => 0,
							'ref_number' => $_SESSION['payment']['ref_number'],
							'ccsave' => 0
						);

					//Format request with new billing data
					}else{
						$request = array(
							'type' => 'newOrderRequest',
							'name' => $_SESSION['payment']['first_name'].' '.$_SESSION['payment']['last_name'],
							'email' => $_SESSION['payment']['email'],
							'phone' => $_SESSION['payment']['phone'],
							'bill_address1' => $_SESSION['payment']['bill_address1'],
							'bill_address2' => $_SESSION['payment']['bill_address2'],
							'bill_city' => $_SESSION['payment']['bill_city'],
							'bill_province' => $_SESSION['payment']['bill_province'],
							'bill_postalcode' => $_SESSION['payment']['bill_postalcode'],
							'bill_country' => $_SESSION['payment']['bill_country'],
							'ccnumber' => $ccnumber,
							'exp_month' => $_SESSION['payment']['exp_month'],
							'exp_year' => $_SESSION['payment']['exp_year'],
							'cvv' => $cvv,
							'ordernum' => $ordernum,
							'ordertotal' => $ordertotal,
							'taxes' => 0,
							'ccsave' => $_SESSION['payment']['ccsave']
						);
					}

					//Send request
					include("includes/orbital/request.php");

					//Success response
					if($trxnResponse['status'] == 1 && $trxnResponse['approved'] == 1){
						$success = true;
						$status = '1';
						$paid = '1';
					}else{
						$success = false;
						$status = '0';
						$paid = '0';
						$errors[] = $trxnResponse['message'];
					}

					//Update payment response
					$response = array(
						$status,
						$trxnResponse['response_code'],
						$trxnResponse['txn_num'],
						$trxnResponse['txn_tag'],
						$trxnResponse['auth_code'],
						$trxnResponse['cvd_code'],
						$trxnResponse['message'],
						$ordernum,
						$order_id
					);
					$query = $db->query("UPDATE `payments` SET `status`=?, `response_code`=?, `txn_num`=?, `txn_tag`=?, `auth_code`=?, `cvd_code`=?, `message`=?, `payment_number`=? WHERE `payment_id`=?", $response);
					if(!$query || $db->error()){
						trigger_error('Error updating payment response: '.$db->error());
						$alert = $Account->alert('Error updating payment response: '.$db->error() , false);
					}

					//Success
					if($success){

						//Update record as paid
						if(!is_null(REG_ID)){
							$query = $db->query("UPDATE `reg_registrations` SET `paid`=? WHERE `registration_id`=?", array(1, REG_ID));
							if(!$query || $db->error()){
								trigger_error('Error updating registration payment status: '.$db->error());
								$alert = $Account->alert('Error updating registration payment status: '.$db->error() , false);
							}
						}else{
							$query = $db->query("UPDATE `invoices` SET `paid`=? WHERE `invoice_id`=?", array(1, INVOICE_ID));
							if(!$query || $db->error()){
								trigger_error('Error updating invoice payment status: '.$db->error());
								$alert = $Account->alert('Error updating invoice payment status: '.$db->error() , false);
							}

							//Approve Hole-in-one ****2022-06-27 ALL HIO ARE AUTO APPROVED ON CREATION****
							/*if(!empty($record['hio_id']) && !$record['approved']){

								//Set to approved
								$query = $db->query("UPDATE `hio` SET `approved` = 1, `date_approved` = ? WHERE `hio_id` = ?", array(date('Y-m-d H:i:s'), $record['hio_id']));
								if(!$query || $db->error()){
									trigger_error('Error approving hole-in-one: '.$db->error());
								}
							}*/
						}

						//Send receipt
						$order_data = $_SESSION['payment'];
						$order_data['record_name'] = (!is_null(REG_ID) ? 'Registration No.' : 'Invoice No.');
						$order_data['record_number'] = (!is_null(REG_ID) ? $record['registration_number'] : $record['invoice_number']);
						$order_data['payment_date'] = date('Y-m-d H:i:s');
						$order_data['payment_number'] = $ordernum;
						$order_data['payment_type'] = 'Credit Card';
						$order_data['ccnumber'] = substr($ccnumber, -4, 4);
						$order_data['ccexpiry'] = $_SESSION['payment']['exp_month'].$_SESSION['payment']['exp_year'];
						$order_data['admin_fee'] = $admin_fee;
						$order_data['ordertotal'] = $ordertotal;
						$order_data = array_merge($order_data, $trxnResponse);
						$payment_receipt = $Registration->payment_receipt($order_data);
						$send_email = send_email($_SESSION['payment']['email'], 'Payment Confirmation', $payment_receipt);

						//Also send to admin
						$record_type = substr($order_data['record_number'], -2, 2);
						$admin_email = $global['contact_email'];
						if($record_type == '01'){
							$admin_email = (trim($reg_settings['email_events']) != '' ? $reg_settings['email_events'] : $admin_email);
						}else if($record_type == '02'){
							$admin_email = (trim($reg_settings['email_tournaments']) != '' ? $reg_settings['email_tournaments'] : $admin_email);
						}else if($record_type == '03'){
							$admin_email = (trim($reg_settings['email_hio']) != '' ? $reg_settings['email_hio'] : $admin_email);
						}else if($record_type == '04'){
							$admin_email = (trim($global['email_orders']) != '' ? $global['email_orders'] : $admin_email);
						}
						send_email($admin_email, 'Payment Confirmation', $payment_receipt);

						$record['payment_amount'] = 0;
						$alert = $Account->alert('Your payment has been received' .($send_email ? ' and email confirmation has been sent' : ''). '.', true);

						//Save billing profile to database if necessary
						if($_SESSION['payment']['ccsave'] && !empty($ccnumber)){
							$params = array(
								USER_LOGGED_IN,
								$_SESSION['payment']['bill_address1'],
								$_SESSION['payment']['bill_address2'],
								$_SESSION['payment']['bill_city'],
								$_SESSION['payment']['bill_province'],
								$_SESSION['payment']['bill_postalcode'],
								$_SESSION['payment']['bill_country'],
								$_SESSION['payment']['ccname'],
								$_SESSION['payment']['cctype'],
								substr($ccnumber, -4, 4),
								$_SESSION['payment']['exp_month'].$_SESSION['payment']['exp_year'],
								date("Y-m-d H:i:s"),
								date("Y-m-d H:i:s")
							);

							//Profile was already created with order processing
							if(isset($trxnResponse['ref_number']) && !empty($trxnResponse['ref_number'])){
								$params[] = $trxnResponse['ref_number'];

							//Profile has not been created so send the request now
							}else{

								//Format request
								$request = array(
									'type' => 'profileAddRequest',
									'name' => $_SESSION['payment']['ccname'],
									'email' => $_SESSION['payment']['email'],
									'phone' => $_SESSION['payment']['phone'],
									'bill_address1' => $_SESSION['payment']['bill_address1'],
									'bill_address2' => $_SESSION['payment']['bill_address2'],
									'bill_city' => $_SESSION['payment']['bill_city'],
									'bill_province' => $_SESSION['payment']['bill_province'],
									'bill_country' => $_SESSION['payment']['bill_country'],
									'bill_postalcode' => $_SESSION['payment']['bill_postalcode'],
									'ccnumber' => $ccnumber,
									'exp_month' => $_SESSION['payment']['exp_month'],
									'exp_year' => $_SESSION['payment']['exp_year'],
									'ref_number' => ''
								);

								//Process request
								include("includes/orbital/request.php");
								if($trxnResponse['status'] == 1){
									$params[] = $trxnResponse['ref_number'];
								}
							}

							//Save to database
							if(!empty($trxnResponse['ref_number'])){
								$query = $db->query("INSERT INTO `account_billing_profiles`(`account_id`, `bill_address1`, `bill_address2`, `bill_city`, `bill_province`, `bill_postalcode`, `bill_country`, `ccname`, `cctype`, `ccnumber`, `ccexpiry`, `date_added`, `last_updated`, `ref_number`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
								if(!$query || $db->error()){
									trigger_error('Error inserting billing profile: '.$db->error());
								}
							}
						}

						//Clear data
						unset($ccnumber);
						unset($cvv);
						unset($_SESSION['payment']);

					}

				//Insert error
				}else{
					$errors[] = 'Unable to insert payment: '.$db->error();
					$alert = $Account->alert(implode('<br />', $errors), false);
				}

			//No balance
			}else{
				$errors[] = (!is_null(REG_ID) ? 'Registration' : 'Invoice').' has already been paid in full.';
				$alert = $Account->alert(implode('<br />', $errors) , false);
			}

		}
	}

	//No record found
	if(empty($record)){
		$errors[] = 'No matching registrations or invoices found.';
		$alert = $Account->alert(implode('<br />', $errors) , false);
	}

}

?>