<?php

//Upcoming events
$filter = (PARENT_ID == $_sitepages['facilities']['page_id'] && isset($facility['facility_name']) ? $facility['facility_name'] : '');
$excluded = ((PARENT_ID == $_sitepages['events']['page_id'] || PARENT_ID == $_sitepages['tournaments']['page_id']) && isset($event['occurrence_id']) ? $event['occurrence_id'] : '');
$noevents = true;
$limit = 4;
$count = 0;
$start_date = date("Y-m-d");
$end_date = NULL;

$featured_events = $Registration->get_occurrences(array('Open'), NULL, NULL, $start_date, $end_date, $filter, ($limit+1));
if(!empty($featured_events)){
	echo '<div class="event-boxes">';
	foreach($featured_events as $event){
		if($event['occurrence_id'] != $excluded){
			$noevents = false;
			$count++;
			$event_url = ($event['event_type'] == 1 ? $_sitepages['events']['page_url'] : $_sitepages['tournaments']['page_url']).$event['page'].'-'.$event['occurrence_id'].'/';
			$event['sponsors'] = $Registration->get_occurrence_sponsors($event['occurrence_id']);

			echo '<div class="event-box">
				<div class="date">
					<span class="day">' .date("d", strtotime($event['start_date'])). '</span>
					<small class="month">' .date("F", strtotime($event['start_date'])). '</small>
				</div>
				<div class="content">
					<small>' .$event['category_name']. '</small>
					<h6><a href="' .$event_url. '">' .$event['event_name']. '</a></h6>';
					if($event['location_name'] != ''){
						echo '<small>Host Venue: '.$event['location_name']. '</small>';
					}else if($event['facility_name'] != ''){
						echo '<small>Host Venue: '.$event['facility_name']. '</small>';
					}
					if(!empty($event['sponsors'])){
						echo '<small>Sponsored By: '.$event['sponsors'][0]['name'].'</small>';
					}
				echo '</div>
			</div>';
		}
		if($count == $limit){
			break;
		}
	}
	echo '</div>';
}
if($noevents){
	echo '<p class="center">Currently no upcoming events to display.</p>';
}

?>