<?php


// echo "<pre>";
// print_r($_POST);    
// print_r($_FILES);
// echo "</pre>";


// Add debugging at the beginning of the file
error_log("Delete profile image AJAX endpoint called");

//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

error_reporting(0);
ini_set('display_errors', 'off');

// Include the profile image handler
require_once("../../modules/account/ProfileImageHandler.php");

// Define response array
$response = [
    'errors' => false,
    'content' => '',
    'message' => ''
];

// Log the request
error_log("POST data: " . print_r($_POST, true));
error_log("Session status: " . (isset($_SESSION) ? "Session exists" : "No session"));
error_log("User logged in: " . (isset($Account) && $Account->login_status() ? "Yes" : "No"));

// Check if user is logged in
if (!isset($Account) || !$Account->login_status()) {
    $response['errors'] = true;
    $response['message'] = 'Access denied. Please log in.';
    error_log("Access denied - user not logged in");
    echo json_encode($response);
    exit;
}

// Validate request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check XSSID if it's being used
    // $xssid_valid = true;
    // if (isset($_POST['xssid']) && isset($_COOKIE['xssid'])) {
    //     $xssid_valid = $_POST['xssid'] == $_COOKIE['xssid'];
    //     error_log("XSSID validation: " . ($xssid_valid ? "Valid" : "Invalid"));
    // }
    
    // if ($xssid_valid) {
        // Get the filename from the request
        $filename = $_POST['filename'] ?? '';
        $account_id = $Account->account_id ?? 0;
        
        error_log("Processing delete for filename: " . $filename . ", account ID: " . $account_id);
        
        if (empty($filename)) {
            $response['errors'] = true;
            $response['message'] = 'No filename provided.';
            error_log("Error: No filename provided");
        } else {
            // Delete the physical files
            $delete_result = delete_profile_image($filename);
            error_log("File deletion result: " . ($delete_result ? "Success" : "Failed"));

            $_FILES = [];

            // Only proceed if file seems gone OR if you want to try DB update regardless
    if ($delete_result) { // <-- Optional: Only update DB if file deletion reported success
            
            // Update the database to remove the reference
            try {
                // Create parameter array for update
                $params = [
                    [
                        'param' => 'photo',
                        'value' => NULL,
                        'label' => 'Profile Photo',
                        'required' => false,
                        'unique' => false,
                        'validate' => false,
                        'hash' => false
                    ]
                ];
                
                // Update the profile
                $update_result = $Account->update_profile($params, $account_id);
                error_log("Database update result: " . ($update_result ? "Success" : "Failed"));
                
                if ($update_result) {
                    $response['content'] = '<p>Profile photo has been deleted successfully.</p>';
                    $response['message'] = 'Profile photo deleted successfully.';
                } else {
                    throw new Exception("Failed to update database.");
                }
            } catch (Exception $e) {
                $response['errors'] = true;
                $response['message'] = 'Database update failed: ' . $e->getMessage();
                error_log("Database update exception: " . $e->getMessage());
            }
         } else {
        $response['errors'] = true;
        $response['message'] = 'Failed to delete image file from server.';
        error_log("Delete AJAX Error: File deletion function failed.");
    }    
        }
    // } else {
    //     $response['errors'] = true;
    //     $response['message'] = 'Invalid security token.';
    //     error_log("Error: Invalid security token");
    // }
} else {
    $response['errors'] = true;
    $response['message'] = 'Invalid request method.';
    error_log("Error: Invalid request method " . $_SERVER['REQUEST_METHOD']);
}

// Return JSON response
error_log("Sending response: " . json_encode($response));
echo json_encode($response);
// exit;
?>
