<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']) {
	$total_records = $db->get_record_count('hio');
	$CMSBuilder->set_widget(83, 'Total Hole In One Events', $total_records, 'map-pin');
}

if(SECTION_ID == $_cmssections['hole-in-one']){
	
	//Define vars
	$record_db = 'hio';
	$record_id = 'hio_id';
	$record_name = 'Hole in one';
	
	$errors = false;
	$required = array();

	//Get records
	$records_arr = array();
	$params = array();
	$where = "";

	if(ITEM_ID != '' && ACTION == 'edit'){
		$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`$record_id` = ?";
		$params[] = ITEM_ID;

	}else{
		
		if($searchterm != ""){
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."(CONCAT(`account_profiles`.`first_name`, ? ,`account_profiles`.`last_name`) LIKE ? || `facilities`.`facility_name` LIKE ? || `$record_db`.`event_name` LIKE ?)";
			$params[] = ' ';
			$params[] = '%' .$searchterm. '%';
			$params[] = '%' .$searchterm. '%';
			$params[] = '%' .$searchterm. '%';
		}
		if(!isset($_SESSION['search_start_date'][SECTION_ID]) || isset($_POST['clear-search'])){
			$_SESSION['search_start_date'][SECTION_ID] = date('Y-m-d');
		}
		if(!isset($_SESSION['search_end_date'][SECTION_ID]) || isset($_POST['clear-search'])){
			$_SESSION['search_end_date'][SECTION_ID] = date('Y-m-d');
		}
		if(isset($_GET['start_date'])){
			$_SESSION['search_start_date'][SECTION_ID] = $_GET['start_date'];
		}
		if(isset($_GET['end_date'])){
			$_SESSION['search_end_date'][SECTION_ID] = $_GET['end_date'];
		}
		if(isset($_SESSION['search_start_date'][SECTION_ID]) && $_SESSION['search_start_date'][SECTION_ID] != '') {
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`date_added` >= ?";
			$params[] = date('Y-m-d 00:00:00', strtotime($_SESSION['search_start_date'][SECTION_ID]));
		}
		if(isset($_SESSION['search_end_date'][SECTION_ID]) && $_SESSION['search_end_date'][SECTION_ID] != '') {
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`date_added` <= ?";
			$params[] = date('Y-m-d 23:59:59', strtotime($_SESSION['search_end_date'][SECTION_ID]));
		}
	}

	//Get data
	$query = $db->query("SELECT `$record_db`.*, `accounts`.`email`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `accounts`.`email`, `account_profiles`.`phone`, `facilities`.`facility_name`, `facilities`.`email` AS `facility_email`, `facilities`.`phone` AS `facility_phone`, `invoices`.`invoice_number`, ".
	"(SELECT `event_date` FROM `hio_dates` WHERE `hio_dates`.`$record_id` = `$record_db`.`$record_id` ORDER BY `hio_dates`.`event_date` ASC LIMIT 1) AS `start_date`, ".
	"(SELECT `event_date` FROM `hio_dates` WHERE `hio_dates`.`$record_id` = `$record_db`.`$record_id` ORDER BY `hio_dates`.`event_date` DESC LIMIT 1) AS `end_date`".
	"FROM `$record_db` ".
	"LEFT JOIN `accounts` ON `accounts`.`account_id` = `$record_db`.`account_id` ".
	"LEFT JOIN `account_profiles` ON `account_profiles`.`account_id` = `$record_db`.`account_id` ".
	"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `$record_db`.`facility_id` ".
	"LEFT JOIN `invoices` ON `invoices`.`invoice_id` = `$record_db`.`invoice_id`".
	$where." ORDER BY `$record_db`.`date_added` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			
			//Get dates
			$records_arr[ITEM_ID]['event_dates'] = array();
			$query = $db->query("SELECT * FROM `hio_dates` WHERE `hio_id` = ? ORDER BY `event_date` ASC", array(ITEM_ID));
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $date){
					$records_arr[ITEM_ID]['event_dates'][] = date('F j, Y', strtotime($date['event_date']));
				}
			}

			//Get courses
			$records_arr[ITEM_ID]['courses'] = array();
			$query = $db->query("SELECT * FROM `hio_courses` WHERE `hio_id` = ?", array(ITEM_ID));
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $course){

					//Get holes
					$course['holes'] = array();
					$query = $db->query("SELECT * FROM `hio_holes` WHERE `course_id` = ?", array($course['course_id']));
					if($query && !$db->error()){
						$course['holes'] = $db->fetch_array();
					}

					$records_arr[ITEM_ID]['courses'][] = $course;
				}
			}			
			
			$row = $records_arr[ITEM_ID];
		}
	}
	
	//Editing only
	if(ACTION != '' && ACTION != 'edit'){
		header('Location:' .PAGE_URL);
		exit();
	}

	//Delete item
	if(isset($_POST['delete'])){

		$db->new_transaction();
		
		//Delete event
		$delete = $db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		
		//Trash invoice
		$delete = $db->query("UPDATE `invoices` SET `status` = ? WHERE `invoice_id` = ?", array('Trashed', $records_arr[ITEM_ID]['invoice_id']));
		
		if(!$db->error()){
			$db->commit();
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(), false);	
		}

		header("Location: " .PAGE_URL);
		exit();

	//Save item
	}else if(isset($_POST['save'])){
		$prize_total = 0;
		$premium_total = 0;
		
		//Dates
		$event_dates = (isset($_POST['event_dates']) ? $_POST['event_dates'] : array());
		$row['event_dates'] = array();
		foreach($event_dates as $date){
			if(trim($date) != ""){
				$row['event_dates'][] = $date;
			}
		}
		$dates = count($row['event_dates']);
		
		//Validation
		if(trim($_POST['event_name']) == ""){
			$errors[] = 'Event name is required.';
			$required[] = 'event_name';
		}
		if(empty($_POST['field'])){
			$errors[] = 'Event field is required.';
			$required[] = 'field';
		}
		if($dates == 0){
			$errors[] = 'At least one event date is required.';
		}
		
		if(!$errors){

			$db->new_transaction();

			//Update holes
			$delete_holes = (isset($_POST['delete_holes']) ? $_POST['delete_holes'] : array());
			foreach($_POST['prize'] as $key=>$data){
				if(!in_array($key, $delete_holes)){

					$hole = $_POST['hole'][$key];
					$yards_men = $_POST['yards_men'][$key];
					$yards_women = $_POST['yards_women'][$key];
					$prize = $_POST['prize'][$key];
					$premium = $_POST['premium'][$key];

					$prize_total += $prize;
					$premium_total += ($premium*$dates);

					//Validation
					if(empty($hole)){
						$errors[0] = 'Hole number is required.';
						$required[] = 'hole'.$key;
					}
					if(empty($yards_men) && empty($yards_women)){
						$errors[1] = 'Yardage for at least one gender is required.';
						if(empty($yards_men)){
							$required[] = 'yards_men'.$key;
						}
						if(empty($yards_women)){
							$required[] = 'yards_women'.$key;
						}
					}
					if(!is_numeric($prize) || $prize <= 0){
						$errors[2] = 'Invalid prize amount.';
						$required[] = 'prize'.$key;
					}

					//Update hole
					$params = array(
						$hole, 
						(!empty($yards_men) ? $yards_men : NULL), 
						(!empty($yards_women) ? $yards_women : NULL), 
						$prize,
						$premium,
						$key
					);
					$query = $db->query("UPDATE `hio_holes` SET `hole` = ?, `yards_men` = ?, `yards_women` = ?, `prize` = ?, `premium` = ? WHERE `hole_id` = ?", $params);				
				}
			}

			//Validate totals
			if($prize_total <= 0){
				$errors[] = 'Total prize amount cannot be $0.00';
			}
			if($premium_total <= 0){
				$errors[] = 'Total premium cannot be $0.00';
			}

			if(!$errors){

				//Delete holes
				foreach($delete_holes as $hole_id){
					$query = $db->query("DELETE FROM `hio_holes` WHERE `hole_id` = ?", array($hole_id));
				}

				//Delete courses without holes
				foreach($records_arr[ITEM_ID]['courses'] as $course){
					$delete_course = true;
					foreach($course['holes'] as $hole){
						if(!in_array($hole['hole_id'], $delete_holes)){
							$delete_course = false;
						}
					}
					if($delete_course){
						$query = $db->query("DELETE FROM `hio_courses` WHERE `course_id` = ?", array($course['course_id']));
					}
				}

				//Update event dates
				$query = $db->query("DELETE FROM `hio_dates` WHERE `$record_id` = ?", array(ITEM_ID));
				foreach($row['event_dates'] as $date){
					$query = $db->query("INSERT INTO `hio_dates`(`hio_id`, `event_date`) VALUES(?,?)", array(ITEM_ID, $date));
				}

				//Generate invoice if being approved for the first time, or if already approved and the total has changed
				$invoice_id = $records_arr[ITEM_ID]['invoice_id'];
				if((!$records_arr[ITEM_ID]['approved'] && isset($_POST['approved'])) || ($records_arr[ITEM_ID]['approved'] && $premium_total != $records_arr[ITEM_ID]['premium_total'])){
					$params = array(
						$invoice_id,
						$records_arr[ITEM_ID]['facility_name'].' ('.$records_arr[ITEM_ID]['first_name'].' '.$records_arr[ITEM_ID]['last_name'].')',
						$records_arr[ITEM_ID]['first_name'],
						$records_arr[ITEM_ID]['last_name'],
						(!empty($records_arr[ITEM_ID]['facility_email']) ? $records_arr[ITEM_ID]['facility_email'] : $records_arr[ITEM_ID]['email']),
						(!empty($records_arr[ITEM_ID]['facility_phone']) ? $records_arr[ITEM_ID]['facility_phone'] : $records_arr[ITEM_ID]['phone']),
						number_format($premium_total, 2, '.', ''),
						date('Y-m-d H:i:s'),
						NULL,
						$records_arr[ITEM_ID]['account_id'],
						date('Y-m-d H:i:s'),
						USER_LOGGED_IN,
						number_format($premium_total, 2, '.', ''),
						date('Y-m-d H:i:s'),
						date('Y-m-d H:i:s'),
						USER_LOGGED_IN
					);
					$query = $db->query("INSERT INTO `invoices`(`invoice_id`, `bill_to`, `first_name`, `last_name`, `email`, `phone`, `invoice_total`, `invoice_date`, `due_date`, `account_id`, `last_updated`, `updated_by`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `invoice_total`=?, `invoice_date`=?, `last_updated`=?, `updated_by`=?", $params);

					//Update invoice number
					if(empty($invoice_id)){
						$invoice_id = $db->insert_id();
					}
					$invoice_number = 'PGA'.str_pad($invoice_id, 5, '0', STR_PAD_LEFT).'-03';
					$query = $db->query("UPDATE `invoices` SET `invoice_number` = ? WHERE `invoice_id` = ?", array($invoice_number, $invoice_id));

				}

				//Already approved
				if($records_arr[ITEM_ID]['approved']){
					$_POST['approved'] = true;
				}

				//Update db
				$params = array(
					$_POST['event_name'],
					$_POST['field'],
					$prize_total,
					$premium_total,
					$_POST['comments'],
					$invoice_id,
					(isset($_POST['approved']) ? 1 : 0),
					(!$records_arr[ITEM_ID]['approved'] && isset($_POST['approved']) ? USER_LOGGED_IN : $records_arr[ITEM_ID]['approved_by']),
					(!$records_arr[ITEM_ID]['approved'] && isset($_POST['approved']) ? date('Y-m-d H:i:s') : $records_arr[ITEM_ID]['date_approved']),
					date('Y-m-d H:i:s'),
					ITEM_ID
				);
				$query = $db->query("UPDATE `$record_db` SET `event_name`=?, `field`=?, `prize_total`=?, `premium_total`=?, `comments`=?, `invoice_id`=?, `approved`=?, `approved_by`=?, `date_approved`=?, `last_updated`=? WHERE `$record_id` = ?", $params);
				if(!$db->error()){
					$db->commit();
					
					//Display button
					$button = '';
					if(!empty($invoice_id)){
						$button = "<br /><br /><a href='" .$sitemap[80]['page_url']."?action=edit&item_id=".$row['invoice_id']. "' class='button-sm'><i class='fa fa-file-text-o'></i>View Invoice</a>";
					}

					//Send email notification
					if(isset($invoice_number)){
						$send_email = false;

						$subject = 'Invoice No. '.$invoice_number;
						$message = '<h3>Invoice Notification</h3><p>This email is to inform you of a new invoice on your account.</p>
						<p><strong>Hole In One Event: ' .$records_arr[ITEM_ID]['event_name']. '</strong><br />
						<strong>Invoice No. ' .$invoice_number. '</strong><br />
						<strong>Amount Due: $' .number_format($premium_total, 2). '</strong><br />
						' .(!empty($_POST['due_date']) ? '<strong>Due Date: ' .date('F j, Y', strtotime($_POST['due_date'])). '</strong><br />' : ''). '
						</p>
						' .(!empty($_POST['comments']) ? '<p>'.nl2br($_POST['comments']).'</p>' : ''). '
						<p>To manage your invoices or to make a payment, please <a href="' .$siteurl.$root.get_page_url(22). '">login</a> to your account or phone our office at ' .$global['contact_phone'].'.</p>
						<p>If you have any questions or concerns regarding this message, please contact us.</p>';
						$send_email = send_email($records_arr[ITEM_ID]['email'], $subject, $message);

						if($send_email){
							$CMSBuilder->set_system_alert($record_name.' was successfully saved and email notification was sent.'.$button, true);
						}else{
							$CMSBuilder->set_system_alert($record_name.' was successfully saved but email notification failed to send.'.$button, false);
						}

					//Save only
					}else{
						$CMSBuilder->set_system_alert($record_name.' was successfully saved.'.$button, true);
					}

					header("Location: " .PAGE_URL);
					exit();

				}else{
					$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
				}

			}
			
		}
		
		//Errors	
		if($errors){
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}
		
	}

}

?>