<?php

//System files
include('../../includes/config.php');
include('../../../config/database.php');
include('../../includes/functions.php');
include('../../includes/utils.php');


$response = array();

	// echo "<pre>";
	// print_r($_POST);
	// echo "</pre>";

if(isset($_POST) && USER_LOGGED_IN){

	$searchterm = $_POST['searchterm'];
	$params = array('Pending', 'Trashed', ' ', '%'.$searchterm.'%');
	
	$query = $db->query("SELECT `account_profiles`.*, `accounts`.`email` ".
	"FROM `account_profiles` LEFT JOIN `accounts` ON `account_profiles`.`account_id` = `accounts`.`account_id` ".
	"WHERE `accounts`.`status` != ? && `accounts`.`status` != ? && CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ? ".
	"GROUP BY `account_profiles`.`account_id` ORDER BY `account_profiles`.`account_id`", $params);
	if($query && !$db->error() && $db->num_rows() > 0) {
		$accounts = $db->fetch_array();
		foreach($accounts as $account) {
			$autofill_item = array();
			$autofill_item['account_id'] = $account['account_id'];
			$autofill_item['first_name'] = str_replace("&rsquo;", "'", $account['first_name']);
			$autofill_item['last_name'] = str_replace("&rsquo;", "'", $account['last_name']);
			$autofill_item['email'] = str_replace("&rsquo;", "'", $account['email']);
			$autofill_item['phone'] = str_replace("&rsquo;", "'", $account['phone']);
			$autofill_item['company'] = str_replace("&rsquo;", "'", $account['company']);
			$autofill_item['address1'] = str_replace("&rsquo;", "'", $account['address1']);
			$autofill_item['address2'] = str_replace("&rsquo;", "'", $account['address2']);
			$autofill_item['city'] = str_replace("&rsquo;", "'", $account['city']);
			$autofill_item['province'] = str_replace("&rsquo;", "'", $account['province']);
			$autofill_item['postal_code'] = str_replace("&rsquo;", "'", $account['postal_code']);
			$autofill_item['country'] = str_replace("&rsquo;", "'", $account['country']);
			
			$autofill_item['label'] = str_replace("&rsquo;", "'", $account['first_name']." ".$account['last_name']." (No. ".$account['account_id'].")");
			$response[] = $autofill_item;
		}
	}
}

print json_encode($response);
	
?>