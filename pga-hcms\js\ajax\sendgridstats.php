<?php

//System files
include("../../includes/config.php");
include("../../../includes/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

if(isset($_POST) && USER_LOGGED_IN){
	
	$json = array();
	
	//stats for current month
	$start = date('Y-m-01');
	$end = date("Y-m-d"); //now
	$curMonthStats = $sendgrid->get_stats($start, $end);	
	
	$total = 0;
	foreach($curMonthStats AS $data){
		$total += $data['stats'][0]['metrics']['requests'];
	}
	$json['current_month'] = $total;
	
	//stats for previous month
	$start = date('Y-m-d', strtotime('first day of last month'));
	$end = date('Y-m-d', strtotime('last day of last month'));
	$lastMonthStats = $sendgrid->get_stats($start, $end);	
	
	$total = 0;
	foreach($lastMonthStats AS $data){
		$total += $data['stats'][0]['metrics']['requests'];	
	}
	$json['previous_month'] = $total;
	
	//stats for today
	$start = date('Y-m-d');
	$end = date("Y-m-d");
	$todayStats = $sendgrid->get_stats($start, $end, "day");
	
	$total = 0;
	foreach($todayStats AS $data){
		$total += $data['stats'][0]['metrics']['requests'];	
	}
	$json['today'] = $total;
	
	echo json_encode($json);
	
} else {
	echo "Access Denied.";	
}
?>