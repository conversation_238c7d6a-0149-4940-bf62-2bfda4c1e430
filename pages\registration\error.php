<?php

//Processing error
if(isset($_SESSION['reg']['checkout']['error']) && $_SESSION['reg']['checkout']['error'] != ''){
	$html .= $Account->alert('Processing error: '.$_SESSION['reg']['checkout']['error'].'. Please <a href="' .$_sitepages['reg_checkout']['page_url']. '">go back</a> and try again.', false);
	unset($_SESSION['reg']['checkout']['error']);
	
//Unknown error
}else{
	$html .= $Account->alert('Unable to process registration. Please <a href="' .$_sitepages['reg_checkout']['page_url']. '">go back</a> and try again.', false);
}

//Set panel content
$page['page_panels'][65]['content'] .= $html;

?>