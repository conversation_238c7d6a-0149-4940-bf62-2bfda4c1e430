<?php if(SLIDESHOW && !LANDING){ ?>
<section id="slideshow" class="animate">
	<div class="slideshow-wrapper swiper-wrapper">
	<?php

	$slidecount = 0;
	foreach($slideshow as $slide){
		$slidecount++;
		$video_mp4   = check_file('videos/'.$slide['video_mp4']);
		$video_webm  = check_file('videos/'.$slide['video_webm']);
		$video_ogg   = check_file('videos/'.$slide['video_ogg']);
		$slide_image = check_file($slide['image'], 'images/slideshow/480/');

		echo '<div class="slide swiper-slide'.($video_mp4 ? ' video' : '').($slidecount == 1 ? ' ' : ''). ' theme-'.$slide['theme'].'">
			<div class="slide-wrapper">
				<div class="slide-media">';

					//Slide image
					if($slide_image){
						echo '<div class="slide-image">
							<div class="responsive-bg'.($slidecount == 1 ? ' visible' : '').'" style="'.responsive_bg('images/slideshow/', $slide_image).'"></div>
						</div>';
					}

					//Slide video
					if($video_mp4){
						echo '<div class="slide-video">
							<video class="video-bg" id="video-'.$slide['slide_id'].'" preload="none" playsinline="" loop muted>'
								.($video_webm ? '<source type="video/webm" src="'.$path.$video_webm.'">' : '')
								.($video_mp4 ? '<source type="video/mp4" src="'.$path.$video_mp4.'">' : '')
								.($video_ogg ? '<source type="video/ogg" src="'.$path.$video_ogg.'">' : '')
							.'</video>
						</div>';
					}

					echo '<div class="overlay overlay-'.$slide['theme'].'"></div>
				</div>';

				//Slide content
				echo '<div class="slide-content">
					<div class="container">
						<div class="slide-header">
							<div class="logoimg"><img src="images/svg/logo.svg"></div>
							<div class="slide-title">
								<h2>'.fancy_text($slide['title']).'</h2>
							</div>'.

							($slide['content'] ? '<div class="slide-text">'.fancy_text(nl2br($slide['content'])).'</div>' : '');

							// if($slide['url'] || $slide['url2']){
							// 	echo '<div class="slide-buttons">'.
							// 		($slide['url'] ? create_button($slide['url'], $slide['url_target'], $slide['url_text']) : '').' '.
							// 		($slide['url2'] ? create_button($slide['url2'], $slide['url_target2'], $slide['url_text2']) : '').
							// 	'</div>';
							// }

							if($slide['url'] || $slide['url2']){
								echo '<div class="slide-buttons">'.
									($slide['url'] ? '<div class="page-buttons"><a class="button secondary" href="'.$slide['url'].'" target="'.$slide['url_target'].'"><span class="left-border"></span><span class="right-border"></span>'.$slide['url_text'].'</a></div>' : '').' '.
									($slide['url2'] ? '<div class="page-buttons"><a class="button secondary light" href="'.$slide['url2'].'" target="'.$slide['url_target2'].'"><span class="left-border"></span><span class="right-border"></span>'.$slide['url_text2'].'</a></div>' : '').
								'</div>';
							}

						echo '</div>
					</div>
				</div>
			</div>
		</div>';
	}

	?>
	</div>

	<?php
	if($slidecount > 1){
		echo '<div class="slideshow-navigation">
			<div class="slideshow-button-prev"></div>
			<div class="slideshow-button-next"></div>
		</div>';

		echo '<div class="slideshow-pagination"></div>';
	}
	?>
</section>
<?php } ?>