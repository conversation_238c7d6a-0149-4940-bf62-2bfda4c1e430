<?php  

//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

error_reporting(0);
ini_set('display_errors', 'off');

//Get leadin form
$leadin_id = $_POST['leadin_id'] ?? NULL;
$page_id = $_POST['page_id'] ?? NULL;
$leadin = $SiteBuilder->get_attached_leadin($leadin_id);

$to = $leadin['form_recipient'] ?: $global['email_leadinform'] ?: $global['contact_email'];
$subject = 'Attention Box Form Submission';
$emailbody = '';

$response  = [
	'to'             => $to,
	'errors'         => false,
	'error_fields'   => [],
	'msg_validation' => ''
];

//Cookie validation
if($_POST['xid'] != $_COOKIE['xid']) {
	$response['errors'][] = 'Please make sure cookies are enabled on your browser then try again.';
}

//Recaptcha
if (validate_recaptcha()) {
	$response['errors'][] = 'Please verify you are not a robot.';
}

if(!empty($leadin_id) && !empty($leadin)){
	$emailbody = '<h3>' .$leadin['title']. '</h3>';

	//Validate fields
	foreach($leadin['form_fields'] as $field){
		$field_name = 'field-'.$leadin['leadin_id'].'-'.$field['field_id'];

		if($field['required']){
			if(!isset($_POST[$field_name]) || trim($_POST[$field_name]) == '' || preg_match('/^\s+$/', $_POST[$field_name]) || $_POST[$field_name] == $field['label']){
				$response['error_fields'][] = $field_name;
				$response['errors'][0] = 'Please fill in all required fields.';
			}
		}
		if($field['type'] == 'email'){
			if(isset($_POST[$field_name]) && trim($_POST[$field_name]) != '' && !checkmail($_POST[$field_name])){
				$response['error_fields'][] = $field_name;
				$response['errors'][1] = 'Email address is invalid.';
			}
		}
	}

	//All valid
	if(!$response['errors']){
		
		//Insert to db
		$db->new_transaction();

		$params = array($leadin['leadin_id'], $page_id, date('Y-m-d H:i:s'));
		$db->query("INSERT INTO `leadin_submissions` (`leadin_id`, `page_id`, `timestamp`) VALUES (?,?,?)", $params);
		$submission_id = $db->insert_id();

		foreach($leadin['form_fields'] as $field){
			$field_name = 'field-'.$leadin['leadin_id'].'-'.$field['field_id'];
			$params = array($submission_id, $field['label'], $_POST[$field_name]);
			$db->query("INSERT INTO `leadin_submission_fields`(`submission_id`, `label`, `value`) VALUES(?,?,?)", $params);
			$emailbody .= '<p><strong>' .$field['label']. ':</strong><br />' .nl2br($_POST[$field_name]). '</p>';
		}
		$emailbody .= '<p><small>Submitted from: ' .$siteurl.$sitemap[$page_id]['page_url']. '</small></p>';

		if(!$db->error()){
			$db->commit();
		}
		
		//Send email
		$mail_sent = send_email($to, $subject, $emailbody);
		
		//Display error if both operations have failed
		if($db->error() && !$mail_sent){
			$response['errors'][] = 'An error occurred and your submission could not be processed. Please refresh the page and try again. If the issue persists, email us at <a href="mailto:'.$global['contact_email'].'">'.$global['contact_email'].'</a>.';
		}else{
			$response['msg_validation'] = (trim($leadin['form_success_content']) != '' ? $leadin['form_success_content'] : 'Thank you! Form has been successfully submitted!');
		}
	}

}else{
	$response['errors'][] = 'Unable to retrieve form data.';
}

//Has Errors
if($response['errors']) {
	$response['msg_validation'] = implode('<br/>', $response['errors']);
}

print json_encode($response);

?>