<?php  

/*-----------------------------------/
* Class for image optimization using reSmush.it API
* <AUTHOR> Army
* @date		18-06-07
* @file		reSmush.class.php
*/

class reSmush{

	/*-----------------------------------/
	* @constant api_base
	* Base endpoint of API
	*/
	const API_BASE = '{0}.resmush.it';

	/*-----------------------------------/
	* @var platform
	*/
	private $platform;

	/*-----------------------------------/
	* @var URLs
	*/
	private $upload_url;

	/*-----------------------------------/
	* @var siteurl
	* Complete url of site
	*/
	public $siteurl;

	/*-----------------------------------/
	* @var root
	* Relative root of the site
	*/
	public $root;


	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @return	reSmush	New reSmush object
	* @throws	Exception
	*/
	public function __construct($platform='api'){
		
		global $siteurl, $root;
		$this->siteurl = &$siteurl;
		$this->root = &$root;

		$this->platform = $platform;
		$this->upload_url = 'https://'.str_replace('{0}', $this->platform, self::API_BASE) . '/ws.php';
	}

	/*-----------------------------------/
	* Optimize image
	*
	* <AUTHOR> Army
	* @param	$image 		Filename of image
	* @param	$dir 		Directory of image (ie: images/heroes/)
	* @param	$quality 	Target quality (0-100); 90+ for good quality
	* @param	$exif 		Keep/remove EXIF data of image; Removing data will result in a smaller file size
	* @return	Array 		Image data
	* @throws	Exception
	*/
	public function optimize_image($image, $dir, $save=true, $quality=70, $exif=false){
		$file = $_SERVER['DOCUMENT_ROOT'].$this->root.$dir.$image;

		//make sure image exists
		if(file_exists($file)){

			// Create file to cURL
			$mime     = mime_content_type($file);
			$name     = pathinfo($file)['basename'];
			$CURLFile = new CURLFile($file, $mime, $name);

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, 'https://api.resmush.it/?qlty='.$quality.'&exif='.($exif ? 'true' : 'false'));
			curl_setopt($ch, CURLOPT_POST,1);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
			curl_setopt($ch, CURLOPT_POSTFIELDS, ["files" => $CURLFile]);
			curl_setopt($ch, CURLOPT_TIMEOUT, 30);
			$result = curl_exec($ch);

			if (curl_errno($ch)) {
			   $result = curl_error($ch);
			}

			curl_close($ch);
				
			$output = json_decode($result);

			//optimization failed
			if(empty($output)) {
				throw new Exception($result);
			} else if (!isset($output->dest) || isset($output->error)) {
				throw new Exception($output->error.': '.$output->error_long);
			}

			//overwrite image
			if($save){
				try{
					$this->save_image($output->dest, $image, $dir);
				}catch(Exception $e){
					throw new Exception($e->getMessage());
				}
			}

			//get image info
			if(isset($output->format)){
				$format = NULL;
			}else{
				$image_info = getimagesize($output->dest);
				$image_type = $image_info[2];
				$image_ext = pathinfo($_SERVER['DOCUMENT_ROOT'].$this->root.$dir.$image, PATHINFO_EXTENSION);
				if($image_type == IMAGETYPE_JPEG || $image_type == IMAGETYPE_GIF || $image_type == IMAGETYPE_PNG){
					$format = $image_ext;
				}
			}
							
			return array(
				'image' => $image,
				'dir' => $dir,
				'src' => $output->src,
				'dest' => $output->dest, 					//Complete path to image saved in reSmush's server
				'src_size' => $output->src_size, 			//Original file size
				'dest_size' => $output->dest_size, 			//New file size
				'percent' => $output->percent, 				//Savings
				'format' => $format,						//File extension
				'expires' => strtotime($output->expires)	//Expiry date (time format)
			);
			
		}else{
			throw new Exception('Image does not exist.');
		}
	}

	/*-----------------------------------/
	* Save new image from resmush
	*
	* <AUTHOR> Army
	* @param	$resmush_url 	Complete path of image saved in reSmush's server (ie: https://static0.resmush.it/output/48c59480c5888bf2eb6b0a0181b803d0/image.jpg)
	* @param	$image 			Filename of image
	* @param	$dir 			Directory to save image to

	* @return	Boolean 	True/False
	* @throws	Exception
	*/
	public function save_image($resmush_url, $image, $dir){

		//make sure image is coming from resmush
		$base_url = str_replace('{0}.', '', self::API_BASE);
		if(strpos($resmush_url, '.'.$base_url) !== false){

			//get image from resmush's server
			$newimage = file_get_contents($resmush_url);

			//validate mime type
			$file_info = new finfo(FILEINFO_MIME_TYPE);
			$mime_type = $file_info->buffer($newimage);
			if(in_array($mime_type, array('image/jpeg','image/pjpeg','image/png','image/gif'))){

				//save image to destination
				if(file_put_contents($_SERVER['DOCUMENT_ROOT'].$this->root.$dir.$image, $newimage)){
					return true;
				}else{
					throw new Exception('Failed to save image. Cannot save to destination.');
				}

			}else{
				throw new Exception('Failed to save image. Invalid file. '.$mime_type);
			}

		}else{
			throw new Exception('Failed to save image. URL not valid.');
		}
	}

}

?>