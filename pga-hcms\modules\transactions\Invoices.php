<?php
// error_reporting(-1);
// ini_set('display_errors', 'on');

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = 0;
	$query = $db->query("SELECT `invoice_id` FROM `invoices` WHERE `status` = ?", array('Active'));
	if($query && !$db->error()){
		$total_records = $db->num_rows();
	}
	$CMSBuilder->set_widget(80, 'Total Invoices', $total_records);
}

if(SECTION_ID == $_cmssections['transactions-invoices']){
	// exit('hi tinv');
		
	//Define vars
	$record_db = 'invoices';
	$record_id = 'invoice_id';
	$record_name = 'Invoice';
	
	$errors = false;
	$required = array();
	$required_fields = array('bill_to' => 'Bill To', 'first_name' => 'First Name', 'last_name' => 'Last Name', 'email' => 'Email Address', 'phone' => 'Phone Number'); // for validation

	//Get GL Accounts
	$glaccounts = array();
	$query = $db->query("SELECT * FROM `gl_accounts` ORDER BY `gl_number`");
	if($query && !$db->error()){
		$glaccounts = $db->fetch_array();
	}
	
	//Get default rates
	$tax_rates = $Registration->get_taxes(0, 'AB');
	
	//Get records
	$records_arr = array();
	$params = array(' ', 'Active');
	$where = " WHERE `$record_db`.`status` = ?";

	if(ITEM_ID != '' && ACTION == 'edit'){
		$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`$record_id` = ?";
		$params[] = ITEM_ID;

	}else{
		
		if($searchterm != ""){
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."(`$record_db`.`invoice_number` LIKE ? || `$record_db`.`bill_to` LIKE ? || `$record_db`.`email` LIKE ? || `$record_db`.`phone` LIKE ?)";
			$params[] = '%' .$_GET['search']. '%';
			$params[] = '%' .$_GET['search']. '%';
			$params[] = '%' .$_GET['search']. '%';
			$params[] = '%' .$_GET['search']. '%';
		}
		if(!isset($_SESSION['search_start_date'][SECTION_ID]) || isset($_POST['clear-search'])){
			$_SESSION['search_start_date'][SECTION_ID] = date('Y-m-d', strtotime('-1 year'));
		}
		if(!isset($_SESSION['search_end_date'][SECTION_ID]) || isset($_POST['clear-search'])){
			$_SESSION['search_end_date'][SECTION_ID] = date('Y-m-d');
		}
		if(isset($_GET['start_date'])){
			$_SESSION['search_start_date'][SECTION_ID] = $_GET['start_date'];
		}
		if(isset($_GET['end_date'])){
			$_SESSION['search_end_date'][SECTION_ID] = $_GET['end_date'];
		}
		if(isset($_SESSION['search_start_date'][SECTION_ID]) && $_SESSION['search_start_date'][SECTION_ID] != '') {
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`invoice_date` >= ?";
			$params[] = date('Y-m-d 00:00:00', strtotime($_SESSION['search_start_date'][SECTION_ID]));
		}
		if(isset($_SESSION['search_end_date'][SECTION_ID]) && $_SESSION['search_end_date'][SECTION_ID] != '') {
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`invoice_date` <= ?";
			$params[] = date('Y-m-d 23:59:59', strtotime($_SESSION['search_end_date'][SECTION_ID]));
		}
		
		if(isset($_GET['status'])){
			$_SESSION['search_status'][SECTION_ID] = $_GET['status'];
		}
		if(isset($_POST['clear-search'])){
			$_SESSION['search_status'][SECTION_ID] = '';
		}
		if(isset($_SESSION['search_status'][SECTION_ID]) && $_SESSION['search_status'][SECTION_ID] != ''){
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`paid` = ?";
			$params[] = $_SESSION['search_status'][SECTION_ID];
		}
	}
	$query = $db->query("SELECT `$record_db`.*, CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) AS `updated_by_name`, `hio`.`event_name`, `hio`.`hio_id`, `hio`.`field`, `hio`.`comments` AS `hio_comments` FROM `$record_db` ".
	"LEFT JOIN `account_profiles` ON `$record_db`.`updated_by` = `account_profiles`.`account_id` ".
	"LEFT JOIN `hio` ON `hio`.`$record_id` = `$record_db`.`$record_id`".
	$where." ORDER BY `$record_db`.`invoice_date` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){		
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}
	
	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];
			
			//Get payments
			$records_arr[ITEM_ID]['total_paid'] = 0;
			$records_arr[ITEM_ID]['admin_fee'] = 0;
			$records_arr[ITEM_ID]['payments'] = array();
			$get_payments = $db->query("SELECT * FROM `payments` WHERE `$record_id` = ? ORDER BY `payment_date` DESC", array(ITEM_ID));
			if($get_payments && !$db->error() && $db->num_rows() > 0){
				$payment_result = $db->fetch_array();
				foreach($payment_result as $payment){
					if($payment['status'] == 1){
						$records_arr[ITEM_ID]['total_paid'] += $payment['amount'];
						$records_arr[ITEM_ID]['admin_fee'] += $payment['admin_fee'];
					}
					$records_arr[ITEM_ID]['payments'][] = $payment;
				}
			}
			
			//Get refunds
			$records_arr[ITEM_ID]['total_refunded'] = 0;
			$records_arr[ITEM_ID]['attendees_refunded'] = array();
			$records_arr[ITEM_ID]['refunds'] = array();
			$get_invoice_refunds = $db->query("SELECT `refunds`.*, `payments`.`payment_number`, `payments`.`cctype`, `payments`.`ccnumber`, `payments`.`ccexpiry` FROM `refunds` LEFT JOIN `payments` ON `refunds`.`payment_id` = `payments`.`payment_id` WHERE `refunds`.`$record_id` = ?", array(ITEM_ID));
			if($get_invoice_refunds && !$db->error() && $db->num_rows() > 0){
				$refund_result = $db->fetch_array();
				foreach($refund_result as $refund){
					if($refund['status'] == 1){
						$records_arr[ITEM_ID]['total_refunded'] += $refund['amount'];
					}
					
					$records_arr[ITEM_ID]['refunds'][] = $refund;
				}
			}	
			
			//Get hio
			$records_arr[ITEM_ID]['hio'] = array();
			$records_arr[ITEM_ID]['comments'] .= $records_arr[ITEM_ID]['hio_comments'];
			if(!empty($records_arr[ITEM_ID]['hio_id'])){
				$records_arr[ITEM_ID]['hio']['event_name'] = $records_arr[ITEM_ID]['event_name'];
				$records_arr[ITEM_ID]['hio']['field'] = $records_arr[ITEM_ID]['field'];
				
				//Get dates
				$records_arr[ITEM_ID]['hio']['event_dates'] = array();
				$query = $db->query("SELECT * FROM `hio_dates` WHERE `hio_id` = ? ORDER BY `event_date` ASC", array($records_arr[ITEM_ID]['hio_id']));
				if($query && !$db->error()){
					$result = $db->fetch_array();
					foreach($result as $date){
						$records_arr[ITEM_ID]['hio']['event_dates'][] = date('F j, Y', strtotime($date['event_date']));
					}
				}
				
				//Get courses
				$records_arr[ITEM_ID]['hio']['courses'] = array();
				$query = $db->query("SELECT * FROM `hio_courses` WHERE `hio_id` = ?", array($records_arr[ITEM_ID]['hio_id']));
				if($query && !$db->error()){
					$result = $db->fetch_array();
					foreach($result as $course){
						
						//Get holes
						$course['holes'] = array();
						$query = $db->query("SELECT * FROM `hio_holes` WHERE `course_id` = ?", array($course['course_id']));
						if($query && !$db->error()){
							$course['holes'] = $db->fetch_array();
						}
						
						$records_arr[ITEM_ID]['hio']['courses'][] = $course;
					}
				}
			}
	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){

		//Trash record
		$delete = $db->query("UPDATE `$record_db` SET `status` = ? WHERE `$record_id` = ?", array('Trashed', ITEM_ID));
		if($delete && !$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(), false);	
		}

		header("Location: " .PAGE_URL);
		exit();
		
	//Download
	}else if(isset($_POST['download'])){
		
		//Generate document
		$pdf = $Account->generate_invoice($records_arr[ITEM_ID]);
		require_once("../includes/plugins/mpdf60/mpdf.php");
		if(class_exists('mPDF')){
			$filename = 'Invoice-'.$records_arr[ITEM_ID]['invoice_number'].'.pdf';
			$mpdf = new mPDF('utf-8',array(216,279.4),8,'Arial',20,20,16,16,5,7,'P');
			$mpdf->SetDisplayMode('fullpage');
			$mpdf->list_indent_first_level = 0;
			$mpdf->WriteHTML($pdf, 2);
			$mpdf->Output($filename,'D');
		}

	//Save item
	}else if(isset($_POST['save'])){

		if(ACTION == 'add'){
					
			//Validate
			$required_missing = false;
			if(!empty($required_fields)) {
				foreach($required_fields as $field_key => $field_name) {
					if(isset($_POST[$field_key])) {
						if(trim($_POST[$field_key]) == '') {
							$required_missing = true;
							array_push($required, $field_key);
						}
					} else {
						$required_missing = true;
						array_push($required, $field_key);
					}
				}
			}
			if($required_missing) {
				$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
			}
		
			//Calculate total
			if($_POST['amount'] <= 0){
				$errors[] = 'Invoice amount cannot be $0.00.';
				array_push($required, 'amount');
			}
			if($_POST['taxes'] <= 0){
				$_POST['taxes'] = 0.00;
			}
			$_POST['invoice_total'] = number_format($_POST['amount']+$_POST['taxes'], 2, '.', '');
		

			if(!$errors){

				$db->new_transaction();

				//Insert to db
				$params = array(
					$_POST['bill_to'],
					$_POST['first_name'],
					$_POST['last_name'],
					$_POST['email'],
					$_POST['phone'],
					$_POST['comments'],
					$_POST['taxes'],
					$tax_rates['gst_rate'],
					$tax_rates['pst_rate'],
					$tax_rates['hst_rate'],
					$_POST['invoice_total'],
					(!empty($_POST['gl_id']) ? $_POST['gl_id'] : NULL),
					date('Y-m-d H:i:s'),
					(!empty($_POST['due_date']) ? $_POST['due_date'] : NULL),
					(!empty($_POST['account_id']) ? $_POST['account_id'] : NULL),
					date('Y-m-d H:i:s'),
					USER_LOGGED_IN
				);
				$query = $db->query("INSERT INTO `$record_db`(`bill_to`, `first_name`, `last_name`, `email`, `phone`, `comments`, `taxes`, `gst_rate`, `pst_rate`, `hst_rate`, `invoice_total`, `gl_id`, `invoice_date`, `due_date`, `account_id`, `last_updated`, `updated_by`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);

				//Update invoice number
				if(ITEM_ID == ''){
					$invoice_id = $db->insert_id();
					$invoice_number = 'PGA'.str_pad($invoice_id, 5, '0', STR_PAD_LEFT).'-04';
					$query = $db->query("UPDATE `$record_db` SET `invoice_number` = ? WHERE `invoice_id` = ?", array($invoice_number, $invoice_id));

				}else{
					$invoice_number = $records_arr[ITEM_ID]['invoice_number'];
				}

				//No errors
				if(!$db->error()){
					$db->commit();

					//Send email notification
					if(isset($invoice_id)){
						$subject = 'Invoice No. '.$invoice_number;
						$message = '<h3>Invoice Notification</h3><p>This email is to inform you of a new invoice on your account.</p>
						<p><strong>Invoice No. ' .$invoice_number. '</strong><br />
						<strong>Amount Due: $' .number_format($_POST['invoice_total'], 2). '</strong><br />
						' .(!empty($_POST['due_date']) ? '<strong>Due Date: ' .date('F j, Y', strtotime($_POST['due_date'])). '</strong><br />' : ''). '
						</p>
						' .(!empty($_POST['comments']) ? '<p>'.nl2br($_POST['comments']).'</p>' : ''). '
						<p>To manage your invoices or to make a payment, please <a href="' .$siteurl.$root.get_page_url(22). '">login</a> to your account or phone our office at ' .$global['contact_phone'].'.</p>
						<p>If you have any questions or concerns regarding this message, please contact us.</p>';
						$send_email = send_email($_POST['email'], $subject, $message,'');

						if($send_email){
							$CMSBuilder->set_system_alert($record_name.' was successfully saved and email notification was sent.', true);
							header("Location: " .PAGE_URL);
							exit();
						}else{
							$CMSBuilder->set_system_alert($record_name.' was successfully saved but email notification failed to send.', false);
						}

					}else{
						$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
						header("Location: " .PAGE_URL);
						exit();
					}

				}else{
					$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
				}

			}else{
				$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
				foreach($_POST AS $key=>$data){
					$row[$key] = $data;
				}	
			}
			
		}else if(ACTION == 'edit'){
									
			$params = array(
				(!empty($_POST['gl_id']) ? $_POST['gl_id'] : NULL),
				$_POST['comments'],
				date('Y-m-d H:i:s'),
				USER_LOGGED_IN,
				ITEM_ID
			);
			$query = $db->query("UPDATE `$record_db` SET `gl_id` = ?, `comments` = ?, `last_updated` = ?, `updated_by` = ? WHERE `$record_id` = ?", $params);
			if($query && !$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			}else{
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}
			
		}

	}

}

?>