<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']) {
	$get_total = $db->query("SELECT * FROM `reg_waivers`");
	if($get_total && !$db->error()) {
		$CMSBuilder->set_widget(64, 'Total Waivers', $db->num_rows());
	}
}

if(SECTION_ID == $_cmssections['registration-waivers']){
	
	//Define vars
	$record_db = 'reg_waivers';
	$record_id = 'waiver_id';
	$record_name = 'Waiver';

	$filedir = "../uploads/files/";
	$filetypes = array('pdf');
	
	$errors = false;
	$required = array();
	$required_fields = array('title' => 'Title'); //for validation

	//Get Records
	$records_arr = array();
	
	$params = array();
	if($searchterm != ""){
		$params[] = '%' .$searchterm. '%';
	}
	$query = $db->query("SELECT * FROM `$record_db` " .($searchterm != "" ? " WHERE `title` LIKE ?" : ""). " ORDER BY `ordering`", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		$delete = $db->query("DELETE FROM `reg_waivers` WHERE `$record_id` = ?", array(ITEM_ID));
		if($delete && !$db->error()){ 
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	
	//Save item
	}else if(isset($_POST['save'])){

		//Validate
		$required_missing = false;
		if(!empty($required_fields)) {
			foreach($required_fields as $field_key => $field_name) {
				if(isset($_POST[$field_key])) {
					if(trim($_POST[$field_key]) == '') {
						$required_missing = true;
						array_push($required, $field_key);
					}
				} else {
					$required_missing = true;
					array_push($required, $field_key);
				}
			}
		}
		if($required_missing) {
			$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
		}
		if(!empty($_FILES['file']['size']) && $_FILES['file']['size'] > 20480000){
			$errors[] = 'File size is too large.';
		}
		if(!empty($_FILES['file']['name'])){
			$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
			if(!in_array($ext, $filetypes)){
				$errors[] = 'File type `' .$ext. '` is restricted. Allowed file types include '.implode(', ', $filetypes).'.';
			}
		}
		if(!isset($_POST['showhide'])){
			$_POST['showhide'] = 1;
		}

		if(!$errors){
			
			//Upload file
			$file = ($_POST['old_file'] != '' ? $_POST['old_file'] : NULL);
			if(!empty($_FILES['file']['name'])){
				$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
				$newname = clean_url($_POST['title']).'-'.date("ymdhis").'.'.$ext;
				
				// $fileUpload = new FileUpload();
				// $fileUpload->load($_FILES['file']['tmp_name']);
				// $fileUpload->save($filedir, $newname);

				// Attempt to copy the temporary file to its final destination
				if (!@copy($_FILES['file']['tmp_name'], $filedir . $newname)) {
					// If the copy fails, add an error to be displayed to the user
					$errors[] = "Failed to upload the file. Please check permissions or contact an administrator.";
				
				} else {
					// The copy was successful, so we can proceed.
					if(file_exists($filedir.$newname)){
						$file = $newname;
						// If there was an old file associated with this record, delete it.
						if(!empty($_POST['old_file']) && file_exists($filedir.$_POST['old_file'])){
							unlink($filedir.$_POST['old_file']);
						}
					} else {
						$errors[] = "File upload seemed to succeed, but the file could not be found. Please try again.";
					}
				}
				//

				// if(file_exists($filedir.$newname)){
				// 	$file = $newname;
				// 	if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
				// 		unlink($filedir.$_POST['old_file']);
				// 	}
				// }
				
			}else{
				if(isset($_POST['deletefile']) && $_POST['old_file'] != ''){
					if(file_exists($filedir.$_POST['old_file'])) {
						unlink($filedir.$_POST['old_file']);
					}
					$file = NULL;
				}
			}

			//Insert to db
			$db->new_transaction();
			
			$params = array(
				ITEM_ID, 
				($_POST['event_type'] != '' ? $_POST['event_type'] : NULL),
				$_POST['title'], 
				$_POST['description'], 
				$file, 
				$_POST['required'], 
				$_POST['showhide'], 
				$_POST['ordering'],
				date("Y-m-d H:i:s"),
				date("Y-m-d H:i:s"),
				
				($_POST['event_type'] != '' ? $_POST['event_type'] : NULL),
				$_POST['title'],
				$_POST['description'],
				$file,
				$_POST['required'],
				$_POST['showhide'],
				$_POST['ordering'],
				date("Y-m-d H:i:s"),
			);
			$insert = $db->query("INSERT INTO `$record_db` (`$record_id`, `event_type`, `title`, `description`, `file_name`, `required`, `showhide`, `ordering`, `date_added`, `last_updated`) VALUES (?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `event_type`=?, `title`=?, `description`=?, `file_name`=?, `required`=?, `showhide`=?, `ordering`=?, `last_updated`=?", $params);
			
			//If assigned to an event type, remove from specific events
			if(ITEM_ID != '' && $_POST['event_type'] != ''){
				$delete = $db->query("DELETE FROM `reg_event_waivers` WHERE `waiver_id` = ?", array(ITEM_ID));
			}
			
			if(!$db->error()){
				$db->commit();
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	}
}

?>