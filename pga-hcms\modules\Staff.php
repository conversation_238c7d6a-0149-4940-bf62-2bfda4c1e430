<?php

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('staff');
	$CMSBuilder->set_widget($_cmssections['staff'], 'Total Staff Members', $total_records);
}

if(SECTION_ID == $_cmssections['staff']){

	//Define vars
	$record_db    = 'staff';
	$record_id    = 'staff_id';
	$record_name  = 'Staff Member';
	$records_name = $record_name.'s';

	//Validation
	$errors   = false;
	$required = [];
	$required_fields = [
		'name',
		'category_id'
	];

	//Image Uploader
	$imagedir    = '../images/staff/';
	$CMSUploader = new CMSUploader('staff', $imagedir);

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.name",
		"$record_db.position",
		"$record_db.email",
		"$record_db.phone",
		"staff_categories.name"
	];

	//Redirects / SEO
	$seo_page_id    = $_sitepages['staff'];
	$staff_page_url = get_page_url($seo_page_id);
	$cat_sectionurl = $CMSBuilder->get_section($_cmssections['staff_categories']);


	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;

	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get Records
	$db->query("SELECT $record_db.*, staff_categories.name as category FROM $record_db INNER JOIN staff_categories ON staff_categories.category_id = staff.category_id $where ORDER BY staff.ordering", $params);
	$records_arr = $db->fetch_assoc($record_id);
	foreach($records_arr as $item_id => &$record){
		$record['image']    = check_file($record['image'], $imagedir);
		$record['page_url'] = $siteurl.$root.$staff_page_url.$record['page'].'-'.$item_id.'/';

		unset($record);
	}

	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);
	}


	//Get staff categories
	$db->query("SELECT * FROM staff_categories ORDER BY ordering");
	$staff_categories = $db->fetch_assoc('category_id');

	//Hide yo kids
	$social_services = array_column($global['global_social'], 'service');



	//Specific Item
	if(ACTION == 'edit'){
		if($row = ($records_arr[ITEM_ID] ?? false)){

			//Grab social
			$db->query("SELECT * FROM staff_social WHERE $record_id = ?", [ITEM_ID]);
			$row['social'] = array_column($db->fetch_array(), 'url', 'service');
			$records_arr[ITEM_ID] = $row;

		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);

		//Delete images
		if(!$db->error()){
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			
			//Save sitemap
			sitemap_XML();

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);
		}

		header("Location: " .PAGE_URL);
		exit();


	//Save item
	}else if(isset($_POST['save'])){

		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);

		//Set SEO tools if they don't exist
		$_POST['focus_keyword'] = $_POST['focus_keyword'] ?? $records_arr[ITEM_ID]['focus_keyword'] ?? NULL;

		//Required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Validate image
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large.';
			$required[] = 'image';
		}

		//Specific validation
		if($_POST['email'] && !checkmail($_POST['email'])){
			$errors[] = 'Please enter a valid email.';
			$required[] = 'email';
		}

		if(!$errors){

			//Format validated data
			$pagename = clean_url($_POST['name']);
			$content  = trim(str_replace(['&nbsp;'], '', strip_tags($_POST['content']))) ? $_POST['content'] : NULL;

			//Delete old images
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//Upload new images
			try{
				$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			$db->new_transaction();

			//Insert to db
			$params = [
				ITEM_ID,
				$_POST['name'],
				$pagename,
				$_POST['category_id'],
				$_POST['position'],
				$_POST['phone'],
				$_POST['email'],
				$content,
				$images['image'] ?? NULL,
				$_POST['image_alt'],
				$_POST['showhide'],
				$_POST['ordering'],
				$_POST['meta_title'],
				$_POST['meta_description'],
				$_POST['focus_keyword'],
				date("Y-m-d H:i:s"),

				//update
				$_POST['name'],
				$pagename,
				$_POST['category_id'],
				$_POST['position'],
				$_POST['phone'],
				$_POST['email'],
				$content,
				$images['image'] ?? NULL,
				$_POST['image_alt'],
				$_POST['showhide'],
				$_POST['ordering'],
				$_POST['meta_title'],
				$_POST['meta_description'],
				$_POST['focus_keyword'],
				date("Y-m-d H:i:s")
			];
			$db->query("INSERT INTO `$record_db`(`$record_id`, `name`, `page`, `category_id`, `position`, `phone`, `email`, `content`, `image`, `image_alt`, `showhide`, `ordering`, `meta_title`, `meta_description`, `focus_keyword`, `last_modified`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `name` = ?, `page` = ?, `category_id` = ?, `position` = ?, `phone` = ?, `email` = ?, `content` = ?, `image` = ?, `image_alt` = ?, `showhide` = ?, `ordering` = ?, `meta_title` = ?, `meta_description` = ?, `focus_keyword` = ?, `last_modified` = ?", $params);
			$item_id = (ITEM_ID != '' ? ITEM_ID : $db->insert_id());

			//Insert/Update new socials
			foreach($social_services as $service){
				$url    = $_POST['social'][$service] ?? false ?: NULL;
				$params = [$item_id, $service, $url, $url];

				$db->query("INSERT INTO `staff_social` (`$record_id`, `service`, `url`) VALUES (?,?,?) ON DUPLICATE KEY UPDATE `url` = ?", $params);
			}

			if(!$db->error()){
				$db->commit();

				//Save sitemap
				sitemap_XML();

				//Save SEO score
				if($cms_settings['enhanced_seo']){

					//Set new page_url
					$page_url = $siteurl.$root.$staff_page_url.$pagename."-".$item_id."/";

					try{
						$save_score = $Analyzer->save_score($_POST['focus_keyword'], $page_url, $pagename, $_POST['name'], ($records_arr[ITEM_ID]['seo_score'] ?? NULL), $item_id, $record_db, $record_id);
						if(!empty($save_score) && (MASTER_USER || SEO_USER)){
							$seo_message = "<br/><small>".$save_score."</small>";
						}
					}catch(Exception $e){
						unset($e);
					}
				}

				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.'.($seo_message ?? ''), true);
					header("Location: " .PAGE_URL);
					exit();
				}
			}else{
				$CMSBuilder->set_system_alert('Unable to update record. ', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}
		}

	//Handle images
	}else{
		include('modules/CropImages.php');
	}
}

?>