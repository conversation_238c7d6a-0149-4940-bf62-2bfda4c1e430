<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN) {
	exit();
}

//Dashboard widget
if(SECTION_ID == 4) {
	$total_records = $db->get_record_count('account_profile_questions');
	$CMSBuilder->set_widget($_cmssections['questionnaire'], 'Total Questions', $total_records);
}

if(SECTION_ID == $_cmssections['questionnaire']) {

	// Define vars
	$record_db    = 'account_profile_questions';
	$record_id    = 'question_id';
	$record_name  = 'Question';
	$records_name = 'Questions';

	// Validation
	$errors   = false;
	$required = [];
	$required_fields = ['question'];

	// Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.question"
	];

	// Build search query
	if ($searchterm) {
		foreach ($searchable_fields as $key => $field) {
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' OR ', $searchable_fields).')';
	}

	// Get Records
	$db->query("SELECT * FROM $record_db $where ORDER BY ordering, $record_id", $params);
	$records_arr = $db->fetch_assoc($record_id);

	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}


	// Not found
	if(ACTION == 'edit' && empty($records_arr[ITEM_ID])) {
		$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
		header('Location:' .PAGE_URL);
		exit();
	}


	// Delete item
	if(isset($_POST['delete'])) {
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()) {
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		} else {
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}

		header("Location: " .PAGE_URL);
		exit();


	// Save item
	} else if(isset($_POST['save'])) {

		// Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		if(!$errors) {

			// Insert to db
			$params = [
				'question'     => $_POST['question'],
				'ordering'     => $_POST['ordering'],
				'showhide'     => !isset($_POST['showhide']),
				'date_added'   => date("Y-m-d H:i:s"),
				'last_updated' => date("Y-m-d H:i:s")
			];

			// For update, don't change date_added
			if(ACTION == 'edit'){
				unset($params['date_added']);
			}

			$db->insert($record_db, [$record_id => ITEM_ID] + $params, $params);
			if(!$db->error()) {
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			} else {
				$CMSBuilder->set_system_alert('Unable to save record.', false);
			}

		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);	
		}

	}
}

?>