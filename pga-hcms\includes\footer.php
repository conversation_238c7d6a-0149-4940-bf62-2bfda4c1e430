<?php if((defined('USER_LOGGED_IN') && USER_LOGGED_IN) && (SECTION_ID > 2 || SECTION_ID == '')){ ?>

    </section><!--close cms-content-->

</div><!--close cms-wrapper-->

<?php include('includes/widgets/sitemapreference.php'); ?>

<!--script vars-->
<?php include('includes/jsvars.php'); ?>

<!--tinymce-->
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/tinymce/tinymce.min.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/tinymce/tinymce.init.js"></script>

<!--plugins-->
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/tablesorter/js/jquery.tablesorter.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/tablesorter/js/jquery.tablesorter.widgets.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/tablesorter/addons/pager/jquery.tablesorter.pager.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/tablesorter/js/extras/jquery.metadata.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/cropperjs/cropper.min.js"></script>
<script type="text/javascript" src="<?php echo $root; ?>core/plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="<?php echo $root; ?>core/plugins/light-gallery/js/lightgallery-all.min.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/tagEditor/jquery.caret.min.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/tagEditor/jquery.tag-editor.min.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/dropzone/dropzone.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>includes/plugins/fontawesome-iconpicker/js/fontawesome-iconpicker.min.js"></script>

<!--scripts-->
<script type="text/javascript" src="<?php echo $path; ?>js/jquery.ui.touch-punch.min.js"></script>
<script type="text/javascript" src="<?php echo $path; ?>js/script.js"></script>

<?php } ?>

</body>
</html>