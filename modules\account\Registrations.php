<?php
//Display user events
if(PAGE_ID == $_sitepages['my-registrations']['page_id']){

    //Check for active login
    if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
        header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
        exit();
    }

    //Restrict hio accounts
    if(defined('HIO_ACCESS') && HIO_ACCESS){
        header('Location: ' .$_sitepages['account']['page_url']);
        exit();
    }
    
    //Define vars
    $panel_id = 74;
    $tournaments = array();
    $events = array();
    $waitinglists = array();
    $registration = array();
    $billing_profiles = array();
    $params = array();
    
    //Session alert
    if(isset($_SESSION['reg_alert']) && $_SESSION['reg_alert'] != ''){
        $alert = $_SESSION['reg_alert'];
        unset($_SESSION['reg_alert']);
    }
	
    //Get selected registration
    if(ACTION == 'edit' && ITEM_ID != ''){
        
        $registration = $Registration->get_registration(ITEM_ID);
        if(empty($registration)){
            $notfound = true;
            $errors[] = 'Registration not found. Please select from the list below.';
        }else{
                            
            //Determine if registration has any registered attendees
            $active_registrants = 0;
            $registration['event_type'] = 1;
            foreach($registration['events'] as $event){
                foreach($event['attendees'] as $attendee){
                    if($attendee['reg_status'] == 'Registered'){
                        $active_registrants++;
                    }
                }
                $registration['event_type'] = $event['event_type'];
            }
			
            //If user is registrat, allow updates
            if($registration['account_id'] == USER_LOGGED_IN){
		
                //Get billing profiles
                $query = $db->query("SELECT * FROM `account_billing_profiles` WHERE `account_id` = ? ORDER BY `date_added` ASC", array(USER_LOGGED_IN));
                if($query && !$db->error()){
                    $result = $db->fetch_array();
                    foreach($result as $row){
                        $billing_profiles[$row['billing_id']] = $row;
                        $billing_profiles[$row['billing_id']]['exp_month'] = substr($row['ccexpiry'], 0, 2);
                        $billing_profiles[$row['billing_id']]['exp_year'] = substr($row['ccexpiry'], -2, 2);

						//Check card expiry
						$billing_profiles[$row['billing_id']]['expired'] = false;
						$expiry_date = '20'.substr($row['ccexpiry'], -2, 2).substr($row['ccexpiry'], 0, 2).'01';
						if($expiry_date <= date("Ymd")){
							$billing_profiles[$row['billing_id']]['expired'] = true;
						}
					}
				}

				//Update billing profile
				if(isset($_POST['billing'])){
					if(!isset($_POST['billing_id'])){
						$errors[] = 'Unable to update billing information: No billing profile selected.';
					}else{
						if(array_key_exists($_POST['billing_id'], $billing_profiles)){
							$query = $db->query("UPDATE `reg_registrations` SET `billing_id` = ? WHERE `registration_id` = ?", array($_POST['billing_id'], ITEM_ID));
							if($query && !$db->error()){
								$registration['billing_id'] = $_POST['billing_id'];
								$alert = $Account->alert('Billing information has been updated.', true);
							}else{
								$errors[] = 'Unable to update billing information: '.$db->error();
							}
						}else{
							$errors[] = 'Unable to update billing information: Billing profile not found.';
						}
					}
				}

				//Partner selection
				if($registration['event_type'] == 2 && isset($_POST['register'])){
					if(isset($_POST['team_event_id']) && array_key_exists($_POST['team_event_id'], $registration['events'])){
						$team_event = $registration['events'][$_POST['team_event_id']];
						$team_members = 0;
						$partner_id = NULL;
						foreach($team_event['attendees'] as $team_attendee){
							if($team_attendee['reg_status'] == 'Registered'){
								$partner_id = $team_attendee['attendee_id'];
								$team_members++;
							}
						}
						if($team_event['event_type'] == 2 && $team_event['team_event'] && $team_members < 2 && $team_event['start_date'] > date("Y-m-d") && $registration['account_id'] == USER_LOGGED_IN){
							if(!empty($_POST['partner_id_'.$team_event['event_id']]) || (!empty($_POST['partner_first_name_'.$team_event['event_id']]) && !empty($_POST['partner_last_name_'.$team_event['event_id']]))){

								//Insert attendee to registration
								$params = array(
									(!empty($_POST['partner_id_'.$team_event['event_id']]) ? $_POST['partner_id_'.$team_event['event_id']] : NULL),
									$registration['registration_id'],
									$team_event['event_id'],
									$team_event['occurrence_id'],
									$partner_id,
									'Registered',
									1,
									'Partner Fee',
									0,
									date("Y-m-d H:i:s"),
									$_POST['partner_handicap_'.$team_event['event_id']]
								);
								if(!empty($_POST['partner_id_'.$team_event['event_id']])){
									try{
										$partner = $Account->get_account_profile($_POST['partner_id_'.$team_event['event_id']]);
										$params[] = (isset($partner['first_name']) ? $partner['first_name'] : NULL);
										$params[] = (isset($partner['last_name']) ? $partner['last_name'] : NULL);
										$params[] = (isset($partner['email']) ? $partner['email'] : NULL);
										$params[] = (isset($partner['phone']) ? $partner['phone'] : NULL);
										$params[] = (isset($partner['gender']) ? $partner['gender'] : NULL);
									}catch(Exception $e){
										$errors[] = 'Unable to register partner: '.$e->getMessage();
									}
								}else{
									$params[] = $_POST['partner_first_name_'.$team_event['event_id']];
									$params[] = $_POST['partner_last_name_'.$team_event['event_id']];
									$params[] = NULL;
									$params[] = NULL;
									$params[] = NULL;
								}
								if(empty($errors)){
									$query = $db->query("INSERT INTO `reg_attendees`(`account_id`, `registration_id`, `event_id`, `occurrence_id`, `partner_id`, `reg_status`, `attendee_sharing`, `ticket_type`, `ticket_price`, `date_added`, `handicap`, `first_name`, `last_name`, `email`, `phone`, `gender`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
									if($query && !$db->error()){
										$alertmsg = 'Partner has been registered for `'.$team_event['name'].'`';

										//Reload registration details
										$registration = $Registration->get_registration(ITEM_ID);

										//Send email notification
										if(isset($partner['email']) && !empty($partner['email'])){
											$message = '<h3>Registration Confirmation</h3>
											<p>This email is to inform you that <strong>' .$Account->first_name.' '.$Account->last_name.'</strong> has registered you as his/her partner for the following tournament:</p>
											<p><strong>' .$team_event['name']. ' on ' .format_date_range($team_event['start_date'], $team_event['end_date']). '.</strong></p>
											<p>To view and manage your registrations, <a href="' .$_sitepages['account']['page_url']. '" class="button" target="_blank">login</a> to your account.</p>';
											if(send_email($partner['email'], 'Registration Confirmation', $message)){
												$alertmsg .= ' and a notification email has been sent';
											}
										}

										//Remove partner from waiting list (if applicable)
										if(!empty($_POST['partner_id_'.$team_event['event_id']])){
											try{
												$Registration->wait_list_unsubscribe($team_event['occurrence_id'], $_POST['partner_id_'.$team_event['event_id']]);
											}catch(Exception $e){
											}
										}

										$alert = $Account->alert($alertmsg.'.', true);

									}else{
										$errors[] = 'Unable to register partner: '.$db->error();
									}
								}
							}else{
								$errors[] = 'Unable to register partner: Missing partner information for `' .$team_event['name']. '`.';
							}
						}else if($team_members >= 2){
							$errors[] = 'Unable to register partner: Partner has already been selected for `' .$team_event['name']. '`.';
						}else{
							$errors[] = 'Unable to register partner: Partner registration is currently unavailable for `' .$team_event['name']. '`.';
						}
					}else{
						$errors[] = 'Unable to register partner: Tournament not found.';
					}
				}
			}
			
			//Download invoice
			if(isset($_GET['download'])){				
				//Generate document
				$pdf = $Registration->generate_invoice($registration);
				// echo "mdpf found1 <br>";
				require_once("includes/plugins/mpdf60/mPDF.php");
				// echo "mdpf found2<br>";
				if(class_exists('mPDF')){
					echo "mdpf found<br>";
					$filename = 'Registration-'.$registration['registration_number'].'.pdf';
					$mpdf = new mPDF('utf-8',array(216,279.4),8,'Arial',20,20,16,16,5,7,'P');
					$mpdf->SetDisplayMode('fullpage');
					$mpdf->list_indent_first_level = 0;
					$mpdf->WriteHTML($pdf, 2);
					$mpdf->Output($filename,'D');
				} else {
					echo "mPDF not found";
				}
				
			}
		}
	}
	
	//Get all registrations
	if(ACTION == '' || (ACTION == 'edit' && empty($registration))){
		
		//Default to this year if none selected
		$_GET['year'] = (isset($_GET['year']) ? $_GET['year'] : date('Y'));
	
		//Registrations
		$params = array(USER_LOGGED_IN, USER_LOGGED_IN, 'Registered', 'Withdrawn');
		if(isset($_GET['search']) && trim($_GET['search']) != ''){
			$params[] = '%'.$_GET['search'].'%';
			$params[] = '%'.$_GET['search'].'%';
			$params[] = '%'.str_replace('$', '', $_GET['search']).'%';
		}
		if(isset($_GET['year']) && trim($_GET['year']) != ''){
			$params[] = $_GET['year'].'-01-01';
			$params[] = ($_GET['year'] == date('Y') ? ($_GET['year']+1) : $_GET['year']).'-12-31';
		}
		// $query = $db->query("SELECT `reg_attendees`.*, `reg_attendees`.`account_id` AS `attendee_account_id`, `reg_events`.`event_type`, `reg_events`.`name` AS `event_name`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`, `reg_occurrences`.`payment_deadline`, `reg_registrations`.`registration_number`, `reg_registrations`.`paid`, `reg_registrations`.`registration_total`, `reg_registrations`.`registration_date`, ".
		// "(SELECT SUM(`payments`.`admin_fee`) FROM `payments` WHERE `payments`.`registration_id` = `reg_registrations`.`registration_id` && `payments`.`status` = 1) AS `reg_admin_fee` ".
		// "FROM `reg_attendees` ".
		// "LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `reg_attendees`.`event_id` ".
		// "LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `reg_attendees`.`occurrence_id` ".
		// "LEFT JOIN `reg_registrations` ON `reg_attendees`.`registration_id` = `reg_registrations`.`registration_id` ".
		// "WHERE (`reg_attendees`.`account_id` = ? || `reg_registrations`.`account_id` = ?) && (`reg_attendees`.`reg_status` = ? || `reg_attendees`.`reg_status` = ?) ".
		// (isset($_GET['search']) && trim($_GET['search']) != '' ? "&& (`reg_registrations`.`registration_number` LIKE ? || `reg_events`.`name` LIKE ? || `reg_registrations`.`registration_total` LIKE ?) " : "").
		// (isset($_GET['year']) && trim($_GET['year']) != '' ? "&& (`reg_occurrences`.`start_date` >= ? && `reg_occurrences`.`end_date` <= ?) " : "").
		// "ORDER BY `reg_occurrences`.`start_date` DESC, `reg_occurrences`.`end_date` DESC", $params);

		$query = $db->query("SELECT `reg_attendees`.*, `reg_attendees`.`account_id` AS `attendee_account_id`, `reg_events`.`event_type`, `reg_events`.`name` AS `event_name`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`, `reg_occurrences`.`payment_deadline`, `reg_registrations`.`registration_number`, `reg_registrations`.`paid`, `reg_registrations`.`registration_total`, `reg_registrations`.`registration_date`, ".
		"(SELECT SUM(`payments`.`admin_fee`) FROM `payments` WHERE `payments`.`registration_id` = `reg_registrations`.`registration_id` && `payments`.`status` = 1) AS `reg_admin_fee`, ".
		"`reg_categories`.`name` AS `event_category` ". // <--- CHANGE #1: Added the new field
		"FROM `reg_attendees` ".
		"LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `reg_attendees`.`event_id` ".
		"LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `reg_attendees`.`occurrence_id` ".
		"LEFT JOIN `reg_registrations` ON `reg_attendees`.`registration_id` = `reg_registrations`.`registration_id` ".
		"LEFT JOIN `reg_event_categories` ON `reg_events`.`event_id` = `reg_event_categories`.`event_id` ". // <--- CHANGE #2: Joined the event_categories table
		"LEFT JOIN `reg_categories` ON `reg_categories`.`category_id` = `reg_event_categories`.`category_id` ". // <--- CHANGE #3: Joined the categories table
		"WHERE (`reg_attendees`.`account_id` = ? || `reg_registrations`.`account_id` = ?) && (`reg_attendees`.`reg_status` = ? || `reg_attendees`.`reg_status` = ?) ".
		(isset($_GET['search']) && trim($_GET['search']) != '' ? "&& (`reg_registrations`.`registration_number` LIKE ? || `reg_events`.`name` LIKE ? || `reg_registrations`.`registration_total` LIKE ?) " : "").
		(isset($_GET['year']) && trim($_GET['year']) != '' ? "&& (`reg_occurrences`.`start_date` >= ? && `reg_occurrences`.`end_date` <= ?) " : "").
		"GROUP BY `reg_attendees`.`attendee_id` ". // <--- CHANGE #4: Added GROUP BY to prevent duplicate rows
		"ORDER BY `reg_occurrences`.`start_date` DESC, `reg_occurrences`.`end_date` DESC", $params);

		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				if($row['event_type'] == 2){ //Split tournament registrations into each individual tournament
					if($row['attendee_account_id'] == USER_LOGGED_IN){
						$tournaments[$row['event_id']] = $row;	
					}
				}else{ //Events display as one row
					$events[$row['registration_id']] = $row;	
				}
			}
		}
		
		//Unsubscribe from waiting list
		if(isset($_POST['unsubscribe'])){
			try{
				$Registration->wait_list_unsubscribe($_POST['unsubscribe'], USER_LOGGED_IN);
				$alert = $Account->alert('You have successfully unsubscribed from waitlist.', true);
			}catch(Exception $e){
				$errors[] = 'Unable to unsubscribe from waitlist. '.$e->getMessage();
			}
		}
		
		//Waiting list subscriptions
		$params = array(USER_LOGGED_IN, date("Y-m-d H:i:s"));
		if(isset($_GET['search']) && trim($_GET['search']) != ''){
			$params[] = '%'.$_GET['search'].'%';
		}
		if(isset($_GET['year']) && trim($_GET['year']) != ''){
			$params[] = $_GET['year'].'-01-01';
			$params[] = $_GET['year'].'-12-31';
		}
		$query = $db->query("SELECT `reg_waiting_list`.*, `reg_events`.`name` AS `event_name`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date` FROM `reg_waiting_list` ".
		"LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `reg_waiting_list`.`event_id` ".
		"LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `reg_waiting_list`.`occurrence_id` ".
		"WHERE `reg_waiting_list`.`account_id` = ? && `reg_occurrences`.`start_date` >= ? ".
		(isset($_GET['search']) && trim($_GET['search']) != '' ? "&& `reg_events`.`name` LIKE ? " : "").
		(isset($_GET['year']) && trim($_GET['year']) != '' ? "&& (`reg_occurrences`.`start_date` >= ? && `reg_occurrences`.`end_date` <= ?) " : "").
		"GROUP BY `reg_waiting_list`.`wait_id` ORDER BY `reg_occurrences`.`start_date` ASC, `reg_occurrences`.`end_date` ASC", $params);
		if($query && !$db->error()){
			$waitinglists = $db->fetch_array();
		}
		
	}
	
} 

?>