<?php

if(!empty($page['leadin'])){
	$html     = '';
	$leadin   = $page['leadin'];
	$type     = $leadin['type'];
	$position = $leadin['position'];
	$imagedir = 'images/leadins/thumbs/';
	$leadin['image_alt'] = $leadin['image_alt'] ?: $leadin['title'];

	if(($_COOKIE['leadin_'.$leadin['leadin_id']] ?? '') != 'false'){

		//Set classes
		$classes = implode(' ', array_filter([
			'type-'.$type,
			'theme-'.$leadin['theme'],
			$position ? 'position-'.$position : '',
			$type == 'bar' && $position == 'top' ? 'open' : '',
			$type == 'popup' ? 'hidden-modal' : '',
			!$leadin['show_mobile'] == 'popup' ? 'show-tablet-l' : '',
			$type != 'bar' && check_file($leadin['image'], $imagedir) ? '' : 'noimage',
		]));

		//Display leadin
		$html .= '<aside id="leadin-popup-'.$leadin['leadin_id'].'"
						 class="leadin-popup '.$classes.'"
						 data-id="'.$leadin['leadin_id'].'"
						 data-delay="'.$leadin['delay'].'"
						 data-delay-type="'.$leadin['delay_type'].'"'.
						 ($type == 'popup' ? ' data-width="600" data-classes="leadin-popup-widget"' : '').'>

			<div class="container'.($leadin['type'] == 'bar' ? ' container-lg' : '').'">
				<div class="control-buttons">
					<span class="close-button control-button"></span>
				</div>';

			if($type != 'bar' && check_file($leadin['image'], $imagedir)){
				[$imgw, $imgh] = getimagesize($imagedir.$leadin['image']);
				$html .= '<div class="leadin-img-wrapper">
					<img class="lazy-load" src="'.empty_src($imgw, $imgh).'" data-src="'.$path.$imagedir.$leadin['image'].'"  alt="'.$leadin['image_alt'].'" />
				</div>';
			}

				//Success Content Wrapper
				$html .= '<div class="leadin-success"></div>';

				//Content
				$html .= '<div class="leadin-content">
					<div class="content-wrapper">
						<h5 class="leadin-title">'.$leadin['title'].'</h5>'.
						$leadin['content'];

				// Button
				if($leadin['url'] || ($leadin['url_target'] == 3 && $type != 'popup')){
					$button_id = 'leadin-button';
					$button_classes = ($leadin['button_style'] ?? 'button');
					$button_classes .= ($leadin['button_animation'] != '' ? ' '.$leadin['button_animation'] : '');

					if($leadin['url_target'] == 3 && ($type == 'bar' || $type == 'corner')){
						$leadin['url_text'] = $leadin['url_text'] ?: 'Expand';
						$html .= '<button class="'.$button_classes.'" data-open-hidden-modal="#leadin-form-'.$leadin['leadin_id'].'"><span>'.$leadin['url_text'].'</span></button>';
					}else{
						if(detect_phone($leadin['url'])){
							$button_classes .= ' '.'leadin-phone-button';
						}else if(checkmail($leadin['url'])){
							$button_classes .= ' '.'leadin-email-button';
						}

						$html .= create_button($leadin['url'], $leadin['url_target'], $leadin['url_text'], $button_classes);
					}
				}

					$html .= '</div>
				</div>';

			//Form
			if($leadin['url_target'] == 3){
				$html .= '<div class="leadin-form-wrapper">
					<form id="leadin-form-'.$leadin['leadin_id'].'"
						  title="'.($leadin['form_title'] ?: $leadin['url_text']).'"
						  class="leadin-form'.($type != 'popup' ? ' hidden-modal' : '').'"
						  method="post"
						  action=""
						  data-recaptcha="#recaptcha-leadin-form"
						  data-width="480">'.
						
						($type == 'popup' && $leadin['form_title'] ? '<h5 class="leadin-title">'.$leadin['form_title'].'</h5>' : '').
						$leadin['form_content'];

						foreach($leadin['form_fields'] as $field){
							$validate = $field['required'] ? ' jsvalidate' : '';

							$html .= '<div class="form-field">
								<label for="'.$field['name'].'">'.$field['label'].($field['required'] ? ' <span class="required">*</span>' : '').'</label>';

							switch($field['type']){
								case 'dropdown':
									$html .= '<select id="'.$field['name'].'" name="'.$field['name'].'" class="select'.$validate.'">
										<option value="">- Select -</option>';

									foreach($field['options'] as $option){
										$html .= '<option>'.$option.'</option>';
									}

									$html .= '</select>';
								break;

								case 'textarea':
									$html .= '<textarea id="'.$field['name'].'" name="'.$field['name'].'" class="textarea'.$validate.'"></textarea>';
								break;

								default:
									$html .= '<input type="'.$field['type'].'" id="'.$field['name'].'" name="'.$field['name'].'" class="input'.$validate.'" />';
								break;
							}

							$html .= '</div>';
						}

						$html .= '<div class="form-buttons right">
							<button type="submit" name="submitform" class="button submit"><span>'.($leadin['form_button_text'] ?: 'Submit').'</span></button>
						</div>

						<input type="hidden" name="g-recaptcha-response" value="" />
						<input type="hidden" name="xid" value="'.$_COOKIE['xid'].'" />
						<input type="hidden" name="leadin_id" value="'.$leadin['leadin_id'].'" />
						<input type="hidden" name="page_id" value="'.PAGE_ID.'" />
					</form>
				</div>';
			}

			$html .= '</div>
		</aside>

		<div class="recaptcha-modal hidden-modal" title="Verify You&rsquo;re Not a Robot">
			<div class="recaptcha-wrapper">
				<div id="recaptcha-leadin-form" class="g-recaptcha" data-sitekey="'.$global['recaptcha_key'].'"></div>
			</div>
		</div>';
	}

	echo $html;
}

?>