<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

$positionimages = $CMSUploader->position_queue();

echo "<form action='' method='post' enctype='multipart/form-data'>";

echo "<div class='panel'>";
	echo "<div class='panel-header'>Choose a Focus Point
		<span class='f_right'><a class='panel-toggle fas fa-chevron-up'></a></span>
	</div>";
	echo "<div class='panel-content clearfix'>";

		$pcount = 0;
		foreach($positionimages as $image) {
			echo "<label>" .$image['label']."</label>";
			echo "<div class='img-position-picker" .($image['name'] == 'image_mobile_position' ? ' img-picker-small' : ''). "'>
				<img src='".$path.$image['img']."' />
				<div class='radios-wrapper'>
					<input type='radio' name='image_position[".$image['name']."]' value='0% 0%' id='imgposition-".$pcount."-1' class='left-0 top-0' />
					<label for='imgposition-".$pcount."-1'></label>
					<input type='radio' name='image_position[".$image['name']."]' value='0% 50%' id='imgposition-".$pcount."-2' class='left-0 top-3' />
					<label for='imgposition-".$pcount."-2'></label>
					<input type='radio' name='image_position[".$image['name']."]' value='0% 100%' id='imgposition-".$pcount."-3' class='left-0 top-6' />
					<label for='imgposition-".$pcount."-3'></label>
					<input type='radio' name='image_position[".$image['name']."]' value='50% 0%' id='imgposition-".$pcount."-4' class='left-3 top-0' />
					<label for='imgposition-".$pcount."-4'></label>
					<input type='radio' name='image_position[".$image['name']."]' value='50% 50%' id='imgposition-".$pcount."-5' class='left-3 top-3' checked />
					<label for='imgposition-".$pcount."-5'></label>
					<input type='radio' name='image_position[".$image['name']."]' value='50% 100%' id='imgposition-".$pcount."-6' class='left-3 top-6' />
					<label for='imgposition-".$pcount."-6'></label>
					<input type='radio' name='image_position[".$image['name']."]' value='100% 0%' id='imgposition-".$pcount."-7' class='left-6 top-0' />
					<label for='imgposition-".$pcount."-7'></label>
					<input type='radio' name='image_position[".$image['name']."]' value='100% 50%' id='imgposition-".$pcount."-8' class='left-6 top-3' />
					<label for='imgposition-".$pcount."-8'></label>
					<input type='radio' name='image_position[".$image['name']."]' value='100% 100%' id='imgposition-".$pcount."-9' class='left-6 top-6' />
					<label for='imgposition-".$pcount."-9'></label>
				</div>
			</div><br class='clear'/>";
			$pcount++;
		}

	echo "</div>";
echo "</div>";

echo "<footer id='cms-footer' class='resize sticky'>";
	echo "<button type='submit' class='button f_right' name='position'><i class='fas fa-check'></i>Save Changes</button>";
echo "</footer>";

echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
echo "<input type='hidden' name='redirect' value='" .(isset($redirect) ? $redirect : "") ."' />";
echo "<input type='hidden' name='item_id' value='" .(isset($item_id) ? $item_id : ITEM_ID) ."' />";
echo "</form>";
	
?>