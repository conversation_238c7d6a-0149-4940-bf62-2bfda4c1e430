<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}


// if(SECTION_ID == 53){
if(SECTION_ID == $_cmssections['registration-settings']){
	
	//Define vars
	$errors = false;
	$required = array();

	//Save changes
	if(isset($_POST['save'])) {
		
		//Defaults
		if(trim($_POST['cancellation_fee']) == ''){
			$_POST['cancellation_fee'] = 0;
		}
		if(trim($_POST['cancellation_fee_late']) == ''){
			$_POST['cancellation_fee_late'] = 0;
		}

		if(!$errors) {
			
			//Start transaction
			$db->new_transaction();

			//Update settings
			$params = array(
				$_POST['social_sharing'],
				$_POST['attendee_sharing'],
				$_POST['waiting_lists'],
				$_POST['cancellation_fee'],
				$_POST['cancellation_fee_late'],
				$_POST['email_tournaments'],
				$_POST['email_events'],
				$_POST['email_conferences'],
				$_POST['email_hio'],
				date("Y-m-d H:i:s"),
				$reg_settings['reg_system_id']
			);
			$query = $db->query("UPDATE `reg_settings` SET `social_sharing`=?, `attendee_sharing`=?, `waiting_lists`=?, `cancellation_fee`=?, `cancellation_fee_late`=?, `email_tournaments`=?, `email_events`=?, `email_conferences`=?, `email_hio`=?, `last_updated`=? WHERE `reg_system_id`=?", $params);

			//Commit transaction
			if(!$db->error()){
				$db->commit(); 

				$CMSBuilder->set_system_alert('Registration system settings have been updated.', true);
				header('Location: '.PAGE_URL);
				exit();

			} else {
				$CMSBuilder->set_system_alert('Unable to save settings: '.$db->error(), false);
			}

		} else if($errors && is_array($errors)) {
			$CMSBuilder->set_system_alert(implode('<br/>', $errors), false);
		}
	
	//Tournament reset
	}else if(isset($_POST['reset'])){
		
		$query = $db->query("UPDATE `account_profiles` SET `category_id` = ?", array(NULL));
		if($query && !$db->error()){
			$CMSBuilder->set_system_alert('Tournament eligibility categories have been cleared for all members.', true);
			header('Location: '.PAGE_URL);
			exit();
		}else{
			$CMSBuilder->set_system_alert('Unable to reset tournament eligibility. '.$db->error(), false);
		}

	}
}


?>