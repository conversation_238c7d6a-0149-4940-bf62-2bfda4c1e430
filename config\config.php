<?php
ini_set('display_errors', 1);
	ini_set('display_startup_errors', 1);
	error_reporting(E_ALL);
session_start();

//Path info
//$root = "/";
$root = "/pga/";

$path = $root;
$cmspath = $root."pga-hcms/";
$ssl = (($_SERVER['HTTPS'] ?? '') == 'on') || (($_SERVER['HTTP_X_FORWARDED_PROTO'] ?? '') == 'https');
$siteurl = "http".($ssl ? 's' : '')."://".$_SERVER['HTTP_HOST'];

//Filemanager access token
// $fm_access_token = 'p2bUbA5r2NeThuSwUJs9d8f';
$fm_access_token = 't4bVDa6U5BWcY85DwHK8Y4s';

//Error reporting
error_reporting(-1);
ini_set('display_errors', 'off');

//Dynamic pages IDs
$_sitepages = array(
	'home' => 3,
	'sitemap' => 2,
	'contact' => 5,
	'gallery' => 7,
	'faqs' => 8,
	'staff' => 9,
	'reviews' => 10,
	'careers' => 11,
	'partners' => 20,// 17 REPLACE_ME
	'blog' => 21, // REPLACE_ME
	// Account pages
	'account'      => 22,// REPLACE_ME,
	'profile'      => 23,// REPLACE_ME,
	'register'     => 24,// REPLACE_ME,
	'login'        => 25,// REPLACE_ME,
	'reset'        => 26,// REPLACE_ME,
	'terms'        => 27,// REPLACE_ME,
	'privacy'      => 28,// REPLACE_ME
	'account-settings' => 29,
	'change-password' => 30,
	'edit-facility' => 31,
	'directory' => 32,
	'facilities' => 33,
    'post-job' => 35,
    'job-postings' => 36,
    'my-job-postings' => 37,
    'application' => 38,
	'compensation-survey' => 55,
	'motm'=>39,
	'member-resources'=>41,
	'classifields'=>42,
	'my_classifields'=>43,
	'award-winners'=> 45,
	'billing-profiles'=>48,
	'hole-in-one'=>49,
	'invoices'=>52,
	'payments'=>53,
	'my-registrations'=>56,
	'withdrawal'=>57,
	'message-centre'=>58,

	//Registration system
	// $_sitepages['events'] = $sitemap[47];
	// $_sitepages['tournaments'] = $sitemap[48];
	// $_sitepages['registration'] = $sitemap[49];
	// $_sitepages['reg_cart'] = $sitemap[53];
	// $_sitepages['reg_checkout'] = $sitemap[54];
	// $_sitepages['reg_confirm'] = $sitemap[55];
	// $_sitepages['reg_success'] = $sitemap[56];
	// $_sitepages['reg_error'] = $sitemap[57];
	//Registration system
	'tournaments' => 71,
	'events' => 72,
	'registration' => 65,
	'reg_cart' => 66,
	'reg_checkout' => 67,
	'reg_confirm' => 68,
	'reg_success' => 69,
	'reg_error' => 70,

	'top-100-program' => 59,
	'past-winners' => 77,
	'search' => 80
  );

//Months
$months = array(
	1 => 'January',
	2 => 'February',
	3 => 'March',
	4 => 'April',
	5 => 'May',
	6 => 'June',
	7 => 'July',
	8 => 'August',
	9 => 'September',
	10 => 'October',
	11 => 'November',
	12 => 'December',
);

//Days
$weekdays = array(
	1 => 'Monday',
	2 => 'Tuesday',
	3 => 'Wednesday',
	4 => 'Thursday',
	5 => 'Friday',
	6 => 'Saturday',
	7 => 'Sunday'
);

//Provinces
$provinces = array(
	'AB' => 'Alberta',
	'BC' => 'British Columbia',
	'MB' => 'Manitoba',
	'NB' => 'New Brunswick',
	'NL' => 'Newfoundland',
	'NT' => 'Northwest Territories',
	'NS' => 'Nova Scotia',
	'NU' => 'Nunavut',
	'ON' => 'Ontario',
	'PE' => 'Prince Edward Island',
	'QC' => 'Quebec',
	'SK' => 'Saskatchewan',
	'YT' => 'Yukon'
);

//States
$states = array(
	'AL' => 'Alabama',
	'AK' => 'Alaska',
	'AZ' => 'Arizona',
	'AR' => 'Arkansas',
	'CA' => 'California',
	'CO' => 'Colorado',
	'CT' => 'Connecticut',
	'DE' => 'Delaware',
	'FL' => 'Florida',
	'GA' => 'Georgia',
	'HI' => 'Hawaii',
	'ID' => 'Idaho',
	'IL' => 'Illinois',
	'IN' => 'Indiana',
	'IA' => 'Iowa',
	'KS' => 'Kansas',
	'KY' => 'Kentucky',
	'LA' => 'Louisiana',
	'ME' => 'Maine',
	'MD' => 'Maryland',
	'MA' => 'Massachusetts',
	'MI' => 'Michigan',
	'MN' => 'Minnesota',
	'MS' => 'Mississippi',
	'MO' => 'Missouri',
	'MT' => 'Montana',
	'NE' => 'Nebraska',
	'NV' => 'Nevada',
	'NH' => 'New Hampshire',
	'NJ' => 'New Jersey',
	'NM' => 'New Mexico',
	'NY' => 'New York',
	'NC' => 'North Carolina',
	'ND' => 'North Dakota',
	'OH' => 'Ohio',
	'OK' => 'Oklahoma',
	'OR' => 'Oregon',
	'PA' => 'Pennsylvania',
	'RI' => 'Rhode Island',
	'SC' => 'South Carolina',
	'SD' => 'South Dakota',
	'TN' => 'Tennessee',
	'TX' => 'Texas',
	'UT' => 'Utah',
	'VT' => 'Vermont',
	'VA' => 'Virginia',
	'WA' => 'Washington',
	'WV' => 'West Virginia',
	'WI' => 'Wisconsin',
	'WY' => 'Wyoming'
);

$countries = ['CA' => 'Canada', 'US' => 'United States']; 

?>