function recaptchaForm(e){var t=$("#"+e),i=!0,n="";"draft"!=t.find('input[name="form_action"]').val()&&(t.find(".jsvalidate").each((function(e,t){if(0!==$(t).find("input.radio, input.checkbox").length){var a=!1;$(t).find("input.radio, input.checkbox").each((function(){$(this).is(":checked")&&(a=!0)})),a||(i=!1,$(t).addClass("required"),n="Please fill out all the required fields.<br />")}else $(t).hasClass("texteditor")||""!=$.trim($(t).val())||(i=!1,$(t).addClass("required"),n="Please fill out all the required fields.<br />")})),t.find('input[type="email"]').each((function(e,t){""==$.trim($(t).val())||checkmail($(t).val())||(i=!1,$(t).addClass("required"),n+="Please enter a valid email address.<br />")}))),i?$("#recaptcha-modal").dialog("open"):dialogAlert("Error!",n,"error")}function showTab(e){const t=document.querySelectorAll(".tab-content"),i=document.querySelectorAll(".tab-buttons div");t.forEach(((t,n)=>{t.classList.toggle("active",n===e),i[n].classList.toggle("active",n===e)}))}$((function(){let e=$(".panel.partners:not(.gallery-listings) .light-gallery");observeOnce(e,(()=>loadScript(["swiper","lightgallery"],(()=>{e.each((function(){let e=$(this);e.addClass("swiper"),e.children().wrap('<div class="swiper-slide"/>'),e.children().wrapAll('<div class="swiper-wrapper"/>'),e.append('<div class="swiper-scrollbar"/>');const t=new Swiper(this,{spaceBetween:1,slidesPerView:"auto",watchSlidesProgress:!0,centerInsufficientSlides:!0,freeMode:!0,scrollbar:{el:".swiper-scrollbar",draggable:!0,hide:!1,dragSize:150},mousewheel:{forceToAxis:!0},breakpoints:{769:{scrollbar:{dragSize:"auto"}}}});e.data({swiper:t})}))}))))})),$((function(){observeOnce(".partner .gallery-listings .light-gallery",(()=>{loadScript(["savvior","lightgallery"],(()=>{window.addEventListener("savvior:redraw",(()=>{$(".light-gallery").lightGallery({selector:".gal-item"})})),savvior.init(".gallery-listings .light-gallery",{"screen and (max-width: 480px)":{columns:2},"screen and (min-width: 481px) and (max-width: 769px)":{columns:3},"screen and (min-width: 769px) and (max-width: 1024px)":{columns:6},"screen and (min-width: 1025px)":{columns:8}})}))}))})),$((function(){$(document).on("click",".dropdown-checkbox-wrapper > .side",(function(){$(this).siblings(".checklist").stop().slideToggle(300),$arrow=$(this).children("i").length?$(this).children("i"):$(this).next().children("i"),$arrow.attr("style")?$arrow.removeAttr("style"):($arrow.css("transform","scaleY(-1)"),$arrow.css("top","0"))})),$('.dropdown-checkbox-wrapper input[type="checkbox"]').on("change",(function(){var e=$(this),t=e.closest(".dropdown-checkbox-wrapper"),i=t.find(":checked"),n=t.find(".title"),a=i.length,o=[];if(a){switch(i.each((function(){o.push(e.next().text())})),keyword=n.data("default").split(" ").pop(),keyword.substr(-1)){case"y":keyword=keyword.substring(0,keyword.length-1)+"ies";break;case"s":break;default:keyword+="s"}n.text("Selected "+keyword+": "+o.length)}else n.text(n.data("default"))})),$('.dropdown-checkbox-wrapper input[type="checkbox"]').trigger("change")})),$((function(){$("button.delete").bind("click",(function(){var e=this.form;$('<div id="dialog-box"></div>').appendTo("body").html("Are you sure you want to permanently delete this item?").dialog({modal:!0,title:"Confirm",autoOpen:!0,width:300,resizable:!1,closeOnEscape:!0,closeText:"<i class='fa fa-close'></i>",buttons:{Confirm:function(){$(this).dialog("close"),$(e).submit()},Cancel:function(){$(this).dialog("close")}},show:{effect:"drop",direction:"up",duration:200},hide:{effect:"drop",direction:"up",duration:200}})})),$(".delete-button.confirm").on("click",(function(){var e=this;$('<div id="dialog-box"></div>').appendTo("body").html("Are you sure you want to permanently delete this item?").dialog({modal:!0,title:"Confirm",autoOpen:!0,width:300,resizable:!1,closeOnEscape:!0,closeText:"",buttons:{Confirm:function(){$(this).dialog("close"),$('<input type="hidden" name="delete" value="delete">').appendTo($(e).closest("form")),$(e).closest("form").submit()},Cancel:function(){$(this).dialog("close")}},show:{effect:"drop",direction:"up",duration:200},hide:{effect:"drop",direction:"up",duration:200}})}))})),$((function(){$("#contact-form").on("submit",(function(){return submitForm("contact-form","js/ajax/contactform.php",(function(){})),!1})),$("#payment-form .button").on("click",(function(){return console.log("payment form submitted 2"),submitFormPayment2("payment-form","",(function(){$("#payment-form").submit()})),!1})),$("form.dynamic-form .button").on("click",(function(){var e=$(this).parents("form").attr("id"),t=$(this).val();$("#"+e).find('input[name="form_action"]').val(t)})),$("form.dynamic-form").on("submit",(function(){var e=$(this).attr("id");return submitForm(e,"js/ajax/submitform.php",(function(){})),!1})),$("form.hidden-recaptcha .button").on("click",(function(){var e=$(this).parents("form").attr("id"),t=$(this).val();return $("#"+e).find('input[name="form_action"]').val(t),recaptchaForm(e),!1}))})),$((function(){$("#recaptcha-modal").dialog({autoOpen:!1,modal:!0,width:400})})),$((function(){$(".content-tabs").each((function(){$(this).hasClass("static-tabs")?$(this).find(".decorative").css("left",$(this).find(".ui-tabs-nav").width()+20):$(this).tabs({show:{effect:"fadeIn",duration:300},hide:{effect:"fadeOut",duration:300},activate:function(){responsiveItems()}}).each((function(){var e=$(this),t=$(e).data("tabs-limit");if($(e).find(".ui-tabs-anchor").length>t){var i="";$(e).find(".ui-tabs-anchor").each((function(){i+='<option value="'+$(this).data("index")+'">'+$(this).text()+"</option>"})),$(e).addClass("responsive-tabs").prepend('<select name="content-tabs-select" class="select ui-tabs-select">'+i+"</select>").find(".ui-tabs-select").bind("change",(function(){$(e).tabs("option","active",$(this).val())}))}$(this).find(".decorative").css("left",$(this).find(".ui-tabs-nav").width()+20)}))}))})),$(window).resize((function(){$(".content-tabs").each((function(){$(this).find(".decorative").css("left",$(this).find(".ui-tabs-nav").width()+20)}))})),$((function(){$(".slider").each((function(){const e=$(this),t=parseInt(e.data("min"))||0,i=parseInt(e.data("max"))||100,n=parseInt(e.data("value"))||0,a=e.closest(".slider-container").find('input[type="hidden"]');e.slider({min:t,max:i,value:n,create:function(e,t){$(this).find(".ui-slider-handle").text(n)},slide:function(e,t){a.val(t.value),$(this).find(".ui-slider-handle").text(t.value)}})}))}));