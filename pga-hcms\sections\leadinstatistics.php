<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Submissions
if(ITEM_ID == ''){

	include('includes/widgets/leadinchart.php');

	echo '<div class="panel">
		<div class="panel-header">Conversions
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
				<thead>
					<th width="10px" class="center">Type</th>
					<th>Page</th>
					<th data-sorter="date">Date Submitted</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
				</thead>

				<tbody>';
				foreach($conversions as $row){
					if($row['event'] == 'open') continue;
					
					$row['icon'] = ($row['event'] == 'submission' ? 'envelope' : 'mouse-pointer');
					echo '<tr>
						<td height="60px" class="center"><i class="fas fa-'.$row['icon'].'" title="'.ucfirst(str_replace('-', ' ', $row['event'])).'"></i></td>
						<td height="60px">'.(!empty($row['page_name']) ? '<a href="'.$row['page_edit_url'].'">'.$row['page_name'].'</a> &nbsp; <a href="'.$row['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a>' : '').'</td>
						<td height="60px">' .date('M j, Y H:i', strtotime($row['timestamp'])). '</td>
						<td height="60px" class="right">'.($row['event'] == 'submission' ? '<a href="' .PAGE_URL. '?action=edit&item_id=' .$row['submission_id']. '&page_id='.PAGE_ID.'" class="button-sm"><i class="fas fa-eye"></i>View</a>' : '').'</td>
					</tr>';	
				}
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>	
	</div>

	<div id="cms-footer" class="resize">
		<a href="'.$mainpage['page_url']. '" class="cancel">Back to '.$mainpage['name'].'</a>
	</div>';

//Selected submission
}else{

	$data = $submissions[ITEM_ID];

	echo '<form action="" method="post" enctype="multipart/form-data">

	<div class="panel">
		<div class="panel-header">&quot;'.$leadin['title'].'&quot;
			<span class="f_right"><a class="panel-toggle fa fa-chevron-up"></a></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" width="100%">';

			foreach($data['form_fields'] as $field){
				echo '<tr>
					<td width="200px">' .$field['label']. ':</td>
					<td>'.$field['value'].'</td>
				</tr>';
			}

				echo '<tr>
					<td width="200px">Submitted on:</td>
					<td>'.date('M j, Y g:iA', strtotime($data['timestamp'])).'</td>
				</tr>

				<tr>
					<td width="200px">Page:</td>
					<td>'.(!empty($data['page_name']) ? '<a href="'.$data['page_edit_url'].'">'.$data['page_name'].'</a> &nbsp; <a href="'.$data['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a>' : '').'</td>
				</tr>

			</table>
		</div>
	</div>

	<div id="cms-footer" class="resize">
		<a href="'.PAGE_URL. '?page_id='.PAGE_ID.'" class="cancel">Cancel</a>
	</div>

	<input type="hidden" name="xssid" value="' .$_COOKIE['xssid'] .'" />
	</form>';

}

?>