<!--open login-wrapper-->
<div id="login-wrapper">

	<!--open login-form-->
	<div id="login-form">
		<header id="login-header"><img src="<?php echo $path; ?>images/logo.svg" alt="Honeycomb CMS" /></header>
		<form name="login-form" action="" method="post">
			<?php 
			$system_alert = $CMSBuilder->system_alert();
			echo (!is_null($system_alert) ? '<div id="login-alert"'.($system_alert[count($system_alert)-1]['status'] == true ? " class='success'": "").'>' .$system_alert[count($system_alert)-1]['message']. '</div>' : '');
			?>

			<?php if($displayform){ ?>

				<p>Enter your email address below to have a password reset request emailed to you. Follow the instructions to reset your password.</p>
				<div class="form-field">
					<label>Email Address</label>
					<input type="text" name="email" class="input" tabindex="1" />
				</div>

			<?php }else if(isset($_GET['reset'])){ ?>

				<p>Please enter your new password below.</p>
				<div class="form-field">
					<label>New Password</label>
					<input type="password" name="new_password" class="input" value="" tabindex="1" />
				</div>
				<div class="form-field">
					<label>Re-enter Password</label>
					<input type="password" name="confirm_password" class="input" value="" tabindex="2" />
				</div>

			<?php } ?>

			<div class="right">
				<p><button type="submit" name="<?php echo (isset($_GET['reset']) ? 'reset' : 'forgot'); ?>" class="button" tabindex="2"><i class="fas fa-key"></i>Reset Password</button></p>
				<a href="<?php echo $path; ?>login/"><i class="fas fa-lock"></i> Back to Login</a>
			</div>

			<input type="hidden" name="xssid" value="<?php echo $_COOKIE['xssid']; ?>" />
		</form>

	</div><!--close login-form-->
    
</div><!--close login-wrapper-->