@charset "utf-8";
/* 
	mixins.less

*/


/*------ utilities ------*/
//Fluid property values - set a min and max value between two viewport widths.
.fluid-property(@property, @min-size, @max-size, @min-width: 768px, @max-width: 1367px){
	@y1: unit(@min-size); // Y axis is font size
	@y2: unit(@max-size);
	@x1: unit(@min-width); // X axis is viewport width
	@x2: unit(@max-width); 
	
	// Reduce formula and round
	@v: round(( 100 * (@y2 - @y1) / (@x2 - @x1) ), 5);
	@r: round(( (@x1 * @y2 - @x2 * @y1) / (@x1 - @x2) ), 5);
	
	// Format result
	@fluid: unit(@v, vw);
	@sign: if(@r = 0, "", if(@r > 0, " + ", " - "));
	@relative: if(@r = 0, "", unit(abs(@r), get-unit(@min-size)));

	// Output
	@min: min(@min-size, @max-size);
	@scale: ~"@{fluid}@{sign}@{relative}";
	@max: max(@min-size, @max-size);
	@{property}: @min-size;
	@{property}: clamp(@min, @scale, @max);
}

//Shorthand for font size, with units auto generated
.fluid-size(@min-size, @max-size, @min-width: 480px, @max-width: 1366px){
	.fluid-property(font-size, unit(@min-size, px), unit(@max-size, px), @min-width, @max-width);
}


/*------ typography ------*/
.light{font-weight: 300; }
.regular{font-weight: 400; }
.medium{font-weight: 500; }
.semibold{font-weight: 600;}
.bold{font-weight: 700; }
.extrabold{font-weight: 800; }
.black{font-weight: 900; }

.uppercase{text-transform: uppercase; }
.capitalize{text-transform: capitalize; }
.strikethrough{text-decoration: line-through; }
.underline{text-decoration: underline; }

.letter-spacing(@ps-value){
	@ps-tracking: 1000;
	letter-spacing:(@ps-value/@ps-tracking)*(1em);
}
.text-shadow(@x:0; @y:0; @blur:5px; @color: fade(@color-dark, 25%)){
	text-shadow+:@x @y @blur @color;
}
.font-awesome(@unicode:" "; @weight:900; @family: "Free";){
	font-family: "Font Awesome 6 @{family}";
	font-weight: @weight;
	content:"\@{unicode}";
}
.unstyled-list(@margin: 0; @padding: 0;) {
	list-style: none;
	margin: @margin;
	padding: @padding;
}


/*------ forms ------*/
.no-appearance(){
	-webkit-appearance:none;
	-moz-appearance:none;
	appearance:none;
}
.placeholder(@rules){
	&::-webkit-input-placeholder{@rules();}
	&:-ms-input-placeholder{@rules();}
	&::-moz-placeholder{@rules();}
	&:-moz-placeholder{@rules();}
	&::placeholder{@rules();}
}
.select-arrow(@color: #aaa){
	@svg: escape('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 7" fill="@{color}"><path d="M5.5 6.8A.6.6 0 006 7a.6.6 0 00.5-.2l5.3-5.1a.6.6 0 00.2-.5.6.6 0 00-.2-.4l-.6-.6a.7.7 0 00-.5-.2.6.6 0 00-.4.2L6 4.2 1.7.3a.6.6 0 00-.4-.2.7.7 0 00-.5.2L.2.8a.6.6 0 00-.2.4.6.6 0 00.2.5z"/></svg>');
	background-image:url('data:image/svg+xml;charset=UTF-8,@{svg}');
}


/*------ display ------*/
.full{width: 100%;}
.half{width: 50%;}
.auto{width: auto;}
.auto-width{width: auto !important;}
.auto-height{height: auto !important;}

// Screen readers only
.sr-only{
	position: absolute;
	margin: 0;
	padding: 0;
	border: 0;
	width: 0.1px;
	height: 0.1px;
	opacity: 0;
	background: none;
	overflow: hidden;
}

.f_right{float:right; display:block;}
.f_left{float:left; display:block;}
.clear{display:block; clear:both;}
.clearfix{
	&:after{content:""; display:table; clear:both;}
}

.right{text-align:right;}
.left{text-align:left;}
.center{text-align:center;}

.relative{position:relative;}
.absolute{position:absolute;}

.block{display:block !important;}
.inline-block{display:inline-block !important;}
.inline{display:inline !important;}
.hidden{display:none !important;}
.deferred(){display: none; }

.show-tablet-p{ @media @max-tablet-p {display: none !important; } }
.show-tablet-l{ @media @max-tablet-l {display: none !important; } }
.show-notebook{ @media @max-notebook {display: none !important; } }
.show-desktop{ @media @max-desktop {display: none !important; } }
.show-widescreen{ @media @max-widescreen {display: none !important; } }

.hide-tablet-p{ @media @tablet-p{display: none !important; } }
.hide-tablet-l{ @media @tablet-l{display: none !important; } }
.hide-notebook{ @media @notebook{display: none !important; } }
.hide-desktop{ @media @desktop{display: none !important; } }
.hide-widescreen{ @media @widescreen{display: none !important; } }

.position(@tbrl: 0){.position(@tbrl, @tbrl, @tbrl, @tbrl); }
.position(@tb, @rl){.position(@tb, @rl, @tb, @rl); }
.position(@t, @rl, @b){.position(@t, @rl, @b, @rl); }
.position(@t, @r, @b, @l){position: absolute; top: @t; right: @r; bottom: @b; left: @l; }
.position(@t, @r, @b, @l, @w, @h: @w){.position(@t, @r, @b, @l); width: @w; height: @h; margin: auto; }

.noborder{border:0 !important;}
.nobg{background:none !important;}

.nomargin{margin:0 !important;}
.nomargin-v, .nomargin-t{margin-top: 0 !important;}
.nomargin-h, .nomargin-r{margin-right: 0 !important;}
.nomargin-v, .nomargin-b{margin-bottom: 0 !important;}
.nomargin-h, .nomargin-l{margin-left: 0 !important;}

.nopadding{padding:0 !important;}
.nopadding-v, .nopadding-t{padding-top: 0 !important;}
.nopadding-h, .nopadding-r{padding-right: 0 !important;}
.nopadding-v, .nopadding-b{padding-bottom: 0 !important;}
.nopadding-h, .nopadding-l{padding-left: 0 !important;}

.box-shadow(@x:0px; @y:0px; @blur:4px; @spread:0px; @color: fade(@color-dark, 16%);) {
	-moz-box-shadow+:@arguments;
	-webkit-box-shadow+:@arguments;
	box-shadow+:@arguments;
}
.box-shadow-inset(@x:0px; @y:0px; @blur:4px; @spread:0px; @color: fade(@color-dark, 16%);) {
	-moz-box-shadow+:@arguments inset;
	-webkit-box-shadow+:@arguments inset;
	box-shadow+:@arguments inset;
}
.no-shadow(){
	-moz-box-shadow:none;
	-webkit-box-shadow:none;
	box-shadow:none;
	text-shadow:none;
}
.no-select(){
	-moz-user-select:none;
	-webkit-user-select:none;
	-ms-user-select:none;
	user-select:none;
}


/*------ gradients ------*/
@default-colours: ~"@{color-grad-in}, @{color-grad-out}";
.gradient(repeat; @colours: @default-colours; @dir: 45deg;) {background-image+: repeating-linear-gradient(@dir, @colours); }
.gradient(radial; @colours: @default-colours; @dir: ~"circle at center";) {background-image+: radial-gradient(@dir, @colours); }
.gradient(conic; @colours: @default-colours; @dir: ~"from 0deg";) {background-image+: conic-gradient(@dir, @colours); }
.gradient(~"radial repeat"; @colours: @default-colours; @dir: ~"circle at center";) {background-image+: repeating-radial-gradient(@dir, @colours); }
.gradient(~"conic repeat"; @colours: @default-colours; @dir: ~"from 0deg";) {background-image+: repeating-conic-gradient(@dir, @colours); }
.gradient(@colours: @default-colours; @dir: 45deg;) when 
	not (@colours = repeat) and 
	not (@colours = radial) and 
	not (@colours = ~"radial repeat") and 
	not (@colours = conic) and 
	not (@colours = ~"conic repeat") {background-image+: linear-gradient(@dir, @colours); }

.gradient-text(@out: @color-grad-in; @in: @color-grad-out; @scale: 0.5) {
	color: @out;
	background-clip: text;
	background-size: (100% / (@scale)) auto; // Scale is the number of "ribbons" in the gradient
	-webkit-background-clip: text;
	text-fill-color: transparent;
	-webkit-text-fill-color: transparent;
	.gradient(@out, @in, @out; to right;);
}


/*------ flexbox ------*/
.flexbox(@flow:row wrap; @main:flex-start; @cross:stretch){
	display:-webkit-flex;
	display:-ms-flexbox;
	display:-webkit-box;
	display:-moz-box;
	display:flex;
	flex-flow:@flow;
	justify-content:@main;
	align-items:@cross;
}
.inline-flexbox(@flow:row wrap; @main:flex-start; @cross:stretch){
	display:-webkit-inline-box;
	display:-ms-inline-flexbox;
	display:-webkit-inline-flex;
	display:-moz-inline-box;
	display:inline-flex;
	flex-flow:@flow;
	justify-content:@main;
	align-items:@cross;
}
.flex(@arguments: 0 0 auto;){
	-webkit-box-flex:@arguments;
	-moz-box-flex:@arguments;
	-webkit-flex:@arguments;
	-ms-flex:@arguments;
	flex:@arguments;
}


/*------ columns ------*/
.multi-column(@column-count: 3; @column-gap: 0px;) {
	-webkit-column-count: @column-count;
	-moz-column-count: @column-count;
	column-count: @column-count;
	-webkit-column-gap: @column-gap;
	-moz-column-gap: @column-gap;
	column-gap: @column-gap;
}
/* For elements within a .multi-column element. Prevents elements from breaking into multiple columns */
.preserve-column(@overflow: hidden;) {
	overflow: @overflow; /* Fix for firefox and IE 10-11  */
	-webkit-column-break-inside: avoid; /* Chrome, Safari, Opera */
	page-break-inside: avoid; /* Firefox */
	break-inside: avoid; /* IE 10+ */
	break-inside: avoid-column;
}


/*------ filters ------*/
.filter(@arguments:none;){
	-webkit-filter+_:@arguments;
	-moz-filter+_:@arguments;
	-o-filter+_:@arguments;
	filter+_:@arguments;
}
.blur(@amount:20px){
	-webkit-filter+_:blur(@amount);
	-moz-filter+_:blur(@amount);
	-ms-filter+_:blur(@amount);
	filter+_:blur(@amount);
}
.grayscale(@amount:100%){
	-webkit-filter+_:grayscale(@amount);
	-moz-filter+_:grayscale(@amount);
	-ms-filter+_:grayscale(@amount);
	filter+_:grayscale(@amount);
}


/*------ transformations ------*/
.transform(@arguments:none;){
	-webkit-transform+_:@arguments;
	-moz-transform+_:@arguments;
	-o-transform+_:@arguments;
	transform+_:@arguments;
}
.scale(@factor){
	-ms-transform+_:scale(@factor);
	-o-transform+_:scale(@factor);
	transform+_:scale(@factor);
	-webkit-transform+_:scale(@factor) rotate(0.02deg);
	-moz-transform+_:scale(@factor) rotate(0.02deg);
}
.scaleX(@factor){
	-ms-transform+_:scaleX(@factor);
	-o-transform+_:scaleX(@factor);
	transform+_:scaleX(@factor);
	-webkit-transform+_:scaleX(@factor) rotate(0.02deg);
	-moz-transform+_:scaleX(@factor) rotate(0.02deg);
}
.scaleY(@factor){
	-ms-transform+_:scaleY(@factor);
	-o-transform+_:scaleY(@factor);
	transform+_:scaleY(@factor);
	-webkit-transform+_:scaleY(@factor) rotate(0.02deg);
	-moz-transform+_:scaleY(@factor) rotate(0.02deg);
}
.rotate(@deg){
	-webkit-transform+_:rotate(@deg);
	-moz-transform+_:rotate(@deg);
	-ms-transform+_:rotate(@deg);
	-o-transform+_:rotate(@deg);
	transform+_:rotate(@deg);
}
.rotateX(@deg){
	-webkit-transform+_:rotateX(@deg);
	-moz-transform+_:rotateX(@deg);
	-ms-transform+_:rotateX(@deg);
	-o-transform+_:rotateX(@deg);
	transform+_:rotateX(@deg);
}
.rotateY(@deg){
	-webkit-transform+_:rotateY(@deg);
	-moz-transform+_:rotateY(@deg);
	-ms-transform+_:rotateY(@deg);
	-o-transform+_:rotateY(@deg);
	transform+_:rotateY(@deg);
}
.skew(@deg, @deg2){
	-webkit-transform+_:skew(@deg, @deg2);
	-moz-transform+_:skew(@deg, @deg2);
	-ms-transform+_:skew(@deg, @deg2);
	-o-transform+_:skew(@deg, @deg2);
	transform+_:skew(@deg, @deg2);
}
.skewX(@deg){
	-webkit-transform+_:skewX(@deg);
	-moz-transform+_:skewX(@deg);
	-ms-transform+_:skewX(@deg);
	-o-transform+_:skewX(@deg);
	transform+_:skewX(@deg);
}
.skewY(@deg){
	-webkit-transform+_:skewY(@deg);
	-moz-transform+_:skewY(@deg);
	-ms-transform+_:skewY(@deg);
	-o-transform+_:skewY(@deg);
	transform+_:skewY(@deg);
}
.translate(@arguments){
	-webkit-transform+_:translate(@arguments);
	-moz-transform+_:translate(@arguments);
	-ms-transform+_:translate(@arguments);
	-o-transform+_:translate(@arguments);
	transform+_:translate(@arguments);
}
.translateX(@x){
	-webkit-transform+_:translateX(@x);
	-moz-transform+_:translateX(@x);
	-ms-transform+_:translateX(@x);
	-o-transform+_:translateX(@x);
	transform+_:translateX(@x);
}
.translateY(@y){
	-webkit-transform+_:translateY(@y);
	-moz-transform+_:translateY(@y);
	-ms-transform+_:translateY(@y);
	-o-transform+_:translateY(@y);
	transform+_:translateY(@y);
}
.translateZ(@z){
	-webkit-transform+_:translateZ(@z);
	-moz-transform+_:translateZ(@z);
	-ms-transform+_:translateZ(@z);
	-o-transform+_:translateZ(@z);
	transform+_:translateZ(@z);
}


/*------ animations ------*/
.trans(@type; @time:0.3s; @trans:ease; @delay:0s;) {
	-webkit-transition+:@arguments;
	-moz-transition+:@arguments;
	-ms-transition+:@arguments;
	-o-transition+:@arguments;
	transition+:@arguments;
}
.no-trans(){
	-webkit-transition:none;
	-moz-transition:none;
	-ms-transition:none;
	-o-transition:none;
	transition:none;
}
.animation(@keyframe; @time; @rest...) {
	-webkit-animation+:@arguments;
	-ms-animation+:@arguments;
	animation+:@arguments;
	-webkit-animation-fill-mode:both; 
	-ms-animation-fill-mode:both; 
	animation-fill-mode:both;
	animation-play-state: var(--animation-play-state, running);
}
.no-anim(){
	-webkit-animation:none !important;
	-ms-animation:none !important;
	animation:none !important;
}
.keyframes(@name,@rules) {
	@-webkit-keyframes @name {@rules();}
	@-moz-keyframes @name {@rules();}
	@-o-keyframes @name {@rules();}
	@keyframes @name {@rules();}
}