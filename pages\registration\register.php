<?php

$required = array();

//Display registration steps
if(!empty($event)){
			
	$html .= '<h3>' .$event['event_name']. '</h3>
	<h4>' .format_date_range($event['start_date'], $event['end_date']). '</h4>
	<p><strong>Spots Available: ' .(!is_null($event['spots_available']) ? $event['spots_available'] : 'Unlimited'). '</strong></p>
	<hr />';	
	
	//Steps
	if(count($steps) > 1){
		$html .= '<div class="content-tabs static-tabs ui-tabs clear clearfix">
			<nav class="tabs-nav-wrapper clearfix">
				<ul class="ui-tabs-nav clearfix">
					<li class="' .(STEP == 1 ? 'ui-state-active' : ''). '"><span>Event Attendees</span></li>
				</ul>
				<span class="decorative"></span>
			</nav><br />
		</div>';
	}
	
	//Attendees
	if(STEP == 1){
							
		$html .= '<form id="event-register-form" name="event-register-form" action="' .$page['page_url']. '" method="post">';
		
			if(!is_null($event['max_attendees']) && $event['max_attendees'] > 1){
				$html .= '<p>Enter the information below for each individual attending this event (maximum of ' .$event['max_attendees']. ').</p>';
			}
			$html .= '<p><small>Required Fields</small> <strong class="color-red">*</strong></p>';
		
			//Display attendee fields
			foreach($data['attendees'] as $key=>$attendee){
				$html .= '<div class="reg-attendee">
					<h4 class="attendee-header">Attendee Information</h4>
					<fieldset class="form-grid">';

						//Select ticket
						$html .= '<div class="clearfix">
							<div class="form-field">
								<label>Ticket Type <strong class="color-red">*</strong></label>
								<select name="ticket_id[]" class="select' .(in_array('ticket_id_'.$key, $required) ? ' required' : ''). '">
									<option value="">- Please Choose -</option>';
									foreach($event['pricing'] as $price){
										$html .= '<option value="' .$price['pricing_id']. '"' .((!empty($attendee['ticket_type']) && $attendee['ticket_type'] == $price['price_type']) && (!empty($attendee['ticket_price']) && $attendee['ticket_price'] == $price['price']) ? ' selected' : ''). '>' .$price['price_type'].' - $'.number_format($price['price'], 2). '</option>';
									}
								$html .= '</select>
							</div>
						</div>';

						//Form fields

						// echo "<pre>";
						// print_r($event['attendee_information']);
						// echo "</pre>";

						$count = 0;
						foreach($event['attendee_information'] as $field=>$optional){
							$count++;
							$label = ucwords(str_replace('_', ' ', $field));
							// $html .= '<div class="form-column ' .($count%2 ? 'f_left' : 'f_right'). '">';
							$html .= '<div class="form-field">';
								$html .= '<label>' .$label. ($optional == 'Required' ? ' <strong class="color-red">*</strong>' : '').'</label>
								<input type="text" name="' .$field. '[]" class="input' .(in_array($field.'_'.$key, $required) ? ' required' : ''). '" value="' .(isset($attendee['attendee_fields'][$field]) ? $attendee['attendee_fields'][$field] : ''). '" />
							</div>';
						}
				
						//Addons
						if(!empty($event['addons'])){
							foreach($event['addons'] as $addon){
								$count++;
								// $html .= '<div class="form-column ' .($count%2 ? 'f_left' : 'f_right'). '">';
									$html .= '<div class="form-field">';
									$html .= '<label>' .$addon['name']. ($addon['required'] == 'Required' ? ' <strong class="color-red">*</strong>' : '').'</label>
									<select name="addon_' .$addon['addon_id']. '[]" class="select' .(in_array('addon_'.$addon['addon_id'].'_'.$key, $required) ? ' required' : ''). '">
										<option value="">- Please Choose -</option>';
										foreach($addon['options'] as $option){
											$html .= '<option value="' .$option['option_id']. '"' .(isset($attendee['addons'][$addon['addon_id']]['option_id']) && $attendee['addons'][$addon['addon_id']]['option_id'] == $option['option_id'] ? ' selected' : ''). '>' .$option['name'].($option['price_adjustment'] > 0 ? ' - $'.number_format($option['price_adjustment'], 2) : ''). '</option>';	
										}
									$html .= '</select>
								</div>';
							}
						}

						$html .= '<input type="hidden" name="account_id[]" value="' .$attendee['account_id']. '" />';
						if($key > 0){
							$html .= '<a onclick="deleteAttendee(this);" class="delete">x Remove Attendee</a>';
						}
					$html .= '</fieldset>
				</div>';
			}

			$html .= '<div class="form-buttons right">
				' .(is_null($event['max_attendees']) || $event['max_attendees'] > 1 ? '<a onclick="addAttendee();" class="addnew button primary black">+ Add Another Attendee<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>' : ''). '
				<button type="submit" name="continue" value="continue" class="button solid primary red">Continue<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
				<a href="' .$_sitepages['events']['page_url'].$event['page'].'-'.$event['occurrence_id']. '/" class="previous f_left button primary black">Cancel<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
			</div>

			<input type="hidden" name="event_id" value="' .$event['event_id']. '" />
			<input type="hidden" name="occurrence_id" value="' .$event['occurrence_id']. '" />
			<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
		</form>';
		
	}

//Event not found
}else{
	if(isset($_POST['tournament_id'])){
		$html .= $Account->alert('Selected tournament was not found. <a href="' .$_sitepages['tournaments']['page_url']. '">Browse Tournaments &rsaquo;</a>', false);
	}else{
		$html .= $Account->alert('Selected event was not found. <a href="' .$_sitepages['events']['page_url']. '">Browse Events &rsaquo;</a>', false);
	}
}

//Set panel content
$page['page_panels'][$panel_id]['content'] .= $html;

?>