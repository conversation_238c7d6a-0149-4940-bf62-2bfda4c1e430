@charset "utf-8";
/*
	base.css
	Project: Honeycomb CMS v4.0
*/


/*------ imports ------*/
@import "definitions.less";
@import "../../../core/less/mixins.less";
@import "forms.less";
@import "regstyles.less";


/*------ reset ------*/
* {margin: 0; box-sizing: border-box;}

body, html{
	width: 100%;
	height: 100%;
	background: @color-lightest;
}

article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section{
	display: block;
}

img{
	border: 0;
	-ms-interpolation-mode: bicubic;
}

p{
	padding: 0 0 20px 0;
	margin: 0;
}

ul, ol{
	padding: 0 0 20px 0;
	margin: 0 0 0 40px;
}

hr{
	border: 0;
	border-top: 1px solid @color-light;
	height: 0px;
	background: @color-white;
	padding: 0;
	margin: 0 0 20px 0;
}


/*------ typography ------*/
body, tr, td{
	font-family: @font-base;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.4em;
	color: @color-darkest;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	.letter-spacing(-20);
}

a{
	text-decoration: none;
	cursor: pointer;
	outline: none;
	color: @color-theme1;
	.trans(color);
	.trans(background-color);

	&:hover, &:focus{
		color: @color-theme2;
	}
}

h1, h2, h3, h4, h5, h6{
	font-family: @font-alt;
	padding: 0;
	margin: 0;
	.medium;
}
h1{
	font-size: 24px;
	line-height: 1.2;
}
h2{
	font-size: 18px;
	margin-bottom: 20px;
	font-weight: 600;
}
h3{
	font-size: 16px;
	margin-bottom: 15px;
}
h4{
	font-size: 14px;
	margin-bottom: 10px;
	letter-spacing: 0;
}
h5{
	font-size: 13px;
	margin-bottom: 5px;
	letter-spacing: 0;
}
h6{
	font-size: 12px;
	margin-bottom: 0px;
	letter-spacing: 0;
}

small{
	font-size: 80%;
	color: @color-theme3;
}

.color-theme1{color: @color-theme1;}
.color-theme2{color: @color-theme2;}
.color-theme3{color: @color-theme3;}
.color-grey{color: @color-medium;}
.color-darkest{color: @color-darkest;}
.color-error{color: @color-error;}

.fas{font-weight: 900 !important;}
.far{font-weight: 400 !important;}
.fab{font-weight: 400 !important;}

.medium{font-weight: 500;}
.extrabold{font-weight: 800;}
.bolder{font-weight: 900;}

.text-underlined{text-decoration: underline;}
.text-caps{text-transform: uppercase;}

.truncate{
	display: block;
	vertical-align: middle;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	width: 100%;

	&.rtl{
		direction: rtl;
	}
}


/*------ cms mixins ------*/
.flex-container{
	.flexbox();
	gap: 10px;
	width: 100%;

	&.no-wrap{flex-wrap: nowrap; }
	&.align-start{align-items: flex-start; }
	&.align-center{align-items: center; }
	&.align-end{align-items: flex-end; }
	&.justify-start{justify-content: flex-start; }
	&.justify-center{justify-content: center; }
	&.justify-space{justify-content: space-between; }
	&.justify-end{justify-content: flex-end; }

	.flex-column{
		max-width: 100%;

		&.no-shrink{flex-shrink: 0; }
		&.grow{flex-grow: 1; }
		&.align-start{align-self: flex-start; }
		&.align-center{align-self: center; }
		&.align-end{align-self: flex-end; }
		&.center{margin-right: auto; }
		&.center,
		&.right{margin-left: auto; }
	}
}

.input-button{
	.flexbox(row nowrap);
	flex-wrap: nowrap;
	width: 260px;
	max-width: 100%;

	.input{
		display: block;
		margin: 0;
		height: auto;
		.flex(1 1 auto);
	}

	.button-sm,
	.button{
		margin: 0;
		padding: 0;
		text-align: center;
		.flex(0 0 auto);
	}

	.button{width: 50px; }
	.button-sm{width: 40px; }

	// Default 'search' icon
	.button::empty::before,
	.button-sm::empty::before{.font-awesome(f002); }

	&.large{width: 320px; }
}

.gravatar-link{
	.relative;
	display: block;

	.tooltip{
		.absolute;
		top: 0;
		bottom: 0;
		left: 0 !important;
		right: 0;
		width: 40px !important;
		height: 40px !important;
		margin: auto;
		padding: 0;
		background: fade(darken(@color-error, 5%), 75%);
		color: @color-white;
		text-align: center;
		line-height: 40px;
	}
}
.gravatar{
	display: block;
	width: 40px;
	height: 40px;
	border: 1px solid @color-light;
	overflow: hidden;
}


/*------ interface ------*/
#cms-wrapper{
	.relative;
	width: 100%;
	min-height: 100%;
	box-sizing: border-box;
	padding-left: 20px;
}

#section-title{
	.flexbox(row nowrap; space-between; center;);
	.relative;
	.trans(left, 0.3s, ease-out);
	z-index: 100;
	top: 0;
	left: 0;
	right: 0;
	background: @color-white;
	padding: 0 20px;
	margin: 0 0 30px -20px;
	height: 85px;
	width: ~"calc(100% + 40px)";
	box-sizing: border-box;

	h1{
		.flex(1 1 auto);
		padding-left: 10px;

		i{padding-right: 15px; }
		small{font-size: 60%; }
	}
	.back-to-site{
		.flex(0 1 auto);
		display: block;
		white-space: nowrap;
		padding-left: 15px;

		i{padding-left: 5px;}
	}
}

#cms-content{
	.relative;
	z-index: 1;
	padding: 0 20px;
}

#dashboard-overview{
	.flexbox(@cross:flex-start);
	.relative;
	width: ~"calc(100% + 20px)";
	left: -10px;

	.flex-column{
		margin-left: 10px;
		margin-right: 10px;
	}
}

.cms-overview{
	width: 100%;
	max-width: 400px;

	table a{color: @color-darkest;
		&:hover{color: @color-theme1;}

		i{
			width: 18px;
			text-align: center;
		}
	}
}

.dashboard-box{
	width: 200px;
	height: 170px;
	margin: 0 20px 20px 0;
	padding: 20px 0 0 0;
	background: @color-white;
	.box-shadow(@blur:10px; @color:rgba(0,0,0,0.2););
}

.cms-overview{
	width: 100%;
	max-width: 400px;

	table a{color: @color-darkest;
		&:hover{color: @color-theme1;}

		i{
			width: 18px;
			text-align: center;
		}
	}
}


.panel{
	display: block;
	clear: both;
	margin-bottom: 20px;
	background: @color-white;
	.box-shadow(@blur:10px; @color:rgba(0,0,0,0.2););

	&.f_left, &.f_right{clear: none;} //LEGACY
	&.f_left {margin-right: 20px;} //LEGACY
	&.f_right {margin-left: 20px;} //LEGACY
}

.panel-header{
	.relative;
	padding: 15px 50px 15px 20px;
	line-height: 30px;
	background: @color-darkest;
	color: @color-white;
	font-family: @font-alt;
	font-size: 16px;
	.extrabold;
	.text-caps;

	&.required{color: @color-error;}

	form{line-height:40px;
		.input, .select{text-transform: none;}
	}

	.panel-toggle{
		.absolute;
		.translateY(-50%);
		top: 50%;
		right: 20px;
		color: @color-white;
		cursor: pointer;
		.trans(color);

		&:hover{color: @color-theme1;}
	}

	.panel-switch{
		.flexbox();
		margin: 0;
		float: none;
		clear: both;
		line-height: 10px;

		label:first-child{
			padding: 0 10px 0 0;
			line-height: 30px;
			height: auto;
			margin: 0;
		}
	}
}

.panel-content{
	padding: 15px 15px 5px;
	overflow: hidden;
	overflow-x: auto;

	&.collapsed{display: none;}

	p:only-child{padding-bottom: 10px;}

	//
	.privacy-settings-container {
		// No specific styles needed for the main container unless you want padding/margin

		.privacy-setting-row {
			display: flex;
			align-items: center; // Vertically align label and radio group
			// justify-content: space-between; // Push label to left, radios to right
			padding: 12px 15px; // Padding for each row
			// border-bottom: 1px solid @color-gray-lighter;
			// background-color: @color-light; // White background for row content

			&:nth-child(odd) { // Alternate row background color like image
				background-color: @color-lightest;
			}
			&:last-child {
				border-bottom: none;
			}

			.privacy-label {
				width:125px;
				// flex-grow: 0.1; // Label takes available space
				color: @color-dark;
				font-weight: normal; // Or bold if you prefer
			}

			.privacy-radio-group {
				display: flex;
				align-items: center;
				gap: 15px; // Space between radio button options

				input[type="radio"] {
					margin-right: 5px;
					// Optionally, style custom radio buttons if you're not using browser defaults
					// Standard browser radios will pick up OS theme.
					// Custom styling for radio buttons is more involved.
					// For a quick green check like image, you might need custom CSS radio buttons.
					// Example for accenting the checked one (very basic):
					// &:checked + label {
					//    color: @color-theme3; // Greenish text for label of checked radio
					//    font-weight: bold;
					// }
				}

				label {
					color: @color-dark;
					font-weight: normal;
					cursor: pointer;
					margin-right: 10px; // Space after the label text before next radio
					&:last-child {
						margin-right: 0;
					}
				}
			}
		}
	}
	//

	.history-label-cell{
		width:200px;
	}
}

#cms-footer{
	.absolute;
	z-index: 900;
	background: @color-white;
	left: 0;
	right: 0;
	padding: 10px 20px;
	text-align: left;

	.flex-container{
		.flexbox(@main:space-between);
	}

	.flex-column{width: 100%;

		&.left{order: 2; min-width: 175px;}
		&.right{order: 1;}
	}

	.delete{margin-right: 15px;}

	.button, .cancel{
		margin-top: 5px;
		margin-bottom: 5px;
	}
}

@media @tablet-p{
	#cms-footer{
		.flex-container{
			.flexbox(row nowrap, space-between);
		}
		.flex-column{width: auto;

			&.left{order: 1;}
			&.right{order: 2;}
		}
	}
}

@media @tablet-l{
	#cms-wrapper{
		padding-left: 85px;
	}

	#section-title{
		position: fixed;
		z-index: 1000;
		padding: 0 30px;
		width: auto;
		left: 85px;
		right: 0;
		margin: 0;

		h1{padding: 0;}
	}

	#cms-content{
		padding: 115px 30px 95px;
		overflow: hidden;
	}

	.panel-header{
		.flexbox(row wrap, flex-start, stretch);
		gap: 5px;

		.panel-switch{margin-left: auto;}
	}

	.panel-content{
		padding: 20px 20px 10px;
	}

	#cms-footer{
		position: fixed;
		left: 85px;
		right: 0;
		bottom: 0;
		width: auto;
		padding: 15px 30px;
	}
}

@media @notebook{
	#cms-content{
		padding: 125px 30px 105px;
	}
}


/*------ navigation ------*/
#cms-menu{
	display: block;
	position: fixed;
	z-index: 900;
	top: 0;
	bottom: 0;
	left: -280px; //closed menu
	width: 300px;
	background: @color-white;
	font-family: @font-alt;
	.medium;
	.trans(left, 0.3s, ease-out);

	#menu-header{
		.flexbox(row nowrap, flex-start, center);
		.absolute;
		z-index: 3;
		top: 0;
		left: 0;
		right: 0;
		height: 85px;
		padding-left: 75px;
		box-sizing: border-box;
		background: @color-darkest;

		a{
			.relative;
			z-index: 1;
			display: inline-block;
			margin: 0 20px;
			padding: 10px 0 10px 30px;
			color: @color-white;

			i{
				.absolute;
				.trans(color);
				top: 12px;
				left: 0;
				width: 18px;
				color: @color-theme3;
			}

			&:hover, &.active{
				color: @color-theme1;

				i{
					color: inherit;
				}
			}
		}

		#cms-title{
			.absolute;
			z-index: 2;
			top: 0;
			left: 0;
			width: 75px;
			height: 85px;
			padding: 15px 10px 0;
			box-sizing: border-box;
			background: @color-white;
			text-align: center;

			a{
				display: block;
				margin: 0;
				padding: 0;
				height: 70px;
				border-bottom: 1px solid @color-lightest;
				box-sizing: border-box;

				small{
					display: block;
					color: @color-medium;
					font-size: 9px;
					line-height: 26px;
					white-space: nowrap;
				}
			}
		}
	}

	nav#menu-icons{
		.absolute;
		z-index: 2;
		top: 85px;
		bottom: 0;
		left: 0;
		width: 75px;
		overflow: hidden;
		box-sizing: border-box;
		background: @color-white;

		> ul{
			.absolute;
			.flexbox(column nowrap);
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			margin: 0;
			padding: 24px 0 0;
			list-style: none;
			box-sizing: border-box;
			text-align: center;

			> li{
				display: block;
				padding: 0 10px 24px;

				a{
					display: block;
					overflow: hidden;
					padding: 0;
					color: @color-theme3;
					font-size: 9px;
					line-height: 1.4;
					text-overflow: ellipsis;
					.uppercase;

					i{
						display: block;
						margin-bottom: 8px;
						font-size: 20px;
					}

					.initials{
						.relative;
						display: block;
						width: 24px;
						height: 24px;
						margin: 0 auto 10px;

						span{
							.relative;
							display: block;
							z-index: 1;
							line-height: 26px;
							color: @color-white;
							text-indent: 1px;
							.letter-spacing(0);
							.black;
						}
					}

					.hexagon {
						.absolute;
						z-index: 0;
						display: block;
						top: 0;
						left: 0;
						width: 24px;
						height: 14px;
						background: @color-theme3;
						margin: 6px auto 12px;
						.trans(background);
						.rotate(-90deg);

						&:before,
						&:after{
							.absolute;
							.trans(border);
							content: '';
							width: 0;
							height: 0;
						}

						&:before{
							top: -6px;
							left: 0;
							border-left: 12px solid transparent;
							border-right: 12px solid transparent;
							border-bottom: 6px solid @color-theme3;
						}
						&:after{
							bottom: -6px;
							left: 0;
							border-left: 12px solid transparent;
							border-right: 12px solid transparent;
							border-top: 6px solid @color-theme3;
						}
					}

					&:hover, &.active{
						color: @color-theme1;

						.hexagon{
							background: @color-theme1;

							&:before{
								border-bottom: 6px solid @color-theme1;
							}
							&:after{
								border-top: 6px solid @color-theme1;
							}
						}
					}
				}

				ul{
					display: block;
					list-style: none;
					margin: 0;
					padding: 0;

					li{
						padding-top: 24px;
					}
				}

				//Logout
				&:last-child{
					margin-top: auto;

					a{
						padding-top: 24px;
						border-top: 1px solid @color-lightest;

						i{
							margin: 6px 0 0;
						}
					}
				}
			}
		}
	}

	nav#menu-list{
		.absolute;
		z-index: 1;
		top: 85px;
		bottom: 0;
		left: 75px;
		right: 0;
		overflow: hidden;
		background: @color-darkest;

		> ul{
			list-style: none;
			margin: 0;
			padding: 10px 0 40px;

			li{
				display: block;
				padding: 0 20px;

				a{
					.relative;
					display: inline-block;
					padding: 10px 0 10px 30px;
					color: @color-white;
					font-size: 14px;
					line-height: 1.2;

					i{
						.absolute;
						.trans(color);
						top: 10px;
						left: 0;
						width: 18px;
						text-align: center;
						color: @color-theme3;
					}

					&:hover, &.active, &.ui-state-active{
						color: @color-theme1;

						i{
							color: inherit;
						}
					}
				}

				ul{
					list-style: none;
					margin: 0 0 2px;
					padding: 0;

					li{
						padding: 0 0 0 8px;

						a{
							padding: 5px 0 5px 20px;
							border-left: 2px solid @color-theme3;
							box-sizing: border-box;
							font-size: 12px;
							color: @color-light;

							&:hover, &.active{
								color: @color-white;
							}
						}
					}
				}

				&.accordion:not(.expanded){
					ul{
						display: none;
					}
				}

				&.menu-header{

					h6{
						color: @color-theme3;
						margin-bottom: 10px;
						.letter-spacing(200);
						.extrabold;
						.uppercase;
					}

					&:not(:first-child){
						padding-top: 30px;
					}
				}

				&.expanded{
					> a{
						color: @color-theme1;

						i{
							color: inherit;
						}
					}
				}
			}
		}
	}
}

#menu-toggle{
	.absolute;
	.transform(translate(50%, -50%));
	.trans(background-color);
	display: block;
	z-index: 100;
	top: 50%;
	right: 0;
	width: 30px;
	height: 30px;
	margin: 0;
	padding: 8px;
	color: @color-white;
	font-size: 14px;
	text-align: center;
	line-height: 32px;
	background-color: @color-theme3;
	border-radius: 50%;
	box-sizing: border-box;
	line-height: 1;
	cursor: pointer;

	&:hover{background-color: @color-theme1;}
}

//Minified menu
.menu-simple{
	#cms-menu{
		width: 95px;
		left: -75px; //closed menu

		#menu-header{
			a:last-child{display: none;}
		}
	}

	#menu-toggle{
		right: 0;
	}
}

//Open menu
.menu-open{
	#cms-menu{left: 0;}
}

//Animations on open/close
.animated{
	#cms-content{
		.trans(margin, 0.3s, ease-out);
	}
	#menu-toggle{
		.trans(all);
	}
	#cms-footer{
		.trans(left, 0.3s, ease-out);
	}
}

//Steps nav
.steps-nav{
	display: block;
	margin-bottom: 20px;

	ul{
		.flexbox(row wrap, flex-start, center);
		list-style: none;
		margin: 0;
		padding: 0;

		li{
			padding: 0 1px 0 0;
			text-align: center;
			font-size: 16px;
			min-width: 120px;

			&:last-child{padding-right: 0;}

			a{
				.relative;
				display: block;
				padding: 15px 30px;
				box-sizing: border-box;
				background: @color-theme3;
				color: @color-white;
				border-top: 2px solid @color-lightest;
				border-bottom: 2px solid @color-lightest;
				.extrabold;
				.uppercase;

				small{
					display: block;
					color: @color-light;
				}

				&:hover, &.active{
					background: @color-darkest;

					small{color: @color-theme1;}
				}

				 &.active{
					border-color: @color-darkest;
				}

				&.disabled{
					background: lighten(@color-theme3, 35%);
					border-color: @color-lightest;
					color: @color-lightest;
					cursor: not-allowed;

					small{color: @color-lightest;}
				}
			}

		}
	}
}

@media @tablet-l{
	#cms-wrapper{
		padding-left: 85px;
	}

	#menu-toggle{
		right: 5px;
	}

	#cms-menu{
		left: -215px;

		#menu-header #cms-title{
			position: fixed;
		}

		nav#menu-icons{
			position: fixed;
		}
	}

	//Open Menu
	.menu-open{
		#menu-toggle{
			right: 0;
		}
	}

	//Minified
	.menu-simple{
		#cms-menu{
			width: 85px !important;
			left: 0;
		}
		#menu-toggle{
			display: none;
		}
	}
}

@media @notebook{

	//Open menu
	.menu-open:not(.menu-simple){
		#cms-content{
			margin-left: 215px;
		}
		#section-title, #cms-footer{
			left: 300px;
		}
	}
}


/*------ tables ------*/
table{
	.relative;
	width: 100%;

	tr{
		background: @color-white;
		&:nth-child(2n){background: @color-lightest;}

		&.first-child{
			.box-shadow-inset(0, 10px, 10px, -10px, rgba(0,0,0,0.50));
			&:nth-child(2n){.box-shadow-inset(0, 10px, 10px, -10px, rgba(0,0,0,0.35));}
		}
		&.last-child{
			.box-shadow-inset(0, -10px, 10px, -10px, rgba(0,0,0,0.50));
			&:nth-child(2n){.box-shadow-inset(0, -10px, 10px, -10px, rgba(0,0,0,0.35));}
		}

		&.first-child.last-child{
			.box-shadow-inset(0, 10px, 10px, -10px, rgba(0,0,0,0.50));
			.box-shadow-inset(0, -10px, 10px, -10px, rgba(0,0,0,0.50));
			&:nth-child(2n){
				.box-shadow-inset(0, 10px, 10px, -10px, rgba(0,0,0,0.35));
				.box-shadow-inset(0, -10px, 10px, -10px, rgba(0,0,0,0.35));
			}
		}

		&.bolder td,
		&.bold td{font-weight: inherit;}
	}

	th{
		background: lighten(@color-theme3, 35%);
		font-family: @font-alt;
		padding: 5px 20px;
		text-align: left;
		font-weight: 800;
		height: 60px;
		.text-caps;
		.extrabold;
	}

	td{
		padding: 10px 20px;
		.trans(background-color, 0.5s);
	}

	tr[data-level] td{padding: 15px 20px;}

	tr[data-level='1']:after{left: 0px;}
	tr[data-level='2']:after{left: 30px;}
	tr[data-level='3']:after{left: 60px;}
	tr[data-level='4']:after{left: 90px;}
	tr[data-level='5']:after{left: 120px;}

	&.sortable:not(.tablesorter), &.helper-sortable{
		tr[data-level] td.show-lvl:before{
			.font-awesome(f3bf);
			.rotate(@deg:90deg);
			display: inline-block;
			margin-right: 10px;
			font-size: 12px;
			color: @color-theme1;
		}

		tr[data-level='1'] td.show-lvl:before{content: ""; margin-right: 0;}
		tr[data-level='2'] td.show-lvl {padding-left: 25px;}
		tr[data-level='3'] td.show-lvl {padding-left: 50px;}
		tr[data-level='4'] td.show-lvl {padding-left: 75px;}
		tr[data-level='5'] td.show-lvl {padding-left: 100px;}
	}

	td.handle{color: @color-medium;}
	&.sortable.saveable{
		tr.higher td.handle,
		tr.lower td.handle{color: @color-theme1; }
	}


	.highlight{
		height: 70px;
		position: relative !important;

		&:after{
			.absolute;
			content: "";
			display: inline-block;
			height: inherit;
			left: 0;
			right: 0;
			box-sizing: border-box;
			border: 1px dashed @color-theme1;
			z-index: 9;
		}

		&:nth-child(n){background-color: @color-white;}
		~ tr:nth-child(2n + 1){background: #eee;}
		~ tr:nth-child(2n){background-color: @color-white;}

		td{display: none;}
	}

	.handle{.relative;
		&:hover{
			cursor: move;
			cursor: -webkit-grab;
			cursor: -moz-grab;
			cursor: grab;
		}
		&:active, .ui-sortable-helper{
			cursor: move;
			cursor: -webkit-grabbing;
			cursor: -moz-grabbing;
			cursor:grabbing;
		}
	}
}
.ui-sortable-helper{
	border: 1px solid lighten(@color-theme3, 35%);
	transform-origin: left;
	transition: transform 0.3s;
	opacity: 0.9;
	background: @color-white !important;

	tr[data-level] td{
		padding-top: 0 !important;
		padding-bottom: 0 !important;
	}
}

table, td, th{
	&.nowrap{white-space: nowrap;}
}

.leadin-field-options-table{padding: 10px 0;
	tr:nth-child(n){background: transparent;}
}


/*------ tablesorter ------*/
.tablesorter{
	.tablesorter-header{
		.relative;
		cursor: pointer;
		outline: none;
	}
	.sorter-false{cursor: default;}

	.tablesorter-headerAsc,
	.tablesorter-headerDesc{background: lighten(@color-theme3, 30%);}

	.tablesorter-headerAsc:after,
	.tablesorter-headerDesc:after{
		.absolute;
		font-size: 16px;
		top: 50%;
		right: 8px;
		height: 20px;
	}

	.tablesorter-headerAsc:after{
		.font-awesome(f0d8);
		margin-top: -10px;
	}
	.tablesorter-headerDesc:after{
		.font-awesome(f0d7);
		margin-top: -8px;
	}

	.show, .hide{
		display: block;
		text-indent: -9999px;
		height: 18px;

		&:after{
			display: block;
			text-align: center;
			font-size: 16px;
			text-indent: 0;
			margin-top: -16px;
		}
	}
	.show:after{
		.font-awesome(f00c);
		color: @color-success;
	}
	.hide:after{
		.font-awesome(f00d);
		color: @color-darkest;
	}
}

.pager{
	display: block;
	position: relative !important;
	top: auto !important;
	padding: 10px 20px;
	background: @color-white;
	border-top: 1px solid @color-lightest;
	text-align: center;

	.pagebuttons .button-sm{
		width: 40px;
		text-align: center;
		margin: 0 1px;
		border: 0;
		padding: 0;

		&.hoverable{
			outline-offset: -3px;
			outline: 3px dashed @color-theme1;
			.trans(background-color);
		}
		&.dragged-over{
			background-color: @color-theme1;
			outline-color: @color-theme2;
		}
		&.disabled{
			background: @color-lightest !important;
			color: @color-white !important;
			cursor: default;
			outline: 0;
			text-decoration: none;
		}
	}

	.pagedisplay{
		display: block;
		padding: 0 0 5px;
	}
	.pagebuttons{
		.flexbox(row nowrap, center, center);
		width: 240px;
		margin: 0 auto;
	}
	.gotoPage{
		width: 70px;
		height: 40px;
		margin: 0 1px;
	}
}

.tablesorter-sticky-visible{
	visibility: hidden !important;
}

.tablesorter-save-order{
	display: none;
	&:extend(#cms-footer all);
	.flex-column{
		&:not(.right){.flexbox(row nowrap; flex-start; center;);}
		&.right{.flexbox(column nowrap; center; flex-end;);}
	}
}

@media @tablet-l{
	.tablesorter-sticky-visible{
		visibility: visible !important;
	}

	.tablesorter-save-order{
		.flex-column.right{.flexbox(row nowrap; flex-start; center;);
			.button{
				margin-left: 10px;
			}
		}
	}
}


/*------ charts ------*/

.panel-content.chart{
	display: block !important;
	min-height: 68px !important; //prevent panel from closing fully
	padding: 0;
	overflow: hidden;

	table.chart-stats{
		width: 100%;
		height: 68px;
		table-layout: fixed;

		th:not(:first-child){border-left: 1px solid @color-theme3; }

		b{
			display: block;
			color: @color-white;
			font-size: 18px;
		}
	}

	&.closed :not(.chart-stats){display: none; }
}

.chart-container{
	position: relative;
	width: 100%;
	margin-bottom: 30px;
	padding: 20px 20px 0 20px;

	canvas{
		display: block;
		width: 100%;
		height: 200px;
	}
}


/*------ sitemap ------*/
#site-pages{
	.page-name{
		i{
			margin-right: 5px;
			color: @color-medium;
			vertical-align: middle;
		}
	}
}

#sitemap-reference{padding: 0;
	li{margin-top: 5px;}

	.sitemap-button{font-size: 14px; margin: 0;
		i{margin-right: 0;}
	}
}

.sitemap-pages{
	tr:not(.level-1){
		.page-name{
			&:before{
				.font-awesome(f3bf);
			    .rotate(90deg);
			    display: inline-block;
			    margin-right: 10px;
			    font-size: 12px;
			    color: @color-theme1;
			}
		}
	}

	tr.level-2 .page-name{padding-left: 30px;}
	tr.level-3 .page-name{padding-left: 50px;}
	tr.level-4 .page-name{padding-left: 70px;}
	tr.level-5 .page-name{padding-left: 90px;}

	tr td:last-child{
		padding-right: 10px;
		padding-left: 0;
		width: 100px;
	}
}


/*------ toggle switch ------*/
.onoffswitch{
	display: inline-block;
	width: 80px;
	background-color: @color-white;
	overflow: hidden;
	user-select: none;

	input{.sr-only(); }

	label{
		display: block;
		width: 150%;
		height: auto;
		margin: 0;
		cursor: pointer;
		.flexbox(row nowrap, space-between, stretch);
		.trans(background-color, 0.2s, ease-in);
		.trans(transform, 0.2s, ease-in);

		.inner,
		&::before,
		&::after{
			text-align: center;
			width: 40px;
			height: 30px;
			padding: 0;
			font-size: 14px;
			line-height: 30px;
			color: @color-white;
			.trans(transform, 0.3s);
		}

		&::before{
			background-color: @color-theme1;
			.font-awesome(f00c);
		}

		&::after{
			background-color: @color-error;
			.font-awesome(f00d);
		}

		.inner{
			background-color: @color-theme3;
			opacity: 0;
			.trans(opacity, 0.15s);

			&::before{.font-awesome(f068); }
		}
	}

	:not(:checked) + label{.translateX(-33.33%); }

	:indeterminate + label,
	[indeterminate] + label{
		.translateX(-16.665%);

		&::before{.translateX(-100%); }
		&::after{.translateX(100%); }
		.inner{opacity: 1; }
	}
}

.switch-sorter{display: none; }

table{
	tr:nth-child(odd) td .onoffswitch{background-color: @color-lightest; }

	td, th {
		.onoffswitch,
		.item-status{display: block; }

		&.center {
			.onoffswitch,
			.item-status{margin: auto;}
		}

		&.right {
			.onoffswitch,
			.item-status{margin-left: auto;}
		}
	}
}


/*------ page status buttons ------*/
.item-status{
	.relative;
	display: inline-block;
	width: 125px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;

	button{
		width: 40px;
		margin: 0 1px 0 0 !important;
		height: 30px;
		padding: 0;
		font-size: 16px;
		font-weight: normal;
		box-sizing: border-box;
		background-clip: padding-box;

		i{
			width: 40px;
			height: 30px;
			line-height: 30px;
			color: @color-dark;
			background: @color-lightest;
		}

		&:hover{
			i{
				color: @color-white;
				background: darken(@color-theme1, 10%);
			}
		}
		&.active{
			i{
				color: @color-white;
				background: @color-theme1;
			}
		}
	}
}

table tr:nth-child(2n), .panel-header{
	.item-status button{
		i{
			background: @color-white;
		}

		&:hover{
			i{
				color: @color-white;
				background: darken(@color-theme1, 10%);
			}
		}
		&.active{
			i{
				color: @color-white;
				background: @color-theme1;
			}
		}
	}
}


/*------ jquery ui helpers ------*/
.ui-helper-hidden{display: none;}
.ui-helper-hidden-accessible{
	.absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	border: 0;
	overflow: hidden;
	clip:rect(0 0 0 0);
}
.ui-helper-clearfix:before, .ui-helper-clearfix:after{
	content: "";
	display: table;
}
.ui-helper-clearfix:after{clear: both;}
.ui-helper-clearfix{zoom: 1;}
.ui-widget-overlay, .ui-widget-overlay.ui-front{
	position: fixed;
	z-index: 9999;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: #000;
	opacity: 0.5;
}


/*------ tabs ------*/
.tabs.page-content{display: none;}
.tabs{
	display: block !important;
	margin-bottom: 20px;

	> ul{
		.relative;
		list-style:none;
		margin: 0;
		padding: 0 0 0 1px;
		width: 100%;
		z-index: 1;

		> li{
			.relative;
			display: inline-block;
			margin-right: 2px;

			a{
				display: block;
				z-index: 1;
				padding: 15px 20px;
				background: @color-light;
				color: @color-dark;
				font-weight: 800;
				line-height: 30px;
				margin:0 0 0 -1px;
				outline: none;
				text-decoration: none;
				.text-caps;
				.box-shadow-inset(0,-5px,10px,-5px);

				&:hover{background: lighten(@color-theme1, 40%);}
			}
		}

		li:not(.ui-tabs-tab):first-child a,
		li.ui-state-active a:hover,
		li.ui-state-active a{
			background: @color-white;
			color: @color-darkest;
			z-index: 2;
			.no-shadow();
		}

		li:not(.ui-tabs-tab):first-child::before,
		li.ui-state-active::before{
			.absolute;
			content: "";
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: -1;
			.box-shadow(@blur:10px; @color:rgba(0,0,0,0.2););
			clip-path: inset(-10px -10px 0 -10px);
		}


		~ .ui-tabs-panel.nopadding > .mce-tinymce.mce-container{margin-left: -1px; } // first tinymce panel has negative margin
		~ .ui-tabs-panel ~ .ui-tabs-panel > .mce-tinymce.mce-container{margin-left: 0px; }
	}

	> :not(ul:first-child){
		.relative;
		display: block;
		clear: both;
		padding: 20px 20px 10px;
		background: @color-white;

		&:before{
			.absolute;
			content: "";
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: -1;
			.box-shadow(@blur:10px; @color:rgba(0,0,0,0.2););
		}
		~ *{display: none; }
	}
}


/*------ dialog box ------*/
.ui-dialog{
	.absolute;
	max-height: ~"calc(100% - 40px)";
	z-index: 10000;
	top: 0;
	left: 0;
	padding: 0;
	overflow: hidden;
	outline: none !important;
	background: @color-white;
	color: @color-darkest;
	border: 0;
	.box-shadow(@blur:10px;);

	.ui-dialog-titlebar{
		.relative;
		padding: 20px;
		background: @color-theme1;
		font-family: @font-alt;
		color: @color-white;
		font-size: 18px;
		.extrabold;
	}
	&.dialog-error .ui-dialog-titlebar{background: @color-error;}

	.ui-dialog-title{
		float: left;
		margin: 0 25px 0 0;
		.text-caps;
	}
	.ui-dialog-titlebar-close{
		.absolute;
		right: 10px;
		top: 50%;
		width: 20px;
		margin: -8px 4px 0 0;
		padding: 0;
		height: 20px;
		background: none;
		color: @color-white;
		text-align: center;
		text-decoration: none;
		border: 0;
		font-size: 0;
		cursor: pointer;
		.bold;

		&:hover, &:focus{
			background: none;
			color: @color-white;
		}

		span{display: block;}

		.ui-icon-closethick{
			display: block;
			background: none;
			outline: none;

			&:before{
				.font-awesome(f00d);
				font-size: 20px;
				font-style: normal;
				text-decoration: inherit;
			}
		}
	}

	ul, ol{margin: 0 0 0 20px;
		&:last-child{
			padding: 0;
		}
		ul, ol{
			padding: 5px 0;
		}
	}

	.ui-dialog-content{
		.relative;
		max-height: 300px !important;
		border: 0;
		padding: 20px;
		background: none;
		overflow: auto;
		zoom: 1;
	}

	@media @tablet-l{
		.ui-dialog .ui-dialog-content{max-height: 400px !important;}
	}

	@media @notebook{
		.ui-dialog .ui-dialog-content{max-height: 500px !important;}
	}

	.ui-dialog-buttonpane{
		text-align: left;
		border-width: 1px 0 0 0;
		background-image: none;
		margin: 0;
		padding: 0 10px 10px;

		.ui-dialog-buttonset{
			clear: both;
			.flexbox(@main:space-between);
		}

		.ui-button{
			margin: 0 10px 5px 10px;
			cursor: pointer;
			background: @color-dark;
			color: @color-white;
			outline: none !important;
			border:0;
			font-size: 12px;
			text-transform: none;
			padding: 5px 10px;
			font-weight: 600;
			.trans(all);

			i{
				margin-right: 5px;
			}

			&:hover{
				background: @color-darkest;
				color: @color-white;
			}

			.ui-resizable-se{
				width: 14px;
				height: 14px;
				right: 3px;
				bottom: 3px;
			}
		}
	}
}


/*------ tooltip ------*/
.tooltip{
	font-weight: 800;
	color: @color-theme1;
	cursor: pointer;
	padding-left: 5px;
	font-family: @font-base;
}
.handle .tooltip {
	.absolute;
	top: 0;
	left: -5px;
	bottom: 0;
}
.ui-tooltip{
	.absolute;
	display: none;
	background: @color-darkest;
	font-size: 12px;
	width: 210px;
	padding: 20px;
	color: @color-white;
	z-index: 99;
	line-height: 18px;
	opacity: 0.9;
	word-break: break-word;
	.box-shadow(0,0,20px);

	h4{color: @color-theme1;
		&:only-child{margin: 0;}
	}

	p{
		padding-bottom: 15px;
		&:only-child{padding: 0;}
	}

	small{
		color: @color-lightest;
		font-size: 85%;
		line-height: 1.3;
		display: inline-block;
	}
}


/*------ seo styles ------*/
.google-preview{
	overflow: hidden;
	min-width: 240px;

	div{
		font-family: Arial, Helvetica, sans-serif;
		color: #545454;
		font-size: 13px;
		line-height: 18px;

		h2{
			font-family: inherit;
			font-size: 18px;
			font-weight: normal;
			color: #1a0dab;
			margin: 0 0 3px;
			padding: 0;
		}

		h6{
			font-family: inherit;
			margin-bottom: 6px;
			font-size: 14px;
			color: #006621;
		}
	}
}

.seo-pass{color: @color-success !important;}
.seo-fail{color: @color-error !important;}
.seo-average{color: @color-alert !important;}


tr[class^="seo-"]{
	> td:first-child{
		.relative;
		border-left: 5px solid lighten(#ff1a1a, 15%);

		.tooltip {
			.absolute;
			display: block;
			top: 0;
			left: -5px;
			width: 5px;
			height: 100%;
		}
	}
}
tr{
	&.seo-1{
		> td:first-child{
			border-left: 5px solid @color-error;
		}
	}
	&.seo-2{
		> td:first-child{
			border-left: 5px solid @color-alert;
		}
	}
	&.seo-3{
		> td:first-child{
			border-left: 5px solid @color-success;
		}
	}
}

.graph, .progress-bar{
	width: 150px;
	margin-right: 20px;

	p{
		color: @color-darkest;
		.extrabold;
		.text-caps;

		small{
			display: block;
			font-size: 14px;
			.medium;
		}
	}
}

.progress-bar{
	width: 400px;
	height: auto;
	max-width: 100%;
	min-height: 130px;
	font-size: 16px;
	padding: 20px;
	box-sizing: border-box;
	font-family: @font-alt;

	.percentage{
		.flexbox(row nowrap, flex-start, center);

		.numbers{
			margin-right: 10px;
			font-size: 60px;
			font-family: @font-alt;
			line-height: 1;
			.bolder;
		}

		p{
			padding: 0;
		}
	}

	.percent-progress{
		.relative;
		display: block;
		width: 100%;
		height: 10px;
		overflow: hidden;
		margin-bottom: 20px;
		background: @color-lightest;

		&:before{
			.absolute;
			content: '';
			width: var(--percent);
			height: 10px;

			.animation(progress-bar, 0.8s);
		}

		&.seo-pass:before{background-color: @color-success;}
		&.seo-average:before{background-color: darken(@color-alert,6%);}
		&.seo-fail:before{background-color: darken(@color-error,10%);}
	}
}

#seo-summary{
	max-width: 750px;
	padding-left: 30px;

	.progress-bar{width: 450px; }
}

.seo-summary{
	--theme: @color-error;
	overflow: hidden; // child margins dont expand past box boundries
	width: 100%;
	margin-bottom: 20px;
	padding-bottom: 20px;
	border-bottom: 1px solid @color-light;

	&.warning{--theme: @color-alert; }
	&.passed{--theme: @color-success; }

	&:last-child{
		border: 0;
	}

	.summary-title{
		.flexbox(row nowrap; flex-start; center);
		gap: 10px;
		cursor: pointer;

		&::before{
			width: 1em;
			height: 1em;
			background-color: var(--theme);
			border-radius: 50%;
			content: '';
			.flex(0 0 auto);
		}

		&::after{
			.font-awesome(f077);
			color: @color-theme2;
			margin-left: auto;

			&:hover{
				color: @color-theme1;
			}
		}

		&.ui-accordion-header-collapsed::after{
			.rotate(180deg);
		}
	}

	ul{
		list-style-type: none;
		margin: 10px 0 0 6px;
		border-left: 2px solid var(--theme);
		padding: 0 0 0 16px;
	}

	li{
		font-size: 12px;
		// margin: 0 0 10px;
		padding: 5px 0;
		border-top: 1px solid @color-lightest;

		&:first-child{
			// margin-top: 10px;
			// padding-top: 10px;
			border: 0;
		}
	}

	.ui-accordion-header-icon{display: none;}
}


/*------ animations ------*/
.keyframes(progress-bar, {
	0% 		{.translateX(-100%);}
	100% 	{.translateX(0);}
});

.keyframes(come-in-top, {
	0%{
		opacity:0;
		top:10px;
	}
	100%{
		opacity:0.9;
		top:0;
	}
});

.come-in-top{
	.animation(come-in-top, 0.8s, ease);
}

.keyframes(bounce-out-top, {
	0%{
		opacity:0.9;
		top:0;
	}
	40%{
		opacity:0.5;
		top:20px;
	}
	100%{
		opacity:0;
		top:-50px;
	}
});

.bounce-out-top{
	.animation(bounce-out-top, 0.8s, ease);
}

// --- CMS User Form - Social Networking Styles ---

// Assumed Global Variables from your previous LESS
// @color-light: #FFFFFF;
// @color-dark: #000000;
// @color-gray-light: #CCCCCC; // For input borders
// @color-gray-dark: #666666;  // For placeholder text
// @color-theme3: #B3C6BB; // Greenish color from image for icon background

// .social-networking-flex-container {
    // Uses .flex-container rules already defined in your CMS for general layout.
    // You might want to adjust how items wrap or their basis if needed:
    // For example, to make them take up ~50% width each to get two per row:
    // .form-field {
        // flex-basis: calc(50% - 10px); // Adjust 10px for gap between them
        // On smaller screens, they will wrap due to flex-container's flex-wrap: wrap;
        // Or force full width on mobile:
        // @media (max-width: 767px) { // Example breakpoint
        //    flex-basis: 100%;
        // }
    // }
// }

.input-with-icon {
    display: flex; // Align icon and input horizontally
    align-items: stretch; // Make icon and input same height

    .input-icon {
        display: flex;
        align-items: center; // Vertically center the icon inside its span
        justify-content: center; // Horizontally center the icon
        padding: 0 12px; // Padding for the icon background
        background-color: @color-theme3; // Greenish background for icon
        color: @color-light; // White icon color
        border: 1px solid darken(@color-theme3, 10%); // Slightly darker border for the icon box
        border-right: none; // Remove right border to merge with input
        border-radius: 4px 0 0 4px; // Rounded corners on the left side

        i { // Target Font Awesome icon
            font-size: 1.1em; // Adjust icon size as needed
        }
    }

    .input { // Targeting the input field next to the icon
        flex-grow: 1; // Input takes remaining space
        border-left: none; // Remove left border to merge with icon box
        border-radius: 0 4px 4px 0; // Rounded corners on the right side
        padding-left: 12px; // Add some padding so text isn't right against the icon box edge

        // Inherit other .input styles from your global CMS styles
        // e.g., border-color, height, padding-top, padding-bottom
    }

	input{
		margin-bottom:0;
	}
}


.input.readonly {
	border-color:@color-white;
	padding-left:0px;
}
