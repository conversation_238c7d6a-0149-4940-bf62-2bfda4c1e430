// Created by <PERSON>
// <EMAIL>
// v2.1.0
// Modified to work with FontAwesome 5.5
tinymce.PluginManager.requireLangPack('fontawesome');
tinymce.PluginManager.add('fontawesome', function(editor, url) {

    var translate = tinymce.util.I18n.translate;
    var iconList;
    var iconListGroups = [];
    var listGroups = {};

    function defineIconLibrary(callback){
        //get icons
        $.ajax({
            url: url + "/metadata/icons.json",
            dataType: "json",
        }).done(function(icon_result){
            iconList = icon_result;

            $.ajax({
                url: url + "/metadata/categories.json",
                dataType: "json",
            }).done(function(category_result){
                $.each(category_result, function(category_key, category_data){
                    iconListGroups[category_key] = [];

                    for(var i = 0; i < category_data.icons.length; i++){
                        if(iconList[category_data.icons[i]] != undefined){
                            var icon_data = iconList[category_data.icons[i]];
                            var icon_prefix = 'fa';
                            if(icon_data.styles != undefined){
                                if(icon_data.styles.indexOf('regular') >= 0){
                                    icon_prefix = 'far';
                                }else if(icon_data.styles.indexOf('solid') >= 0){
                                    icon_prefix = 'fas';
                                }else if(icon_data.styles.indexOf('brands') >= 0){
                                    icon_prefix = 'fab';
                                }
                            }

                            var new_icon = {
                                id: category_data.icons[i],
                                name: icon_data.label,
                                unicode: icon_data.unicode,
                                created: (icon_data.changes[0] != undefined ? icon_data.changes[0] : ''),
                                filter: icon_data.search.terms,
                                categories: [category_key],
                                prefix: icon_prefix
                            };

                            iconListGroups[category_key].push(new_icon);
                        }
                    }

                    listGroups[category_key] = category_data.label;
                });

                if(typeof callback == 'function'){
                    callback();
                }
            });
        });
    }

    function showDialog() {

        function groupHtml(group, iconTitle) {
            var iconGroup = iconListGroups[group];
            var gridHtml;
            var id;
            var prefix;

            gridHtml = '<div class="mce-fontawesome-panel-accordion">';
                gridHtml += '<div class="mce-fontawesome-panel-title">' + iconTitle + '<span class="mce-fontawesome-panel-accordion-indicator fas fa-chevron-right"></span></div>';
                gridHtml += '<div class="mce-fontawesome-panel-content">';

                for (var y = 0; y < (iconGroup.length / width); y++) {
                    for (var x = 0; x < width; x++) {
                        if (iconGroup[y * width + x]) {
                            id = iconGroup[y * width + x].id;
                            name = iconGroup[y * width + x].name;
                            prefix = iconGroup[y * width + x].prefix;
                            gridHtml += '<div class="mce-icon-cell js-mce-fontawesome-insert" title="' + name + '" data-id="' + id + '" data-prefix="' + prefix + '"' + (group === 'spinnerIcons' ? ' data-spin="true"' : '' ) + '>';
                                gridHtml += '<i class="'+prefix+' fa-' + id + (group === 'spinnerIcons' ? ' fa-spin' : '' ) +'"></i>';
                            gridHtml += '</div>';
                        }
                    }
                }
                gridHtml += '</div>';
            gridHtml += '</div>';

            return gridHtml;
        }

        var win;
        var width = 23;
        var panelHtml = '';
        var iconsHtml = '';
        var iconInserts;
        var accordionItems;
        var accordionTitle;
        var accordionContent;

        //create window
        panelHtml += '<div class="mce-fontawesome-groups"></div>';
        panelHtml += '<p class="mce-fontawesome-search-noresults" style="display: none;">' + translate('No icons matched your search') + '.</p>';
        win = editor.windowManager.open({
            autoScroll: true,
            width: 690,
            height: 500,
            title: translate('Icons'),
            spacing: 20,
            padding: 10,
            id: 'fontawesome-picker',
            classes: 'fontawesome-panel',
            items: [
                {
                    type: 'container',
                    html: panelHtml
                }
            ],
            buttons: [{
                text: translate('Close'),
                onclick: function() {
                    win.close();
                }
            }]
        });

        //add groups of icons to window
        if($.isEmptyObject(iconListGroups)){
            defineIconLibrary(function(){
                displayIconLibrary();
            });
        }else{
            displayIconLibrary();
        }

        function displayIconLibrary(){
            $.each(listGroups, function(cat_id, cat_label){
                iconsHtml += groupHtml(cat_id, cat_label);
            });
            document.querySelectorAll('.mce-fontawesome-groups')[0].innerHTML = iconsHtml;

            // Bind icon event listener
            iconInserts = document.querySelectorAll('.js-mce-fontawesome-insert');
            for (var i = 0; i < iconInserts.length; i++) {
                iconInserts[i].addEventListener('click', insertIcon);
            }

            // Accordion
            accordionItems = document.querySelectorAll('.mce-fontawesome-panel-accordion');
            for (i = 0; i < accordionItems.length; i++) {
                accordionTitle = accordionItems[i].querySelector('.mce-fontawesome-panel-title');
                accordionTitle.addEventListener('click', toggleItem);

                accordionContent = accordionItems[i].querySelector('.mce-fontawesome-panel-content');
                accordionContent.style.height = '0';
            }

            // Open first item
            var firstAccordion = document.querySelector('.mce-fontawesome-panel-accordion');
            firstAccordion.classList.add('mce-fontawesome-panel-accordion-open');

            var firstAccordionContent = firstAccordion.querySelector('.mce-fontawesome-panel-content');
            firstAccordionContent.style.height = 'auto';
            var nextHeight = Math.ceil(firstAccordionContent.offsetHeight);
            firstAccordionContent.style.height = nextHeight + 'px';
            firstAccordionContent.style.transitionDuration = transitionCalc(nextHeight);

            var firstAccordionIndicator = firstAccordion.querySelector('.mce-fontawesome-panel-accordion-indicator');
            firstAccordionIndicator.classList.remove('fa-chevron-right');
            firstAccordionIndicator.classList.add('fa-chevron-down');
        }

        // Insert icon
        function insertIcon() {
            var id = this.getAttribute('data-id');
            var prefix = this.getAttribute('data-prefix');
            if (this.hasAttribute('data-spin')) {
                id += ' fa-spin';
            }
            var content = '<span class="' + prefix + ' fa-' + id + '"></span>';
            if (editor.selection.getNode().textContent === '') {
                content += '&nbsp;';
            }
            editor.execCommand('mceInsertContent', false, content);
            win.close();
        }

        function toggleItem() {
            // Check if search is in use
            if (document.querySelector('.mce-fontawesome-panel-search')) {
                return;
            }

            var accordionItem = this.parentNode;
            var open = false;
            if (accordionItem.classList.contains('mce-fontawesome-panel-accordion-open')) {
                open = true;
            }

            // Hide all items
            var accordionPanel;
            for (var i = 0; i < accordionItems.length; i++) {
                accordionItems[i].classList.remove('mce-fontawesome-panel-accordion-open');

                accordionPanel = accordionItems[i].querySelector('.mce-fontawesome-panel-content');
                accordionPanel.style.height = '0';

                var accordionIndicator = accordionItems[i].querySelector('.mce-fontawesome-panel-accordion-indicator');
                accordionIndicator.classList.remove('fa-chevron-down');
                accordionIndicator.classList.add('fa-chevron-right')
            }

            // Show this item if it was previously hidden
            if (!open) {
                var accordionItemContent = accordionItem.querySelector('.mce-fontawesome-panel-content');

                accordionItemContent.style.height = 'auto';
                var nextHeight = Math.ceil(accordionItemContent.offsetHeight);
                accordionItemContent.style.height = '0';
                accordionItem.classList.add('mce-fontawesome-panel-accordion-open');
                accordionItemContent.style.transitionDuration = transitionCalc(nextHeight);

                accordionIndicator = accordionItem.querySelector('.mce-fontawesome-panel-accordion-indicator');
                accordionIndicator.classList.remove('fa-chevron-right');
                accordionIndicator.classList.add('fa-chevron-down')

                // Force reflow
                window.getComputedStyle(accordionItemContent).opacity;
                accordionItemContent.style.height = nextHeight + 'px';
            }
        }

        // Transition length based on height but also has min / max
        function transitionCalc(length) {
            var result = length / 300;

            if (result > .8) {
                result = .8;
            }

            if (result < .3) {
                result = .3;
            }

            return result + 's';
        }

        // Initialize search input
        var foot = document.querySelector('.mce-fontawesome-panel .mce-foot .mce-container-body');
        var searchContainer = document.createElement('div');
        searchContainer.className = 'mce-fontawesome-search-container';
        searchContainer.innerHTML = '<input type="search" placeholder="' + translate('Search') + '"><div class="mce-fontawesome-search-container-clear"><i class="fa fa-times-circle"></i></div>';
        foot.insertBefore(searchContainer, foot.firstChild);

        var searchInput = searchContainer.querySelector('input');
        searchInput.addEventListener('input', search);

        function search() {
            var categoryList = document.querySelectorAll('.mce-fontawesome-panel-accordion');
            var categoryContentList = document.querySelectorAll('.mce-fontawesome-panel-content');
            var iconList = document.querySelectorAll('.js-mce-fontawesome-insert');
            var searchTerm = this.value.toLowerCase().replace(' ', '-');
            var i;
            var hiddenCategories = 0;

            if (this.value.length) {
                document.querySelector('.mce-fontawesome-panel').classList.add('mce-fontawesome-panel-search');

                // Check whether to hide or show icons
                for (i = 0; i < iconList.length; i++) {
                    hideOrShowIcon(searchTerm, iconList[i]);
                }

                for (i = 0; i < categoryList.length; i++) {
                    // Open all categories
                    categoryList[i].classList.add('mce-fontawesome-panel-accordion-open');

                    // Check if the category has an icons that aren't hidden
                    if (categoryList[i].querySelector('.js-mce-fontawesome-insert:not(.js-mce-fontawesome-insert-hidden)')) {
                        categoryList[i].style.display = '';
                    } else {
                        categoryList[i].style.display = 'none';
                        hiddenCategories++;
                    }
                }

                // Open all categories
                for (i = 0; i < categoryContentList.length; i++) {
                    categoryContentList[i].style.height = 'auto';
                }

                // Show or hide no results message
                if (hiddenCategories === categoryList.length) {
                    document.querySelector('.mce-fontawesome-search-noresults').style.display = 'block';
                } else {
                    document.querySelector('.mce-fontawesome-search-noresults').style.display = 'none';
                }
            } else {
                document.querySelector('.mce-fontawesome-panel').classList.remove('mce-fontawesome-panel-search');
                document.querySelector('.mce-fontawesome-search-noresults').style.display = 'none';

                for (i = 0; i < iconList.length; i++) {
                    iconList[i].classList.remove('js-mce-fontawesome-insert-hidden');
                    iconList[i].style.display = '';
                }

                for (i = 0; i < categoryList.length; i++) {
                    // Close all categories
                    categoryList[i].classList.remove('mce-fontawesome-panel-accordion-open');
                    categoryList[i].style.display = '';
                }

                // Close all categories
                for (i = 0; i < categoryContentList.length; i++) {
                    categoryContentList[i].style.height = '0';
                }
            }
        }

        function hideOrShowIcon(search, iconElement) {
            var id = iconElement.getAttribute('data-id');

            if (strInStr(search, id)) {
                iconElement.classList.remove('js-mce-fontawesome-insert-hidden');
                iconElement.style.display = '';
                return;
            }

            for(var icon_id in iconList){
                if(iconList.hasOwnProperty(icon_id)){
                    if(icon_id === id){
                        if(iconList[icon_id].filter){
                            for(var ii = 0; ii < iconList[icon_id].filter.length; ii++){
                                if(strInStr(search, iconList[icon_id].filter[ii])) {
                                    iconElement.classList.remove('js-mce-fontawesome-insert-hidden');
                                    iconElement.style.display = '';
                                    return;
                                }
                            }
                        }
                        iconElement.classList.add('js-mce-fontawesome-insert-hidden');
                        iconElement.style.display = 'none';
                    }
                }
            }
        }

        function strInStr(needle, haystack) {
            return haystack.indexOf(needle) > -1;
        }

        // Focus the searchbox on open
        searchInput.focus();

        document.querySelector('.mce-fontawesome-search-container-clear').addEventListener('click', function() {
            searchInput.value = '';
            search.call(searchInput);
            searchInput.focus();
        });
    }

    // Include plugin CSS
    editor.on('init', function() {
        var csslink = editor.dom.create('link', {
            rel: 'stylesheet',
            href: url + '/css/fontawesome.min.css'
        });
        document.getElementsByTagName('head')[0].appendChild(csslink);
    });

    editor.addButton('fontawesome', {
        icon: 'flag',
        text: translate('Icons'),
        tooltip: translate('Icons'),
        onclick: showDialog
    });

    editor.addMenuItem('fontawesome', {
        icon: 'flag',
        text: translate('Icons'),
        onclick: showDialog,
        context: 'insert'
    });
});