// jQuery tagEditor v1.0.19
// https://github.com/Pixabay/jQuery-tagEditor
!function(t){t.fn.tagEditorInput=function(){var e=" ",i=t(this),a=parseInt(i.css("fontSize")),r=t("<span/>").css({position:"absolute",top:-9999,left:-9999,width:"auto",fontSize:i.css("fontSize"),fontFamily:i.css("fontFamily"),fontWeight:i.css("fontWeight"),letterSpacing:i.css("letterSpacing"),whiteSpace:"nowrap"});return r.insertAfter(i),i.bind("keyup keydown focus",(function(){if(e!==(e=i.val())){r.text(e);var t=r.width()+a;20>t&&(t=20),t!=i.width()&&i.width(t)}}))},t.fn.tagEditor=function(e,a,r){function l(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}var n,o=t.extend({},t.fn.tagEditor.defaults,e);if(o.dregex=new RegExp("["+o.delimiter.replace("-","-")+"]","g"),"string"==typeof e){var c=[];return this.each((function(){var i=t(this),l=i.data("options"),n=i.next(".tag-editor");if("getTags"==e)c.push({field:i[0],editor:n,tags:n.data("tags")});else if("addTag"==e){if(l.maxTags&&n.data("tags").length>=l.maxTags)return!1;t('<li><div class="tag-editor-spacer">&nbsp;'+l.delimiter[0]+'</div><div class="tag-editor-tag"></div><div class="tag-editor-delete"><i></i></div></li>').appendTo(n).find(".tag-editor-tag").html('<input type="text" maxlength="'+l.maxLength+'">').addClass("active").find("input").val(a).blur(),r?t(".placeholder",n).remove():n.click()}else"removeTag"==e?(t(".tag-editor-tag",n).filter((function(){return t(this).text()==a})).closest("li").find(".tag-editor-delete").click(),r||n.click()):"destroy"==e&&i.removeClass("tag-editor-hidden-src").removeData("options").off("focus.tag-editor").next(".tag-editor").remove()})),"getTags"==e?c:this}return window.getSelection&&t(document).off("keydown.tag-editor").on("keydown.tag-editor",(function(e){if(8==e.which||46==e.which||e.ctrlKey&&88==e.which){try{var a=getSelection(),r="INPUT"!=document.activeElement.tagName?t(a.getRangeAt(0).startContainer.parentNode).closest(".tag-editor"):0}catch(e){r=0}if(a.rangeCount>0&&r&&r.length){var l=[],n=a.toString().split(r.prev().data("options").dregex);for(i=0;i<n.length;i++){var o=t.trim(n[i]);o&&l.push(o)}return t(".tag-editor-tag",r).each((function(){~t.inArray(t(this).text(),l)&&t(this).closest("li").find(".tag-editor-delete").click()})),!1}}})),this.each((function(){var e=t(this),i=[],a=t("<ul "+(o.clickDelete?'oncontextmenu="return false;" ':"")+'class="tag-editor"></ul>').insertAfter(e);e.addClass("tag-editor-hidden-src").data("options",o).on("focus.tag-editor",(function(){a.click()})),a.append('<li style="width:1px">&nbsp;</li>');var r,c,s='<li><div class="tag-editor-spacer">&nbsp;'+o.delimiter[0]+'</div><div class="tag-editor-tag"></div><div class="tag-editor-delete"><i></i></div></li>';function d(){!o.placeholder||i.length||t(".deleted, .placeholder, input",a).length||a.append('<li class="placeholder"><div>'+o.placeholder+"</div></li>")}function g(r){var l=i.toString();i=t(".tag-editor-tag:not(.deleted)",a).map((function(e,i){var a=t.trim(t(this).hasClass("active")?t(this).find("input").val():t(i).text());if(a)return a})).get(),a.data("tags",i),e.val(i.join(o.delimiter[0])),r||l!=i.toString()&&o.onChange(e,a,i),d()}function f(r){for(var n,c=r.closest("li"),s=r.val().replace(/ +/," ").split(o.dregex),d=r.data("old_tag"),f=i.slice(0),h=!1,u=0;u<s.length;u++)if(p=t.trim(s[u]).slice(0,o.maxLength),o.forceLowercase&&(p=p.toLowerCase()),n=o.beforeTagSave(e,a,f,d,p),p=n||p,!1!==n&&p&&(o.removeDuplicates&&~t.inArray(p,f)&&t(".tag-editor-tag",a).each((function(){t(this).text()==p&&t(this).closest("li").remove()})),f.push(p),c.before('<li><div class="tag-editor-spacer">&nbsp;'+o.delimiter[0]+'</div><div class="tag-editor-tag">'+l(p)+'</div><div class="tag-editor-delete"><i></i></div></li>'),o.maxTags&&f.length>=o.maxTags)){h=!0;break}r.attr("maxlength",o.maxLength).removeData("old_tag").val(""),h?r.blur():r.focus(),g()}a.click((function(e,i){var r,l,c=99999;if(!window.getSelection||""==getSelection())return o.maxTags&&a.data("tags").length>=o.maxTags?(a.find("input").blur(),!1):(n=!0,t("input:focus",a).blur(),!!n&&(n=!0,t(".placeholder",a).remove(),i&&i.length?l="before":t(".tag-editor-tag",a).each((function(){var a=t(this),n=a.offset(),o=n.left,s=n.top;e.pageY>=s&&e.pageY<=s+a.height()&&(e.pageX<o?(l="before",r=o-e.pageX):(l="after",r=e.pageX-o-a.width()),r<c&&(c=r,i=a))})),"before"==l?t(s).insertBefore(i.closest("li")).find(".tag-editor-tag").click():"after"==l?t(s).insertAfter(i.closest("li")).find(".tag-editor-tag").click():t(s).appendTo(a).find(".tag-editor-tag").click(),!1))})),a.on("click",".tag-editor-delete",(function(r){if(t(this).prev().hasClass("active"))return t(this).closest("li").find("input").caret(-1),!1;var l=t(this).closest("li"),n=l.find(".tag-editor-tag");return!1===o.beforeTagDelete(e,a,i,n.text())||(n.addClass("deleted").animate({width:0},o.animateDelete,(function(){l.remove(),d()})),g()),!1})),o.clickDelete&&a.on("mousedown",".tag-editor-tag",(function(r){if(r.ctrlKey||r.which>1){var l=t(this).closest("li"),n=l.find(".tag-editor-tag");return!1===o.beforeTagDelete(e,a,i,n.text())||(n.addClass("deleted").animate({width:0},o.animateDelete,(function(){l.remove(),d()})),g()),!1}})),a.on("click",".tag-editor-tag",(function(e){if(o.clickDelete&&(e.ctrlKey||e.which>1))return!1;if(!t(this).hasClass("active")){var i=t(this).text(),r=Math.abs((t(this).offset().left-e.pageX)/t(this).width()),n=parseInt(i.length*r),c=t(this).html('<input type="text" maxlength="'+o.maxLength+'" value="'+l(i)+'">').addClass("active").find("input");if(c.data("old_tag",i).tagEditorInput().focus().caret(n),o.autocomplete){var s=t.extend({},o.autocomplete),d="select"in s?o.autocomplete.select:"";s.select=function(e,i){d&&d(e,i),setTimeout((function(){a.trigger("click",[t(".active",a).find("input").closest("li").next("li").find(".tag-editor-tag")])}),20)},c.autocomplete(s)}}return!1})),a.on("blur","input",(function(r){r.stopPropagation();var c=t(this),s=c.data("old_tag"),h=t.trim(c.val().replace(/ +/," ").replace(o.dregex,o.delimiter[0]));if(h){if(h.indexOf(o.delimiter[0])>=0)return void f(c);if(h!=s)if(o.forceLowercase&&(h=h.toLowerCase()),cb_val=o.beforeTagSave(e,a,i,s,h),h=cb_val||h,!1===cb_val){if(s)return c.val(s).focus(),n=!1,void g();try{c.closest("li").remove()}catch(r){}s&&g()}else o.removeDuplicates&&t(".tag-editor-tag:not(.active)",a).each((function(){t(this).text()==h&&t(this).closest("li").remove()}))}else{if(s&&!1===o.beforeTagDelete(e,a,i,s))return c.val(s).focus(),n=!1,void g();try{c.closest("li").remove()}catch(r){}s&&g()}c.parent().html(l(h)).removeClass("active"),h!=s&&g(),d()})),a.on("paste","input",(function(e){t(this).removeAttr("maxlength"),r=t(this),setTimeout((function(){f(r)}),30)})),a.on("keypress","input",(function(e){o.delimiter.indexOf(String.fromCharCode(e.which))>=0&&(c=t(this),setTimeout((function(){f(c)}),20))})),a.on("keydown","input",(function(i){var r=t(this);if((37==i.which||!o.autocomplete&&38==i.which)&&!r.caret()||8==i.which&&!r.val())return(l=r.closest("li").prev("li").find(".tag-editor-tag")).length?l.click().find("input").caret(-1):!r.val()||o.maxTags&&a.data("tags").length>=o.maxTags||t(s).insertBefore(r.closest("li")).find(".tag-editor-tag").click(),!1;if((39==i.which||!o.autocomplete&&40==i.which)&&r.caret()==r.val().length)return(n=r.closest("li").next("li").find(".tag-editor-tag")).length?n.click().find("input").caret(0):r.val()&&a.click(),!1;if(9==i.which){if(i.shiftKey){var l;if((l=r.closest("li").prev("li").find(".tag-editor-tag")).length)l.click().find("input").caret(0);else{if(!r.val()||o.maxTags&&a.data("tags").length>=o.maxTags)return e.attr("disabled","disabled"),void setTimeout((function(){e.removeAttr("disabled")}),30);t(s).insertBefore(r.closest("li")).find(".tag-editor-tag").click()}return!1}var n;if((n=r.closest("li").next("li").find(".tag-editor-tag")).length)n.click().find("input").caret(0);else{if(!r.val())return;a.click()}return!1}if(!(46!=i.which||t.trim(r.val())&&r.caret()!=r.val().length))return(n=r.closest("li").next("li").find(".tag-editor-tag")).length?n.click().find("input").caret(0):r.val()&&a.click(),!1;if(13==i.which)return a.trigger("click",[r.closest("li").next("li").find(".tag-editor-tag")]),o.maxTags&&a.data("tags").length>=o.maxTags&&a.find("input").blur(),!1;if(36!=i.which||r.caret()){if(35==i.which&&r.caret()==r.val().length)a.find(".tag-editor-tag").last().click();else if(27==i.which)return r.val(r.data("old_tag")?r.data("old_tag"):"").blur(),!1}else a.find(".tag-editor-tag").first().click()}));for(var h=o.initialTags.length?o.initialTags:e.val().split(o.dregex),u=0;u<h.length&&!(o.maxTags&&u>=o.maxTags);u++){var p=t.trim(h[u].replace(/ +/," "));p&&(o.forceLowercase&&(p=p.toLowerCase()),i.push(p),a.append('<li><div class="tag-editor-spacer">&nbsp;'+o.delimiter[0]+'</div><div class="tag-editor-tag">'+l(p)+'</div><div class="tag-editor-delete"><i></i></div></li>'))}g(!0);var v="object"==typeof o.sortable?o.sortable:{};o.sortable&&t.fn.sortable&&a.sortable({distance:5,cancel:".tag-editor-spacer, input",helper:"clone",tolerance:"pointer",cursor:"move",...v,update:function(){"function"==typeof v.update&&v.update.apply(this,arguments),g()}})}))},t.fn.tagEditor.defaults={initialTags:[],maxTags:0,maxLength:50,delimiter:",;",placeholder:"",forceLowercase:!0,removeDuplicates:!0,clickDelete:!1,animateDelete:175,sortable:!0,autocomplete:null,onChange:function(){},beforeTagSave:function(){},beforeTagDelete:function(){}}}(jQuery);