<?php  

//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

// error_reporting(0);
// ini_set('display_errors', 'off');

// Define vars
$to        = $_POST['recipient'] ?? false ?: $global['email_contactform'] ?: $global['contact_email'];
$inquiry   = $_POST['inquiry'] ?? false ?: 'General Inquiry';
$response  = [
	'to'             => $to,
	'errors'         => false,
	'error_fields'   => [],
	'msg_validation' => ''
];

$ip_address = get_ip();

// Validation
$required_fields  = [];
$email_fields     = [];

// Query building as well as email content 
// Like: [column_name => label];
$field_labels = [];


// Configure script variables based on inquiry type
switch ($inquiry) {
	
	// Contact Form Submission
	case 'General Inquiry':

		// Message content
		$field_labels['name']    = 'From';
		$field_labels['subject'] = 'Subject';
		$field_labels['email']   = 'Email';
		$field_labels['phone']   = 'Phone No';
		$field_labels['message'] = ''; //Leave blank if you do not want a label in the email

		// Validation
		$email_fields[] = 'email';
		$required_fields[] = 'name';
		$required_fields[] = 'subject';
		$required_fields[] = 'email';
		$required_fields[] = 'message';
		break;

	// Landing Page Submission
	case 'Lead':

		// Loop through page data
		if ($page_form = $SiteBuilder->get_page_form($_POST['page_id'] ?? '')) {
			foreach ($page_form as $name => $field) {

				// Message content
				$field_labels[$name] = rtrim($field['label'], ':');

				// Validation
				if ($field['required'] && !$field['showhide']) {
					$required_fields[] = $name;
				}
				
				if ($field['type'] == 'email') {
					$email_fields[] = $name;
				}
			}
			
			// Set subject to page title if field is missing or empty
			if(($_POST['subject'] ?? '') == '' && ($_POST['page_id'] ?? '') != ''){
				$_POST['subject'] = $sitemap[$_POST['page_id']]['page_title'];
				$field_labels['subject'] = 'Subject';
			}
			
		} else {
			$response['errors'][] = 'An error occurred and your submission could not be processed. Please refresh the page and try again. If the issue persists, email us at <a href="mailto:'.$global['contact_email'].'">'.$global['contact_email'].'</a>.';
		}

		break;
}


//Cookie validation
if($_POST['xid'] != $_COOKIE['xid']) {
	$response['errors'][] = 'Please make sure cookies are enabled on your browser then try again.';
}

//Required fields validation
foreach($required_fields as $field) {
	if(empty($_POST[$field])) {
		$response['errors'][0] = 'Please fill in all required fields.';
		$response['error_fields'][] = $field;
	}
}

//Email validation
foreach($email_fields as $field) {
	if(!empty($_POST[$field]) && !checkmail($_POST[$field])) {
		$response['errors'][1] = 'Email address is invalid.';
		$response['error_fields'][] = 'email';
	}
}

// Recaptcha
if(validate_recaptcha()) {
	$response['errors'][] = 'Please verify you are not a robot.';
}


// All valid
if(!$response['errors']) {

	// Build message content
	$html = '<h3>Website Inquiry</h3><p>';
	foreach ($field_labels as $field => $label) {
		$html .= (trim($label) != '' ? '<strong>'.$label.':</strong> ' : '<br/>');
		$html .= ($_POST[$field] ?? '' ?: '<em>N/A</em>').'<br/>';
	}
	$html .= '</p>';
	
	// Include page url
	if(($_POST['page_id'] ?? '') != ''){
		$html .= '<p><small>Submitted from: ' .$siteurl.$sitemap[$_POST['page_id']]['page_url']. '</small></p>';
	}

	// Send mail
	$mail_sent = send_email($to, 'Website Inquiry - '.$inquiry, $html);

	// Insert to DB
	$params = [];
	foreach ($field_labels as $field => $label) {
		$params[$field] = ($_POST[$field] ?? NULL) ?: NULL;
	}
	$params += [
		'inquiry'    => $inquiry,
		'mail_sent'  => $mail_sent,
		'ip_address' => $ip_address,
		'hostname'   => gethostbyaddr($ip_address),
		'session_id' => session_id(),
		'timestamp'  => date('Y-m-d H:i:s')
	];
	$item_id = $db->insert('inquiries', $params);


	// Display error if both operations have failed
	if (!$item_id && !$mail_sent) {
		$response['errors'][] = 'An error occurred and your submission could not be processed. Please refresh the page and try again. If the issue persists, email us at <a href="mailto:'.$global['contact_email'].'">'.$global['contact_email'].'</a>.';
	} else {
		$response['msg_validation'] = 'Your message was successfully sent.';
	}
}

// Has Errors
if($response['errors']) {
	$response['msg_validation'] = implode('<br/>', $response['errors']);
}

// Debug data
// $response['mail_error'] = $mail_error;

echo json_encode($response);

?>