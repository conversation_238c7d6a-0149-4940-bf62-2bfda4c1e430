<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?>">
	<div class="container container-lg">

		<div class="panel-content">
		<?php
		if((trim($panel['title']) != '' && $panel['show_title']) || (!$panelcount && SLIDESHOW)){
			echo '<header class="panel-header">
				'.(!$panelcount && SLIDESHOW ? '<h1>'.fancy_text($page['page_title']).'</h1>' : '').'
				'.(trim($panel['title']) != '' && $panel['show_title'] ? '<div class="panel-title"><h2>' .$panel['title']. '</h2></div>' : '').'
			</header>';
		}
		if(trim($panel['content']) != ''){
			echo '<div class="panel-text">
				<p>'.nl2br($panel['content']).'</p>
			</div>';
		}
		?>
		</div>

		<?php
		if(!empty($panel['panel_staff'])){
			echo '<div class="staff-listing blog-entries-container">';

			foreach($panel['panel_staff'] as $staff){
				$staffdir = 'images/staff/thumbs/';
				$staffimg = check_file($staff['image'], $staffdir) ? $staff['image'] : 'default.jpg';
				$imgUrl = $path.$staffdir.$staffimg;
				$defaultImg = $path.$staffdir.'default.jpg';
				
				// Determine the index for rotation
				static $cardIndex = 0;
				$cardIndex++;
				
				// Apply rotation based on position (odd/even)
				$rotation = ($cardIndex % 2 == 0) ? 'rotate(5deg)' : 'rotate(-5deg)';
				
				echo '<div class="staff-card lazy-load" style="background-size:cover;object-fit:cover;background-position:center;background-repeat:no-repeat; background-image: url(\'' . $defaultImg . '\');transform: ' . $rotation . ';transition: transform 0.3s ease;" data-src="' . $imgUrl . '" onmouseover="this.style.transform=\'rotate(0deg)\'" onmouseout="this.style.transform=\'' . $rotation . '\'">
					<a href="' .$_sitepages['staff']['page_url']. $staff['page']. '-' .$staff['staff_id']. '/">
						<span class="sr-only">' . $staff['name'] . '</span>
					</a>
					<div class="staff-info-container">
						<div class="staff-position"><span>' .(!empty($staff['position']) ? $staff['position'] : '').'</span></div>
						<div class="staff-name"><span>' .$staff['name']. '</span></div>
						<div class="staff-email"><span>' .(!empty($staff['email']) ? $staff['email'] : '').'</span></div>
					</div>
				</div>';
			}

			echo '</div>';
			// echo '</div>';
		}

		// Add navigation arrows with vertical design
		// echo '<div class="swiper-navigation">
		// 	<div class="swiper-button-prev" aria-label="Previous slide"></div>
		// 	<div class="swiper-button-next" aria-label="Next slide"></div>
		// </div>';
	?>
	</div>
</section>