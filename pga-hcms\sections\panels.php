<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Image cropping
if($CMSUploader->crop_queue()){
	include("includes/jcropimages.php");

//Position images
}else if($CMSUploader->position_queue()){
	include("includes/positionimages.php");

//Display form
}else{

	$data         = $records_arr[ITEM_ID] ?? [];
	$row          = !isset($_POST['save']) ? $data : $_POST;
	$image        = $data['image'] ?? NULL;
	$image_mobile = $data['image_mobile'] ?? NULL;
	$dynamic      = ITEM_ID != '' && $data['deletable'] == 0;

	echo '<form id="panel-form" class="multiselects" action="" method="post" enctype="multipart/form-data">';

	//Panel details
	echo '<div class="panel">
		<div class="panel-header">Panel Details
			<span class="panel-toggle fas fa-chevron-up"></span>
			<div class="panel-switch">
				<label>Show Panel</label>
				<div class="onoffswitch">
					<input type="checkbox" name="showhide" id="showhide" value="0"' .(($row['showhide'] ?? 0) ? '' : ' checked'). ' />
					<label for="showhide">
						<span class="inner"></span>
						<span class="switch"></span>
					</label>
				</div>
			</div>
		</div>
		<div class="panel-content">
			<div class="flex-container">
				<div class="form-field">
					<label>Panel Type <span class="required">*</span></label>
					<select name="panel_type" class="select animation-control"' .(($row['deletable'] ?? 1) == 0 ? ' disabled' : ''). '>
						<option value="standard"' .($panel_type == 'standard' ? ' selected' : ''). '>Standard</option>
						<option value="parallax"' .($panel_type == 'parallax' ? ' selected' : ''). '>Parallax</option>
						<option value="side"' .($panel_type == 'side' ? ' selected' : ''). '>Side by Side</option>';

						if($CMSBuilder->get_section_status($_cmssections['promo_boxes']) == 'Enabled'){
							echo '<option value="promo"' .($panel_type == 'promo' ? ' selected' : ''). '>Promo Boxes</option>';
						}
						if($CMSBuilder->get_section_status($_cmssections['ctas']) == 'Enabled'){
							echo '<option value="cta"' .($panel_type == 'cta' ? ' selected' : ''). '>Call to Action</option>';
						}
						if($CMSBuilder->get_section_status($_cmssections['galleries']) == 'Enabled'){
							echo '<option value="gallery"' .($panel_type == 'gallery' ? ' selected' : ''). '>Gallery</option>';
						}
						if($CMSBuilder->get_section_status($_cmssections['faqs']) == 'Enabled'){
							echo '<option value="faqs"' .($panel_type == 'faqs' ? ' selected' : ''). '>FAQs</option>';
						}
						if($CMSBuilder->get_section_status($_cmssections['staff']) == 'Enabled'){
							echo '<option value="staff"' .($panel_type == 'staff' ? ' selected' : ''). '>Staff</option>';
						}
						if($CMSBuilder->get_section_status($_cmssections['reviews']) == 'Enabled'){
							echo '<option value="reviews"' .($panel_type == 'reviews' ? ' selected' : ''). '>Reviews</option>';
						}

						//
						if($CMSBuilder->get_section_status($_cmssections['contact-panel']) == 'Enabled'){
							echo '<option value="contact-panel"' .($panel_type == 'contact-panel' ? ' selected' : ''). '>Contact Panel</option>';
						}
						//

						// ADD NEW PANEL OPTION  VV
						if($CMSBuilder->get_section_status($_cmssections['partners']) == 'Enabled'){
							echo '<option value="partners"'.($panel_type == 'partners' ? ' selected' : '').'>Partners & Sponsors</option>';
						}

						//
					if($CMSBuilder->get_section_status($_cmssections['blog']) == "Enabled"){
						echo '<option value="blog"'.($panel_type == "blog" ? " selected" : "").'>Blog Posts</option>';
					}
						//
						if($CMSBuilder->get_section_status($_cmssections['form_builder']) == 'Enabled'){
							echo "<option value='form'" .(isset($row['panel_type']) && $row['panel_type'] == 'form' ? ' selected' : ''). ">Form</option>"; //Add form to panel_type selection
						}

					echo '</select>
				</div>
			</div>

			<hr />

			<div class="flex-container">

				<div class="form-field">
					<label>Panel Title <span class="required">*</span>' .$CMSBuilder->tooltip('Panel Title', 'Put special emphasis on a word or phrase by enclosing it in {curly brackets}.'). '</label>
					<input type="text" name="title" value="' .($row['title'] ?? ''). '" class="input' .(in_array('title', $required) ? ' required' : ''). '" style="margin-bottom:5px;" />
					<p class="cta" data-anim-inverse'.($panel_type == 'cta' ? ' style="display:none"' : ''). '>
						<input type="checkbox" id="show_title" name="show_title" class="checkbox" value="1"' .(($row['show_title'] ?? 1) ? ' checked' : ''). ' />
						<label for="show_title"><small>Show Panel Title</small></label>
					</p>
				</div>

				<div class="form-field parallax" style="' .($panel_type == 'parallax' ? '' : 'display:none'). '">
					<label>Colour Overlay <span class="required">*</span>' .$CMSBuilder->tooltip('Colour Overlay', 'This will be used as the overlay colour for the panel background image. If no image is uploaded, this will be the panel background color.'). '</label>
					<select name="theme" class="select">';
						foreach($_themes as $theme_key => $theme_name){
							echo '<option value="' .$theme_key. '"' .(($row['theme'] ?? '') == $theme_key ? ' selected' : ''). '>' .$theme_name. '</option>';
						}
					echo '</select>
				</div>

				<div class="form-field">
					<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
					<select name="ordering" class="select">
						<option value="101">Default</option>';
						for($i=1; $i<101; $i++){
							echo '<option value="' .$i. '"' .(($row['ordering'] ?? '') == $i ? ' selected' : ''). '>' .$i. '</option>';
						}
					echo '</select>
				</div>

			</div>';

			//FAQ Fields
			echo '<div class="flex-container faqs panel-field" style="'.($panel_type == 'faqs' ? '' : 'display:none'). '">
				<div class="form-field">
					<label>FAQ Category <span class="required">*</span></label>
					<select name="page_faqs" class="select' .(in_array('page_faqs', $required) ? ' required' : ''). '">
						<option value="">- Select -</option>';
						foreach($faq_categories as $category_id => $category){
							echo '<option value="' .$category_id. '"' .(in_array($category_id, ($row['page_faqs'] ?? 0 ?: [])) ? ' selected' : ''). '>' .$category['name']. '</option>';
						}
					echo '</select>
				</div>
			</div>';

			// Blog Fields
			echo '<div class="form-field blog panel-field" style="display:'.($panel_type == "blog" ? "block" : "none").';">
				<label>Blog Categories </label>
				<select name="blog_category_id" class="select'.(in_array("blog_category_id", $required) ? " required" : "").'">
					<option value="">All Categories</option>';

				foreach($blog_categories as $category_id => $category){
					echo '<option value="'.$category_id.'"'.($category_id == ($row['blog_category_id'] ?? false) ? " selected" : "").'>'.$category['name'].'</option>';
				}

				echo '</select>
			</div>';

			//Promo Fields
			echo '<div class="flex-container promo panel-field" style="' .($panel_type == 'promo' ? '' : 'display:none'). '">
				<div class="form-field">
					<label>Promo Box Type <span class="required">*</span></label>
					<select name="promo_type" class="select animation-control' .(in_array('promo_type', $required) ? ' required' : '').'"'.($dynamic ? ' disabled' : '').'>
						<option value="standard"' .(($row['promo_type'] ?? '') == 'standard' ? ' selected' : ''). ' data-toggle=".standard-promo">Standard</option>
						<option value="mini"' .(($row['promo_type'] ?? '') == 'mini' ? ' selected' : ''). ' data-toggle=".mini-promo">Mini</option>
					</select>
				</div>
			</div>';

			//CTA Fields
			echo '<div class="flex-container cta panel-field" style="' .($panel_type == 'cta' ? '' : 'display:none'). '">
				<div class="form-field">
					<label>Call to Action <span class="required">*</span></label>
					<select name="cta_id" class="select' .(in_array('cta_id', $required) ? ' required' : ''). '">
						<option value="">- Select -</option>';
						foreach($ctas as $cta){
							echo '<option value="' .$cta['cta_id']. '"' .(($row['cta_id'] ?? '') == $cta['cta_id'] ? ' selected' : ''). '>' .$cta['title']. '</option>';
						}
					echo '</select>
				</div>
			</div>';

			//Gallery fields
			echo '<div class="flex-container gallery panel-field" style="' .($panel_type == 'gallery' ? '' : 'display:none'). '">
				<div class="form-field">
					<label>Gallery <span class="required">*</span></label>
					<select name="gallery_id" class="select' .(in_array('gallery_id', $required) ? ' required' : ''). '">
						<option value="">- Select -</option>';
						foreach($galleries as $gallery){
							echo '<option value="' .$gallery['gallery_id']. '"' .(($row['gallery_id'] ?? '') == $gallery['gallery_id'] ? ' selected' : ''). '>' .$gallery['name']. '</option>';
						}
					echo '</select>
				</div>
				<div class="form-field">
					<label>Gallery Limit <span class="required">*</span>' .$CMSBuilder->tooltip('Gallery Limit', 'If you wish to limit the number of gallery photos being displayed, enter the number you would like to show. Leave blank to show all.'). '</label>
					<input type="text" name="gallery_limit" value="' .($row['gallery_limit'] ?? ''). '" class="input' .(in_array('gallery_limit', $required) ? ' required' : ''). '" />
				</div>
			</div>';

			//Side by Side fields
			echo '<div class="flex-container side panel-field" style="' .($panel_type == 'side' ? '' : 'display:none'). '">
				<div class="form-field">
					<label>Panel Subtitle</label>
					<input type="text" name="subtitle" value="' .($row['subtitle'] ?? ''). '" class="input" />
				</div>
				<div class="form-field">
					<label>Image Position</label>
					<select name="side_image_position" class="select">
						<option value="0"' .(!($row['side_image_position'] ?? 0) ? ' selected' : ''). '>Right</option>
						<option value="1"' .(($row['side_image_position'] ?? 0) ? ' selected' : ''). '>Left</option>
					</select>
				</div>
				<div class="form-field">
					<label>Image Position (Mobile)</label>
					<select name="side_image_position_mobile" class="select">
						<option value="0"' .(!($row['side_image_position_mobile'] ?? 0) ? ' selected' : ''). '>Top</option>
						<option value="1"' .(($row['side_image_position_mobile'] ?? 0) ? ' selected' : ''). '>Bottom</option>
					</select>
				</div>
			</div>';

		echo '</div>
	</div>'; //Panel details

	//Panel image
	echo '<div class="panel parallax side panel-field" style="' .($panel_type == 'parallax' || $panel_type == 'side' ? '' : 'display:none'). '">
		<div class="panel-header">Panel Image
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">';

				$thumb = $panel_type == 'side' ? '480/' : '1024/';
				$full  = '1920/';
				echo $CMSBuilder->img_holder($image, $imagedir.$full, $imagedir.$thumb).

				'<div class="form-field">
					<label class="panel-field parallax" style="' .($panel_type == 'parallax' ? '' : 'display:none'). '">Upload Image (Desktop)' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least '.$CMSUploader::size_label('parallax', 'image').' and file size must be smaller than ' .$_max_filesize['megabytes']. '. This image will be used for high screen resolutions.'). '</label>

					<label class="panel-field side" style="' .($panel_type == 'side' ? '' : 'display:none'). '">Upload Side Image' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least '.$CMSUploader::size_label('side', 'image').' and file size must be smaller than ' .$_max_filesize['megabytes']. '. This image will appear beside the panel content.'). '</label>

					<input type="file" class="input' .(in_array('image', $required) ? ' required' : ''). '" name="image" value="" />
				</div>

				<div class="form-field panel-field side" style="' .($panel_type == 'side' ? '' : 'display:none'). '">
					<label>Alt Text <small>(SEO)</small>' .$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.'). '</label>
					<input type="text" name="image_alt" value="' .($row['image_alt'] ?? ''). '" class="input" />
				</div>

			</div>

			<hr />

			<div class="flex-container panel-field side" style="' .($panel_type == 'side' ? '' : 'display:none'). '">
				<div class="form-field">
					<label>Video URL (YouTube or Vimeo)' .$CMSBuilder->tooltip('Video URL', 'Enter a valid youtube or vimeo URL.  Videos will always take priority over uploaded images.'). '</label>
					<input type="text" name="video_url" value="' .($row['video_url'] ?? ''). '" class="input' .(in_array('video_url', $required) ? ' required' : ''). '" />
				</div>
			</div>

			<div class="flex-container panel-field parallax" style="' .($panel_type == 'parallax' ? '' : 'display:none'). '">'.
				$CMSBuilder->img_holder($image_mobile, $imagedir.'768/', $imagedir.'480/', true, 'image_mobile').

				'<div class="form-field">
					<label>Upload Image (Mobile)' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least '.$CMSUploader::size_label('parallax', 'image_mobile').' and file size must be smaller than ' .$_max_filesize['megabytes']. '. This image will be used for low screen resolutions like mobile devices and tablets.'). '</label>
					<input type="file" class="input'.(in_array('image_mobile', $required) ? ' required' : ''). '" name="image_mobile" value="" />
				</div>

			</div>

		</div>
	</div>'; //Panel image

	//Reviews
	echo '<div class="panel panel-field reviews" style="' .($panel_type == 'reviews' ? '' : 'display:none'). '">
		<div class="panel-header">Reviews
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<p>To attach reviews to the panel, select from the list of reviews below and use the arrow keys to populate the list on the right.<br /><strong>Note: Reviews without any content cannot be attached to a panel and will not be included in the list below.</strong></p>';

			echo '<div class="multiselects-wrapper">
				<div class="multiselect-field">
					<label>Reviews</label>
					<select name="review_ids" class="multiselect multiselect-lg" size="5" data-action="add" data-move="1" data-level="1">';
						foreach($reviews as $review){
							echo '<option value="' .$review['review_id']. '">' .$review['label']. '</option>';
						}
					echo '</select>
				</div>

				<div class="multiselect-arrows">
					<a href="#" class="multiselect-arrow button disabled" data-action="add">&raquo;</a>
					<a href="#" class="multiselect-arrow button disabled" data-action="remove">&laquo;</a>
				</div>

				<div class="multiselect-field">
					<label>Assigned Reviews</label>
					<select name="page_reviews[]" class="multiselect multiselect-lg" multiple="multiple" data-action="remove" data-move="1">';
						if(!empty($row['page_reviews'])){
							foreach($row['page_reviews'] as $review_id) {
								echo '<option value="' .$review_id. '">' .$reviews[$review_id]['label']. '</option>';
							}
						}
					echo '</select>
				</div>
			</div>';

		echo '</div>
	</div>'; //Reviews

	//Content, tabs for contact-panel
	echo '<div class="page-content contact-panel panel-field" style="' .($panel_type == 'contact-panel' ? '' : 'display:none'). '">

		<div class="tabs tab-ui">
			<ul>
				<li><a href="#content">Panel Content</a></li><li><a href="#panel-tabs">Tabbed Content</a></li>
			</ul>';

			//Page Content
			echo '<div id="content">
				<textarea name="TINYMCE_Editor1" class="tinymceEditor">' .($row['content'] ?? ''). '</textarea>
			</div>';

			//Page Tabbed Content
			echo '<div id="panel-tabs">';
				if(!empty($row['page_tabs'])){
					foreach($row['page_tabs'] as $index => $panel){

						echo '<div class="tabspanel panel-tabs-container">
							<div class="flex-container">
								<div class="flex-column flex-container auto-width left">

									<div class="form-field">
										<label>Tab Label <span class="required">*</span></label>
										<input type="text" name="panel_title[]" value="' .$panel['title']. '" class="input' .(in_array('panel_title'.$index, $required) ? ' required' : ''). '" />
									</div>

									<div class="form-field">
										<label>Show/Hide</label>
										<select name="panel_showhide[]" class="select">
											<option value="0"' .(!($panel['showhide'] ?? 0) ? ' selected' : ''). '>Show</option>
											<option value="1"' .(($panel['showhide'] ?? 0) ? ' selected' : ''). '>Hide</option>
											</select>
									</div>

									<div class="form-field">
										<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
										<select name="panel_ordering[]" class="select">
											<option value="101">Default</option>';
											for($i=1; $i<101; $i++){
												echo '<option value="' .$i. '"' .(($panel['ordering'] ?? '') == $i ? ' selected' : ''). '>' .$i. '</option>';
											}
										echo '</select>
									</div>
								</div>

								<div class="flex-column right">
									<p><button type="button" class="button-sm delete-template-btn"><i class="fas fa-trash-alt"></i>Delete Tab</button></p>
								</div>

							</div>

							<textarea name="panel_content[]" class="tinymceEditor">' .str_replace('\r\n', '', $panel['content']). '</textarea>

							<input type="hidden" name="tab_id[]" class="tab_id" value="' .$panel['tab_id']. '" />
							<br/><hr /><br/>
						</div>';
					}
				}

				//Add new tab button
				echo '<p class="copy-btn-container"><button type="button" class="copy-template-btn button-sm"><i class="fas fa-plus"></i>Add New Tab</button></p>';

			echo '</div>

		</div>
	</div>'; //Content, tabs for contact-panel

	//Content, tabs
	echo '<div class="page-content standard panel-field" style="' .($panel_type == 'standard' ? '' : 'display:none'). '">

		<div class="tabs tab-ui">
			<ul>
				<li><a href="#content">Panel Content</a></li><li><a href="#panel-tabs">Tabbed Content</a></li>
			</ul>';

			//Page Content
			echo '<div id="content">
				<textarea name="TINYMCE_Editor" class="tinymceEditor">' .($row['content'] ?? ''). '</textarea>
			</div>';

			//Page Tabbed Content
			echo '<div id="panel-tabs">';
				if(!empty($row['page_tabs'])){
					foreach($row['page_tabs'] as $index => $panel){

						echo '<div class="tabspanel panel-tabs-container">
							<div class="flex-container">
								<div class="flex-column flex-container auto-width left">

									<div class="form-field">
										<label>Tab Label <span class="required">*</span></label>
										<input type="text" name="panel_title[]" value="' .$panel['title']. '" class="input' .(in_array('panel_title'.$index, $required) ? ' required' : ''). '" />
									</div>

									<div class="form-field">
										<label>Show/Hide</label>
										<select name="panel_showhide[]" class="select">
											<option value="0"' .(!($panel['showhide'] ?? 0) ? ' selected' : ''). '>Show</option>
											<option value="1"' .(($panel['showhide'] ?? 0) ? ' selected' : ''). '>Hide</option>
											</select>
									</div>

									<div class="form-field">
										<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
										<select name="panel_ordering[]" class="select">
											<option value="101">Default</option>';
											for($i=1; $i<101; $i++){
												echo '<option value="' .$i. '"' .(($panel['ordering'] ?? '') == $i ? ' selected' : ''). '>' .$i. '</option>';
											}
										echo '</select>
									</div>
								</div>

								<div class="flex-column right">
									<p><button type="button" class="button-sm delete-template-btn"><i class="fas fa-trash-alt"></i>Delete Tab</button></p>
								</div>

							</div>

							<textarea name="panel_content[]" class="tinymceEditor">' .str_replace('\r\n', '', $panel['content']). '</textarea>

							<input type="hidden" name="tab_id[]" class="tab_id" value="' .$panel['tab_id']. '" />
							<br/><hr /><br/>
						</div>';
					}
				}

				//Add new tab button
				echo '<p class="copy-btn-container"><button type="button" class="copy-template-btn button-sm"><i class="fas fa-plus"></i>Add New Tab</button></p>';

			echo '</div>

		</div>
	</div>'; //Content, tabs

	//Add form section
		echo "<div class='form-field form panel-field' style='display:" .($panel_type == 'form' ? 'block' : 'none'). ";'>
			<label>Form <span class='required'>*</span></label>
			<select name='form_id' class='select" .(in_array('form_id', $required) ? ' required' : ''). "'>
				<option value=''>- Select -</option>";
				foreach($forms as $form){
					echo "<option value='" .$form['form_id']. "'" .((isset($row['form_id']) && $row['form_id'] == $form['form_id'] ? ' selected' : '')). ">" .$form['form_name']. "</option>";
				}
		echo "</select>
		</div>";

	//Simple content - side and parallax
	echo '<div class="panel panel-field side promo gallery faqs staff reviews parallax" style="' .($panel_type == 'side' || $panel_type == 'promo' || $panel_type == 'gallery' || $panel_type == 'faqs' || $panel_type == 'staff' || $panel_type == 'reviews' || $panel_type == 'parallax' || $panel_type == 'form'  ? '' : 'display:none'). '">
		<div class="panel-header">Content
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<textarea name="simple_content" class="tinymceMini">' .($row['content'] ?? ''). '</textarea>
		</div>
	</div>';

	//Promos
	if (!$dynamic) {
		echo '<div class="panel promo panel-field" style="' .($panel_type == 'promo' ? '' : 'display:none'). '">
			<div class="panel-header">Promo Boxes
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">';

				//Move selected items to front of array
				$promoboxes = array_replace(array_flip($row['page_promos'] ?? []), $promoboxes);
				if(!empty($promoboxes)){
					echo '<div class="item-picker sortable-container">';

					foreach($promoboxes as $promo_id => $promo) {
						$promo['image'] = check_file($promo['image'], '../images/promos/thumbs/') ?: 'default.jpg';
						$promo['image'] = $root.'images/promos/thumbs/'.$promo['image'];
						$checked = in_array($promo_id, $row['page_promos'] ?? []) ? ' checked' : '';

						echo '<div class="item">
							<input type="checkbox" name="page_promos[]" value="'.$promo_id.'" id="promo-'.$promo_id.'"'.$checked.'>
							<label for="promo-'.$promo_id.'" class="sort-handler">
								<i class="fas fa-arrows-alt handle"></i>
								<img src="'.$promo['image'].'" alt="'.$promo['title'].'" class="standard-promo" style="' .(($row['promo_type'] ?? 'standard') == 'standard' ? 'display:block;' : 'display:none;'). '">
								<i class="icon mini-promo ' .$promo['icon']. '" style="' .(($row['promo_type'] ?? '') == 'mini' ? 'display:block;' : 'display:none;'). '"></i>
								<small class="truncate">'.($promo['url'] ?: '#').'</small>
								<span class="bold">'.$promo['title'].'</span>
							</label>
						</div>';
					}

					echo '</div>';
				}else{
					echo '<p>You must create a promo box before attaching it to a page. &nbsp; <a href="' .$promospage['page_url']. '?action=add">Add new promo box now</a></p>';
				}

			echo '</div>
		</div>'; //Promos
	}

	//Staff
	if(!$dynamic && $CMSBuilder->get_section_status($_cmssections['staff']) == 'Enabled'){
		echo '<div class="panel staff panel-field" style="' .($panel_type == 'staff' ? '' : 'display:none'). '">
			<div class="panel-header">Staff Members
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">';

				//Move selected items to front of array
				$staff_members = array_replace(array_flip($row['page_staff'] ?? []), $staff_members);
				if(!empty($staff_members)){
					echo '<div class="item-picker sortable-container">';

					foreach($staff_members as $staff_id => $member) {
						$member['image'] = check_file($member['image'], '../images/staff/thumbs/') ?: 'default.jpg';
						$member['image'] = $root.'images/staff/thumbs/'.$member['image'];
						$checked = in_array($staff_id, $row['page_staff'] ?? []) ? ' checked' : '';

						echo '<div class="item">
							<input type="checkbox" name="page_staff[]" value="'.$staff_id.'" id="member-'.$staff_id.'"'.$checked.'>
							<label for="member-'.$staff_id.'" class="sort-handler">
								<i class="fas fa-arrows-alt handle"></i>
								<img src="'.$member['image'].'" alt="'.$member['name'].'">
								<small class="truncate">'.($member['category'] ?: 'No Category').'</small>
								<span class="bold">'.$member['name'].'</span>
							</label>
						</div>';
					}

					echo '</div>';
				}else{
					echo '<p>You must create a staff member before attaching it to a page. &nbsp; <a href="' .$staffpage['page_url']. '?action=add">Add new staff member now</a></p>';
				}

			echo '</div>
		</div>';
	}

	//
	//Logos + Partners
	if($CMSBuilder->get_section_status($_cmssections['partners']) == 'Enabled'){
		echo '<div class="panel partners panel-field" style="' .($panel_type == 'partners' ? '' : 'display:none'). '">
			<div class="panel-header">Partners
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">';

				//Move selected items to front of array
				$partners = array_replace(array_flip($row['page_partners'] ?? []), $partners);
				if(!empty($partners)){
					echo '<div class="item-picker sortable-container">';

					foreach($partners as $partner_id => $partner) {
						$partner['image'] = check_file($partner['image'], '../images/partners/thumbs/') ?: 'default.jpg';
						$partner['image'] = $root.'images/partners/thumbs/'.$partner['image'];
						$checked = in_array($partner_id, $row['page_partners'] ?? []) ? ' checked' : '';

						echo '<div class="item">
							<input type="checkbox" name="page_partners[]" value="'.$partner_id.'" id="partner-'.$partner_id.'"'.$checked.'>
							<label for="partner-'.$partner_id.'" class="sort-handler">
								<i class="fas fa-arrows-alt handle"></i>
								<img src="'.$partner['image'].'" alt="'.$partner['name'].'" style="aspect-ratio: 1; object-fit: contain;">
								<small class="truncate">'.($partner['url'] ? get_domain($partner['url']) : 'No Link').'</small>
								<span class="bold">'.$partner['name'].'</span>
							</label>
						</div>';
					}

					echo '</div>';
				}else{
					echo '<p>You must create a partner before attaching it to a page. &nbsp; <a href="' .$partnerspage['page_url']. '?action=add">Add new partner now</a></p>';
				}

			echo '</div>
		</div>';
	}
	//

	//Sticky footer
	echo '<footer id="cms-footer">
		<div class="flex-container">
			<div class="flex-column right">
				<button type="submit" class="button" name="save" value="save"><i class="fas fa-check"></i>Save Changes</button>
			</div>
			<div class="flex-column left">';
				if(ITEM_ID != ""){
					echo (!($row['deletable'] ?? true) ? $CMSBuilder->tooltip('Delete Panel', 'Due to the dynamic nature of this panel, it cannot be deleted. If you wish to remove this panel, set it to hidden instead.').'&nbsp;' : '');
					echo '<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn"' .(!($row['deletable'] ?? true) ? ' disabled' : ''). '><i class="fas fa-trash-alt"></i>Delete</button>';
				}
				echo '<a href="' .$mainpage['page_url']. '?action=edit&item_id=' .PAGE_ID. '" class="cancel">Cancel</a>
			</div>
		</div>
	</footer>';

	//Fields with tags
	echo '<input type="hidden" name="keep_tags[]" value="TINYMCE_Editor" />
	<input type="hidden" name="keep_tags[]" value="panel_content" />
	<input type="hidden" name="keep_tags[]" value="simple_content" />';

	echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

	//Panel template
	echo '<div class="hidden">
		<div id="panel-tabs-template" class="panel-tabs-container">
			<div class="flex-container">
				<div class="flex-column flex-container auto-width left">

					<div class="form-field">
						<label>Tab Label <span class="required">*</span></label>
						<input type="text" name="panel_title[]" value="" class="input" />
					</div>

					<div class="form-field">
						<label>Show/Hide</label>
						<select name="panel_showhide[]" class="select">
							<option value="0">Show</option>
							<option value="1">Hide</option>
							</select>
					</div>

					<div class="form-field">
						<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
						<select name="panel_ordering[]" class="select">
							<option value="101">Default</option>';
							for($i=1; $i<101; $i++){
								echo '<option value="' .$i. '">' .$i. '</option>';
							}
						echo '</select>
					</div>
				</div>

				<div class="flex-column right">
					<p><button type="button" class="button-sm delete-template-btn"><i class="fas fa-trash-alt"></i>Delete Tab</button></p>
				</div>

			</div>

			<textarea name="panel_content[]" class="tinymceDynamic"></textarea>
			<input type="hidden" name="tab_id[]" class="tab_id" value="" />
			<br/><hr/><br/>

		</div>
	</div>';
}

?>