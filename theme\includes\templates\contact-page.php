<?php

//Locations
if(!empty($global['locations'])){
	$contact_tabs = [];

	foreach($global['locations'] as $loc){
		$html = '';

		//Contact info
		$html .= '<div class="location-panel">
			<div class="contact-information">
				<h3>'.$loc['location_name'].'</h3>';

				//Address
				//  if($loc['full_address']){
				// 	$html .= '<ul class="page-contact page-contact-address">';
				// 	$html .= '<li class="address">
				// 		<span class="label">Address</span>
				// 		<a class="value" href="http://maps.google.com/?q='.urlencode($loc['full_address']).'" target="_blank" rel="noopener noreferrer">'.prettify_address($loc, true).'</a>
				// 	</li>';
				// 	$html .= '</ul>';
				// }

				//Phone numbers
				$html .= '<ul class="page-contact page-contact-numbers">';
				if($loc['phone']){
					$html .= '<li class="phone">
						<span class="label">Phone</span>
						<a class="value" href="tel://'.format_intl_number($loc['phone']).'">'.$loc['phone'].'</a>
					</li>';
				}

				if($loc['toll_free']){
					$html .= '<li class="tollfree">
						<span class="label">Toll Free</span>
						<a class="value" href="tel://'.format_intl_number($loc['toll_free']).'">'.$loc['toll_free'].'</a>
					</li>';
				}

				foreach($loc['location_numbers'] as $number){
					$html .= '<li class="phone phone-alt">
						<span class="label">'.ucwords($number['type']).'</span>
						<a class="value" href="tel://'.format_intl_number($number['phone']).'">'.$number['phone'].'</a>
					</li>';
				}

				if($loc['fax']){
					$html .= '<li class="fax">
						<span class="label">Fax</span>
						<span class="value">'.$loc['fax']. '</span>
					</li>';
				}
				$html .= '</ul>';

				//Email Addresses
				$html .= '<ul class="page-contact page-contact-emails">';
				if($loc['email']){
					$html .= '<li class="mailto">
						<span class="label">Email</span>
						<a class="value" href="mailto:'.$loc['email'].'">'.$loc['email'].'</a>
					</li>';
				}

				foreach($loc['location_contacts'] as $contact){
					$html .= '<li class="mailto '.($contact['type'] == 'email' ? 'mailto' : $contact['type']).'-alt">
						<span class="label">'.($contact['label'] ?: 'Alt '.ucfirst($contact['type'])).'</span>'
						.create_button($contact['value'], 0, $contact['value'], 'value').
					'</li>';
				}

				$html .= '</ul>';
			$html .= '</div>';

		//Business hours
		if ($loc['show_hours']) {
			$grouped_hours = group_hours($loc['location_hours']);

			$html .= '<div class="contact-hours">
				<h4 class="hours-title">Hours of Operation</h4>';
			
				$today   = $loc['location_hours'][date('N')-1];
				$now     = strtotime('now');
				$open    = strtotime($today['start_time']);
				$closed  = strtotime($today['end_time']);
				$closing = strtotime($today['end_time'].' - 1 hour');
				
				// Current status
				if ($today['closed'] || $now < $open || $now >= $closed) {
					$html .= '<p class="open-text closed">'.($loc['closed_text'] ?: 'Currently Closed').'</p>';
				} else if ($now >= $closing) {
					$html .= '<p class="open-text closing">'.($loc['closing_text'] ?: 'Closing Soon').'</p>';
				} else {
					$html .= '<p class="open-text open">'.($loc['open_text'] ?: 'Currently Open').'</p>';
				}

				$html .= '<ul class="hours-list">';
				foreach($grouped_hours as $hours){
					$label = substr($hours['start_day'], 0, 3);
					$timeframe = date('g:ia', strtotime($hours['start_time'])).' - '.date('g:ia', strtotime($hours['end_time']));

					if($hours['end_day']){
						$start_day_index = array_search($hours['start_day'], ($weekdays ?? []));
						$end_day_index = array_search($hours['end_day'], ($weekdays ?? []));
						$label_separator = ($start_day_index !== false && $end_day_index !== false ? ($end_day_index - $start_day_index === 1 ? ' & ' : ' - ') : ' - ');
						$label .= $label_separator. substr($hours['end_day'], 0, 3);
					}

					$html .= '<li>';
						$html .= '<span class="label">'.$label.'</span>';
						$html .= '<span class="value">'.($hours['closed'] ? 'Closed' : $timeframe).'</span>';
					$html .= '</li>';
				}
				$html .= '</ul>';

				$html .= ($loc['hours_disclaimer'] ? '<small class="hours-disclaimer">' .$loc['hours_disclaimer']. '</small>' : '');
			$html .= '</div>';
		}

		$html .= '</div>';

		// Set tab attributes
		if (GOOGLE_MAP && $loc['google_map']) {
			$attrs = ' data-search="'.$loc['full_address'].'"';
			$attrs .= ' data-lat="'.$loc['gpslat'].'"';
			$attrs .= ' data-lng="'.$loc['gpslong'].'"';
			$attrs .= ' data-zoom="'.$loc['zoom'].'"';
			$attrs .= ' data-map="1"';
		} else {
			$attrs = ' data-map="0"';
		}

		$contact_tabs[] = [
			'tab_id' => $loc['location_id'],
			'title' => $loc['location_name'],
			'content' => $html,
			'page' => 'location',
			'attrs' => $attrs,
		];

		unset($loc);
	}

	//Add to panel
	if(isset($page['page_panels'][2])){
		//Set panel tab content
		if(count($global['locations']) > 1){
			$page['page_panels'][2]['panel_tabs_id'] = 'contact-locations';
			$page['page_panels'][2]['panel_tabs'] = array_merge($page['page_panels'][2]['panel_tabs'], $contact_tabs);

		//Set panel content
		}else{
			$page['page_panels'][2]['content'] .= '<div id="contact-locations">'.$contact_tabs[0]['content'].'</div>';
		}
	}
}

//Contact form
include(include_path('includes/templates/contact-form.php'));

//Set panel content
if(isset($page['page_panels'][1])){
	$page['page_panels'][1]['class'] = ($page['page_panels'][1]['class'] ?? '').' contact-form-panel';
	$page['page_panels'][1]['append_content'] = $html;
}

?>