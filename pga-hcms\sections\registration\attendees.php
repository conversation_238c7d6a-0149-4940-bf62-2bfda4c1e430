<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Search form
if(ACTION == '') {

	if(isset($_GET['advanced_search'])) {
		echo "<div id='listings-extra-buttons'>";

			//Export
			echo "<form method='post' action='".$path."exports/export-attendees.php?".$_SERVER['QUERY_STRING']."' target='_blank' class='f_right'>";
				echo "<button type='submit' name='export' class='button'><i class='fa fa-download'></i>Export</button>";
				echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
			echo "</form>";

			//Mass Mail
			echo "<form method='post' action='' class='f_right'>";
				echo "<button type='button' name='show_massmail' id='show_massmail' class='button'><i class='fa fa-envelope'></i>Mass Mail</button>";
				echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
			echo "</form>";
			echo "<div class='hidden'>";
				echo "<div id='massmail_modal' title='Mass Mail Attendees'>";
					echo "<form id='massmail_form' method='post' action=''>";
						$default_email = "<h3>Hello,</h3>";
						$default_email .= "<p>This is a general message for selected attendees. Please alter this email to your liking.</p>";
						echo "<p><label>Email Content <small>(This is the body of the email that will send to each selected $record_name)</small></label></p>
						<textarea name='message' class='tinymceEmail'>".(isset($row['message']) && $row['message'] != '' ? $row['message'] : $default_email)."</textarea>";
						echo "<button type='submit' name='massmail' class='button f_right'><i class='fa fa-send'></i>Send</button>";
						echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
						echo "<input type='hidden' name='keep_tags[]' value='message' />";
						foreach($search_results as $attendee) {
							echo "<input type='hidden' name='attendee_emails[]' value='".$attendee['attendee_id']."' />";
						}
					echo "</form>";
				echo "</div>";
			echo "</div>";

		echo "</div>";
	}

	//Search Form
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Search Attendees
			<span class='f_right'><a class='panel-toggle fa fa-chevron-".($show_search_form ? "up" : "down")."'></a></span>
		</div>";

		echo "<div class='panel-content clearfix' ".($show_search_form ? "" : "style='display:none;'").">";
			echo "<form id='advanced-search-form' class='clearfix' action='' method='get' enctype='multipart/form-data'>";
				echo "<div id='search-fields' class='column clearfix'>";
					echo "<p><strong>Search/Filter:</strong></p>";

					//Search fields
					echo "<div class='form-field'>
						<label>First Name </label>
						<input type='text' name='filter[reg_attendees][first_name]' value='".(isset($_GET['filter']['reg_attendees']['first_name']) ? $_GET['filter']['reg_attendees']['first_name'] : "")."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Last Name </label>
						<input type='text' name='filter[reg_attendees][last_name]' value='".(isset($_GET['filter']['reg_attendees']['last_name']) ? $_GET['filter']['reg_attendees']['last_name'] : "")."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Email </label>
						<input type='text' name='filter[reg_attendees][email]' value='".(isset($_GET['filter']['reg_attendees']['email']) ? $_GET['filter']['reg_attendees']['email'] : "")."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Registration Status </label>
						<select name='filter[reg_attendees][reg_status]' class='select'>";
							foreach($reg_statuses as $status){
								echo "<option value='" .$status. "'".(isset($_GET['filter']['reg_attendees']['reg_status']) && $_GET['filter']['reg_attendees']['reg_status'] == $status ? " selected" : "").">" .$status. "</option>";
							}
						echo "</select>
					</div>";

					echo "<br class='clear'/>";

					echo "<div class='form-field'>
						<label>Event/Tournament</label>
						<input type='text' name='filter[reg_events][name]' value='".(isset($_GET['filter']['reg_events']['name']) ? $_GET['filter']['reg_events']['name'] : "")."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Start Date</label>
						<input type='text' name='filter[reg_occurrences][start_date]' value='".(isset($_GET['filter']['reg_occurrences']['start_date']) ? $_GET['filter']['reg_occurrences']['start_date'] : "")."' class='input datepicker' autocomplete='off' />
					</div>";

					echo "<div class='form-field'>
						<label>End Date</label>
						<input type='text' name='filter[reg_occurrences][end_date]' value='".(isset($_GET['filter']['reg_occurrences']['end_date']) ? $_GET['filter']['reg_occurrences']['end_date'] : "")."' class='input datepicker' autocomplete='off' />
					</div>";

				echo "</div>"; //Search fields

				//View columns
				echo "<div id='view_fields' class='column clearfix'>";
					echo "<p><strong>View Columns:</strong></p>";

					echo "<div class='f_left'>";

						echo "<div>
							<input type='checkbox' name='column[reg_events][name]' id='column_event_name' class='checkbox' value='".EVENT_CODE."' ".((isset($_GET['column']['reg_events']['name']) && $_GET['column']['reg_events']['name']) || (in_array('reg_events.name', $db_columns) || !isset($_GET['advanced_search'])) ? "checked" : "")." />
							<label for='column_event_name'>".EVENT_CODE."</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_occurrences][occurrence_id]' id='column_occurrence_id' class='checkbox' value='".OCCURRENCE_CODE."' ".((isset($_GET['column']['reg_occurrences']['occurrence_id']) && $_GET['column']['reg_occurrences']['occurrence_id']) || (in_array('reg_occurrences.occurrence_id', $db_columns) || !isset($_GET['advanced_search'])) ? "checked" : "")." />
							<label for='column_occurrence_id'>".OCCURRENCE_CODE."</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='attendee_name' id='attendee_name' class='checkbox' value='' checked disabled />
							<label for='attendee_name'>Name</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][account_id]' id='column_account_id' class='checkbox' value='Account No.' ".(isset($_GET['column']['reg_attendees']['account_id']) && $_GET['column']['reg_attendees']['account_id'] ? "checked" : "")." />
							<label for='column_account_id'>Account No.</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][reg_status]' id='column_reg_status' class='checkbox' value='Status' ".(isset($_GET['column']['reg_attendees']['reg_status']) && $_GET['column']['reg_attendees']['reg_status'] || !isset($_GET['advanced_search']) ? "checked" : "")." />
							<label for='column_reg_status'>Status</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][email]' id='column_email' class='checkbox' value='Email' ".(isset($_GET['column']['reg_attendees']['email']) && $_GET['column']['reg_attendees']['email'] || !isset($_GET['advanced_search']) ? "checked" : "")." />
							<label for='column_email'>Email</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][phone]' id='column_phone' class='checkbox' value='Phone' ".(isset($_GET['column']['reg_attendees']['phone']) && $_GET['column']['reg_attendees']['phone'] ? "checked" : "")." />
							<label for='column_phone'>Phone</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][company]' id='column_company' class='checkbox' value='Company' ".(isset($_GET['column']['reg_attendees']['company']) && $_GET['column']['reg_attendees']['company'] ? "checked" : "")." />
							<label for='column_company'>Company</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][facility]' id='column_facility' class='checkbox' value='Facility' ".(isset($_GET['column']['reg_attendees']['facility']) && $_GET['column']['reg_attendees']['facility'] ? "checked" : "")." />
							<label for='column_facility'>Facility</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][position]' id='column_position' class='checkbox' value='Position' ".(isset($_GET['column']['reg_attendees']['position']) && $_GET['column']['reg_attendees']['position'] ? "checked" : "")." />
							<label for='column_position'>Position</label>
						</div>";

					echo "</div>";
					echo "<div class='f_right'>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][comments]' id='column_comments' class='checkbox' value='Comments' ".(isset($_GET['column']['reg_attendees']['comments']) && $_GET['column']['reg_attendees']['comments'] ? "checked" : "")." />
							<label for='column_comments'>Comments</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][gender]' id='column_gender' class='checkbox' value='Gender' ".(isset($_GET['column']['reg_attendees']['gender']) && $_GET['column']['reg_attendees']['gender'] ? "checked" : "")." />
							<label for='column_gender'>Gender</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][handicap]' id='column_handicap' class='checkbox' value='Handicap' ".(isset($_GET['column']['reg_attendees']['handicap']) && $_GET['column']['reg_attendees']['handicap'] ? "checked" : "")." />
							<label for='column_handicap'>Handicap</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][ticket_type]' id='column_ticket_type' class='checkbox' value='Price Type' ".(isset($_GET['column']['reg_attendees']['ticket_type']) && $_GET['column']['reg_attendees']['ticket_type'] ? "checked" : "")." />
							<label for='column_ticket_type'>Price Type</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][ticket_price]' id='column_ticket_price' class='checkbox' value='Price' ".(isset($_GET['column']['reg_attendees']['ticket_price']) && $_GET['column']['reg_attendees']['ticket_price'] ? "checked" : "")." />
							<label for='column_ticket_price'>Price</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][discount]' id='column_discount' class='checkbox' value='Discount' ".(isset($_GET['column']['reg_attendees']['discount']) && $_GET['column']['reg_attendees']['discount'] ? "checked" : "")." />
							<label for='column_discount'>Discount</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_registrations][promocode]' id='column_promocode' class='checkbox' value='Discount Code' ".(isset($_GET['column']['reg_registrations']['promocode']) && $_GET['column']['reg_registrations']['promocode'] ? "checked" : "")." />
							<label for='column_promocode'>Discount Code</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_registrations][paid]' id='column_paid' class='checkbox' value='Paid' ".(isset($_GET['column']['reg_registrations']['paid']) && $_GET['column']['reg_registrations']['paid'] ? "checked" : "")." />
							<label for='column_paid'>Paid</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_attendees][date_withdrawn]' id='column_date_withdrawn' class='checkbox' value='Date Withdrawn' ".(isset($_GET['column']['reg_attendees']['date_withdrawn']) && $_GET['column']['reg_attendees']['date_withdrawn'] ? "checked" : "")." />
							<label for='column_date_withdrawn'>Date Withdrawn</label>
						</div>";

						echo "<div>
							<input type='checkbox' name='column[reg_registrations][registration_date]' id='column_registration_date' class='checkbox' value='Date Registered' ".(isset($_GET['column']['reg_registrations']['registration_date']) && $_GET['column']['reg_registrations']['registration_date'] ? "checked" : "")." />
							<label for='column_registration_date'>Date Registered</label>
						</div>";

					echo "</div>";

				echo "</div>"; //View Columns

				echo "<div class='buttons-wrapper'>";
					echo "<button type='submit' name='advanced_search' value='advanced_search' class='button f_right'><i class='fa fa-search'></i>Search</button>";
					echo "<a class='button f_left reset' href='".PAGE_URL."'><i class='fa fa-times'></i>Clear</a>";
				echo "</div>";
			echo "</form>";
		echo "</div>";
	echo "</div>"; //Search form

	//Listing
	if(isset($search_results)) {
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Search Result
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";

			echo "<div class='panel-content clearfix nopadding autoscroll'>";
				if(!empty($search_results)) {
					echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter nopager'>";
						echo "<thead>";
							echo "<tr>";
								echo "<th width='120px' class='{sorter:false}'>Mass Mail ".$CMSBuilder->tooltip('Mass Mail', 'All checked ' .$record_name. 's will be included in the mass mail. Any ' .$record_name. 's that are unchecked will not be contacted.')."</th>";
								if(!empty($table_columns)) {
									foreach($table_columns as $key => $column) {
										if($column == EVENT_CODE) {
											echo "<th>".$column."</th>";
										}else if($column == OCCURRENCE_CODE){
											echo "<th class='{sorter:\"monthDayYear\"}'>".$column."</th>";
										}
									}
								}
								echo "<th>Name</th>";
								if(!empty($table_columns)) {
									foreach($table_columns as $key => $column) {
										if($column != EVENT_CODE && $column != OCCURRENCE_CODE) {
											if($column == 'Paid'){
												echo "<th class='center'>".$column."</th>";
											}else{
												echo "<th>".$column."</th>";
											}
										}
									}
								}
								echo "<th class='{sorter:false}'>&nbsp;</th>";
							echo "</tr>";
						echo "</thead>";

						echo "<tbody>";
							foreach($search_results as $attendee) {
								echo "<tr>";
									echo "<td><input type='checkbox' name='email_attendee[]' value='".$attendee['attendee_id']."' id='attendee_".$attendee['attendee_id']."' class='checkbox massmail_recipient' checked /><label for='attendee_".$attendee['attendee_id']."'></label></td>";
									if(!empty($alias_columns)) {
										foreach($alias_columns as $key => $column) {
											if($column == 'name' || $column == 'occurrence_id') {
												echo "<td>";
													if($column == 'occurrence_id') {
														echo ($attendee['occurrence_name'] != "" ? $attendee['occurrence_name']."<br />" : "").
														format_date_range($attendee['start_date'], $attendee['end_date']);
													} else {
														echo $attendee[$column];
													}
												echo "</td>";
											}
										}
									}
									echo "<td>".$attendee['first_name']." ".$attendee['last_name'].(!empty($attendee['partner_id']) ? "<br /><small>(Partner)</small>" : "")."</td>";
									if(!empty($alias_columns)) {
										foreach($alias_columns as $key => $column) {
											if($column != 'name' && $column != 'occurrence_id') {
												if($column == 'registration_date' || $column == 'date_withdrawn') {
													echo "<td>".($attendee[$column] != '' && $attendee[$column] != '0000-00-00' ? date('M j, Y', strtotime($attendee[$column])) : "")."</td>";
												} else if($column == 'ticket_price' || $column == 'discount') {
													echo "<td>$".number_format($attendee[$column], 2)."</td>";
												} else if($column == 'paid') {
													echo "<td class='center'>" .($attendee[$column] == '1' ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
												} else {
													echo "<td>".$attendee[$column]."</td>";
												}
											}
										}
									}
									$editlink = explode('&action=', $_SERVER['REQUEST_URI']);
									echo "<td class='right'><a href='" .$editlink[0]. "&action=edit&item_id=" .$attendee[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
								echo "</tr>";
							}
						echo "</tbody>";
					echo "</table>";

					echo "<div class='pager'><div class='pagedisplay'>Displaying 1 - " .count($search_results). " (" .count($search_results). " Total)</div></div>";

				} else {
					echo "<div class='instructions-text'><em>Your search did not return any results. Please try again.</em></div>";
				}
			echo "</div>";
		echo "</div>";
	}


//Edit attendee
} else if(ACTION == 'edit') {

	$data = $records_arr[ITEM_ID];
	if(!isset($_POST['save'])){
		$row = $data;
	}

	if(!(isset($occurrence['end_date']) && $occurrence['end_date'] < date("Y-m-d"))){
		echo $CMSBuilder->important('<strong class="error">PLEASE NOTE: Changing the ticket type and addons can result to a change in the registration\'s price.</strong> <br/><br/>In which case, any applied discounts would need to be manually recalculated. If the registrant has already paid, they will need to be refunded if the new price is lower than the original price, <u>or</u> invoiced if the new price is higher than the original price. <br/><br/><u>Please review the pricing of the registration when making this change.</u>');
	}

	echo "<form id='attendee_form' action='' method='post' enctype='multipart/form-data'>";

		//Registration details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Registration Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
					echo "<tr>
						<td width='150px'>Registration No:</td>
						<td><a href='" .$regpage['page_url']. "?action=edit&item_id=" .$row['registration_id']. "'>".$row['registration_number']."</a></td>
					</tr>";
					echo "<tr>
						<td>Registration Date:</td>
						<td>".date('M j, Y', strtotime($row['registration_date']))."</td>
					</tr>";
					echo "<tr>
						<td>" .EVENT_CODE. ":</td>
						<td>".$occurrence['name']."</td>
					</tr>";
					echo "<tr>
						<td>" .OCCURRENCE_CODE. ":</td>
						<td>".(trim($occurrence['occurrence_name']) != "" ? $occurrence['occurrence_name'].", " : "").format_date_range($occurrence['start_date'], $occurrence['end_date'])."</td>
					</tr>";
					if(isset($row['reg_status']) && $row['reg_status'] == 'Withdrawn'){
						echo "<tr>
							<td>Withdrawal Date:</td>
							<td>".date('M j, Y', strtotime($row['date_withdrawn']))."</td>
						</tr>";
					}
					if(!empty($row['updated_by'])){
						$updated_by = $Account->get_account_profile($row['updated_by']);
						echo "<tr>
							<td>Updated By:</td>
							<td>" .$updated_by['first_name']." ".$updated_by['last_name']."</td>
						</tr>";
					}
					echo "<tr>
						<td>Ticket Type:</td>
						<td>";
							echo "<select name='pricing_id' class='select nomargin f_left'" .(isset($occurrence['end_date']) && $occurrence['end_date'] < date("Y-m-d") ? ' disabled' : ''). ">";
								echo "<optgroup label='Current Value'>";
									echo "<option value=''".(($row['pricing_id'] ?? '') == '' ? " selected" : "").">" .$data['ticket_type']. " ($".number_format($data['ticket_price'], 2).")</option>";
								echo "</optgroup>";

								$other_types = '';
								foreach($ticket_types as $ticket){
									if($ticket['price_type'] != $data['ticket_type'] && $ticket['price'] != $data['ticket_price']){
										$other_types .= "<option value='" .$ticket['pricing_id']. "'".(($row['pricing_id'] ?? '') == $ticket['pricing_id'] ? " selected" : "").">" .$ticket['price_type']." ($".number_format($ticket['price'], 2).")". "</option>";
									}
								}

								if($other_types){
									echo "<optgroup label='Change To'>";
										echo $other_types;
									echo "</optgroup>";
								}
							echo "</select>";
						echo "</td>
					</tr>";
					echo "<tr>
						<td>Registration Status:</td>
						<td>";
						if(isset($row['reg_status']) && $row['reg_status'] == 'Incomplete'){
							echo "Incomplete";
						}else{
							echo "<select id='reg_status' name='reg_status' class='select nomargin f_left'" .(isset($occurrence['end_date']) && $occurrence['end_date'] < date("Y-m-d") ? ' disabled' : ''). ">";
								foreach($reg_statuses as $status){
									if($status != 'Incomplete'){
										echo "<option value='" .$status. "'".(isset($row['reg_status']) && $row['reg_status'] == $status ? " selected" : "").">" .$status. "</option>";
									}
								}
							echo "</select>";
						}
						echo "</td>
					</tr>";
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Registration Information

		//Attendee Information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";

			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
					if(!empty($row['account_id'])){
						echo "<tr>
							<td>Account No:</td>
							<td><a href='" .$accountpage['page_url']. "?action=edit&item_id=" .$row['account_id']. "'>" .$row['account_id']."</a></td>
						</tr>";
					}
					echo "<tr>
						<td width='150px'>$record_name:</td>
						<td>".$row['first_name']." ".$row['last_name']."</td>
					</tr>";
					if(trim($row['facility']) != ""){
						echo "<tr>
							<td>Facility:</td>
							<td>".$row['facility']."</td>
						</tr>";
					}
					if(trim($row['email']) != ""){
						echo "<tr>
							<td>Email:</td>
							<td><input type='text' name='email' class='input nomargin' value='" .$row['email']. "' /></td>
						</tr>";
					}
					if(trim($row['phone']) != ""){
						echo "<tr>
							<td>Phone:</td>
							<td>".$row['phone']."</td>
						</tr>";
					}
					if(trim($row['company']) != ""){
						echo "<tr>
							<td>Company:</td>
							<td>".$row['company']."</td>
						</tr>";
					}
					if(trim($row['positon'] ?? '') != ""){
						echo "<tr>
							<td>Position:</td>
							<td>".$row['position']."</td>
						</tr>";
					}
					if(trim($row['comments']) != ""){
						echo "<tr>
							<td>Comments:</td>
							<td>".$row['comments']."</td>
						</tr>";
					}
					if($occurrence['event_type'] == 2){
						echo "<tr>
							<td>Gender:</td>
							<td>".$row['gender']."</td>
						</tr>";
						echo "<tr>
							<td>Handicap:</td>
							<td><input type='text' name='handicap' class='input input_sm nomargin' value='".$row['handicap']."' /></td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Attendee Information

		//Partner
		if(!empty($row['partner'])){

			echo "<div class='panel'>";
					echo "<div class='panel-header'>Partner Information
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";
					echo "<div class='panel-content nopadding'>";
						echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
							echo "<tr>
								<td width='150px'>Partner:</td>
								<td><a href='" .PAGE_URL. "?action=edit&item_id=" .$row['partner']['attendee_id']. "'>".$row['partner']['first_name']." ".$row['partner']['last_name']."</a></td>
							</tr>";
						echo "</table>";
				echo "</div>";
			echo "</div>";

		}else{

			//Select partner
			if($row['reg_status'] == 'Registered' && empty($row['partner']) && $occurrence['event_type'] == 2 && $occurrence['team_event'] && $occurrence['end_date'] >= date("Y-m-d")){

				echo "<div class='panel'>";
					echo "<div class='panel-header'>Partner Information
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";

					echo "<div class='panel-content clearfix'>";

						$eligible_attendees = $Registration->get_eligible_attendees($occurrence['occurrence_id']);
						echo "<div class='form-field'>
							<label>Select Partner (Eligible Member)</label>
							<select name='partner_id' class='select'>
								<option value=''>- Select -</option>";
								foreach($eligible_attendees as $eligible){
									if(empty($eligible['facility_name'])){
										$eligible['facility_name'] = "No Facility";
									}
									echo "<option value='" .$eligible['account_id']."'" .(isset($_POST['partner_id']) && $_POST['partner_id'] == $eligible['account_id'] ? " selected" : ""). ">" .$eligible['last_name'].", ".$eligible['first_name']. " - ".$eligible['facility_name']. "</option>";
								}
							echo "</select>
						</div>
						<div class='form-field'>
							<label><strong>OR</strong> Enter Partner Name (Non-Member)</label>
							<input type='text' name='partner_first_name' class='input' value='" .(isset($_POST['partner_first_name']) ? $_POST['partner_first_name'] : ""). "' placeholder='First Name' />
						</div>
						<div class='form-field'>
							<label>&nbsp;</label>
							<input type='text' name='partner_last_name' class='input' value='" .(isset($_POST['partner_last_name']) ? $_POST['partner_last_name'] : ""). "' placeholder='Last Name' />
						</div>
						<div class='form-field'>
							<label>Handicap</label>
							<input type='text' name='partner_handicap' class='input input_sm' value='" .(isset($_POST['partner_handicap']) ? $_POST['partner_handicap'] : ""). "' />
						</div>";

					echo "</div>";
				echo "</div>";

			}
		}

		//Attendee Extras
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Extras
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";

			echo "<div class='panel-content nopadding clearfix'>";
				if($occurrence_addons || $deleted_addons){
					//Available addons
					if($occurrence_addons){
						echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
						foreach($occurrence_addons as $addon){
							$current_option = $selected_addons_by_name[$addon['name']] ?? NULL;
							$other_options = '';

							foreach($addon['options'] as $option){
								if(
									!$current_option
									|| ($option['name'] != $current_option['name'] && $option['price_adjustment'] != $current_option['price_adjustment'])
								){
									$other_options .= "<option value='".$option['option_id']."'".(($row['update_addon'][$addon['addon_id']] ?? '') == $option['option_id'] ? " selected" : "").">".$option['name']." ($".number_format($option['price_adjustment'], 2).")</option>";
								}
							}

							echo "<tr>";
								echo "<td width='150px'>" .$addon['name']. ":</td>";
								echo "<td>";
									echo "<select class='select nomargin' name='update_addon[".$addon['addon_id']."]'".((isset($occurrence['end_date']) && $occurrence['end_date'] < date("Y-m-d") ? ' disabled' : '')).">";
										echo "<optgroup label='Current Value'>";
										if($current_option){
											echo "<option value=''".(($row['update_addon'][$addon['addon_id']] ?? '') == '' ? " selected" : "").">".$current_option['value']." ($".number_format($current_option['price_adjustment'], 2).")</option>";
										} else {
											echo "<option value=''>None</option>";
										}
										echo "</optgroup>";

										if($other_options){
											echo "<optgroup label='Change To'>";
												echo $other_options;
											echo "</optgroup>";
										}
									echo "</select>";
									echo ($current_option ? "<input type='hidden' name='current_option_id[".$addon['addon_id']."]' value='".$current_option['option_id']."'/>" : "");
								echo "</td>";
							echo "</tr>";
						}
						echo "</table>";
					}

					//Deleted addons
					if($deleted_addons){
						echo ($occurrence_addons ? "<hr style='margin: 20px 0;'/>" : "");
						echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
							echo "<tr><td colspan='2' class='error'><strong>The addon(s) below are no longer available for this event.</strong></td></tr>";
							foreach($deleted_addons as $addon){
								echo "<tr>";
									echo "<td width='150px'>" .$addon['name']. ":</td>";
									echo "<td>";
										echo $addon['value']." ($".number_format($addon['price_adjustment'], 2).")";
										echo "<small class='block' style='margin-top: 5px;'>";
											echo "<input id='delete-addon-".$addon['option_id']."' type='checkbox' class='checkbox' name='delete_addon[]' value='".$addon['option_id']."'".((isset($occurrence['end_date']) && $occurrence['end_date'] < date("Y-m-d") ? ' disabled' : ''))."/>";
											echo "<label for='delete-addon-".$addon['option_id']."'>Delete</label>";
										echo "</small>";
									echo "</td>";
								echo "</tr>";
							}
						echo "</table>";
					}
				} else {
					echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'><tr><td><em>No extras to display for this attendee.</em></td></tr></table>";
				}
			echo "</div>";
		echo "</div>"; //Attendee Extras

		//Comments
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Notes ".$CMSBuilder->tooltip('Notes', 'Notes are for internal use only. They will not appear on the front-end of the website.')."
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<textarea name='notes' class='textarea'>".(isset($row['notes']) ? $row['notes'] : "")."</textarea>";
			echo "</div>";
		echo "</div>"; //Comments

		//Sticky footer
		$backlink = explode('action=', $_SERVER['REQUEST_URI']);
		echo "<footer id='cms-footer' class='resize'>";
		echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
		echo "<a href='".substr($backlink[0], 0, -1)."' class='cancel'>Cancel</a>";
		echo "</footer>";

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";


//New Registration
} else if(ACTION == 'add') {

	include("sections/registration/register.php");

}

?>