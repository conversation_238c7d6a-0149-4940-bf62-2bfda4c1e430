<?php

//Account navigation
$navmenu = '<nav id="account-navigation">
	<ul>';
	foreach($navigation as $nav){
		if($nav['page_id'] == $_sitepages['account']['page_id']){

			//Add logout button
			array_push($nav['sub_pages'], array(
				'page_id' => NULL,
				'name' => 'Logout',
				'url' => '#',
				'page_url' => '#',
				'class' => 'logout-btn',
				'icon' => 'fas fa-sign-out-alt',
				'type' => 0
			));

			//Display account sections
			foreach($nav['sub_pages'] as $subnav){
				// $navmenu .= build_menu($subnav);
				// $navmenu .= '<li><a href="' .$subnav['page_url']. '" class="' .($subnav['page_id'] == PAGE_ID ? 'active' : ''). '"><i class="' .$subnav['icon']. '"></i>' .$subnav['name']. '</li>';
				if($subnav['name'] == 'Logout'){
					$subnav['page_url'] = $path.'modules/account/Logout.php';
				}
				$navmenu .= '<li><a href="' .$subnav['page_url']. '" class="' .($subnav['page_id'] == PAGE_ID ? 'active' : ''). '">' .$subnav['name']. '</li>';
			}
			break;
		}
	}
	$navmenu .= '</ul>
</nav>';

//Alerts
// $html = (isset($alert) ? $alert : '');

//Account alerts
if(PAGE_ID != $_sitepages['account']['page_id']){
	$html = (isset($alert) ? $alert : '');
	if(ACTION == '' || $notfound){
		// $html .= '<p><a href="' .$_sitepages['account']['page_url']. '"><i class="fa fa-user"></i>&nbsp; Back to ' .$_sitepages['account']['name']. '</a></p>';	
	}else{
		$html .= '<p><a href="' .$page['page_url']. '">&lsaquo; Back to All</a></p>';
	}
}

//Include file as content string
ob_start();
switch((!is_null(PAGE_ID) ? PAGE_ID : PARENT_ID)){

	case $_sitepages['register']['page_id']:
		include("pages/account/register.php");
	break;

	case $_sitepages['login']['page_id']:
		include("pages/account/login.php");
	break;

	case $_sitepages['reset']['page_id']:
		include("pages/account/reset.php");
	break;

	case $_sitepages['profile']['page_id']:
		include("pages/account/profile.php");
	break;

	case $_sitepages['account-settings']['page_id']:
        include("pages/account/settings.php");
    break;

	case $_sitepages['change-password']['page_id']:
        include("pages/account/change-password.php");
    break;

	case $_sitepages['edit-facility']['page_id']:
        include("pages/account/facility.php");
    break;

	case $_sitepages['my-job-postings']['page_id']:
       	include("pages/account/careers.php");
    break;

	case $_sitepages['my_classifields']['page_id']:
		include("pages/account/classifieds.php");
	break;

	case $_sitepages['billing-profiles']['page_id']:
		include("pages/account/billingprofiles.php");
	break;

	case $_sitepages['invoices']['page_id']:
		include("pages/account/invoices.php");
	break;

	case $_sitepages['payments']['page_id']:
		include("pages/account/payments.php");
	break;

	case $_sitepages['hole-in-one']['page_id']:
		include("pages/account/holeinone.php");
	break;

	case $_sitepages['my-registrations']['page_id']:
		include("pages/account/registrations.php");
	break;

	case $_sitepages['withdrawal']['page_id']:
		include("pages/account/withdrawal.php");
	break;

	case $_sitepages['message-centre']['page_id']:
		include("pages/account/messagecentre.php");
	break;

	case $_sitepages['compensation-survey']['page_id']:
		include("pages/account/compensationform.php");
	break;

	default:
		// echo '<h2>Hii, ' .$Account->first_name. '!</h2>';
		// echo $navmenu;
		include("pages/account/dashboard.php");

	break;
}
$html .= ob_get_clean();

//Set panel content
if(isset($panel_id) && array_key_exists($panel_id, $page['page_panels'])){
	$page['page_panels'][$panel_id]['content'] .= $html;
}else{
	array_unshift($page['page_panels'], array(
		'panel_id' => 0,
		'panel_type' => 'standard',
		'title' => '',
		'subtitle' => '',
		'show_title' => 0,
		'content' => $html,
		'theme' => 'default',
		'google_map' => 0
	));
}

//Page panels
include("includes/pagepanels.php");

?>