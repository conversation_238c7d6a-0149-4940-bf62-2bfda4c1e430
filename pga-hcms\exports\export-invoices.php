<?php  

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../includes/utils.php");

if(isset($_GET) && USER_LOGGED_IN){

	//Define vars
	$record_db = 'invoices';
	$record_id = 'invoice_id';
	$record_name = 'Invoice';
	$records_arr = array();

	$db_columns = array(); //for SELECT in query
	$table_columns = array(); //for listing label
	$alias_columns = array(); //for listing value
	$rearrange_columns = false;

	$params = array();
	$wheretxt = "";
	$querytxt = "";

	//Set columns to get
	$db_columns = array(
		$record_db.'.invoice_number',
		$record_db.'.invoice_date',
		$record_db.'.bill_to',
		$record_db.'.account_id',
		$record_db.'.first_name',
		$record_db.'.last_name',
		$record_db.'.email',
		$record_db.'.phone',
		$record_db.'.taxes',
		$record_db.'.invoice_total',
		$record_db.'.due_date',
		$record_db.'.paid',
		$record_db.'.comments'
	);
	$table_columns = array(
		'Invoice No.',
		'Invoice Date',
		'Bill To',
		'Account No.',
		'First Name',
		'Last Name',
		'Email Address',
		'Phone No.',
		'Taxes',
		'Invoice Total',
		'Due Date',
		'Paid',
		'Comments'
	);
	foreach($db_columns as $key => $column) {
		$alias_columns[$key] = substr($column, (strpos($column, '.')+1));
	}

	//Get records
	$params = array('Active');
	$wheretxt = " WHERE `$record_db`.`status` = ?";
	
	//Search term
	if(isset($_GET['search']) && $_GET['search'] != ""){
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."(`$record_db`.`invoice_number` LIKE ? OR `$record_db`.`email` LIKE ? OR `$record_db`.`phone` LIKE ? OR `$record_db`.`bill_to` LIKE ?)";
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
	}

	//Filter by dates
	$date_range = '';
	if(isset($_GET['start_date']) && $_GET['start_date'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`invoice_date` >= ?";
		$params[] = date('Y-m-d 00:00:00', strtotime($_GET['start_date']));
		$date_range .= date('M j, Y', strtotime($_GET['start_date']));
	}
	if(isset($_GET['end_date']) && $_GET['end_date'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`invoice_date` <= ?";
		$params[] = date('Y-m-d 23:59:59', strtotime($_GET['end_date']));
		$date_range .= (!empty($date_range) ? ' - ' : '').date('M j, Y', strtotime($_GET['end_date']));
	}
	if(isset($_GET['status']) && $_GET['status'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`paid` = ?";
		$params[] = $_GET['status'];
	}

	//Create query
	$querytxt .= "SELECT $record_db.* ";
	$querytxt .= "FROM $record_db ";
	$querytxt .= $wheretxt;
	$querytxt .= " GROUP BY $record_db.$record_id";
	$querytxt .= " ORDER BY $record_db.invoice_date ASC";
	$query = $db->query($querytxt, $params);
	if($query && !$db->error() && $db->num_rows() > 0){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}

	//Compile records
	$csv_rows = array();
	foreach($records_arr as $row) {
		$data = array();
		foreach($alias_columns as $key => $column) {
			if($column == 'invoice_date' || $column == 'due_date') {
				$data[] = ($row[$column] != '' && $row[$column] != '0000-00-00' && $row[$column] != '0000-00-00 00:00:00' ? date('Y-m-d', strtotime($row[$column])) : "");
			} else if($column == 'taxes' || $column == 'invoice_total') {
				$data[] = '$'.number_format($row[$column],2);
			} else if($column == 'status' || $column == 'paid') {
				$data[] = ($row[$column] == '1' ? 'Yes' : 'No');
			} else {
				$data[] = $row[$column];
			}
		}
		$csv_rows[] = $data;
	}

	//Output CSV
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=invoices-".date("Ymd").".csv");
	header("Content-Transfer-Encoding: binary ");

	$fp = fopen('php://output', 'w');
	
	//Data
	fputcsv($fp, str_replace("&rsquo;", "'", $table_columns));
	foreach($csv_rows as $row) {
		fputcsv($fp, str_replace("&rsquo;", "'", $row));
	}
	fputcsv($fp, array(''));
	
	//Footer
	$footer = array('Date Exported: ', date('M j, Y'));
	fputcsv($fp, $footer);
	if(!empty($date_range)){
		$footer = array('Date Filter: ', $date_range);
		fputcsv($fp, $footer);
	}
	if(isset($_GET['search']) && $_GET['search'] != ""){
		$footer = array('Search Filter: ', '`'.str_replace("&rsquo;", "'", $_GET['search']).'`');
		fputcsv($fp, $footer);
	}
	
	fclose($fp);

} 
?>