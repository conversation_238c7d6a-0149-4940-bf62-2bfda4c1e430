<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}



//Dashboard widget
// if(SECTION_ID == 4) {
if(SECTION_ID ==  $_cmssections['dashboard']){
	$get_total = $db->query("SELECT COUNT(*) AS `total` FROM `reg_categories` WHERE `reg_system_id` = ".$reg_settings['reg_system_id']." AND `status` = ?", array('Active'));
	if($get_total && !$db->error()) {
		$total_records = $db->fetch_array();
		$total_records = $total_records[0]['total'];
		$CMSBuilder->set_widget($_cmssections['registration-categories'], 'Total Categories', $total_records);
	}
}

// //Dashboard widget
// if(SECTION_ID ==  $_cmssections['dashboard']){
// 	$total_records = $db->get_record_count('resources');
// 	$CMSBuilder->set_widget($_cmssections['resources'], 'Total Member Resources', $total_records, 'files-o');
// }


// if(SECTION_ID == 55) {
if(SECTION_ID == $_cmssections['registration-categories']){
	
	// Define vars
	$record_db = 'reg_categories';
	$record_id = 'category_id';
	$record_name = EVENT_CODE.' Category';

	$errors = false;
	$required = array();
	$required_fields = array('name' => 'Name'); // for validation

	// Get Records
	$records_arr = array();
	$params = array();

	$query = $db->query("SELECT * FROM `$record_db` WHERE `reg_system_id` = 1 AND `status` = ? ORDER BY `ordering`", array('Active'));
	if($query && !$db->error()) {
		$result = $db->fetch_array();
		foreach($result as $row) {
			$records_arr[$row[$record_id]] = $row;
			$records_arr[$row[$record_id]]['sub_items'] = array();
		}

		$lvl = 1; //tracking depth of array
		$records_arr = build_hierarchy($records_arr, $record_id);
		foreach($records_arr as $record_key => &$record){
			if($record['parent_id'] && array_key_exists($record['parent_id'], $records_arr)){
				$records_arr[$record['parent_id']]['sub_items'][$record_key] = &$record;
				$records_arr[$record['parent_id']]['sub_items'][$record_key]['parent_name'] = $records_arr[$record['parent_id']]['name'];
			}
		}
	}

	//search
	if(ACTION == '') {
		$_GET['search'] = $CMSBuilder->system_search(SECTION_ID);
		if(isset($_GET['search']) && $_GET['search'] != '') {
			$search_result = array();
			foreach($records_arr as $key => $search_record) {
				if(stripos($search_record['name'],$_GET['search']) !== false){
					$search_result[$key] = $search_record;
				}
			}
			$records_arr = $search_result;
		}
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			//set vars
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		if($records_arr[ITEM_ID]['deletable']){
		
			//Multiple queries so utilize transactions
			$db->new_transaction();

			// Trash categories and delete events
			$update1 = $db->query("UPDATE `$record_db` SET `status` = ? WHERE `$record_id` = ".ITEM_ID, array('Trashed'));
			if(isset($records_arr[ITEM_ID]['sub_items']) && !empty($records_arr[ITEM_ID]['sub_items'])) {
				$update2 = $db->query("UPDATE $record_db SET `status` = ? WHERE `$record_id` IN (".implode(",", array_keys($records_arr[ITEM_ID]['sub_items'])).")", array('Trashed'));
				foreach($records_arr[ITEM_ID]['sub_items'] as $subitem) {
					if(isset($subitem['sub_items']) && !empty($subitem['sub_items'])) {
						$update3 = $db->query("UPDATE `$record_db` SET `status` = ? WHERE `$record_id` IN (".implode(",", array_keys($subitem['sub_items'])).")", array('Trashed'));
						$delete3 = $db->query("DELETE FROM `reg_event_categories` WHERE `$record_id` IN (".implode(",", array_keys($subitem['sub_items'])).")");
					}
				}
				$delete2 = $db->query("DELETE FROM `reg_event_categories` WHERE `$record_id` IN (".implode(",", array_keys($records_arr[ITEM_ID]['sub_items'])).")");
			}
			$delete1 = $db->query("DELETE FROM `reg_event_categories` WHERE `$record_id` = ".ITEM_ID);

			if(!$db->error()) {
				$db->commit();
				$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			} else {
				$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(), false);	
			}
			
		}else{
			$CMSBuilder->set_system_alert('Dynamic categories cannot be deleted.', false);	
		}

		header("Location: " .PAGE_URL);
		exit();
	
	//Save item
	} else if(isset($_POST['save'])){
		
		if(ITEM_ID == '' || ITEM_ID > 2){
			$required_fields['parent_id'] = 'Parent Category';
		}else{
			$_POST['parent_id'] = NULL;
		}

		// Validate
		$required_missing = false;
		if(!empty($required_fields)) {
			foreach($required_fields as $field_key => $field_name) {
				if(isset($_POST[$field_key])) {
					if(trim($_POST[$field_key]) == '') {
						$required_missing = true;
						array_push($required, $field_key);
					}
				} else {
					$required_missing = true;
					array_push($required, $field_key);
				}
			}
		}
		if($required_missing) {
			$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
		}
		if(!isset($_POST['showhide'])){
			$_POST['showhide'] = 1;
		}

		// create safe page name
		$pagename = clean_url($_POST['name']);

		if(!$errors) {
			//Insert to db
			$params = array(
				ITEM_ID, 
				$reg_settings['reg_system_id'],
				$_POST['name'],
				$pagename,
				($_POST['parent_id'] != '' ? $_POST['parent_id'] : NULL),
				$_POST['showhide'],
				$_POST['ordering'],
				date('Y-m-d H:i:s'),

				$_POST['name'],
				$pagename,
				($_POST['parent_id'] != '' ? $_POST['parent_id'] : NULL),
				$_POST['showhide'],
				$_POST['ordering']
			);
			$insert = $db->query("INSERT INTO `$record_db` (`$record_id`, `reg_system_id`, `name`, `page`, `parent_id`, `showhide`, `ordering`, `date_added`) VALUES (?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `name`=?, `page`=?, `parent_id`=?, `showhide`=?, `ordering`=?", $params);
			if($insert && !$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		} else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	}
}

?>