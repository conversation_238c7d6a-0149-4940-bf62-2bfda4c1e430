<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?>">
	<div class="panel-wrapper">
		<div class="container">
			<div class="panel-content">
				<?php if($panel['include_h1'] || $panel['cta']['title'] || $panel['cta']['subtitle']) { ?>
					<header class="panel-header">
						<?php echo ($panel['include_h1'] ? '<h1>'.fancy_text($page['page_title']).'</h1>' : ''); ?>
						<?php echo ($panel['cta']['title'] ? '<div class="panel-title"><h2>'.fancy_text($panel['cta']['title']).'</h2></div>' : ''); ?>
						<?php echo ($panel['cta']['subtitle'] ? '<div class="panel-subtitle"><h3>'.fancy_text($panel['cta']['subtitle']).'</h3></div>' : ''); ?>
					</header>
				<?php } ?>

				<?php if($panel['cta']['url'] || $panel['cta']['url2']){ ?>
				<div class="panel-buttons">
					<?php //echo ($panel['cta']['url'] ? create_button($panel['cta']['url'], $panel['cta']['url_target'], $panel['cta']['url_text']) : ''); ?>
					<?php // echo ($panel['cta']['url2'] ? create_button($panel['cta']['url2'], $panel['cta']['url_target2'], $panel['cta']['url_text2']) : ''); ?> 

					<?php echo ($panel['cta']['url'] ? '<a href="'.$panel['cta']['url'].'" class="button primary " '.($panel['cta']['url_target'] ? 'target="'.$panel['cta']['url_target'].'"' : '').'><span class="top-border"></span> <span class="bottom-border"></span> <span class="left-border"></span>
						<span class="right-border"></span>
						'.$panel['cta']['url_text'].'</a>' : ''); ?>
					<?php echo ($panel['cta']['url2'] ? '<a href="'.$panel['cta']['url2'].'" class="button primary red " '.($panel['cta']['url_target2'] ? 'target="'.$panel['cta']['url_target2'].'"' : '').'><span class="top-border"></span> <span class="bottom-border"></span> <span class="left-border"></span>
						<span class="right-border"></span>
						'.$panel['cta']['url_text2'].'</a>' : ''); ?>
				</div>
				<?php } ?>
			</div>
		</div>
	</div>
</section>