@charset "utf-8";
/*
	global.less

*/

/*------ imports ------*/
@import "../../../core/less/core.less";
@import "definitions.less";


/*------ typography ------*/

body{
	&:extend(.font-paragraph);
	font-family: @font-base;
	color: @font-color;
	// color:@color-light;
	line-height: var(--line-height-thick);
	.letter-spacing(@letter-spacing);
	// background: @color-dark-navy-blue;
	background: @color-light;
}

.font-footnote{font-size: var(--font-footnote); }
.font-caption{font-size: var(--font-caption); }
.font-paragraph{font-size: var(--font-paragraph); }
.font-blockquote{font-size: var(--font-blockquote); }
.font-h6{font-size: var(--font-h6); }
.font-h5{font-size: var(--font-h5); }
.font-h4{font-size: var(--font-h4); }
.font-h3{font-size: var(--font-h3); }
.font-h2{font-size: var(--font-h2); }
.font-h1{font-size: var(--font-h1); }

h1, h2, h3, h4, h5, h6{
	font-family: @font-alt;
	word-break: break-word;
}
h1{
	&:extend(.font-h1);
	.extrabold();
	// color: @color-light;
	line-height: var(--line-height-thin);
	.letter-spacing(@letter-spacing-thin);
}
h2{
	&:extend(.font-h2);
	margin-bottom: 20px;
	.bold();
	// color: @color-light;
	line-height: var(--line-height-thin);
	.letter-spacing(@letter-spacing-thin);
}
h3{
	&:extend(.font-h3);
	.bold();
	color: @color-theme1;
	line-height: var(--line-height-thin);
	.letter-spacing(@letter-spacing-thin);
}
h4{
	&:extend(.font-h4);
	.bold();
	color: @color-theme2;
	line-height: var(--line-height-normal);
	.letter-spacing(@letter-spacing-thin);
}
h5{
	&:extend(.font-h5);
	.bold();
	color: @color-theme1;
	// color: @color-light;
	line-height: var(--line-height-normal);
	.letter-spacing(@letter-spacing);
}
h6{
	&:extend(.font-h6);
	.bold();
	color: @color-theme1;
	line-height: var(--line-height-normal);
	.letter-spacing(@letter-spacing);
}
small{
	&:extend(.font-caption);
	font-family: @font-alt;
	color: @font-color-light;
}


/*------ interface ------*/

// Targetted through hash links
:target{
	scroll-margin-top: 100px;
}

::selection{
	// background: fade(@color-dark, 50%);
	// color: #fff;
	color: @color-theme2;
}

hr{
	border-color: @color-gray-lighter;
}

a{
	// color: @color-theme4;
	word-break: break-word;

	&:hover,
	&:active{
		// color: @color-theme2;
	}
}

blockquote{
	// &:extend(.font-h6);
	&:extend(.font-blockquote);
	position: relative;
	.fluid-property(margin-block, 20px, 60px);
	.fluid-property(padding-left, 0px, 50px);
	// .bold();
	// font-style: italic;
	color: @color-theme1;
	line-height: var(--line-height-thick);
	.letter-spacing(@letter-spacing);
	font-family: 'contralto-big';

	&::before{
		// content: open-quote;
		content: '';
		background-image: url("../../images/quote.png");
		background-repeat: no-repeat;
		position: absolute;
		top: -10px;
		// .fluid-property(left, -15px, -5px);
		.fluid-property(left, 5px, 10px);
		font-size: 1250px;
		font-style: normal;
		.bold();
		color: @color-theme3;
		line-height: 0.6;
		.letter-spacing(@letter-spacing-thin);
		// opacity: 0.25;
		z-index: 2;
		width: 74px;
		height: 150px;
	}

	small{
		.regular();
		font-style: normal;
	}
}
p + blockquote{
	.fluid-property(margin-top, 0px, 40px);
}

table{
	th, td{
		color: @font-color;
		padding: 10px 20px;
		&:extend(.font-body);
	}

	&.responsive label,
	th{
		color: @color-theme1;
		font-weight: 700;
	}
}


// Animations
@keyframes slideInTopBorder {
    0% { 
        width: 0%;
        transform: scaleY(1);
    }
    100% { 
        width: 100%;
        transform: scaleY(1);
    }
}

@keyframes slideInBottomBorderDouble {
    0% { 
        width: 0%;
        transform: scaleY(1);
    }
    40% { 
        width: 100%;
        transform: scaleY(1);
    }
    41% { 
        width: 0%;
        transform: scaleY(1);
    }
    100% { 
        width: 100%;
        transform: scaleY(1);
    }
}

@keyframes slideInTopBorderContinue {
    0% { 
        width: 65%;
        transform: scaleY(1);
    }
    100% { 
        width: 100%;
        transform: scaleY(1);
    }
}

@keyframes slideInBottomBorderDoubleContinue {
    0% { 
        width: 65%;
        transform: scaleY(1);
    }
    // 100% { 
	40% { 
        width: 100%;
        transform: scaleY(1);
    }
    41% { 
        width: 65%;
        transform: scaleY(1);
    }
    100% { 
        width: 100%;
        transform: scaleY(1);
    }
}

/* Left border animation (bottom to top, twice) */
@keyframes leftBorderAnimation {
    0% { 
        height: 0%;
        bottom: 0;
        transform: scaleX(1);
    }
    40% { 
        height: 100%;
        bottom: 0;
        transform: scaleX(1);
    }
    41% { 
        height: 0%;
        bottom: 0;
        transform: scaleX(1);
    }
    100% { 
        height: 100%;
        bottom: 0;
        transform: scaleX(1);
    }
}

/* Right border animation (top to bottom, once) */
@keyframes rightBorderAnimation {
    0% { 
        height: 0%;
        top: 0;
        transform: scaleX(1);
    }
    100% { 
        height: 100%;
        top: 0;
        transform: scaleX(1);
    }
}

@keyframes leftBorderSlideUp {
    60% {
        transform: translateY(100%);
    }
	40% { 
        height: 100%;
        bottom: 0;
        transform: translateY(0);
    }
    41% { 
        height: 0%;
        bottom: 0;
		transform: translateY(100%);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes rightBorderSlideDown {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(0);
    }
}

.button{
	&:extend(.font-paragraph);
	// --text: @color-light;
	--text-hover: @color-light;
	--bg: @color-theme2;
	--bg-hover: @color-theme1;
	--border: var(--bg);
	--border-hover: var(--bg-hover);
	// position: relative;
	display: inline-block;
	width: auto;
	// color: var(--text);
	// background-color: var(--bg);
	// border: 1px solid var(--border);
	// border-radius: 3px;
	font-family: @font-base;
	.bold();
	font-size: 18px;
	line-height: var(--line-height-thin);
	text-align: center;
	text-transform: uppercase;
	vertical-align: middle;
	cursor: pointer;
	overflow: hidden;
	padding: 18px 30px;
	.no-shadow();
	.trans(background-color);
	.trans(border-color);
	.trans(opacity);
	.trans(color);

	//
	--text: @color-coral-red;
	// --border-color: @color-coral-red;
	--border-color: @color-dark;
	--border-color-light: @color-light;
	--border-color-dark: @color-dark;

	position: relative;
	padding: 15px 30px 12px;
	background: transparent;
	border: none;
	color: var(--text);
	text-transform: uppercase;
	
	//

	// Icons
	.fa, .far, .fab, .fas,
	span.mailto::before,
	span.phone::before{
		line-height: 1;
		margin-right: 8px;
	}
	span.phone::before{.font-awesome(f3cd); }
	span.mailto::before{.font-awesome(f0e0); }

	// Sizing
	&.small{
		.fluid-property(padding-block, 13px, 15px);
		.fluid-property(padding-inline, 15px, 20px);
	}

	// Variant styles update css vars
	&.simple{
		&:extend(.font-caption);
		--border: @color-theme3;
		--bg: @color-light;
		--text: @color-theme2;
		--bg-hover: @color-theme2;
		--text-hover: @color-light;
		.bold();
		.fluid-property(padding-block, 13px, 15px);
		.fluid-property(padding-inline, 15px, 20px);
		text-transform: none;
	}

	&.light{
		--bg: @color-light;
		--text: @color-theme4;
	}

	&.outline{
		--border: @color-theme3;
		--bg: transparent;
		--text: @color-light;
	}

	&.hover-light{
		--bg-hover: @color-light;
		--text-hover: @color-theme4;
	}

	&.hover-theme4{
		--bg-hover: @color-theme4;
		--text-hover: @color-light;
	}

	&:disabled{
		background-color: @color-gray-lightest;
		border-color: @color-gray-lightest;
		color: @color-gray-light;
		cursor: default;
	}

	&.normal{
		border:3px solid var(--border-color);
	}



	&.primary {
		--border-width: 3px;
		position: relative;
		color: @color-dark;
		
		// Remove original left/right borders
		border-left: none;
		border-right: none;
	
		// Add vertical border spans
		.left-border, .right-border {
			position: absolute;
			top: 0;
			bottom: 0;
			width: var(--border-width);
			background-color: var(--border-color);
			transform: translateZ(0);  // Force hardware acceleration
       		backface-visibility: hidden;  // Helps with rendering
		}
	
		.left-border {
			left: 0;
			transform: translateX(0);  // Force pixel alignment
		}
	
		.right-border {
			right: 0;
			transform: translateX(0);  // Force pixel alignment
		}

		.left-border, .right-border {
			background: none;
			border-left: var(--border-width) solid var(--border-color);
		}
	
		// Top and bottom borders using pseudo elements
		&::before, &::after {
			content: '';
			position: absolute;
			width: 10%;
			height: var(--border-width);
			background-color: var(--border-color);
			transform: scaleY(1);
			will-change: width, transform;
		}
	
		&::before {
			top: 0;
			left: 0;
			transform-origin: left;
		}
		
		&::after {
			bottom: 0;
			right: 0;
			transform-origin: right;
		}
	
		&:hover, &:focus {
			--border-color: @color-theme2;
			color: @color-theme2;
			
			&::before {
				width: 0%;
				animation: slideInTopBorderContinue 0.3s ease 0.3s forwards;
			}
			
			&::after {
				width: 0%;
				animation: slideInBottomBorderDoubleContinue 0.3s ease 0.3s forwards;
			}
		}
	
		&:not(:hover):not(:focus) {
			&::before, &::after {
				animation: none;
				width: 10%;
				transition: width 0.3s ease;
			}
		}

		// &.btn-colour{
		// 	.top-border,
		// 	.bottom-border,
		// 	.left-border,
		// 	.right-border{
		// 		border-color: @font-color;
		// 		color: @font-color;
		// 	}
		// }

		&.light{
			--border-color-light: @color-light;
			--border-color: @color-light;
			color: @color-light;
			.left-border, .right-border {
				background-color: @color-light;
				color: @color-light;
			}
		}

		&.black{
			--border-color-light: @color-dark;
			--border-color: @color-dark;
			color: @color-dark;
			.left-border, .right-border {
				background-color: @color-dark;
				color: @color-dark;
			}
		}

		&.red{
			--border-color-light: @color-coral-red;
			--border-color: @color-coral-red;
			color: @color-coral-red;
			.left-border, .right-border {
				background-color: @color-dark;
				color: @color-dark;
			}
		}
	}
	
	&.secondary {
		--text-color: @color-coral-red;  
		padding: 10px 20px 10px 0;  
		font-size: 18px;
		letter-spacing: -20;
		border: none;
		background: transparent;
		color: var(--text-color);
		position: relative;
		display: inline-block;
		transition: all 0.2s ease;
	
		// Remove any inherited borders or lines
		border-left: none;
		border-right: none;
	
		// Underline
		&::before {
			content: '';
			position: absolute;
			bottom: 0;
			left: 10px;
			width: 20px;  
			height: 3px;
			background-color: var(--text-color);
			transition: width 0.2s ease-in-out;
		}
	
		// Arrow
		&::after {
			content: '>';
			display: inline-block;
			position: absolute;
			right: 0;  
			top: 50%;
			transform: translateY(-50%);
			opacity: 0;  
			font-size: 18px;  
			font-family: @font-base;
			line-height: 1;
			transition: all 0.2s ease;
		}
	
		// Hover effects
		&:hover {
			transform: translateX(-8px);
	
			&::before {
				width: calc(100% - 31px);  
			}
	
			&::after {
				opacity: 1;
				right: 0;  
			}
		}

		&.light{
			--text-color: @color-light;
		}
	}

	&.ternary {
		--border-width: 3px;
		position: relative;
		color: @color-dark;

		// Remove original left/right borders
		border-left: none;
		border-right: none;
	
		// Add vertical border spans
		.top-border, .bottom-border {
			position: absolute;
			// top: 0;
			// bottom: 0;
			width: var(--border-width);
			// width: 50%;
			background-color: var(--border-color-dark);
			transform: translateZ(0);  // Force hardware acceleration
			opacity: 0;
       		backface-visibility: hidden;  // Helps with rendering
		}
	
		.top-border {
			top: 0;
			left: 0;
			transform: translateY(0);  // Force pixel alignment
		}
	
		.bottom-border {
			bottom: 0;
			right:0;
			transform: translateY(0);  // Force pixel alignment
		}

		.top-border, .bottom-border {
			background: none;
			border-left: var(--border-width) solid var(--border-color-dark);
		}

		.left-border, .right-border {
			position: absolute;
			width: var(--border-width);
			// width:1px;
			height: 100%;
			// height: auto;
			background-color: var(--border-color-dark);
			transform: translateZ(0);
			backface-visibility: hidden;
			transition: transform 0.3s ease;
			visibility: hidden;
		}
	
		//
		.left-border {
			top: 0;
			left: 0;
			transform: translateY(100%);		
		}
	
		.right-border {
			top: 0;
			right: 0;
			transform: translateY(-100%);}
		//
	
		// Top and bottom borders using pseudo elements
		&::before, &::after {
			content: '';
			position: absolute;
			width: 40%;
			height: var(--border-width);
			background-color: var(--border-color-dark);
			transform: scaleY(1);
			will-change: width, transform;
		}
	
		&::before {
			top: 0;
			left: 0;
			transform-origin: left;
		}
		
		&::after {
			bottom: 0;
			right: 0;
			transform-origin: right;
		}
	
		&:hover, &:focus {
			// --border-color: @color-theme2;
			// --border-color-light: #1A2636;
			--border-color-light: #000;
			color: #000;
			// border-left: var(--border-width) solid #000;
			// border-right: var(--border-width) solid #000;
			
			&::before {
				width: 65%;
				animation: slideInTopBorderContinue 0.1s ease 0.1s forwards;
			}
			
			&::after {
				width: 65%;
				animation: slideInBottomBorderDoubleContinue 0.2s ease 0.2s forwards;
			}

			//
			.left-border {
				opacity: 1;
				visibility: visible;
				animation: leftBorderSlideUp 0.3s ease forwards;
			}
			.right-border {
				opacity: 1;
				visibility: visible;
				animation: rightBorderSlideDown 0.3s ease forwards;
			}
			//
		}
	
		&:not(:hover):not(:focus) {
			border-left: none;
			border-right: none;
			border-color: #000;

			&::before, &::after {
				animation: none;
				width: 65%;
				transition: width 0.3s ease;
				// border-left:0;
				// border-right:0;
				// border-color: #000;
			}
			.left-border, .right-border {
				opacity: 0;
				visibility: hidden;
				// width: 0px;
				transition: opacity 0.3s ease, visibility 0s 0.3s;
			}
		}

		&.light{
			--border-color-light: @color-light;
			--border-color: @color-light;
			color: @color-light;
			.left-border, .right-border {
				background-color: @color-light;
				color: @color-light;
			}

			.top-border, .bottom-border {
				border-color: var(--border-color-light);
				background-color: var(--border-color-light);
			}

			&:not(:hover):not(:focus) {	
				&::before, &::after {
					background-color: var(--border-color-light);
				}
			}
			
			&:hover, &:focus{
				border-color: var(--border-color-light);
				&::before, &::after {
					border-color: var(--border-color-light);
					background-color: var(--border-color-light);
				}
			}
		}
	}
}

.panel-text{
	word-break: break-word;
	max-width: var(--text-wrap, 100%);

	//TinyMCE buttons
	.button{
		margin: 0 10px 10px 0;

	}

	[style*="text-align: center;"] .button,
	.center .button {
		margin-inline: 5px;
	}

	[style*="text-align: right;"] .button,
	.right .button {
		margin-right: 0;
		margin-left: 10px;
	}
}

.gradient-text{
	.gradient-text();
	padding-bottom: 0.15ch; // avoid clipped descenders
}


/*------ email template ------*/

body.email-template{
	background: @color-gray-lightest;
	max-width: none;

	#email-wrapper{
		margin: 0 auto;

		#email-header,
		#email-content,
		#email-footer{border: 0; }

		#email-header{
			padding: 30px 0;
			background: none;

			img{display: block; }
		}

		#email-content{
			padding: 0 0 30px;
			background: none;

			#email-content-inner{
				background: @color-light;
				padding: 30px 30px 10px;
				border-radius: 10px;
			}
		}

		#email-footer{
			padding: 20px 0;
			background: none;
			border-top: 1px solid @color-gray-light;
			text-align: center;
		}
	}
}