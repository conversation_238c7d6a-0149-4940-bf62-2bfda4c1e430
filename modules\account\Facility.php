<?php
// // modules/account/facility.php - <PERSON>les POST requests from the Facility form

// // if(PAGE_ID == $_sitepages['edit-facility']['page_id']){
// // //     // echo "mo fcxx";
// // //     // exit;
// // }

// // modules/account/facility.php - <PERSON>les POST requests ONLY for the Edit Facility form

// // --- PRIMARY CHECK: Only execute if this is the facility edit page AND a POST request ---
// if (
//     // isset(PAGE_ID, $_sitepages['facility']['page_id']) && // Use YOUR correct page ID key
//     PAGE_ID == $_sitepages['edit-facility']['page_id']
//     // && $_SERVER['REQUEST_METHOD'] === 'POST'
//    )
// {
//     // --- Secondary Check: Was the correct form submitted? ---
//     if (isset($_POST['update_facility']) && $_POST['update_facility'] == '1') {
        
//         require_once($_SERVER['DOCUMENT_ROOT'] . (isset($path) ? $path : '/') . 'core/classes/ImageUpload.class.php');


//         // --- Environment Setup ---
//         if (session_status() === PHP_SESSION_NONE) { session_start(); }
//         global $path, $db, $Account, $_sitepages; 

//         // --- Login & Access Check ---
//         if (!isset($Account) || !$Account->login_status()) {
//              $_SESSION['facility_form_error_message'] = 'Access Denied. Please log in.';
//              header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'login/'));
//              exit;
//         }
//         $current_account_id = $Account->account_id;
//         $facility_id_to_update = (int)($_POST['facility_id_to_update'] ?? 0);

//         // FACILITY_ACCESS check (repeat or use session flag)
//         $can_edit_facility = false;
//         if (!empty($Account->facility_id) && $Account->facility_id == $facility_id_to_update && isset($db) && is_object($db)) {
//              $facility_classes_with_access = [];
//              $query_fc = $db->query("SELECT `class_id` FROM `membership_classes` WHERE `facility_access` = 1");
//              if ($query_fc && !$db->error()) {
//                  $fc_rows = $db->fetch_array($query_fc);
//                  if ($fc_rows) foreach ($fc_rows as $fc_row) $facility_classes_with_access[] = $fc_row['class_id'];
//              }
//              if (($Account->facility_access ?? 0) == 1 || in_array($Account->class_id ?? null, $facility_classes_with_access)) {
//                  $can_edit_facility = true;
//              }
//         }
//         if (!$can_edit_facility) {
//             $_SESSION['facility_form_error_message'] = 'You do not have permission to update this facility.';
//             // header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/xit;
//         }
//         // --- End Login & Access Check ---


//         // --- XID Check ---
//         if (!isset($_POST['xid']) || !isset($_COOKIE['xid']) || $_POST['xid'] != $_COOKIE['xid']){
//             $_SESSION['facility_form_error_message'] = 'Invalid session. Please ensure cookies are enabled and try again.';
//             header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/'));
//             exit;
//         }

//         // Clear session messages
//         unset($_SESSION['facility_form_success_message']);
//         unset($_SESSION['facility_form_error_message']);
//         unset($_SESSION['facility_form_data']);

//         // --- Define Paths & Image Settings ---
//         $logo_storage_dir_relative = 'images/facility/logos/';
//         $banner_storage_dir_base_relative = 'images/facility/banners/';
//         $abs_logo_dir = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $path . $logo_storage_dir_relative;
//         $abs_banner_dir_base = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $path . $banner_storage_dir_base_relative;
//         // Ensure ImageUpload class is included
//         $image_upload_class_path = $_SERVER['DOCUMENT_ROOT'] . $path . 'modules/classes/ImageUpload.class.php'; // Adjust path if necessary
//         if (!class_exists('ImageUpload') && file_exists($image_upload_class_path)) {
//             require_once($image_upload_class_path);
//         } elseif (!class_exists('ImageUpload')) {
//              // Fatal error if class cannot be loaded
//              $_SESSION['facility_form_error_message'] = 'Server configuration error: Image processing library missing.';
//             //   header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/xit;
//         }
//         $filetypes = ['jpg', 'jpeg', 'png', 'gif'];
//         $max_file_size = 2 * 1024 * 1024; // 2MB
//         $cropsizes = [ // Banner crop sizes from your old code
//             '1920' => ['width' => 1920, 'height' => 560], '1366' => ['width' => 1366, 'height' => 560],
//             '1024' => ['width' => 1024, 'height' => 300], '768'  => ['width' => 768, 'height' => 260],
//             '480'  => ['width' => 480, 'height' => 200]
//         ];

//         // --- Variable Initialization for Images ---
//         $new_logo_filename = $_POST['old_logo'] ?? null;
//         $new_banner_filename = $_POST['old_banner'] ?? null;
//         $logo_action = 'keep'; // keep, upload, delete
//         $banner_action = 'keep'; // keep, upload, delete


//         // --- Process Facility Logo ---
//         if (isset($_POST['delete_logo']) && $_POST['delete_logo'] == '1') {
//             $logo_action = 'delete';
//             if (!empty($new_logo_filename)) {
//                 $file_path = $abs_logo_dir . $new_logo_filename;
//                 if (file_exists($file_path)) { @unlink($file_path); }
//                 $new_logo_filename = null;
//             }
//         } elseif (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
//             $logo_action = 'upload';
//             $file_logo = $_FILES['logo'];
//             $ext_logo = strtolower(pathinfo($file_logo['name'], PATHINFO_EXTENSION));
//             if ($file_logo['size'] > $max_file_size) { $_SESSION['facility_form_error_message'] = 'Logo file too large (Max 2MB).'; }
//             elseif (!in_array($ext_logo, $filetypes)) { $_SESSION['facility_form_error_message'] = 'Invalid logo file type (JPG, PNG, GIF only).'; }
//             else {
//                 $temp_logo_filename = 'facility_' . $facility_id_to_update . '_logo_' . time() . '.' . $ext_logo;
//                 $imageUploadLogo = new ImageUpload();
//                 if ($imageUploadLogo->load($file_logo['tmp_name'])) {
//                     $imageUploadLogo->smartFit(600, 600); // Resize as per old logic
//                     // Note: Your old code saved twice here, likely a typo. Saving once.
//                     if ($imageUploadLogo->save(str_replace('/', DIRECTORY_SEPARATOR, $abs_logo_dir), $temp_logo_filename)) {
//                         // Delete old if successful
//                         if (!empty($new_logo_filename) && $new_logo_filename != $temp_logo_filename && file_exists($abs_logo_dir . $new_logo_filename)) {
//                             @unlink($abs_logo_dir . $new_logo_filename);
//                         }
//                         $new_logo_filename = $temp_logo_filename; // Set new filename
//                     } else { $_SESSION['facility_form_error_message'] = 'Failed to save new logo.'; $logo_action='keep'; $new_logo_filename = $_POST['old_logo']??null; } // Revert on fail
//                 } else { $_SESSION['facility_form_error_message'] = 'Failed to load uploaded logo.'; $logo_action='keep'; $new_logo_filename = $_POST['old_logo']??null; } // Revert on fail
//             }
//         }
//         // If error occurred during logo processing, redirect back now
//         if (isset($_SESSION['facility_form_error_message'])) {
//             $_SESSION['facility_form_data'] = $_POST;
//             // header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/xit;
//         }


//         // --- Process Banner Image ---
//          if (isset($_POST['delete_banner']) && $_POST['delete_banner'] == '1') {
//              $banner_action = 'delete';
//              if (!empty($new_banner_filename)) {
//                 foreach ($cropsizes as $dir_suffix => $size) {
//                     $file_path = $abs_banner_dir_base . $dir_suffix . '/' . $new_banner_filename;
//                      if (file_exists($file_path)) { @unlink($file_path); }
//                 }
//              }
//              $new_banner_filename = null;
//          } elseif (isset($_FILES['banner']) && $_FILES['banner']['error'] === UPLOAD_ERR_OK) {
//              $banner_action = 'upload';
//              $file_banner = $_FILES['banner'];
//              $ext_banner = strtolower(pathinfo($file_banner['name'], PATHINFO_EXTENSION));
//              if ($file_banner['size'] > $max_file_size) { $_SESSION['facility_form_error_message'] = 'Banner file too large (Max 2MB).'; }
//              elseif (!in_array($ext_banner, $filetypes)) { $_SESSION['facility_form_error_message'] = 'Invalid banner file type (JPG, PNG, GIF only).'; }
//              else {
//                  $temp_banner_filename = 'facility_' . $facility_id_to_update . '_banner_' . time() . '.' . $ext_banner;
//                  $imageUploadBanner = new ImageUpload();
//                  if ($imageUploadBanner->load($file_banner['tmp_name'])) {
//                      $imageUploadBanner->smartFit(1920, 1920); // Fit within max dimensions first
//                      $banner_save_success = true;
//                      foreach ($cropsizes as $dir_suffix => $size) {
//                          $target_dir = $abs_banner_dir_base . $dir_suffix . '/';
//                           $target_dir_cleaned = str_replace('/', DIRECTORY_SEPARATOR, $target_dir);
//                           if (!is_dir($target_dir_cleaned)) @mkdir($target_dir_cleaned, 0755, true);

//                           // Reload or clone the image resource before each crop
//                           $imageUploadBanner->load($file_banner['tmp_name']); // Reload original
//                           $imageUploadBanner->smartFit(1920, 1920);         // Re-apply initial fit

//                           $imageUploadBanner->smartCrop($size['width'], $size['height']);
//                           if (!$imageUploadBanner->save($target_dir_cleaned, $temp_banner_filename)) {
//                               $_SESSION['facility_form_error_message'] = 'Failed to save banner size: ' . $dir_suffix;
//                               $banner_save_success = false;
//                               break; // Stop processing sizes on first failure
//                           }
//                      } // End foreach crop size

//                      if ($banner_save_success) {
//                           // Delete old banner if it exists and is different
//                           if (!empty($new_banner_filename) && $new_banner_filename != $temp_banner_filename) {
//                               foreach ($cropsizes as $dir_suffix => $size) {
//                                   $old_file_path = $abs_banner_dir_base . $dir_suffix . '/' . $new_banner_filename;
//                                   if (file_exists($old_file_path)) { @unlink($old_file_path); }
//                               }
//                           }
//                           $new_banner_filename = $temp_banner_filename; // Set new filename
//                      } else {
//                          $banner_action = 'keep'; $new_banner_filename = $_POST['old_banner']??null; // Revert on failure
//                          // Clean up any successfully saved sizes of the FAILED banner upload
//                          if (!empty($temp_banner_filename)) { foreach ($cropsizes as $dir_suffix => $size) { if (file_exists($abs_banner_dir_base . $dir_suffix . '/' . $temp_banner_filename)) @unlink($abs_banner_dir_base . $dir_suffix . '/' . $temp_banner_filename); }}
//                      }

//                  } else { $_SESSION['facility_form_error_message'] = 'Failed to load uploaded banner.'; $banner_action='keep'; $new_banner_filename = $_POST['old_banner']??null; } // Revert on fail
//              }
//          }
//         // If error occurred during banner processing
//         if (isset($_SESSION['facility_form_error_message'])) {
//             $_SESSION['facility_form_data'] = $_POST;
//             // header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/xit;
//         }

//         // --- Prepare fields and values for direct SQL UPDATE ---
//         // We are not using Account::update_profile here
//         $update_fields = [];
//         $update_values = [];

//         echo "<pre>";
//         print_r($_POST);
//         echo "</pre>";
//         // exit;

//         // Add fields from $_POST, mapping to DB columns in 'facilities' table
//         if (isset($_POST['type']))         { $update_fields[] = "`type` = ?";          $update_values[] = $_POST['type'] ?: null; }
//         if (isset($_POST['content']))      { $update_fields[] = "`content` = ?";       $update_values[] = trim($_POST['content']); }
//         if (isset($_POST['address1']))     { $update_fields[] = "`address1` = ?";      $update_values[] = trim($_POST['address1']); }
//         if (isset($_POST['address2']))     { $update_fields[] = "`address2` = ?";      $update_values[] = trim($_POST['address2']); }
//         if (isset($_POST['city']))         { $update_fields[] = "`city` = ?";          $update_values[] = trim($_POST['city']); }
//         if (isset($_POST['province']))     { $update_fields[] = "`province` = ?";      $update_values[] = $_POST['province'] ?: null; } // Assuming storing code/name based on form value
//         if (isset($_POST['postal_code']))  { $update_fields[] = "`postal_code` = ?";   $update_values[] = trim($_POST['postal_code']); }
//         if (isset($_POST['country']))      { $update_fields[] = "`country` = ?";       $update_values[] = $_POST['country'] ?: null; }
//         if (isset($_POST['region']))       { $update_fields[] = "`region` = ?";        $update_values[] = $_POST['region'] ?: null; }
//         if (isset($_POST['email']))        { $update_fields[] = "`email` = ?";         $update_values[] = trim($_POST['email']); }
//         if (isset($_POST['phone']))        { $update_fields[] = "`phone` = ?";         $update_values[] = trim($_POST['phone']); } // Add formatting?
//         if (isset($_POST['website']))      { $update_fields[] = "`website` = ?";       $update_values[] = trim($_POST['website']); } // Add http:// prefix?

//         // Add logo and banner based on determined action
//         if ($logo_action == 'upload' || $logo_action == 'delete') {
//              $update_fields[] = "`logo` = ?";
//              $update_values[] = $new_logo_filename; // Will be null if deleted
//         }
//         if ($banner_action == 'upload' || $banner_action == 'delete') {
//              $update_fields[] = "`image` = ?"; // Banner uses 'image' column
//              $update_values[] = $new_banner_filename; // Will be null if deleted
//         }

//         // Add last_updated timestamp
//         $update_fields[] = "`last_updated` = ?";
//         $update_values[] = date("Y-m-d H:i:s");

//         // --- Perform Database Update ---
//         if (count($update_fields) > 1 && isset($db) && is_object($db)) { // Check if there are fields to update (>1 because last_updated is always added)
//             $sql = "UPDATE `facilities` SET " . implode(', ', $update_fields) . " WHERE `facility_id` = ?";
//             $update_values[] = $facility_id_to_update; // Add facility ID for WHERE clause

//             try {
//                  error_log("Facility Update: Attempting DB update for facility ID: " . $facility_id_to_update);
//                  $query = $db->query($sql, $update_values);
//                  if ($query && !$db->error()) {
//                       $_SESSION['facility_form_success_message'] = "Facility details updated successfully!";
//                       error_log("Facility Update: DB update successful.");
//                  } else {
//                       throw new Exception("Database update failed: " . ($db->error() ?? 'Unknown DB error'));
//                  }
//             } catch (Exception $e) {
//                  $_SESSION['facility_form_error_message'] = "Error: " . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
//                  $_SESSION['facility_form_data'] = $_POST; // Store data for repopulation
//                  error_log("Facility Update Exception: " . $e->getMessage());
//                  // Clean up any newly uploaded files on DB error
//                   if ($logo_action === 'upload' && !empty($new_logo_filename)) { if (file_exists($abs_logo_dir . $new_logo_filename)) @unlink($abs_logo_dir . $new_logo_filename); }
//                   if ($banner_action === 'upload' && !empty($new_banner_filename)) { foreach ($cropsizes as $dir_suffix => $size) { if (file_exists($abs_banner_dir_base . $dir_suffix . '/' . $new_banner_filename)) @unlink($abs_banner_dir_base . $dir_suffix . '/' . $new_banner_filename); } }
//             }
//         } else {
//              // No fields changed other than potentially images which were already handled?
//              // Or DB object not available.
//              if (count($update_fields) <= 1) {
//                   $_SESSION['facility_form_success_message'] = "No changes detected in form fields."; // Or just redirect without message
//              } else {
//                   $_SESSION['facility_form_error_message'] = "Database connection error.";
//                   error_log("Facility Update: DB object not available.");
//              }
//         }

//         // --- Redirect back to the facility form page ---
//         // header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/xit;


//     } // End secondary check for 'update_facility' flag
//     else {
//         // POST request to facility page but wrong/missing flag
//         //  header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/'));
//         //  exit;
//     }

// } // --- END primary IF block ---
?>

<?php
// modules/account/facility.php - Handles POST requests ONLY for the Edit Facility form

// --- PRIMARY CHECK: Only execute if this is the facility edit page AND a POST request ---
if (
    // isset(PAGE_ID, $_sitepages['edit-facility']['page_id']) && // Use YOUR correct page ID key
    PAGE_ID == $_sitepages['edit-facility']['page_id'] 
    // && $_SERVER['REQUEST_METHOD'] === 'POST'
   )
{
    // --- Secondary Check: Was the correct form submitted? ---
    if (isset($_POST['update_facility']) && $_POST['update_facility'] == '1') {

        // --- Environment Setup ---
        if (session_status() === PHP_SESSION_NONE) { session_start(); }
        global $path, $db, $Account, $_sitepages; // Ensure needed globals are accessible

        // --- Login & Access Check ---
        if (!isset($Account) || !$Account->login_status()) {
             $_SESSION['facility_form_error_message'] = 'Access Denied. Please log in.';
             header('Location: ' . ($_sitepages['login']['page_url'] ?? $path . 'login/')); // Redirect to login
             exit;
        }

        require_once($_SERVER['DOCUMENT_ROOT'] . (isset($path) ? $path : '/') . 'core/classes/ImageUpload.class.php');

        $current_account_id = $Account->account_id;
        $facility_id_to_update = (int)($_POST['facility_id_to_update'] ?? 0);

        // FACILITY_ACCESS check (Ensure user can edit this specific facility)
        $can_edit_facility = false;
        if (!empty($Account->facility_id) && $Account->facility_id == $facility_id_to_update && isset($db) && is_object($db)) {
            // (Simplified check: User can only edit their OWN facility_id)
            // Add your more complex role/class check here if needed
            $can_edit_facility = true; // Assuming $Account->facility_id is the authoritative link
        }
        if (!$can_edit_facility) {
            $_SESSION['facility_form_error_message'] = 'You do not have permission to update this facility.';
            // header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/xit;
        }
        // --- End Login & Access Check ---


        // --- XID Check ---
        if (!isset($_POST['xid']) || !isset($_COOKIE['xid']) || $_POST['xid'] != $_COOKIE['xid']){
            $_SESSION['facility_form_error_message'] = 'Invalid session. Please try again.';
            // header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/'));
            // exit;
        }

        // Clear session messages
        unset($_SESSION['facility_form_success_message']);
        unset($_SESSION['facility_form_error_message']);
        unset($_SESSION['facility_form_data']);

        // --- Define Paths & Image Settings ---
        $logo_storage_dir_relative = 'images/facility/logos/'; // <-- ADJUSTED PATH
        $banner_storage_dir_relative = 'images/facility/banners/'; // <-- ADJUSTED PATH (single location)
        $abs_logo_dir = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $path . $logo_storage_dir_relative;
        $abs_banner_dir = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $path . $banner_storage_dir_relative;
        // Ensure target directories exist and are writable
        if (!is_dir($abs_logo_dir)) { @mkdir($abs_logo_dir, 0775, true); }
        if (!is_dir($abs_banner_dir)) { @mkdir($abs_banner_dir, 0775, true); }
        if (!is_writable($abs_logo_dir) || !is_writable($abs_banner_dir)) {
             $_SESSION['facility_form_error_message'] = 'Server error: Cannot write to image directories.';
             $_SESSION['facility_form_data'] = $_POST;
            //  header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/'));
            //  exit;
        }
        // Ensure ImageUpload class is included
        $image_upload_class_path = $_SERVER['DOCUMENT_ROOT'] . $path . 'modules/classes/ImageUpload.class.php'; // Adjust path if necessary
        if (!class_exists('ImageUpload') && file_exists($image_upload_class_path)) {
            require_once($image_upload_class_path);
        } elseif (!class_exists('ImageUpload')) {
             $_SESSION['facility_form_error_message'] = 'Server configuration error: Image processing library missing.';
            //  header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/'));
            //  exit;
        }
        $filetypes = ['jpg', 'jpeg', 'png', 'gif'];
        $max_file_size = 2 * 1024 * 1024; // 2MB

        // --- Variable Initialization for Images ---
        $current_logo_filename = $_POST['old_logo'] ?? null;    // Filename currently in DB (passed from form)
        $current_banner_filename = $_POST['old_banner'] ?? null;
        $final_logo_filename = $current_logo_filename;      // What will be saved to DB
        $final_banner_filename = $current_banner_filename;


        // --- Process Facility Logo ---
        $logo_error = false;
        if (isset($_POST['delete_logo']) && $_POST['delete_logo'] == '1') {
            error_log("Facility Update: Delete logo requested for: " . $current_logo_filename);
            if (!empty($current_logo_filename)) {
                $logo_path_to_delete = str_replace('/', DIRECTORY_SEPARATOR, $abs_logo_dir . $current_logo_filename);
                if (file_exists($logo_path_to_delete)) {
                     if(!@unlink($logo_path_to_delete)) {
                        error_log("Facility Update Warning: Failed to delete old logo file: " . $logo_path_to_delete);
                        // Continue processing DB update anyway
                     } else {
                        error_log("Facility Update: Successfully deleted old logo file: " . $logo_path_to_delete);
                     }
                } else {
                    error_log("Facility Update Warning: Old logo file not found for deletion: " . $logo_path_to_delete);
                }
            }
            $final_logo_filename = null; // Set to null for DB update
        }
        elseif (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            error_log("Facility Update: Logo upload detected.");
            $file_logo = $_FILES['logo'];
            $ext_logo = strtolower(pathinfo($file_logo['name'], PATHINFO_EXTENSION));

            if ($file_logo['size'] > $max_file_size) { $_SESSION['facility_form_error_message'] = 'Logo file too large (Max 2MB).'; $logo_error = true; }
            elseif (!in_array($ext_logo, $filetypes)) { $_SESSION['facility_form_error_message'] = 'Invalid logo file type (JPG, PNG, GIF only).'; $logo_error = true; }
            else {
                // Generate new filename (facility_id + type + timestamp + extension)
                $temp_logo_filename = 'facility_' . $facility_id_to_update . '_logo_' . time() . '.' . $ext_logo;
                $target_logo_path = str_replace('/', DIRECTORY_SEPARATOR, $abs_logo_dir . $temp_logo_filename);
                $imageUploadLogo = new ImageUpload();

                if ($imageUploadLogo->load($file_logo['tmp_name'])) {
                    $imageUploadLogo->smartFit(600, 600); // Resize to fit max 600x600 box
                    if ($imageUploadLogo->save($abs_logo_dir, $temp_logo_filename)) { // Use cleaned dir path
                        error_log("Facility Update: New logo saved successfully: " . $target_logo_path);
                        // Delete old logo file if it exists and is different
                        if (!empty($current_logo_filename) && $current_logo_filename != $temp_logo_filename && file_exists($abs_logo_dir . $current_logo_filename)) {
                            error_log("Facility Update: Deleting old logo after successful upload: " . $abs_logo_dir . $current_logo_filename);
                            @unlink($abs_logo_dir . $current_logo_filename);
                        }
                        $final_logo_filename = $temp_logo_filename; // Set final filename for DB
                    } else { $_SESSION['facility_form_error_message'] = 'Failed to save new logo file.'; $logo_error = true; error_log("Facility Update Error: ImageUpload->save() failed for logo: " . $target_logo_path); }
                } else { $_SESSION['facility_form_error_message'] = 'Failed to load uploaded logo.'; $logo_error = true; error_log("Facility Update Error: ImageUpload->load() failed for logo."); }
            }
        }
        // Redirect immediately if a logo processing error occurred
        if ($logo_error) {
            $_SESSION['facility_form_data'] = $_POST;
            // header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/'));
            // exit;
        }


        // --- Process Banner Image ---
        $banner_error = false;
        if (isset($_POST['delete_banner']) && $_POST['delete_banner'] == '1') {
            error_log("Facility Update: Delete banner requested for: " . $current_banner_filename);
            if (!empty($current_banner_filename)) {
                $banner_path_to_delete = str_replace('/', DIRECTORY_SEPARATOR, $abs_banner_dir . $current_banner_filename);
                if (file_exists($banner_path_to_delete)) {
                    if(!@unlink($banner_path_to_delete)) {
                        error_log("Facility Update Warning: Failed to delete old banner file: " . $banner_path_to_delete);
                    } else {
                        error_log("Facility Update: Successfully deleted old banner file: " . $banner_path_to_delete);
                    }
                } else {
                     error_log("Facility Update Warning: Old banner file not found for deletion: " . $banner_path_to_delete);
                }
            }
            $final_banner_filename = null; // Set to null for DB update
        }
        elseif (isset($_FILES['banner']) && $_FILES['banner']['error'] === UPLOAD_ERR_OK) {
             error_log("Facility Update: Banner upload detected.");
             $banner_action = 'upload';
             $file_banner = $_FILES['banner'];
             $ext_banner = strtolower(pathinfo($file_banner['name'], PATHINFO_EXTENSION));

             if ($file_banner['size'] > $max_file_size) { $_SESSION['facility_form_error_message'] = 'Banner file too large (Max 2MB).'; $banner_error = true; }
             elseif (!in_array($ext_banner, $filetypes)) { $_SESSION['facility_form_error_message'] = 'Invalid banner file type (JPG, PNG, GIF only).'; $banner_error = true; }
             else {
                 $temp_banner_filename = 'facility_' . $facility_id_to_update . '_banner_' . time() . '.' . $ext_banner;
                 $target_banner_path = str_replace('/', DIRECTORY_SEPARATOR, $abs_banner_dir . $temp_banner_filename);
                 $imageUploadBanner = new ImageUpload();

                 if ($imageUploadBanner->load($file_banner['tmp_name'])) {
                     $imageUploadBanner->smartFit(1920, 1920); // Fit to max width/height (e.g., 1920)
                     // We are NOT doing multiple crops anymore, just save this one version
                     if ($imageUploadBanner->save($abs_banner_dir, $temp_banner_filename)) {
                         error_log("Facility Update: New banner saved successfully: " . $target_banner_path);
                          // Delete old banner file if it exists and is different
                          if (!empty($current_banner_filename) && $current_banner_filename != $temp_banner_filename && file_exists($abs_banner_dir . $current_banner_filename)) {
                              error_log("Facility Update: Deleting old banner after successful upload: " . $abs_banner_dir . $current_banner_filename);
                              @unlink($abs_banner_dir . $current_banner_filename);
                          }
                          $final_banner_filename = $temp_banner_filename; // Set final filename for DB
                     } else { $_SESSION['facility_form_error_message'] = 'Failed to save new banner file.'; $banner_error = true; error_log("Facility Update Error: ImageUpload->save() failed for banner: " . $target_banner_path);}
                 } else { $_SESSION['facility_form_error_message'] = 'Failed to load uploaded banner.'; $banner_error = true; error_log("Facility Update Error: ImageUpload->load() failed for banner.");}
             }
         }
        // Redirect immediately if a banner processing error occurred
        if ($banner_error) {
            $_SESSION['facility_form_data'] = $_POST;
            // header('Location: ' . ($_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/'));
            // exit;
        }


        // --- Prepare fields and values for direct SQL UPDATE ---
        $update_fields = [];
        $update_values = [];

        // Add text/select fields from $_POST
        if (isset($_POST['type']))         { $update_fields[] = "`type` = ?";          $update_values[] = $_POST['type'] ?: null; }
        if (isset($_POST['content']))      { $update_fields[] = "`content` = ?";       $update_values[] = trim($_POST['content']); }
        if (isset($_POST['address1']))     { $update_fields[] = "`address1` = ?";      $update_values[] = trim($_POST['address1']); }
        if (isset($_POST['address2']))     { $update_fields[] = "`address2` = ?";      $update_values[] = trim($_POST['address2']); }
        if (isset($_POST['city']))         { $update_fields[] = "`city` = ?";          $update_values[] = trim($_POST['city']); }
        if (isset($_POST['province']))     { $update_fields[] = "`province` = ?";      $update_values[] = $_POST['province'] ?: null; } // Save code or name? Ensure consistency!
        if (isset($_POST['postal_code']))  { $update_fields[] = "`postal_code` = ?";   $update_values[] = trim($_POST['postal_code']); }
        if (isset($_POST['country']))      { $update_fields[] = "`country` = ?";       $update_values[] = $_POST['country'] ?: null; }
        if (isset($_POST['region']))       { $update_fields[] = "`region` = ?";        $update_values[] = $_POST['region'] ?: null; }
        if (isset($_POST['email']))        { $update_fields[] = "`email` = ?";         $update_values[] = trim($_POST['email']); } // Add email validation if desired
        if (isset($_POST['phone']))        { $update_fields[] = "`phone` = ?";         $update_values[] = trim($_POST['phone']); } // Add phone formatting/validation
        if (isset($_POST['website']))      { $update_fields[] = "`website` = ?";       $update_values[] = trim($_POST['website']); } // Add http prefix/URL validation

        // Add FINAL logo and banner filenames determined above
        $update_fields[] = "`logo` = ?";
        $update_values[] = $final_logo_filename;
        $update_fields[] = "`image` = ?"; // Banner uses 'image' column
        $update_values[] = $final_banner_filename;

        // Add last_updated timestamp
        $update_fields[] = "`last_updated` = ?";
        $update_values[] = date("Y-m-d H:i:s");

        // --- Perform Database Update ---
        if (count($update_fields) > 1 && isset($db) && is_object($db)) { // Check if there are fields to update
            $sql = "UPDATE `facilities` SET " . implode(', ', $update_fields) . " WHERE `facility_id` = ?";
            $update_values[] = $facility_id_to_update; // Add facility ID for WHERE clause

            try {
                 error_log("Facility Update: Attempting DB update for facility ID: " . $facility_id_to_update);
                 $query = $db->query($sql, $update_values);
                 if ($query && !$db->error()) {
                      $_SESSION['facility_form_success_message'] = "Facility details updated successfully!";
                      error_log("Facility Update: DB update successful.");
                 } else {
                      // Use detailed error if available
                      throw new Exception("Database update failed: " . ($db->error() ? $db->error() : 'Query execution failed'));
                 }
            } catch (Exception $e) {
                 $_SESSION['facility_form_error_message'] = "Error saving facility: " . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
                 $_SESSION['facility_form_data'] = $_POST; // Store data for repopulation
                 error_log("Facility Update Exception: " . $e->getMessage());
                 // Clean up newly uploaded files on DB error
                  if ($logo_action === 'upload' && !empty($final_logo_filename)) { if (file_exists($abs_logo_dir . $final_logo_filename)) @unlink($abs_logo_dir . $final_logo_filename); }
                  if ($banner_action === 'upload' && !empty($final_banner_filename)) { if (file_exists($abs_banner_dir . $final_banner_filename)) @unlink($abs_banner_dir . $final_banner_filename); }
            }
        } else {
             // No fields changed other than potentially images which were already handled OR DB missing
             if (count($update_fields) <= 1) {
                  $_SESSION['facility_form_success_message'] = "No changes detected to save.";
             } else {
                  $_SESSION['facility_form_error_message'] = "Database connection error.";
                  error_log("Facility Update: DB object not available for update.");
             }
        }

        // --- Redirect back to the facility form page ---
        $facility_form_url = $_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/';
        
        // header('Location: ' . $facility_form_url);
        // exit;

    } // End secondary check for 'update_facility' flag
    else {
         // POST request to facility page but wrong/missing flag
         $facility_form_url = $_sitepages['edit-facility']['page_url'] ?? $path . 'account/edit-facility/';
        //   . $facility_form_url;
        //  exit;
    }

} // --- END primary IF block ---
?>