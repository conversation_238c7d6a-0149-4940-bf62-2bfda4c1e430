<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}
	
//Table listing
if(ACTION == ''){
	
	//Search
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Search ".$record_name."s
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";

		echo "<div class='panel-content clearfix'>";
			echo "<form class='clearfix' action='' method='get' enctype='multipart/form-data'>";
				echo "<div id='search-fields' class='column clearfix'>";
					echo "<div class='form-field'>
						<label>Search All</label>
						<input type='text' name='search' value='".$searchterm."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Start Date </label>
						<input type='text' name='start_date' value='".(isset($_SESSION['search_start_date'][SECTION_ID]) ? $_SESSION['search_start_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";

					echo "<div class='form-field'>
						<label>End Date </label>
						<input type='text' name='end_date' value='".(isset($_SESSION['search_end_date'][SECTION_ID]) ? $_SESSION['search_end_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";
	
					echo "<div class='form-field'>
						<label>Status </label>
						<select name='status' class='select'>
							<option value=''>All</option>
							<option value='1'".(isset($_SESSION['search_status'][SECTION_ID]) && $_SESSION['search_status'][SECTION_ID] == '1' ? " selected" : "").">Processed</option>
							<option value='0'".(isset($_SESSION['search_status'][SECTION_ID]) && $_SESSION['search_status'][SECTION_ID] == '0' ? " selected" : "").">Failed</option>
						</select>
					</div>";
	
					echo "<div class='form-field'>
						<label>GL Account </label>
						<select name='gl_account' class='select'>
							<option value=''>All</option>";
							foreach($glaccounts as $gl){
								echo "<option value='" .$gl['gl_number']. "'".(isset($_SESSION['search_glnum'][SECTION_ID]) && $_SESSION['search_glnum'][SECTION_ID] == $gl['gl_number'] ? " selected" : "").">" .$gl['gl_name']. "</option>";
							}
						echo "</select>
					</div>";
	
				echo "</div>";

				echo "<div class='buttons-wrapper'>";
					echo "<div class='f_right'>";
						echo "<button type='button' class='button' onclick='exportForm(this.form);'><i class='fa fa-download'></i>Export</button> &nbsp;";
						echo "<button type='submit' class='button'><i class='fa fa-search'></i>Search</button>";
					echo "</div>";
					echo "<button type='button' class='button reset' onclick='document.getElementById(\"clear-search-form\").submit();'><i class='fa fa-times'></i>Clear</button>";
				echo "</div>";
	
			echo "</form>";
			echo "<form id='clear-search-form' name='clear-search-form' class='hidden' action='".PAGE_URL."' method='post'>
				<input type='hidden' name='clear-search' value='Clear' />
				<input type='hidden' name='search' value='' />
				<input type='hidden' name='start_date' value='' />
				<input type='hidden' name='end_date' value='' />
				<input type='hidden' name='xssid' value='".$_COOKIE['xssid']."' />
			</form>";
		echo "</div>";
	echo "</div>";

	//Export Form
	echo "<script>
		function exportForm(this_form) {
			this_form.target=\"_blank\"; 
			this_form.action=\"".$path."exports/export-payments.php\"; 
			this_form.submit(); 
			this_form.target=\"\"; 
			this_form.action=\"\";
		}
	</script>";
	
	//Records 
	echo "<div class='panel'>";
		echo "<div class='panel-header'>".$record_name."s  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";
		
			echo "<thead>";
			echo "<th>No.</th>";
			echo "<th class='{sorter:\"monthDayYear\"}'>Date</th>";	
			echo "<th>GL No.</th>";
			echo "<th>Item No.</th>";
			echo "<th>Name</th>";
			echo "<th>Total</th>";
			echo "<th class='center' width='100px'>Processed</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .$row['payment_number']. "</td>";
					echo "<td>" .date('M j, Y', strtotime($row['payment_date'])). "</td>";
					echo "<td>" .$row['gl_account']. "</td>";
					echo "<td>" .(!empty($row['registration_id']) ? $row['registration_number'] : $row['invoice_number']). "</td>";
					echo "<td>" .(!empty($row['first_name']) ? $row['first_name']." " .$row['last_name'] : $row['ccname']). "</td>";
					echo "<td>$" .number_format($row['amount']+$row['admin_fee'], 2). "</td>";
					echo "<td>" .($row['status'] == 1 ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-eye'></i>View</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo "</div>";	
	echo "</div>";

	
//Edit payment
}else if(ACTION == 'edit'){

	$data = $records_arr[ITEM_ID];
	if(!isset($_POST['save'])){
		$row = $data;
	}
	
	echo "<form class='print_el' action='' method='post' enctype='multipart/form-data'>";
	
		//Order Information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
					<tr>
						<td width='150px'>Payment No:</td>
						<td>".$row['payment_number']."</td>
					</tr>
					<tr>
						<td>Payment Date:</td>
						<td>".date('M j, Y g:iA', strtotime($row['payment_date']))."</td>
					</tr>
					<tr>
						<td>GL Account No:</td>
						<td>" .$row['gl_account']. "</td>
					</tr>";
					if(!empty($row['registration_number'])){
						echo "<tr>
							<td>Registration No:</td>
							<td><a href='" .$sitemap[$_cmssections['registration-registrations']]['page_url']. "?action=edit&item_id=" .$row['registration_id']. "'>" .$row['registration_number']."</a></td>
						</tr>";
					}
					if(!empty($row['invoice_number'])){
						echo "<tr>
							<td>Invoice No:</td>
							<td><a href='" .$sitemap[$_cmssections['transactions-invoices']]['page_url']. "?action=edit&item_id=" .$row['invoice_id']. "'>" .$row['invoice_number']."</a></td>
						</tr>";
					}
					if(!empty($row['account_id'])){
						echo "<tr>
							<td>Account No:</td>
							<td><a href='" .$sitemap[$_cmssections['manage_users']]['page_url']. "?action=edit&item_id=" .$row['account_id']. "'>" .$row['account_id']."</a></td>
						</tr>";
					}
					echo "<tr>
						<td>Name:</td>
						<td>".$row['first_name']." ".$row['last_name']."</td>
					</tr>
					<tr>
						<td>Email:</td>
						<td>".$row['email']."</td>
					</tr>
					<tr>
						<td>Phone:</td>
						<td>".$row['phone']."</td>
					</tr>";
					if(trim($row['bill_address1']) != ''){
						echo "<tr>
							<td valign='top'>Billing Address:</td>
							<td>".(trim($row['bill_address2']) != '' ? $row['bill_address2'].' - ' : '').$row['bill_address1']."<br/>".$row['bill_city'].", ".$row['bill_province'].", ".$row['bill_country']."<br/>".$row['bill_postalcode']."</td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Order Information

		//Transaction Info
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Transaction Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
					echo "<tr>
						<td>Processed By:</td>
						<td>";
						if(!empty($row['processed_by'])){
							echo $row['processed_by_name'];
						}else{
							echo "Unknown";
						}
						echo "</td>
					</tr>";
					echo "<tr>
						<td>Payment Status:</td>
						<td>".($row['status'] == '1' ? 'Processed' : 'Failed')."</td>
					</tr>";
					echo "<tr>
						<td>Total Amount:</td>
						<td>$".number_format($row['amount']+$row['admin_fee'], 2)."</td>
					</tr>";
					echo "<tr>
						<td>Service Fee:</td>
						<td>$".number_format($row['admin_fee'], 2)."</td>
					</tr>";
					echo "<tr>
						<td width='150px'>Payment Type:</td>
						<td>";
						if($row['payment_type'] == 'Credit Card' && !empty($row['ccnumber'])){
							echo $row['cctype']." **** **** **** ".$row['ccnumber']. " &nbsp; " .substr($row['ccexpiry'], 0, 2)."/".substr($row['ccexpiry'], -2, 2); 
						}else{
							echo $row['payment_type'];
						}
						echo "</td>
					</tr>";
					if($row['payment_type'] == 'Credit Card'){
						echo "<tr>
							<td>Response Code:</td>
							<td>".(!empty($row['response_code']) ? $row['response_code'] : "NULL")."</td>
						</tr>";
						echo "<tr>
							<td>Transaction No:</td>
							<td>".(!empty($row['txn_num']) ? $row['txn_num'] : "NULL")."</td>
						</tr>";
						echo "<tr>
							<td>Authorization Code:</td>
							<td>".(!empty($row['auth_code']) ? $row['auth_code'] : "NULL")."</td>
						</tr>";
						echo "<tr>
							<td>CVD Response:</td>
							<td>".(!empty($row['cvd_code']) ? $row['cvd_code'] : "NULL")."</td>
						</tr>";
					}
					echo "<tr>
						<td>Message:</td>
						<td>".(!empty($row['message']) ? $row['message'] : "NULL")."</td>
					</tr>";
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Transaction Info
	
		//Notes
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Notes ".$CMSBuilder->tooltip('Notes', 'Notes are for internal use only. They will not appear on the front-end of the website.')."
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<textarea name='notes' class='textarea'>".(isset($row['notes']) ? $row['notes'] : "")."</textarea>
				</div>";
			echo "</div>";
		echo "</div>"; //Notes
		
		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			//echo "<button type='button' name='print' class='button f_right' onclick='printPage();'><i class='fa fa-print'></i>Print</button>";
			echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
			echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
		echo "</footer>";

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";
	

//Process payment
}else if(ACTION == 'process'){
	include("sections/transactions/process.php");
}

?>