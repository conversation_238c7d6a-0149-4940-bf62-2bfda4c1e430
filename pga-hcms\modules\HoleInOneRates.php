<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['hole-in-one-rates']){
		
	//Define vars
	$errors = false;
	$required = array();
	
	//Get settings
	$settings = array();
	$query = $db->query("SELECT * FROM `hio_settings` WHERE `id`=1");
	if($query && !$db->error()){
		$settings = $db->fetch_array()[0];
	}

	//Get Rates
	$rates = array();
	$query = $db->query("SELECT * FROM `hio_rates` ORDER BY `prize_total` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$rates[] = $row;
		}
	}

	//Save changes
	if(isset($_POST['save'])){
		
		$rates = array();
		
		//Validate
		foreach($_POST['prize'] as $index=>$rate){

			//If not all empty, validate
			if($_POST['prize'][$index] != '' || $_POST['premium'][$index] != '' || $_POST['premium_100'][$index] != ''){
				
				if(!is_numeric($_POST['prize'][$index])){
					$errors[0] = 'Invalid prize amount. Must be currency.';
					$required[] = 'prize_'.$index;
				}else{
					$_POST['prize'][$index] = number_format($_POST['prize'][$index], 2, '.', '');
				}
				if(!is_numeric($_POST['premium'][$index])){
					$errors[1] = 'Invalid premium amount. Must be currency.';
					$required[] = 'premium_'.$index;
				}else{
					$_POST['premium'][$index] = number_format($_POST['premium'][$index], 2, '.', '');
				}
				if(!is_numeric($_POST['premium_100'][$index])){
					$errors[2] = 'Invalid premium amount. Must be currency.';
					$required[] = 'premium_100_'.$index;
				}else{
					$_POST['premium_100'][$index] = number_format($_POST['premium_100'][$index], 2, '.', '');
				}
				
				$rates[] = array(
					'prize' => $_POST['prize'][$index],
					'premium' => $_POST['premium'][$index],
					'premium_100' => $_POST['premium_100'][$index]
				);
				
			}
		}
		
		//Defaults
		if(trim($_POST['admin_fee']) == ''){
			$_POST['admin_fee'] = 0;
		}
				
		//Update
		if(!$errors){	
			$db->new_transaction();
			
			//Update settings
			$params = array(
				$_POST['admin_fee'],
				$_POST['admin_fee_type'],
				date('Y-m-d H:i:s')
			);
			$query = $db->query("UPDATE `hio_settings` SET `admin_fee`=?, `admin_fee_type`=?, `last_updated`=? WHERE `id`=1", $params);
			
			//Insert rates
			$db->query("DELETE FROM `hio_rates`");
			foreach($rates as $index=>$rate){
				
				$params = array(
					$rate['premium'], 
					$rate['premium_100'], 
					$rate['prize'],
					$rate['premium'], 
					$rate['premium_100']
				);
				$db->query("INSERT INTO `hio_rates`(`premium`, `premium_100`, `prize_total`) VALUES(?,?,?) ON DUPLICATE KEY UPDATE `premium` = ?, `premium_100` = ?", $params);
			}			
			if(!$db->error()){
				$db->commit();
				
				$CMSBuilder->set_system_alert('Hole in one premiums were successfully saved.', true);
				header('Location: '.PAGE_URL);
				exit();
				
			}else{
				$CMSBuilder->set_system_alert('Unable to update premiums. '.$db->error(), false);	
			}
			
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}
		
	}
}

?>