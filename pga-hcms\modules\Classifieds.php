<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']) {
	$total_records = $db->get_record_count('classifieds');
	$CMSBuilder->set_widget($_cmssections['classifields'], 'Total Classified Ads', $total_records);
}

if(SECTION_ID == $_cmssections['classifields']) {
	
	// Define vars
	$record_db = 'classifieds';
	$record_id = 'classified_id';
	$record_name = 'Classified Ad';
	
	$filedir = "../uploads/files/";
	$filetypes = array('pdf');
	$file_fields = [
		'file'
	];

	$seo_page_id      = $_sitepages['classifields'];
	$classifieds_page_url = get_page_url($seo_page_id);

	$errors = false;
	$required = array();
	$required_fields = array(
		'title' => 'Title',
		'facility_id' => 'Facility',
		'first_name' => 'First Name',
		'last_name' => 'Last Name',
		'email' => 'Email',
		'phone' => 'Phone',
		'description' => 'Description'
	); // for validation

	// Get Records
	$records_arr = array();
	$params = array(' ');

	if($searchterm != ""){
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
	}
	$query = $db->query("SELECT `classifieds`.*, `facilities`.`facility_name`, CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) AS `posted_by` FROM `$record_db` ".
	"LEFT JOIN `facilities` ON `classifieds`.`facility_id` = `facilities`.`facility_id` ".
	"LEFT JOIN `account_profiles` ON `classifieds`.`account_id` = `account_profiles`.`account_id` ".
	($searchterm != "" ? " WHERE `classifieds`.`title` LIKE ? || `facilities`.`facility_name` LIKE ? || CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ? || CONCAT(`classifieds`.`first_name`, ?, `classifieds`.`last_name`) LIKE ?" : ""). " ORDER BY `classifieds`.`date_added` DESC, `$record_id` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	$facilities = array();
	$facilities_query = $db->query("SELECT * FROM facilities");
	if($facilities_query && !$db->error() && $db->num_rows() > 0) {
		$facilities = $db->fetch_array();
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		$delete = $db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if($delete && !$db->error()){
			if($_POST['old_file'] != ''){
				if(file_exists($filedir.$_POST['old_file'])) {
					unlink($filedir.$_POST['old_file']);
				}
			}
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			sitemap_XML();
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	
	//Save item
	} else if(isset($_POST['save'])){

		// Validate
		$required_missing = false;
		if(!empty($required_fields)) {
			foreach($required_fields as $field_key => $field_name) {
				if(isset($_POST[$field_key])) {
					if(trim($_POST[$field_key]) == '') {
						$required_missing = true;
						array_push($required, $field_key);
					}
				} else {
					$required_missing = true;
					array_push($required, $field_key);
				}
			}
		}
		if($required_missing) {
			$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
		}
		if(!empty($_FILES['file']['size']) && $_FILES['file']['size'] > 20480000){
			$errors[] = 'File size is too large.';
		}
		if(!empty($_FILES['file']['name'])){
			$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
			if(!in_array($ext, $filetypes)){
				$errors[] = 'File type `' .$ext. '` is restricted. Allowed file types include '.implode(', ', $filetypes).'.';
			}
		}
		if(!isset($_POST['showhide'])){
			$_POST['showhide'] = 1;
		}

		$pagename = clean_url($_POST['title']);

		if(!$errors) {
			
			//Upload file
			$file = ($_POST['old_file'] != '' ? $_POST['old_file'] : NULL);
			if(!empty($_FILES['file']['name'])){
				$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
				$filename = $pagename.'-'.date("ymdhis").'.'.$ext;
				
				// $fileUpload = new FileUpload();
				// $fileUpload->load($_FILES['file']['tmp_name']);
				// $fileUpload->save($filedir, $newname);
				// if(file_exists($filedir.$newname)){
				// 	$file = $newname;
				// 	if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
				// 		unlink($filedir.$_POST['old_file']);
				// 	}
				// }
				foreach ($file_fields as $field) {
								$$field = NULL;
							
								if (!empty($_FILES[$field]['name'])) {
									// Check for errors during upload
									if ($_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
										$errors[] = "Error uploading file: " . $_FILES[$field]['name'] . " - " . $_FILES[$field]['error'];
										continue; // Skip this file and continue with the next
									}
							
									// Attempt to copy the file
									if (!@copy($_FILES[$field]['tmp_name'], $filedir . $filename)) {
										$errors[] = "Failed to upload the file: " . $_FILES[$field]['name'];
									} else {
										// Validate file exists
										$$field = check_file($filename, $filedir) ?: NULL;
							
										// Add to array for email attachment
										if ($$field) {
											$attachments[] = $filedir . $filename;
										}
									}
								}
								
						}
				
			}else{
				if(isset($_POST['deletefile']) && $_POST['old_file'] != ''){
					if(file_exists($filedir.$_POST['old_file'])) {
						unlink($filedir.$_POST['old_file']);
					}
					$file = NULL;
				}
			}
			
			//Insert to DB
			$params = array(
				ITEM_ID, 
				USER_LOGGED_IN,
				$_POST['title'], 
				$pagename, 
				$_POST['facility_id'], 
				$_POST['description'], 
				$_POST['first_name'],
				$_POST['last_name'],
				$_POST['email'],
				$_POST['phone'],
				$_POST['public'],
				$_POST['showhide'], 
				$_POST['focus_keyword'],
				$_POST['meta_title'], 
				$_POST['meta_description'],
				date("Y-m-d H:i:s"),
				date("Y-m-d H:i:s"),
				$file,

				$_POST['title'], 
				$pagename, 
				$_POST['facility_id'], 
				$_POST['description'], 
				$_POST['first_name'],
				$_POST['last_name'],
				$_POST['email'],
				$_POST['phone'],
				$_POST['public'],
				$_POST['showhide'], 
				$_POST['focus_keyword'],
				$_POST['meta_title'], 
				$_POST['meta_description'],
				date("Y-m-d H:i:s"),
				$file
			);

			$insert = $db->query("INSERT INTO `classifieds` (`classified_id`, `account_id`, `title`, `page`, `facility_id`, `description`, `first_name`, `last_name`, `email`, `phone`, `public`, `showhide`, `focus_keyword`, `meta_title`, `meta_description`, `date_added`, `last_updated`, `file_name`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `title` = ?, `page` = ?, `facility_id` = ?, `description` = ?, `first_name` = ?, `last_name` = ?, `email` = ?, `phone` = ?, `public` = ?, `showhide` = ?, `focus_keyword` = ?, `meta_title` = ?, `meta_description` = ?, `last_updated` = ?, `file_name` = ?", $params);
			
			if($insert && !$db->error()) {
				$item_id = (ITEM_ID != "" ? ITEM_ID : $db->insert_id());

				// sitemapXML();
				
				//save SEO score
				// if($cms_settings['enhanced_seo']){
				// 	//set new page_url
				// 	$page_url = $siteurl.$root.$classifieds_page.$pagename."-".$item_id."/";
				// 	try{
				// 		$Analyzer->set_page($_POST['focus_keyword'], $page_url, $pagename, $_POST['title'], $item_id, $record_db, $record_id);
				// 		$Analyzer->analyze_page();
				// 	}catch(Exception $e){
				// 		unset($e);
				// 	}
				// 	$Analyzer->save_score();
				// 	if(array_key_exists(3, $Account->roles) || array_key_exists(4, $Account->roles)){
				// 		$new_score = $Analyzer->get_score();
				// 		if(ITEM_ID != '' && $records_arr[ITEM_ID]['seo_score'] != $new_score){
				// 			$seo_message = "<br/><small>Page SEO score has been updated from ".(ITEM_ID == "" ? 0 : number_format($records_arr[ITEM_ID]['seo_score'],1))." to <strong>".$new_score."</strong>.</small>";
				// 		} else if(ITEM_ID != '') {
				// 			$seo_message = "<br/><small>Page SEO score has not changed from <strong>".$new_score."</strong>.</small>";
				// 		} else {
				// 			$seo_message = "<br/><small>Page SEO score is <strong>".$new_score."</strong>.</small>";
				// 		}
				// 	}
				// }

				// Save SEO score
				if($cms_settings['enhanced_seo']){
					// Set new page_url
					$page_url = $siteurl.$root.$classifieds_page_url.$pagename."-".$item_id."/";
		
					try {
						$save_score = $Analyzer->save_score($_POST['focus_keyword'], $page_url, $pagename, $_POST['title'], ($records_arr[ITEM_ID]['seo_score'] ?? NULL), $item_id, $record_db, $record_id);
						if(!empty($save_score) && (MASTER_USER || SEO_USER)){
							$seo_message = "<br/><small>".$save_score."</small>";
						}
					} catch(Exception $e) {
						unset($e);
					}
				}

				$CMSBuilder->set_system_alert($record_name.' was successfully saved.'. (isset($seo_message) ? $seo_message : ''), true);
				header("Location: " .PAGE_URL);
				exit();

			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	}

}

?>