<?php

//Table listing
if(ACTION == ''){
		
	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';
	
	//Display listings
	echo '<div class="panel">
		<div class="panel-header">'.$record_names.
			'<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">
		
				<thead>
					<th width="1px" class="nopadding-r" data-sorter="false"></th>	
					<th width="1px" class="nopadding-r" data-sorter="false"></th>	
					<th>Name</th>
					<th class="center">Entries</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
					<th width="1px" class="nopadding-l" data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				
				foreach($records_arr as $row){
					$seo_class = $seo_tooltip = '';
					if($cms_settings['enhanced_seo'] && (MASTER_USER || SEO_USER)){
						$seo_indicator = $Analyzer->score_tooltip($row['seo_score']);
						$seo_class     = $seo_indicator['class'];
						$seo_tooltip   = $seo_indicator['tooltip'];
					}

					echo '<tr class="'.$seo_class.'" data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-id="'.$row[$record_id].'">
						<td class="handle nopadding-r">'.$seo_tooltip.'<i class="fas fa-arrows-alt"></i></td>
						<td class="nopadding-r">'.($row['image'] ? '<a href="'.$path.$row['full_image'].'" class="light-gallery" rel="prettyPhoto" title="'.$row['name'].'">'.render_gravatar($row['full_image']).'</a>' : '').'</td>
						<td>' .$row['name']. '</td>
						<td class="center">' .($row['num_entries'] ? '<a class="bold" href='.$entries_section['page_url'].'?'.$record_id.'='.$row[$record_id].'>'.$row['num_entries'].'</a>' : $row['num_entries']). '</td>
						<td class="center">'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td class="right"><a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
						<td class="nopadding-l"><a href="'.$row['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td>
					</tr>';	
				}

				echo '</tbody>
			</table>';
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo '</div>	
	</div>';
	

//Image cropping
}else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');

//Display form	
}else{
	
	$image = '';
	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = $data['image'];	
		if(!isset($_POST['save'])){
			$row = $data;
		}
	
		echo '<div class="actions-nav flex-container">
			<div class="flex-column right">
				<small><b>Link to '.$record_name.':</b> '.$data['page_url'].'&nbsp;&nbsp;<a href="'.$data['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td></small>
			</div>
		</div>';
		
	}else if(ACTION == 'add' && !isset($_POST['save'])){		
		unset($row);
	}	

	echo '<form action="" method="post" enctype="multipart/form-data">';
	
		echo $CMSBuilder->important('Deleting a category will delete all related entries. Ensure all entries are categorized correctly before deleting a category.');
		
		//Category details
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show '.$record_name.' '.$CMSBuilder->tooltip('Show '.$record_name, 'Disabling this option will hide all entries within this category.'). '</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"' .(($row['showhide'] ?? 0) ? '' : ' checked'). ' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>

			<div class="panel-content">
				<div class="flex-container">
					<div class="form-field">
						<label>Name'.(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input id="button-text" type="text" name="name" value="' .(isset($row['name']) ? $row['name'] : ''). '" class="input' .(in_array('name', $required) ? ' required' : ''). '" />
					</div>

					<div class="form-field">
						<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
						<select name="ordering" class="select">
							<option value="101">Default</option>';
							for($i=1; $i<101; $i++){
								echo '<option value="' .$i. '" ' .($row['ordering'] == $i ? 'selected' : ''). '>' .$i. '</option>';	
							}
						echo '</select>
					</div>
				</div>
			</div>
		</div>'; //Category details
		
		//Image
		echo '<div class="panel">
			<div class="panel-header">Banner Image
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">';

				//Upload Image
				if($image){
					echo '<div class="img-holder">
						<button type="button" name="recrop" value="image" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
						<a href="'.$path.$imagedir.'1920/'.$image.'" class="light-gallery" rel="prettyphoto" target="_blank" title="">
							<img src="'.$path.$imagedir.'1024/'.$image.'" alt="" />
						</a>
						<input type="checkbox" class="checkbox" name="deleteimage" id="deleteimage" value="1">
						<label for="deleteimage">Delete Current Image</label>
					</div>';
				}

					[$max_W, $max_H] = CMSUploader::max_size('banner', 'image');
					echo '<div class="form-field">
						<label>Upload Image '.(in_array('image', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Upload Image', 'Image must be smaller than '.$_max_filesize['megabytes'].' and larger than '.$max_W.' x '.$max_H.'.').'</label>
						<input type="file" class="input'.(in_array('image', $required) ? ' required' : '').'" name="image" value="" />
					</div>
					
					<div class="form-field">
						<label>Alt Text <small>(SEO)</small>' .$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.'). '</label>
						<input type="text" name="image_alt" value="' .($row['image_alt'] ?? ''). '" class="input" />
					</div>
					
				</div>
			</div>
		</div>';

		//SEO Content/Analysis
		include('includes/widgets/seotabs.php');
			
		//Sticky footer
		include('includes/widgets/formbuttons.php');
		
		echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid'] .'" />
	</form>';
	
}

?>