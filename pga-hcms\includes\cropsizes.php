<?php

//Common crop dimensions
$_cropsizes = [];

//Page banners
$_cropsizes[1]['Pages'] = [
	1920 => ['width' => 1920, 'height' => 500],
	1366 => ['width' => 1366, 'height' => 500],
	1024 => ['width' => 1024, 'height' => 385],
	768  => ['width' => 768, 'height' => 300],
	480  => ['width' => 480, 'height' => 300],
];
$_cropsizes[2]['Pages'] = [
	1920 => ['width' => 1920, 'height' => 666],
	1366 => ['width' => 1366, 'height' => 666],
	1024 => ['width' => 1024, 'height' => 666],
	768  => ['width' => 768, 'height' => 588],
	480  => ['width' => 480, 'height' => 500],
];
$_cropsizes[3]['Pages'] = [
	1920 => ['width' => 1582, 'height' => 540],
	1366 => ['width' => 1306, 'height' => 540],
	1024 => ['width' => 964, 'height' => 488],
	768  => ['width' => 728, 'height' => 450],
	480  => ['width' => 440, 'height' => 450],
];
$_cropsizes[4]['Pages'] = [
	1920 => ['width' => 1036, 'height' => 500],
	1366 => ['width' => 759, 'height' => 500],
	1024 => ['width' => 1024, 'height' => 500],
	768  => ['width' => 768, 'height' => 300],
	480  => ['width' => 480, 'height' => 250],
];

//Slideshow slides
$_cropsizes[1]['Slideshow'] = [
	1920 => ['width' => 1920, 'height' => 930],
	1366 => ['width' => 1366, 'height' => 651],
	1024 => ['width' => 1024, 'height' => 674],
	768  => ['width' => 768, 'height' => 930],
	480  => ['width' => 480, 'height' => 930]
];
$_cropsizes[2]['Slideshow'] = [
	1920 => ['width' => 1920, 'height' => 1080],
	1366 => ['width' => 1366, 'height' => 768],
	1024 => ['width' => 1024, 'height' => 768],
	768  => ['width' => 768, 'height' => 1024],
	480  => ['width' => 480, 'height' => 800]
];
$_cropsizes[3]['Slideshow'] = [
	1920 => ['width' => 1582, 'height' => 760],
	1366 => ['width' => 1306, 'height' => 630],
	1024 => ['width' => 964, 'height' => 540],
	768  => ['width' => 728, 'height' => 540],
	480  => ['width' => 440, 'height' => 540],
];
$_cropsizes[4]['Slideshow'] = [
	1920 => ['width' => 1036, 'height' => 600],
	1366 => ['width' => 759, 'height' => 600],
	1024 => ['width' => 1024, 'height' => 600],
	768  => ['width' => 768, 'height' => 400],
	480  => ['width' => 480, 'height' => 300]
];

//Promo boxes
$_cropsizes[1]['PromoBoxes'] = ['width' => 500, 'height' => 340];
$_cropsizes[2]['PromoBoxes'] = ['width' => 500, 'height' => 340];
$_cropsizes[3]['PromoBoxes'] = ['width' => 476, 'height' => 676];
$_cropsizes[4]['PromoBoxes'] = ['width' => 346, 'height' => 325];

//Call to action
$_cropsizes[1]['CallToAction'] = [
	1920 => ['width' => 1920, 'height' => 1080],
	1366 => ['width' => 1366, 'height' => 768],
	1024 => ['width' => 1024, 'height' => 768],
	768  => ['width' => 768, 'height' => 1024],
	480  => ['width' => 480, 'height' => 768]
];
$_cropsizes[2]['CallToAction'] = [
	1920 => ['width' => 1920, 'height' => 1080],
	1366 => ['width' => 1366, 'height' => 768],
	1024 => ['width' => 1024, 'height' => 768],
	768  => ['width' => 768, 'height' => 1024],
	480  => ['width' => 480, 'height' => 768]
];
$_cropsizes[3]['CallToAction'] = [
	1920 => ['width' => 1920, 'height' => 1080],
	1366 => ['width' => 1366, 'height' => 768],
	1024 => ['width' => 1024, 'height' => 768],
	768  => ['width' => 768, 'height' => 1024],
	480  => ['width' => 480, 'height' => 768]
];
$_cropsizes[4]['CallToAction'] = [
	1920 => ['width' => 1920, 'height' => 1080],
	1366 => ['width' => 1366, 'height' => 768],
	1024 => ['width' => 1024, 'height' => 768],
	768  => ['width' => 768, 'height' => 1024],
	480  => ['width' => 480, 'height' => 768]
];

//Lead-in (Corner)
$_cropsizes[1]['LeadInCorner'] = ['width' => 140, 'height' => 200];
$_cropsizes[2]['LeadInCorner'] = ['width' => 140, 'height' => 200];
$_cropsizes[3]['LeadInCorner'] = ['width' => 140, 'height' => 200];
$_cropsizes[4]['LeadInCorner'] = ['width' => 140, 'height' => 200];

//Lead-in (Intrusive)
$_cropsizes[1]['LeadInPopup'] =  ['width' => 600, 'height' => 200];
$_cropsizes[2]['LeadInPopup'] =  ['width' => 600, 'height' => 200];
$_cropsizes[3]['LeadInPopup'] =  ['width' => 600, 'height' => 200];
$_cropsizes[4]['LeadInPopup'] =  ['width' => 600, 'height' => 200];

//Side-by-side panels
$_cropsizes[1]['SideBySide'] = [
	1920 => ['width' => 785, 'height' => 478],
	1366 => ['width' => 616, 'height' => 451],
	1024 => ['width' => 406, 'height' => 397],
	768  => ['width' => 728, 'height' => 569],
	480  => ['width' => 480, 'height' => 291]
];
$_cropsizes[2]['SideBySide'] = [
	1920 => ['width' => 780, 'height' => 540],
	1366 => ['width' => 722, 'height' => 500],
	1024 => ['width' => 1024, 'height' => 600],
	768  => ['width' => 606, 'height' => 420],
	480  => ['width' => 548, 'height' => 380]
];
$_cropsizes[3]['SideBySide'] = [
	1920 => ['width' => 570, 'height' => 650],
	1366 => ['width' => 485, 'height' => 554],
	1024 => ['width' => 368, 'height' => 420],
	768  => ['width' => 768, 'height' => 512],
	480  => ['width' => 480, 'height' => 320]
];
$_cropsizes[4]['SideBySide'] = $_cropsizes[1]['SideBySide'];

//Parallax panels
$_cropsizes[1]['Parallax'] = [
	1920 => ['width' => 1920, 'height' => 1080],
	1366 => ['width' => 1366, 'height' => 768],
	1024 => ['width' => 1024, 'height' => 768],
	768  => ['width' => 768, 'height' => 1024],
	480  => ['width' => 480, 'height' => 768]
];
$_cropsizes[2]['Parallax'] = [
	1920 => ['width' => 1920, 'height' => 1080],
	1366 => ['width' => 1366, 'height' => 768],
	1024 => ['width' => 1024, 'height' => 768],
	768  => ['width' => 768, 'height' => 1024],
	480  => ['width' => 480, 'height' => 768]
];
$_cropsizes[3]['Parallax'] = [
	1920 => ['width' => 1320, 'height' => 1080],
	1366 => ['width' => 1326, 'height' => 768],
	1024 => ['width' => 984, 'height' => 768],
	768  => ['width' => 728, 'height' => 1024],
	480  => ['width' => 440, 'height' => 768]
];
$_cropsizes[4]['Parallax'] = [
	1920 => ['width' => 1920, 'height' => 1080],
	1366 => ['width' => 1366, 'height' => 768],
	1024 => ['width' => 1024, 'height' => 768],
	768  => ['width' => 768, 'height' => 1024],
	480  => ['width' => 480, 'height' => 768]
];

//Gallery photos
$_cropsizes[1]['GalleryPhoto'] = [
	'featured' => $_cropsizes[1]['PromoBoxes'],
	'thumbs'   => ['width' => 320, 'height' => 320]
];
$_cropsizes[2]['GalleryPhoto'] = [
	'featured' => $_cropsizes[2]['PromoBoxes'],
	'thumbs'   => ['width' => 310, 'height' => 200]
];
$_cropsizes[3]['GalleryPhoto'] = [
	'featured' => $_cropsizes[3]['PromoBoxes'],
	'thumbs'   => ['width' => 300, 'height' => 480]
];
$_cropsizes[4]['GalleryPhoto'] = [
	'featured' => $_cropsizes[4]['PromoBoxes'],
	'thumbs'   => ['width' => 470, 'height' => '']
];


$_cropsizes = $_cropsizes[THEME];



/* --- CROPPING PARAMETERS --- */

/* 	Create a new croptype by creating a unique new key in the $_croptypes array.
	Each key of the child array is the fieldname of an input.  In most cases this is simply 'image'.

	$_croptypes['CropKey'] = [
		'fieldname' => [
			* required fields

			dir* (string)
			Directory to upload image.  Starts from the folder defined by $imagedir.

			label (string)
			Label content that appears above the cropping window.

			width* (integer)
			Crop width in pixels.  An empty string indicates a variable width determined by the user.

			height* (integer)
			Crop height in pixels.  An empty string indicates a variable height determined by the user.

			position (string)
			Unique fieldname to save position data into.  Used as a key of $_POST['image_position'].  Must exist in database table.

			position_label (string)
			Label content that appears above the positioning window.  Default to "Background Positioning"

			auto (boolean)
			Enable/Disable manual cropping.  If enabled, user cropping will be skipped.  Disabled by default.
		]
	];
*/

//Standard page banner
$_croptypes['banner']['image'] = [
	[
		'dir'    => '1920/',
		'label'  => 'Background Image (Desktop)',
		'width'  => $_cropsizes['Pages'][1920]['width'],
		'height' => $_cropsizes['Pages'][1920]['height']
	],
	[
		'dir'    => '1366/',
		'label'  => 'Background Image (Notebook)',
		'width'  => $_cropsizes['Pages'][1366]['width'],
		'height' => $_cropsizes['Pages'][1366]['height']
	],
	[
		'dir'    => '1024/',
		'label'  => 'Background Image (Tablet - Landscape)',
		'width'  => $_cropsizes['Pages'][1024]['width'],
		'height' => $_cropsizes['Pages'][1024]['height']
	],
	[
		'dir'    => '768/',
		'label'  => 'Background Image (Tablet - Portrait)',
		'width'  => $_cropsizes['Pages'][768]['width'],
		'height' => $_cropsizes['Pages'][768]['height']
	],
	[
		'dir'    => '480/',
		'label'  => 'Background Image (Mobile)',
		'width'  => $_cropsizes['Pages'][480]['width'],
		'height' => $_cropsizes['Pages'][480]['height']
	]
];

//Landing page banner
$_croptypes['landing']['image'] = [
	[
		'dir'    => '1920/',
		'label'  => 'Background Image (Desktop)',
		'width'  => $_cropsizes['Parallax'][1920]['width'],
		'height' => $_cropsizes['Parallax'][1920]['height']
	],
	[
		'dir'    => '1366/',
		'label'  => 'Background Image (Notebook)',
		'width'  => $_cropsizes['Parallax'][1366]['width'],
		'height' => $_cropsizes['Parallax'][1366]['height']
	],
	[
		'dir'    => '1024/',
		'label'  => 'Background Image (Tablet - Landscape)',
		'width'  => $_cropsizes['Parallax'][1024]['width'],
		'height' => $_cropsizes['Parallax'][1024]['height']
	],
	[
		'dir'    => '768/',
		'label'  => 'Background Image (Tablet - Portrait)',
		'width'  => $_cropsizes['Parallax'][768]['width'],
		'height' => $_cropsizes['Parallax'][768]['height']
	],
	[
		'dir'    => '480/',
		'label'  => 'Background Image (Mobile)',
		'width'  => $_cropsizes['Parallax'][480]['width'],
		'height' => $_cropsizes['Parallax'][480]['height']
	]
];

//Slideshow
$_croptypes['slideshow']['image'] = [
	[
		'dir'    => '1920/',
		'label'  => 'Slide Image (Desktop)',
		'width'  => $_cropsizes['Slideshow'][1920]['width'],
		'height' => $_cropsizes['Slideshow'][1920]['height']
	],
	[
		'dir'    => '1366/',
		'label'  => 'Slide Image (Notebook)',
		'width'  => $_cropsizes['Slideshow'][1366]['width'],
		'height' => $_cropsizes['Slideshow'][1366]['height']
	],
	[
		'dir'    => '1024/',
		'label'  => 'Slide Image (Tablet - Landscape)',
		'width'  => $_cropsizes['Slideshow'][1024]['width'],
		'height' => $_cropsizes['Slideshow'][1024]['height']
	],
	[
		'dir'    => '768/',
		'label'  => 'Slide Image (Tablet - Portrait)',
		'width'  => $_cropsizes['Slideshow'][768]['width'],
		'height' => $_cropsizes['Slideshow'][768]['height']
	],
	[
		'dir'    => '480/',
		'label'  => 'Slide Image (Mobile)',
		'width'  => $_cropsizes['Slideshow'][480]['width'],
		'height' => $_cropsizes['Slideshow'][480]['height']
	]
];

//Parallax Panel
$_croptypes['parallax']['image'] = [
	[
		'dir'    => '1920/',
		'label'  => 'Background Image (Desktop)',
		'width'  => $_cropsizes['Parallax'][1920]['width'],
		'height' => $_cropsizes['Parallax'][1920]['height'],

		'position' => 'image_desktop_position',
		'position_label' => 'Desktop'
	],
	[
		'dir'    => '1366/',
		'label'  => 'Background Image (Notebook)',
		'width'  => $_cropsizes['Parallax'][1366]['width'],
		'height' => $_cropsizes['Parallax'][1366]['height']
	],
	[
		'dir'    => '1024/',
		'label'  => 'Background Image (Tablet - Landscape)',
		'width'  => $_cropsizes['Parallax'][1024]['width'],
		'height' => $_cropsizes['Parallax'][1024]['height']
	],
	[
		'dir'    => '768/',
		'label'  => 'Background Image (Tablet - Portrait)',
		'width'  => $_cropsizes['Parallax'][768]['width'],
		'height' => $_cropsizes['Parallax'][768]['height'],
		'auto'   => true
	],
	[
		'dir'    => '480/',
		'label'  => 'Background Image (Mobile)',
		'width'  => $_cropsizes['Parallax'][480]['width'],
		'height' => $_cropsizes['Parallax'][480]['height'],
		'auto'   => true
	]
];

//Parallax Panel Mobile
$_croptypes['parallax']['image_mobile'] = [
	[
		'dir'    => '768/',
		'label'  => 'Background Image (Tablet - Portrait)',
		'width'  => $_cropsizes['Parallax'][768]['width'],
		'height' => $_cropsizes['Parallax'][768]['height'],

		'position' => 'image_mobile_position',
		'position_label' => 'Mobile'
	],
	[
		'dir'    => '480/',
		'label'  => 'Background Image (Mobile)',
		'width'  => $_cropsizes['Parallax'][480]['width'],
		'height' => $_cropsizes['Parallax'][480]['height']
	]
];

//CTA Panel
$_croptypes['cta']['image'] = [
	[
		'dir'    => '1920/',
		'label'  => 'Background Image (Desktop)',
		'width'  => $_cropsizes['CallToAction'][1920]['width'],
		'height' => $_cropsizes['CallToAction'][1920]['height'],

		'position' => 'image_desktop_position',
		'position_label' => 'Desktop'
	],
	[
		'dir'    => '1366/',
		'label'  => 'Background Image (Notebook)',
		'width'  => $_cropsizes['CallToAction'][1366]['width'],
		'height' => $_cropsizes['CallToAction'][1366]['height']
	],
	[
		'dir'    => '1024/',
		'label'  => 'Background Image (Tablet - Landscape)',
		'width'  => $_cropsizes['CallToAction'][1024]['width'],
		'height' => $_cropsizes['CallToAction'][1024]['height']
	],
	[
		'dir'    => '768/',
		'label'  => 'Background Image (Tablet - Portrait)',
		'width'  => $_cropsizes['CallToAction'][768]['width'],
		'height' => $_cropsizes['CallToAction'][768]['height'],
		'auto'   => true
	],
	[
		'dir'    => '480/',
		'label'  => 'Background Image (Mobile)',
		'width'  => $_cropsizes['CallToAction'][480]['width'],
		'height' => $_cropsizes['CallToAction'][480]['height'],
		'auto'   => true
	]
];

//CTA Panel Mobile
$_croptypes['cta']['image_mobile'] = [
	[
		'dir'    => '768/',
		'label'  => 'Background Image (Tablet - Portrait)',
		'width'  => $_cropsizes['CallToAction'][768]['width'],
		'height' => $_cropsizes['CallToAction'][768]['height'],
		'position' => 'image_mobile_position',
		'position_label' => 'Mobile'
	],
	[
		'dir'    => '480/',
		'label'  => 'Background Image (Mobile)',
		'width'  => $_cropsizes['CallToAction'][480]['width'],
		'height' => $_cropsizes['CallToAction'][480]['height']
	]
];

//Side by Side Panel
$_croptypes['side']['image'] = [
	[
		'dir'    => '1920/',
		'label'  => 'Side by Side Image (Desktop)',
		'width'  => $_cropsizes['SideBySide'][1920]['width'],
		'height' => $_cropsizes['SideBySide'][1920]['height'],
	],
	[
		'dir'    => '1366/',
		'label'  => 'Side by Side Image (Notebook)',
		'width'  => $_cropsizes['SideBySide'][1366]['width'],
		'height' => $_cropsizes['SideBySide'][1366]['height']
	],
	[
		'dir'    => '1024/',
		'label'  => 'Side by Side Image (Tablet - Landscape)',
		'width'  => $_cropsizes['SideBySide'][1024]['width'],
		'height' => $_cropsizes['SideBySide'][1024]['height']
	],
	[
		'dir'    => '768/',
		'label'  => 'Side by Side Image (Tablet - Portrait)',
		'width'  => $_cropsizes['SideBySide'][768]['width'],
		'height' => $_cropsizes['SideBySide'][768]['height'],
	],
	[
		'dir'    => '480/',
		'label'  => 'Side by Side Image (Mobile)',
		'width'  => $_cropsizes['SideBySide'][480]['width'],
		'height' => $_cropsizes['SideBySide'][480]['height'],
	],
];

//Side by Side Panel
$_croptypes['promo']['image'] = [
	[
		'dir'    => 'thumbs/',
		'width'  => $_cropsizes['PromoBoxes']['width'],
		'height' => $_cropsizes['PromoBoxes']['height'],
		'label'  => 'Promo Box Image'
	]
];

//Staff Listing
$_croptypes['staff']['image'] = [
	[
		'dir'    => 'thumbs/',
		'width'  => 250,
		'height' => 250
	],
	[
		'dir'    => 'featured/',
		'width'  => 450,
		'height' => 450
	]
];

//Leadins
$_croptypes['leadin-corner']['image'] = [
	[
		'dir'    => 'thumbs/',
		'width'  => $_cropsizes['LeadInCorner']['width'],
		'height' => $_cropsizes['LeadInCorner']['height']
	]
];
$_croptypes['leadin-popup']['image'] = [
	[
		'dir'    => 'thumbs/',
		'width'  => $_cropsizes['LeadInPopup']['width'],
		'height' => $_cropsizes['LeadInPopup']['height']
	]
];

//Galleries
$_croptypes['gallery']['image'] = $_croptypes['banner']['image'];

$_croptypes['gallery-photo']['image'] = [
	[
		'dir'    => 'thumbs/',
		'width'  => $_cropsizes['GalleryPhoto']['thumbs']['width'],
		'height' => $_cropsizes['GalleryPhoto']['thumbs']['height'],
	],
	[
		'dir'    => 'featured/',
		'width'  => $_cropsizes['GalleryPhoto']['featured']['width'],
		'height' => $_cropsizes['GalleryPhoto']['featured']['height'],
	]
];

//Account avatar
$_croptypes['avatar']['photo'] = [
	[
		'dir'    => 'thumbs/',
		'width'  => 150,
		'height' => 150
	]
];

//Add mini_image to the promo crop type
$_croptypes['promo']['mini_image'] = [
    [
        'dir'    => 'thumbs/',
        'width'  => 750,
        'height' => 600,
        'label'  => 'Mini Promo Image'
    ]
];

//Partners logos
$_croptypes['partners']['image'] = [
	[
		'dir'    => 'thumbs/',
		'width'  => 364,
		'height' => ''
	]
];

//Blog
$_croptypes['blog_author']['image'] = [
	[
		'dir'    => 'thumbs/',
		'width'  => 150,
		'height' => 150
	]
];
$_croptypes['blog_entry']['image'] = [
	[
		'dir'    => 'thumbs/',
		'label'  => 'Thumbnail',
		'width'  => 428,
		'height' => 320
	]
];

// NEW: Facility Logo Crop Type
$_croptypes['facility_logo']['logo'] = [ // Crop type key 'facility_logo', fieldname 'logo'
    [
        'dir'    => 'thumbs/', // Will save to images/logos/thumbs/
        'width'  => 300,
        'height' => 300,
        'label'  => 'Facility Logo Thumbnail',
        'auto' => true,
    ]
];

$_croptypes['award_winners_img']['image'] = [ // Crop type key 'award_winners', fieldname 'logo'
    [
        'dir'    => 'thumbs/', 
        'width'  => 300,
        'height' => 300,
        'label'  => 'Award Winner'
      
    ]
];

$_croptypes['top_settings']['image'] = [
	[
		'dir'    => 'logos/',
		'width'  => 400,
		'height' => 400
	]
];

?>