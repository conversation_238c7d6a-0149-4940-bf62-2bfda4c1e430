<?php

//Login prompt
if(!USER_LOGGED_IN){
	$html .= $Account->important('<i class="fa fa-lock"></i> Account holders <a href="' .$_sitepages['login']['page_url']. '?redirect=' .base64_encode($_SERVER['REQUEST_URI']). '">login</a> for faster checkout and order tracking.');
}

$html .= '<form name="checkout-form" action="" method="post" id="checkout-form">';
	$html .= '<p><small>Required Fields</small> <strong class="color-red">*</strong></p>';

	//Tournaments
	if(EVENT_TYPE == 2){
		
		//Personal
		$html .= '<h4>Personal Information</h4>
		<fieldset class="clearfix form-grid">
			<div class="form-column f_left">
				<label>First Name <strong class="color-red">*</strong></label>
				<input type="text" name="first_name" value="'.(isset($_SESSION['reg']['checkout']['first_name']) ? $_SESSION['reg']['checkout']['first_name'] : (USER_LOGGED_IN ? $Account->first_name : '')).'" class="input'.(in_array('first_name', $required) ? ' required' : '').'" />
			</div>
			<div class="form-column f_right">	
				<label>Last Name <strong class="color-red">*</strong></label>
				<input type="text" name="last_name" value="'.(isset($_SESSION['reg']['checkout']['last_name']) ? $_SESSION['reg']['checkout']['last_name'] : (USER_LOGGED_IN ? $Account->last_name : '')).'" class="input'.(in_array('last_name', $required) ? ' required' : '').'" />
			</div>
			<div class="form-column f_left">
				<label>Email Address <strong class="color-red">*</strong></label>
				<input type="text" name="email" value="'.(isset($_SESSION['reg']['checkout']['email']) ? $_SESSION['reg']['checkout']['email'] : (USER_LOGGED_IN ? $Account->email : '')).'" class="input'.(in_array('email', $required) ? ' required' : '').'" />
			</div>
			<div class="form-column f_right">
				<label>Phone Number <strong class="color-red">*</strong></label>
				<input type="text" name="phone" class="input' .(in_array('phone', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['phone']) ? $_SESSION['reg']['checkout']['phone'] : (USER_LOGGED_IN ? $Account->phone : '')). '" />
			</div>
		</fieldset>';

		//Team events
		foreach($team_events as $item_id=>$item){
			$html .= '<h4>Partner Information: <small>' .$item['event_name']. '</small></h4>
			<fieldset class="clearfix form-grid partner-info">
			<div class="form-field-container">
				<div class="form-column f_left form-field">
					<label>Select Partner (Eligible Member)</label>
					<select name="partner_id['.$item_id.']" class="select">
						<option value="">- Please Choose -</option>';
						$members = $Registration->get_eligible_attendees($item['occurrence_id']);
						foreach($members as $member){
							if($member['account_id'] != USER_LOGGED_IN && !$member['overdue_invoices']){
								$html .= '<option value="' .$member['account_id']. '"' .(isset($item['attendees'][0]['partner']['account_id']) && $item['attendees'][0]['partner']['account_id'] == $member['account_id'] ? ' selected' : ''). '>' .$member['last_name'].', '.$member['first_name']. ' - ' .(!empty($member['facility_name']) ? $member['facility_name'] : 'No Facility'). '</option>';
							}
						}
					$html .= '</select>
				</div>
				<div class="clear clearfix">
					<div class="form-column f_left">
						<label><strong>OR</strong> Enter Partner Name (Non-Member)</label>
						<input type="text" name="partner_first_name['.$item_id.']" class="input half f_left" placeholder="First Name" value="' .(isset($item['attendees'][0]['partner']['first_name']) && empty($item['attendees'][0]['partner']['account_id']) ? $item['attendees'][0]['partner']['first_name'] : ''). '" />
						<input type="text" name="partner_last_name['.$item_id.']" class="input half f_right" placeholder="Last Name" value="' .(isset($item['attendees'][0]['partner']['last_name']) && empty($item['attendees'][0]['partner']['account_id']) ? $item['attendees'][0]['partner']['last_name'] : ''). '" />
					</div>
					<div class="form-column f_right">
						<label>Partner Handicap</label>
						<input type="text" name="partner_handicap['.$item_id.']" class="input" value="' .(isset($item['attendees'][0]['partner']['handicap']) ? $item['attendees'][0]['partner']['handicap'] : ''). '" />
					</div>
				</div>
				</div>
				<small class="clear">Note: If you do not currently know who your partner is, you can always enter this information later.</small>
			</fieldset>';
		}
	
	//Events	
	}else{
				
		//Contact
		$html .= '<h4>Contact Information</h4>
		<fieldset class="clearfix form-grid">
			<div class="form-column f_left">
				<label>First Name <strong class="color-red">*</strong></label>
				<input type="text" name="first_name" value="'.(isset($_SESSION['reg']['checkout']['first_name']) ? $_SESSION['reg']['checkout']['first_name'] : (USER_LOGGED_IN ? $Account->first_name : '')).'" class="input'.(in_array('first_name', $required) ? ' required' : '').'" />
			</div>
			<div class="form-column f_right">	
				<label>Last Name <strong class="color-red">*</strong></label>
				<input type="text" name="last_name" value="'.(isset($_SESSION['reg']['checkout']['last_name']) ? $_SESSION['reg']['checkout']['last_name'] : (USER_LOGGED_IN ? $Account->last_name : '')).'" class="input'.(in_array('last_name', $required) ? ' required' : '').'" />
			</div>
			<div class="form-column f_left">
				<label>Email Address <strong class="color-red">*</strong></label>
				<input type="text" name="email" value="'.(isset($_SESSION['reg']['checkout']['email']) ? $_SESSION['reg']['checkout']['email'] : (USER_LOGGED_IN ? $Account->email : '')).'" class="input'.(in_array('email', $required) ? ' required' : '').'" />
			</div>
			<div class="form-column f_right">
				<label>Phone Number <strong class="color-red">*</strong></label>
				<input type="text" name="phone" class="input' .(in_array('phone', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['phone']) ? $_SESSION['reg']['checkout']['phone'] : (USER_LOGGED_IN ? $Account->phone : '')). '" />
			</div>
			<div class="form-column f_left">
				<label>Company Name</label>
				<input type="text" name="company" class="input' .(in_array('company', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['company']) ? $_SESSION['reg']['checkout']['company'] : (USER_LOGGED_IN ? $Account->company : '')). '" />
			</div>
			<div class="form-column f_right">
				<label>Golf Facility</label>
				<input type="text" name="facility" class="input' .(in_array('facility', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['facility']) ? $_SESSION['reg']['checkout']['facility'] : ''). '" />
			</div>';
			
			// <br class="clear" /><br /><hr />
			
			// $html .= '<div class="form-column f_left">';
			$html .= '<div class="form-field">
				
				<label>Mailing Address <strong class="color-red">*</strong></label>
				<input type="text" name="address1" class="input' .(in_array('address1', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['address1']) ? $_SESSION['reg']['checkout']['address1'] : (USER_LOGGED_IN ? $Account->address1 : '')). '" />

				</div><div class="form-field">

				<label>Unit No.</label>
				<input type="text" name="address2" class="input' .(in_array('address2', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['address2']) ? $_SESSION['reg']['checkout']['address2'] : (USER_LOGGED_IN ? $Account->address2 : '')). '" />

				</div><div class="form-field">

				<label>City/Town <strong class="color-red">*</strong></label>
				<input type="text" name="city" class="input' .(in_array('city', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['city']) ? $_SESSION['reg']['checkout']['city'] : (USER_LOGGED_IN ? $Account->city : '')). '" />
			</div>';

			// $html .= '<div class="form-column f_left">
			// $html .= '<div class="form-field">';
		
				$selected_province = (isset($_SESSION['reg']['checkout']['province']) ? $_SESSION['reg']['checkout']['province'] : (USER_LOGGED_IN && $Account->province != '' ? $Account->province : 'AB'));
				$selected_country = (isset($_SESSION['reg']['checkout']['country']) ? $_SESSION['reg']['checkout']['country'] : (USER_LOGGED_IN && $Account->country != '' ? $Account->country : 'CA'));

				$html .= '<div class="form-field addr" id="addr-CA" style="' .($selected_country != 'CA' && $selected_country != '' ? "display:none;" : ""). '">
					<label>Province/State <strong class="color-red">*</strong></label>
					<select name="province" class="select' .(in_array('province', $required) ? ' required' : ''). '">
						<option value="">- Select -</option>';
						// for($p=1; $p<=count($provinces); $p++){
						// 	$html .= '<option value="' .$provinces[$p][1]. '"' .($selected_province == $provinces[$p][1] ? ' selected' : ''). '>' .$provinces[$p][0]. '</option>';  
						// }
						if(isset($provinces)&&is_array($provinces)):
							$html .= '<optgroup label="Canada">';
							foreach($provinces as $code=>$name){
								$selected = ($selected_province == $code ? ' selected' : '');
								$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
							}
							$html .= '</optgroup>';
						endif;
						if(isset($states)&&is_array($states)):
							$html .= '<optgroup label="United States">';
							foreach($states as $code=>$name){
								$selected = ($selected_province == $code ? ' selected' : '');
								$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
							}
							$html .= '</optgroup>';
						endif;
					$html .= '</select>
				</div>';
				
				$html.= '<div class="form-field addr" id="addr-US" style="' .($selected_country == 'US' ? "" : "display:none;"). '">
					<label>State <strong class="color-red">*</strong></label>
					<select name="state" class="select' .(in_array('province', $required) ? ' required' : ''). '">
						<option value="">- Select -</option>';
						for($p=1; $p<=count($states); $p++){
							$html .= '<option value="' .$states[$p][1]. '"' .($selected_province == $states[$p][1] ? ' selected' : ''). '>' .$states[$p][0]. '</option>';  
						}
					$html .= '</select>
				</div>
				<div class="form-field addr" id="addr-ALT" style="' .($selected_country != 'CA' && $selected_country != 'US' && $selected_country != '' ? "" : "display:none;"). '">
					<label>Region <strong class="color-red">*</strong></label>
					<input type="text" name="region" class="input' .(in_array('province', $required) ? ' required' : ''). '" value="' .$selected_province. '" />
				</div>';

				$html .= '<div class="form-field">
				<label>Postal/Zip Code <strong class="color-red">*</strong></label>
				<input type="text" name="postal_code" class="input' .(in_array('postal_code', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['postal_code']) ? $_SESSION['reg']['checkout']['postal_code'] : (USER_LOGGED_IN ? $Account->postal_code : '')). '" />

				</div><div class="form-field">

				<label>Country <strong class="color-red">*</strong></label>
				<select name="country" class="select country' .(in_array('country', $required) ? ' required' : ''). '" id="addr">
					<option value="">- Select -</option>';
					foreach($countries as $code=>$country){
						$html .= '<option value="' .$code. '"' .($selected_country == $code ? ' selected' : ''). '>' .$country. '</option>';
					}
				$html .= '</select>';

			// $html .='</div><div class="form-field">';
			
			$html .= '</div>
		</fieldset>';
		
	}

	//Credit cards for tournaments and pay now events
	if((EVENT_TYPE == 2 || (EVENT_TYPE != 2 && $pay_now_option)) && $ordertotal > 0){
		
		//Billing
		$html .= '<h4>Billing Information</h4>';
		if(!empty($billing_profiles)){
			foreach($billing_profiles as $billing){
				$card_errors = array();

				//Check card expiry
				$expiry_date = '20'.substr($billing['ccexpiry'], -2, 2).substr($billing['ccexpiry'], 0, 2).'01';
				if($expiry_date <= date("Ymd")){
					$card_errors[] = 'Credit card is expired. Please choose another.';
				}

				//Check card type
				$valid_card = false;
				$accepted_cards = array();
				foreach($payment_options as $payopt){
					$accepted_cards[] = $payopt['name'];
					if($billing['cctype'] == $payopt['type']){
						$valid_card = true;
					}
				}
				if(!$valid_card){
					$card_errors[] = 'Invalid card type. We currently only accept '.implode(' &amp; ', $accepted_cards).'.';
				}

				$billing_label = $billing['cctype']. ' **** **** **** '.$billing['ccnumber']. ' &nbsp; '.substr($billing['ccexpiry'], 0, 2).'/'.substr($billing['ccexpiry'], -2, 2);
				if(empty($card_errors)){
					$html .= '<fieldset class="form-grid">
						<input type="radio" class="radio billing-toggle" id="billing-' .$billing['billing_id']. '" name="billing_id" value="' .$billing['billing_id']. '" onclick="billingToggle(false);"' .(isset($_SESSION['reg']['checkout']['billing_id']) && $_SESSION['reg']['checkout']['billing_id'] == $billing['billing_id'] ? ' checked' : ''). ' />
						<label class="billing-new-1" for="billing-' .$billing['billing_id']. '">&nbsp;&nbsp;' .$billing_label. '</label>
					</fieldset>';
				}else{
					$html .= '<fieldset class="form-grid">
						<input type="radio" class="radio billing-toggle" id="billing-' .$billing['billing_id']. '" disabled />
						<label class="billing-new-1" for="billing-' .$billing['billing_id']. '">&nbsp;&nbsp;' .$billing_label. '</label>
						<small class="color-red"><i class="fa fa-exclamation-triangle"></i> ' .(implode(' ', $card_errors)). '</small>
					</fieldset>';
				}
			}
		}
		$html .= '<fieldset class="clearfix form-grid-container">';
			if(!empty($billing_profiles)){
				$html .= '<input type="radio" class="radio billing-toggle" name="billing_id" id="billing-new" value="" onclick="billingToggle(true);"' .(isset($_SESSION['reg']['checkout']['billing_id']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? ' checked' : ''). ' />
				<label for="billing-new" class="billing-new">&nbsp;&nbsp;New Credit Card</label>';
			}

			//New profile form
			$html .= '<div id="billing-profile" class="clear" style="' .(empty($billing_profiles) || (isset($_SESSION['reg']['checkout']['billing_id']) && $_SESSION['reg']['checkout']['billing_id'] == '') ? '' : 'display:none;'). '">' .(!empty($billing_profiles) ? '<br />' : '');
				
				if(EVENT_TYPE != 2){
					$html .= '<p><small><input type="checkbox" class="checkbox" name="use_mailing" id="use_mailing" value="1" />
					<label for="use_mailing">Billing address is the same as mailing address</label></small></p>';
				}
				
				// $html .= '<div class="form-grid no-border"><div class="form-column f_left">
				// 	<label>Billing Address <strong class="color-red">*</strong></label>
				// 	<input type="text" name="bill_address1" class="input' .(in_array('bill_address1', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['bill_address1']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_address1'] : (USER_LOGGED_IN ? $Account->address1 : '')). '" />
				// 	<label>Unit No.</label>
				// 	<input type="text" name="bill_address2" class="input' .(in_array('bill_address2', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['bill_address2']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_address2'] : (USER_LOGGED_IN ? $Account->address2 : '')). '" />
				// 	<label>City/Town <strong class="color-red">*</strong></label>
				// 	<input type="text" name="bill_city" class="input' .(in_array('bill_city', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['bill_city']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_city'] : (USER_LOGGED_IN ? $Account->city : '')). '" />
				// </div>
				// <div class="form-column f_right">';
		
				// 	$selected_province = (isset($_SESSION['reg']['checkout']['bill_province']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_province'] : (USER_LOGGED_IN && $Account->province != '' ? $Account->province : 'AB'));
				// 	$selected_country = (isset($_SESSION['reg']['checkout']['bill_country']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_country'] : (USER_LOGGED_IN && $Account->country != '' ? $Account->country : 'CA'));

				// 	$html .= '<div class="form-field billaddr" id="billaddr-CA" style="' .($selected_country != 'CA' && $selected_country != '' ? "display:none;" : ""). '">
				// 		<label>Province <strong class="color-red">*</strong></label>
				// 		<select name="bill_province" class="select' .(in_array('bill_province', $required) ? ' required' : ''). '">
				// 			<option value="">- Select -</option>';
				// 			for($p=1; $p<=count($provinces); $p++){
				// 				$html .= '<option value="' .$provinces[$p][1]. '"' .($selected_province == $provinces[$p][1] ? ' selected' : ''). '>' .$provinces[$p][0]. '</option>';  
				// 			}
				// 		$html .= '</select>
				// 	</div>
				// 	<div class="form-field billaddr" id="billaddr-US" style="' .($selected_country == 'US' ? "" : "display:none;"). '">
				// 		<label>State <strong class="color-red">*</strong></label>
				// 		<select name="bill_state" class="select' .(in_array('bill_province', $required) ? ' required' : ''). '">
				// 			<option value="">- Select -</option>';
				// 			for($p=1; $p<=count($states); $p++){
				// 				$html .= '<option value="' .$states[$p][1]. '"' .($selected_province == $states[$p][1] ? ' selected' : ''). '>' .$states[$p][0]. '</option>';  
				// 			}
				// 		$html .= '</select>
				// 	</div>
				// 	<div class="form-field billaddr" id="billaddr-ALT" style="' .($selected_country != 'CA' && $selected_country != 'US' && $selected_country != '' ? "" : "display:none;"). '">
				// 		<label>Region <strong class="color-red">*</strong></label>
				// 		<input type="text" name="bill_region" class="input' .(in_array('bill_province', $required) ? ' required' : ''). '" value="' .$selected_province. '" />
				// 	</div>

				// 	<label>Postal/Zip Code <strong class="color-red">*</strong></label>
				// 	<input type="text" name="bill_postalcode" class="input' .(in_array('bill_postalcode', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['bill_postalcode']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_postalcode'] : (USER_LOGGED_IN ? $Account->postal_code : '')). '" />

				// 	<label>Country <strong class="color-red">*</strong></label>
				// 	<select name="bill_country" class="select country' .(in_array('bill_country', $required) ? ' required' : ''). '" id="billaddr">
				// 		<option value="">- Select -</option>';
				// 		foreach($countries as $code=>$country){
				// 			$html .= '<option value="' .$code. '"' .($selected_country == $code ? ' selected' : ''). '>' .$country. '</option>';
				// 		}
				// 	$html .= '</select>';
		
				// $html .= '</div>
				// </div><hr />';

                $html .= '<div class="form-grid no-border">';
                $html .= '<div class="form-column f_left">';
					$html .= '<label>Billing Address <strong class="color-red">*</strong></label>
					<input type="text" name="bill_address1" class="input' .(in_array('bill_address1', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['bill_address1']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_address1'] : (USER_LOGGED_IN ? $Account->address1 : '')). '" />';

					$html .= '<label>Unit No.</label>
					<input type="text" name="bill_address2" class="input' .(in_array('bill_address2', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['bill_address2']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_address2'] : (USER_LOGGED_IN ? $Account->address2 : '')). '" />';

					$html .= '<label>City/Town <strong class="color-red">*</strong></label>
					<input type="text" name="bill_city" class="input' .(in_array('bill_city', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['bill_city']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_city'] : (USER_LOGGED_IN ? $Account->city : '')). '" />
				</div>';

				$html .= '<div class="form-column f_right">';
		
					$selected_province = (isset($_SESSION['reg']['checkout']['bill_province']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_province'] : (USER_LOGGED_IN && $Account->province != '' ? $Account->province : 'AB'));
					$selected_country = (isset($_SESSION['reg']['checkout']['bill_country']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_country'] : (USER_LOGGED_IN && $Account->country != '' ? $Account->country : 'CA'));

					$html .= '<div class=" billaddr" id="billaddr-CA" style="' .($selected_country != 'CA' && $selected_country != '' ? "display:none;" : ""). '">
						<label for="bill_province">Province <strong class="color-red">*</strong></label>
						<select name="bill_province" id ="bill_province" class="select' .(in_array('bill_province', $required) ? ' required' : ''). '">
							<option value="">- Select -</option>';
							// for($p=1; $p<=count($provinces); $p++){
							// 	$html .= '<option value="' .$provinces[$p][1]. '"' .($selected_province == $provinces[$p][1] ? ' selected' : ''). '>' .$provinces[$p][0]. '</option>';  
							// }
							if(isset($provinces)&&is_array($provinces)):
							$html .= '<optgroup label="Canada">';
							foreach($provinces as $code=>$name){
								$selected = ($selected_province == $code ? ' selected' : '');
								$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
							}
							$html .= '</optgroup>';
							endif;
							if(isset($states)&&is_array($states)):
								$html .= '<optgroup label="United States">';
								foreach($states as $code=>$name){
									$selected = ($selected_province == $code ? ' selected' : '');
									$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
								}
								$html .= '</optgroup>';
							endif;
						$html .= '</select>
					</div>';

					$html .= '<div class="form-field billaddr" id="billaddr-US" style="' .($selected_country == 'US' ? "" : "display:none;"). '">
						<label>State <strong class="color-red">*</strong></label>
						<select name="bill_state" class="select' .(in_array('bill_province', $required) ? ' required' : ''). '">
							<option value="">- Select -</option>';
							for($p=1; $p<=count($states); $p++){
								$html .= '<option value="' .$states[$p][1]. '"' .($selected_province == $states[$p][1] ? ' selected' : ''). '>' .$states[$p][0]. '</option>';  
							}
						$html .= '</select>
					</div>';
                    
					$html .= '<div class="form-field billaddr" id="billaddr-ALT" style="' .($selected_country != 'CA' && $selected_country != 'US' && $selected_country != '' ? "" : "display:none;"). '">
						<label>Region <strong class="color-red">*</strong></label>
						<input type="text" name="bill_region" class="input' .(in_array('bill_province', $required) ? ' required' : ''). '" value="' .$selected_province. '" />
					</div>';

					$html .= '<label>Postal/Zip Code <strong class="color-red">*</strong></label>
					<input type="text" name="bill_postalcode" class="input' .(in_array('bill_postalcode', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['bill_postalcode']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['bill_postalcode'] : (USER_LOGGED_IN ? $Account->postal_code : '')). '" />';

					$html .= '<label>Country <strong class="color-red">*</strong></label>
					<select name="bill_country" class="select country' .(in_array('bill_country', $required) ? ' required' : ''). '" id="billaddr">
						<option value="">- Select -</option>';
						foreach($countries as $code=>$country){
							$html .= '<option value="' .$code. '"' .($selected_country == $code ? ' selected' : ''). '>' .$country. '</option>';
						}
					$html .= '</select>';
		
				$html .= '</div>
				</div><hr />';
                 
                $html .= '<div class="form-grid no-border">';
				$html .= '<div class="form-field">
					<label>Cardholder Name <strong class="color-red">*</strong></label>
					<input type="text" name="ccname" class="input' .(in_array('ccname', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['reg']['checkout']['ccname']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_SESSION['reg']['checkout']['ccname'] : ''). '" /></div>';

				// 	$html .= '<div class="form-field"><label for="ccnumber">Card Number <strong class="color-red">*</strong> &nbsp; ';
				// 	foreach($payment_options as $payopt){
				// 		$cctype = ($payopt['type'] == 'MC' ? 'mastercard' : strtolower($payopt['type']));
				// 		$html .= '<i class="fa fa-cc-' .$cctype. ' fa-lg" title="' .$payopt['name']. '"></i> ';
				// 	}
				// 	$html .= '</label>
				// 	<input type="text" id="ccnumber" name="ccnumber" class="input number' .(in_array('ccnumber', $required) ? ' required' : ''). '" value="' .(isset($_POST['ccnumber']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_POST['ccnumber'] : ''). '" maxlength="16" />
				// </div>';

    			$html .= '<div class="form-field expiry-date-fields" style="display: grid; grid-template-columns: 1fr 1fr; column-gap: 10px;">
                    <div>
					<label for="exp_month">Expiry Date <strong class="color-red">*</strong></label>
					<select name="exp_month" id="exp_month" class="select half f_left' .(in_array('exp_month', $required) ? ' required' : ''). '">
					<option value="">- Month -</option>';
					foreach($months as $key=>$value){
						$key = str_pad($key, 2, '0', STR_PAD_LEFT);
						$html .= '<option value="' .$key. '"' .(isset($_SESSION['reg']['checkout']['exp_month']) && $_SESSION['reg']['checkout']['exp_month'] == $key && $_SESSION['reg']['checkout']['billing_id'] == '' ? ' selected' : ''). '>' .$value. ' (' .$key. ')</option>';	
					}
					$html .= '</select>
                    </div>
                    <div>
                    <label for="exp_year" style="visibility: hidden;">Expiry Year</label>
					<select name="exp_year" class="select half f_right' .(in_array('exp_year', $required) ? ' required' : ''). '">
					<option value="">- Year -</option>';
					for($y=date('Y'); $y<=(date('Y')+20); $y++){
						$html .= '<option value="' .substr($y, -2). '"' .(isset($_SESSION['reg']['checkout']['exp_year']) && $_SESSION['reg']['checkout']['exp_year'] == substr($y, -2) && $_SESSION['reg']['checkout']['billing_id'] == '' ? ' selected' : ''). '>' .$y. '</option>';	
					}
					$html .= '</select></div></div>';
                    $html .= '<div class="form-field">
					<label for="cvv">CVV Code <strong class="color-red">*</strong></label>
					<input type="text" name="cvv" id="cvv" class="input number half' .(in_array('cvv', $required) ? ' required' : ''). '" value="' .(isset($_POST['cvv']) ? $_POST['cvv'] : ''). '" maxlength="4" />
				</div>';

                //
                $html .= '<div class="form-field"><label for="ccnumber">Card Number <strong class="color-red">*</strong> &nbsp; ';
					foreach($payment_options as $payopt){
						$cctype = ($payopt['type'] == 'MC' ? 'mastercard' : strtolower($payopt['type']));
						$html .= '<i class="fa fa-cc-' .$cctype. ' fa-lg" title="' .$payopt['name']. '"></i> ';
					}
					$html .= '</label>
					<input type="text" id="ccnumber" name="ccnumber" class="input number' .(in_array('ccnumber', $required) ? ' required' : ''). '" value="' .(isset($_POST['ccnumber']) && $_SESSION['reg']['checkout']['billing_id'] == '' ? $_POST['ccnumber'] : ''). '" maxlength="16" />
				</div>';
		$html .= '</div>';
                //
		
				if(USER_LOGGED_IN){
					$html .= '<div class="clear ccsave-container">
						<small><input type="checkbox" name="ccsave" id="ccsave" class="checkbox" value="1"' .(!isset($_SESSION['reg']['checkout']['ccsave']) || (isset($_SESSION['reg']['checkout']['ccsave']) && $_SESSION['reg']['checkout']['ccsave'] == true) ? ' checked' : ''). ' />
						<label for="ccsave">Save this credit card to my billing profiles</label></small>
					</div>';
				}
		$html .= '</div>';
		$html .= '</fieldset>';
	}

	//Terms
	if(!empty($waiver_forms)){
		$html .= '<h4>Terms &amp; Conditions</h4>
		<fieldset class="clearfix">';
			$count=0;
			foreach($waiver_forms as $waiver){
				$count++;
				$html .= '<span class="' .(in_array('waiver-' .$waiver['waiver_id'], $required) ? 'color-red' : ''). '">' .$waiver['title']. '</span>'.
				($waiver['required'] ? ' <strong class="color-red">*</strong>' : ''). '<br />
				<small><input type="checkbox" class="checkbox" name="waiver-' .$waiver['waiver_id']. '" id="waiver-' .$waiver['waiver_id']. '" value="1"' .(isset($_SESSION['reg']['checkout']['waivers'][$waiver['waiver_id']]) && $_SESSION['reg']['checkout']['waivers'][$waiver['waiver_id']] == true ? ' checked' : ''). ' />
				<label for="waiver-' .$waiver['waiver_id']. '">I agree/consent to the conditions outlined below:</label></small>
				<div class="terms-box' .($count == count($waiver_forms) ? ' nomargin' : ''). '">';
					if($waiver['file_name'] != '' && file_exists('uploads/files/'.$waiver['file_name'])){
						$html .= '<a href="' .$path.'uploads/files/'.$waiver['file_name']. '" target="_blank"><i class="fa fa-file-pdf-o"></i>&nbsp; Download ' .$waiver['title']. '</a><br />';
					}
					$html .= nl2br($waiver['description']).'
				</div>' .($count < count($waiver_forms) ? '<hr />' : '');
			}	
		$html .= '</fieldset>';
	}

	//Payment options
	$html .= '<div class="form-buttons">';

		//Notice
		if($ordertotal > 0){
			if(!$pay_later_option){
				if(EVENT_TYPE != 2){
					$html .= '<p class="f_right"><small><i class="fa fa-exclamation-triangle"></i> Immediate payment is required for this registration.</small></p>';
				}
			}else if(!$pay_now_option){
				$html .= '<p class="f_right"><small><i class="fa fa-exclamation-triangle"></i> Online payment is not currently available for this order.</small></p>';
			}
		}

		//Buttons
		$html .= '<div class="clear">';
			if($ordertotal > 0){
				if($pay_later_option){
					$html .= '<button type="submit" name="payment" class="button solid f_right primary red" value="0">Pay Later<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>';	
				}else{
					if(EVENT_TYPE != 2){
						$html .= '<button type="button" class="button solid f_right primary red" disabled>Pay Later<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>';
					}
				}
				if($pay_now_option){
					$html .= '<button type="submit" name="payment" class="button solid f_right primary red" value="1">Pay Now<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>';
				}else{
					$html .= '<button type="button" class="button solid f_right" disabled primary red>Pay Now<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>';
				}
			}else{
				$html .= '<button type="submit" name="payment" class="button solid f_right primary red" value="1">Continue<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>';
			}
			$html .= '<a href="' .$_sitepages['reg_cart']['page_url']. '" class="previous f_right button primary black">Cancel<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
		</div>
	</div>';
		
	$html .= '<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
</form>';

//Set panel content
$page['page_panels'][$panel_id]['content'] .= $html;

?>