<?php

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

if(isset($_POST) && USER_LOGGED_IN){

	$record_db = $_POST['table'];
	$record_id = $_POST['table_id'];
	$item_id = $_POST['item_id'];
	$unique = (isset($_POST['unique']) ? $_POST['unique'] : '');
	$record_column = $_POST['item_col'];
	$status = (bool)$_POST['item_status'];

	$db->new_transaction();

	$params = array($status, $item_id);
	$db->query("UPDATE `".$record_db."` SET `".$record_column."` = ? WHERE `".$record_id."` = ?", $params);
	if($unique){
		//get relevant rows
		$db->query("SELECT `".$unique."` FROM `".$record_db."` WHERE `".$record_id."` = ?", array($item_id));
		if(!$db->error() && $db->num_rows()) {
			$unique_id = $db->fetch_array();
			$unique_id = $unique_id[0][$unique];

			$reverse_status = false;
			$params = array($reverse_status, $item_id, $unique_id);
			$db->query("UPDATE `".$record_db."` SET `".$record_column."` = ? WHERE `".$record_id."` != ? AND `".$unique."` = ?", $params);
		}
	}

	if(!$db->error()){
		$db->commit();
		echo $CMSBuilder->mini_alert("<p>Featured item was successfully saved!</p>", true);
	} else {
		echo $CMSBuilder->mini_alert("<p>There was an error updating this record: ".$db->error()."</p>", false);
	}
	
} else {
	echo 'error';
}
	
?>