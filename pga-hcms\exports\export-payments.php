<?php  

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../includes/utils.php");

if(isset($_GET) && USER_LOGGED_IN){

	//Define vars
	$record_db = 'payments';
	$record_id = 'payment_id';
	$record_name = 'Payment';
	$records_arr = array();

	$db_columns = array(); //for SELECT in query
	$table_columns = array(); //for listing label
	$alias_columns = array(); //for listing value
	$rearrange_columns = false;

	$params = array();
	$wheretxt = "";
	$querytxt = "";
	
	//Get GL Accounts
	$glaccounts = array();
	$gldefaults = array();
	$query = $db->query("SELECT * FROM `gl_accounts` ORDER BY `gl_number`");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$glaccounts[$row['gl_id']] = $row;
			if(!empty($row['item_no'])){
				$gldefaults[$row['item_no']] = $row;
			}
		}
	}

	//Set columns to get
	$db_columns = array(
		$record_db.'.payment_number',
		$record_db.'.payment_date',
		'.gl_account',
		'reg_registrations.registration_number',
		'invoices.invoice_number',
		'reg_registrations.first_name',
		'reg_registrations.last_name',
		'reg_registrations.email',
		'reg_registrations.phone',
		$record_db.'.bill_address1',
		$record_db.'.bill_address2',
		$record_db.'.bill_city',
		$record_db.'.bill_province',
		$record_db.'.bill_postalcode',
		$record_db.'.bill_country',
		$record_db.'.amount',
		$record_db.'.admin_fee',
		$record_db.'.payment_type',
		$record_db.'.ccname',
		$record_db.'.cctype',
		$record_db.'.ccnumber',
		$record_db.'.ccexpiry',
		$record_db.'.response_code',
		$record_db.'.txn_num',
		$record_db.'.auth_code',
		$record_db.'.cvd_code',
		$record_db.'.message',
		$record_db.'.status'
	);
	$table_columns = array(
		'Payment No.',
		'Payment Date',
		'GL No.',
		'Registration No.',
		'Invoice No.',
		'First Name',
		'Last Name',
		'Email Address',
		'Phone No.',
		'Billing Address 1',
		'Billing Address 2',
		'Billing City',
		'Billing Province',
		'Billing Postal Code',
		'Billing Country',
		'Total Amount',
		'Service Fee',
		'Payment Type',
		'Cardholder',
		'Card Type',
		'Card No.',
		'Card Expiry',
		'Response Code',
		'Transaction No.',
		'Authorization Code',
		'CVD Response',
		'Message',
		'Processed'
	);
	foreach($db_columns as $key => $column) {
		$alias_columns[$key] = substr($column, (strpos($column, '.')+1));
	}

	//Get records
	$params = array();
	$wheretxt = "";
	
	//Search term
	if(isset($_GET['search']) && $_GET['search'] != ""){
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."(`$record_db`.`payment_number` LIKE ? OR `reg_registrations`.`registration_number` LIKE ? OR CONCAT(`reg_registrations`.`first_name`, ?, `reg_registrations`.`last_name`) LIKE ? OR `invoices`.`invoice_number` LIKE ? OR CONCAT(`invoices`.`first_name`, ?, `invoices`.`last_name`) LIKE ? OR `$record_db`.`ccname` LIKE ? OR `$record_db`.`amount` LIKE ?)";
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = ' ';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = ' ';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
	}

	//Filters
	$date_range = '';
	if(isset($_GET['start_date']) && $_GET['start_date'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`payment_date` >= ?";
		$params[] = date('Y-m-d 00:00:00', strtotime($_GET['start_date']));
		$date_range .= date('M j, Y', strtotime($_GET['start_date']));
	}
	if(isset($_GET['end_date']) && $_GET['end_date'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`payment_date` <= ?";
		$params[] = date('Y-m-d 23:59:59', strtotime($_GET['end_date']));
		$date_range .= (!empty($date_range) ? ' - ' : '').date('M j, Y', strtotime($_GET['end_date']));
	}
	if(isset($_GET['status']) && $_GET['status'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`status` = ?";
		$params[] = $_GET['status'];
	}

	//Create query
	$querytxt .= "SELECT `$record_db`.*, `reg_registrations`.`registration_number`, `invoices`.`invoice_number`, IFNULL(`reg_events`.`gl_id`, `invoices`.`gl_id`) AS `gl_id`, IFNULL(`reg_registrations`.`account_id`, `invoices`.`account_id`) AS `account_id`, IFNULL(`reg_registrations`.`first_name`, `invoices`.`first_name`) AS `first_name`, IFNULL(`reg_registrations`.`last_name`, `invoices`.`last_name`) AS `last_name`, IFNULL(`reg_registrations`.`email`, `invoices`.`email`) AS `email`, IFNULL(`reg_registrations`.`phone`, `invoices`.`phone`) AS `phone` ";
	$querytxt .= "FROM $record_db ";
	$querytxt .= "LEFT JOIN `reg_registrations` ON `$record_db`.`registration_id` = `reg_registrations`.`registration_id` ";
	$querytxt .= "LEFT JOIN `reg_attendees` ON `reg_registrations`.`registration_id` = `reg_attendees`.`registration_id` ";
	$querytxt .= "LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ";
	$querytxt .= "LEFT JOIN `invoices` ON `$record_db`.`invoice_id` = `invoices`.`invoice_id`";
	$querytxt .= $wheretxt;
	$querytxt .= " GROUP BY `$record_db`.`$record_id`";
	$querytxt .= " ORDER BY `$record_db`.`payment_date`";
	$query = $db->query($querytxt, $params);
	if($query && !$db->error() && $db->num_rows() > 0){
		$result = $db->fetch_array();
		foreach($result as $row){
			
			//Determine total amount
			$row['amount'] = $row['amount']+$row['admin_fee'];
			
			//Determine gl account
			$row['gl_account'] = NULL;
			if(!empty($row['gl_id'])){
				$row['gl_account'] = $glaccounts[$row['gl_id']]['gl_number'];
			}else{
				if(!empty($row['registration_id'])){
					$item_no = substr($row['registration_number'], -1);
					$row['gl_account'] = $gldefaults[$item_no]['gl_number'];
				}else if(!empty($row['invoice_number'])){
					$item_no = substr($row['invoice_number'], -1);
					$row['gl_account'] = $gldefaults[$item_no]['gl_number'];
				}
			}
			
			//Filter by gl account
			if(isset($_GET['gl_account']) && $_GET['gl_account'] != '') {
				if($_GET['gl_account'] == $row['gl_account']){
					$records_arr[$row[$record_id]] = $row;	
				}
			}else{
				$records_arr[$row[$record_id]] = $row;
			}
		}
	}

	//Compile records
	$csv_rows = array();
	foreach($records_arr as $row) {
		$data = array();
		foreach($alias_columns as $key => $column) {
			if($column == 'payment_date') {
				$data[] = ($row[$column] != '' && $row[$column] != '0000-00-00' && $row[$column] != '0000-00-00 00:00:00' ? date('Y-m-d', strtotime($row[$column])) : "");
			} else if($column == 'admin_fee' || $column == 'amount') {
				$data[] = '$'.number_format($row[$column],2);
			} else if($column == 'status') {
				$data[] = ($row[$column] == '1' ? 'Yes' : 'No');
			} else if($column == 'ccexpiry' && !empty($row[$column])) {
				$data[] = substr($row[$column], 0, 2).'/'.substr($row[$column], -2, 2);
			} else {
				$data[] = $row[$column];
			}
		}
		$csv_rows[] = $data;
	}

	//Output CSV
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=payments-".date("Ymd").".csv");
	header("Content-Transfer-Encoding: binary ");
	
	$fp = fopen('php://output', 'w');
	
	//Data
	fputcsv($fp, str_replace("&rsquo;", "'", $table_columns));
	foreach($csv_rows as $row) {
		fputcsv($fp, str_replace("&rsquo;", "'", $row));
	}
	fputcsv($fp, array(''));
	
	//Footer
	$footer = array('Date Exported: ', date('M j, Y'));
	fputcsv($fp, $footer);
	if(!empty($date_range)){
		$footer = array('Date Filter: ', $date_range);
		fputcsv($fp, $footer);
	}
	if(isset($_GET['search']) && $_GET['search'] != ""){
		$footer = array('Search Filter: ', '`'.str_replace("&rsquo;", "'", $_GET['search']).'`');
		fputcsv($fp, $footer);
	}
	
	fclose($fp);

} 
?>