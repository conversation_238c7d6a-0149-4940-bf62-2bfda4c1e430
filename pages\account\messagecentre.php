<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Restrict to members
if(!defined('MEMBER_ACCESS') || !MEMBER_ACCESS){
	exit();
}

// URL for the main account page
$account_page_url = (isset($path) ? $path : '/') . 'account/'; // Adjust if needed
//var_dump("mc_subscribe",$Account->mc_subscribe);

//View message
if(ITEM_ID != '' && array_key_exists(ITEM_ID, $messages)){
	
	//Panel title
	$page['page_panels'][$panel_id]['title'] = '';
	$page['page_panels'][$panel_id]['show_title'] = true;
	
	$html .= '<h3>' .$message['subject']. '</h3>';
	$html .= '<h6>Sent on ' .date('F j, Y', strtotime($message['date_added'])). '</h6><br />
	<hr />';
	$html .= $message['content'];
	
	if($message['filename'] != '' && file_exists('docs/attachments/'.$message['filename'])){
		$html .= '<hr /><p><a href="' .$path.'download.php?file='.$message['filename']. '&dir=attachments" target="_blank">
			<i class="fa fa-file-pdf-o"></i> ' .$message['filename']. '
		</a></p>';
	}
	
	
//View all	
}else{
	
	$html .= $page['page_panels'][$panel_id]['content'];
	$html .= '<div class="message-center-controls-container">';
	//Search form
	$html .= '<form name="search-form" id="message-center-search-bar" action="" method="get" class="clearfix directory-search-form">
	<div class="search-input-container">
		<input type="text" name="search" class="input directory-search-input" value="' .(isset($_GET['search']) ? $_GET['search'] : ''). '" placeholder="Search" />
		<button type="submit" class="button solid button search-icon-btn"><i class="fa fa-search"></i></button>
		' .(isset($_GET['search']) && trim($_GET['search']) != '' ? '<a href="' .$page['page_url']. '" class="fa fa-times-circle"></a>' : ''). '
		</div>	
		</form>
	
	';
	
	//Subscribe
	$html .= '<p style="width: 38%;">
		<small>
			
			<input type="checkbox" name="mc_subscribe" id="mc_subscribe" class="checkbox" value="1"' .($Account->mc_subscribe == 1 ? ' checked' : ''). '/>
			<label for="mc_subscribe">Check this box to receive message centre notifications by email. You can unsubscribe at any time.</label>
			
		</small>
	</p></div>';

	$html .= '<table id="message-table" class="directory-table" cellpadding="10" cellspacing="0" border="0" width="100%" style="border-collapse: collapse; width: 100%;">';
	if(!empty($messages)){
		$html .= '<thead><tr style="border-bottom: 0px solid #fff;">
			<th style="text-align: left; padding: 8px;" class="left">Subject</th>
			<th style="text-align: left; padding: 8px;" class="left" width="150px">Date</th>
			<th style="text-align: left; padding: 8px;" align="center" width="80px">Read</th>
			<th style="text-align: left; padding: 8px;" width="120px" class="left">Action</th>
		</tr></thead>';

		foreach($messages as $message){
			
			$html .= '<tr>
				<td>' .(!$message['read'] ? '<strong class="color-dark">' .$message['subject']. '</strong>' : $message['subject']). '</td>
				<td>' .date('M j, Y', strtotime($message['date_added'])). '</td>
				<td align="center">' .($message['read'] ? 'Yes' : 'No'). '</td>
				<td align="center" ><a href="' .$page['page_url'].'?action=view&id='.$message['message_id'].'" class="button simple">View</a></td>
			</tr>';
		}

	}else{
		$html .= '<tr><td class="nobg" colspan="4">No messages found' .(isset($_GET['search']) && trim($_GET['search']) != '' ? ' matching <strong>`'.$_GET['search'].'`</strong>' : ''). '.</td></tr>';

	}


	
	//Pager
	if($totalresults > 0){
		$searchterm = (isset($_GET['search']) ? $_GET['search'] : '');
		$html .= '<tr style="border:none !important;">
			<td class="pager" colspan="4">
				<small class="pages_list">';
					$html .= '<div>Displaying '.($pg == 'all' ? '1 - '.$totalresults : (1+($limit*($pg-1))).' - '.(count($messages)+($limit*($pg-1)))).' (of '.$totalresults.' Total)<br /></div>';
					if($totalresults > $limit && $pg != 'all'){
						$tagend = round($totalresults % $limit, 0);
						$splits = round(($totalresults - $tagend)/$limit, 0);
						$num_pages = ($tagend == 0 ? $splits : $splits+1);	
						$pos = $pg;
						$startpos = ($pos*$limit)-$limit;
						$html .= '<div>';
						$html .= ($pos > 1 ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos-1).'">&lsaquo; Prev</a> ' : '');
						for($i=1; $i<=$num_pages; $i++){
							$html .= ($i != $pos ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.$i.'">'.$i.'</a> ' : '<span><u>'.$i.'</u></span> ');
						}
						$html .= ($pos < $num_pages ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos+1).'">Next &rsaquo;</a> ' : '');
						$html .= '</div>';
					}
				$html .= '</small>
			</td>
		</tr>';
	}
	
	$html .= '</table>';

	$html .= '<div class="form-row"><a href="' .htmlspecialchars($account_page_url, ENT_QUOTES, 'UTF-8'). '" class="button primary black back-button"> BACK TO MY ACCOUNT <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a></div>';

}

//Set panel content
//$page['page_panels'][$panel_id]['content'] = $html;

?>