<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){
	
	echo "<form id='search-form' name='search-form' action='' method='get' class='multiple-search f_left'>";
		echo "<select name='year' class='select f_left'>";
		for($y=$curryear; $y>=2018; $y--){
			echo "<option value='" .$y. "'" .($year == $y ? " selected" : ""). ">" .$y. "</option>";
		}
		echo "</select>";
		echo "<select name='mid' class='select f_left'>";
			echo "<option value=''>All Memberships</option>";
			foreach($membership_types as $type){
				echo "<option value='" .$type['membership_id']. "'" .($mid == $type['membership_id'] ? " selected" : ""). ">" .$type['membership_name']. "</option>";
			}
		echo "</select>";
		echo "<div class='relative f_left'>
			<input type='text' name='search' class='input f_left' value='" .$searchterm. "' placeholder='Search' />";
			if($searchterm != ''){ 
				echo "<a id='clear-search'><i class='fa fa-times-circle'></i></a>";
			}
			echo "<button type='button' class='button' onclick='this.form.submit();'><i class='fa fa-search'></i></button>
		</div>
	</form>
	<form id='clear-search-form' name='clear-search-form' class='hidden' action='" .PAGE_URL. "' method='post'>
		<input type='hidden' name='clear-search' value='Clear' />
		<input type='hidden' name='search' value='' />
		<input type='hidden' name='xssid' value='" .$_COOKIE['xssid']. "' />
	</form>";
	
	//Export Form
	echo "<script>
		function exportForm() {
			var this_form = document.getElementById(\"search-form\");
			this_form.target=\"_blank\"; 
			this_form.action=\"".$root."reports/report-pd.php\"; 
			this_form.submit(); 
			this_form.target=\"\"; 
			this_form.action=\"\";
		}
	</script>";
	
	echo "<p class='f_right'>
		<a class='button' onclick='exportForm();'><i class='fa fa-file-pdf-o'></i>View Report</a>
		<a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Award Points</a>
	</p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>" .$record_name."  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter stickyheader sortable'>";
		
			echo "<thead>";
			echo "<th width='250px'>Name</th>";
			echo "<th width='300px'>Membership</th>";
			echo "<th width='auto'>Facility</th>";
			echo "<th width='100px'>Year</th>";
			echo "<th width='100px' class='center'>Points</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .$row['first_name']. " " .$row['last_name']. "</td>";
					echo "<td>" .$row['membership_name']. "</td>";
					echo "<td>" .$row['facility_name']. "</td>";
					echo "<td>" .$row['year']. "</td>";
					echo "<td class='center'>" .$row['points_total']. "</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row['account_id']. "&year=".$year."' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo "</div>";	
	echo "</div>";

//Image cropping
} else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');
} else {
	
	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}
		
		echo "<form action='" .$root."reports/report-pd.php' method='get' target='_blank'>
			<input type='hidden' name='year' value='" .$year. "' />
			<input type='hidden' name='id' value='" .$data['profile_id']. "' />
			<p class='clearfix'><button name='button' type='button' class='button f_right' onclick='this.form.submit();'><i class='fa fa-file-pdf-o'></i>View Report</button></p>
		</form>";
		
	} else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data' class='multiselects'>";

		if(ACTION == 'add'){
	
			//Details
			echo "<div class='panel'>";
				echo "<div class='panel-header'>$year $record_name
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content clearfix'>";			
					echo "<div class='form-field auto-width'>
						<label>Category <span class='required'>*</span></label>
						<select name='category_id' class='select" .(in_array('category_id', $required) ? ' required' : ''). "' style='width:512px;'>
						<option value=''>- Select -</option>";
							if(!empty($categories)) {
								foreach($categories as $category){
									if(empty($category['parent_id'])){
										echo "<option value='".$category['category_id']."' ".(isset($_POST['category_id']) && $_POST['category_id'] == $category['category_id'] ? "selected" : "").">".$category['category_name']."</option>";
										if(!empty($category['sub_categories'])){
											foreach($category['sub_categories'] as $subcat){
												echo "<option value='".$subcat['category_id']."' ".(isset($_POST['category_id']) && $_POST['category_id'] == $subcat['category_id'] ? "selected" : "")."> &nbsp;&rsaquo; ".$subcat['category_name']."</option>";
											}
										}
									}
								}
							}
						echo "</select>
					</div>";
					echo "<div class='form-field'>
						<label>Title <span class='required'>*</span></label>
						<input type='text' name='title' class='input" .(in_array('title', $required) ? ' required' : ''). "' value='" .(isset($_POST['title']) ? $_POST['title'] : ''). "' />
					</div>";
					echo "<div class='form-field'>
						<label>Points <span class='required'>*</span></label>
						<input type='text' name='points' class='input input_sm number" .(in_array('points', $required) ? ' required' : ''). "' value='" .(isset($_POST['points']) ? $_POST['points'] : ''). "' />
					</div>";
				echo "</div>";
			echo "</div>";
			
			//Recipients		
			echo "<div class='panel'>";
				echo "<div class='panel-header'>".$record_name. " Recipients
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";

				echo "<div class='panel-content clearfix'>";						
					echo "<div class='multiselects-wrapper clearfix'>";
						echo "<div class='multiselect-field inlineblock v_middle'>";
							echo "<label>Members</label>";
							echo "<select name='account_id' class='multiselect' size='5' data-action='add' data-move='1' data-level='1' style='width:500px; height:600px;'>";
								foreach($active_members as $member) {
									echo "<option value='".$member['account_id']."'>" .$member['last_name'].", ".$member['first_name']." - ".$member['facility_name']. " (" .$member['account_id']. ")</option>";
								}
							echo "</select>";
						echo "</div>";

						echo "<div class='multiselect-arrows inlineblock v_middle'>";
							echo "<a href='#' class='multiselect-arrow button disabled' data-action='add'>&raquo;</a>";
							echo "<a href='#' class='multiselect-arrow button disabled' data-action='remove'>&laquo;</a>";
						echo "</div>";

						echo "<div class='multiselect-field inlineblock v_middle'>";
							echo "<label>Selected Members</label>";
							echo "<select name='selected_members[]' class='multiselect' multiple='multiple' data-action='remove' data-move='1' style='width:500px; height:600px;'>";
								if(isset($selected_members) && !empty($selected_members)) {
									foreach($selected_members as $selected) {
										$member_name = $active_members[$selected]['last_name'].", ".$active_members[$selected]['first_name']." - ".$active_members[$selected]['facility_name']." (" .$selected. ")";
										echo "<option value='".$selected."'>".$member_name."</option>";
									}
								}
							echo "</select>";
						echo "</div>";							
					echo "</div>";				

				echo "</div>";
			echo "</div>";
			
			
			//Sticky footer
			include("includes/widgets/formbuttons.php");
			
			
		//Edit points	
		}else{
			
			echo "<div class='panel'>";
				echo "<div class='panel-header'>$year $record_name, " .$row['first_name']." ".$row['last_name']. "
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content nopadding'>";
					echo "<table cellpadding='0' cellspacing='0' border='0' width='100%' class='tablesorter nopager'>
						<thead>
							<tr>
								<th>Category</th>
								<th>Title</th>
								<th class='center'>Points</th>
								<th class='center'>Awarded</th>
								<th class='{sorter:false}'>&nbsp;</th>
							</tr>
						</thead>
						<tbody>";
							$total_points = 0;
							foreach($row['pd_points'] as $pd){
								$total_points += $pd['points'];
								echo "<tr>
									<td>" .$pd['category_name']. "</td>
									<td>" .$pd['title']. "</td>
									<td class='center'>" .$pd['category_points']. "</td>
									<td class='center'>" .$pd['points']. "</td>
									<td class='right'>
										<input type='checkbox' name='delete[]' id='delete_".$pd['points_id']."' value='" .$pd['points_id']. "' class='checkbox' />
										<label for='delete_".$pd['points_id']."'>Delete</label>
									</td>
								</tr>";
							}
						echo "</tbody>
					</table>";
			
					echo "<div class='pager'><div class='pagedisplay right'><strong>Total Points: &nbsp; " .$total_points. "</strong></div></div>";
			
				echo "</div>";
			echo "</div>";
			
			//Sticky footer
			echo "<footer id='cms-footer' class='resize'>";
				echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
				echo "<a href='" .PAGE_URL. "?year=".$year."' class='cancel'>Cancel</a>";
			echo "</footer>";
			
		}


		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "</form>";

}

?>