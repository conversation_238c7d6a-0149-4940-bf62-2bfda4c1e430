<div id='statistics-wrapper' class='clearfix'>
    <div class='panel'>
		<div class='panel-header'>Statistics Overview
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>
		<div class='panel-content clearfix'>				
			<div class='chart-container full'>
	            <div id='chart-basic-bar' class='nopadding nomargin' style='height:300px;'></div>
	            <ul id='legend' class='legend-container'></ul>
	        </div>
		</div>
	</div>
</div>

<?php  
$stats_type = array(
	'processed' => 'Processed',
	'delivered' => 'Delivered',
	'uniqueOpens' => 'Opens (Unique)',
	'opens' => 'Opens (Total)',
	'uniqueClicks' => 'Clicks (Unique)',
	'clicks' => 'Clicks (Total)',
	'bounces' => 'Bounces',
	'unsubscribes' => 'Unsubscribes',
	'complaints' => 'Complaints'
);

$dataset = array();
foreach(array_keys($stats_type) as $stat_key) {
	$dataset[] = (isset($statistics[$stat_key]) ? $statistics[$stat_key] : 0);
}
?>

<script src='<?php echo $path; ?>js/statistics/Chart.min.js'></script>
<script src='<?php echo $path; ?>js/statistics/chart_helpers.js'></script>
<script src='<?php echo $path; ?>js/statistics/moment.min.js'></script>
<script type='text/javascript'>
	var labels = <?php echo json_encode(array_values($stats_type)); ?>;
	var data = {
		labels: labels, 
		datasets: [
			<?php  
			$scount = 0;
			echo "{
				label: 'Count',
				fillColor : 'rgba('+fillColors[$scount]+',0.5)',
				strokeColor : 'rgba('+fillColors[$scount]+',1)',
				highlightFill : 'rgba('+fillColors[$scount]+',1)',
				highlightStroke : 'rgba('+fillColors[$scount]+',1)',
				data : ".json_encode($dataset)."
			}";
			$scount++;
			?>
		]
	}

	new Chart(makeCanvas('chart-basic-bar')).Bar(data);
	generateLegend('legend', data.datasets);  
</script>