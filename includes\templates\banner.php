<?php if(!SLIDESHOW && !LANDING){ ?>
<!--page hero-->
<?php $noimage = $page['image_showhide'] || !check_file($page['banner_image'], 'images/heroes/480/'); ?>
<section id="page-hero" class="animate theme-<?php echo ($page['theme'] ?: $global['theme']).($noimage ? ' noimage' : ''); ?>">
	<div class="page-hero-wrapper">

		<?php if(!$noimage){ ?>
		<div id="page-hero-image">
			<div class="responsive-bg" style="<?php echo responsive_bg('images/heroes/', $page['banner_image']); ?>"></div>
			<div class="overlay overlay-<?php echo $page['theme'] ?: $global['theme']; ?>"></div>
		</div>
		<?php } ?>

		<header id="page-header">
			<div class="container">
				<div class="page-title-wrapper">
					<div class="page-title"><h1><?php echo fancy_text($page['page_title']); ?></h1></div>
					<?php
					echo ($page['description'] ? '<div class="page-subtitle">'.fancy_text(nl2br($page['description'])).'</div>' : '');
					?>

				<?php
				// echo ($page['button_url'] ? '<div class="page-buttons button ternary">'.create_button($page['button_url'], $page['button_target'], $page['button_text']).'</div>' : '');
				echo ($page['button_url'] ? '<div class="page-buttons"><a class="button primary light" href="'.$page['button_url'].'" target="'.$page['button_target'].'"><span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span>'.$page['button_text'].'</a></div>' : '');
				?>
				</div>
			</div>
		</header>
	</div>
</section>
<?php } ?>