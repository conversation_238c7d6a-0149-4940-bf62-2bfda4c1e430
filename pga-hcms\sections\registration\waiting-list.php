<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
echo "<form id='search-form' class='multiple-search clearfix' action='' method='get'>";
	echo "<div class='f_left relative'>";
		echo "<input type='text' name='search' class='input' value='$searchterm' placeholder='Search' />"
		.($searchterm != "" ? "<a id='clear-search'><i class='fa fa-times-circle'></i></a>" : "");
	echo "</div>";
	echo "<button type='button' class='button' onclick='this.form.submit();'><i class='fa fa-search'></i></button>
</form>
<form id='clear-search-form' name='clear-search-form' class='hidden' action='".PAGE_URL."' method='post'>
	<input type='hidden' name='clear-search' value='Clear' />
	<input type='hidden' name='search' value='' />
	<input type='hidden' name='xssid' value='".$_COOKIE['xssid']."' />
</form>";

//Add to waitlist
if(!empty($event)){
	echo "<form action='' method='post'>";
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Add to List
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding' style='overflow:visible;'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
					echo "<tr>
						<td width='80px' height='40px'>" .($event['event_type'] == 2 ? 'Tournament' : 'Event'). ":</td>
						<td>" .$event['event_name']. " on " .format_date_range($event['start_date'], $event['end_date']). "</td>
					</tr>
					<tr>
						<td>Attendee:</td>
						<td>
							<div class='form-field ui-front'>
								<input type='text' name='term' value='' class='account_suggest input nomargin' placeholder='Account Search' />
								<input type='hidden' name='account_id' value='' class='account_id' />
								<input type='hidden' name='first_name' value='' class='first_name' />
								<input type='hidden' name='last_name' value='' class='last_name' />
							</div>
							<button tyle='submit' name='subscribe' value='1' class='button-sm'><i class='fa fa-plus'></i>Add</button>
						</td>
					</tr>";
				echo "</table>";
			echo "</div>
		</div>
		<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />
	</form>";
}

//Display waitlist
echo "<form action='' method='post'>";
	echo "<div class='panel'>";
		echo "<div class='panel-header'>$record_name 
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

			echo "<thead>";	
			echo "<th width='225px'>Name</th>";
			echo "<th width='225px'>Email</th>";
			echo "<th width='250px'>".EVENT_CODE."</th>";
			echo "<th class='{sorter:\"monthDayYear\"}' width='120px'>Start Date</th>";
			echo "<th class='{sorter:\"monthDayYear\"}' width='120px'>End Date</th>";
			echo "<th class='{sorter:\"monthDayYear\"}' width='120px'>Requested</th>";
			echo "<th width='60px' class='center'>Priority</th>";
			echo "<th width='140px' class='right {sorter:false}'>Add or Remove ".$CMSBuilder->tooltip('Add or Remove', '<strong>Add</strong> - Will take you to the attendee registration page.<br /><strong>Remove</strong> - Entry will be permanently removed from the waiting list.')."</th>";
			echo "</thead>";

			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .(trim($row['wait_name']) != '' ? $row['wait_name'] : $row['first_name']. " " .$row['last_name'])."<br /><small>".($row['member_category'] != "" ? $row['member_category'] : "Unassigned"). "</small></td>";
					echo "<td>" .(trim($row['wait_email']) != '' ? $row['wait_email'] : $row['email']). "</td>";
					echo "<td>" .$row['event_name']. "<small class='clear'>Current Capacity: ".$row['current_capacity']."/".($row['max_capacity'] != '' && $row['max_capacity'] != 0 ? $row['max_capacity'] : "&infin;")."</small></td>";
					echo "<td>" .($row['start_date'] != '' && $row['start_date'] != '0000-00-00' ? date('M j, Y', strtotime($row['start_date'])) : ""). "</td>";
					echo "<td>" .($row['end_date'] != '' && $row['end_date'] != '0000-00-00' ? date('M j, Y', strtotime($row['end_date'])) : ""). "</td>";
					echo "<td>" .date('M j, Y', strtotime($row['date_requested'])). "</td>";
					echo "<td>
						<span class='hidden'>" .$row['priority']. "</span>
						<input type='text' name='priority[" .$row['wait_id']. "]' class='input number nomargin center' value='" .$row['priority']. "' style='width:60px;' />
					</td>";
					echo "<td class='approval-buttons right'>
						<a href='" .$sitemap[$_cmssections['registration-attendees']]['page_url']. "?action=add&event=" .$row['occurrence_id']. "&account_id=" .$row['account_id']. "' class='button-sm'><i class='fa fa-user-plus nomargin'></i></a>
						<a href='#' data-id='".$row[$record_id]."' class='remove-wait-list button-sm'><i class='fa fa-trash nomargin'></i></a>
					</td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo "</div>";	
	echo "</div>";

	//Sticky footer
	echo "<footer id='cms-footer' class='resize'>";		
		echo "<a href='" .PAGE_URL.(!empty($_SERVER['QUERY_STRING']) ? '?'.$_SERVER['QUERY_STRING'] : ''). "' class='cancel'>Cancel</a>";
		echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
	echo "</footer>";

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
echo "</form>";


?>