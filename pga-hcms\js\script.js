/*------ config ------*/


//Dialog initial params
var default_dialog_params = {
	modal: true,
	autoOpen: true,
	width: 300,
	maxWidth: ($(window).width()-20),
	resizable: false,
	closeOnEscape: true,
	closeText: "",
	show:{effect:"drop", direction:"up", duration:200},
	hide:{effect:"drop", direction:"up", duration:200},
	open: function(){
		$('.ui-dialog-buttonpane').
			find('button:contains("Cancel")').button({
			icons: {
				primary: 'fas fa-ban'
			}
		});
		$('.ui-dialog-buttonpane').
			find('button:contains("Confirm")').button({
			icons: {
				primary: 'fas fa-check'
			}
		});
	},
	create: function(event, ui){
		var widget = $(this).dialog("widget");
		widget.find(".ui-dialog-titlebar-close .ui-icon").addClass('fas fa-times');
	}
};

//Default datepicker params
var default_datepicker_params = {
	prevText: 'Previous',
	nextText: 'Next',
	dateFormat: 'yy-mm-dd',
	dayNamesMin: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
	numberOfMonths: 1
}


/*------ interface ------*/


//Navigation Menu
$(function(){

	let $wrapper 	= $('#cms-wrapper'),
		$menu 		= $('#cms-menu'),
		breakpoint  = getBreakpoint();

	//Toggle side menu
	$(document).on('click', '#menu-toggle', function(){
		$wrapper.addClass('animated'); //Used for adding css transitions on menu open/close
		if($wrapper.hasClass('menu-open')){
			$wrapper.removeClass('menu-open');
			setCookie('cmsmenu', 'hidden');
		}else{
			$wrapper.addClass('menu-open');
			if(breakpoint.minwidth >= 1025){ //Only set cookie to visible if desktop
				setCookie('cmsmenu', 'visible');
			}
		}
	});

	//Set to closed by default on mobile/tablet
	if(breakpoint.minwidth < 1025){
		$wrapper.removeClass('menu-open');
		setCookie('cmsmenu', 'hidden');
	}

	//Accordions
	$menu.find('.accordion').each(function(){
		var activated = false;
		if($(this).hasClass('expanded')){
			activated = 0;
		}
		$(this).accordion({
			collapsible: true,
			active: activated,
			animate: 200
		});
	});

	//Custom scrollbar
	$menu.find('nav').toArray().forEach(function(nav){
		new PerfectScrollbar(nav, {
			suppressScrollX: true,
			minScrollbarLength: 100
		});
	});

});

//Default open/close state on resize
$(window).resize(function(){
	let breakpoint = getBreakpoint();
	if(breakpoint.minwidth < 1025 && getCookie('cmsmenu') != 'hidden'){
		setCookie('cmsmenu', 'hidden');
	}else if(breakpoint.minwidth >= 1025 && getCookie('cmsmenu') != 'visible'){
		setCookie('cmsmenu', 'visible');
	}
});

//Toggle Panel
$(function(){
	$('.panel-toggle').click(function(){
		var panel_box = $(this).parents('.panel');
		var panel = $(panel_box).find('.panel-content');
		if($(this).hasClass('fa-chevron-up')){
			$(this).removeClass('fa-chevron-up').addClass('fa-chevron-down');
		}else{
			$(this).removeClass('fa-chevron-down').addClass('fa-chevron-up');
		}
		$(panel).stop().slideToggle(300, function(){
			$(panel).toggleClass("closed");
			if($(panel).find(".gllpLatlonPicker").length > 0){
				$(".gllpLatlonPicker").each(function() {
					(new GMapsLatLonPicker()).init( $(this) );
				});
			}
			var panelStatus = (panel.is(":hidden") || panel.hasClass("closed"));
			if($('.panel').length > 1){
				$.ajax({
					method: "POST",
					url: path+"js/ajax/save-user-panel.php",
					data: { panel: panel_box.index('.panel'), status: panelStatus, xssid: c_xssid }
				});
			}
		});
	});

	//Set default state per user/section
	if($('.panel').length > 1){
		$.ajax({
			method: "POST",
			url: path+"js/ajax/set-user-panels.php",
			data: {xssid: c_xssid},
			dataType: 'json'
		}).done(function(data){
			$.each(data,function(key,val){
				$('.panel').eq(val).find('.panel-toggle').removeClass('fa-chevron-up').addClass('fa-chevron-down');
				$('.panel').eq(val).find('.panel-content').stop().slideUp("fast",function(){
					$(this).addClass("closed");
				});
			});
		});
	}
});

//System Alerts
$(function(){
	$('.system-alert').each(function(){
		$(this).animate({opacity: 1}, 600);
	});
});

//Search clear
$(function(){
	$(document).on("click","#clear-search",function(){
		$('#search-form input').val('');
		document.getElementById("clear-search-form").submit();
	});
});

// Select all checkbox
$(function(){
	$('.select-all[data-group]:checkbox').each(function () {
		let $this  = $(this),
			$group = $(`[data-group="${$this.data('group')}"]:checkbox`).not(this);

		$this.change(() => $group.prop('checked', this.checked).change());

		$group.change(() => {
			let count   = $group.filter(':checked').length,
				checked = count == $group.length,
				indeterminate = !checked && count != 0;

			$this.prop({checked, indeterminate});
			$this.removeAttr('indeterminate');
		}).change();
	});
});


/*------ plugins ------*/


//Lightbox
function setLightBox(){
	var lGVideoOpts = {counter: false, speed: 800, videoMaxWidth: '1280px', youtubePlayerParams: { modestbranding: 1, showinfo: 0, controls: 0 }};
	$('.light-video').lightGallery(lGVideoOpts);
	$('.light-gallery').lightGallery({selector: 'this'});
	$('.light-iframe').lightGallery({selector: 'this', iframeMaxWidth: '90%'});
}

//Tag Editor
function initTagEditor() {
	$('.tagEditor').tagEditor('destroy').each(function () {
		let $this   = $(this),
			data    = $this.data(),
			classes = this.classList.toString(), // tag editor inherits classes
			source  = data.source || null, // jQuery selector, array, object, or URL
			values  = document.querySelector(source)?.children, // Check if source is selector string
			matchSource = data.matchSource !== undefined, // Tags must be included in source

			defaults = {
				delimiter: '|',
				forceLowercase: false,

				// Remove error classes
				onChange: (field, editor, tags) => {
					tags.length && $(editor).add(field).removeClass('required');
				},

				// Check if tag exists in source, ignore case
				beforeTagSave: (field, editor, tags, tag, val) => {
					if (matchSource && typeof source == 'object') {
						let sourceValues = Array.isArray(source) ? source : Object.values(source),
							valueIndex   = sourceValues.map(item => item.toLowerCase()).indexOf(val.toLowerCase());

						// Overwrite with existing source value, old tag, or cancel tag creation
						return sourceValues[valueIndex] || tag || false;
					}
				}
			},

			autocomplete;

		// Get array of values from DOM
		source = values ? [...values].map(el => el.innerText) : source;

		// Autocomplete options
		if (source) {
			autocomplete = {
				minLength: 0,
				source,

				// Open menu on creation
				create: e => $(e.target).autocomplete('search', ''),

				// Resize menu to fit editor
				open: function (e, ui) {
					let $autoInput = $(this),
						$menu      = $autoInput.autocomplete('widget'),
						$editor    = $autoInput.closest('ul.tag-editor');

					$menu.outerWidth($editor.outerWidth());
					$menu.position({my: 'left top', at: 'left bottom', of: $editor});
				},

				// Filter out selected options
				response: function (e, ui) {
					let selected = ui.content.filter(opt => !$this.val().split('|').includes(opt.value));
					ui.content.splice(0, ui.content.length, ...selected);
				}
			}
		}

		$this.tagEditor({...defaults, ...data, autocomplete});
		$this.next('.tag-editor').addClass(classes);
	});
};

$(function() {
	setLightBox();
	initTagEditor();
});


/*------ charts ------*/

// Map data to Chart.js format
$(function () {
	let $charts = $('.chart-container canvas');
	if ($charts.length && !!window.Chart) {

		// Global config
		Chart.defaults.maintainAspectRatio = false;
		Chart.defaults.animation.duration = 600;
		Chart.defaults.animation.easing = 'easeInOutQuart';
		Chart.defaults.font.family = "'Open Sans', sans-serif";
		Chart.defaults.font.size = 12;
		Chart.defaults.font.lineHeight = 1.2;
		Chart.defaults.elements.line.tension = 0.4;
		Chart.defaults.elements.line.fill = 'start';
		Chart.defaults.elements.line.borderWidth = 1;
		Chart.defaults.elements.point.hitRadius = 3;
		Chart.defaults.plugins.legend.position = 'bottom';
		Chart.defaults.plugins.legend.labels.boxWidth = 10;
		Chart.defaults.plugins.legend.labels.boxHeight = 10;
		Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0,30,47,0.9)'; // Match ui tooltips
		Chart.defaults.plugins.tooltip.boxPadding = 4;
		Chart.defaults.plugins.tooltip.boxWidth = 10;
		Chart.defaults.plugins.tooltip.boxHeight = 10;
		Chart.defaults.plugins.tooltip.cornerRadius = 0;
		Chart.defaults.plugins.tooltip.titleColor = '#53C5EB';
		Chart.defaults.plugins.tooltip.titleFont = {size: 14};
		Chart.defaults.plugins.tooltip.titleMarginBottom = 10;
		Chart.defaults.plugins.tooltip.padding = 10;
		Chart.defaults.plugins.tooltip.position = 'nearest';
		Chart.defaults.scale.beginAtZero = true;
		Chart.defaults.scale.grace = '5%';
		Chart.defaults.scale.ticks.stepSize = 1;

		// Make RGBA string from colour library, with optional alpha channel
		const fillColors = ['83,197,235','0,58,94','101,119,136','0,30,47','102,102,102','153,153,153','51,51,51','17,17,17','204,204,204'];
		const makeColour = (seed, alpha = 1) => `rgba(${fillColors[seed - Math.floor(seed / fillColors.length) * fillColors.length]}, ${alpha})`;

		// Fill dataset with zeroes and hide (looks like a 'falling' animation)
		const toggleDataset = (item, chart, chartData) => {
			let datasetIndex = item.datasetIndex;
			if (chart.isDatasetVisible(datasetIndex)) {
				chart.data.datasets[datasetIndex].data.fill(0);
				chart.hide(datasetIndex);
				item.hidden = true;
			} else {
				chart.data.datasets[datasetIndex].data = Object.values(Object.values(chartData)[datasetIndex]);
				chart.show(datasetIndex);
				item.hidden = false;
			}

			focusDataset(chart, datasetIndex);
		};

		// Place emphasis on a dataset while making others transparent
		const focusDataset = (chart, datasetIndex) => {
			chart.data.datasets.forEach((dataset, index) => {
				if (datasetIndex !== null && chart.isDatasetVisible(datasetIndex)) {
					if (index != datasetIndex) {
						dataset.backgroundColor = makeColour(index, 0.1);
						dataset.borderColor = makeColour(index, 0.1);

					} else {
						dataset.backgroundColor = makeColour(index, 0.6);
						dataset.borderWidth = 2;
					}

				} else {
					dataset.backgroundColor = makeColour(index, 0.5);
					dataset.borderColor = makeColour(index, 1);
					dataset.borderWidth = 1;
				}
			});

			chart.update();
		};

		// Generate charts
		$charts.each(function () {
			const chartKind = $(this).data('type') ?? 'inquiries';
			const chartData = $(this).data('source');

			// Format data
			let data = {datasets: [], labels: []};
			Object.entries(chartData).forEach(([label, dataset], i) => {
				data.datasets.push({
					backgroundColor: makeColour(i, 0.5),
					borderColor: makeColour(i),
					data: Object.values(dataset),
					label
				});

				Object.keys(dataset).forEach(label => !data.labels.includes(label) && data.labels.push(label));
			});

			// Init different chart types
			if (chartKind == 'inquiries') {
				const chart = new Chart(this, {
					type: 'line',
					data,

					options: {
						plugins: {
							legend: {
								onClick: (e, item, legend) => toggleDataset(item, legend.chart, chartData),
								onHover: (e, item, legend) => focusDataset(legend.chart, item.datasetIndex),
								onLeave: (e, item, legend) => focusDataset(legend.chart, null)
							},
						}
					},
				});
			}

			// ...custom charts here
		});
	}
});


/*------ table scripts ------*/


//Set Item Status
$(function(){
	$(document).on("click",".item-status button",function(){
		var $this = $(this);
		var $parent = $this.parent();

		if($parent.hasClass("no-ajax")){ //new page, set status ready for save
			$("input[name='"+$parent.attr("data-itemcol")+"']").val($this.val());
			$this.siblings().removeClass("active");
			$this.addClass("active");
		} else {
			if(!$this.hasClass("active")){ //don't make request if already selected
				$.ajax({
					method: "POST",
					url: path+"js/ajax/save-item-status.php",
					data: { table: $parent.attr("data-table"), table_id: $parent.attr("data-tableid"), item_id: $parent.attr("data-itemid"), item_col: $parent.attr("data-itemcol"), item_status: $this.val(), xssid: c_xssid }
				}).done(function(data){
					setMiniAlert(data);
					$this.siblings().removeClass("active");
					$this.addClass("active");
				});
			}
		}
	});
});

//Ajax showhide for table list items
$(function(){
	$(document).on("click", ".ajax-showhide", function(e){
		$this = $(this);
		$.ajax({
			method: "POST",
			url: path+"js/ajax/showhide-item.php",
			data: { table: $this.attr("data-table"), table_id: $this.attr("data-tableid"), item_id: $this.attr("data-itemid"), item_col: $this.attr("data-itemcol"), item_status: $this.is(':checked'), xssid: c_xssid }
		}).done(function(data){
			var tblcell = $this.parents('td');

			$this.parents('table').trigger('disablePager'); // disable pager temporarily
			if($this.is(':checked')){
				$(tblcell).find('.switch-sorter').text('Visible');
			}else{
				$(tblcell).find('.switch-sorter').text('Hidden');
			}
			$this.parents('table').trigger('enablePager'); // re-enable pager

			setMiniAlert(data);
		});
	});
});

//Ajax delete for table list items
$(function(){
	$(".ajax-delete").on("click",function(e){
		var that = $(this);

		if(that.attr('type') == 'button'){
			var this_dialog_params = default_dialog_params;
				this_dialog_params.title = 'Confirm';
				this_dialog_params.buttons = {
					"Confirm": function(){
						var thisdialog = $(this);
						$.ajax({
							method: "POST",
							url: path+"js/ajax/delete-item.php",
							data: { table: that.attr("data-table"), table_id: that.attr("data-tableid"), item_id: that.attr("data-itemid"), item_col: that.attr("data-itemcol"), item_status: that.attr("data-itemstatus"), xssid: c_xssid },
							dataType: 'json'
						}).done(function(data){
							if(!data.errors) {
								thisdialog.dialog("close");

								var parent_table = that.parents('tr').parent('tbody').parent('table');

								parent_table.trigger('disablePager'); // disable pager temporarily
								that.parents('tr').remove();
								parent_table.trigger('enablePager'); // re-enable pager
							}

							setMiniAlert(data.content);
						});
					},
					Cancel: function(){
						$(this).dialog("close");
					}
				};
			$('<div id="dialog-box"></div>').appendTo('body').html('Are you sure you want to delete this item?').dialog(this_dialog_params);
		}
	});
});

//Table Sorter
$(function(){
	$.tablesorter.addParser({
		id: 'monthDayYear',
		is: function(s) {
			return false;
		},
		format: function(s) {
			var date = new Date(Date.parse(s));
			return new Date(date);
		},
		type: 'numeric'
	});
	$.tablesorter.addParser({
		id: 'dataDate',
		is: function(s) {
			return false;
		},
		format: function(string, table, cell, count) {
			return new Date($(cell).data('date'));
		},
		type: 'numeric'
	});
	$('table.tablesorter').each(function(){

		//No header sorting for drag and drop ordering
		if($(this).hasClass('sortable')){
		   $(this).find('th').attr('data-sorter', 'false');
		}

		//No pager
		if($(this).hasClass('nopager')){
			$(this).tablesorter({
				emptyTo: '9999',
				widgets: ['stickyHeaders'],
				widgetOptions: {
					stickyHeaders_offset: 85 //offset for sticky page header
				}
			}).bind('sortEnd', function(e, table) {
				setLightBox();
			});

		//Pager
		}else{
			var pager = $(this).closest('.panel').find('.pager').first();
			var pagesize = $(pager).data('pagesize');
			var pagerOptions = {
				container: pager,
				size: pagesize,
				savePages : true,
				storageKey:'tablesorter-pager',
				output: 'Displaying {startRow} - {endRow} ({totalRows} Total)',
				updateArrows: true,
				fixedHeight: false,
				removeRows: true,
				pagerChange: setLightBox()
			};
			$(this).tablesorter({
				emptyTo: '9999',
				widgets: ['stickyHeaders'],
				widgetOptions: {
					stickyHeaders_offset: 85 //offset for sticky page header
				}
			}).bind('sortEnd', function(e, table) {
				setLightBox();
			}).tablesorterPager(pagerOptions);

			// If no rows are being shown but there are results, fix pager by resetting it
			if($(this).find('tbody tr').length == 0 && $(this)[0].config.pager.totalPages > 0) {
				pagerOptions.savePages = false;
				$(this).tablesorterPager(pagerOptions);
			}
		}
	});
});


/*------ jquery ui ------*/


//Tooltips
$(function(){
	initTooltips();
});
function initTooltips(){
	$('.tooltip').tooltip({
		track: true,
		content: function(){
			return $(this).prop('title');
		}
	});
}

//Tabs
$(function(){
	$(".tabs").tabs({
		show: { effect:"fade", duration:300 },
		hide: { effect:"fade", duration:300 }
	});
});


/*------ forms ------*/


//Clear Form Errors
$(function(){
	$('input, textarea').on('blur change clear-error', function(){
		if($(this).hasClass('required') && $(this).val() != ''){
			$(this).removeClass('required');
		}
	});
});

//Confirm Submit
$(function(){
	$(document).on('click', '.confirm-submit-btn:not(:disabled)', function(){
		var btn = $(this);
		var btn_text = (btn.data('confirm') != undefined && btn.data('confirm') != '' ? btn.data('confirm') : (btn.hasClass('delete') ? 'Are you sure you want to permanently delete this entry? This action is NOT undoable. You can also set this entry to hidden instead.' : 'Are you sure you want to proceed?'));
		var callback = (btn.data('confirm-callback') != undefined && btn.data('confirm-callback') != '' ? btn.data('confirm-callback') : false);
		var this_dialog_params = default_dialog_params;
			this_dialog_params.title = 'Confirm';
			this_dialog_params.buttons = {
				"Cancel": function(){
					$(this).dialog("close");
				},
				"Confirm": function(){
					if(btn.attr('type') == 'button'){
						$(btn).prop('type', 'submit');
					}
					if(callback && window[callback] && typeof(window[callback]) == 'function'){
						window[callback](btn.get(0));
						$(this).dialog("close");
					}else{
						$(btn).data('confirmed', 1).get(0).click();
					}
				}
			};

		if((btn.data('confirmed') == undefined || !btn.data('confirmed')) && (btn.attr('type') == 'button' || btn.prop('tagName') == 'A')){
			$('<div id="dialog-box"></div>').appendTo('body').html(btn_text).dialog(this_dialog_params);
			if(btn.prop('tagName') == 'A'){
				return false;
			}
		}
	});
});

//Head office location
$(function(){
	$(document).on("click",".head-office",function(){
		var selected = $(this);
		if(!selected.hasClass("active")){ //don't make request if already selected
			$.ajax({
				method: "POST",
				url: path+"js/ajax/set-head-office.php",
				data: { id: selected.val(), xssid: c_xssid }
			}).done(function(data){
				setMiniAlert(data);
				$(".head-office").removeClass("active");
				selected.addClass("active");
			});
		}
	});
});

//Prevent double form submissions
$(function(){
	$(document).on('submit', 'form', function(e){
		if($(this).attr('target') == undefined){
			if($(this).data('submitted') != undefined){
				return false;
			}
			$(this).data('submitted', true);
		}
	});
});

//Submit form when clicking recrop button
$(function () {
	$('.recrop-button').click(function () {
		this.type = 'submit';
		this.click();
	});
});


/*------ input scripts ------*/


// Disable/hide an input/block element depending on the value of a checkbox
$(function () {
	$(document).on('change', 'input.checkbox.disable', function(){
		var bool = $(this).prop('checked'),
			el   = $(this).data('disable'),
			els  = el.split(',');

		for(var i=0; i<els.length; i++){
			var this_el = $.trim(els[i]);
			el_bool = bool;

			//If starts with a "!", remove it and inverse the switch
			if(this_el.indexOf('!') >= 0){
				el_bool = !el_bool;
				this_el = this_el.substring((this_el.indexOf('!')+1), this_el.length);
			}

			//Disable inputs, hide anything else
			if($(this_el).is(':input')){
				$(this_el).prop('disabled', el_bool);
			}else if(el_bool){
				$(this_el).fadeOut(300);
			}else{
				$(this_el).fadeIn(300);
			}
		}
	});
	$('input.checkbox.disable').change();
});

//Numbers/Decimals only
$(function(){
	$(document).on('input focus blur', '.number', function () {
		this.value = this.value.replace(/[^0-9]/g, '');

	}).on('focus blur', '.decimal', function () {
		let val     = this.value.replace(/[^0-9.]/g, ''),
			decimal = this.dataset.decimal || 0;

		if (val != '' && decimal > 0) {
			val = (+val).toFixed(decimal);
		}

		this.value = val;

	}).on('input', '.decimal', function () {
		let val   = this.value.replace(/[^0-9.]/g, ''),
			arr   = val.split('.'),
			first = arr.shift(),
			join  = arr.join('');

		this.value = (arr.length > 1 ? first+'.'+join : val);
	});
});

//Character Counts
$(function(){
	$(document).on("keyup","textarea[class*='char-count-'],input[class*='char-count-']",function(){
		var maxLength = 0;
		var textLength = $(this).val().length;
		var target = $("#count-"+$(this).attr("id"));
		var classList = $(this).attr('class').split(/\s+/);
		$.each(classList, function(index, item) {
			if(item.substring(0, 11) === 'char-count-'){
				var split = item.split('-');
				maxLength = split[2];
			}
		});
		target.text(textLength);
		if(textLength > maxLength)
			target.addClass("error");
		else
			target.removeClass("error");
	});
});

//Date Picker
$(function(){
	$(".datepicker").datepicker(default_datepicker_params);

	$(".datepicker.multi").datepicker($.extend({}, default_datepicker_params, {numberOfMonths: 3}));

	$(".dobpicker").datepicker($.extend({}, default_datepicker_params, {
		changeMonth: true,
		changeYear: true,
		yearRange: "-100:+1",
		onSelect: function(){
			$(this).removeClass('required');
		},
		onChangeMonthYear:function(y, m, i){
			var d = i.selectedDay;
			$(this).datepicker('setDate', new Date(y, m-1, d));
		}
	}));
});

// Google Places Autocomplete (Requiers Places API)
$(function() {
	const init = function () {
		let $this       = $(this),
			data        = $this.data(),

			// Clears the filled inputs
			$clear      = $(data.clear),

			// Contains all component fields, searches in panel content if not explicit
			$container  = $(data.container || $this.closest('.panel-content')),

			// Set component fields explicitly with data attributes, or attempt to find named fields within $container
			$address    = $(data.address).first(),
			$city       = $(data.city || $container.find('[name*="city"]')).first(),
			$province   = $(data.province || $container.find('[name*="province"]')).first(),
			$country    = $(data.country || $container.find('[name*="country"]')).first(),
			$postalCode = $(data.postalCode || $container.find('[name*="postal_code"], [name*="postalcode"]')).first();

		const PAC = new google.maps.places.Autocomplete(this, {
			componentRestrictions: { country: ['us', 'ca'] },
			fields: ['address_components']
		});

		// Toggle display of element according to boolean
		const showhide = (el, bool) => bool ? $(el).show() : $(el).hide();

		// Change the value of detected inputs as long as they are empty
		const setVal = (field, value) => {
			$field = $(field);
			if ($field.length && $field.is(':visible') && !$field.prop('disabled')) {
				$field.val(value).trigger('change');

				if (value && $field.is('select') && !$field.val()) {
					$field.val(''); // Prevent blank-state select fields
				}
			}

			return $field;
		}

		// Update all fields
		const update = components => {
			let values = {},
				address,

				// These component types should use the short_name instead of the long_name
				shortNamedTypes = ['administrative_area_level_1'];

			// Create object using each type and component value as the key value pair
			components.forEach(component => component.types.forEach(type => {
				values[type] = shortNamedTypes.includes(type) ? component.short_name : component.long_name;
			}));

			// Combine different component types to build street address
			address = (values.street_number ?? '')+' '+(values.route ?? '')+' '+(values.street_address ?? '').trim().replace('  ', ' ');
			// console.log({$this, $address, $city, $province, $country, $postalCode});
			// console.log({...values, address});

			setVal($address, address);
			setVal($city, values.locality ?? '');
			setVal($province, values.administrative_area_level_1 ?? '');
			setVal($country, values.country ?? '');
			setVal($postalCode, values.postal_code ?? '');

			// If no address is set overwrite search field value
			if (!$address.length) {
				$this.val(address).trigger('change');
			}

			showhide($clear, true);
		};

		// Clear all fields
		const clear = () => {
			setVal($address, '');
			setVal($city, '');
			setVal($province, '');
			setVal($country, '');
			setVal($postalCode, '');
			if (!$address.length) {
				$this.val('').trigger('change');
			}

			showhide($clear, false);
		};

		// Update all assoicated fields
		google.maps.event.addListener(PAC, 'place_changed', () => {
			update(PAC.getPlace().address_components);

			if($this.hasClass('gllpSearchField')) {
				$this.closest('.gllpLatlonPicker').find('.gllpSearchButton').click();
			}
		});

		// Clear all associated fields
		$clear.click(e => clear());

		// Store API
		$this.data('PAC', PAC);

		$this.on('input.pac', () => {
			let last = $this.attr('autocomplete');
			$this.attr('autocomplete', $this.val() ? 'new-password' : '');
			last == $this.attr('autocomplete') && $this.val() && $this.blur().focus();
		});

		// Show or hide clear button
		showhide($clear, !!$this.val());
	};

	if(window?.google?.maps?.places) {
		$('.places-autocomplete').each(init);
		$(document).on('init-pac', '.places-autocomplete', init);
	}
});


/*------ multiselect ------*/


// Select Items in Multiselect on Form Submit
$(function() {
	$('.multiselects').on('submit', function() {
		$(this).find('.multiselect[data-action="remove"] option').prop('selected', true);
	});
});

// Get Items for Multi-select
function getMultiselectOptions(curr_field, id, level, filename, exclusion_field, exclusion_id) {
	$.ajax({
		url: path+"js/ajax/"+filename,
		type: "post",
		data: {'parent_id':id, 'level':level, 'exclusion_field':exclusion_field, 'exclusion_id':exclusion_id, 'xssid':c_xssid},
		dataType: 'json',
		success: function(data) {
			if(!data.errors) {
				$(curr_field).nextAll('.multiselect-field').not(':last-child').remove();
				$(curr_field).after(data.content);

			} else {
				dialogAlert('Error', data.content, 'error');
			}
		},
		error: function(data) {
			dialogAlert('Error', data.content, 'error');
		}
	});
}

// Move Multi-select Item
function moveMultiselectItem(this_arrow) {
	if($(this_arrow).data('action') == 'add') {
		var $item_parent_select = $(this_arrow).data('item').parent();
		var $item_parent_wrapper = $(this_arrow).data('item').parents('.multiselects-wrapper');
		var item_level = parseInt($item_parent_select.data('level'));
		var new_item = '';
		var item_name = '';
		var item_value = '';

		for(var i=0; i<item_level; i++) {
			if($item_parent_wrapper.find('.multiselect[data-action="add"]').eq(i).data('move') == '1') {
				item_name += $item_parent_wrapper.find('.multiselect[data-action="add"]').eq(i).find('option:selected').text();
				item_name += ((i+1) < item_level ? ' &raquo; ' : '');
				item_value = $item_parent_wrapper.find('.multiselect[data-action="add"]').eq(i).find('option:selected').val();
			}
		}

		// create new option
		new_item = '<option value="'+item_value+'">'+item_name+'</option>';
		$(new_item).appendTo($(this_arrow).data('target'));

	} else if($(this_arrow).data('action') == 'remove') {
		// remove from assigned list
		$(this_arrow).data('target').find('option:selected').remove();
		// disable arrow again
		$(this_arrow).addClass('disabled');
	}
}

// Multi-select Events
$(function() {
	// Multiselect
	$(document).on('click', '.multiselect', function() {
		var $this_select = $(this);
		var $this_field_wrapper = $this_select.parent('.multiselect-field');
		var $current_destination = $this_field_wrapper.siblings('.multiselect-field').find('.multiselect[data-action="remove"]');

		if($this_select.data('action') == 'add') {
			// get sub-categories if there's any
			if($this_select.data('filename') != undefined && $this_select.data('filename') != '' && $this_select.find('option:selected').length > 0) {
				getMultiselectOptions($this_field_wrapper, $this_select.find('option:selected').val(), $this_select.data('level'), $this_select.data('filename'), $this_select.data('exclusion-field'), $this_select.data('exclusion-id'));
			}
			// enable add arrow and
			if($this_select.data('move') && $this_select.find('option:selected').length > 0) {
				$this_field_wrapper.siblings('.multiselect-arrows').find('.multiselect-arrow[data-action="add"]')
					.removeClass('disabled')
					.data({'item':$this_select.find('option:selected'), 'target':$current_destination});
			}
			// disable remove arrow
			$this_field_wrapper.siblings('.multiselect-arrows').find('.multiselect-arrow[data-action="remove"]').addClass('disabled');

		} else if($this_select.data('action') == 'remove') {
			// enable remove arrow and set current item(s) selected
			if($this_select.data('move') && $this_select.find('option:selected').length > 0) {
				$this_field_wrapper.siblings('.multiselect-arrows').find('.multiselect-arrow[data-action="remove"]')
					.removeClass('disabled')
					.data({'target':$this_select});
			}
			// disable add arrow
			$this_field_wrapper.siblings('.multiselect-arrows').find('.multiselect-arrow[data-action="add"]').addClass('disabled');
		}
	});

	// Arrows
	$(document).on('click', '.multiselect-arrow', function() {
		if(!$(this).hasClass('disabled')) {
			moveMultiselectItem($(this));
		}

		return false;
	});
});


/*------ seo ------*/


//Page SEO Google Preview
$(function(){
	if($("#seo-information").length > 0){
		if($("#seo-slug").val() == '' || $("#seo-slug").val() == undefined){
			if($("#default-slug").val() == '' || $("#default-slug").val() == undefined){
				var current_slug = $("#button-text").val().replace(/[^a-zA-Z0-9\/_|+ -]/gi,'').replace(/[\/_|+ -]+/gi, '-').replace(/(^-|-$)/g,'').toLowerCase();
			} else {
				var current_slug = $("#default-slug").val();
			}
		} else {
			var current_slug = $("#seo-slug").val().replace(/[^a-zA-Z0-9\/_|+ -]/gi,'').replace(/[\/_|+ -]+/gi, '-').replace(/(^-|-$)/g,'').toLowerCase();
		}

		$(document).on("change","#button-text, #seo-slug, #seo-title, #seo-description",function(){
			$(".google-preview").stop().fadeOut("fast",function(){
				if($("#seo-title").val() != "" && $("#seo-title").val() != undefined){
					$(".seo-title").text($("#seo-title").val());
				} else {
					$(".seo-title").text($("#default-meta-title").val());
				}
				$(".seo-description").text($("#seo-description").val());
				if($("#seo-slug").val() != "" && $("#seo-slug").val() != undefined){
					var new_slug = $("#seo-slug").val().replace(/[^a-zA-Z0-9\/_|+ -]/gi,'').replace(/[\/_|+ -]+/gi, '-').replace(/(^-|-$)/g,'').toLowerCase();
				} else {
					if($("#default-slug").val() == '' || $("#default-slug").val() == undefined){
						var new_slug = $("#button-text").val().replace(/[^a-zA-Z0-9\/_|+ -]/gi,'').replace(/[\/_|+ -]+/gi, '-').replace(/(^-|-$)/g,'').toLowerCase();
					} else {
						var new_slug = $("#default-slug").val();
					}
				}
				$(".seo-slug span").text(new_slug);
				current_slug = new_slug;
				$(this).fadeIn("slow");
			});
		});
	}
});

//Focus Keyword
$(function() {
	if($("#seo input[name='focus_keyword']").val() == '') {
		$("#seo input[name='focus_keyword']").css("background", "#ff1a1a");
		$("#seo input[name='focus_keyword']").css("border", "1px solid #CC0000");
	}

	$("#seo input[name='focus_keyword']").on("change", function() {
		if($(this).val() != '') {
			$(this).css("background", "#fff");
			$(this).css("border", "1px solid #ccc");
			$(this).css("color", "inherit");
		} else {
			$(this).css("background", "#ff1a1a");
			$(this).css("border", "1px solid #CC0000");
		}
	});
});

$(function () {
	$('.seo-summary').each(function (i) {
		let $this = $(this);

		$this.accordion({
			collapsible: true,
			active: $this.hasClass('failed') ? 0 : null,
			animate: 200
		});
	});
})


/*------ dialogs ------*/


function dialogAlert(title, message, status){
	var this_dialog_params = default_dialog_params;
		this_dialog_params.title = title;
		this_dialog_params.classes = {
			'ui-dialog': 'dialog-'+status
		};
		this_dialog_params.open = function(event, ui){
			var el = $(this);
			$('.ui-widget-overlay').bind('click', function(){
				$(el).dialog('close');
			});
		};
	$('<div id="dialog-box"></div>').appendTo('body').html(message).dialog(this_dialog_params);
}

//Mini Ajax Alert
function createMiniAlert(messageContent, status) {
	var miniAlert = document.createElement('div');
	var title = document.createElement('div');
	var message = document.createElement('div');

	$(miniAlert).addClass('system-alert mini ' + (status ? 'success' : 'error'));
	$(title).addClass('title').html((status ? '<i class="fas fa-check"></i>Success' : '<i class="fas fa-times"></i>Error'));
	$(message).addClass('message').html(messageContent);

	miniAlert.append(title, message);

	setMiniAlert(miniAlert);
}

function setMiniAlert(message){
	var this_alert = $(message);
	this_alert.addClass("come-in-top");
	$("#system-mini-alerts").prepend(this_alert);
	this_alert.delay(4000).queue(function(){$(this).removeClass('come-in-top').addClass('bounce-out-top')});
	setTimeout(function(){
		this_alert.remove();
	}, 5000);
}

//Create Modal Boxes
$(function(){ createDialogs(); });
function createDialogs() {
	$('.hidden-modal').each(function(index, el) {
		var window_width = $(window).width();
		var modal_width = 600;
		if($(el).data('width') && $(el).data('width') != '' && $(el).data('width') > 0){
			modal_width = parseFloat($(el).data('width'));
		}

		$(el).appendTo('body')
		.dialog({
			modal: true,
			classes: {
				'ui-dialog': ($(el).data('classes') != undefined ? $(el).data('classes') : '')
			},
			title: $(el).attr('title'),
			autoOpen: $(el).data('open') != undefined && $(el).data('open') == 1,
			width: modal_width,
			maxWidth: (window_width-20),
			resizable: false,
			draggable: false,
			closeOnEscape: $(el).data('escape') == undefined || $(el).data('escape') == 1,
			closeText: '',
			dialogClass: ($(el).data('class') != undefined ? $(el).data('class') : ''),
			show: ($(el).data('no-animation') ? '' : {effect:"drop", direction:"up", duration:200}),
			hide: ($(el).data('no-animation') ? '' : {effect:"drop", direction:"up", duration:200}),
			create: function(event, ui){
				var widget = $(this).dialog("widget");
				widget.find(".ui-dialog-titlebar-close .ui-icon").addClass('fas fa-times');
			}
		});

	});

	//Custom scrollbar
	$('.ui-dialog .ui-dialog-content').toArray().forEach(function(field){
		new PerfectScrollbar(field, {
			suppressScrollX: true,
			minScrollbarLength: 100
		});
	});
}

//Open/Close dialog boxes
$(function(){
	$('a[data-open-hidden-modal], :input[data-open-hidden-modal]').on('click', function() {
		var target_modal = $(this).data('open-hidden-modal');
		$(target_modal).dialog('open');
		return false;
	});

	$('a[data-close-modal], :input[data-close-modal]').on('click', function() {
		var target_modal = ($(this).data('close-modal') != '' ? $(this).data('close-modal') : $(this).closest('.ui-dialog-content'));
		$(target_modal).dialog('close');
		return false;
	});
});

//Sitemap Reference Tooltips
$(function() {

	//Open #sitemap-reference dialog box
	$(document).on('click', '.sitemap-reference', function(e) {
		e.preventDefault();

		var form_field = $(this).closest('.form-field'); // .form-field container
		var target_input = ''; // name of the text input that's associated with the tooltip
		var target_input_el = ''; // the target input DOM object
		var target_input_index = 0; // index of the target input

		//If the tooltip is within a .form-field
		if(form_field.length > 0) {
			target_input_el = form_field.find('input[type="text"]');
			target_input = form_field.find('input[type="text"]').attr('name');

			//Find the index of the input (i.e. in case there are inputs that have the same name like tab_title[])
			$('input[name="' + target_input + '"]').each(function(index, el) {
				if($(this).is(target_input_el)) {
					target_input_index = index;
				}
			});

			//Update the #sitemap-reference data values to know which input field to update
			$('#sitemap-reference').data('input', target_input);
			$('#sitemap-reference').data('input-index', target_input_index);
		}

		$('#sitemap-reference').dialog('open');
	});

	//Assign the href of the clicked link as the value of the input
	$(document).on('click', '#sitemap-reference .sitemap-pages a:not(.view-page, .copy-to-clipboard)', function(e) {
		e.preventDefault();

		var target_input = $('#sitemap-reference').data('input');
		var target_input_index = $('#sitemap-reference').data('input-index');
		var target_input_el = $('input[name="' + target_input + '"]')[target_input_index];
		var page_url = $(this).attr('href');

		$(target_input_el).val(page_url);

		$('#sitemap-reference').dialog('close');
	});

	//Copy to clipboard
	$('.copy-to-clipboard').on('click', function(e) {
		e.preventDefault();

		var textHolder = document.createElement('textarea');
		var selected;

		//Place the link to be copied into the textarea and select it
		$(textHolder).text($(this).data('copy'));
		$(textHolder).css({
			'position': 'absolute',
			'left': '-9999px',
			'top': 0
		});
		$(textHolder).appendTo('#sitemap-reference');
		$(textHolder).select();

		//Get the selected text within the textarea and copy it to the clipboard
		selected = document.getSelection().rangeCount > 0 ? document.getSelection().getRangeAt(0) : false;
		document.execCommand('copy');
		$(textHolder).remove();

		if (selected) {
			document.getSelection().removeAllRanges();    // Unselect everything on the HTML document
			document.getSelection().addRange(selected);   // Restore the original selection
		}

		//Close the sitemap pop up and show alert that link has been copied
		$('#sitemap-reference').dialog('close');
		createMiniAlert('Link Copied!', true);
	});
});


/*------ cropper ------*/

$(function() {
	cropimages.forEach((image, i) => {
		let $container  = $('.cropper-wrapper').eq(i),
			$image      = $container.find('.cropper-image img'),
			$alert      = $container.find('.cropper-alert'),

			$controls  = $container.find('.cropper-controls'),
			$dragMove  = $controls.find('.drag-move'),
			$dragCrop  = $controls.find('.drag-crop'),
			$flipHorz  = $controls.find('.flip-horz'),
			$flipVert  = $controls.find('.flip-vert'),
			$zoomIn    = $controls.find('.zoom-in'),
			$zoomOut   = $controls.find('.zoom-out'),
			$zoomScale = $controls.find('.zoom-scale'),
			$reset     = $controls.find('.reset'),

			aspectRatio = image.width && image.height ? image.width/image.height : NaN,

			lastRatio, pixelRatio;

		const Crop = new Cropper($image[0], {
			aspectRatio,
			restore: false,
			viewMode: 1, 	 // Cropwindow does not expand outside container
			autoCropArea: 1, // Cropwindow inits full width
			zoomOnWheel: false,
			background: false, // remove checkered bg
			zoomOnTouch: false,

			// Update config fields for server side crop
			crop: ({detail}) => {
				pixelRatio = image.width ? detail.width/image.width : detail.height/image.height;

				$alert.toggleClass('active', pixelRatio < 0.9);

				$('#x'+i).val(detail.x);
				$('#y'+i).val(detail.y);
				$('#w'+i).val(detail.width);
				$('#h'+i).val(detail.height);
				$('#flip-horz'+i).val(detail.scaleX == -1 ? 1 : 0);
				$('#flip-vert'+i).val(detail.scaleY == -1 ? 1 : 0);
			},

			// Change input value if ratios haven't changed
			zoom: ({detail}) => {
				if (!lastRatio || detail.oldRatio == lastRatio) {
					$zoomScale.val(detail.oldRatio);
				}

				lastRatio = detail.oldRatio;
			},

			// Set inital zoom to current value
			ready: function (e) {
				centerCanvas();
				setScale();

				$container.addClass('cropper-init');
			}
		});

		// Center canvas in container
		const centerCanvas = () => {
			Crop.zoom(-1);
			const container = Crop.getContainerData();
			const canvas    = Crop.getCanvasData();
			Crop.moveTo(container.width/2-canvas.width/2, container.height/2-canvas.height/2);
		};

		// Set scale input to current ratio
		const setScale = () => {
			let {width, naturalWidth} = Crop.getCanvasData();
			$zoomScale.val(width / naturalWidth);
		};

		// Handle buttons
		$dragMove.click(e => {
			Crop.setDragMode('move');
			$dragMove.hide();
			$dragCrop.show();
		});

		$dragCrop.click(e => {
			Crop.setDragMode('crop');
			$dragCrop.hide();
			$dragMove.show();
		});

		$zoomScale.on('input', e => {
			const cropbox = Crop?.getCropBoxData();
			Crop?.zoomTo(e.target.value, {
				// Pivot to center of crop box
				x: cropbox.width / 2 + cropbox.left,
				y: cropbox.height / 2 + cropbox.top,
			});
		});

		$flipHorz.click(e => Crop.scaleX(Crop.imageData.scaleX * -1));
		$flipVert.click(e => Crop.scaleY(Crop.imageData.scaleY * -1));

		$reset.click(e => {
			Crop.reset();
			centerCanvas();
			setScale();
		});

		// Store class in data
		$image.data('cropper', Crop);
	});
});


/*------ session timeouts ------*/


//Refresh session through ajax
function refreshSession(){
	$.post(path+'js/ajax/refresh.php');
	showTimeoutWarning();
}

//Session timeout warning
if(typeof session_limit !== 'undefined'){ //use ini_get (set in footer)
	session_limit = (session_limit*1000); //convert to ms

	var sessionexp_warning_s = 60; //alert will display in last 60s
	var sessionexp_warning_ms = (sessionexp_warning_s*1000); //convert to ms
	var sessionexp_warning_time = (session_limit-sessionexp_warning_ms); //warning time is session length minus the 60s buffer
	var sessionexp_warning_msg = 'Your session will expire in <strong id="session-countdown">01:00</strong> seconds. Click Extend Session to continue. If your session expires, you will be logged out and will lose any unsaved changes.';
	var sessionexp_title_msg = document.title;
	var sessionexp_timeout; //timer to show session expiring modal
	var sessionexp_countdown; //timer of remaining time before session finally expires
}

//On page load run timeout warning (if limit is set)
$(function(){
	if(typeof session_limit !== 'undefined'){
		showTimeoutWarning();

		//On ajax call reset timeout warning
		$(document).ajaxComplete(function(){
			showTimeoutWarning();
		});
	}
});

//Timeout warning countdown display
function countdownSessionExpiring(duration, display){

	//Current time has not been refreshed so alert user
	if((localStorage.getItem("currentTime")) != undefined && (localStorage.getItem("currentTime") != "")){

		//Show warning popup
		if($('#session-countdown-dialog-box').length == 0){
			$('<div id="session-countdown-dialog-box"></div>').appendTo('body')
			.html(sessionexp_warning_msg)
			.dialog({
				modal: true,
				title: 'Warning',
				autoOpen: true,
				width: 320,
				maxWidth: ($(window).width()-20),
				resizable: false,
				closeOnEscape: false,
				closeText: "",
				buttons: {
					"Extend Session": {
						text: 'Extend Session',
						click: function(){
							refreshSession();
							$(this).dialog("close");
						}
					}
				},
				show:{effect:"drop", direction:"up", duration:200},
				hide:{effect:"drop", direction:"up", duration:200},
				create: function(event, ui){
					var widget = $(this).dialog("widget");
					widget.find(".ui-dialog-titlebar-close").remove(); //don't let them exit out
				}
			});
		}else{
			$('#session-countdown-dialog-box').html(sessionexp_warning_msg).dialog('open');
		}

		var sessionexp_countdown_int = duration;
		var minutes, seconds;
		sessionexp_countdown = setInterval(function(){

			//Current time has not been refreshed so display countdown
			if((localStorage.getItem("currentTime")) != undefined && (localStorage.getItem("currentTime") != "")){

				minutes = parseInt(sessionexp_countdown_int / 60, 10)
				seconds = parseInt(sessionexp_countdown_int % 60, 10);

				//Format: (ii:ss)
				minutes = minutes < 10 ? "0" + minutes : minutes;
				seconds = seconds < 10 ? "0" + seconds : seconds;

				document.title = "Logout in " + minutes + ":" + seconds + " | " + sessionexp_title_msg;
				$('#session-countdown').html(minutes + ":" + seconds);

				//Countdown over
				if(--sessionexp_countdown_int < 0){
					clearTimeout(sessionexp_countdown);

					//send to login page
					window.location = path+'login/';
				}

			}else{
				clearTimeout(sessionexp_countdown);
				refreshSession();
				$('#session-countdown-dialog-box').remove();
			}

		}, 1000);
	}
}

//Show countdown warning if session is expiring soon
function showTimeoutWarning(){

	localStorage.setItem("currentTime", ""); //reset currentTime
	document.title = sessionexp_title_msg;

	//Reset countdowns
	if(typeof sessionexp_timeout !== 'undefined'){
		clearTimeout(sessionexp_timeout);
	}
	if(typeof sessionexp_countdown !== 'undefined'){
		clearInterval(sessionexp_countdown);
	}

	//Wait X time before session expires to show warning
	sessionexp_timeout = setTimeout(function(){

		var currentTime = new Date();
		localStorage.setItem("currentTime", currentTime); //set currentTime for reference (works between tabs)

		//Run countdown time left before session expires
		countdownSessionExpiring((sessionexp_warning_s-1));

	}, sessionexp_warning_time);
}


/*------ drag and drop table rows ------*/


//Sortable items
$(function(){
	$('.sortable-container').each((i, el) => $(el).sortable($.extend({
		handle: '.sort-handler',
		placeholder: 'placeholder-item',
		tolerance: 'pointer'
	}, $(el).data())));

	$(document).on('click', 'a.sort-handler', () => false);
});

//Drag and Drop Reordering
$(document).ready(function() {
	var fixHelperModified = function(e, tr) {
		var elements = getSubPages(tr),
			tablesorter = $(tr).closest('table.sortable.tablesorter');

		if(tablesorter.length && tablesorter.data('allow-sub-level') === undefined) {
			$(tr).attr("data-system-page", 'true');
			$(tr).removeAttr("data-level");
		}

		if ($(tr).attr("data-system-page") == 'true'){//If it's a system page restrict movement to the level it's currently on
			tr.data('restrict-level', true);
			tr.data('max-level', $(tr).attr("data-level"));
			tr.data('min-level', $(tr).attr("data-level"));
		} else {
			tr.data('restrict-level', false);
		}

		// Chrome bug - cannot remove selected elements untill AFTER the item has placed
		tr.data('multidrag', elements).siblings('.selected').addClass('delete-me').hide(); //Store elements in data and then remove from dom.  (Place them back when it's dropped)
		var helper = $('<table class="helper-sortable" cellspacing=0 cellborder=0></table>').append(elements);
		helper.find('tr').css("height", tr.height());
		return helper;
	}


	$('table.sortable.saveable').each(function () {
		let $table = $(this);

		let $save = $(`<div class="tablesorter-save-order">
			<div class="flex-container">
				<div class="flex-column"><a href="${window.location.pathname+window.location.search}" class="cancel">Cancel</a></div>
				<div class="flex-column right">
					The item ordering has been modified. Please save to confirm changes.
					<button type="button" class="button save"><i class="fas fa-check"></i>Save Order</button>
				</div>
			</div>
		</div>`);

		let $saveButton = $save.find('button.save');

		$save.insertAfter($table);
		$saveButton.click(() => saveOrder($table));

		setRowPositions(this);
		applyPositionalChanges(this);
	});

	$(".sortable tbody").sortable({
		helper: fixHelperModified,
		handle: ".handle",
		placeholder: "highlight",
		sort: sortPosition,
		start: moveItem,
		stop: placeItem,
		beforeStop: createItem,
		appendTo: '#cms-content',
		tolerance:"pointer",
		cursorAt:{ top: 35 },
	}).disableSelection();

	$('table.sortable.tablesorter th').addClass('sorter-false');
});

// Set attrs before initalization and data after.  Records record index and level attrs to detect changes
function setRowPositions(table) {
	$(table).find('tbody tr').each((i, el) => {
		$(el).attr({
			'data-old-level': $(el).data('level'),
			'data-old-index': i,
		}).data({
			'old-level': $(el).data('level'),
			'old-index': i,
		});
	});
}

//Called when item is picked up
function moveItem(e,ui){
	if (!$(ui.item).closest('table.sortable.tablesorter').length) {
		$(".highlight").css("height", ui.item.data('multidrag').length * 70 + "px");
	} else {
		$(".highlight").css("height", ui.item.data('multidrag').length * ui.item.height());
	}
}

//Is an element is being dragged over?
function isDraggedOver(e, $under) {
	var t = e.pageY,
		l = e.pageX,


		uT = $under.offset().top,
		uB = $under.offset().top + $under.outerHeight(),
		uL = $under.offset().left,
		uR = $under.offset().left + $under.outerWidth();

	return (t >= uT && t <= uB && l >= uL && l <= uR);
}

//Called when item is being dragged
function sortPosition(e, ui) {
	// Click pager buttons when hovering over them
	var $table = $(this).closest('.tablesorter.sortable');

	if ($table.length && $table.data().tablesorter) {

		var $sortable = $(this),
			data = $table.data().tablesorter,
			$buttons = $table.data().tablesorter.pager.$container.find('.pagebuttons').find('.prev, .first, .last, .next');


		$buttons.each(function(index, el) {
			var $this = $(this);

			$this.addClass('hoverable');

			if(isDraggedOver(e, $this)) {

				$this.addClass('dragged-over');

				// Create interval if none exists
				if (!$this.data('interval') && !$this.hasClass('disabled')) {
					$this.data('interval', setInterval(function () { // Click button every 0.7 seconds

						page = data.pager.page + 1;
						if ($this.hasClass('prev')) {
							page = Math.max(1, page - 1);
						}
						if ($this.hasClass('next')) {
							page = Math.min(data.pager.totalPages, page + 1);
						}
						if ($this.hasClass('last')) {
							page = data.pager.totalPages;
						}
						if ($this.hasClass('first')) {
							page = 1;
						}

						$sortable = $table.find('tbody.ui-sortable');

						$table.trigger('pageSet', page);
						$sortable.sortable('refresh');
						ui.placeholder.insertAfter($sortable.find('tr').last());


					}, 700));
				}

			} else {

				// Clear interval
				if ($this.data('interval')) {
					clearInterval($this.data('interval'));
					$this.data('interval', false);
					$this.removeClass('dragged-over');

				}

			}
		});
	}

	left = ui.position.left - ui.originalPosition.left;
	ui.item.data('height', ui.item.height());
	sublevel = getSubLevelDraggedItem(ui);

	// Overwrite child/parent relationship when using tablesorter and sortable at the same time (unless needed)
	// if ($table.length && $table.data('allow-sub-level') === undefined) {
		// sublevel = 1;
	// }

	$(ui.item).attr("data-level", sublevel);
	$(".ui-sortable-helper").attr("data-level", sublevel);
	$(".highlight").attr("data-level", sublevel);
}

//Called just before item is dropped, when the placeholder is still avaliable
function createItem(e, ui){
	var $sortable = $(e.target),
		$table = $sortable.closest("table.tablesorter");

	if ($table.length) {
		ui.item.insertAfter($sortable);
		prevRow = ui.placeholder.prev(),
		nextRow = ui.placeholder.next(),
		currentPage = $table.data().tablesorter.pager.page + 1;

		// Update table
		refreshTable($table, currentPage, function () {
			ui.placeholder.insertAfter($sortable); // Give the placeholder a parent (prevent errors)

			if (prevRow.length) {
				ui.item.insertAfter(prevRow);
			} else {
				ui.item.insertBefore(nextRow);
			}

			ui.item.removeClass('selected'); // Insert and unselect the drag item

		});

		$sortable.sortable('refresh'); // update the sortable
	}
}

function refreshTable($table, page, fn){
	if ($table.data('tablesorter')) {

		page = page || $table.data().tablesorter.pager.page+1; // Default to current page

		$table.trigger('disablePager'); // Show all items

		if (fn !== undefined)
			fn.call($table); // Execute code

		$table.trigger('enablePager'); // Hide items

		$table.trigger('pageSet', page); // Set the page (or next page if added to bottom)
	}
}

//Called when item is dropped
function placeItem(e,ui){

	let $children = ui.item.data('multidrag'), // Child elements being dragged
		$table    = $(e.target).closest("table.sortable"),
		$sortable = $(e.target).closest("tbody.ui-sortable"),

		level = getSubLevelDraggedItem(ui),
		diff = parseInt(level) - parseInt($($children).first().attr("data-level"));

	// Check radio buttons
	$children.find(':radio:checked').filter(function () {
		return this.id;
	}).each(function(index, el) {
		$table.find('tr #'+this.id).prop('checked', true);
	});


	// Remove children being dragged
	if (!$table.hasClass('tablesorter')) {
		ui.item.after($children).remove();

	// Remove button functionality on drag if tablesorter is enabled
	} else {

		ui.item.height('auto');
		var $buttons = $table.data().tablesorter.pager.$container.find('.pagebuttons').find('.prev, .first, .last, .next');

		$buttons.removeClass('dragged-over hoverable').off('mouseenter mouseleave');
		$buttons.each(function(index, el) {
			clearInterval($(this).data('interval'));
		});
	}

	// Chrome bug - cannot remove elements untill AFTER the item has been placed
	$table.find('.delete-me').remove();


	$children.each(function(){
		var attr = $(this).attr('data-level');
		if (typeof attr !== typeof undefined && attr !== false) {
			var maxLevel = $(this).data('max-level') || 99,
				minLevel = $(this).data('min-level') || 0,
				newlevel = +$(this).attr("data-level") + diff;


				newlevel = Math.max(minLevel, newlevel);
				newlevel = Math.min(maxLevel, newlevel);

			$(this).attr("data-level", newlevel);
		}
	});

	reclassTable(ui, level);
	restyleTable($(".sortable"));

	if ($table.hasClass('tablesorter sortable')) {
		refreshTable($table, 0, () => saveOrder($table));
	} else if (!$table.hasClass('saveable')) {
		saveOrder($table);
	}

	initTagEditor();
	initTooltips();
}

//Save the new table order to the db
function saveOrder(tableElement){
	let $table = $(tableElement),
		$save  = $table.siblings('.tablesorter-save-order'),
		$saveButton = $save.find('.button.save');

	$saveButton.addClass('loading');
	$.ajax({
		url: path + 'js/ajax/save-table.php',
		data: {
			pages: getTableData(tableElement),
			xssid: c_xssid,
		},
		method: 'post',
		dataType: 'json',
		success:function(data){

			if ($table.hasClass('saveable')) {
				$table.find('tr').removeClass('higher lower');

				setRowPositions($table);

				$save.slideUp(300);
				createMiniAlert(`<p>Order successfully updated</p>`, true);
			}
		},
		complete:function(data){
			$saveButton.removeClass('loading');
		}
	});
}

//Gets all subpages of rows
function getSubPages(tr){
	$("tr").removeClass("selected");
	$(tr).addClass("selected");
	parentLevel = $(tr).attr("data-level");
	currentElement = $(tr).next();
	while(currentElement.attr("data-level") > parentLevel){
		$(currentElement).addClass("selected");
		currentElement = $(currentElement).next();
	}

	elements = $(tr).parent().children(".selected").clone();
	$(elements).each(function(){
		var $originals = $(tr).children();
		$(this).children().each(function(index){
			$(this).width($originals.eq(index).width());
		});
	});
	return elements;
}

//Get the level and id of each row and return as array
function getTableData(tableElement){

	var tableArray = new Array();

	if ($(tableElement).hasClass('tablesorter')) {
		var data = $('table.tablesorter.sortable').data().tablesorter;

		for (var i = 0; i < data.rowsCopy.length; i++) {
			if (data.rowsCopy[i].attr("data-id") !== undefined) {
				tableArray.push({table: data.rowsCopy[i].attr("data-table"), column: data.rowsCopy[i].attr("data-column-name"), level: data.rowsCopy[i].attr("data-level"), page_id: data.rowsCopy[i].attr("data-id"), system_page: data.rowsCopy[i].data('system-page')});
			}
		}
	} else {
		$(tableElement).find("tr").each(function(){
			if ($(this).attr("data-id") !== undefined) {
				tableArray.push({table: $(this).attr("data-table"), column: $(this).attr("data-column-name"), level: $(this).attr("data-level"), page_id: $(this).attr("data-id"), system_page: $(this).data('system-page')});
			}
		});
	}

	return tableArray;
}

function reclassTable(ui, level){
	if (!isNaN(level)) {
		$(ui.item).attr("data-level", level);
	} else {
		$(ui.item).removeAttr("data-level");
	}
}

function restyleTable(tableElement){
	applyPositionalChanges(tableElement);
	applyParentPages();
}

//Applies utility classes to ancestor and descendant rows of a structured table
//Toggles the save-order window in and out
function applyPositionalChanges(table){
	let change = false,
		$save  = $(table).siblings('.tablesorter-save-order');

	$(table).find('tbody tr').each(function (newIndex, el) {
		var $this    = $(this),
			$next    = $this.next('tr'),
			oldLvl   = $this.data('old-level'),
			oldIndex = $this.data('old-index'),
			newLvl   = $this.data('level'),
			nextLvl  = $next.data('level');

		$this.toggleClass('higher', oldLvl < newLvl || oldIndex > newIndex); // Row has moved up
		$this.toggleClass('lower', oldLvl > newLvl || oldIndex < newIndex); // Row has moved down
		$next.toggleClass('first-child', newLvl < nextLvl); // Following row is a descendant
		$this.toggleClass('last-child', newLvl > nextLvl); // Following row is an ancestor

		if (oldIndex != newIndex || oldLvl != newLvl) {
			change = true;
		}
	});

	if (change) {
		$save.slideDown(300);
	} else {
		$save.slideUp(300);
	}
}

//Go through each row and find the new parent page and label it
function applyParentPages(){
	$('[data-level]').each(function(){
		level = $(this).attr("data-level");
		if (level > 0){
			current = $(this);
			while($(current).prev().attr("data-level") >= level && $(current).prev().attr("data-level") !== undefined){ //Keep going up until parent is found (or until we reach top)
				current = $(current).prev();
			}
			parentPageName = $(current).prev().attr("data-name");
			if ($(current).prev().attr("data-name") === undefined){
				parentPageName = '';
			}
			$(this).find(".parent-page").html(parentPageName);
		}
	});
}

//Gets the sublevel of the dragged item
function getSubLevelDraggedItem(ui){
	if ($(".highlight").length == 0){		//Item is not being dragged anymore
		if ($(".selected").eq(0).prev().attr("data-level") == undefined){
			maxLevel = 1;
		}
		else {
			maxLevel = (parseInt($(".selected").prev().attr("data-level"))+ 1);
		}
	}
	else if ($(".highlight").prev().attr("data-level") == undefined){	//Placeholder is at top. So nothing before it.  -> Max Level is 1
		maxLevel = 1;
	}
	else if ($(".highlight").prev().hasClass("selected")){ //Placeholder is just after (this item being dragged) in the dom so we need to go 2 elements back (instead of 1) to find the parent to get the maxLevel
		if ($(".highlight").prev().prev().attr("data-level") == undefined){
			maxLevel = 1;
		}
		else {
			maxLevel = (parseInt($(".highlight").prev().prev().attr("data-level"))+ 1);
		}
	}
	else {
		maxLevel = (parseInt($(".highlight").prev().attr("data-level"))+ 1);
	}

	left = ui.position.left - ui.originalPosition.left;
	minLevel = Math.min(5, Math.max(1, Math.floor(left / 30) + 1));

	if (ui.item.data('restrict-level') == true){//If this is a system page, restrict the level to the one it is currently on  (When being dragged)
		maxLevel = ui.item.data('max-level');
		minLevel = ui.item.data('min-level');
	}

	if ($(".highlight").length == 0 && $(".selected").attr("data-system-page") == 'true'){//If this is a system page, restrict the level to the one it is currently on (Item is not dragged anymore)
		maxLevel = $(".selected").attr("data-level");
		minLevel = $(".selected").attr("data-level");
	}


	// Overwrite child/parent relationship when using tablesorter and sortable at the same time (unless needed)
	$table = $(".selected").closest('table.tablesorter.sortable');
	if ($table.length && $table.data('allow-sub-level') === undefined) {
		minLevel = 1;
		maxLevel = 1;
	}


	return Math.min(minLevel, maxLevel);
}

//Gets the sublevel of the list item
function getSubLevelFixedItem(element){
	return $(element).attr("data-level");
}

//Returns true if the two objects are within specified distance
function colliding(e1, e2, distance){
	if (e1.left - e2.left < distance && e1.left - e2.left > (distance * -1) && e1.top - e2.top < distance && e1.top - e2.top > (distance * -1)){
		return true;
	}
	else {
		false;
	}
}
/* END drag and drop reordering */


/*------ drag and drop file uploader ------*/


//Drag and drop upload
Dropzone.autoDiscover = false;
$(function(){
	$('.dz-dropzone').each(function() {
		let $this   = $(this),
			$target = $($this.data('target') || $this.next('.dz-gallery')),
			params  = $this.data('params') || {},
			i = 0;

		const mydropzone = new Dropzone(this, {
			url: path+'js/ajax/'+($this.data('url') || 'upload-image.php'),
			params: {...params, xssid: c_xssid}
		});

		// Append additonal data based on inputs.  Inputs can be directly selected or children of a container
		mydropzone.on('sending', function(file, xhr, FormData) {
			FormData.set('i', ++i);
			$('.dropzone-params').find(':input').addBack(':input').each(function () {
				this.name && FormData.set(this.name, this.value);
			});
		});

		mydropzone.on('success', function(result) {
			if(result.status == 'success') {
				let response = $.parseJSON(result.xhr.response);

				if(response.errors < 1) {
					if(response.content) {
						$(response.content).hide().appendTo($target).fadeIn(300);
						setLightBox();
					}

				} else {
					dialogAlert('Error', response.msg_validation, 'error');
				}
			}
		});

		mydropzone.on('complete', function(file) {
			let $el = $(file.previewElement);

			if(file.status == 'success') {
				$el.fadeOut(300, () => mydropzone.removeFile(file));
			} else {
				$el.css('cursor', 'pointer').one('click', e => {
					$(e.currentTarget).fadeOut(300, () => mydropzone.removeFile(file));
				});
			}
		});
	});
});

//Delete gallery image
function deleteGalleryImage(btn){
	var photo = $(btn).closest('.dz-uploaded-image');
	var gallery = $(btn).closest('.dz-gallery');
	var uploader = gallery.prev('.dz-dropzone');
	var ajax_params = {
		'item_id': $(btn).data('id'),
		'xssid': c_xssid
	};

	if(uploader.data('params') != undefined && uploader.data('params') != ''){
		if(uploader.data('params').record_db != undefined){
			ajax_params.record_db = uploader.data('params').record_db;
		}
		if(uploader.data('params').imagedir != undefined){
			ajax_params.imagedir = uploader.data('params').imagedir;
		}
		if(uploader.data('params').crop_type != undefined){
			ajax_params.crop_type = uploader.data('params').crop_type;
		}
	}
	if($(btn).data('record_id') != undefined && $(btn).data('record_id') != ''){
		ajax_params.record_id = $(btn).data('record_id');
	}

	$.post(path+'js/ajax/delete-image.php', ajax_params, function(result){
		if(!result.errors){
			photo.fadeOut(300, function(){
				photo.remove();
			});
		}

		setMiniAlert(result.content);
	}, 'json');
}

//Default Gallery Featured image
$(function(){
	$(document).on("click",".default-featured",function(){
		var selected = $(this);
		if(!selected.hasClass("active")){
			$.ajax({
				method: "POST",
				url: path+"js/ajax/set-default-featured.php",
				data: {table: selected.attr("data-table"), table_id: selected.attr("data-tableid"), item_id: selected.val(), unique: selected.attr("data-unique"), item_col: selected.attr("data-itemcol"), item_status: selected.is(':checked'), xssid: c_xssid }
			}).done(function(data){
				setMiniAlert(data);
				$(".default-featured").removeClass("active");
				selected.addClass("active");
			});
		}
	});
});


/*------ leadins ------*/

$(function(){

	// Disable delay related fields for topbar
	$(document).on('change', '#leadin-type-selector, #leadin-position-selector', function(){
		if($('#leadin-type-selector').val() == 'bar' && $('#leadin-position-selector').val() == 'top'){
			$('form .delay-related-field').prop('disabled', true);
		}else{
			$('form .delay-related-field').prop('disabled', false);
		}
	});

	// Disable button fields for popup form
	$(document).on('change', '#leadin-type-selector, #leadin-button-type-selector', function(){
		var type = $('#leadin-type-selector').val();
		var button_type = $('#leadin-button-type-selector').val();

		$('form .button-fields').toggle(!(type == 'popup' && button_type == 'form'));
	});

	// Disable certain options for leadin type
	$(document).on('change', '#leadin-type-selector', function(){
		var type = this.value;

		if(type == 'bar'){
			$('form select[name="position"] option[value!=""]').prop('disabled', true);
			$('form select[name="position"]').find('option[value="top"], option[value="bottom"]').prop('disabled', false);
		}else if(type == 'corner'){
			$('form select[name="position"] option[value!=""]').prop('disabled', true);
			$('form select[name="position"]').find('option[value="left"], option[value="right"]').prop('disabled', false);
		}
		$('form select[name="position"]').val('');
	});
});


/*------ templating ------*/


//Copy Template and Add New
function copyTemplate(main_template, button, add_id, placement, target_placement, limit) {
	var targetplacement = (target_placement != undefined && $(target_placement).length ? $(target_placement) : $(button));

	var newid = '';
	if($(button).data('id') != undefined) {
		newid = parseInt($(button).data('id'));
	}
	if($(button).data('lastid') != undefined) {
		newid = parseInt($(button).data('lastid')) + 1;
	}
	if($(button).data('firstid') != undefined) {
		newid = parseInt($(button).data('firstid')) - 1;
	}
	var newitem = $($(main_template).clone().attr('id', '')[0].outerHTML.replace(/%index%/g, newid));
	var newitem_classes = (newitem.attr('class') != undefined && newitem.attr('class') != '' ? '.'+newitem.attr('class').replace(' ', '.') : '');

	//check limit (//template will be included so add 1 whe checking)
	if(limit != undefined && limit !== false && $(newitem_classes).length >= (limit+1)){
		dialogAlert('Error', 'Only '+limit+' items are allowed!', 'error');
		return false;
	}

	//update index of form elements
	if(newid != '') {

		newitem.find(':input, option').each(function(el_index, form_el) {
			var el_name = $(form_el).attr('name');
			el_name && $(form_el).attr({
				name: el_name?.replace('[','['+newid), // replace first instance only
			});
		});
	}

	//append new item
	if(placement == undefined || placement == 'after') {
		newitem.insertAfter(targetplacement).fadeIn(300);
	} else {
		newitem.insertBefore(targetplacement).fadeIn(300);
	}

	if (newitem.find('.tag-editor')) {
		initTagEditor();
	}

	//update last id
	if(add_id) {
		if($(button).data('lastid') != undefined) {
			$(button).data({'lastid':newid}).attr({'data-lastid':newid});
		}
		if($(button).data('firstid') != undefined) {
			$(button).data({'firstid':newid}).attr({'data-firstid':newid});
		}
	}

	//custom
	if(main_template == '#leadin-field-template'){
		newitem.find('.copy-sub-template-btn').data({'id':newid}).attr({'data-id':newid});
	}else if(main_template == '#panel-tabs-template'){
		newitem.addClass('new-panel').find('.tinymceDynamic').addClass('tinymceEditor').removeClass('tinymceDynamic');
		tinymceInitDefault('.new-panel textarea.tinymceEditor');
	}
}

//Remove template item
function deleteTemplateItem(template, button, confirm_action, always_have1) {
	if(confirm_action) {
		var this_dialog_params = default_dialog_params;
			this_dialog_params.title = 'Confirm';
			this_dialog_params.buttons = {
				Cancel: function(){
					$(this).dialog("close");
				},
				"Confirm": function(){
					var thisdialog = $(this);

					$(button).parents(template).fadeOut(300, function() {
						$(button).parents(template).remove();
					});

					thisdialog.dialog("close");
				}
			};
		$('<div id="dialog-box"></div>').appendTo('body').html('Are you sure you want to delete this item?').dialog(this_dialog_params);

	} else {
		$(button).parents(template).fadeOut(300, function() {
			$(button).parents(template).remove();
		});
	}
}

//Copy/Remove Template Buttons
$(function(){
	//Copy Panel Tab
	$(document).on('click', '#panel-tabs .copy-template-btn', function(){
		copyTemplate('#panel-tabs-template', this, true, 'before', '.copy-btn-container');
		return false;
	});
	//Delete Panel Tab
	$(document).on('click', '#panel-tabs .delete-template-btn', function(){
		deleteTemplateItem('.panel-tabs-container', this, true, true);
		return false;
	});

	//Copy Location Email
	$(document).on('click', '#location-contacts-table .copy-template-btn', function(){
		copyTemplate('#location-contact-template', this, true, 'before', '.copy-btn-row');
		return false;
	});
	//Delete Location Email
	$(document).on('click', '#location-contacts-table .delete-template-btn', function(){
		deleteTemplateItem('.location-contact-row', this, true, true);
		return false;
	});

	//Copy LeadIn Field
	$(document).on('click', '#leadin-fields-table .copy-template-btn', function(){
		copyTemplate('#leadin-field-template', this, true, 'after', '#leadin-fields-table tbody tr:last-child', 5);
		return false;
	});
	//Delete LeadIn Field
	$(document).on('click', '#leadin-fields-table .delete-template-btn', function(){
		deleteTemplateItem('.leadin-field-row', this, true, true);
		return false;
	});

	//Copy Leadin Field Options
	$(document).on('click', '.leadin-field-options-table .copy-sub-template-btn', function(){
		copyTemplate('#leadin-field-option-template', this, false, 'before', $(this).closest('.copy-sub-btn-row'));
		return false;
	});
	//Delete LeadIn Field Option
	$(document).on('click', '.leadin-field-option-row .delete-sub-template-btn', function(){
		deleteTemplateItem('.leadin-field-option-row', this, true, true);
		return false;
	});
});


/*------ utilities ------*/


//Get min and max breakpoints for screen size
function getBreakpoint(){
	var window_width = window.innerWidth;
	window_width = (window_width == undefined ? $(window).innerWidth() : window_width);

	var new_minwidth;
	var new_maxwidth;

	if(window_width > 1366){
		new_minwidth = 1366+1;
		new_maxwidth = 1920;
	}else if(window_width > 1024){
		new_minwidth = 1024+1;
		new_maxwidth = 1366;
	}else if(window_width > 768){
		new_minwidth = 768+1;
		new_maxwidth = 1024;
	}else if(window_width > 480){
		new_minwidth = 480+1;
		new_maxwidth = 768;
	}else{
		new_minwidth = 0;
		new_maxwidth = 480;
	}

	return {'minwidth':new_minwidth, 'maxwidth':new_maxwidth};
}

// Element animations
$(function() {
	/**
	 * Animate element. Set animation type, and duration based on element data.  'bool' optionally indicates a
	 * direction.  Triggers custom events before and after animation is completed (before-animation, after-animation).
	 * Note: "triggerHandler" returns the last event handler's output.  Events triggered by triggerHandler don't
	 * propagate up the DOM.
	 */
	const animate = function(el, bool) {
		let $el      = $(el),
			visible  = $el.is(':visible'),
			duration = $el.data('duration') || 300,
			cancel, anim;

		const complete = () => $el.trigger('after-animation', bool);

		bool   = bool ?? !visible; // Toggle visibility if not specifically requested
		cancel = $el.triggerHandler('before-animation', bool) === false; // Trigger event and cancel if returned false

		// Guess the best animation type if it's left undefined (add to this if you want!)
		if ($el.data('anim') !== undefined) {
			anim = $el.data('anim');
		} else if ($el.is('.input, .select, .textarea, .form-field, .panel-field:not(.panel)')) {
			anim = 'showhide';
		} else if ($el.is('.panel') || $el.children('.tabs').length) {
			anim = 'slide';
		} else if ($el.is('label')) {
			anim = 'none';
		}

		// Animate element
		if (!cancel && visible !== bool) {
			switch(anim) {
				case 'fade':
					bool ? $el.fadeIn(duration, complete) : $el.fadeOut(duration, complete);
					break;

				case 'showhide':
					bool ? $el.show(duration, complete) : $el.hide(duration, complete);
					break;

				case 'none':
					bool ? $el.show() : $el.hide();
					complete(); // apply event immediately
					break;

				default:
					bool ? $el.slideDown(duration, complete) : $el.slideUp(duration, complete);
					break;
			}
		}
	};

	/**
	 * Triggers an animation on select change.
	 * Optionally set a animation type with data-anim on target elements.  See animate() for avaliable types.
	 * Optionally set a duration with data-anim-duration on target element, option or select.
	 * Optionally inverse animation state with data-anim-inverse on target element or option.
	 */
	$(document).on('change', 'select.animation-control', function() {
		var $this       = $(this),
			$options    = $this.find('option').sort((a, b) => a.selected - b.selected), // Selected should be last
			$show = $hide = $('');

		// Sort elements to toggle into $show and $hide.  $show should take priority
		$options.each(function(index, el) {
			var $option   = $(this),
				$selector = $($option.data('toggle') || '.'+($option.val() || $option.text()));

			// Set animation for each element
			$selector.each(function(index, el) {
				var $el      = $(this),
					inverse  = $el.data('anim-inverse') !== undefined,
					duration = $el.data('anim-duration') ?? $option.data('anim-duration') ?? $this.data('anim-duration') ?? 300;

				// Inverse animation (again) based on option data
				inverse = $option.data('anim-inverse') !== undefined ? !inverse : inverse;

				// Update data attrs on element incase they were undefined
				$el.data('anim-duration', +duration);

				if (inverse ? !$option.is(':selected') : $option.is(':selected')) {
					$show = $show.add($el);
					$hide = $hide.not($el);
				} else {
					$hide = $hide.add($el);
					$show = $show.not($el);
				}
			});
		});

		// Animate
		$hide.not($show).trigger('animate-hide');
		$show.trigger('animate-show');
	});

	// Animate a visible element in/out.
	$(document).on('animate-toggle', (e, dir) => animate(e.target, dir));
	$(document).on('animate-show', e => animate(e.target, true))
	$(document).on('animate-hide', e => animate(e.target, false));
});

//Cookie Functions
function setCookie(cname, cvalue, exdays){
	if(exdays == 0){
		var expires = "expires=0";
	}else{
		var d = new Date();
		d.setTime(d.getTime() + (exdays*24*60*60*1000));
		var expires = "expires="+d.toUTCString();
	}
	document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/";
}
function getCookie(cname){
	var name = cname + "=";
	var ca = document.cookie.split(';');
	for(var i=0; i<ca.length; i++) {
		var c = ca[i];
		while (c.charAt(0)==' ') c = c.substring(1);
		if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
	}
	return "";
}

//Icon picker
var iconpicker_icons = [];
function initIconpicker(el){
	if($(el).data('iconpicker') != undefined){
		$.iconpicker.batch(el, 'destroy');
	}

	var iconpicker_params = {
		inputSearch: true,
		component: '.iconpicker-component',
	};
	if(iconpicker_icons.length){
		iconpicker_params.icons = iconpicker_icons;
	}

	$(el).iconpicker(iconpicker_params);
}

$(function(){
	var $iconpicker_el = $('.iconpicker');
	if($iconpicker_el.length){
		$iconpicker_el.each(function(){
			initIconpicker(this);
		});

		$.ajax({
			url: root+'core/plugins/font-awesome/metadata/icons.json',
			dataType: 'json',
			success: function(result){
				for(var i in result){
					for(s=0; s<result[i].styles.length; s++){
						let classname;
						switch(result[i].styles[s]){
							case 'regular':
								classname = 'far';
							break;

							case 'brands':
								classname = 'fab';
							break;

							case 'solid':
							default:
								classname = 'fas';
							break;
						}

						iconpicker_icons.push({
							title: classname+' fa-'+i,
							searchTerms: result[i].search.terms
						});
					}
				}

				$iconpicker_el.each(function(){
					initIconpicker(this);
				});
			}
		});

		$(document).on('click', '.iconpicker-component', function(){
			$(this).parent().find('.iconpicker').focus();
		});
	}
});

// Test SMTP
$(function () {
	$('#smtp-test').click(e => {
		$(e.target).addClass('loading');

			const jqXHR = $.post(path+'js/ajax/test-smtp.php', {
				// to: $('#smtp-to').val(),
				host: $('#smtp-server').val(),
				email: $('#smtp-email').val(),
				password: $('#smtp-pass').val(),
				xssid: c_xssid
			}, html => {
				try {
					setMiniAlert(html);
					error = $(html).is('.error');
				} catch (msg) {
					createMiniAlert('Test failed due to unexpected server response.', false);
					error = true;
				}

				$('#smtp-server, #smtp-email, #smtp-pass').toggleClass('required', error);
			});

			jqXHR.always(() => $(e.target).removeClass('loading'));
	});
});

//Blog Comments (approve/disapprove/delete)
$(document).ready(function(){
	$(document).on('click','.approve-comment',function(e){
		var container = $(this).parents('tr');
		var comment_id = $(this).attr('data-id');
		var approve = $(this).attr('data-approve');
		var go = false;
		if(approve == "-1"){
			$('<div id="dialog-box"></div>').appendTo('body')
			.html('Are you sure you want to permanently delete this comment?')
			.dialog({
				modal: true,
				title: 'Confirm',
				autoOpen: true,
				width: 300,
				resizable: false,
				closeOnEscape: true,
				closeText: "x",
				buttons: {
					Cancel: function() {
						$(this).dialog("close");
						return false;
					},
					"Confirm": function() {
						$(this).dialog("close");
						$.ajax({
							url: path+'js/ajax/approve-blog-comments.php',
							data: 'comment_id='+comment_id+"&approve="+approve+"&xssid="+c_xssid,
							type: 'post',
							dataType: 'json',
							success: function(data){
								if(data.success == true){
									$(container).fadeOut(300, function(){
										$(container).remove();
									});
								}
								setMiniAlert(data.message);
							},
							error: function(data){
								alert('Error! Unable to update comment status. Please try again.');
							}
						});
					}
				 },
				show:{effect:"drop", direction:"up", duration:200},
				hide:{effect:"drop", direction:"up", duration:200},
				open: function(){
					$('.ui-dialog-buttonpane').
	                    find('button:contains("Cancel")').button({
	                    icons: {
	                        primary: 'fa fa-ban'
	                    }
	                });
	                $('.ui-dialog-buttonpane').
	                    find('button:contains("Confirm")').button({
	                    icons: {
	                        primary: 'fa fa-check'
	                    }
	                });
				}
			});
		} else {
			go = true;
		}
		if(go){
			$.ajax({
				url: path+'js/ajax/approve-blog-comments.php',
				data: 'comment_id='+comment_id+"&approve="+approve+"&xssid="+c_xssid,
				type: 'post',
				dataType: 'json',
				success: function(data){
					if(data.success == true){
						if(approve == "1"){
							$(container).fadeOut(300, function(){
								//replace buttons with new ones
								$(container).find("td.approval-buttons").html($("#disapprove-button-template").html().replace(/{id}/g, comment_id));
								$(container).prependTo("#comments-approved").fadeIn();
							});
						} else if(approve == "0"){
							$(container).fadeOut(300, function(){
								//replace buttons with new ones
								$(container).find("td.approval-buttons").html($("#approve-button-template").html().replace(/{id}/g, comment_id));
								$(container).prependTo("#comments-pending").fadeIn();
							});
						}
					}
					setMiniAlert(data.message);
				},
				error: function(data){
					alert('Error! Unable to update comment status. Please try again.');
				}
			});
		}
		e.preventDefault();
	});
});

// board role dropdown
$(function() {

    const $boardMemberSelect = $('select[name="board_member"]');
    const $boardRoleField = $('.board-role-field'); // Assuming this class uniquely identifies the div

    function toggleBoardRoleField() {
        if ($boardMemberSelect.length && $boardRoleField.length) { // Check if elements exist
            if ($boardMemberSelect.val() !== '0') {
                $boardRoleField.slideDown(200); // Or .show() or .fadeIn()
            } else {
                $boardRoleField.slideUp(200);   // Or .hide() or .fadeOut()
                // Optionally clear the board_member_role select when hidden
                // $boardRoleField.find('select[name="board_member_role"]').val('');
            }
        }
    }

    // Check if the select element exists on the page
    if ($boardMemberSelect.length) {
        // Event listener for when the "Board Member?" selection changes
        $boardMemberSelect.on('change', function() {
            toggleBoardRoleField();
        });

        // Initial check on page load to set the correct state
        toggleBoardRoleField();
    }

});



//Autocomplete - Members
$(function() {
	$('input.member_suggest').bind().autocomplete({ 
		minLength: 3,
		delay: 0,
		source: function(request, response) {
			$.ajax({
				url: path+"js/ajax/get-members.php",
				method: "post",
				data: {'searchterm':request.term, 'xssid':c_xssid},
				dataType: 'json'
			}).done(function(data){
				response(data);
			});

		},
		select: function(event, ui){
			$('input.account_id').val(ui.item.account_id);
			$('input.first_name').val(ui.item.first_name);
			$('input.last_name').val(ui.item.last_name);
			$('input.facility_name').val(ui.item.facility_name);
			if($('#profile_photo').length > 0) {
				$('#profile_photo').html(ui.item.photo);
			}
		}
	});
});

//Autocomplete - Accounts
$(function() {
	$('input.account_suggest').bind().autocomplete({ 
		minLength: 3,
		delay: 0,
		source: function(request, response) {
			$.ajax({
				url: path+"js/ajax/get-accounts.php",
				method: "post",
				data: {'searchterm':request.term, 'xssid':c_xssid},
				dataType: 'json'
			}).done(function(data){
				response(data);
			});

		},
		select: function(event, ui){
			$('input.account_id').val(ui.item.account_id);
			$('input.first_name').val(ui.item.first_name);
			$('input.last_name').val(ui.item.last_name);
			$('input.full_name').val(ui.item.first_name+' '+ui.item.last_name);
			$('input.email').val(ui.item.email);
			$('input.phone').val(ui.item.phone);
			$('input.company').val(ui.item.company);
			$('input.address1').val(ui.item.address1);
			$('input.address2').val(ui.item.address2);
			$('input.city').val(ui.item.city);
			$('select.province').val(ui.item.province);
			$('input.postal_code').val(ui.item.postal_code);
			$('select.country').val(ui.item.country);
		}
	});
});


////////////////////////////////


//Registration System------------------------------------------------------------//

//Add Notification Day
function addNotificationDay(el, input_name, when) {
	$(el).before("<input type='text' name='"+input_name+"' value='' class='input input_sm' /> days "+when+" "+event_code+"<br/>");
}

//Select Items in Multiselect on Form Submit
$(function() {
	$('#event1_form, #certifications_form, #occ_sponsors_form, form.multiselects').on('submit', function() {
		$(this).find('.multiselect[data-action="remove"] option').prop('selected', true);
		//$(this).off('submit').submit();
		//return false;
	});
});

// //Get Items for Multi-select
// function getMultiselectOptions(curr_field, id, level, filename, exclusion_field, exclusion_id) {
// 	$.ajax({
// 		url: path+"/js/ajax/"+filename,
// 		type: "post",
// 		data: {'parent_id':id, 'level':level, 'exclusion_field':exclusion_field, 'exclusion_id':exclusion_id, 'xssid':getCookie("xssid")},
// 		dataType: 'json',
// 		success: function(data) {
// 			if(!data.errors) {
// 				$(curr_field).nextAll('.multiselect-field').not(':last-child').remove();
// 				$(curr_field).after(data.content);

// 			} else {
// 				alert("ERROR: " + data.content);
// 			}
// 		},
// 		error: function(data) {
// 			alert("ERROR: " + data.content);
// 		}
// 	});
// }

// //Move Multi-select Item
// function moveMultiselectItem(this_arrow) {
// 	if($(this_arrow).data('action') == 'add') {
// 		var $item_parent_select = $(this_arrow).data('item').parent();
// 		var $item_parent_wrapper = $(this_arrow).data('item').parents('.multiselects-wrapper');
// 		var item_level = parseInt($item_parent_select.data('level'));
// 		var new_item = '';
// 		var item_name = '';
// 		var item_value = '';

// 		for(var i=0; i<item_level; i++) {
// 			if($item_parent_wrapper.find('.multiselect[data-action="add"]').eq(i).data('move') == '1') {
// 				item_name += $item_parent_wrapper.find('.multiselect[data-action="add"]').eq(i).find('option:selected').text();
// 				item_name += ((i+1) < item_level ? ' &raquo; ' : '');
// 				item_value = $item_parent_wrapper.find('.multiselect[data-action="add"]').eq(i).find('option:selected').val();
// 			}
// 		}
		
// 		// create new option
// 		new_item = '<option value="'+item_value+'">'+item_name+'</option>';
// 		$(new_item).appendTo($(this_arrow).data('target'));

// 	} else if($(this_arrow).data('action') == 'remove') {
// 		// remove from assigned list
// 		$(this_arrow).data('target').find('option:selected').remove();
// 		// disable arrow again
// 		$(this_arrow).addClass('disabled');
// 	}
// }

// //Multi-select Events
// $(function() {	
// 	// Multiselect
// 	$(document).on('click', '.multiselect', function() {
// 		var $this_select = $(this);
// 		var $this_field_wrapper = $this_select.parent('.multiselect-field');
// 		var $current_destination = $this_field_wrapper.siblings('.multiselect-field').find('.multiselect[data-action="remove"]');

// 		if($this_select.data('action') == 'add') { 
// 			// get sub-categories if there's any
// 			if($this_select.data('filename') != undefined && $this_select.data('filename') != '' && $this_select.find('option:selected').length > 0) {
// 				getMultiselectOptions($this_field_wrapper, $this_select.find('option:selected').val(), $this_select.data('level'), $this_select.data('filename'), $this_select.data('exclusion-field'), $this_select.data('exclusion-id'));
// 			}
// 			// enable add arrow and 
// 			if($this_select.data('move') && $this_select.find('option:selected').length > 0) {
// 				$this_field_wrapper.siblings('.multiselect-arrows').find('.multiselect-arrow[data-action="add"]')
// 					.removeClass('disabled')
// 					.data({'item':$this_select.find('option:selected'), 'target':$current_destination});
// 			}
// 			// disable remove arrow
// 			$this_field_wrapper.siblings('.multiselect-arrows').find('.multiselect-arrow[data-action="remove"]').addClass('disabled');

// 		} else if($this_select.data('action') == 'remove') {
// 			// enable remove arrow and set current item(s) selected
// 			if($this_select.data('move') && $this_select.find('option:selected').length > 0) {
// 				$this_field_wrapper.siblings('.multiselect-arrows').find('.multiselect-arrow[data-action="remove"]')
// 					.removeClass('disabled')
// 					.data({'target':$this_select});
// 			}
// 			// disable add arrow 
// 			$this_field_wrapper.siblings('.multiselect-arrows').find('.multiselect-arrow[data-action="add"]').addClass('disabled');
// 		}
// 	});

// 	// Arrows
// 	$(document).on('click', '.multiselect-arrow', function() {
// 		if(!$(this).hasClass('disabled')) {
// 			moveMultiselectItem($(this));
// 		} 

// 		return false;
// 	});
// });

//Tags
$(function() {
	$('.tagEditor').tagEditor({
		forceLowercase: false
	});
});

//New Tickets
function addNewTicket(){
	var new_row = $('.ticket_template')[0];
		new_row = $(new_row).clone(),
		total_rows = $('.ticket_template').length;

	new_row.find('input, select').val('');
	new_row.find('select[name^="price_types["]').attr({'name':'price_types['+total_rows+']'});
	new_row.find('input[name^="price_types["]').attr({'name':'price_types['+total_rows+']'});
	new_row.find('input[name^="prices["]').attr({'name':'prices['+total_rows+']'});
	new_row.find('input[name^="price_id["]').attr({'name':'price_id['+total_rows+']'});
	new_row.insertBefore($("#add-ticket-btn"));

	new_row.find('.tooltip').tooltip({ 
		track: true,
		content: function(){
			return $(this).prop('title');
		}
	});
};


//Addons 
function addNewAddon() {
	// new addon row
	var new_row = $('.addon-template')[0];
		new_row = $(new_row).clone(),
		total_rows = $('.addon-template').length;

	new_row.find('input, select').val('');
	new_row.find('input[name^="addon_name["]').attr({'name':'addon_name['+total_rows+']'});
	new_row.find('select[name^="addon_required["]').attr({'name':'addon_required['+total_rows+']'});
	new_row.find('input[name^="addon_id["]').attr({'name':'addon_id['+total_rows+']'});
	new_row.insertBefore($("#add-addon-btn"));

	// new option row
	var new_opt_row = $('.addon-template').eq(0).next('.addon-options')[0],
		new_opt_row = $(new_opt_row).clone();

	new_opt_row.attr({'data-count':total_rows});
	new_opt_row.find('input, select').val('');
	new_opt_row.find('.option-template').not(':first-child').remove(); // use one opt template only
	new_opt_row.find('input[name^="option_name["]').attr({'name':'option_name['+total_rows+'][0]'});
	new_opt_row.find('input[name^="option_price["]').attr({'name':'option_price['+total_rows+'][0]'});
	new_opt_row.find('input[name^="option_id["]').attr({'name':'option_id['+total_rows+'][0]'});
	new_opt_row.insertBefore($("#add-addon-btn"));

	// reset tooltip
	new_row.find('.tooltip').tooltip({ 
		track: true,
		content: function(){
			return $(this).prop('title');
		}
	});
}

//Addon Options
function addNewOption(this_button) {
	var rows_parent = $(this_button).parents('.addon-options'),
		new_row = $('.option-template')[0];
		new_row = $(new_row).clone(),
		total_rows = rows_parent.find('.option-template').length;

	new_row.find('input, select').val('');
	new_row.find('input[name^="option_name["]').attr({'name':'option_name['+rows_parent.data('count')+']['+total_rows+']'});
	new_row.find('input[name^="option_price["]').attr({'name':'option_price['+rows_parent.data('count')+']['+total_rows+']'});
	new_row.find('input[name^="option_id["]').attr({'name':'option_id['+rows_parent.data('count')+']['+total_rows+']'});
	new_row.insertBefore($(this_button));
}

//Add Attendee 
$(function() {
	$(document).on('click', '#add-attendee-btn', function(e) {
		
		var template = $('.reg-attendee').html();
		$(this).before('<div class="row reg-attendee new-attendee" style="display:none;">'+template+'</div>');
		$('.new-attendee input, .new-attendee select').each(function(){
			$(this).removeClass('required').val('');
		});
		$('.new-attendee').append('<div class="clear"><a onclick="removeAttendee(this);" class="button-sm"><i class="fa fa-close"></i>Remove</a></div>');
		$('.new-attendee').removeClass('new-attendee').slideDown(300);

		e.preventDefault();
	});
});

//Remove Attendee Rows 
function removeAttendee(el) {
	$(el).parents('.reg-attendee').slideUp(300, function(){
		$(this).remove();
	});
}

//Remove from Waiting List 
$(function() {
	$(document).on('click', '.remove-wait-list', function(){
		var removebtn = $(this);
		var container = $(this).parents('tr');

		$('<div id="dialog-box"></div>').appendTo('body')
		.html('Are you sure you want to delete this entry?')
		.dialog({
			modal: true, 
			title: 'Confirm',
			autoOpen: true,
			width: 300,
			resizable: false,
			closeOnEscape: true,
			closeText: "x",
			buttons: {
				"Confirm": function(){
					$(this).dialog("close");
					$.ajax({
						url: path+"js/ajax/remove-wait-list.php",
						method: "post",
						dataType: 'json',
						data: {id:removebtn.data('id'), 'xssid':c_xssid}
					}).done(function(data){
						if(data.success == true){
							$(container).fadeOut(300, function(){
								$(container).remove();
							});
						}
						setMiniAlert(data.message);
					}).fail(function(data){
						alert("ERROR: " + data);
					});
				},
				Cancel: function(){
					$(this).dialog("close");
				}
			 },
			show:{effect:"drop", direction:"up", duration:200},
			hide:{effect:"drop", direction:"up", duration:200},
			open: function(){
				$('.ui-dialog-buttonpane').
                    find('button:contains("Cancel")').button({
                    icons: {
                        primary: 'fa fa-ban'
                    }
                });
                $('.ui-dialog-buttonpane').
                    find('button:contains("Confirm")').button({
                    icons: {
                        primary: 'fa fa-check'
                    }
                });
			}
		});	

		return false;
	});
});

//Attendee date fields
function showDateField(val, el){
	if(val == 'Withdrawn'){
		$(el).show();	
	}else{
		$(el).hide();
	}
}

//Mass Mail Modal
$(function() {
	if($('#massmail_modal').length > 0) {
		var win_width = (window.innerWidth !== undefined ? window.innerWidth : $(window).innerWidth());

		$('#massmail_modal').dialog({
			modal: true, 
			autoOpen: true,
			width: (win_width > 800 ? 800 : (win_width-40)),
			resizable: false,
			closeOnEscape: true,
			closeText: "x"
		});
		$('#massmail_modal').dialog('close'); // close on init
	}

	$(document).on('click', '#show_massmail', function() {
		$('#massmail_modal').dialog('open');
		return false;
	});
});

//Mass Mail Recipients (checkboxes)
$(function() {
	$(document).on('change', '.massmail_recipient', function() {
		var this_form = $('#massmail_form');
		var this_checkbox = $(this);

		if(this.checked) {
			this_form.append("<input type='hidden' name='attendee_emails[]' value='"+this_checkbox.val()+"' />");
		} else {
			this_form.find('input:hidden').filter(function() { return $(this).val() == this_checkbox.val() }).remove();
		}
	});
});

//Generates a random string for the promo codes
function generateRandomString(strLength){
	var text = "";
    var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"; //abcdefghijklmnopqrstuvwxyz
    for( var i=0; i < strLength; i++ ) {
    	text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}

//Promo Codes
$(function() {
	//used in promo codes and promotions and loyalty rewards
	$(document).on('change', ':input[data-disable-input]', function() {
		var target = $($(this).data('disable-input'));
		if(this.checked) {
			target.prop({'disabled':'disabled'});
		} else {
			target.removeProp('disabled');
		}
	});

	$(document).on('click', '#generate_code_button', function() {
		var randomCode = generateRandomString(12);
		$("#code").val(randomCode);
		return false;
	});
});

//Print button 
function printPage() {
	w=window.open();
	w.document.write('<html><head><title>Print</title>');
	w.document.write('<link rel="stylesheet" href="'+path+'css/base.css" type="text/css" />');
	w.document.write('<style type="text/css">#cms-footer {display: none;}</style>');
	w.document.write('</head><body>');
	w.document.write($('.print_el').html());
	w.document.write('</body></html>');
	w.print();
}

//Hole in One
function addNewHIO(){
	var new_row = $('.hio_template')[0];
		new_row = $(new_row).clone(),
		total_rows = $('.hio_template').length;

	new_row.find('input').val('');
	new_row.find('input[name^="prize["]').attr({'name':'prize['+total_rows+']'});
	new_row.find('input[name^="premium["]').attr({'name':'premium['+total_rows+']'});
	new_row.find('input[name^="premium_100["]').attr({'name':'premium_100['+total_rows+']'});
	new_row.appendTo($("#hio-rates-tbl tbody"));

};
////////////////////////////////

$(function () {
	$('.recrop-button, .submit-button').click(function () {
		this.type = 'submit';
		this.click();
	});
});

//Copy/Remove Template Buttons
$(function(){
	//Copy Field Option
	$(document).on('click', '#field-options-table .copy-template-btn', function(){
		copyTemplate('#field-option-template', this, true, 'before', '.copy-btn-row');
		return false;
	});
	//Delete Field Option
	$(document).on('click', '#field-options-table .delete-template-btn', function(){
		deleteTemplateItem('.field-option-row', this, true, true);
		return false;
	});
});

//Field Options
function fieldOptions(){
	var type = $('#field-type').val();
	if(type == 'dropdown' || type == 'checkbox' || type == 'radio'){
		$('#field-options').slideDown(300);
		$('#max-chars').hide();
		$('#send-copy').hide();
		$('#notify-facility').hide();
	}else if(type == 'email'){
		$('#field-options').slideUp(300);	
		$('#max-chars').hide();	
		$('#send-copy').show();
		$('#notify-facility').hide();
	}else if(type == 'textarea'){
		$('#field-options').slideUp(300);	
		$('#max-chars').show();	
		$('#send-copy').hide();
		$('#notify-facility').hide();
	}else if(type == 'facilities'){
		$('#field-options').slideUp(300);	
		$('#max-chars').hide();	
		$('#send-copy').hide();
		$('#notify-facility').show();
	}else{
		$('#field-options').slideUp(300);	
		$('#max-chars').hide();
		$('#send-copy').hide();
		$('#notify-facility').hide();
	}
}

//SendGrid newsletter audience selection
function showLists(sendto) {
	if(sendto == 'Send To Subscribers') {
		$('#test_table').addClass('hidden');
		$('#lists_table').removeClass('hidden');
	} else {
		$('#test_table').removeClass('hidden');
		$('#lists_table').addClass('hidden');
	}
}

//Message centre audience selection
function showAudience(sendto) {
	if(sendto == 'test'){
		$('#test_table').removeClass('hidden');
		$('.audience_table').addClass('hidden');
	}else if(sendto == 'all'){
		$('#test_table').addClass('hidden');
		$('.audience_table').addClass('hidden');
	}else{
		$('#test_table').addClass('hidden');
		$('.audience_table').addClass('hidden');
		$('#audience-'+sendto).removeClass('hidden');
	}
}