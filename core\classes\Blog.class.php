<?php

/*-----------------------------------/
* Deals with all functionality associated with blog categories, entries, and comments
* <AUTHOR> Army
* @date		15-11-03
* @file		Blog.class.php
*/

class Blog{

	/*-----------------------------------/
	* @var blog_id
	* Unique ID that represents this blog
	*/
	public $blog_id;

	/*-----------------------------------/
	* @var db
	* Mysqli database object for this class
	*/
	private $db;

	/*-----------------------------------/
	* @var settings
	* Array of blog settings data
	*/
	private $settings;

	/*-----------------------------------/
	* @var entry_ids
	* Entry IDs returned by the most recent call to the get_entries method
	*/
	public $entry_ids;

	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @return	Blog	New Blog object
	*/
	public function __construct($blog_id=1){
		$this->blog_id = $blog_id;

		//Set database instance
		if(class_exists('Database')){
			$this->db = Database::get_instance();
		}else{
			throw new Exception('Missing class file `Database`');
		}

		//Load blog settings
		$this->settings = array();
		try{
			$this->fetch_settings();
		}catch(Exception $e){
			throw new Exception($e->getMessage());
		}
	}

	/*-----------------------------------/
	* Loads global website settings into this object
	*
	* <AUTHOR> Army
	* @throws	Exception
	*/
	private function fetch_settings(){
		$this->db->query("SELECT * FROM `blog_settings` WHERE `blog_id` = ?", array($this->blog_id));
		if(!$this->db->error() && $this->db->num_rows() > 0) {
			$this->settings = $this->db->fetch_array()[0];
		}else{
			throw new Exception('Error retrieving blog settings: '.$this->db->error());
		}
	}

	/*-----------------------------------/
	* Gets public blog settings
	*
	* <AUTHOR> Army
	* @return	Array of setting data
	*/
	public function blog_settings(){
		return $this->settings;
	}

	/*-----------------------------------/
	* Gets all blog categories
	*
	* <AUTHOR> Army
	* @return	Array of categories
	*/
	public function get_categories(){
		global $_sitepages;
		$response = array();

		$this->db->query("SELECT * FROM `blog_categories` WHERE `showhide` = 0 ORDER BY `ordering`, `name`");
		if(!$this->db->error()) {
			$result = $this->db->fetch_array();
			foreach($result as $row) {
				$row['path'] = $row['page'].'-'.$row['category_id'];
				$row['page_url'] = $_sitepages['blog']['page_url'].$row['path'].'/';
				$row['total_entries'] = $this->get_entry_count('category', $row['category_id']);
				if($this->settings['empty_categories'] || $row['total_entries'] > 0){
					$response[$row['category_id']] = $row;
				}
			}
		}
		return $response;
	}

	/*-----------------------------------/
	* Gets all blog authors
	*
	* <AUTHOR> Army
	* @return	Array of blog authors
	*/
	public function get_authors(){
		global $_sitepages;
		$response = array();

		$this->db->query("SELECT * FROM `blog_authors` WHERE `showhide` = 0 ORDER BY `ordering`, `name`");
		if(!$this->db->error()) {
			$result = $this->db->fetch_array();
			foreach($result as $row) {
				$row['path'] = $row['page'].'-'.$row['author_id'];
				$row['page_url'] = $_sitepages['blog']['page_url'].$row['path'].'/';
				$row['total_entries'] = $this->get_entry_count('author', $row['author_id']);
				if($this->settings['empty_categories'] || $row['total_entries'] > 0){
					$response[$row['author_id']] = $row;
				}
			}
		}
		return $response;
	}

	/*-----------------------------------/
	* Gets a list of the last twelve months
	*
	* <AUTHOR> Army
	* @return	Array of archive dates
	*/
	public function get_archives(){
		global $_sitepages;

		$response = [];

		for($b=date("n"); $b>0; $b--){
			$datetime = date("Y-m-d", mktime(0, 0, 0, date($b), 1, date("Y")));
			$archive  = date("mY", strtotime($datetime));
			$total    = $this->get_archive_count($datetime);

			if ($this->settings['empty_categories'] || $total > 0) {
				$response[$archive] = [
					'name'          => date("F Y", strtotime($datetime)),
					'path'          => $archive,
					'page_url'      => $_sitepages['blog']['page_url'].$archive.'/',
					'datetime'      => $datetime,
					'total_entries' => $total
				];
			}
		}

		for($b=12; $b>date("n"); $b--){
			$datetime = date("Y-m-d", mktime(0, 0, 0, date($b), 1, date("Y")-1));
			$archive  = date("mY", strtotime($datetime));
			$total    = $this->get_archive_count($datetime);

			if ($this->settings['empty_categories'] || $total > 0) {
				$response[$archive] = [
					'name'          => date("F Y", strtotime($datetime)),
					'path'          => $archive,
					'page_url'      => $_sitepages['blog']['page_url'].$archive.'/',
					'datetime'      => $datetime,
					'total_entries' => $total
				];
			}
		}

		return $response;
	}


	/*-----------------------------------/
	 * Return an array of blog entries ordered by most recent
	 *
	 * <AUTHOR> Army
	 * @param	string 		$type		Keyword to filter by (author, category, entry, or archive month)
	 * @param	string|int 	$filter		Value to filter by
	 * @param	string|int 	$limit		SQL limit and/or offset string
	 * @return	array		Array of blog entry data
	 */
	public function get_entries($type = false, $filter = false, $limit = false){
		$response = [];
		$type     = $type ?: 'recent';
		$where    = '';
		$params   = [date('Y-m-d')];
		$limit    = $limit ? " LIMIT $limit" : "";

		// Loop through filters
		switch ($type) {

			// By author
			case 'author':
				$where .= " AND blog_entries.author_id = ?";
				$params[] = $filter;
				break;

			// By category
			case 'category':
				$where .= " AND blog_entries.category_id = ?";
				$params[] = $filter;
				break;

			// By month
			case 'archive':
				$where .= " AND (blog_entries.post_date >= ? AND blog_entries.post_date <= ?)";
				$params[] = date('Y-m-01', strtotime($filter));
				$params[] = date('Y-m-t', strtotime($filter));
				break;

			// By searchterm (example)
			case 'search':
				$where .= " AND (LOWER(blog_entries.title) LIKE ? || LOWER(blog_entries.description) LIKE ?)";
				$params[] = '%'.strtolower($filter).'%';
				$params[] = '%'.strtolower($filter).'%';
				break;

			// Single blog post
			case 'entry':
				$where .= " AND blog_entries.entry_id = ?";
				$params[] = $filter;
				break;
		}




		// Save IDs of response to an array
		if ($type != 'entry') {
			$this->db->query("SELECT
				blog_entries.entry_id
			FROM blog_entries
			LEFT JOIN blog_categories ON blog_entries.category_id = blog_categories.category_id
			LEFT JOIN blog_authors ON blog_entries.author_id = blog_authors.author_id
			WHERE
				blog_authors.showhide = 0
				AND blog_entries.showhide = 0
				AND blog_categories.showhide = 0
				AND DATE(blog_entries.post_date) <= ?
				$where
			GROUP BY blog_entries.entry_id
			ORDER BY blog_entries.post_date DESC, blog_entries.entry_id DESC ".($type == 'recent' ? $limit : ''), $params);
			$this->entry_ids = array_column($this->db->fetch_array(), 'entry_id');
		}

		// Query blog data
		$this->db->query("SELECT
			blog_entries.*,
			blog_categories.name AS category_name,
			blog_categories.page AS category_page,
			blog_authors.name AS author_name,
			blog_authors.page AS author_page,
			blog_authors.title AS author_title,
			blog_authors.email AS author_email,
			blog_authors.phone AS author_phone,
			blog_authors.image AS author_image
		FROM blog_entries
		LEFT JOIN blog_categories ON blog_entries.category_id = blog_categories.category_id
		LEFT JOIN blog_authors ON blog_entries.author_id = blog_authors.author_id
		WHERE
			blog_authors.showhide = 0
			AND blog_entries.showhide = 0
			AND blog_categories.showhide = 0
			AND DATE(blog_entries.post_date) <= ?
			$where
		GROUP BY blog_entries.entry_id
		ORDER BY blog_entries.post_date DESC, blog_entries.entry_id DESC
		$limit", $params);
		$response = $this->db->fetch_assoc('entry_id');

		foreach($response as $entry_id => &$row) {
			$row['archive']        = date("mY", strtotime($row['post_date']));
			$row['description']    = $row['description'] ?: truncate($row['content'], 50);
			$row['comments']       = $this->get_comments($row['entry_id']);
			$row['total_comments'] = count($row['comments']);
			unset($row);
		}

		return $response;
	}


	/*-----------------------------------/
	 * Return previous and next blog entries based on a given ID and the results of the last execution of get_entries
	 *
	 * <AUTHOR> Army
	 * @param	int 	$current_id		Blog entry ID to compare against
	 * @return	array	Array of blog entry data
	 */
	public function get_prevnext($current_id){
		$response = [];

		// Create references to previous and next entries according to the last execution of get_entries
		foreach ($this->entry_ids as $index => $entry_id) {
			if ($current_id == $entry_id) {
				$prev = $this->entry_ids[$index-1] ?? false;
				$next = $this->entry_ids[$index+1] ?? false;
				$response['prev'] = $prev ? $this->get_entries('entry', $prev)[$prev] : false;
				$response['next'] = $next ? $this->get_entries('entry', $next)[$next] : false;
			}
		}

		return $response;
	}

	/*-----------------------------------/
	* Gets a list of all popular blog entries
	*
	* <AUTHOR> Army
	* @param	sortby	The ordering condition to add
	* @param	limit	Max amount of entries to return
	* @return	Array	An array of all the popular entries
	*/
	public function get_popular_entries($type='', $category='', $sortby='DESC', $limit=10){
		$all_entries = array();

		$datelimit = date("Y-m-d", mktime(0, 0, 0, date("m", strtotime($category))+1, 1, date("Y", strtotime($category))));

		if($type == 'author') {
			$params = array($category);
		} else if($type == '' || $type == 'recent'){
			$params = array();
		} else {
			$params = array($category, $category, $datelimit);
		}

		$query = $this->db->query("SELECT
			`blog_entries`.*,
			`blog_categories`.`name` AS `category_name`,
			`blog_categories`.`page` AS `category_page`,
			`blog_authors`.`name` AS `author_name`,
			`blog_authors`.`title` AS `author_title`,
			`blog_authors`.`email` AS `author_email`,
			`blog_authors`.`phone` AS `author_phone`,
			`blog_authors`.`image` AS `author_image`,
			`blog_authors`.`showhide` AS `author_showhide`,
			`blog_authors`.`page` AS `author_page`,
			SUM(
				`blog_entries`.`view_count` +
				(SELECT COUNT(`entry_id`) FROM `blog_comments` WHERE `blog_comments`.`entry_id` =  `blog_entries`.`entry_id`) +
				(SELECT COUNT(`entry_id`) FROM `blog_entries_shares` WHERE `blog_entries_shares`.`entry_id` =  `blog_entries`.`entry_id`)
			) AS score
		FROM `blog_entries`
		LEFT JOIN `blog_categories` ON `blog_entries`.`category_id` = `blog_categories`.`category_id`
		LEFT JOIN `blog_authors` ON `blog_entries`.`author_id` = `blog_authors`.`author_id`
		WHERE
			`blog_entries`.`post_date` <= CURDATE()
			AND `blog_authors`.`showhide` = 0
			AND `blog_entries`.`showhide` = 0
			AND `blog_categories`.`showhide` = 0".
			($type == "" || $type == "recent" ? "" : ($type == 'author' ? " AND `blog_entries`.`author_id` = ? " : " AND (`blog_entries`.`category_id` = ? || `blog_entries`.`post_date` >= ? AND `blog_entries`.`post_date` < ?)")). "
		GROUP BY `blog_entries`.`entry_id`
		ORDER BY `score` $sortby LIMIT $limit", $params);
		if($query && !$this->db->error()) {
			$result = $this->db->fetch_array();

			foreach($result as $row) {

				if(trim($row['description']) == ''){
                    $row['description'] = string_limit_words(strip_tags($row['content']), 50). '...';
                }
				$row['total_comments'] = count($this->get_comments($row['entry_id']));

				if($row['score'] > 0) {
					$all_entries[$row['entry_id']] = $row;
				}
			}
		}

		return $all_entries;
	}

	/*-----------------------------------/
	* Gets the total number of entries for a given category
	*
	* <AUTHOR> Army
	* @param	$type			Search by category_id or author_id
	* @param	$category_id	The id of the category/author
	* @return	Int				Total number of entries
	*/
	public function get_entry_count($type='', $category_id=NULL){
		$query = $this->db->query("SELECT COUNT(`entry_id`) AS `total` FROM `blog_entries` LEFT JOIN `blog_authors` ON `blog_authors`.`author_id` = `blog_entries`.`author_id` LEFT JOIN `blog_categories` ON `blog_categories`.`category_id` = `blog_entries`.`category_id` WHERE `blog_entries`.`post_date` <= CURDATE() AND `blog_authors`.`showhide` = 0 AND `blog_categories`.`showhide` = 0 AND `blog_entries`.`showhide` = 0 AND " . ($type == 'author' ? "`blog_authors`.`author_id` = $category_id" : "`blog_entries`.`category_id` = $category_id"));
		if($query && !$this->db->error()) {
			$result = $this->db->fetch_array();
		}
		return $result[0]['total'];
	}

	/*-----------------------------------/
	* Gets the total number of entries for a given month
	*
	* <AUTHOR> Army
	* @param	$datetime		Datestamp of archive month (format Y-m-d)
	* @return	Int				Total number of entries
	*/
	public function get_archive_count($datetime){
		$datelimit = date("Y-m-d", mktime(0, 0, 0, date("m", strtotime($datetime))+1, 1, date("Y", strtotime($datetime))));
		$params = array($datetime, $datelimit);
		$query = $this->db->query("SELECT COUNT(`entry_id`) AS `total` FROM `blog_entries` LEFT JOIN `blog_authors` ON `blog_authors`.`author_id` = `blog_entries`.`author_id` LEFT JOIN `blog_categories` ON `blog_categories`.`category_id` = `blog_entries`.`category_id` WHERE `blog_entries`.`post_date` <= CURDATE() AND `blog_authors`.`showhide` = 0 AND `blog_categories`.`showhide` = 0 AND `blog_entries`.`showhide` = 0 AND `post_date` >= ? AND `post_date` < ?", $params);
		if($query && !$this->db->error()) {
			$result = $this->db->fetch_array();
		}
		return $result[0]['total'];
	}

	/*-----------------------------------/
	* Gets all blog comments for given entry
	*
	* <AUTHOR> Army
	* @param	$entry_id		Blog entry id
	* @param	$limit 			Limit number of comments to get
	* @param	$offset 		Offset fetched comments
	* @return	Array of comments
	*/
	public function get_comments($entry_id, $limit='', $offset='0'){
		$response = array();
		$query = $this->db->query("SELECT * FROM `blog_comments` WHERE `approved` = true AND `entry_id` = $entry_id ORDER BY `date_added` DESC " . ($limit != '' ? "LIMIT $limit OFFSET $offset" : ""));
		if($query && !$this->db->error()) {
			$result = $this->db->fetch_array();
			foreach($result as $row) {
				$row['date_added'] = $this->ago($row['date_added'], $format='d F, Y');
				$response[] = $row;
			}
		}
		return $response;
	}

	/*-----------------------------------/
	* Post a comment to an entry
	*
	* <AUTHOR> Army
	* @param	$entry_id	Blog entry id
	* @param	$message	Comments
	* @param	$name		Name of commenter
	* @param	$email		Email of commenter
	* @return	Boolean		True/false on success/error
	*/
	public function post_comment($entry_id, $message, $name='', $email=''){
		$params = [
			$entry_id,
			$name,
			$email,
			$message,
			+!$this->settings['comment_approval'],
			defined('USER_LOGGED_IN') && USER_LOGGED_IN ? USER_LOGGED_IN : NULL,
			get_ip(),
			date("Y-m-d H:i:s")
		];

		return $this->db->query("INSERT INTO `blog_comments` (`entry_id`, `name`, `email`, `message`, `approved`, `account_id`, `ip_address`, `date_added`) VALUES (?,?,?,?,?,?,?,?)", $params);
	}

	/*-----------------------------------/
	* Track social shares
	*
	* <AUTHOR> Army
	* @param	entry_id		Specific Entry ID
	* @param	service			The social media service used to share product (facebook, twitter, etc.)
	* @param	url				URL that was shared
	* @param	account_id		Account ID of user if logged in
	* @return	boolean			True/False on success or failure
	*/
	public function share_entry($entry_id, $service, $url, $account_id=NULL){
		$params = array($entry_id, $account_id, session_id(), get_ip(), gethostbyaddr(get_ip()), $service, $url, date("Y-m-d H:i:s"));
		return $this->db->query("INSERT INTO `blog_entries_shares`(`entry_id`, `account_id`, `session_id`, `ip_address`, `hostname`, `service`, `url`, `share_time`) VALUES(?,?,?,?,?,?,?,?)", $params);
	}

	/*-----------------------------------/
	* Tracks amount of views for a blog post
	*
	* <AUTHOR> Army
	* @param	$entry_id		ID of the post being viewed
	* @return	Boolean			True/false upon success status
	*/
	public function log_entry_view($entry_id){
		return $this->db->query("UPDATE `blog_entries` SET `view_count` = `view_count`+1 WHERE `entry_id` = ?", [$entry_id]);
	}

	/*-----------------------------------/
	* Formats a timestamp to display as text
	*
	* <AUTHOR> Army
	* @param	$timestamp		Timestamp to be formatted
	* @param	$format			Date format to be returned, if over a week ago
	* @return	String			Formatted text
	*/
	public function ago($timestamp, $format='M j, Y'){
		$granularity=1;
		$difference = time() - strtotime($timestamp);
		if($difference < 0) return 'Just Now';
		elseif($difference < 864000){
			$periods = array('week' => 604800,'day' => 86400,'hour' => 3600,'minute' => 60,'second' => 1);
			$output = '';
			foreach($periods as $key => $value){
				if($difference >= $value){
					$time = round($difference / $value);
					$difference %= $value;
					$output .= ($output ? ' ' : '').$time.' ';
					$output .= (($time > 1) ? $key.'s' : $key);
					$granularity--;
				}
				if($granularity == 0) break;
			}
			return ($output ? $output : '0 seconds').' ago';
		}
		else return date($format, strtotime($timestamp));
	}
}

?>