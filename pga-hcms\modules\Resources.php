<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID ==  $_cmssections['dashboard']){
	$total_records = $db->get_record_count('resources');
	$CMSBuilder->set_widget($_cmssections['resources'], 'Total Member Resources', $total_records, 'files-o');
}

if(SECTION_ID == $_cmssections['resources']){

	//Define vars
	$record_db = 'resources';
	$record_id = 'file_id';
	$record_name = 'File';
	$category_id = (isset($_GET['category_id']) && !empty($_GET['category_id']) ? $_GET['category_id'] : NULL); //Remember category

	$filedir = "../docs/resources/";
	$cropimages = array();
	$errors = false;
	$required = array();

	$imagetypes = array('jpg', 'jpeg', 'pjpeg', 'png', 'gif');
	$filetypes = array('pdf', 'doc', 'docx', 'xls', 'xlsx');

	//Get files
	$records_arr = array();
	$params = array();
	if(!empty($category_id)){
		$params[] = $category_id;
	}
	if($searchterm != ""){
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
	}
	$query = $db->query("SELECT `$record_db`.*, `resource_categories`.`name` AS `category_name`, `account_profiles`.`first_name`, `account_profiles`.`last_name` FROM `$record_db` ".
	"LEFT JOIN `resource_categories` ON `$record_db`.`category_id` = `resource_categories`.`category_id` ".
	"LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` WHERE `$record_db`.`$record_id` IS NOT NULL ".
	(!empty($category_id) ? "&& `$record_db`.`category_id` = ? " : "").
	($searchterm != "" ? "&& (`$record_db`.`file_name` LIKE ? OR `$record_db`.`file_location` LIKE ? OR CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ?) " : "").
	"ORDER BY `$record_db`.`date_added` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();

        // echo "<pre>";
        // print_r ($result[0]);
        // echo "</pre>";

		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	//Get categories
	$categorypage = $CMSBuilder->get_section($_cmssections['resource_categories']);
	$categories = array();
	$query = $db->query("SELECT * FROM `resource_categories` ORDER BY `ordering` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$categories[$row['category_id']] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];
		}
	}

	//Delete item
	if(isset($_POST['delete'])){

		$delete = $db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if($delete && !$db->error()){
			if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
				unlink($filedir.$_POST['old_file']);
			}
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(), false);	
		}
		header("Location: " .PAGE_URL);
		exit();

	//Save item
	}else if(isset($_POST['save'])){

		// if files empty then upload only data
		if(empty($_FILES)) {
			$params = array(
				ITEM_ID,
				USER_LOGGED_IN, 
				$_POST['category_id'], 
				$_POST['file_name'], 
				date("Y-m-d H:i:s"),
				ITEM_ID
			);

			$update = $db->query("UPDATE `resources` SET `file_id` = ?, `account_id` = ?, `category_id` = ?, `file_name` = ?, `last_updated` = ? WHERE `file_id` = ? ", $params);

			if($update && !$db->error()){
				$CMSBuilder->set_system_alert('File was successfully updated.', true);
			} else {
				$CMSBuilder->set_system_alert('Unable to update file. Please try again ' .$db->error(),false);	
			}
			header("Location: " .PAGE_URL);
			exit();
		}

		//Validate
		if(trim($_POST['file_name']) == ''){
			$errors[] = 'Please enter a name for your file.';
		}
		if($_POST['old_file'] == '' && empty($_FILES['file']['name'])){
			$errors[] = 'Please select a file to upload.';
			array_push($required, 'file');
		}
		if(!empty($_FILES['file']['size']) && $_FILES['file']['size'] > ********){
			$errors[] = 'File size is too large.';
		}
		if(!empty($_FILES['file']['name'])){
			$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
			if(!in_array($ext, $imagetypes) && !in_array($ext, $filetypes)){
				$errors[] = 'File type `' .$ext. '` is restricted. Allowed file types include '.implode(', ', $imagetypes).', '.implode(', ', $filetypes).'.';
			}
		}

		// Category validation
		if(!empty($_POST['category_id']) && !is_numeric($_POST['category_id'])){
			$errors[] = 'Please select a valid folder.';
		}

		//file upload error checking
		if(!empty($_FILES['file']['name']) && $_FILES['file']['error'] !== UPLOAD_ERR_OK){
			$errors[] = 'File upload error occurred. Please try again.';
		}

		$filesize = $_FILES['file']['size'];

		if(!$errors){

			//Upload file
			$file = ($_POST['old_file'] != '' ? $_POST['old_file'] : NULL);
			// if(!empty($_FILES['file']['name'])){
			// 	$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
			$newname = clean_url($_POST['file_name']).'-'.date("ymdhis").'.'.$ext;

			// copy() method for file upload
			if (!@copy($_FILES['file']['tmp_name'], $filedir . $newname)) {
				$errors[] = "Failed to upload the file!: " . $_FILES['file']['name'];
			} else {
				if(file_exists($filedir.$newname)){
					$file = $newname;
					
					if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
						unlink($filedir.$_POST['old_file']);
					}

					//File type detection
					$file_type = NULL;
					switch($ext){
						case 'jpg':
						case 'jpeg':
						case 'pjepg':
						case 'png':
						case 'gif':
							$file_type = 'image';
							break;
						case 'pdf':
							$file_type = 'pdf';
							break;
						case 'doc':
						case 'docx':
							$file_type = 'word';
							break;
						case 'xls':
						case 'xlsx':
							$file_type = 'excel';
							break;
						}	
						
					// echo "<pre>POST data: ";
					// print_r($_POST);
					// echo "\nFILES data: ";
					// print_r($_FILES);
					// echo "\nParams: ";
					// print_r($params);
					// echo "\nFile: " . $file;
					// echo "\nFile type: " . $file_type;
					// echo "\nFile size: " . $filesize;
					// echo "\nFile NAME: " . $newname;
					// echo "</pre>";

					//
					$params = array(
						USER_LOGGED_IN, 
						$_POST['category_id'], 
						$_POST['file_name'], 
						formatBytes($filesize),
						// $filesize,
						$file_type,
						$newname, 
						date("Y-m-d H:i:s"),
						date("Y-m-d H:i:s")
						);

					$insert = $db->query("INSERT INTO `resources`(`account_id`, `category_id`, `file_name`, `file_size`, `file_type`, `file_location`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?,?,?)", $params);

					if($insert && !$db->error()){
						$CMSBuilder->set_system_alert('File was successfully uplaoded.', true);
					} else {
						$CMSBuilder->set_system_alert('Unable to save file. Please try again ' .$db->error(),false);	
					}

					header("Location: " .PAGE_URL);
					exit();
				}
				else{
					$errors[] = 'Unable to upload file. Please try again.';
				}
			}
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	//Crop images
	}else if(isset($_POST['crop'])){
		include("includes/jcropimages.php");
		$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
		header("Location: " .(!empty($_POST['redirect']) ? $_POST['redirect'] : PAGE_URL));
		exit();
	}

}

?>