<?php
	
//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}
// exit('transactions settings');

echo "<form action='' method='post' enctype='multipart/form-data'>";

	//General settings
	echo "<div class='panel'>";
		echo "<div class='panel-header'>General Settings
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			echo "<div class='form-field'>
				<label>Maximum Online Payment Amount " .$CMSBuilder->tooltip('Maximum Online Payment Amount', 'Orders exceeding this amount will not be able to make credit card payments online, unless a service fee has been set. Leave blank for no limit.'). "</label>
				<input type='text' name='max_payment_amount' value='".(isset($reg_settings['max_payment_amount']) ? $reg_settings['max_payment_amount'] : '')."' class='input number' />
			</div>";
		echo "</div>";
	echo "</div>"; //General settings

	//Tabs: Payment Methods, Taxes and Fees
	echo "<div class='page-content'>";
		echo "<div class='tabs tab-ui'>";
			echo "<ul>";
				echo "<li><a href='#payment-methods'>Payment Methods</a></li>";
				echo "<li><a href='#taxes-fees'>Taxes &amp; Fees</a></li>";
			echo "</ul>";

			//Payment Methods
			echo "<div id='payment-methods'>";
				echo "<p><strong>NOTE: </strong>Payment methods <span class='text-underlined'>must</span> be enabled with your payment processing gateway to be accepted on your website.</p>";
				foreach($payment_options as $payment_option) {
					$card_type = strtolower($payment_option['type']);
					echo "<input type='checkbox' value='".$payment_option['payment_id']."' id='payment_option_".$card_type."' name='payment_options[]' class='checkbox'".(isset($reg_settings['payment_options']) && in_array($payment_option['payment_id'], $reg_settings['payment_options']) ? " checked" : "")." /><label for='payment_option_".$card_type."'>".$payment_option['name']."</label><br/>";
				}
			echo "</div>";

			//Taxes and Fees
			echo "<div id='taxes-fees' class='nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0'>";
					echo "<tr>
						<td width='210px'><label class='nopadding'>Federal GST Rate</label></td>
						<td><input type='text' name='federal_gst' value='".(isset($reg_settings['federal_gst']) ? $reg_settings['federal_gst'] : '')."' class='input input_sm nomargin' /> %</td>
					</tr>";
				echo "</table>";

				echo "<table cellpadding='0' cellspacing='0' border='0'>";
					echo "<tr><td colspan='3' height='40px' class='text-caps'><strong>Provincial Tax Rates</strong></td></tr>";
					
					foreach($reg_settings['tax_rates'] as $tax_rate) {
						$state_code = strtolower($tax_rate['state']);
						$state_name = str_replace("é", "&eacute", $tax_rate['state_name']);
						echo "<tr>
							<td width='210px'><label class='nopadding'>".$state_name." (".$tax_rate['state'].")</label></td>
							<td width='160px'><input type='text' name='tax_rate_".$state_code."' value='".($tax_rate['state'] == 'QC' ? number_format($tax_rate['rate'], 3) : number_format($tax_rate['rate'], 1))."' class='input input_sm nomargin' /> %</td>
							<td><input type='checkbox' value='1' id='hst_".$state_code."' name='hst_".$state_code."' class='checkbox'".($tax_rate['hst'] == 1 ? " checked" : "")." /><label for='hst_".$state_code."'>HST Province</label></td>
						</tr>";
					}
				echo "</table>";

				echo "<table cellpadding='0' cellspacing='0' border='0'>";
					echo "<tr><td colspan='2' height='40px' class='text-caps'><strong>Additional Fees</strong></td></tr>";
					echo "<tr>
						<td width='210px'><label class='nopadding'>Service Fee ".$CMSBuilder->tooltip('Service Fee', 'Administrative fee to be added to all online credit card purchases. May be a flat rate, or a percentage (example: 5.00 <b>or</b> 2.3). Please be sure to select the correct type below. This setting can be overwritten for individual events.')."</label></td>
						<td><input type='text' name='admin_fee' value='".(isset($reg_settings['admin_fee']) ? number_format($reg_settings['admin_fee'], 2, '.', '') : '')."' class='input input_sm decimal nomargin' /></td>
					</tr>";
					echo "<tr>
						<td width='210px'><label class='nopadding'>Service Fee Type ".$CMSBuilder->tooltip('Service Fee Type', 'Select if the Service Fee (if applicable) will be calculated as a percentage, or a flat fee added to all online credit card purchases. This setting can be overwritten for individual events.')."</label></td>
						<td>
							<select name='admin_fee_type' class='select nomargin'>
								<option value='Dollar'".(isset($reg_settings['admin_fee_type']) && $reg_settings['admin_fee_type'] == 'Dollar' ? " selected" : "").">Dollar Amount</option>
								<option value='Percent'".(isset($reg_settings['admin_fee_type']) && $reg_settings['admin_fee_type'] == 'Percent' ? " selected" : "").">Percentage</option>
							</select>
						</td>
					</tr>";
				echo "</table>";
			echo "</div>";

		echo "</div>";
	echo "</div>"; //Tabs
	
	//Sticky footer
	echo "<footer id='cms-footer' class='resize'>";
	echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
	echo "</footer>";

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid']. "'/>";	
echo "</form>";

?>