<?php

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

if(isset($_POST) && USER_LOGGED_IN){
			
	//Update head office
	$params = array($_POST['id']);
	$db->new_transaction();
	$db->query("UPDATE `locations` SET `head_office` = false WHERE `location_id` != ?", $params);
	$db->query("UPDATE `locations` SET `head_office` = true WHERE `location_id` = ?", $params);
	
	if(!$db->error()){
		$db->commit();
		echo $CMSBuilder->mini_alert("<p>Head office successfully saved!</p>",true);
		sitemap_XML();
	} else {
		echo $CMSBuilder->mini_alert("<p>There was an error updating this record: ".$db->error()."</p>",false);
	}
	
} else {
	echo 'error';
}
	
?>