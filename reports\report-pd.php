<?php

//Config
include("config.php");

//Get vars
$id = (isset($_GET['id']) ? $_GET['id'] : NULL);
$mid = (isset($_GET['mid']) ? $_GET['mid'] : NULL);
$year = (isset($_GET['year']) ? $_GET['year'] : date('Y')); //Default this year
$start_date = $year.'-01-01';
$end_date = $year.'-12-31';
if($year == date('Y')){
	$end_date = $year.'-'.date('m-d'); //only go up to today if viewing this year
}

//Set data array
$records = array();

//Individual report
if(!empty($id)){
	
	//Get member details
	$member = array('first_name' => '', 'last_name' => '');
	$query = $db->query("SELECT `first_name`, `last_name` FROM `account_profiles` WHERE `profile_id` = ?", array($id));
	if(!$db->error() && $db->num_rows()){
		$member = $db->fetch_array()[0];
	}
	
	//Get top level categories
	$query = $db->query("SELECT `category_id`, `name` FROM `pd_categories` WHERE `parent_id` IS NULL ORDER BY `ordering`");
	if($query && !$db->error()){
		$categories = $db->fetch_array();
		foreach($categories as $cat){
			$records[$cat['category_id']] = array(
				'category_name' => $cat['name'],
				'records' => array()
			);
		}
	}
	
	$params = array($year, $id);
	$query = $db->query("SELECT `pd_points`.*, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `account_profiles`.`profile_id`, `pd_categories`.`name` AS `category_name`, `pd_categories`.`parent_id` AS `pd_parent_id` FROM `pd_points` ".
	"LEFT JOIN `account_profiles` ON `pd_points`.`account_id` = `account_profiles`.`account_id` ".
	"LEFT JOIN `pd_categories` ON `pd_points`.`category_id` = `pd_categories`.`category_id` ".
	"WHERE `pd_points`.`year` = ? && `account_profiles`.`profile_id` = ? GROUP BY `pd_points`.`points_id`", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records[$row['pd_parent_id']]['records'][] = $row;	
		}
	}
		
	//Set html
	$html = '<table cellpadding="0" cellspacing="0" width="100%" border="0">
		<tr>
			<td width="120px"><img src="' .$logo. '" width="120px" /></td>
			<td align="center"><h2>' .(!empty($member) ? $member['first_name'].' '.$member['last_name'].'<br />' : ''). 'Top 100 Program Points ' .$year. '</h2></td>
			<td width="120px" align="right">';
			if(file_exists('../images/logos/foresight-sports-canada-************.png')){
				$html .= '<img src="' .$imagepath.'logos/foresight-sports-canada-************.png" width="120px" />';	
			}
			$html .= '</td>
		</tr>
	</table><br />';

	$grandtotal = 0;
	foreach($records as $category){
		if(!empty($category['records'])){
			$html .= '<h3 style="' .$css['heading']. '">' .$category['category_name']. '</h3>
			<table cellpadding="5" cellspacing="1" width="100%" border="0">
				<tr>
					<th style="' .$css['th']. '" width="35%">Category</th>
					<th style="' .$css['th']. '" width="35%">Title</th>
					<th style="' .$css['th']. '" width="15%">Points</th>
					<th style="' .$css['th']. '" width="15%">Awarded</th>
				</tr>';
				$total = 0;
				$count = 0;
				foreach($category['records'] as $record){

					//Only display records with a total
					if($record['points'] > 0 || $records['prize'] > 0){
						$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
						$html .= '<tr>
							<td style="' .$css_td. '">' .$record['category_name']. '</td>
							<td style="' .$css_td. '">' .$record['title']. '</td>
							<td style="' .$css_td. '" align="center">' .$record['category_points']. '</td>
							<td style="' .$css_td. '" align="center">' .number_format($record['points']). '</td>
						</tr>';
						$count++;
						$total+=$record['points'];
						$grandtotal+=$record['points'];
					}
				}

			$html .= '</table>';
			$html .= '<hr style="margin:0;" /><p style="text-align:right;"><strong>Total Points:&nbsp; '.$total.' &nbsp;</strong></p>';
		}
	}
	if($grandtotal == 0){
		$html .= '<br /><p>No records found.</p>';			
	}

	//Category maximums
	$query = $db->query("SELECT `name`, `max_points` FROM `pd_categories` WHERE `max_points` IS NOT NULL && `status` = ? ORDER BY `ordering`", array('Active'));
	if($query && !$db->error() && $db->num_rows()){
		$categories = $db->fetch_array();
		
		$html .= '<br />
		<p><strong>NOTE:</strong> Awarded points are based on the following maximums for the categories below:</p>';
		foreach($categories as $cat){
			$html .= $cat['name'].' - Maximum '.$cat['max_points'].' points<br />';
		}
	}
	
	//Generate pdf and send to browser
	$mpdf->WriteHTML($html, 2);
	$mpdf->Output('Individual PD Points '.$year. (!empty($records) ? ' - '.$records[0]['first_name'].' '.$records[0]['last_name'] : '').'.pdf','I');
	
	
//Full report	
}else{
	
	$params = array($year);
	if(!empty($mid)){
		$params[] = $mid;
	}
	$query = $db->query("SELECT `pd_points`.*, SUM(`pd_points`.`points`) AS `total_points`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `account_profiles`.`profile_id`, `membership_types`.`membership_name`, `facilities`.`facility_name` ".
	"FROM `pd_points` ".
	"LEFT JOIN `account_profiles` ON `pd_points`.`account_id` = `account_profiles`.`account_id` ".
	"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
	"LEFT JOIN `membership_types` ON `account_profiles`.`membership_id` = `membership_types`.`membership_id` ".
	"WHERE `pd_points`.`year` = ? ".
	(!empty($mid) ? "&& `account_profiles`.`membership_id` = ? " : "").
	"GROUP BY `pd_points`.`account_id` ORDER BY `total_points` DESC", $params);
	if($query && !$db->error()){
		$records = $db->fetch_array();
	}
	
	//Set html
	$html = '<table cellpadding="0" cellspacing="0" width="100%" border="0">
		<tr>
			<td width="120px"><img src="' .$logo. '" width="120px" /></td>
			<td align="center"><h2>Top 100 Program Standings ' .$year. '</h2></td>
			<td width="120px" align="right">';
			if(file_exists('../images/logos/foresight-sports-canada-************.png')){
				$html .= '<img src="' .$imagepath.'logos/foresight-sports-canada-************.png" width="120px" />';	
			}
			$html .= '</td>
		</tr>
	</table><br />';

	$html .= '<table cellpadding="5" cellspacing="1" width="100%" border="0"><tr>
			<th style="' .$css['th']. '" width="50px">Rank</th>
			<th style="' .$css['th']. '" width="150px">Professional</th>
			<th style="' .$css['th']. '" width="150px">Membership</th>
			<th style="' .$css['th']. '">Facility</th>
			<th style="' .$css['th']. '" width="100px">Points</th>
		</tr>';
		$count = 0;
		$rank = 1;
		$prev_points = '';
		foreach($records as $record){

			//Only display records with a total
			if($record['points'] > 0 || $records['prize'] > 0){
				$count++;
				
				//Determine rank
				if($record['total_points'] != $prev_points){
					$rank = $count;
					$prev_points = $record['total_points'];
				}
				
				$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
				$html .= '<tr>
					<td style="' .$css_td. '" align="center">' .$rank. '</td>
					<td style="' .$css_td. '"><a href="' .$siteurl.$path. 'reports/report-pd.php?year='.$year.'&id='.$record['profile_id']. '">' .$record['last_name'].', '.$record['first_name']. '</a></td>
					<td style="' .$css_td. '">' .$record['membership_name']. '</td>
					<td style="' .$css_td. '">' .$record['facility_name']. '</td>
					<td style="' .$css_td. '" align="center">' .number_format($record['total_points']). '</td>
				</tr>';
				
				if($count == 100){
					//break;
				}
			}
		}
	$html .= '</table>';
	if($count == 0){
		$html .= '<p>No records found.</p>';
	}

	//Generate pdf and send to browser
	$mpdf->WriteHTML($html, 2);
	$mpdf->Output('PD Points '.$year.'.pdf','I');
	
}

?>