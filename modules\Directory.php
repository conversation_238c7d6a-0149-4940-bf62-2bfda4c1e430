<?php
// modules/directory.php - Fetches data for directory list or profile detail

// Initialize variables to avoid errors if this module runs when not on the directory page
$directory_list = [];
$profile_detail = null;
$is_directory_list_page = false;
$is_profile_detail_page = false;
$profile_id_from_url = null;

// global $error404;

// --- Check if we are on the Directory List Page ---
if (
    // isset(PAGE_ID, $_sitepages['directory']['page_id']) && 
PAGE_ID == $_sitepages['directory']['page_id']) {
    $is_directory_list_page = true;
    error_log("Directory Module: Detected Directory List Page (PAGE_ID: " . PAGE_ID . ")");

    // Get Search Parameters
    $search_term = trim($_GET['search'] ?? '');
    $sql_params = [' ', 'Active']; // Base parameters
    $search_where_clause = '';

    // if (!empty($search_term)) {
    //     error_log("Directory Module: Search term found: " . $search_term);
    //     $search_param = '%' . $search_term . '%';
    //     // Add params for each LIKE condition
    //     array_push($sql_params, ' ', $search_param, $search_param, $search_param, $search_param);
    //     // Note: Parameter count must match placeholders '?'
    //     $search_where_clause = "AND (CONCAT(ap.`first_name`, ?, ap.`last_name`) LIKE ? OR acc.`email` LIKE ? OR f.`facility_name` LIKE ? OR mc.`class_name` LIKE ?) ";
    // }

    // // Fetch Directory List Query
    // $sql = "SELECT
    //             acc.`email`, acc.`showhide` AS account_showhide,
    //             ap.`profile_id`, ap.`account_id`, ap.`first_name`, ap.`last_name`,
    //             ap.`show_email`, ap.`show_phone`, ap.`phone`, ap.`phone_alt`,
    //             CONCAT(ap.`first_name`, ?, ap.`last_name`) AS `full_name`,
    //             mc.`class_name`,
    //             f.`facility_name`
    //         FROM `accounts` acc
    //         LEFT JOIN `account_permissions` apm ON acc.`account_id` = apm.`account_id` AND apm.`role_id` = 2 -- Role ID 2 assumed for members displayable in directory
    //         LEFT JOIN `account_profiles` ap ON acc.`account_id` = ap.`account_id`
    //         LEFT JOIN `membership_classes` mc ON ap.`class_id` = mc.`class_id`
    //         LEFT JOIN `facilities` f ON ap.`facility_id` = f.`facility_id`
    //         WHERE acc.`status` = ?
    //           AND apm.`role_id` IS NOT NULL -- Ensures they have the specific role
    //           AND acc.`showhide` = 0        -- Check account visibility flag
    //           AND ap.`show_profile` != 0    -- Check profile visibility (allow 1=Public or -1=Members Only)
    //           {$search_where_clause}
    //         GROUP BY ap.`account_id` -- Group by account to avoid duplicates if multiple permissions/roles exist
    //         ORDER BY ap.`last_name` ASC, ap.`first_name` ASC";

    // if (isset($db) && is_object($db)) {
    //     $query_list = $db->query($sql, $sql_params);
    //     if ($query_list && !$db->error()) {
    //         $directory_list = $db->fetch_array($query_list); // Fetch all matching members
    //          error_log("Directory Module: Fetched " . count($directory_list) . " members for list.");
    //     } else {
    //         error_log("Directory Module: Error fetching directory list: " . ($db->error() ?? 'Query failed'));
    //     }
    // } else {
    //      error_log("Directory Module: DB object not available for list query.");
    // }



    //
    $directory = array();
	$params = array(' ', 'Active');
	$searchqry = '';
	if(isset($_GET['search']) && trim($_GET['search']) != ''){
		$params[] = ' ';
		$params[] = '%'.$_GET['search'].'%';
		$params[] = '%'.$_GET['search'].'%';
		$params[] = '%'.$_GET['search'].'%';
		$params[] = '%'.$_GET['search'].'%';
		
		$searchqry .= "&& (CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ? || `accounts`.`email` LIKE ? || `facilities`.`facility_name` LIKE ? || `membership_classes`.`class_name` LIKE ?) ";
	}
	
	$query = $db->query("SELECT `accounts`.`email`, `accounts`.`email_alt`, `account_profiles`.*, CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) AS `full_name`, `account_permissions`.`role_id`, `membership_classes`.`class_name`, `facilities`.`facility_name` FROM `accounts` ".
	"LEFT JOIN `account_permissions` ON `accounts`.`account_id` = `account_permissions`.`account_id` && `account_permissions`.`role_id` = 2 ".
	"LEFT JOIN `account_profiles` ON `accounts`.`account_id` = `account_profiles`.`account_id` ".
	"LEFT JOIN `membership_classes` ON `account_profiles`.`class_id` = `membership_classes`.`class_id` ".
	"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".	
	"WHERE `accounts`.`status` = ? && `account_permissions`.`role_id` IS NOT NULL && `accounts`.`showhide` = 0 ".$searchqry.
	"GROUP BY `account_profiles`.`account_id` ".
	"ORDER BY `account_profiles`.`last_name` ASC, `account_profiles`.`first_name` ASC", $params);

    // echo "SELECT `accounts`.`email`, `accounts`.`email_alt`, `account_profiles`.*, CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) AS `full_name`, `account_permissions`.`role_id`, `membership_classes`.`class_name`, `facilities`.`facility_name` FROM `accounts` ".	"LEFT JOIN `account_permissions` ON `accounts`.`account_id` = `account_permissions`.`account_id` && `account_permissions`.`role_id` = 2 ".
	// "LEFT JOIN `account_profiles` ON `accounts`.`account_id` = `account_profiles`.`account_id` ".
	// "LEFT JOIN `membership_classes` ON `account_profiles`.`class_id` = `membership_classes`.`class_id` ".
	// "LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".	
	// "WHERE `accounts`.`status` = ? && `account_permissions`.`role_id` IS NOT NULL && `accounts`.`showhide` = 0 ".$searchqry.
	// "GROUP BY `account_profiles`.`account_id` ".
	// "ORDER BY `account_profiles`.`last_name` ASC, `account_profiles`.`first_name` ASC";

    // echo "<pre>";
    // print_r($params);
    // echo '</pre>';

	if($query && !$db->error()){
		$directory_list = $db->fetch_array();
        // echo "<pre>";
        // print_r($directory_list);
        // echo '</pre>';
	} 
    //

}
// --- Check if we are on a Directory Detail Page ---
// Assumes URL structure like /directory-slug/firstname-lastname-profileid/
elseif (
    // isset(PARENT_ID, $_sitepages['directory']['page_id']) && 
    PARENT_ID == $_sitepages['directory']['page_id'] && empty(PAGE_ID)) {
// else{
    // Get the profile identifier from the URL slug
    $pagebits = $SiteBuilder->get_pagebits($_sitepages['directory']['page'] ?? 'directory'); // Use actual page name/slug
    if (isset($pagebits[2]) && !empty($pagebits[2])) { // Expecting slug in the 3rd segment (index 2)
        $slug_parts = explode("-", $pagebits[2]);
        $profile_id_from_url = (int)end($slug_parts); // Get the last part, assume it's the ID

        // echo "profile-id from url - ".$profile_id_from_url;
        // exit;

        if ($profile_id_from_url > 0) {
            $is_profile_detail_page = true;
             $error404 = false;       
            //  error_log("Directory Module: Detected Profile Detail Page. Attempting to load profile ID: " . $profile_id_from_url);

            // Fetch Specific Profile Query (More detailed query needed)
            $sql_detail = "SELECT
                               acc.`email`, acc.`email_alt`, acc.`showhide` AS account_showhide,
                               ap.*,
                               apm.`role_id`,
                               mc.`class_name`,
                               mt.membership_name,
                               f.`facility_name`, f.`phone` AS facility_phone,
                               CONCAT(ap.`first_name`, ?, ap.`last_name`) AS `full_name`
                           FROM `accounts` acc
                           LEFT JOIN `account_permissions` apm ON acc.`account_id` = apm.`account_id` AND apm.`role_id` = 2 -- Ensure they have displayable role
                           LEFT JOIN `account_profiles` ap ON acc.`account_id` = ap.`account_id`
                           LEFT JOIN `membership_classes` mc ON ap.`class_id` = mc.`class_id` 
                           LEFT JOIN `membership_types` mt ON ap.`membership_id` = mt.`membership_id` 
                           LEFT JOIN `facilities` f ON ap.`facility_id` = f.`facility_id`
                           WHERE ap.`profile_id` = ?
                             AND acc.`status` = ? 
                             AND apm.`role_id` IS NOT NULL
                             AND acc.`showhide` = 0
                             AND ap.`show_profile` != 0 
                           GROUP BY ap.`account_id`"; // Should only be one result
 
            // echo $sql_detail;
            
            if (isset($db) && is_object($db)) {
                $query_detail = $db->query($sql_detail, [' ',$profile_id_from_url,'Active']);

                if ($query_detail && !$db->error() && $db->num_rows() > 0) {
                    $result = $db->fetch_array($query_detail);

                    $profile_detail = (object)$result[0]; // Store as object
                   
                    // echo "<pre> profile - detail";
                    // print_r($profile_detail);
                    // echo "</pre>";
                
                     error_log("Directory Module: Successfully fetched profile detail for ID: " . $profile_id_from_url);

                    //  // --- Fetch Q&A for this profile ---
                    //  $profile_detail->questions = []; // Initialize questions array
                    //  $sql_qa = "SELECT pq.`question`, pa.`answer`
                    //             FROM `account_profile_answers` pa
                    //             JOIN `account_profile_questions` pq ON pa.`question_id` = pq.`question_id`
                    //             WHERE pa.`account_id` = ?
                    //               AND pa.`answer` IS NOT NULL AND pa.`answer` != '' -- Only show non-empty answers
                    //               AND pq.`showhide` = 0 -- Only show active questions
                    //             ORDER BY pq.`ordering` ASC";
                    //  $query_qa = $db->query($sql_qa, [$profile_detail->account_id]);
                    //  if ($query_qa && !$db->error()) {
                    //       $profile_detail->questions = $db->fetch_array($query_qa);
                    //       error_log("Directory Module: Fetched " . count($profile_detail->questions) . " Q&A items.");
                    //  } else {
                    //       error_log("Directory Module: Error fetching Q&A: " . ($db->error() ?? 'QA Query failed'));
                    //  }

                     // --- Optional: Fetch PD points / other related data ---
                     // Add queries similar to your old module if needed here,
                     // storing results as properties of $profile_detail, e.g., $profile_detail->pd_years = ...

                     // --- SEO / Page Variable Overrides ---
                     // Set page title, meta description etc. for the profile view
                     global $page, $breadcrumbs; // Make sure these are accessible
                     if (isset($page)) {
                          $parent_page_id = $_sitepages['directory']['page_id'];
                          $parent = $SiteBuilder->get_page_content($parent_page_id); // Fetch parent page data
                          $profile_url_segment = clean_url($profile_detail->first_name . '-' . $profile_detail->last_name . '-' . $profile_detail->profile_id);
                          $profile_full_url = $_sitepages['directory']['page_url'] . $profile_url_segment . '/';

                          // Verify URL and redirect if incorrect (like old module)
                          if ($profile_url_segment != $pagebits[2]) {
                              error_log("Directory Module: URL mismatch. Redirecting from " . $pagebits[2] . " to " . $profile_url_segment);
                              header("HTTP/1.1 301 Moved Permanently");
                              header('Location: ' . $profile_full_url);
                              exit();
                          }

                          $page['page_title'] = $profile_detail->first_name . ' ' . $profile_detail->last_name;
                          $page['content'] = ''; // Content comes from the display file
                          $page['class'] = 'directory directory-detail'; // Add specific classes
                          $page['meta_canonical'] = $profile_full_url;
                          $page['meta_title'] = $profile_detail->first_name . ' ' . $profile_detail->last_name . ' | ' . ($parent['meta_title'] ?? $_sitepages['directory']['name'] ?? 'Member Directory');
                          $page['meta_description'] = ($global['company_name'] ?? '') . ' member profile for ' . $profile_detail->first_name . ' ' . $profile_detail->last_name . '.';
                          // Keep parent banner or potentially use profile photo?
                          // $page['banner_image'] = $parent['banner_image'] ?? null;
                          // $page['banner_image_alt'] = $parent['banner_image'] ?? null;
                          if (!empty($profile_detail->photo)) {
                              $page['logo'] = $path . 'images/users/thumbs/' . $profile_detail->photo; // Set profile photo for potential use
                          }

                           // Adjust breadcrumbs
                           if (isset($breadcrumbs) && is_array($breadcrumbs)) {
                               array_pop($breadcrumbs); // Remove the generic directory entry
                               // Add parent directory link
                            //    $breadcrumbs[] = ['name' => ($parent['name'] ?? $_sitepages['directory']['name'] ?? 'Member Directory'), 'url' => $_sitepages['directory']['page_url']];
                               // Add current profile name (not linked)
                               $breadcrumbs[] = ['name' => 'Member Profile', 'url' =>  $_sitepages['directory']['page_url']];
                           }
                     } // end if isset($page)

                    // echo "is_profile_detail_page - ".$is_profile_detail_page;

                    // $is_profile_detail_page = true; 

                } else {
                    // Profile ID from URL not found or query failed
                    error_log("Directory Module: Profile ID " . $profile_id_from_url . " not found or error: " . ($db->error() ?? 'Query failed'));
                    // Set flag to indicate profile not found, page display can handle this
                    $is_profile_detail_page = false; // Treat as not a valid detail page
                     // Optionally trigger 404 logic if needed, but usually handled by index.php/Pages module
                    // $error404 = true;
                    $error404 = false;
                }
            } else {
                 error_log("Directory Module: DB object not available for detail query.");
            }
        } else {
             error_log("Directory Module: Invalid profile ID extracted from URL segment: " . $pagebits[2]);
             // Potentially trigger 404 or just show directory list
             $is_profile_detail_page = false;
        }
    } else {
         // URL doesn't match expected detail pattern (e.g., just /directory/)
         // This case should typically load the list view via the PAGE_ID check anyway
         error_log("Directory Module: URL segment for profile detail not found.");
         $is_profile_detail_page = false;
    }
} 
// else {
     // This module was included, but we are not on a directory list or detail page
     // Do nothing further in this script.
// }

?>