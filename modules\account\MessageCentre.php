<?php
//Display messages
if(PAGE_ID == $_sitepages['message-centre']['page_id']){

	//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.base64_encode($_SERVER['REQUEST_URI']));
		exit();
	}

	
	//Restrict to members
	if(!defined('MEMBER_ACCESS') || !MEMBER_ACCESS){
		exit();
	}
	
	//Define vars
	$panel_id = 76;
	$messages = array();
	
	//Pagination
	$pg = (isset($_GET['pg']) ? $_GET['pg'] : 1);
	$limit = 3;
	$totalresults = 0;
	
	//Get all messages
	$params = array(USER_LOGGED_IN);
	if(isset($_GET['search']) && trim($_GET['search']) != ''){
		$params[] = '%'.$_GET['search'].'%';
		//$query = $db->query("SELECT * FROM `messages` where `subject` LIKE ? ".
	//"ORDER BY `messages`.`date_sent` DESC",$params);
	}else{
		//$params[] = [];
		//$query = $db->query("SELECT * FROM `messages`".
	//"ORDER BY `messages`.`date_sent` DESC");
	}
	$query = $db->query("SELECT `message_recipients`.*, `messages`.* FROM `message_recipients` ".
	"LEFT JOIN `messages` ON `message_recipients`.`message_id` = `messages`.`message_id` ".
	"WHERE `message_recipients`.`account_id` = ? && `messages`.`date_sent` IS NOT NULL " .(isset($_GET['search']) && trim($_GET['search']) != '' ? " && `messages`.`subject` LIKE ? " : "").
	"ORDER BY `messages`.`date_sent` DESC", $params);

	// $query = $db->query("SELECT `message_recipients`.*, `messages`.* FROM `message_recipients` ".
	// "LEFT JOIN `messages` ON `message_recipients`.`message_id` = `messages`.`message_id` ".
	// "WHERE `messages`.`date_sent` IS NOT NULL " .(isset($_GET['search']) && trim($_GET['search']) != '' ? " && `messages`.`subject` LIKE ? " : "").
	// "ORDER BY `messages`.`date_sent` DESC", $params);

	
	//var_dump($db->last_query());exit;
	if($query && !$db->error()){
		$result = $db->fetch_array();
		//var_dump($result);exit;
		foreach($result as $row){
			$messages[$row['message_id']] = $row;
		}
		
		//Pagination
		if(ITEM_ID == ''){
			$totalresults = count($result);
			if($pg != 'all'){
				$start = (($pg-1)*$limit);
				$end = $limit;
			}else{
				$start = 0;
				$end = $totalresults;
			}
			$messages = array_slice($messages, $start, $end);
		}
		
	}else{
		$errors[] = 'Error retrieving messages: '.$db->error();
	}
	
	//Not found
	if(ITEM_ID != ''){
		if(!array_key_exists(ITEM_ID, $messages)){
			$errors[] = 'Message not found. Please select from the list below.';
		}else{
			$message = $messages[ITEM_ID];
			
			//Mark as read
			$query = $db->query("UPDATE `message_recipients` SET `read` = 1, `date_read` = ? WHERE `recipient_id` = ?", array(date('Y-m-d H:i:s'), $message['recipient_id']));
		}
	}
	
} 

?>