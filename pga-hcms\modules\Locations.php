<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('locations');
	$CMSBuilder->set_widget($_cmssections['locations'], 'Total Locations', $total_records);
}

if(SECTION_ID == $_cmssections['locations']){

	//Define vars
	$record_db   = 'locations';
	$record_id   = 'location_id';
	$record_name = 'Location';
	
	$records_arr = [];
	$location_numbers = [];
	$location_contacts = [];
	$location_hours = [];

	//Validation
	$errors   = false;
	$required = [];
	$required_fields = [
		'location_name',
		'phone',
		'email'
	];

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.location_name",
		"$record_db.address",
		"$record_db.address2",
		"$record_db.city",
		"$record_db.province",
		"$record_db.postal_code",
		"$record_db.country"
	];

	//Set default Phone numbers
	foreach ($db->get_enum_vals('location_numbers', 'type') as $type) {
		$location_numbers[] = ['type' => $type];
	}

	//Set default Hours
	for($i=1; $i<=7; $i++){
		$location_hours[] = array(
			'hours_id'    => NULL,
			'location_id' => NULL,
			'day'         => $weekdays[$i],
			'start_time'  => '09:00:00',
			'end_time'    => '17:00:00',
			'closed'      => 0
		);
	}

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get records
	$db->query("SELECT $record_db.* FROM $record_db $where ORDER BY head_office DESC, ordering, $record_id", $params);
	$records_arr = $db->fetch_assoc($record_id);

	//Format data
	foreach($records_arr as $loc_id => $row){

		//Concatenate Address
		$row['line1'] = trim($row['address2'].' '.$row['address']);
		$row['line2'] = implode(', ', array_filter([$row['city'], $row['province']]));
		$row['line2'] .= rtrim(' '.$row['postal_code']);
        
        //If line 1 is empty and postal code exists then use city as line 1 and province & PC as line 2 
		if(!$row['line1'] && $row['postal_code']){
			$row['line2'] = trim($row['province'].' '.$row['postal_code']);
			$row['line1'] = $row['city'];
		}

		$records_arr[$loc_id] = $row;
	}

	//Display errors
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}

	//Selected item
	if(ACTION == 'edit') {
		if($row = ($records_arr[ITEM_ID] ?? false)) {

			//Get phone numbers
			$db->query("SELECT * FROM location_numbers WHERE location_id = ?", [ITEM_ID]);
			$row['location_numbers'] = $db->fetch_array() ?: $location_numbers;

			//Get additional phones and emails
			$db->query("SELECT * FROM location_contacts WHERE location_id = ?", [ITEM_ID]);
			$row['location_contacts'] = $db->fetch_array() ?: $location_contacts;

			//Get business hours
			$db->query("SELECT * FROM location_hours WHERE location_id = ?", [ITEM_ID]);
			$row['location_hours'] = $db->fetch_array() ?: $location_hours;

			$records_arr[ITEM_ID] = $row;

		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}

	//Delete item
	if(isset($_POST['delete'])){

		if(!$records_arr[ITEM_ID]['head_office']){

			//Delete from table, foreign keys will cascade
			$db->query("DELETE FROM $record_db WHERE $record_id = ?", array(ITEM_ID));
			if(!$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			}else{
				$CMSBuilder->set_system_alert('Unable to delete record.', false);	
			}
			header("Location: " .PAGE_URL);
			exit();

		}else{
			$CMSBuilder->set_system_alert('Head Office location cannot be deleted.', false);	
		}

	//Save item
	}else if(isset($_POST['save'])){
		
		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);
		$_POST['google_map'] = isset($_POST['google_map']);
		$_POST['show_hours'] = isset($_POST['show_hours']);

		//Validate required fields
		foreach($required_fields as $field){
			if(!($_POST[$field] ?? false)){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}	

		//Format phone numbers
		if(is_array($_POST['number'] ?? false)){
			$location_numbers = array();
			foreach($_POST['number'] as $index => $number){
				$location_numbers[] = array(
					'number_id'       => $_POST['number_id'][$index],
					'phone'           => $number,
					'tollfree'        => $_POST['tollfree_'.$index] ?? 0,
					'hearingimpaired' => $_POST['hearingimpaired_'.$index] ?? 0,
					'type'            => $_POST['type'][$index],
				);
			}
		}

		//Format contacts
		if(is_array($_POST['contact_id'] ?? false)){
			$location_contacts = array();
			foreach($_POST['contact_id'] as $index => $contact){
				$location_contacts[] = array(
					'contact_id' 	=> $_POST['contact_id'][$index],
					'label'    		=> $_POST['contact_label'][$index],
					'value'    		=> $_POST['contact_value'][$index],
					'type' 			=> (detect_phone($_POST['contact_value'][$index]) ? 'phone' : (checkmail($_POST['contact_value'][$index]) ? 'email' : NULL))
				);
			}
		}

		//Format business hours
		$location_hours = array();
		for($i=1; $i<=7; $i++){
			if(isset($_POST['hours_id'.$i])){
				$location_hours[] = array(
					'hours_id'    => $_POST['hours_id'.$i],
					'location_id' => NULL,
					'day'         => $weekdays[$i],
					'start_time'  => $_POST['start_time'.$i] ?? 0,
					'end_time'    => $_POST['end_time'.$i] ?? 0,
					'closed'      => $_POST['closed'.$i] ?? 0
				);
			}
		}	

		if(!$errors){

			//Insert to db
			$params = array(
				
				//Insert
				ITEM_ID, 
				$_POST['location_name'], 
				$_POST['address'],
				$_POST['address2'],
				$_POST['city'],
				$_POST['province'],
				$_POST['postal_code'],
				$_POST['country'],
				$_POST['phone'],
				$_POST['fax'],
				$_POST['toll_free'],
				$_POST['email'],
				$_POST['google_place_id'],
				$_POST['google_map'],
				$_POST['gpslat'],
				$_POST['gpslong'],
				$_POST['zoom'],
				$_POST['show_hours'],
				$_POST['open_text'],
				$_POST['closing_text'],
				$_POST['closed_text'],
				$_POST['hours_disclaimer'],
				$_POST['ordering'],
				$_POST['showhide'],
				date("Y-m-d H:i:s"),
				
				//Update
				$_POST['location_name'], 
				$_POST['address'],
				$_POST['address2'],
				$_POST['city'],
				$_POST['province'],
				$_POST['postal_code'],
				$_POST['country'],
				$_POST['phone'],
				$_POST['fax'],
				$_POST['toll_free'],
				$_POST['email'],
				$_POST['google_place_id'],
				$_POST['google_map'],
				$_POST['gpslat'],
				$_POST['gpslong'],
				$_POST['zoom'],
				$_POST['show_hours'],
				$_POST['open_text'],
				$_POST['closing_text'],
				$_POST['closed_text'],
				$_POST['hours_disclaimer'],
				$_POST['ordering'],
				$_POST['showhide'],
				date("Y-m-d H:i:s")
			);			

			$db->new_transaction();

			$db->query("INSERT INTO $record_db($record_id, location_name, address, address2, city, province, postal_code, country, phone, fax, toll_free, email, google_place_id, google_map, gpslat, gpslong, zoom, show_hours, open_text, closing_text, closed_text, hours_disclaimer, ordering, showhide, last_modified) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE location_name=?, address=?, address2=?, city=?, province=?, postal_code=?, country=?, phone=?, fax=?, toll_free=?, email=?, google_place_id=?, google_map=?, gpslat=?, gpslong=?, zoom=?, show_hours=?, open_text=?, closing_text=?, closed_text=?, hours_disclaimer=?, ordering = ?, showhide=?, last_modified=?", $params);
			$item_id = (ITEM_ID == "" ? $db->insert_id() : ITEM_ID);

			//Update phone numbers
			foreach($location_numbers as $number){
				$params = array(
					$number['number_id'], 
					$item_id, 
					$number['phone'], 
					$number['tollfree'], 
					$number['hearingimpaired'], 
					$number['type'], 
					$number['phone'], 
					$number['tollfree'], 
					$number['hearingimpaired']
				);
				$db->query("INSERT INTO location_numbers(number_id, location_id, phone, tollfree, hearingimpaired, type) VALUES(?,?,?,?,?,?) ON DUPLICATE KEY UPDATE phone = ?, tollfree = ?, hearingimpaired = ?", $params);
			}

			//Update additional phones and emails
			$location_contact_ids = array_filter(array_values(array_column($location_contacts, 'contact_id')));
			$db->query("DELETE FROM location_contacts WHERE location_id = ?".(!empty($location_contact_ids) ? " AND contact_id NOT IN (".implode(",", $location_contact_ids).")" : ""), array($item_id));
			foreach($location_contacts as $contact){
				$params = array(
					$contact['contact_id'],
					$item_id,
					$contact['type'],
					$contact['label'],
					$contact['value'],
					date('Y-m-d H:i:s'),
					//update
					$contact['type'],
					$contact['label'],
					$contact['value'],
					date('Y-m-d H:i:s')
				);
				$db->query("INSERT INTO location_contacts (contact_id, location_id, type, label, value, last_updated) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE type = ?, label = ?, value = ?, last_updated = ?", $params);
			}

			//Update business hours
			foreach($location_hours as $i => $hours){
				$params = array(
					$hours['hours_id'], 
					$item_id, 
					$hours['day'], 
					$hours['start_time'], 
					$hours['end_time'], 
					$hours['closed'], 
					date("Y-m-d H:i:s"), 
					$hours['start_time'], 
					$hours['end_time'], 
					$hours['closed'],  
					date("Y-m-d H:i:s")
				);
				$db->query("INSERT INTO location_hours(hours_id, location_id, day, start_time, end_time, closed, last_updated) VALUES(?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE start_time = ?, end_time = ?, closed = ?, last_updated = ?", $params);
			}

			if(!$db->error()){
				$db->commit(); 
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			}else{
				$db->rollback(); 
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}
		}
	}
}

?>