<?php

//System files
include("../../config/config.php");
include ('../../config/database.php');
include('../../includes/functions.php');
include('../../includes/utils.php');

//Get vars
$occurrence_id = (isset($_POST['id']) ? $_POST['id'] : NULL);
$event = $Registration->get_occurrence($occurrence_id);
if(!empty($event)){
	
	//Already registered
	if($event['isregistered']){
		echo 'You are already registered for this event.';
		exit();
	}
	
	//Waiting list disabled
	if($event['waiting_list'] != '1'){
		echo 'Event waiting list is closed.';
		exit();
	}

	//Registration not open
	if(!$event['open'] || $event['started']){
		echo 'Event registration is closed.';
		exit();
	}
	
	//Ineligible gender
	if(!$event['isgender']){
		echo 'You are not eligible for this event.';
		exit();
	}
	
	//Tournaments
	if($event['event_type'] == 2){
		
		//Logged in
		if(!USER_LOGGED_IN){
			echo 'login';
			exit();
		}
		
		//Member eligibility
		if(!$event['eligible']){
			echo 'You are not eligible for this event.';
			exit();
		}
		
	}
	
	
	//Events...
	
	//...
	
	
	//Subscribe to waitlist
	try{
		$Registration->wait_list_subscribe($event['event_id'], $event['occurrence_id'], USER_LOGGED_IN);
		echo 'success';
	}catch(Exception $e){
		echo 'Unable to subscribe. '.$e->getMessage();
	}
	
}else{
	echo 'Event not found. Please try again.';
}

?>