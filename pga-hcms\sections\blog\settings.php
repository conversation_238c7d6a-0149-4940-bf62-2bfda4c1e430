<?php 

//Display form
echo '<form action="" method="post" enctype="multipart/form-data">';

	//General settings
	echo '<div class="panel">
		<div class="panel-header">'.$record_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		
		<div class="panel-content">
			<div class="flex-container">

				<div class="form-field">
					<label>Social Sharing ' .$CMSBuilder->tooltip('Social Sharing', 'Enable to display the social sharing toolbar on blog entries.'). '</label>
					<select name="social_sharing" class="select">
						<option value="1"' .(($row['social_sharing'] ?? -1) == 1 ? ' selected' : ''). '>Enabled</option>
						<option value="0"' .(($row['social_sharing'] ?? -1) == 0 ? ' selected' : ''). '>Disabled</option>
					</select>
				</div>';
				
				if($CMSBuilder->get_section_status($_cmssections['blog_comments']) == 'Enabled'){
					echo '<div class="form-field">
						<label>Blog Comments ' .$CMSBuilder->tooltip('Blog Comments', 'Enable the ability for users to comment on your blog entries.'). '</label>
						<select name="comments" class="select">
							<option value="1"' .(($row['comments'] ?? -1) == 1 ? ' selected' : ''). '>Enabled</option>
							<option value="0"' .(($row['comments'] ?? -1) == 0 ? ' selected' : ''). '>Disabled</option>
						</select>
					</div>

					<div class="form-field">
						<label>Comment Approval ' .$CMSBuilder->tooltip('Comment Approval', 'Enable to require approval of all comments before they are posted.'). '</label>
						<select name="comment_approval" class="select">
							<option value="1"' .(($row['comment_approval'] ?? -1) == 1 ? ' selected' : ''). '>Enabled</option>
							<option value="0"' .(($row['comment_approval'] ?? -1) == 0 ? ' selected' : ''). '>Disabled</option>
						</select>
					</div>';
				}

				if($CMSBuilder->get_section_status($_cmssections['blog_authors']) == 'Enabled'){
					echo '<div class="form-field">
						<label>Show Authors ' .$CMSBuilder->tooltip('Show Authors', 'Display the author for each blog entry and enable the ability to filter entries by author.'). '</label>
						<select name="show_author" class="select">
							<option value="1"' .(($row['show_author'] ?? -1) == 1 ? ' selected' : ''). '>Enabled</option>
							<option value="0"' .(($row['show_author'] ?? -1) == 0 ? ' selected' : ''). '>Disabled</option>
						</select>
					</div>';
				}

				if($CMSBuilder->get_section_status($_cmssections['blog_categories']) == 'Enabled'){
					echo '<div class="form-field">
						<label>Show Empty Categories ' .$CMSBuilder->tooltip('Show Empty Categories', 'Show empty categories and authors in the navigation bar.'). '</label>
						<select name="empty_categories" class="select">
							<option value="1"' .(($row['empty_categories'] ?? -1) == 1 ? ' selected' : ''). '>Enabled</option>
							<option value="0"' .(($row['empty_categories'] ?? -1) == 0 ? ' selected' : ''). '>Disabled</option>
						</select>
					</div>';
				}

			echo '</div>
		</div>
	</div>'; //General settings	
	
	//Sticky footer
	echo '<footer id="cms-footer">
		<div class="flex-container">
			<div class="flex-column right">
				<button type="submit" class="button" name="save" value="save"><i class="fas fa-check"></i>Save Changes</button>
			</div>
		</div>
	</footer>

	<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '"/>	
</form>';

?>