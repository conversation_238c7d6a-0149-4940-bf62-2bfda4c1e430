<?php

/*-----------------------------------/
* Deals with all functionality associated with SendGrid (transactional emails, marketing campaigns, and email event statisticss)
* <AUTHOR>
* @date		17-05-24
* @file		SendGrid.class.php
*/

class MassMail{
	
	/*-----------------------------------/
	* @var db
	* Mysqli database object
	*/
	public $db;
	
	/*-----------------------------------/
	* @var root
	* Global root variable
	*/
	private $root;
	
	/*-----------------------------------/
	* @var cpath
	* Global cpath variable
	*/
	private $cpath;
	
	/*-----------------------------------/
	* @var settings
	* Global settings variable
	*/
	private $settings;
	
	/*-----------------------------------/
	* @var template
	* HTML template to use for sending
	*/
	public $template;
	
	/*-----------------------------------/
	* @var stylesheet
	* CSS stylesheet to use for styling
	*/
	public $stylesheet;
	
	/*-----------------------------------/
	* @var sendgrid
	* Sendgrid Client object
	*/
	protected $sg;
	
	/*-----------------------------------/
	* @var mail
	* SendGrid mail object
	*/
	private $mail;
	
	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @param	$apiKey			Created in SendGrid account
	* @return	NULL
	* @throws	Exception
	*/
	public function __construct($apiKey){		
		
		//Set SendGrid object
		if(class_exists('SendGrid')){
			$this->sg = new \SendGrid($apiKey);
		}else{
			throw new Exception('Missing class file `SendGrid`');	
		}
	
		//Set database instance
		if(class_exists('Database')){
			$this->db = new Database();
		}else{
			throw new Exception('Missing class file `Database`');	
		}	
		
		//Set path variables (for templates)
		global $root, $cpath, $global;
		$this->root = &$root;
		$this->cpath = &$cpath;
		$this->settings = &$global;
		
		//default template/styles
		$this->set_template('includes/emailtemplates/template.htm');
		$this->set_stylesheet('theme/css/typography.css');
		
		//init mail object (not used until sending)
		$this->mail = null;
    }
    
    # ========================= #
	# ==== Email Functions ==== #
	# ========================= #
    	
	/*-----------------------------------/
	* Send email
	*
	* <AUTHOR> Army
	* @param	$recipients		Array (see function set_recipients for format)
	* @param	$subject		String
	* @param	$message		String (text/html)
	* @param	$category		String
	* @param	$uniqe_args		Array
	* @param	$attachments	Array (Expected Format: array(array('name'=>'file1.jpg','file'=>'pathto/file1.jpg','mime_type'=>'application/pdf')))
	* @return	boolean			True/False
	*/
	public function sendit($recipients = array(), $subject, $message, $category, $unique_args = array(), $attachments = array()){
		
		//set sendgrid attributes
		$this->mail = new \SendGrid\Mail();
		$from = new \SendGrid\Email($this->settings['company_name'], $this->settings['sendgrid_email']);
		$emailMessage = $this->prettify_message($message);
		$content = new \SendGrid\Content('text/html', $emailMessage);
		
		$this->mail->setFrom($from);
		$this->mail->setSubject($subject);
		$this->mail->addContent($content);
		$this->set_recipients($recipients);
		
		//tracking category
		if($category != ''){
			$this->mail->addCategory($category);
		}
		
		//tracking custom args
		if(!empty($unique_args)){
			foreach($unique_args as $arg => $val){
				$this->mail->addCustomArg($arg, (string)$val);
			}
		}
		
		//attachment(s)
		if(!empty($attachments)){
			foreach($attachments as $file){
				if(is_file($_SERVER['DOCUMENT_ROOT'].$this->root.$file['file'])){
					$attachment = new \SendGrid\Attachment();
				    $attachment->setContent(base64_encode(file_get_contents($_SERVER['DOCUMENT_ROOT'].$this->root.$file['file'])));
				    $attachment->setType($file['mime_type']);
				    $attachment->setFilename($file['name']);
				    $attachment->setDisposition('attachment');
				    $this->mail->addAttachment($attachment);
				}
			}
		}
				
		$response = $this->sg->client->mail()->send()->post($this->mail);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error sending mail: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Set recipients list for mailout
	*
	* <AUTHOR> Army
	* @param	$recipients		Array
	*			Expected Format: array(array("name"=>"","email"=>"<EMAIL>","substitutions"=>array("%tag%"=>"Value"),"unique_args"=>array("unique_id"=>"Value")));
	*/
	private function set_recipients($recipients = array()){
		if(!is_null($this->mail)){
			if(is_array($recipients) && !empty($recipients)){
				//remove any duplicates
				$recipient_arr = array();
				foreach ($recipients as $val) {
				    $recipient_arr[$val['email']] = $val;    
				}
				$recipients = array_values($recipient_arr);			
				
				foreach($recipients as $recipient){
					$personalization = new SendGrid\Personalization();
					$email = new SendGrid\Email((isset($recipient['name']) && $recipient['name'] != '' ? html_entity_decode($recipient['name']) : null), $recipient['email']);
					$personalization->addTo($email);
					if(isset($recipient['substitutions']) && !empty($recipient['substitutions'])){
						foreach($recipient['substitutions'] as $tag => $sub){
							$personalization->addSubstitution($tag, $sub);
						}
					}
					if(isset($recipient['unique_args']) && !empty($recipient['unique_args'])){
						foreach($recipient['unique_args'] as $arg => $val){
							$personalization->addCustomArg($arg, (string)$val);
						}
					}
					$this->mail->addPersonalization($personalization);
				}
			}else{
				throw new Exception('Invalid recipient format.');	
			}
		}else{
			throw new Exception('Mail object not available.');	
		}
	}
		
	/*-----------------------------------/
	* Replace global variables and apply site styles to email message
	*
	* <AUTHOR> Army
	* @param	$message		String
	* @return	string			Returns prettyfied message
	*/
	public function prettify_message($message){
		
		$address = array();
		if(!empty($this->settings['contact_address2'])) {
			$address[] = $this->settings['contact_address2'];
		}
		if(!empty($this->settings['contact_address'])) {
			$address[] = $this->settings['contact_address'];
		}
		if(!empty($this->settings['contact_city'])) {
			$address[] = $this->settings['contact_city'];
		}
		$address = (!empty($address) ? implode(', ', $address) : '');
		
		if(is_null($this->template)){
			$emailMessage = $message;
		} else {
			$emailMessage = file_get_contents($this->template);
			$emailMessage = str_replace('[EMAIL CONTENT]', $message, $emailMessage);
		}
		
		//replace common placeholder variables
		$emailMessage = str_replace('<a href="/', '<a href="[WEBSITE URL]', $emailMessage);
		$emailMessage = str_replace('[WEBSITE URL]', 'http://'.(str_replace('cms.', 'www.', $_SERVER['HTTP_HOST'])).$this->root, $emailMessage);
		$emailMessage = str_replace('[CLOUD URL]', $this->cpath, $emailMessage);
		$emailMessage = str_replace('[YEAR]',date('Y'),$emailMessage);
		$emailMessage = str_replace('[SLOGAN]', $this->settings['slogan'], $emailMessage);
		$emailMessage = str_replace('[COMPANY NAME]', $this->settings['company_name'], $emailMessage);
		$emailMessage = str_replace('[COMPANY ADDRESS]', $address, $emailMessage);
		$emailMessage = str_replace('[COMPANY PHONE]', $this->settings['contact_phone'], $emailMessage);
		$emailMessage = str_replace('[COMPANY FAX]', $this->settings['contact_fax'], $emailMessage);
		$emailMessage = str_replace('[COMPANY EMAIL]', $this->settings['contact_email'], $emailMessage);
			
		if(class_exists('Emogrifier')){
			//add styles
			$emogrifier = new Emogrifier($emailMessage, $this->stylesheet);
			$emailMessage = $emogrifier->emogrify();			
		}
		
		return $emailMessage;
	}
	
	/*-----------------------------------/
	* Set HTML email template
	*
	* <AUTHOR> Army
	* @param	$template		String (path to template file)
	*/
	public function set_template($template){
		if(is_file($_SERVER['DOCUMENT_ROOT'].$this->root.$template)){
			$this->template = $_SERVER['DOCUMENT_ROOT'].$this->root.$template;
		}else if($template === false){
			$this->template = NULL;
		}else{
			throw new Exception('Could not locate template file.');
		}
	}
	
	/*-----------------------------------/
	* Set CSS styles
	*
	* <AUTHOR> Army
	* @param	$template		String (path to template file)
	*/
	public function set_stylesheet($template){
		if(is_file($_SERVER['DOCUMENT_ROOT'].$this->root.$template)){
			$this->stylesheet = $_SERVER['DOCUMENT_ROOT'].$this->root.$template;
		}else{
			throw new Exception('Could not locate stylesheet file.');
		}
	}
	
	# ============================ #
	# ==== Campaign Functions ==== #
	# ============================ #

	/*-----------------------------------/
	* Create a new campaign
	*
	* <AUTHOR> Army
	* @param	$content		String (html)
	* @param	$title			String (max 100 characters)
	* @param	$unsub_url		String 
	* @param	$supression_id	Int (ID of the associated supression group) 
	* @param	$subject		String (max 100 characters)
	* @param	$categories		Array
	* @return	ID				The ID of the new campaign
	*/
	public function create_campaign($content, $title, $unsub_url, $supression_id=NULL, $subject='', $categories=array('Newsletter')){
						
		$campaign = array();
		
		//set content
		$content = $this->set_campaign_content($content);		
		$campaign['html_content'] = $content['html'];
		$campaign['plain_content'] = $content['text'];
		$campaign['title'] = $title;
		$campaign['subject'] = ($subject != '' ? $subject : $title);

		$campaign['sender_id'] = $this->settings['sendgrid_sender'];
		$campaign['suppression_group_id'] = $supression_id;
		$campaign['custom_unsubscribe_url'] = $unsub_url;
		$campaign['categories'] = (is_array($categories) ? $categories : "");
				
		$response = $this->sg->client->campaigns()->post($campaign);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['id'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error creating campaign: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Update an existing campaign
	*
	* <AUTHOR> Army
	* @param	$id				Int (ID of existing campaign)
	* @param	$content		String (html)
	* @param	$title			String (max 100 characters)
	* @param	$supression_id	Int (ID of the associated supression group) 
	* @param	$subject		String (max 100 characters)
	* @param	$categories		Array
	* @return	Boolean			True on success
	*/
	public function update_campaign($id, $content, $title, $supression_id=NULL, $subject='', $categories=array('Newsletter')){
		
		$campaign = array();

		//set content
		$content = $this->set_campaign_content($content);
		$campaign['html_content'] = $content['html'];
		$campaign['plain_content'] = $content['text'];
		$campaign['title'] = $title;
		$campaign['subject'] = ($subject != '' ? $subject : $title);
		$campaign['categories'] = (is_array($categories) ? $categories : "");
				
		$response = $this->sg->client->campaigns()->_($id)->patch($campaign);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);			
			throw new Exception('Error saving campaign: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Delete an existing campaign
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing campaign
	* @return	Boolean			True on success
	*/
	public function delete_campaign($id){		
		$response = $this->sg->client->campaigns()->_($id)->delete();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error deleting campaign: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Send a campaign
	*
	* <AUTHOR> Army
	* @param	Int $id				ID of existing campaign
	* @param	Int $supression_id	Selected suppression group ID to save
	* @param	Array $lists		Selected list IDs to send to
	* @return	Boolean				True on success
	*/
	public function send_campaign($id, $supression_id, $lists=array()){
				
		try{
			$this->set_campaign_lists($id, $supression_id, $lists);
		} catch(Exception $e){
			throw new Exception($e->getMessage());
		}
		
		$response = $this->sg->client->campaigns()->_($id)->schedules()->now()->post();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error sending campaign: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Send a test campaign to the provided emails
	*
	* <AUTHOR> Army
	* @param	$id				Int (ID of existing campaign)
	* @param	$to				String (comma separated emails)
	* @return	Boolean			True on success
	*/
	public function send_test_campaign($id, $to){
		
		//set emails
		$email_request = array("to"=>"");
		$email_arr = explode(",", $to);
		if(!empty($email_arr) && count($email_arr) > 1){
			$email_request["to"] = array();
			foreach($email_arr as $to_email){
				$email_request["to"][] = trim($to_email);
			}
		} else {
			$email_request["to"] = trim($to);
		}
		
		$response = $this->sg->client->campaigns()->_($id)->schedules()->test()->post($email_request);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error sending test campaign: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Set campaign content
	*
	* <AUTHOR> Army
	* @param	String $content		Unstyled HTML
	* @return	Array				Array of HTML and Plain text content
	*/
	public function set_campaign_content($content){		
		$response = array();
		$template_content = $this->prettify_message($content);
		$template_content = str_replace('-unsubscribe-','[unsubscribe]',$template_content);
		$plain_content = strip_tags($template_content);
		
		$response['html'] = $template_content;
		$response['text'] = $plain_content;
		return $response;
	}
	
	/*-----------------------------------/
	* Set campaign lists to send to
	*
	* <AUTHOR> Army
	* @param	Int $id				ID of existing campaign
	* @param	Int $supression_id	Selected suppression group ID to save
	* @param	Array $lists		Selected list IDs to save
	* @return	Boolean				True on success
	*/
	public function set_campaign_lists($id, $supression_id, $lists=array()){		
		$campaign = array();
		$campaign['list_ids'] = $lists;
		
		//substitute tags in campaign content
		$campaign_response = $this->sg->client->campaigns()->_($id)->get();
		$campaign_response = json_decode($campaign_response->body(),true);
		$campaign['html_content'] = str_replace('[NEWSLETTER TITLE]', $campaign_response['title'], $campaign_response['html_content']);
		$campaign['html_content'] = str_replace('[NEWSLETTER DATE]', date('F j, Y g:ia'), $campaign['html_content']);
		$campaign['plain_content'] = str_replace('[NEWSLETTER TITLE]', $campaign_response['title'], $campaign_response['plain_content']);
		$campaign['plain_content'] = str_replace('[NEWSLETTER DATE]', date('F j, Y g:ia'), $campaign['plain_content']);
		
		//set suppression group - uses default sendgrid unsubscribe page
		if(!empty($supression_id)){
			$campaign['custom_unsubscribe_url'] = "";
			$campaign['suppression_group_id'] = $supression_id;
		}
		
		$response = $this->sg->client->campaigns()->_($id)->patch($campaign);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error saving lists for campaign: '.$body_message['errors'][0]['message']);
		}
	}
	
	
	# =============================== #
	# ==== Contacts DB Functions ==== #
	# =============================== #
	
	/*-----------------------------------/
	* Create a list
	*
	* <AUTHOR> Army
	* @param	String $name	Name of the list (must be unique -- will throw error)	
	* @return	Int				ID of new list
	*/
	public function create_list($name){
		
		$request_body = array(
			"name" => $name
		);
		
		$response = $this->sg->client->contactdb()->lists()->post($request_body);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['id'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error creating list: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Update a list
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing list
	* @param	String $name	Name of the list	
	* @return	Boolean			True on success
	*/
	public function update_list($id, $name){
		
		$request_body = array(
			"name" => $name
		);
		
		$response = $this->sg->client->contactdb()->lists()->_($id)->patch($request_body);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error saving list: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve a given list
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing list
	* @return	Array			List of contact list details
	*/
	public function get_list($id){
		$response = $this->sg->client->contactdb()->lists()->_($id)->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving list: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve all lists
	*
	* <AUTHOR> Army
	* @return	Array			List of contact lists
	*/
	public function get_lists(){
		$response = $this->sg->client->contactdb()->lists()->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['lists'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving list: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve the contacts for a given list
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing list
	* @param	Int $page		Page index of first recipient to return (must be a positive integer)
	* @param	Int $page_size	Number of recipients to return at a time (must be a positive integer between 1 and 1000)
	* @return	Array			List of contacts
	*/
	public function get_list_recipients($id, $page=1, $page_size=1000){
		$query_params = array("page" => $page, "page_size" => $page_size);
		$response = $this->sg->client->contactdb()->lists()->_($id)->recipients()->get(null, $query_params);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['recipients'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving list contacts: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Delete a list
	*
	* <AUTHOR> Army
	* @param	Int $id						ID of existing list
	* @param	Boolean $delete_contacts	True to retroactively delete all contacts in the list; 
	*										False to keep contact in global list (just not attached to any list)
	* @return	Boolean						True on success
	*/
	public function delete_list($id, $delete){
		$query_params = array("delete_contacts" => $delete);
		$response = $this->sg->client->contactdb()->lists()->_($id)->delete(null, $query_params);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error deleting list: '.$body_message['errors'][0]['message']);
		}
	}
	
	
	/*-----------------------------------/
	* Adds recipients
	*
	* <AUTHOR> Army
	* @param	Array $recipients	List of new recipients
	* @return	Array				Any errors that occurred while adding contacts
	*/
	public function add_recipients($recipients){
		$response = $this->sg->client->contactdb()->recipients()->post($recipients);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error saving recipients: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Updates recipients
	*
	* <AUTHOR> Army
	* @param	Array $recipients	List of existing contacts to update
	* @return	Array				Any errors that occurred while adding contacts
	*/
	public function update_recipients($recipients){
		$response = $this->sg->client->contactdb()->recipients()->patch($recipients);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error saving recipients: '.$body_message['errors'][0]['message']);
		}
	}

	/*-----------------------------------/
	* Delete recipients
	*
	* <AUTHOR> Army
	* @param	Array $recipients	List of new recipients
	* @return	Array				Any errors that occurred while deleting contacts
	*/
	public function delete_recipients($recipients){
		$response = $this->sg->client->contactdb()->recipients()->delete($recipients);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error deleting recipients: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Adds recipients to a given list
	*
	* <AUTHOR> Army
	* @param	Int $id				ID of existing list
	* @param	Array $recipients	List of existing contacts to add
	* @return	Boolean				True on success
	*/
	public function add_list_recipients($id, $recipients){
		$response = $this->sg->client->contactdb()->lists()->_($id)->recipients()->post($recipients);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error saving recipients to list: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Removes a given recipient from a given list
	*
	* <AUTHOR> Army
	* @param	Int $list_id			ID of existing list
	* @param	String $recipient_id	ID of existing recipient
	* @return	Boolean					True on success
	*/
	public function remove_list_recipient($list_id, $recipient_id){
		$response = $this->sg->client->contactdb()->lists()->_($list_id)->recipients()->_($recipient_id)->delete();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error removing recipient from list: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve lists subscribed to by a given contact
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing contact
	* @return	Array			List of lists
	*/
	public function get_recipient_lists($id){
		$response = $this->sg->client->contactdb()->recipients()->_($id)->lists()->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['lists'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving subscribed lists: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Get a count of recipients across all lists
	*
	* <AUTHOR> Army
	* @return	Int				The total recipient count
	*/
	public function recipient_count(){
		$response = $this->sg->client->contactdb()->recipients()->count()->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return (isset($body_message['recipient_count']) ? $body_message['recipient_count'] : 0);
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving recipient count: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve recipeints that match search criteria
	*
	* <AUTHOR> Army
	* @param	Array $criteria		Format: array("field_name" => "search_string")
	* @return	Array				List of recipients that match
	*/
	public function recipient_search($criteria){
		$query_params = array("email"=>$criteria);
		$response = $this->sg->client->contactdb()->recipients()->search()->get(null, $query_params);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['recipients'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error searching for recipients: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Get a list pf all recipients
	*
	* <AUTHOR> Army
	* @param	Int $page		Page index of first recipient to return (must be a positive integer)
	* @param	Int $page_size	Number of recipients to return at a time (must be a positive integer between 1 and 1000)
	* @return	Array			List of recipients
	*/
	public function get_all_recipients($page=1, $page_size=1000){
		$query_params = array("page" => $page, "page_size" => $page_size);
		$response = $this->sg->client->contactdb()->recipients()->get(null, $query_params);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['recipients'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving recipients: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Subscribe email/contact to list
	*
	* <AUTHOR> Army
	* @param	Array $contacts		An array of contact information to add as new recipients (Format: array(array("first_name"=>"Test","last_name"=>"Person","email"=>"<EMAIL>")))
	* @param 	Int $list 			The ID of list to subscribe recipient to
	* @return	Boolean				True on success
	*/
	public function subscribe($contacts, $list){
		try{
			//create new recipient(s)
			$recipient_result = $this->add_recipients($contacts);			
			//get new contact ids
			$contact_ids = $recipient_result['persisted_recipients'];
			if(!empty($contact_ids)){
				//add new/existing recipients to list
				$this->add_list_recipients($list, $contact_ids);
			}else if(count($contacts) == 1){
				//look for existing contact
				$search_recipient = $this->recipient_search($contacts[0]['email']);
				if(!empty($search_recipient)){
					$this->add_list_recipients($list, array($search_recipient[0]['id']));
				}
			}
			return true;
		} catch(Exception $e) {
			throw new Exception($e->getMessage());
		}	
	}
	
	/*-----------------------------------/
	* Create a new custom field ** added in for Legacy Migration
	*
	* <AUTHOR> Army
	* @param	String $name	Name of the custom field
	* @param	String $type	Type accepted: date (MM/DD/YYYY), text, number
	* @return	Boolean			True on success
	*/
	public function create_custom_field($name,$type = "text"){
		$request_body = array(
			"name" => $name,
			"type" => $type
		);
		$response = $this->sg->client->contactdb()->custom_fields()->post($request_body);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error creating custom field: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Get either a Gravatar URL or complete image tag for a specified email address.
	*
	* @param 	String $email 	The email address
	* @param 	String $s 		Size in pixels, defaults to 80px [ 1 - 512 ]
	* @param 	String $d 		Default imageset to use [ 404 | mm | identicon | monsterid | wavatar ]
	* @param 	String $r 		Maximum rating (inclusive) [ g | pg | r | x ]
	* @param 	Boolean $img 	True to return a complete IMG tag False for just the URL
	* @param 	Array $atts 	Optional, additional key/value attributes to include in the IMG tag
	* @return 	String 			Containing either just a URL or a complete image tag
	* @source 	http://gravatar.com/site/implement/images/php/
	*/

	public function get_gravatar($email, $s = 80, $d = 'mm', $r = 'g', $img = false, $atts = array()) {
		$url = 'http://www.gravatar.com/avatar/';
		$url .= md5(strtolower(trim($email)));
		$url .= "?s=$s&d=$d&r=$r";
		if($img){
			$url = '<img src="'.$url.'"';	
			foreach($atts as $key => $val){
				$url .= ' '.$key.'="'.$val.'"';
				$url .= ' />';
			}
		}
		return $url;
	}
	
	
	# ======================= #
	# ==== ASM Functions ==== #
	# ======================= #
		
	/*-----------------------------------/
	* Create a new suppression group
	*
	* <AUTHOR> Army
	* @param	String $name		Name of the group (must be unique -- will throw error)
	* @param	String $description	A short description explaining the types of emails this group receives	
	* @return	Int					ID of new group
	*/
	public function create_suppression_group($name, $description){
		
		$request_body = array(
			"name" => $name,
			"description" => $description
		);
		
		$response = $this->sg->client->asm()->groups()->post($request_body);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['id'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error creating group: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Update a suppression group
	*
	* <AUTHOR> Army
	* @param	Int $id				ID of existing group
	* @param	String $name		Name of the group	
	* @param	String $description	A short description explaining the types of emails this group receives	
	* @return	Boolean				True on success
	*/
	public function update_suppression_group($id, $name, $description){
		
		$request_body = array(
			"name" => $name,
			"description" => $description
		);
		
		$response = $this->sg->client->asm()->groups()->_($id)->patch($request_body);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error saving group: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve a given suppression group
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing group
	* @return	Array			List of suppression group details
	*/
	public function get_suppression_group($id){
		$response = $this->sg->client->asm()->groups()->_($id)->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving group: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve all suppression groups
	*
	* <AUTHOR> Army
	* @return	Array			List of suppression groups
	*/
	public function get_suppression_groups(){
		$response = $this->sg->client->asm()->groups()->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving group: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Delete a suppression group
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing group
	* @return	Boolean			True on success
	*/
	public function delete_suppression_group($id){
		$response = $this->sg->client->asm()->groups()->_($id)->delete();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			return true;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error deleting group: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve the supressions for a given suppression group
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing group
	* @return	Array			List of email addresses belonging to suppression group
	*/
	public function get_group_suppressions($id){
		$response = $this->sg->client->asm()->groups()->_($id)->suppressions()->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving group suppressions: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve all suppressions
	*
	* <AUTHOR> Army
	* @return	Array			List of email addresses for each suppression group
	*/
	public function get_all_suppressions(){
		$response = $this->sg->client->asm()->suppressions()->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving suppressions: '.$body_message['errors'][0]['message']);
		}
	}
		
	/*-----------------------------------/
	* Retrieve all suppression groups a given email has unsubscribed from
	*
	* <AUTHOR> Army
	* @param	String $email	Email to search group suppressions for
	* @return	Array			List of suppression groups
	*/
	public function get_suppressions_by_email($email){
		$response = $this->sg->client->asm()->suppressions()->_($email)->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message['suppressions'];
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving suppressions: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieve all global suppressions
	*
	* <AUTHOR> Army
	* @return	Array			List of email addresses on the global unsubscribe list
	*/
	public function get_global_unsubscribes(){
		$response = $this->sg->client->suppression()->unsubscribes()->get();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving global unsubscribes: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Adds recipient to suppression group
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing suppression group
	* @param	Array $emails	List of emails to add
	* @return	Array			List of emails that were added
	*/
	public function unsubscribe($id, $emails){
		$request_body = array("recipient_emails"=>$emails);
		$response = $this->sg->client->asm()->groups()->_($id)->suppressions()->post($request_body);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error adding suppressions: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Adds recipient to global suppression group
	*
	* <AUTHOR> Army
	* @param	Array $emails	List of emails to add
	* @return	Array			List of emails that were added
	*/
	public function global_unsubscribe($emails){
		$request_body = array("recipient_emails"=>$emails);
		$response = $this->sg->client->asm()->suppressions()->global()->post($request_body);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error adding global suppressions: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Removes recipient from suppression group
	*
	* <AUTHOR> Army
	* @param	Int $id			ID of existing suppression group
	* @param	Array $email	Email to remove
	* @return	Array			List of emails that were removed
	*/
	public function resubscribe($id, $email){
		$response = $this->sg->client->asm()->groups()->_($id)->suppressions()->_($email)->delete();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error removing suppressions: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Removes recipient from global suppression group
	*
	* <AUTHOR> Army
	* @param	Array $email	Email to remove
	* @return	Array			List of emails that were removed
	*/
	public function global_resubscribe($email){
		$response = $this->sg->client->asm()->suppressions()->global()->_($email)->delete();
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error removing suppressions: '.$body_message['errors'][0]['message']);
		}
	}
	
	
	# ==================== #
	# ==== Statistics ==== #
	# ==================== #
	
	/*-----------------------------------/
	* Retrieves SendGrid usage data for a given time frame
	*
	* <AUTHOR> Army
	* @param	Date $start_date		Start date to get stats from
	* @param	Date $end_date			End date to get stats from
	* @param	String $aggregated_by	End date to get stats from
	* @return	Array					List of aggregated stat data
	*/
	public function get_stats($start_date = "", $end_date = "", $aggregated_by = "month"){
		$query_params = array(
			"aggregated_by" => $aggregated_by,
			"start_date" => $start_date,
			"end_date" => $end_date
		);
		$response = $this->sg->client->stats()->get(null, $query_params);
		if($response->statusCode() >= 200 && $response->statusCode() <= 299){
			$body_message = json_decode($response->body(),true);
			return $body_message;
		}else{
			$body_message = json_decode($response->body(),true);
			throw new Exception('Error retrieving SendGrid stats: '.$body_message['errors'][0]['message']);
		}
	}
	
	/*-----------------------------------/
	* Retrieves email statistics given a requested newsletter
	*
	* <AUTHOR> Army
	* @param	Int $id				ID of sent newsletter
	* @param	Date $day			Y-m-d for filtering results
	* @param	Boolean $massmail	True to retrieve transactional email stats vs newsletter stats
	* @return	Array				List of emails that were removed
	*/
	public function get_newsletter_stats($id, $day='', $massmail=false){
		$result = array();
		
		$params = array();
		$where = '';
		if ($id != ""){
			if($massmail){
				$where .= " WHERE `item_id` = ?";
				$params[] = $id;
			} else {
				$where .= " WHERE `newsletter_id` = ?";
				$params[] = $id;
			}
		}
		if ($day != ""){
			$where .= ($where != "" ? " AND" : " WHERE")." `date` LIKE ?";
			$params[] = "%".date("Y-m-d", $day)."%";
		}
		
		//basic event stats
		$query = $this->db->query("SELECT SUM(`event` = ?) AS `processed`, SUM(`event` = ?) AS `delivered`, SUM(`event` = ?) AS `opens`, SUM(`event` = ?) AS `clicks`, SUM(`event` = ?) AS `bounces`, SUM(`event` = ?) AS `complaints`, SUM(`event` LIKE ?) AS `unsubscribes` FROM `email_events`".$where,array_merge(array('processed', 'delivered', 'open', 'click', 'bounce', 'spamreport', '%unsubscribe%'),$params));
		if($query && !$this->db->error()) {
			$result = $this->db->fetch_array();
			$row = $result[0];
			foreach($row as $key => $value){
				$return[$key] = $value;
			}
		}
					
		//unique event stats
		$query = $this->db->query("SELECT COUNT(DISTINCT `email`) AS `total` FROM `email_events`".$where.($where != "" ? " AND `event` = ?" : " WHERE `event` = ?"), array_merge($params,array('open')));
		if($query && !$this->db->error()) {
			$result = $this->db->fetch_array();
			$return['uniqueOpens'] = $result[0]['total'];
		}
		
		$query = $this->db->query("SELECT COUNT(DISTINCT `email`) AS `total` FROM `email_events`".$where.($where != "" ? " AND `event` = ?" : " WHERE `event` = ?"), array_merge($params,array('click')));
		if($query && !$this->db->error()) {
			$result = $this->db->fetch_array();
			$return['uniqueClicks'] = $result[0]['total'];
		}
		
		//url stats
		$return['urls'] = array();
		$query = $this->db->query("SELECT `url`, COUNT(`event_id`) AS `total`, COUNT(DISTINCT `email`) AS `total_unique` FROM `email_events`".$where.($where != "" ? " AND `event` = ?" : " WHERE `event` = ?")." GROUP BY `url` ORDER BY `total` DESC", array_merge($params,array('click')));
		if($query && !$this->db->error()) {
			if($this->db->num_rows() > 0) {
				$result = $this->db->fetch_array();
				foreach($result as $row) {
					array_push($return['urls'], array('url'=>$row['url'], 'clicks'=>$row['total'], 'uniqueClicks'=>$row['total_unique']));
				}
			}
		}
	
		return $return;
	}

	
}

?>