@charset "utf-8";
/*
	animations.less

*/

/*---- animation classes ----*/
.animate{
	--animation-play-state: paused;
}

.animated{
	--animation-play-state: running;
}


/*---- fades ----*/
:root{--animation-trans-length: 40px;}
.keyframes(fade-in-ltr, {
	0%		{opacity: 0; .translateX(calc(-1 * var(--animation-trans-length))); }
	100% 	{opacity: 1; .translateX(0); }
});
.keyframes(fade-out-ltr, {
	0% 		{opacity: 1; .translateX(0); }
	100% 	{opacity: 0; .translateX(var(--animation-trans-length)); }
});
.keyframes(fade-in-rtl, {
	0% 		{opacity: 0; .translateX(var(--animation-trans-length)); }
	100% 	{opacity: 1; .translateX(0); }
});
.keyframes(fade-out-rtl, {
	0%		{opacity: 1; .translateX(0); }
	100% 	{opacity: 0; .translateX(calc(-1 * var(--animation-trans-length))); }
});
.keyframes(fade-in-ttb, {
	0%		{opacity: 0; .translateY(calc(-1 * var(--animation-trans-length))); }
	100% 	{opacity: 1; .translateY(0); }
});
.keyframes(fade-out-ttb, {
	0% 		{opacity: 1; .translateY(0); }
	100% 	{opacity: 0; .translateY(var(--animation-trans-length)); }
});
.keyframes(fade-in-btt, {
	0%		{opacity: 0; .translateY(var(--animation-trans-length)); }
	100% 	{opacity: 1; .translateY(0); }
});
.keyframes(fade-out-btt, {
	0% 		{opacity: 1; .translateY(0); }
	100% 	{opacity: 0; .translateY(calc(-1 * var(--animation-trans-length))); }
});
.keyframes(fade-in, {
	0% 		{opacity: 0; }
	100% 	{opacity: 1; }
});
.keyframes(fade-out, {
	0% 		{opacity: 1; }
	100% 	{opacity: 0; }
});


/*---- SVG path draw ----*/
.keyframes(draw, {
	0% 	 {stroke-dashoffset: var(--draw-length); } // must be set for each path
	100% {stroke-dashoffset: 0; }
});
.keyframes(draw-reverse, {
	0% 	 {stroke-dashoffset: var(--draw-length); }
	100% {stroke-dashoffset: calc(2px * var(--draw-length)); }
});


/*---- slides ----*/
.keyframes(slide-in-ltr, {
	0%		{.translateX(calc(-1 * var(--animation-trans-length))); }
	100% 	{.translateX(0); }
});
.keyframes(slide-out-ltr, {
	0% 		{.translateX(0); }
	100% 	{.translateX(var(--animation-trans-length)); }
});
.keyframes(slide-in-rtl, {
	0% 		{.translateX(var(--animation-trans-length)); }
	100% 	{.translateX(0); }
});
.keyframes(slide-out-rtl, {
	0%		{.translateX(0); }
	100% 	{.translateX(calc(-1 * var(--animation-trans-length))); }
});
.keyframes(slide-in-ttb, {
	0%		{.translateY(calc(-1 * var(--animation-trans-length))); }
	100% 	{.translateY(0); }
});
.keyframes(slide-out-ttb, {
	0% 		{.translateY(0); }
	100% 	{.translateY(var(--animation-trans-length)); }
});
.keyframes(slide-in-btt, {
	0%		{.translateY(var(--animation-trans-length)); }
	100% 	{.translateY(0); }
});
.keyframes(slide-out-btt, {
	0% 		{.translateY(0); }
	100% 	{.translateY(calc(-1 * var(--animation-trans-length))); }
});


/*---- daneden.github.io - animate.css ----*/
.keyframes(bounce, {
	0%,
	20%,
	53%,
	80%,
	100% 	{
				-webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
				animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
				.transform(translate3d(0, 0, 0));
			}

	40%,
	43% 	{
				-webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
				animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
				.transform(translate3d(0, -30px, 0));
			}

	70% 	{
				-webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
				animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
				.transform(translate3d(0, -15px, 0));
			}

	90% 	{.transform(translate3d(0, -4px, 0));}
});

.keyframes(rubber-band, {
	0% 		{background-clip: padding-box; .transform(scale3d(1, 1, 1));}
	30% 	{.transform(scale3d(1.25, 0.75, 1));}
	40% 	{.transform(scale3d(0.75, 1.25, 1));}
	50% 	{.transform(scale3d(1.15, 0.85, 1));}
	65% 	{.transform(scale3d(0.95, 1.05, 1));}
	75% 	{.transform(scale3d(1.05, 0.95, 1));}
	100% 	{.transform(scale3d(1, 1, 1));}
});

.keyframes(swing, {
	20% 	{.transform(rotate3d(0, 0, 1, 15deg));}
	40% 	{.transform(rotate3d(0, 0, 1, -10deg));}
	60% 	{.transform(rotate3d(0, 0, 1, 5deg));}
	80% 	{.transform(rotate3d(0, 0, 1, -5deg));}
	100% 	{.transform(rotate3d(0, 0, 1, 0deg));}
});

.keyframes(tada, {
	0% 		{.transform(scale3d(1, 1, 1));}

	10%,	
	20% 	{.transform(scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg));}

	30%,
	50%,
	70%,
	90% 	{.transform(scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg));}

	40%,
	60%,
	80% 	{.transform(scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg));}

	100% 	{.transform(scale3d(1, 1, 1));}
});


/*---- leadin animations ----*/
@media (prefers-reduced-motion: no-preference) {
	.leadin-popup{
		&.open{
			--animation-play-state: running;

			&.type-corner, &.type-bar.position-bottom{.animation(fade-up; 0.4s; ease-out);}	
			.button{
				&.bounce{.animation(bounce; 0.8s; ease-out; 0.8s);}
				&.rubber-band{.animation(rubber-band; 1s; ease-out; 0.8s);}
				&.swing{.animation(swing; 0.6s; ease-out; 0.8s);}
				&.tada{.animation(tada; 1s; ease-out; 0.8s);}
			}
		}
	}
}

/*---- landing page animations ----*/
@media @tablet-l and (prefers-reduced-motion: no-preference) {
	html.no-touch{
		body.landing-page{
			.landing-form-wrapper{
				.animation(fade-in; 0.4s; ease-out; 0.5s);
			}
		}
	}
}
