<?php

//Withdrawal policy
if(!$confirm){

	$html = '<form name="withdrawal-form" action="" method="post">';
		$html .= $page['page_panels'][$panel_id]['content'].'<br /><hr />';

		$html .= '<div class="form-buttons">
			<button type="submit" name="continue" class="button solid f_right primary red" value="1">I Accept<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
			<a href="' .$_sitepages['my-registrations']['page_url']. '?action=edit&id=' .$registration['registration_id']. '" class="previous f_right button primary black">Cancel<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
		</div>';

		$html .= '<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
	</form>';


//Confirmation
}else{

	$page['page_panels'][$panel_id]['title'] = 'Review &amp; Submit';
	$page['page_panels'][$panel_id]['show_title'] = true;

	$html = '<form name="withdrawal-form" action="" method="post">';

		//Attendee
		$html .= '<h4>Withdraw: ' .$registration['first_name'].' '.$registration['last_name']. '</h4>';

		//Tournament
		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive nomargin withdraw-table">
			<tr>
				<th class="left">Tournament</th>
				<th class="right">Total Price</th>
			</tr>
			<tr>
				<td>' .$registration['event_name']. '<br />
					<small class="dblock">' .format_date_range($registration['start_date'], $registration['end_date']). '</small>
				</td>
				<td class="right">$' .number_format($subtotal+$taxes+$fees, 2). '</td>
			</tr>
		</table>';


		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive withdraw-table">
			<tr>
				<td align="right">Refund Amount:</td>
				<td class="right" width="120px">$' .number_format($attendee_paid-$attendee_refunded, 2). '</td>
			</tr>
			<tr>
				<td align="right">Withdrawal Penalty:</td>
				<td class="right">$' .number_format($withdrawal_fee, 2). '</td>
			</tr>';
			if($balance_owing > 0){
				$html .= '<tr>
					<td align="right"><h6>Balance Due:</h6></td>
					<td class="right"><h6>$' .number_format($balance_owing, 2). '</h6></td>
				</tr>';
			}else{
				$html .= '<tr>
					<td align="right"><h6>Refund Due:</h6></td>
					<td class="right"><h6>$' .number_format($refund_owing, 2). '</h6></td>
				</tr>';
			}
		$html .= '</table>';

		//Submit or cancel
		$html .= '<div class="form-buttons right">';

			//Penalty notice
			if($balance_owing > 0){
				$html .= '<p><small>Note: Balance due will be invoiced separately.</small></p>';
			}

			$html .= '<button type="submit" name="submit" class="button solid f_right primary red" value="1">Submit Withdrawal<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
			<a href="' .$_sitepages['my-registrations']['page_url']. '?action=edit&id=' .$registration['registration_id']. '" class="previous f_right button primary black">Cancel<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
		</div>';

		$html .= '<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
	</form>';

}


// //Set panel content
// $page['page_panels'][$panel_id]['content'] = $html;

?>