/* Tablesorter Custom Metro LESS Theme by <PERSON>

To create your own theme, modify the code below and run it through
a LESS compiler, like this one: http://leafo.net/lessphp/editor.html
or download less.js from http://lesscss.org/

Test out these custom less files live
 Basic Theme : http://codepen.io/Mottie/pen/eqBbn
 Bootstrap   : http://codepen.io/Mottie/pen/Ltzpi
 Metro Style : http://codepen.io/Mottie/pen/gCslk

*/
/*** theme ***/
/*** fonts ***/
/*** color definitions ***/
/* for best results, only change the hue (120),
   leave the saturation (60%) and luminosity (75%) alone
   pick the color from here: http://hslpicker.com/#825a2b

  Inspired by http://www.jtable.org/ metro themes:
    Blue:        hsl(212, 86%, 35%)
    Brown        hsl(32, 50%, 30%)
    Crimson      hsl(0, 100%, 38%)
    Dark Grey    hsl(0, 0%, 27%)
    Dark Orange  hsl(13, 70%, 51%)
    Green        hsl(120, 100%, 32%)
    Light Gray   hsl(0, 0%, 44%)
    Pink         hsl(297, 100%, 33%)
    Purple       hsl(257, 51%, 48%)
    Red          hsl(5, 100%, 40%)

 */
/* it might be best to match the document body background color here */
/* ajax error message (added to thead) */
/* becomes height using padding (so it's divided by 2) */
/* 20px should be slightly wider than the icon width to avoid overlap */
/* url(icons/loading.gif); */
/* zebra striping */
.allRows {
  background-color: #fff;
  color: #000;
}
.evenRows {
  background-color: #ffffff;
  color: #000;
}
.oddRows {
  background-color: #cccccc;
}
/* hovered rows */
.oddHovered {
  background-color: #b3b3b3;
  color: #000;
}
.evenHovered {
  background-color: #999999;
  color: #000;
}
/* Columns widget */
/* Filter widget transition */
.filterWidgetTransition {
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
/*** Arrows ***/
/* black */
/* white */
/* automatically choose the correct arrow/text color */
/* variable theme name - requires less.js 1.3+;
   or just replace (!".@{theme}") with the contents of @theme
*/
.tablesorter-metro {
  font: 14px 'Segoe UI Semilight', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  background-color: #cdcdcd;
  margin: 10px 0 15px;
  width: 100%;
  text-align: left;
  border-spacing: 0;
  border: 0;
  /* style th's outside of the thead */
  /* style header */
  /* tfoot */
  /* optional disabled input styling */
  /* body */
  /* hovered row colors
	you'll need to add additional lines for
	rows with more than 2 child rows
	*/
  /* table processing indicator - indeterminate spinner */
  /* pager */
  /* Column Widget - column sort colors */
  /* caption (non-theme matching) */
  /* filter widget */
  /* hidden filter row */
  /* rows hidden by filtering (needed for child rows) */
  /* ajax error row */
}
.tablesorter-metro th,
.tablesorter-metro td {
  border: 0;
}
.tablesorter-metro th,
.tablesorter-metro thead td {
  font: 14px 'Segoe UI Semilight', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  background-color: #734f26;
  color: #eee;
  color: #ffffff;
  border-collapse: collapse;
  padding: 4px;
}
.tablesorter-metro .dark-row th,
.tablesorter-metro .dark-row td,
.tablesorter-metro caption.dark-row {
  background-color: #4d3519;
}
.tablesorter-metro tbody td,
.tablesorter-metro tfoot th,
.tablesorter-metro tfoot td {
  padding: 4px;
  vertical-align: top;
}
.tablesorter-metro .tablesorter-header {
  background-image: url(data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAQBAMAAADQT4M0AAAAElBMVEUAAADu7u7u7u7u7u7u7u7u7u7yb344AAAABnRSTlMAMhIHKyAHBrhHAAAATElEQVQI12NgYGBSYAABQ2Ew5SgCIlkFBQOAlKKgoBADA7MgEBgwsIAoB4ZAECXKAAFQHkg9WIejoCBIv4mgoDOQYgZpAxkDNARqEQBTkAYuMZEHPgAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
  background-position: right 5px center;
  cursor: pointer;
  white-space: normal;
}
.tablesorter-metro .tablesorter-header-inner {
  padding: 4px 20px 4px 4px;
}
.tablesorter-metro .tablesorter-header.sorter-false {
  background-image: none;
  cursor: default;
  padding: 4px;
}
.tablesorter-metro .tablesorter-headerAsc {
  background-image: url(data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAQBAMAAADQT4M0AAAAHlBMVEUAAADu7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u4+jEeEAAAACXRSTlMAMwkqFV7roCD4hW+/AAAAWUlEQVQI1y3MrQ5AABSG4Xd+Rj0jiDabjKZxB6qqaarGNRh27tY5myd8b/uAeML1l2+wPqUlUd0ss+oNoZqG2rOwe15+p5iC1HNAK5IBlUjnZyIlZsxx0QAfzokSZgp96u4AAAAASUVORK5CYII=);
}
.tablesorter-metro .tablesorter-headerDesc {
  background-image: url(data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAQBAMAAADQT4M0AAAAJ1BMVEUAAADu7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u7u4RJgHSAAAADHRSTlMAMiweCQITaU7olrlu2HdvAAAAXElEQVQI12NgYGBLYAABRxEQyaooFACkmAUFDYBUoqCgGAMDiyAQODAEgShVBkMQJcwABWvOAMEphmgQtZWBZc6ZMycdGBhszpw5DJRkOnNGAaSo5wRYLXsBAwMAi4YWQHRX4F0AAAAASUVORK5CYII=);
}
.tablesorter-metro tfoot .tablesorter-headerAsc,
.tablesorter-metro tfoot .tablesorter-headerDesc {
  /* remove sort arrows from footer */
  background-image: none;
}
.tablesorter-metro .disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: not-allowed;
}
.tablesorter-metro tbody {
  /* Zebra Widget - row alternating colors */
}
.tablesorter-metro tbody td {
  background-color: #fff;
  color: #000;
  padding: 4px;
  vertical-align: top;
}
.tablesorter-metro tbody tr.odd > td {
  background-color: #cccccc;
}
.tablesorter-metro tbody tr.even > td {
  background-color: #ffffff;
  color: #000;
}
.tablesorter-metro tbody > tr.hover > td,
.tablesorter-metro tbody > tr:hover > td,
.tablesorter-metro tbody > tr:hover + tr.tablesorter-childRow > td,
.tablesorter-metro tbody > tr:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td,
.tablesorter-metro tbody > tr.even.hover > td,
.tablesorter-metro tbody > tr.even:hover > td,
.tablesorter-metro tbody > tr.even:hover + tr.tablesorter-childRow > td,
.tablesorter-metro tbody > tr.even:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td {
  background-color: #999999;
  color: #000;
}
.tablesorter-metro tbody > tr.odd.hover > td,
.tablesorter-metro tbody > tr.odd:hover > td,
.tablesorter-metro tbody > tr.odd:hover + tr.tablesorter-childRow > td,
.tablesorter-metro tbody > tr.odd:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td {
  background-color: #b3b3b3;
  color: #000;
}
.tablesorter-metro .tablesorter-processing {
  background-image: url('data:image/gif;base64,R0lGODlhEAAQAPIAAP///1VVVdbW1oCAgFVVVZaWlqurq7a2tiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA==');
  background-position: center center;
  background-repeat: no-repeat;
}
.tablesorter-metro div.tablesorter-pager button {
  background-color: #8e612f;
  color: #eee;
  border: #ac7739 1px solid;
  cursor: pointer;
}
.tablesorter-metro div.tablesorter-pager button:hover {
  background-color: #ac7739;
}
.tablesorter-metro tr.odd td.primary {
  background-color: #d9c28c;
}
.tablesorter-metro td.primary,
.tablesorter-metro tr.even td.primary {
  background-color: #e3d2ab;
}
.tablesorter-metro tr.odd td.secondary {
  background-color: #e3d2ab;
}
.tablesorter-metro td.secondary,
.tablesorter-metro tr.even td.secondary {
  background-color: #ede2c9;
}
.tablesorter-metro tr.odd td.tertiary {
  background-color: #ede2c9;
}
.tablesorter-metro td.tertiary,
.tablesorter-metro tr.even td.tertiary {
  background-color: #f7f3e8;
}
.tablesorter-metro caption {
  background-color: #fff;
}
.tablesorter-metro .tablesorter-filter-row input,
.tablesorter-metro .tablesorter-filter-row select {
  width: 98%;
  height: auto;
  margin: 0;
  padding: 4px;
  color: #333;
  background-color: #fff;
  border: 1px solid #bbb;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
.tablesorter-metro .tablesorter-filter-row {
  background-color: #eee;
}
.tablesorter-metro .tablesorter-filter-row td {
  background-color: #eee;
  line-height: normal;
  text-align: center;
  /* center the input */
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
.tablesorter-metro .tablesorter-filter-row.hideme td {
  padding: 2px;
  margin: 0;
  line-height: 0;
  cursor: pointer;
}
.tablesorter-metro .tablesorter-filter-row.hideme * {
  height: 1px;
  min-height: 0;
  border: 0;
  padding: 0;
  margin: 0;
  /* don't use visibility: hidden because it disables tabbing */
  opacity: 0;
  filter: alpha(opacity=0);
}
.tablesorter-metro .filtered {
  display: none;
}
.tablesorter-metro .tablesorter-errorRow td {
  text-align: center;
  cursor: pointer;
  background-color: #e6bf99;
}
