<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN) {
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']) {
	$total_records = $db->get_record_count('slideshow');
	$CMSBuilder->set_widget($_cmssections['slideshow'], 'Total Slides', $total_records);
}

if(SECTION_ID == $_cmssections['slideshow']) {

	//Define vars
	$record_db   = 'slideshow';
	$record_id   = 'slide_id';
	$record_name = 'Slide';
	
	//Validation
	$errors   = false;
	$required = [];
	$required_fields = ['title'];

	//Image/Video Uploading
	$videodir     = "../videos/";
	$video_fields = ['video_mp4', 'video_webm', 'video_ogg'];
	$imagedir     = '../images/slideshow/';
	$CMSUploader  = new CMSUploader('slideshow', $imagedir);

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.title",
		"$record_db.content",
		"$record_db.url_text",
		"$record_db.url_text2"
	];

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get Records
	$db->query("SELECT * FROM $record_db $where ORDER BY ordering", $params);
	$records_arr = $db->fetch_assoc($record_id);
	foreach ($records_arr as $item_id => &$record) {
		$record['image'] = check_file($record['image'], $imagedir);

		foreach($video_fields as $fieldname){
			$record[$fieldname] = check_file($record[$fieldname], $videodir);
		}

		unset($record);
	}

	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}

	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){
			$row = $records_arr[ITEM_ID];
			
		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();	
		}
	}
	

	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);

		//Delete media
		if(!$db->error()){
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);

			//Delete video files
			foreach($video_fields as $fieldname){
				if($records_arr[ITEM_ID][$fieldname]){
					unlink($videodir.$records_arr[ITEM_ID][$fieldname]);
				}
			}

			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}
		
		header("Location: " .PAGE_URL);
		exit();
		

	//Save item
	}else if(isset($_POST['save'])){
		
		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);

		//Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Validate image
		if(empty($records_arr[ITEM_ID]['image']) && empty($_FILES['image']['name'])){
			$errors[] = 'Please select an image to upload.';
			$required[] = 'image';
		}else if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large. Cannot exceed '.$_max_filesize['megabytes'].'.';
			$required[] = 'image';
		}

		//Validate videos
		if(empty($records_arr[ITEM_ID]['video_mp4']) && !empty($_FILES['video_mp4']['name'])){
			$ext = pathinfo($_FILES['video_mp4']['name'], PATHINFO_EXTENSION);
			if(strtolower($ext) != 'mp4'){
				$errors[] = 'Video file (mp4) is not valid.';
			}else if($_FILES['video_mp4']['size'] > 10240000){
				$errors[] = 'Video (mp4) size is too large. Must be smaller than 10MB.';
			}
		}
		if(empty($records_arr[ITEM_ID]['video_webm']) && !empty($_FILES['video_webm']['name'])) {
			$ext = pathinfo($_FILES['video_webm']['name'], PATHINFO_EXTENSION);
			if(strtolower($ext) != 'webm') {
				$errors[] = 'Video file (webm) is not valid.';
			}else if($_FILES['video_webm']['size'] > 10240000) {
				$errors[] = 'Video (webm) size is too large. Must be smaller than 10MB.';
			}
		}
		if(empty($records_arr[ITEM_ID]['video_ogg']) && !empty($_FILES['video_ogg']['name'])) {
			$ext = pathinfo($_FILES['video_ogg']['name'], PATHINFO_EXTENSION);
			if(strtolower($ext) != 'ogg' && strtolower($ext) != 'ogv'){
				$errors[] = 'Video file (ogv) is not valid.';
			}else if($_FILES['video_ogg']['size'] > 10240000){
				$errors[] = 'Video (ogv) size is too large. Must be smaller than 10MB.';
			}
		}

		if(!$errors){

			//Delete old images
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}
			
			//Upload new images
			try{
				$images = $CMSUploader->bulk_upload($_POST['title'], $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			//Upload videos
			foreach($video_fields as $fieldname){
				$$fieldname = $records_arr[ITEM_ID][$fieldname] ?? NULL ?: NULL;

				//Delete old videos
				if(isset($_POST['deletevideo'])){
					if($$fieldname) {
						unlink($videodir.$$fieldname);
						$$fieldname = NULL;
					}

				//Upload new videos
				}else if(!empty($_FILES[$fieldname]['name'])){
					$ext = pathinfo($_FILES[$fieldname]['name'], PATHINFO_EXTENSION);
					$newname = date("ymdhis").'.'.$ext;
					copy($_FILES[$fieldname]['tmp_name'], $videodir.$newname);

					if(file_exists($videodir.$newname)){
						$$fieldname = $newname;

						//Remove old video
						if(!empty($records_arr[ITEM_ID][$fieldname])){
							unlink($videodir.$records_arr[ITEM_ID][$fieldname]);
						}
					}
				}
			}

			//Insert to db
			$params = array(
				
				//Insert
				ITEM_ID, 
				$_POST['title'], 
				$_POST['content'], 
				$_POST['theme'] ?: NULL, 
				$images['image'] ?? NULL, 
				$_POST['image_alt'],
				$video_mp4,
				$video_webm,
				$video_ogg,
				$_POST['url'], 
				$_POST['url_target'], 
				$_POST['url_text'], 
				$_POST['url2'], 
				$_POST['url_target2'], 
				$_POST['url_text2'], 
				$_POST['ordering'], 
				$_POST['showhide'], 

				//Update
				$_POST['title'], 
				$_POST['content'], 
				$_POST['theme'] ?: NULL, 
				$images['image'] ?? NULL, 
				$_POST['image_alt'], 
				$video_mp4,
				$video_webm,
				$video_ogg,
				$_POST['url'], 
				$_POST['url_target'], 
				$_POST['url_text'], 
				$_POST['url2'], 
				$_POST['url_target2'], 
				$_POST['url_text2'], 
				$_POST['ordering'], 
				$_POST['showhide']
			);
			$db->query("INSERT INTO `$record_db` (`$record_id`, `title`, `content`, `theme`, `image`, `image_alt`, `video_mp4`, `video_webm`, `video_ogg`, `url`, `url_target`, `url_text`, `url2`, `url_target2`, `url_text2`, `ordering`, `showhide`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `title` = ?, `content` = ?, `theme` = ?, `image` = ?, `image_alt` = ?, `video_mp4` = ?, `video_webm` = ?, `video_ogg` = ?, `url` = ?, `url_target` = ?, `url_text` = ?, `url2` = ?, `url_target2` = ?, `url_text2` = ?, `ordering` = ?, `showhide` = ?", $params);
			if(!$db->error()){
				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}
			}else{
				$errors[] = 'Unable to update record';
			}
		}

		if($errors){
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}

	//Handle images
	}else{
		include('modules/CropImages.php');
	}
}

?>