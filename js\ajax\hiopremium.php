<?php

//System files
include("../../config/config.php");
include('../../config/database.php');

//Get premium
$prizes = explode(';', $_POST['prizes']);
$premium = 0;

//Get rates
$rates = array();
$query = $db->query("SELECT * FROM `hio_rates` ORDER BY `prize_total` ASC");
if($query && !$db->error() && $db->fetch_array()){
	$rates = $db->fetch_array();
}

foreach($prizes as $prize){
	if(is_numeric($prize) && $prize >= 500){
		
		//Get premiums
		foreach($rates as $index=>$rate){
			
			//Low or high rate?
			$premium_rate = $rate['premium'];
			if($_POST['field'] >= 100){
				$premium_rate = $rate['premium_100'];
			}

			//Exact amount
			if(number_format($prize, 2, '.', '') == $rate['prize_total']){
				$premium += $premium_rate;
				break;
			}

			//Calculate amount
			if($rate['prize_total'] > $prize){
				
				$upper_prize = $rate['prize_total'];
				$lower_prize = (isset($rates[($index-1)]['prize_total']) ? $rates[($index-1)]['prize_total'] : 0);
				
				$upper_premium = $premium_rate;	
				$lower_premium = (isset($rates[($index-1)]['premium']) ? $rates[($index-1)]['premium'] : 0);
				if($_POST['field'] >= 100){
					$lower_premium = (isset($rates[($index-1)]['premium_100']) ? $rates[($index-1)]['premium_100'] : 0);					
				}
				
				$prize_difference = ($upper_prize-$lower_prize);
				$premium_difference = ($upper_premium-$lower_premium);
				
				$prize_increase = ($prize-$lower_prize);
				$percent_increase = ($prize_increase/$prize_difference);
				
				$premium += number_format($lower_premium+($premium_difference*$percent_increase), 2, '.', '');
				break;
			}

		}
	}
}

echo $premium;

?>