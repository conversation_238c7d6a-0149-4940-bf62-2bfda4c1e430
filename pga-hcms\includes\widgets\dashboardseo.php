<?php if($cms_settings['enhanced_seo'] && (MASTER_USER || SEO_USER)){ //SEO and master permissions 
	
	//set all pages that are in the red
	$Analyzer->set_problem_pages();
	//dynamic item pages (EXAMPLE)
	//$Analyzer->set_dynamic_problem_pages("case_studies","title","case-studies","case_id");
	
	//retrive sorted pages
	$problem_pages = $Analyzer->get_problem_pages();
		
	if(!empty($problem_pages)){
	
?>

<div class="cms-overview panel flex-column">
	<div class="panel-header">SEO Pages <?php echo $CMSBuilder->tooltip('SEO Pages', 'The pages below have scored low for SEO and require your attention.'); ?></div>
	<div class="panel-content clearfix nopadding">
        <table cellpadding="0" cellspacing="0" border="0">
        <?php
	    $page_count = 0;
        foreach($problem_pages as $seo_page){
	        if($page_count < 10){
		        echo '<tr>
					<td height="30px"><a href="'.$path.(isset($seo_page['section']) ? $seo_page['section'] : 'pages/content').'/?action=edit&item_id=' .(isset($seo_page['item_id']) ? $seo_page['item_id'] : $seo_page['page_id']). '"><i class="fas fa-edit color-theme1"></i> &nbsp; ' .$seo_page['page_title']. '</a></td>
					<td align="right"><span class="seo-fail">' .$seo_page['seo_score']. '</span></td>
				</tr>';
				$page_count++;
	        }
        }
        ?>
        </table>
    </div>
</div>

<?php }
}//seo permissions ?>