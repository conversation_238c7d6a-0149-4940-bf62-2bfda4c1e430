<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){
	echo "<form id='search-form' action='' method='get' class='multiple-search f_left'>";
		echo "<select name='category_id' class='select f_left'>";
			echo "<option value=''>- Select Folder -</option>";
			if(!empty($categories)) {
				foreach($categories as $category){
				echo "<option value='".$category['category_id']."' ".(!empty($category_id) && $category_id == $category['category_id'] ? "selected" : "").">".$category['name']."</option>";
				}
			}
		echo "</select>";
		echo "<div class='relative f_left'>
			<input type='text' name='search' class='input f_left' value='" .$searchterm. "' placeholder='Search' />";
			if($searchterm != ''){ 
				echo "<a id='clear-search'><i class='fa fa-times-circle'></i></a>";
			}
			echo "<button type='button' class='button' onclick='this.form.submit();'><i class='fa fa-search'></i></button>
		</div>
	</form>
	<form id='clear-search-form' name='clear-search-form' class='hidden' action='" .PAGE_URL. "' method='post'>
		<input type='hidden' name='clear-search' value='Clear' />
		<input type='hidden' name='search' value='' />
		<input type='hidden' name='xssid' value='" .$_COOKIE['xssid']. "' />
	</form>";

	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-upload'></i>Upload File</a></p>";

	echo "<div class='panel'>";
		echo "<div class='panel-header'>Files
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

			echo "<thead>";
			echo "<th width='300px'>File Name</th>";		
			echo "<th width='300px'>Folder</th>";
			echo "<th width='300px'>Posted By</th>";
			echo "<th width='150px' class=''>Date Posted</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "<th width='15px' class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";

			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
				echo "<td><i class='fa " .($row['file_type'] != "" ? "fa-file-".$row['file_type']."-o" : "fa-file-o"). "'></i>&nbsp; " .$row['file_name']. "</td>";	
				echo "<td>" .$row['category_name']. "</td>";
				echo "<td>" .$row['first_name']. " " .$row['last_name']. "</td>";
				echo "<td>" .date("F j, Y", strtotime($row['date_added'])). "</td>";
				echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "<td class='right'><a href='".$root."download.php?file=".$row['file_location']."&dir=resources' target='_blank'><i class='fa fa-download'></i></a></td>";
				echo "</tr>";
			}
			echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager(50);

		echo "</div>";
	echo "</div>";


//Image cropping
}else if(count($cropimages) > 0){
	include("includes/jcropimages.php");

//Display form	
}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$file = $data['file_location'];
		$ext = pathinfo($filedir.$row['file_location'], PATHINFO_EXTENSION);
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		$file = '';
		unset($row);
		$row['category_id'] = (!empty($category_id) ? $category_id : NULL);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

	//Details
	echo "<div class='panel'>";
		echo "<div class='panel-header'>File Details
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";

		if(ACTION == 'edit'){
			echo "<div class='panel-content nopadding'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
				<tr>
				<td width='100px'>File Name: <span class='required'>*</span></td>
				<td width='250px'><input type='text' name='file_name' value='" .(isset($row['file_name']) ? $row['file_name'] : ''). "' class='input nomargin' /></td>
				<td width='50px' class='nopadding right'>Folder:</td>
				<td>";
				if(isset($categories) && !empty($categories)){
				echo "<select name='category_id' class='select nomargin" .(in_array('category_id', $required) ? ' required' : ''). "'>
				<option value=''".(!isset($row) ? ' selected' : '').">- None -</option>";
				foreach($categories as $category){
				echo "<option value='" .$category['category_id']. "'" .($row['category_id'] == $category['category_id'] ? " selected" : ""). ">" .$category['name']. "</option>";	
				}
				echo "</select>";
				}else{
				echo "<a href='" .$categorypage['page_url']. "?action=add' class='button-sm'><i class='fa fa-plus'></i>Create Folder</a>";
				echo "<input type='hidden' name='category_id' value='' />";
				}
				echo "</td>
				</tr>
				<tr>
				<td>Posted By:</td>
				<td colspan='3'>" .$row['first_name']. " " .$row['last_name']. "</td>
				</tr>
				<tr>
				<td>Date Posted:</td>
				<td colspan='3'>" .date("F j, Y", strtotime($row['date_added'])). "</td>
				</tr>
				<tr>
				<td>File Type:</td>
				<td colspan='3'>" .strtoupper($ext)." ".(in_array($ext, $imagetypes) ? 'Image' : 'Document'). "</td>
				</tr>
				<tr>
				<td>File Size:</td>
				<td colspan='3'>" .$row['file_size']. "</td>
				</tr>
				<tr>
				<td>File:</td>
				<td colspan='3'><a href='".$root."download.php?file=".$row['file_location']."&dir=resources' target='_blank'><i class='fa fa-download'></i> Download</a></td>
				</tr>
				</table>";
			echo "</div>";
		}else{
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
				<label>File Name <span class='required'></span></label>
				<input type='text' name='file_name' value='" .(isset($row['file_name']) ? $row['file_name'] : ''). "' class='input' />
				</div>";
				echo "<div class='form-field'>
				<label>Folder</label>";
				if(isset($categories) && !empty($categories)){
				echo "<select name='category_id' class='select" .(in_array('category_id', $required) ? ' required' : ''). "'>
				<option value=''".(!isset($row) ? ' selected' : '').">- None -</option>";
				foreach($categories as $category){
				echo "<option value='" .$category['category_id']. "'" .($row['category_id'] == $category['category_id'] ? " selected" : ""). ">" .$category['name']. "</option>";	
				}
				echo "</select>";
				}else{
				echo "<a href='" .$categorypage['page_url']. "?action=add' class='button-sm'><i class='fa fa-plus'></i>Create Folder</a>";
				echo "<input type='hidden' name='category_id' value='' />";
				}
				echo "</div>";
				echo "<div class='form-field'>
				<label>Upload File <span class='required'>*</span> " .$CMSBuilder->tooltip('Upload File', 'File must be smaller than 20MB.'). "</label>
				<input type='file' class='input" .(in_array('file', $required) ? ' required' : ''). "' name='file' value='' />
				</div>";
			echo "</div>";
		}

	echo "</div>"; //Details

	//Sticky footer
	include('includes/widgets/formbuttons.php');

	// //Sticky footer
	// echo "<footer id='cms-footer' class='resize'>
	// 	<button type='submit' name='save' value='save' class='button f_right'><i class='fa fa-check'></i>Save Changes</button>";
	// 	if(ITEM_ID != "" && (!isset($row['deletable']) || $row['deletable'] == 1)){ 
	// 		echo "<button type='button' name='delete' value='delete' class='button delete'><i class='fa fa-trash'></i>Delete</button>";
	// 	}
	// 	echo "<a href='" .PAGE_URL.(isset($_GET['category_id']) ? '?category_id='.$_GET['category_id'] : ''). "' class='cancel'>Cancel</a>";
	// echo "</footer>";

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "<input type='hidden' name='old_file' value='" .(isset($file) && $file != '' && file_exists($filedir.$file) ? $file : ''). "' />";
	echo "</form>";

}

?>