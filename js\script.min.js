function getBreakpoint(){let e,t,i=window.innerWidth;return i=null==i?$(window).innerWidth():i,i>1366?(e=1367,t=1920):i>1024?(e=1025,t=1366):i>768?(e=769,t=1024):i>480?(e=481,t=768):(e=0,t=480),{minwidth:e,maxwidth:t}}function loadScript(e,t){e=Array.isArray(e)?e:[e],t="function"==typeof t&&t;let i=e.length,o=0,n=()=>t&&++o>=i&&t(),a={recaptcha:{exists:!!window.recaptcha,src:"//www.google.com/recaptcha/api.js?onload=recaptchaCallback&render=explicit"},savvior:{exists:!!window.savvior,src:path+"js/savvior.min.js"},gmapsapi:{exists:!(!window.google||!window.google.maps),src:"//maps.googleapis.com/maps/api/js?key="+google_api_key},jqueryui:{exists:!(!$||!$.ui),src:path+"js/jquery-ui.min.js"},navmenu:{exists:"function"==typeof NavMenu,src:path+"js/navmenu.js"},multilevelpushmenu:{exists:!(!$||!$.fn.multilevelpushmenu),src:path+"js/jquery.multilevelpushmenu.min.js"},lightgallery:{exists:!(!$||!$.fn.lightGallery),src:path+"core/plugins/light-gallery/js/lightgallery-all.min.js"},swiper:{exists:!!window.Swiper,src:path+"includes/plugins/swiper/swiper-bundle.min.js"},sharethis:{exists:!!window.__sharethis__,src:"//platform-api.sharethis.com/js/sharethis.js#property=REPLACE_ME&source=platform"}};for(let t,o=0;o<e.length;o++)t=e[o],null==a[t]?(console.warn('Lazy Load Script: "'+t+'" is not a valid identifier and will be skipped.'),i--):a[t].exists?n():$.getScript(a[t].src,n)}function observe(e,t,i){t="function"==typeof t?t:$.noop;let o=new IntersectionObserver(t,$.extend({rootMargin:"200px",threshold:0},i));return $(e).each(((e,t)=>{o.observe(t)})),o}function observeOnce(e,t,i){return observe(e,(function(e,i){let o=!1;e.forEach((function(e,t){e.isIntersecting&&(o=!0)})),o&&(t(e,i),i.disconnect())}),i)}function observeEach(e,t,i){return observe(e,(function(e,i){e.forEach((function(o,n){o.isIntersecting&&(t.call(o.target,o,n,e,i),i.unobserve(o.target))}))}),i)}function debounce(e,t,i){let o;return function(){let n=this,a=arguments,l=i&&!o;clearTimeout(o),o=setTimeout((function(){o=null,!i&&e.apply(n,a)}),t||200),l&&e.apply(n,a)}}function imagesLoaded(e,t){let i=$(e).find("img").addBack("img"),o=0,n=()=>{++o>=i.length&&t()};"function"==typeof t&&i.each((function(e,t){this.complete?n():(this.addEventListener("load",n),this.addEventListener("error",n))}))}function outOfBounds(e,t){let i=$(t)[0].getBoundingClientRect(),o=$(e)[0].getBoundingClientRect(),n=Math.round(i.left)>Math.round(o.left),a=Math.round(i.right)<Math.round(o.right),l=Math.round(i.top)>Math.round(o.top),s=Math.round(i.bottom)<Math.round(o.bottom);return n||a||l||s}function responsiveItems(){let e=$(".panel-text");e.find("iframe").not("[src*=wmode]").each(((e,t)=>{t.src+=(-1!=t.src.indexOf("?")?"&":"?")+"wmode=transparent"})),e.find("iframe, object, embed").each((function(){let e=$(this),t=e.attr("width"),i=e.attr("height");e.css({"aspect-ratio":t&&i?t+" / "+i:"0.6",height:"auto"})}))}function responsiveTables(){$("#body-content table").not(".noresponsive").each((function(){let e=$(this),t=[];e.addClass("responsive"),e.find("tr").each((function(){let e=0;$(this).find("th").length&&($(this).addClass("header-row"),$(this).find("th").each((function(){t[e]=$(this).html(),e++})));let i=0;$(this).find("td").each((function(){null!=t[i]&&" "!=t[i]&&"&nbsp;"!=t[i]&&$(this).prepend(`<span class="table-header">${t[i]}:&nbsp;</span>`),i++}))}))}))}function embedMedia(){$(".embed-media").each((function(){let e=$(this),t=e.find("> img");t.length&&$(this).append('<span class="play"></span>'),t.attr("style")&&e.attr("style",t.attr("style"))&&t.removeAttr("style"),e.css({width:"",height:""})}))}window.addEventListener("resize",debounce((e=>$(window).trigger("resize-start")),200,!0)),window.addEventListener("resize",debounce((e=>$(window).trigger("resize-end")),200)),$(window).on("resize scroll mousemove click focus blur",(e=>$(window).trigger("interact"))),window.observers={},$.ajaxSetup({cache:!0}),$((function(){window.touch="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,$("html").toggleClass("touch",window.touch).toggleClass("no-touch",!window.touch)})),$(document).ready((function(){embedMedia(),responsiveItems(),responsiveTables(),sanitizeHrefs(),$("#tournament-information").closest(".container").addClass("container-lg")})),$((function(){observeOnce("#main-navigation",(()=>loadScript("navmenu",(()=>{new NavMenu("#main-navigation",{concatenated:e=>e.$el.addClass("concatenated")})}))))})),$((function(){const e=$("#page-navbar"),t=()=>$(".leadin-popup.position-top.type-bar:visible").outerHeight()||0;window.prevScroll=$(window).scrollTop(),window.addEventListener("scroll",debounce((e=>{window.prevScroll=$(window).scrollTop()}),50,!0)),$(window).scrollTop()>150+t()&&e.addClass("sticky"),$(window).on("scroll resize sticky-header",(function(){let i=t(),o=e.outerHeight()+i,n=$(window).scrollTop(),a=n-window.prevScroll<-50,l=n-window.prevScroll>50,s=innerWidth>1024,r=n>o&&e.hasClass("hide"),d=n>i&&e.hasClass("sticky");d=r||d?n>i:a&&n>o,e.toggleClass("sticky",s&&d),r=n>o&&(l||!a&&r),e.toggleClass("hide",s&&r)})).trigger("sticky-header")})),$((function(){let e=$("#mobile-navigation"),t=$("#menu-toggle"),i=$("#page-wrapper");e.length&&e.hasClass("push-menu")&&t.one("click",(function(){loadScript("multilevelpushmenu",(function(){function o(){t.removeClass("open").addClass("close"),e.multilevelpushmenu("expand"),i.css({height:window.innerHeight,overflow:"hidden"}).addClass("pushed")}function n(){e.multilevelpushmenu("collapse"),t.removeClass("close").addClass("open"),i.css({height:"auto"}).delay(200).removeClass("pushed").animate({overflow:"visible"},1,(function(){$(this).removeAttr("style")}))}e.multilevelpushmenu({containersToPush:[i],wrapperClass:"mblmenu",menuInactiveClass:"mblmenu_inactive",direction:"rtl",collapsed:!0,fullCollapse:!0,menuWidth:300,overlapWidth:0,backItemIcon:"hit-area fas fa-angle-left",backText:"Back",groupIcon:"more-icon fas fa-angle-right",swipe:"desktop",preventItemClick:!1,preventGroupItemClick:!0,onMenuReady:function(e,t,i){$("div.levelHolderClass").stop(!0,!0),o()}}),t.bind("click",(function(){$(this).hasClass("open")?o():n()})),$("#close-menu").bind("click",(function(){n()})),$("html").hasClass("no-touch")&&$(window).resize((function(){!1!==e.multilevelpushmenu("visiblemenus")&&(t.removeClass("close").addClass("open"),e.multilevelpushmenu("redraw"),e.multilevelpushmenu("collapse"),i.removeAttr("style").removeClass("pushed"))}))}))}))})),$((function(){const e=()=>$(".video-bg:visible video, video.video-bg:visible").each(((e,t)=>t.play()));$(window).on("resize-end",e),e()})),$((function(){observeEach(".responsive-bg",(e=>{let t=$(e.target);t.closest("#slideshow .slide").index()<1&&t.addClass("visible")}))})),$((function(){$(".embed-media").click((function(e){e.preventDefault();let t=$(this),i=t.attr("href"),o=t.find("> img"),n=$("<iframe></iframe>"),a=i;matches=i.match(/^(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/),matches&&matches[1]&&(a="https://www.youtube-nocookie.com/embed/"+matches[1]+"?rel=0&autoplay=1"),matches=i.match(/^.*vimeo\.com\/(channels\/[A-z]+\/|groups\/[A-z]+\/videos\/)?([0-9]+)/),matches&&matches[2]&&(a="https://player.vimeo.com/video/"+matches[2]+"?autoplay=1"),a&&(n.attr({width:o.width()||t.attr("width")||560,height:o.height()||t.attr("height")||315,src:a,frameborder:0,allow:"autoplay; encrypted-media; fullscreen",allowfullscreen:"",style:t.attr("style")}).insertAfter(t).addClass(this.className),t.remove())}))}));let external_bits=location.host.split(".");external_bits.shift(),external_bits=external_bits.join(".");let external=RegExp("^((f|ht)tps?:)?//(?!(([a-z]*.)?"+external_bits+"))");function sanitizeHrefs(){$('a[target="_blank"]').each((function(){let e=$(this);if(location.hostname!==this.hostname){let t=void 0===this.rel?"":this.rel.toLowerCase(),i=t.split(" ");-1===t.indexOf("noopener")&&i.push("noopener"),-1===t.indexOf("noreferrer")&&i.push("noreferrer"),e.attr("rel",i.join(" ").trim())}}))}function embedMedia(){$(".embed-media").each((function(){let e=$(this),t=e.find("> img");t.length&&$(this).append('<span class="play"></span>'),t.attr("style")&&e.attr("style",t.attr("style"))&&t.removeAttr("style"),e.css({width:"",height:""})}))}function checkmail(e){return/^[^\s()<>@,;:\"\/\[\]?=]+@\w[\w-]*(\.\w[\w-]*)*\.[a-z]{2,}$/i.test(e)}function submitForm(e,t,i){let o=$(e),n=o.find('[name="submitform"]'),a=$(o.data("recaptcha")),l=a.closest(".recaptcha-modal"),s=!0,r=!1,d="";console.log("form data ===- "+o.serialize()),$errors=o.find(".jsvalidate").filter(((e,t)=>""==$(t).val().trim())),$errors.length&&($errors.addClass("error"),s=!1,d+="Please fill out all the required fields.<br />"),$errors=o.find('input[type="email"]').filter(((e,t)=>""!=$(t).val().trim()&&!checkmail($(t).val()))),$errors.length&&($errors.addClass("error"),s=!1,d+="Please enter a valid email address.<br />"),s?(a.length>0?""!=grecaptcha.getResponse(a.data("id"))?r=!0:l.trigger("modal-open"):r=!0,r&&(""!=t?(n.attr("disabled",!0).addClass("loading"),a.length>0&&l.dialog("close"),$.post(path+t,o.serialize(),(function(e){e.errors<1?(o[0].reset(),o.find(":input.error").removeClass("error"),o.hasClass("leadin-form")||dialogAlert("Success!",e.msg_validation,"success "+o.attr("id")+"-success")):($(e.error_fields.map((e=>':input[name="'+e+'"]')).join(",")).addClass("error"),dialogAlert("Error!",e.msg_validation,"error")),n.removeAttr("disabled").removeClass("loading"),i(e),a.length>0&&grecaptcha.reset(a.data("id"))}),"json")):i())):dialogAlert("Error!",d,"error")}function dialogAlert(e,t,i){let o=$("#dialog-box");o=o.length?o:$('<div id="dialog-box"></div>').appendTo("body"),i="string"!=typeof i?i?"success":"error":i,o.html(t).trigger("modal-reinit",{width:350,classes:i?"dialog-alert dialog-"+i:"dialog-alert",title:e,autoOpen:!0})}function dialogConfirm(e,t,i){let o=$("#confirm-box");o=o.length?o:$('<div id="confirm-box"></div>').appendTo("body"),o.html(e).trigger("modal-reinit",{width:400,title:"Confirm",classes:"dialog-alert dialog-confirm",autoOpen:!0,buttons:[{text:"Cancel",class:"button",click:function(){"function"==typeof i&&i(),o.dialog("close")}},{text:"Confirm",class:"button",click:function(){"function"==typeof t&&t(),o.dialog("close")}}]})}function showLeadIn(e){if(!e.hasClass("displayed")){if(e.hasClass("type-popup")){let t=getBreakpoint();t.minwidth;(t.maxwidth>768||!e.hasClass("show-tablet-l"))&&(e.trigger("modal-open"),e.addClass("displayed"),e.addClass("open"))}else e.addClass("displayed"),e.addClass("open");$.post(path+"js/ajax/leadin-events.php","id="+e.data("id")+"&event=open&xid="+c_xid)}}function hideLeadIn(e){e.hasClass("type-popup")?e.dialog("close"):e.removeClass("open"),$.post(path+"js/ajax/hide-leadin.php","id="+e.data("id")+"&xid="+c_xid),e.find(".leadin-content:visible").length&&$.post(path+"js/ajax/leadin-events.php","id="+e.data("id")+"&event=close&xid="+c_xid),e.remove()}function initMap(e,t,i,o){let n,a=$(e);e=a[0],t=t??a.data("lat")??53.54,i=i??a.data("lng")??-113.49928080000001,o=o??a.data("zoom")??15,n=new google.maps.LatLng(t,i);const l=new google.maps.Map(e,{scrollwheel:!1,draggable:!0,center:n,zoom:o,styles:[{stylers:[{visibility:"simplified"},{saturation:20},{weight:3.2},{lightness:25}]}],disableDefaultUI:!0,mapTypeId:google.maps.MapTypeId.ROADMAP,navigationControl:!0,mapTypeControl:!1,scaleControl:!0,panControl:!0,panControlOptions:{position:google.maps.ControlPosition.LEFT_TOP},zoomControl:!0,zoomControlOptions:{style:google.maps.ZoomControlStyle.SMALL,position:google.maps.ControlPosition.LEFT_TOP}});return google.maps.event.addDomListener(window,"resize",(()=>l.setCenter(n))),l}function placeMapMarker(e,t,i,o){const n=new google.maps.Marker({animation:google.maps.Animation.DROP,position:new google.maps.LatLng(t,i),href:o,map:e,icon:{url:path+"images/svg/maps-marker.svg?v=1.2",size:new google.maps.Size(30,40),origin:new google.maps.Point(0,0),anchor:new google.maps.Point(15,40),scaledSize:new google.maps.Size(30,40)}});return n.href&&google.maps.event.addListener(n,"click",(()=>window.open(n.href,"_blank"))),n}function billingToggle(e){1==e?$("#billing-profile").slideDown():$("#billing-profile").slideUp()}function waitList(e){var t=$(e).data("id"),i=$(e).data("name");$(e).find(".fa").addClass("fa-spinner fa-spin"),$('<div id="dialog-box"></div>').appendTo("body").html("Subscribe to the waiting list for "+i+"?").dialog({modal:!0,title:"Confirm",autoOpen:!0,width:300,resizable:!1,closeOnEscape:!0,closeText:"<i class='fa fa-close'></i>",beforeClose:function(t,i){$(e).find(".fa").removeClass("fa-spinner fa-spin")},buttons:{Confirm:function(){$(this).dialog("close"),$(e).find(".fa").addClass("fa-spinner fa-spin"),$.ajax({url:path+"js/ajax/waitinglist.php",data:"id="+t,method:"post"}).done((function(t){"success"==t?dialogAlert("Success!","You have been successfully added to the waiting list.","success"):"login"==t?window.location=window.location.href:dialogAlert("Error!",t,"error"),$(e).find(".fa").removeClass("fa-spinner fa-spin")})).fail((function(){dialogAlert("Error!","Unknown error. Please try again.","error"),$(e).find(".fa").removeClass("fa-spinner fa-spin")}))},Cancel:function(){$(this).dialog("close")}},show:{effect:"drop",direction:"up",duration:200},hide:{effect:"drop",direction:"up",duration:200}})}function numberfy(e){return e=e||"0",parseFloat(e.replace(/[^0-9.]/g,""))||0}function monify(e){return"$"+parseFloat(e).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g,"$1,")}function hioTemplate(){var e=$("#hio-template").html();$("#hio-add").before('<fieldset class="hio-course hio-new" style="display:none;">'+e+"</fieldset>"),$(".hio-new input.number").val(""),$(".hio-new").removeClass("hio-new").slideDown()}function hioDate(){$("#hio-date").before("<input type='text' name='dates[]' class='input clear futuredatepicker' value='' autocomplete='off' />"),$(".input.futuredatepicker").not(".hasDatepicker").each((function(){$(this).datepicker({dateFormat:"MM d, yy",defaultDate:1,minDate:1,showAnim:"slideDown",changeMonth:!0,changeYear:!0})}))}function addCartItem(e){var t=$(e).data("id");$(e).data("name");$(e).find(".fa").addClass("fa-spinner fa-spin"),$.ajax({url:path+"js/ajax/addcartitem.php",data:"id="+t,method:"post"}).done((function(t){$(e).find(".fa").removeClass("fa-spinner fa-spin"),"success"==t?loadCart():"login"==t?window.location=window.location.href:dialogAlert("Error!",t,"error")})).fail((function(){dialogAlert("Error!","Unable to add item to cart. Please try again.","error"),$(e).find(".fa").removeClass("fa-spinner fa-spin")}))}function loadCart(){$.ajax({url:path+"js/ajax/loadcart.php",data:"",method:"post",dataType:"json"}).done((function(e){$('<div id="dialog-box" class="add-to-cart-dialog"></div>').appendTo("body").html(e.html).dialog({modal:!0,title:"Shopping Cart",autoOpen:!0,width:750,resizable:!1,closeOnEscape:!0,closeText:"<i class='fa fa-close'></i>",show:{effect:"drop",direction:"up",duration:200},hide:{effect:"drop",direction:"up",duration:200}}),$("span.cart-total").text(e.cart_total)})).fail((function(){dialogAlert("Error!","Unable to load shopping cart. Please try again.","error")}))}function deleteCartItem(e){$("#delete-confirm-dialog").length&&$("#delete-confirm-dialog").dialog("destroy").remove(),$('<div id="delete-confirm-dialog" title="Confirm Delete"></div>').html('<p><i class="fa fa-warning" style="color: #f0ad4e; margin-right: 10px;"></i>Are you sure you want to delete this item from your cart?</p>').appendTo("body").dialog({modal:!0,autoOpen:!0,width:400,height:"auto",resizable:!1,closeOnEscape:!0,closeText:"<i class='fa fa-close'></i>",dialogClass:"delete-cart-confirmation",buttons:[{text:"Delete Item",class:"btn btn-danger secondary",click:function(){var t=$(this);t.closest(".ui-dialog").find(".btn").prop("disabled",!0),$.ajax({url:path+"js/ajax/deletecartitem.php",data:"id="+e,method:"post"}).done((function(e){t.dialog("close"),$(".add-to-cart-dialog").dialog("destroy").remove(),loadCart()})).fail((function(){t.dialog("close"),dialogAlert("Error!","Unable to delete cart item. Please try again.","error")}))}},{text:"Cancel",class:"btn btn-default",click:function(){$(this).dialog("close")}}],close:function(){$(this).dialog("destroy").remove()},show:{effect:"fadeIn",duration:200},hide:{effect:"fadeOut",duration:200}})}function addAttendee(){var e=$(".reg-attendee").html();$(".form-buttons").before('<div class="reg-attendee new-attendee" style="display:none;">'+e+"</div>"),$(".new-attendee input, .new-attendee select").each((function(){$(this).removeClass("required").val("")})),$(".new-attendee fieldset").append('<a onclick="deleteAttendee(this);" class="delete">x Remove Attendee</a>'),$(".new-attendee").removeClass("new-attendee").slideDown(300)}function deleteAttendee(e){$(e).parents(".reg-attendee").slideUp(300,(function(){$(this).remove()}))}$("a:not([target])").each((function(){external.test($(this).attr("href"))&&$(this).attr("target","_blank")})),$((function(){let e=$("#slideshow");e.find(".slide").length>1&&observeOnce(e,(()=>loadScript("swiper",(()=>{const t=e=>{let t=$(e.slides[e.activeIndex]).find("video:visible")[0];$(e.slides).find("video").not(t).each(((e,t)=>t.pause())),t?.play()},i=()=>window.innerWidth>1024?n.autoplay.start():n.autoplay.stop();let o={loop:!0,speed:800,autoHeight:!0,simulateTouch:!1,fadeEffect:{crossFade:!0},autoplay:{delay:1e4},pagination:{el:e.find(".slideshow-pagination")[0],type:"bullets",clickable:!0},navigation:{nextEl:e.find(".slideshow-button-next")[0],prevEl:e.find(".slideshow-button-prev")[0]},on:{slideChange:i=>{t(i),(t=>{let i=t.previousIndex-t.activeIndex<0?"right":"left";e.removeClass("slide-left slide-right").addClass("slide-"+i)})(i)},activeIndexChange:e=>{(e=>{$(e.slides[e.activeIndex]).next().addBack().find(".responsive-bg").addClass("visible")})(e)},resize:e=>{t(e)}}};const n=new Swiper(e[0],$.extend(!0,o,window?.extendSlideshowOpts));e.hover((()=>n.autoplay.stop()),i),$(window).on("resize-end",i),i(),$(window).one("interact",(()=>e.find(".responsive-bg").addClass("visible")))}))))})),$((function(){observeOnce(".content-tabs, .content-accordion",(function(){loadScript("jqueryui",(function(){$(".content-tabs").tabs({show:{effect:"fadeIn",duration:300},hide:{effect:"fadeOut",duration:300},activate:function(){responsiveItems()}}),$(".content-accordion").accordion({header:".accordion-header",active:!0,collapsible:!0,animate:200,heightStyle:"content",icons:{header:"fas fa-chevron-down",activeHeader:"fas fa-chevron-down rotated"},create:function(e,t){$(this).hasClass("expanded")&&$(this).accordion("option","active",0)}})}))}))})),$((function(){window.lazyLoaderIO=observeEach(".lazy-load",(function(e,t,i,o){let n=e.target,a=$(n),l=a.is("img"),s=l?n.src:a.css("background-image").slice(4,-1).replace(/["']/g,""),r=a.data("src");s=s.replace(window.location.origin,""),r&&s!=r&&(l?n.src=r:a.css("background-image",'url("'+r+'")'),a.removeData("src").addClass("loaded"))}))})),$((function(){const e=new ResizeObserver((e=>{for(let t of e){let e=$(t.target),i=e.children("li").toArray().reduce(((t,i)=>t||outOfBounds(i,e)),!1);e.closest(".content-tabs").toggleClass("responsive-tabs",i)}}));$(".content-tabs").each((function(){let t,i=$(this),o=$("<select/>").addClass("select tabs-select");t=i.find(".tabs-nav-wrapper a").toArray().reduce(((e,t,i)=>e+`<option value="${i}">${t.innerText}</option>`),""),o.html(t).change((function(){i.tabs("option","active",$(this).val())})),i.on("tabsbeforeactivate",((e,t)=>o.val(t.newTab.index()))),i.find(".tabs-nav-wrapper").append(o),e.observe(i.find("ul.tabs-nav")[0])}))})),$((function(){let e=e=>$(e).addClass("animated");window.animatorIO=observeEach(".animate",(function(t,i,o,n){let a=$(t.target).data(),l=a.delay||200;e(t.target).find(a.animate).addClass("animate").each((function(t,i){setTimeout((()=>{e(i)}),l*t)}))}),{rootMargin:"-100px"})})),$((function(){observeOnce(".light-video, .light-gallery, .light-iframe",(function(){loadScript("lightgallery",(function(){$(".light-video").lightGallery({counter:!1,speed:800,videoMaxWidth:"1280px",youtubePlayerParams:{modestbranding:1,showinfo:0,controls:0}}),$(".light-gallery").lightGallery({selector:".gal-item",exThumbImage:"data-exthumbimage"}),$(".light-iframe").lightGallery({selector:"this",iframeMaxWidth:"90%"})}))}))})),$((function(){observeOnce(".gallery-listings .light-gallery",(()=>{loadScript(["savvior","lightgallery"],(()=>{window.addEventListener("savvior:redraw",(()=>{$(".light-gallery").lightGallery({selector:".gal-item"})})),savvior.init(".gallery-listings .light-gallery",{"screen and (max-width: 480px)":{columns:2},"screen and (min-width: 481px) and (max-width: 769px)":{columns:3},"screen and (min-width: 769px) and (max-width: 1024px)":{columns:4},"screen and (min-width: 1025px)":{columns:5}})}))}))})),$((function(){observeEach(".responsive-bg",(e=>{let t=$(e.target);t.closest("#slideshow .slide").index()<1&&t.addClass("visible")}))})),$((function(){$(".embed-media").click((function(e){e.preventDefault();let t=$(this),i=t.attr("href"),o=t.find("> img"),n=$("<iframe></iframe>"),a=i;matches=i.match(/^(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/),matches&&matches[1]&&(a="https://www.youtube-nocookie.com/embed/"+matches[1]+"?rel=0&autoplay=1"),matches=i.match(/^.*vimeo\.com\/(channels\/[A-z]+\/|groups\/[A-z]+\/videos\/)?([0-9]+)/),matches&&matches[2]&&(a="https://player.vimeo.com/video/"+matches[2]+"?autoplay=1"),a&&(n.attr({width:o.width()||t.attr("width")||560,height:o.height()||t.attr("height")||315,src:a,frameborder:0,allow:"autoplay; encrypted-media; fullscreen",allowfullscreen:"",style:t.attr("style")}).insertAfter(t).addClass(this.className),t.remove())}))})),$((function(){let e=observeOnce($("form[data-recaptcha]").filter((function(){return!$(this).closest(".hidden-modal, .ui-dialog").length})),(function(e,t){loadScript("recaptcha")}));$(document).on("dialogopen",(function(t){$(t.target).dialog("widget").find("form[data-recaptcha]").length&&loadScript("recaptcha"),e.disconnect()}))})),recaptchaCallback=function(){$(".g-recaptcha").each((function(e,t){let i=$(t).attr("id"),o=grecaptcha.render(i,{sitekey:recaptcha_sitekey,callback:function(e){let t=$('form[data-recaptcha="#'+i+'"]');t.find('input[name="g-recaptcha-response"]').val(e),t.submit()}});$(t).data("id",o)}))},$((function(){$(document).on("blur",".jsvalidate:input",(function(){let e=$(this);e.toggleClass("error",!e.val()),e.is(':not(.error)[type="email"]')&&e.toggleClass("error",!checkmail(e.val()))}))})),$((function(){$("#contact-form, #landing-form").on("submit",(function(){return submitForm(this,"js/ajax/submit-form.php",(function(){})),!1}))})),$((function(){$(".hidden-modal");let e=function(e){let t=$(this),i=t.data();e="object"==typeof e?e:{},(e=$.extend({modal:!0,classes:i.classes,title:t.attr("title")||i.title,autoOpen:void 0!==i.open,width:i.width||315,maxWidth:window.innerWidth-40,minHeight:0,resizable:void 0!==i.resizable,draggable:void 0!==i.draggable,closeOnEscape:!0,closeText:"",buttons:i.buttons||void 0,show:{effect:"drop",direction:"down",duration:300},hide:{effect:"drop",direction:"up",duration:300},create:function(){let i=t.dialog("widget");e.title&&t.attr("title",e.title),!e.title&&i.find(".ui-dialog-titlebar").addClass("hidden")},open:sanitizeHrefs},e)).classes="string"==typeof e.classes?{"ui-dialog":e.classes}:{},t.hasClass("ui-dialog-content")&&t.dialog("destroy"),t.addClass("hidden-modal").appendTo("body").dialog(e)},t=function(){$(this).each((function(){$modal=$(this),$modal.is(".ui-dialog-content:visible")&&($modal.dialog("option","maxWidth",window.innerWidth-40),$modal.dialog("option","position",{my:"center",at:"center",of:window}))}))};$(document).on("modal-reinit",(function(t,i){loadScript("jqueryui",(function(){e.call(t.target,i)}))})),$(document).on("modal-open",(function(e){let t=$(e.target);t.is(".ui-dialog-content")?t.dialog("open"):t.trigger("modal-reinit",{autoOpen:!0})})),$(".hidden-modal[data-open]").trigger("modal-reinit"),$(document).on("modal-fit",(function(e){t.call(e.target)})),$(window).on("resize",(function(e){$(".ui-dialog:visible .ui-dialog-content").each(t)})),$(document).on("dialogopen",(function(e,t){let i=$(e.target);i.dialog("moveToTop"),void 0===i.data("disable-overlay-click")&&i.dialog("instance").overlay.one("click",(function(){e.preventDefault(),e.stopPropagation(),i.dialog("close")}))})),$(document).on("click","[data-open-hidden-modal]",(function(e){let t=$($(this).data("open-hidden-modal"));t.length&&(e.preventDefault(),t.data("opened-by",this).trigger("modal-open"))})),$(document).on("click",".ui-dialog",(function(e){let t=$(e.target).closest(".ui-dialog").find(".ui-dialog-content"),i=$("#dialog-box, #confirm-box, .recaptcha-modal").filter(".ui-dialog-content").not(t);i.length&&i.dialog("close")}))})),$((function(){let e=function(e){let t=$(this);e.preventDefault(),e.stopImmediatePropagation(),dialogConfirm(t.data("content"),(function(){t.removeClass("confirm-click").click()}))};$(document).on("click.confirm",".confirm-click",e).on("confirm-reinit",e)})),$((function(){if($(".leadin-popup").length){let e=$(".leadin-popup"),t=e.data("delay"),i=e.data("delay-type");if("time"==i){let i=setTimeout((function(){showLeadIn(e),clearTimeout(i)}),1e3*t)}else if("scroll"==i){let i=$(window).scrollTop()/($(document).height()-$(window).height());i=Math.round(100*i),$(window).on("scroll",(function(){i=$(window).scrollTop()/($(document).height()-$(window).height()),i=Math.round(100*i),(isNaN(i)||i>=t)&&showLeadIn(e)})),(isNaN(i)||i>=t)&&showLeadIn(e)}$(document).on("click",".leadin-popup .close-button",(function(){return hideLeadIn(e),!1})),$(window).resize((()=>{window.innerWidth<=768&&e.is(".show-tablet-l.ui-dialog-content")&&e.dialog("close")})),$(".leadin-popup.hidden-modal").on("dialogclose",(function(){hideLeadIn(e)}))}})),$((function(){$(".leadin-form").on("submit",(function(){return submitForm(this,"js/ajax/leadin-form.php",(function(e){null!=e&&e.errors<1&&($(".leadin-form.hidden-modal").length&&$(".leadin-form.hidden-modal").dialog("close"),$(".leadin-popup").find(".leadin-content, .leadin-form").fadeOut(300,(function(){$(".leadin-popup .leadin-success").show().html('<div id="leadin-success-message">'+e.msg_validation+"</div>").fadeIn(300)})))})),!1}))})),$((function(){$(document).on("click",".leadin-popup a.button:not([data-open-hidden-modal])",(function(){let e=$(this).closest(".leadin-popup");$.post(path+"js/ajax/leadin-events.php","id="+e.data("id")+"&event=link-click&xid="+c_xid)})),$(document).on("dialogopen",".leadin-form",(function(){let e=$(this).attr("id").replace("leadin-form-","");$.post(path+"js/ajax/leadin-events.php","id="+e+"&event=form-open&xid="+c_xid)}))})),$((function(){const e=(e,t,i)=>`https://maps.google.com/?q=${encodeURIComponent(e)}&latlng=${t},${i}`;let t=$(".contact-map");observeOnce(t,(()=>loadScript("gmapsapi",(()=>{t.each(((t,i)=>{let o=$(i).data(),n=e(o.search,o.lat,o.lng);const a=initMap(i),l=placeMapMarker(a,o.lat,o.lng,n);$(i).data({googleMap:a,markers:[l]})}))})))),t.length&&$(".content-tabs").on("tabsbeforeactivate",(function(t,i){let o=$(i.newTab).find("a"),n=$(".contact-map"),a=o.data(),l=e(a.search,a.lat,a.lng);if(void 0!==a.map)if(a.map){let e=!!n.data("markers")&&n.data("markers")[0],t=n.data("googleMap"),i={lat:a.lat,lng:a.lng};n.data(a),n.closest(".panel").addClass("has-map"),t&&e&&(e.setPosition(i),e.href=l,t.panTo(i),t.setZoom(a.zoom)),n.fadeIn(300)}else n.fadeOut(300,(function(){n.closest(".panel").removeClass("has-map")}))}))})),$((function(){var e=document.createElement("link"),t=document.getElementsByTagName("head")[0];e.rel="stylesheet",e.href=path+"core/plugins/font-awesome/css/all.min.css",t.parentNode.append(e)})),$((function(){$(".panel.parallax").each((function(){const e=$("<div>",{class:"curve-container"}),t=$("<img>",{src:path+"images/svg/parllax-bg.svg?v=1.0",alt:"",class:"curve-shape"});e.append(t),$(this).append(e)}))})),$(document).ready((function(){const e=$(".location-tab"),t=$("#contact-map"),i=$(".location-slider-wrapper"),o=$(".location-tabs-nav"),n=$(".location-scroll-up"),a=$(".location-scroll-down");let l=null,s=null,r=0,d=0,c=1;function u(){if(window.google&&window.google.maps&&t.length){const e=parseFloat(t.attr("data-lat"))||0,i=parseFloat(t.attr("data-lng"))||0,o=parseInt(t.attr("data-zoom"))||12;l=new google.maps.Map(t[0],{center:{lat:e,lng:i},zoom:o,scrollwheel:!1}),s=new google.maps.Marker({position:{lat:e,lng:i},map:l,animation:google.maps.Animation.DROP}),t.data("map",l),t.data("marker",s)}}function p(){if(console.log("updateSliderPosition"),e.length>0){d=e.first().outerHeight();Math.max(0,(e.length-c)*d);const t=-r*d;o.css("transform",`translateY(${t}px)`),n.toggleClass("disabled",0===r),a.toggleClass("disabled",r>=e.length-c)}}function f(){if(console.log("scroll up"),r>0){r--,p();e.eq(r).trigger("click")}}function h(){if(r<e.length-c){r++,p();e.eq(r).trigger("click")}}window.google&&window.google.maps?u():window.initMap=u,e.length>0&&(!function(){p();const e=debounce((function(){$(this).hasClass("disabled")||f()}),600,!0),t=debounce((function(){$(this).hasClass("disabled")||h()}),600,!0);n.on("click",(function(t){console.log("Up arrow clicked via delegation"),t.preventDefault(),t.stopPropagation(),e.call(this)})),a.on("click",(function(e){console.log("Down arrow clicked via delegation"),e.preventDefault(),e.stopPropagation(),t.call(this)})),$(document).on("keydown",(function(e){i.is(":visible")&&(38===e.which?(f(),e.preventDefault()):40===e.which&&(h(),e.preventDefault()))})),i.on("wheel",(function(e){e.originalEvent.deltaY<0?f():h(),e.preventDefault()}))}(),e.on("click",(function(){e.removeClass("active"),$(this).addClass("active");const t=$(this).data("location-id");$(".location-details").hide(),$("#location-details-"+t).show();!function(e,t,i){if(l&&s){const o={lat:e,lng:t};s.setPosition(o),l.setCenter(o),l.setZoom(i)}}(parseFloat($(this).attr("data-lat"))||0,parseFloat($(this).attr("data-lng"))||0,parseInt($(this).attr("data-zoom"))||12)})),$(window).on("resize",(function(){p()})))})),$(document).ready((function(){const e=$("main").find("section:last-of-type"),t=$("#page-footer");function i(){window.matchMedia("(max-width: 768px)").matches?(t.css({background:"url('/images/footer-m.png') no-repeat center bottom","background-size":"cover",minHeight:"100vw","background-position":"100% 100%"}),e.hasClass("cta")&&e.css({background:"url('/images/cta-m.png') no-repeat center bottom","background-size":"cover",minHeight:"100vw","background-position":"100% 100%"})):e.hasClass("cta")?t.css({background:"url('/images/footer-cta.png')","background-size":"cover","background-repeat":"no-repeat"}):t.css({background:"url('/images/svg/footer.svg') no-repeat center bottom"})}i(),$(window).resize((function(){i()}))})),$((function(){observeOnce("#blog-share",(()=>loadScript("sharethis",(()=>{$(".sharethis-buttons").each((function(){var e=$(this).attr("id"),t=$(this).data("url")||null,i=$(this).data("title")||null,o=$(this).data("description")||null,n=$(this).data("image")||null;window.__sharethis__.load("inline-share-buttons",{enabled:!0,id:e,url:t,title:i,description:o,image:n,networks:["facebook","twitter","linkedin","email","sharethis"],show_mobile_buttons:!0,onLoad:function(){$(".st-btn").on("click",(function(e){$.ajax({url:path+"js/ajax/track-shares.php",data:{service:$(this).data("network"),url:t||window.__sharethis__.href,xid:c_xid},type:"POST"})}))}})}))}))))})),$((function(){$("#blog-comment-form").on("submit",(function(){return submitForm("blog-comment-form","js/ajax/post-comment.php",(function(e){void 0!==e&&""!=e.new_comment&&($("#blog-comments").remove(".no-comments"),$("#blog-comments").prepend(e.new_comment))})),!1}))})),$((function(){if($(".blog-swiper-container").length){const i=$(".blog-swiper-container"),o=$(".blog-entry"),n=$(".swiper-slide",i),a=$(".swiper-button-prev",i),l=$(".swiper-button-next",i),s=$(".blog-search input"),r=$(".blog-category-filter select"),d=3;let c=0,u=n;function e(e){o.hide();const t=o.filter('[data-entry-id="'+e+'"]');if(t.length){t.show();const i=n.filter('[data-entry-id="'+e+'"]');if(i.length){const e=i.find(".post-title").text(),t=i.find(".post-date").text();$("#blog-rightcol .blog-title").text(e),$("#blog-rightcol .blog-date").text("Posted on "+t)}window.innerWidth<=768&&$("html, body").animate({scrollTop:t.offset().top-100},300)}}function t(e,t=0){u=e,c=Math.max(0,t),u.length>0?(c=Math.min(c,u.length-1),c+d>u.length&&(c=Math.max(0,u.length-d))):c=0,n.hide();u.slice(c,c+d).show(),a.toggleClass("swiper-button-disabled",c<=0||u.length<=d),l.toggleClass("swiper-button-disabled",c+d>=u.length||u.length<=d),i.toggleClass("few-posts",u.length<=d)}loadScript("swiper",(function(){if(t(n,0),n.length>0){const t=n.first();t.addClass("active"),e(t.data("entry-id"))}else o.hide()})),$("#mobile-blog-select").on("change",(function(){const t=$(this).val();if(t){n.removeClass("active");n.filter('[data-entry-id="'+t+'"]').addClass("active"),e(t)}})),n.on("click",(function(t){t.preventDefault();const i=$(this);n.removeClass("active"),i.addClass("active"),e(i.data("entry-id")),$("#mobile-blog-select").val(i.data("entry-id"))})),s.on("keyup",(function(){const e=$(this).val().toLowerCase().trim();let i;r.val(""),i=""===e?n:n.filter((function(){const t=$(this).find(".post-title").text().toLowerCase().trim(),i=$(this).find(".post-category").text().toLowerCase().trim(),o=$(this).find(".post-date").text().toLowerCase().trim();return t.includes(e)||i.includes(e)||o.includes(e)})),t(i,0)})),r.on("change",(function(){const e=$(this).val();let i;s.val(""),i=""===e?n:n.filter((function(){return $(this).data("category-id")==e})),t(i,0)})),a.on("click",(function(e){e.preventDefault(),$(this).hasClass("swiper-button-disabled")||t(u,c-1)})),l.on("click",(function(e){e.preventDefault(),$(this).hasClass("swiper-button-disabled")||t(u,c+1)}))}})),$((function(){function e(){let e=$(".button.primary");window.innerWidth>768?e.addClass("light"):e.removeClass("light")}e(),$(window).on("resize",debounce(e,200))})),$((function(){window.location.href.endsWith("/staff/")||window.location.href.endsWith("/staff")?($("main section").first().find(".container").addClass("container-lg"),$("main section").first().find(".container .panel-text").css({width:"100%","max-width":"100%"})):window.location.href.includes("/staff/")&&$("main section").first().find(".container .panel-text").css({width:"100%","max-width":"100%"}),$(".staff-link").on("click",(function(e){window.location.href=$(this).find("a").attr("href")}))})),$((function(){const e=$("#mobile-blog-select"),t=$(".blog-search input"),i=$(".search-results-dropdown"),o=$(".blog-entry"),n=[];e.find("option").each((function(){$(this).val()&&n.push({id:$(this).val(),text:$(this).text()})})),t.on("keyup focus",(function(){const e=$(this).val().toLowerCase().trim();if(""===e)return void i.empty().hide();const t=n.filter((t=>t.text.toLowerCase().includes(e)));t.length>0?(i.empty(),t.forEach((e=>{$("<div>").addClass("search-result-item").text(e.text).attr("data-entry-id",e.id).appendTo(i)})),i.show()):i.html('<div class="no-results">No matching posts found</div>').show()})),$(document).on("click",(function(e){$(e.target).closest(".blog-search").length||i.hide()})),$(document).on("click",".search-result-item",(function(){const n=$(this).data("entry-id"),a=$(this).text();t.val(a),i.hide(),function(t){o.hide();const i=o.filter('[data-entry-id="'+t+'"]');if(i.length){i.show();const o=$(".swiper-slide").filter('[data-entry-id="'+t+'"]'),n=e.find('option[value="'+t+'"]');let a,l;if(o.length)a=o.find(".post-title").text(),l=o.find(".post-date").text();else if(n.length){const e=n.text().split(" - ");a=e[0]||"",l=e[1]||""}$("#blog-rightcol .blog-title").text(a||""),$("#blog-rightcol .blog-date").text(l?"Posted on "+l:""),window.innerWidth<=768&&$("html, body").animate({scrollTop:i.offset().top-100},300)}}(n),e.val("")}))})),$(document).ready((function(){function e(){setTimeout((function(){if(0!==$(".blog-custom-slider").length){console.log("Initializing blog slider...");var e=new Swiper(".blog-custom-slider",{slidesPerView:"auto",spaceBetween:20,loop:!1,cssMode:!0,resistance:!0,resistanceRatio:0,grabCursor:!0,watchOverflow:!0,centerInsufficientSlides:!0,roundLengths:!0,pagination:{el:".blog-swiper-pagination",clickable:!0},navigation:{nextEl:".blog-swiper-button-next",prevEl:".blog-swiper-button-prev"},breakpoints:{0:{slidesPerView:1,spaceBetween:20},768:{slidesPerView:2,spaceBetween:20},992:{slidesPerView:"auto",spaceBetween:20},1100:{slidesPerView:"auto",spaceBetween:25}}}),t=document.querySelector(".blog-swiper-button-prev"),i=document.querySelector(".blog-swiper-button-next");t&&t.addEventListener("click",(function(t){t.preventDefault(),t.stopPropagation(),console.log("Prev button clicked"),e.slidePrev()})),i&&i.addEventListener("click",(function(t){t.preventDefault(),t.stopPropagation(),console.log("Next button clicked"),e.slideNext()})),console.log("Blog slider initialized successfully")}}),500)}"undefined"==typeof Swiper?"function"==typeof loadScript?loadScript("swiper",e):console.error("Swiper is not loaded and loadScript function is not available"):e()})),$((function(){let e=$(".staff-listing.blog-entries-container");observeOnce(e,(()=>loadScript(["swiper"],(()=>{e.each((function(){let e=$(this);e.closest(".panel").addClass("has-swiper"),e.addClass("swiper"),e.find(".swiper-wrapper").length||(e.children().wrap('<div class="swiper-slide" />'),e.children().wrapAll('<div class="swiper-wrapper" />')),e.parent().hasClass("swiper-container-wrapper")||e.wrap('<div class="swiper-container-wrapper"></div>'),e.next(".swiper-navigation").length||e.after('<div class="swiper-navigation"><div class="swiper-button-prev location-scroll-arrow location-scroll-left"></div><div class="swiper-button-next location-scroll-arrow location-scroll-right"></div></div>');let t=function(t){e.toggleClass("swiper-nav-disabled",!t.allowSlideNext&&!t.allowSlidePrev)},i={direction:"horizontal",autoHeight:!0,slidesPerView:3,slidesPerGroup:1,spaceBetween:10,watchSlidesProgress:!0,centerInsufficientSlides:!0,watchOverflow:!0,preventInteractionOnTransition:!0,loopFillGroupWithBlank:!1,normalizeSlidesGrid:!0,grid:{rows:1,fill:"row"},navigation:{nextEl:e.next(".swiper-navigation").find(".swiper-button-next").get(0),prevEl:e.next(".swiper-navigation").find(".swiper-button-prev").get(0)},simulateTouch:!0,mousewheel:{forceToAxis:!0,releaseOnEdges:!0},freeMode:{enabled:!1,sticky:!1},breakpoints:{0:{slidesPerView:1,slidesPerGroup:1,spaceBetween:10,centeredSlides:!0},700:{slidesPerView:2,slidesPerGroup:1,spaceBetween:25,centeredSlides:!0},800:{slidesPerView:3,slidesPerGroup:1,spaceBetween:20,centeredSlides:!1}},on:{init:function(){t(this),console.log("Swiper initialized with navigation"),this.slideTo(0,0),setTimeout((()=>{this.update()}),100)},update:t,resize:function(){this.slideTo(0,0),this.update(),console.log("Staff slider resized, resetting to first slide")}}};const o=new Swiper(this,i);e.data({swiper:o}),setTimeout((()=>{o&&o.update()}),100),$(window).on("resize",(function(){o&&setTimeout((()=>{o.update()}),100)}))}))}))))})),$((function(){$(document).on("click",".logout-btn",(function(){return dialogConfirm("Are you sure you want to logout?",(function(){window.location=path+"modules/account/Logout.php"})),!1}))})),$((function(){$("#register-form").on("submit",(function(){return submitForm(this,"js/ajax/register.php",(function(){})),!1}))})),$((function(){$("#forgot-form").on("submit",(function(){var e=$(this);return e.find('*[name="submitform"]').attr({disabled:!0}),e.find(".alert").remove(),$.ajax({url:path+"js/ajax/forgot-password.php",data:e.serialize(),method:"post"}).done((function(t){e.find('*[name="submitform"]').removeAttr("disabled"),"success"==t?e.prepend('<div class="alert success"><p>Success! Password reset instructions have been sent.</p></div>'):e.prepend('<div class="alert error"><p>Error! '+t+"</p></div>")})).fail((function(){e.find('*[name="submitform"]').removeAttr("disabled"),e.prepend('<div class="alert error"><p>Error! Unable to submit request. Please try again.</p></div>')})),!1}))})),$((function(){"undefined"!=typeof USER_LOGGED_IN&&!0===USER_LOGGED_IN&&$("#main-navigation ul li").each((function(){var e=$(this),t=e.find("a span").text().trim();"Register"!==t&&"Login"!==t||e.remove()}))})),$((function(){$(document).on("click","#delete-profile-photo-btn",(function(e){e.preventDefault(),console.log("Delete button clicked");var t=$(this),i=t.data("filename"),o=(t.closest(".current-photo"),t.closest(".photo-upload-area")),n=t.closest(".current-photo");confirm("Are you sure you want to delete your profile photo? This cannot be undone.")&&(console.log("Confirmed deletion for file:",i),t.addClass("loading").prop("disabled",!0),t.text("Deleting..."),$.ajax({method:"POST",url:path+"js/ajax/delete-profile-image.php",data:{filename:i},dataType:"json"}).done((function(e){console.log("AJAX response:",e),e.errors?(alert("Error: "+(e.message||"Failed to delete photo.")),t.removeClass("loading").prop("disabled",!1),t.html('<i class="fas fa-trash-alt"></i> Delete Photo')):(console.log("AJAX success, removing photo elements..."),n.length?n.fadeOut(400,(function(){$(this).remove()})):(o.find(".profile-thumbnail").remove(),t.remove()),o.find(".upload-field").before('<p class="photo-success-message" style="color: green;">Photo deleted.</p>'),setTimeout((function(){o.find(".photo-success-message").fadeOut().remove()}),3e3),o.find('input[name="old_photo"]').val(""))})).fail((function(e,i,o){console.error("AJAX Error:",i,o),alert("Error: Failed to connect to server."),t.removeClass("loading").prop("disabled",!1),t.html('<i class="fas fa-trash-alt"></i> Delete Photo')})))}))})),$(document).on("click","#delete-facility-logo-btn, #delete-facility-banner-btn",(function(e){e.preventDefault(),console.log("Facility image delete button clicked");var t=$(this),i=t.data("filename"),o=t.data("imagetype"),n=t.data("facilityid"),a=t.closest(".photo-upload-area"),l=t.closest(".current-photo"),s="logo"===o?"old_logo":"old_banner";if(confirm("Are you sure you want to delete this "+o+"? This cannot be undone.")){if(console.log("Confirmed deletion for type:",o,"file:",i,"facility ID:",n),!i||!o||!n)return alert("Error: Missing data needed for deletion. Cannot proceed."),void console.error("Missing data:",{filename:i,imageType:o,facilityId:n});var r=t.html();t.addClass("loading").prop("disabled",!0).text("Deleting..."),$.ajax({method:"POST",url:path+"js/ajax/delete-facility-image.php",data:{filename:i,imagetype:o,facilityid:n},dataType:"json"}).done((function(e){console.log("Facility Delete AJAX response:",e),e.errors?(alert("Error deleting "+o+": "+(e.message||"Failed to delete.")),t.removeClass("loading").prop("disabled",!1).html(r)):(console.log("AJAX success, removing facility",o,"elements..."),l.length?l.fadeOut(400,(function(){$(this).remove()})):(a.find(".profile-thumbnail").remove(),t.remove()),a.find(".upload-field").before('<p class="photo-success-message" style="color: green; margin-top: 5px;">'+o.charAt(0).toUpperCase()+o.slice(1)+" deleted.</p>"),setTimeout((function(){a.find(".photo-success-message").fadeOut().remove()}),3500),a.find('input[name="'+s+'"]').val(""),console.log("Cleared hidden input:",s))})).fail((function(e,i,o){console.error("Facility Delete AJAX Error:",i,o,e.responseText),alert("Error: Failed to connect to server for deletion."),t.removeClass("loading").prop("disabled",!1).html(r)}))}})),$(document).ready((function(){let e=window.location.pathname,t=$("#page-hero"),i=$("#page-footer"),o=/(\/find-facility\/|\/find-pro\/)(.+?-\d+\/)/i;if(o.test(e)){let n=e.match(o);if(console.log("URL matches pattern. Base: "+n[1]+", Dynamic part: "+n[2]),t.length){let e=t.css("background");console.log("Original #page-hero background style:",e);let i="our-members/images/svg/banner.svg",o="images/svg/banner.svg",n="";e&&e.includes(i)?e.includes("url(")&&e.includes(i)?(n=e.replace(i,o),t.css("background",n),console.log("Hero Background style changed. New value:",n)):console.warn("For Hero: '"+i+"' found, but not clearly within a url(). No change applied to be safe."):console.warn("For Hero: Key string part '"+i+"' not found in current style. No change applied.")}if(i.length){let e=i.css("background");console.log("Original #page-footer background style:",e);let t="our-members/images/svg/footer.svg",o="images/svg/footer.svg",n="";e&&e.includes(t)?e.includes("url(")&&e.includes(t)?(n=e.replace(t,o),i.css("background",n),console.log("Footer Background style changed. New value:",n)):console.warn("For Footer: '"+t+"' found, but not clearly within a url(). No change applied to be safe."):console.warn("For Footer: Key string part '"+t+"' not found in current style. No change applied.")}}else console.log("URL does not match the simplified pattern. No background change needed.")})),$(document).ready((function(){$(".folder-header, .folder-toggle").click((function(e){if(!$(e.target).closest(".folder-actions").length){var t=$(this).next(".folder-content"),i=$(this).find(".folder-toggle i");t.slideToggle(),i.toggleClass("fa-chevron-down fa-chevron-up")}})),window.fileUploader=function(e){$("#file-uploader input[name=category_id]").val(e),$("#file-uploader").dialog({modal:!0,width:600,height:"auto",resizable:!1,title:"Upload File"})},$("#upload-form button[name=upload]").click((function(){$("#upload-form").submit()})),$(".delete-btn").click((function(){confirm("Are you sure you want to delete this file?")&&$(this).closest("form").submit()})),$("#file").change((function(){var e=$(this).val().split("\\").pop();if($(this).next("label").html('<i class="fa fa-file"></i> '+e),!$("input[name=file_name]").val()){var t=e.replace(/\.[^/.]+$/,"");$("input[name=file_name]").val(t)}}))})),$(document).ready((function(){$(".search-bar-invoice input").on("keyup",(function(){if($(this).val().length<3)$(".search-bar-invoice .fa-times-circle").on("click",(function(e){e.preventDefault(),$(".search-bar-invoice input").val("").trigger("keyup")}));else{var e=$(this).val().toLowerCase(),t=$(".tabs-panel:visible");if(t.length)if(""===e)t.find("table tr").show();else t.find("table tr.header-row").show(),t.find("table tr:not(.header-row)").each((function(){$(this).text().toLowerCase().indexOf(e)>-1?$(this).show():$(this).hide()})),0===t.find("table tr:visible:not(.header-row)").length?0===t.find(".no-results-row").length?t.find("table").append('<tr class="no-results-row"><td colspan="5" class="nobg">No results found matching "'+e+'"</td></tr>'):t.find(".no-results-row td").html('No results found matching "'+e+'"'):t.find(".no-results-row").remove()}})),$(".search-bar-invoice .fa-times-circle").on("click",(function(e){e.preventDefault(),$(".search-bar-invoice input").val("").trigger("keyup")})),$(".tabs-nav a").on("click",(function(){setTimeout((function(){$(".search-bar-invoice input").trigger("keyup")}),100)}))})),$((function(){$("#use_mailing").bind("click",(function(){if($(this).is(":checked")){var e=$('input[name="address1"]').val(),t=$('input[name="address2"]').val(),i=$('input[name="city"]').val(),o=$('select[name="province"]').val(),n=$('select[name="state"]').val(),a=$('input[name="region"]').val(),l=$('input[name="postal_code"]').val(),s=$('select[name="country"]').val();$('input[name="bill_address1"]').val(e),$('input[name="bill_address2"]').val(t),$('input[name="bill_city"]').val(i),$('select[name="bill_province"]').val(o),$('select[name="bill_state"]').val(n),$('input[name="bill_region"]').val(a),$('input[name="bill_postalcode"]').val(l),$('select[name="bill_country"]').val(s),$('select[name="province"]').is(":visible")?($('select[name="bill_province"]').parents(".form-field").show(),$('select[name="bill_state"]').parents(".form-field").hide(),$('input[name="bill_region"]').parents(".form-field").hide()):$('select[name="state"]').is(":visible")?($('select[name="bill_province"]').parents(".form-field").hide(),$('select[name="bill_state"]').parents(".form-field").show(),$('input[name="bill_region"]').parents(".form-field").hide()):$('input[name="region"]').is(":visible")&&($('select[name="bill_province"]').parents(".form-field").hide(),$('select[name="bill_state"]').parents(".form-field").hide(),$('input[name="bill_region"]').parents(".form-field").show())}else $('input[name="bill_address1"]').val(""),$('input[name="bill_address2"]').val(""),$('input[name="bill_city"]').val(""),$('select[name="bill_province"]').val(""),$('select[name="bill_state"]').val(""),$('input[name="bill_region"]').val(""),$('input[name="bill_postalcode"]').val(""),$('select[name="bill_country"]').val("")}))})),$("#compensation-survey").on("input blur","input, select",(function(){var e=0,t=0,i=0;""!=$("#monthly_salary").val()&&""!=$("#months").val()&&(e=numberfy($("#monthly_salary").val())*numberfy($("#months").val())),""!=$("#vacation_days").val()&&(t=e/365*numberfy($("#vacation_days").val()),i+=t),""!=$("#lesson_total").val()&&(e+=numberfy($("#lesson_total").val())),$("input.sum").each((function(){""!=$(this).val()&&(i+=numberfy($(this).val()))})),$("#vacation_total").html(monify(t)),$("#salary_total").html(monify(e)),$("#benefits_total").html(monify(i)),$("#total").html(monify(e+i))})),$("#hio-form").on("input.sum blur, input.golfers blur, input.futuredatepicker change","input.sum, input.golfers, input.futuredatepicker",(function(){var e=0,t="";$("input.sum").each((function(){""!=$(this).val()&&(e+=numberfy($(this).val()),t=t+numberfy($(this).val())+";")}));var i=0;$("input.futuredatepicker").each((function(){""!=$(this).val()&&i++})),$("#prize_total").html(monify(e));var o=$("input.golfers").val();""==o&&(o=0),e>0?$.ajax({url:path+"js/ajax/hiopremium.php",data:{prizes:t,field:o},method:"post"}).done((function(e){$("#premium_per_date").html(monify(e)),$("#premium_total").html(monify(e*i))})):($("#premium_per_date").html(monify(0)),$("#premium_total").html(monify(0)))})),$(".input.datepicker").each((function(){$(this).datepicker({dateFormat:"MM d, yy",defaultDate:0})})),$(".input.dateselector").each((function(){$(this).datepicker({dateFormat:"MM d, yy",defaultDate:0,changeMonth:!0,changeYear:!0,yearRange:"-100:+0",onChangeMonthYear:function(e,t,i){var o=i.selectedDay;$(this).datepicker("setDate",new Date(e,t-1,o))}})})),$((function(){$("button.delete").bind("click",(function(){var e=this.form;$('<div id="dialog-box"></div>').appendTo("body").html("Are you sure you want to permanently delete this item?").dialog({modal:!0,title:"Confirm",autoOpen:!0,width:300,resizable:!1,closeOnEscape:!0,closeText:"<i class='fa fa-close'></i>",buttons:{Confirm:function(){$(this).dialog("close"),$(e).submit()},Cancel:function(){$(this).dialog("close")}},show:{effect:"drop",direction:"up",duration:200},hide:{effect:"drop",direction:"up",duration:200}})})),$(".delete-button.confirm").on("click",(function(){var e=this;$('<div id="dialog-box"></div>').appendTo("body").html("Are you sure you want to permanently delete this item?").dialog({modal:!0,title:"Confirm",autoOpen:!0,width:300,resizable:!1,closeOnEscape:!0,closeText:"<i class='fa fa-close'></i>",buttons:{Confirm:function(){$(this).dialog("close"),$('<input type="hidden" name="delete" value="delete">').appendTo($(e).closest("form")),$(e).closest("form").submit()},Cancel:function(){$(this).dialog("close")}},show:{effect:"drop",direction:"up",duration:200},hide:{effect:"drop",direction:"up",duration:200}})}))})),$(document).ready((function(e){e(".input.futuredatepicker").each((function(){e(this).hasClass("hasDatepicker")||e(this).datepicker({dateFormat:"MM d, yy",defaultDate:1,minDate:1,showAnim:"slideDown",changeMonth:!0,changeYear:!0})})),window.hioDate=function(){e("#hio-date").before("<input type='text' name='dates[]' class='input clear futuredatepicker' value='' />"),e(".input.futuredatepicker").each((function(){e(this).hasClass("hasDatepicker")||e(this).datepicker({dateFormat:"MM d, yy",defaultDate:1,minDate:1,showAnim:"slideDown",changeMonth:!0,changeYear:!0})}))}})),$(document).ready((function(){window.location.href.includes("account/hole-one/")&&$(".container .panel-text").css("max-width","100%")}));