<?php

$html = '';

//Search form
$html .= '<div class="message-center-controls-container">
	<form name="search-form" id="search-site-bar" action="" method="get" class="clearfix dinline-block directory-search-form">
	<div class="search-input-container">
		<input type="text" name="search" class="input directory-search-input" value="' .$searchterm. '" placeholder="Search..." />
		<button type="button" name="button" class="button solid inline search-icon-btn" onclick="this.form.submit();"><i class="fa fa-search"></i></button>
		' .($searchterm != '' ? '<a href="' .$page['page_url']. '" class="fa fa-times-circle"></a>' : ''). '
		</div>	
		</form>
</div>';

if($searchterm != ''){
	$html .= '<h3>Search Results</h3><p><small>' .$totalresults. ' Result' .($totalresults != 1 ? 's' : ''). ' Found</small></p><hr />';
}else{
	$html .= '';
}

//Search results
if(!empty($searchresults)){
	foreach($searchresults as $row){
		
		//Facilities
		if(isset($row['facility_id']) && !isset($row['event_id'])){
			$str = $row['facility_name']. ($row['city'] != '' ? ', '.$row['city'] : ''). ($row['province'] != '' ? ', '.$row['province'] : '');
			$url = $_sitepages['facilities']['page_url'].$row['page'].'-'.$row['facility_id'].'/';
			
			$html .= '<p>
				<strong>' .$_sitepages['facilities']['name']. ':</strong>&nbsp; ' .highlight($str, $searchterm).'<br />
				<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
			</p><hr />';
		
		//Directory	
		}else if(isset($row['profile_id'])){
			$str = $row['first_name'].' '.$row['last_name'];
			
			//Member
			if($row['role_id'] == 2){
				$str .= ($row['class_name'] != '' ? ', ' .$row['class_name'] : '');
				$str .= ($row['facility_name'] != '' ? ' at ' .$row['facility_name'] : '');
				$url = $_sitepages['directory']['page_url'].clean_url($row['first_name'].'-'.$row['last_name'].'-'.$row['profile_id']).'/';
				
				$html .= '<p>
					<strong>' .$_sitepages['directory']['name']. ':</strong>&nbsp; ' .highlight($str, $searchterm).'<br />
					<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
				</p><hr />';
			
			//Staff
			}else{
				$str .= ($row['title'] != '' ? ', ' .$row['title'] : '');
				$url = $_sitepages['contact']['page_url'];
				
				$html .= '<p>
					<strong>' .$_sitepages['contact']['name']. ':</strong>&nbsp; ' .highlight($str, $searchterm).'<br />
					<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
				</p><hr />';
			}
			
		//Tournaments & Events
		}else if(isset($row['event_id'])){			
			$str = $row['event_name'].($row['facility_name'] != '' ? ' at '.$row['facility_name'] : '');;
			if($row['event_type'] == 2){
				$url = $_sitepages['tournaments']['page_url'].$row['page'].'-'.$row['occurrence_id'].'/';
			}else{
				$url = $_sitepages['events']['page_url'].$row['page'].'-'.$row['occurrence_id'].'/';
			}
			
			$html .= '<p>
				<strong>' .($row['event_type'] == 2 ? 'Tournament' : 'Event'). ':</strong>&nbsp; ' .highlight($str, $searchterm).'<br /><small>'.format_date_range($row['start_date'], $row['end_date']).'</small><br />
				<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
			</p><hr />';
			
		//News	
		}else if(isset($row['newsletter_id'])){			
			$str = $row['title'];
			$url = $_sitepages['newsfeed']['page_url'].date('mY', strtotime($row['post_date'])).'/'.$row['page'].'-'.$row['newsletter_id'].'/';
			
			$html .= '<p>
				<strong>' .($row['category_name'] != '' ? $row['category_name'] : $_sitepages['contact']['name']). ':</strong>&nbsp; ' .highlight($str, $searchterm).'<br /><small>Posted on '.date('F j, Y', strtotime($row['post_date'])).'</small><br />
				<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
			</p><hr />';
		
		//Careers	
		}else if(isset($row['career_id'])){			
			$str = $row['title'].($row['facility_name'] != '' ? ' at '.$row['facility_name'] : '');
			$url = $_sitepages['careers']['page_url'].$row['page'].'-'.$row['career_id'].'/';
			
			$html .= '<p>
				<strong>' .$_sitepages['careers']['name']. ':</strong>&nbsp; ' .highlight($str, $searchterm).'<br /><small>Posted on '.date('F j, Y', strtotime($row['posted_date'])).'</small><br />
				<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
			</p><hr />';
			
		//Classifieds	
		}else if(isset($row['classified_id'])){			
			$str = $row['title'].($row['facility_name'] != '' ? ' at '.$row['facility_name'] : '');
			$url = $_sitepages['classifieds']['page_url'].$row['page'].'-'.$row['classified_id'].'/';
			
			$html .= '<p>
				<strong>' .$_sitepages['classifieds']['name']. ':</strong>&nbsp; ' .highlight($str, $searchterm).'<br /><small>Posted on '.date('F j, Y', strtotime($row['date_added'])).'</small><br />
				<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
			</p><hr />';
			
		//Awards	
		}else if(isset($row['award_id'])){	
			if(isset($row['winner_id'])){
				$str = $row['first_name'].' '.$row['last_name'].', '.$row['year'].' '.$row['award_name'].' '.$row['type'];
			}else{
				$str = $row['name'];
			}
			$url = $_sitepages['awards']['page_url'].$row['page'].'-'.$row['award_id'].'/';
			
			$html .= '<p>
				<strong>' .$_sitepages['awards']['name']. ':</strong>&nbsp; ' .highlight($str, $searchterm).'<br />
				<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
			</p><hr />';
			
		//Pages	
		}else if(isset($row['page_id'])){	
			$str = $sitemap[$row['page_id']]['seo_title'];	
			$desc = $row['meta_description'];
			$url = $sitemap[$row['page_id']]['page_url'];
						
			$html .= '<p>
				<strong>Pages:</strong>&nbsp; ' .highlight($str, $searchterm).'<br />
				' .($desc != '' ? '<small>' .highlight($desc, $searchterm).'</small><br />' : ''). ' 
				<small><a style="color:#EF3E34;" href="' .$url. '" target="' .($row['type'] == '0' ? '_self' : '_blank'). '">' .$siteurl.$url. '</a></small>
			</p><hr />';
						
		//Scholarship & Bursary	
		}else if(isset($row['scholarship_id'])){	
			$str = $row['first_name'].' '.$row['last_name'].', '.$row['year'].' '.$row['award'].' Winner';
			if($row['award'] == 'Scholarship'){
				$url = $sitemap[103]['page_url'];
			}else{
				$url = $sitemap[45]['page_url'];
			}
			
			$html .= '<p>
				<strong>' .$row['award']. ' Winners:</strong>&nbsp; ' .highlight($str, $searchterm).'<br />
				<small><a style="color:#EF3E34;" href="' .$url. '">' .$siteurl.$url. '</a></small>
			</p><hr />';
		}
		
	}
		
	//Pager
	$html .= '<p class="pager clear"><small>';
		$html .= 'Displaying '.($pg == 'all' ? '1 - '.$totalresults : (1+($limit*($pg-1))).' - '.(count($searchresults)+($limit*($pg-1)))).' (of '.$totalresults.' Total)<br />';
		if($totalresults > $limit && $pg != 'all'){
			$tagend = round($totalresults % $limit, 0);
			$splits = round(($totalresults - $tagend)/$limit, 0);
			$num_pages = ($tagend == 0 ? $splits : $splits+1);	
			$pos = $pg;
			$startpos = ($pos*$limit)-$limit;

			$html .= ($pos > 1 ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos-1).'">&lsaquo; Prev</a> ' : '');
			for($i=1; $i<=$num_pages; $i++){
				$html .= ($i != $pos ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.$i.'">'.$i.'</a> ' : '<span><u>'.$i.'</u></span> ');
			}
			$html .= ($pos < $num_pages ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos+1).'">Next &rsaquo;</a> ' : '');
		}
	$html .= '</small></p>';
	
	
//No results	
}else{
	if($searchterm != ''){
		$html .= '<p>No results found matching <strong>`' .$searchterm. '`</strong>. Please try another search.</p>';
	}
}


//Set panel content
$page['page_panels'][$panel_id]['content'] .= $html;

//Page panels
include("includes/pagepanels.php");

?>