<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('form_submissions');
	$CMSBuilder->set_widget($_cmssections['form_submissions'], 'Total Form Submissions', $total_records);
}

if(SECTION_ID == $_cmssections['form_submissions']){
	
	//Define vars
	$record_db = 'form_submissions';
	$record_id = 'submission_id';
	$record_name = 'Submission';

	//Get forms
	$db->query("SELECT * FROM `forms` ORDER BY `form_name` ASC");
	$forms = $db->fetch_assoc('form_id');

	//Filtering
	$where = '';
	$params = [];
	$filters = ['form_id', 'start_date', 'end_date'];

	if(isset($_POST['clear-search'])){
		foreach($filters as $filter){
			unset($_SESSION['search_'.$filter][SECTION_ID]);
		}
	}else if(isset($_GET['advanced_search'])){
		foreach($filters as $filter){
			$_SESSION['search_'.$filter][SECTION_ID] = $_GET[$filter] ?? NULL;
		}
	}

	if($_SESSION['search_form_id'][SECTION_ID] ?? false){
		$where .= ($where ? " AND" : " WHERE")." `$record_db`.`form_id` = ?";
		$params[] = $_SESSION['search_form_id'][SECTION_ID];
	}
	if($_SESSION['search_start_date'][SECTION_ID] ?? false){
		$where .= ($where ? " AND" : " WHERE")." `$record_db`.`timestamp` >= ?";
		$params[] = date('Y-m-d 00:00:00', strtotime($_SESSION['search_start_date'][SECTION_ID]));
	}
	if($_SESSION['search_end_date'][SECTION_ID] ?? false){
		$where .= ($where ? " AND" : " WHERE")." `$record_db`.`timestamp` <= ?";
		$params[] = date('Y-m-d 23:59:59', strtotime($_SESSION['search_end_date'][SECTION_ID]));
	}

	//Get submissions
	$db->query("SELECT * FROM `$record_db` $where ORDER BY `timestamp` DESC", $params);
	$records_arr = $db->fetch_assoc($record_id);

	//Not found
	if(ACTION == 'edit'){
		if($records_arr[ITEM_ID] ?? false){

			//Get form fields
			$form_fields = array();
			$db->query("SELECT `form_submission_fields`.*, IFNULL(`form_submission_fieldsets`.`legend`, ?) AS `legend`, IFNULL(`form_submission_fields`.`fieldset_id`, 0) AS `fieldset_id` FROM `form_submission_fields` LEFT JOIN `form_submission_fieldsets` ON `form_submission_fields`.`fieldset_id` = `form_submission_fieldsets`.`fieldset_id` WHERE `form_submission_fields`.`submission_id` = ? ORDER BY `form_submission_fieldsets`.`fieldset_id`, `form_submission_fields`.`field_id`", array('', ITEM_ID));
			$result = $db->fetch_array();
			foreach($result as $row){
				$form_fields[$row['fieldset_id']]['legend'] = $row['legend'];
				$form_fields[$row['fieldset_id']]['form_fields'][] = $row;
			}
			
			$records_arr[ITEM_ID]['form_fields'] = $form_fields;
			//var_dump($form_fields);exit;
			$row = $records_arr[ITEM_ID];	

		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}

	if(ACTION != '' && ACTION != 'edit'){
		header('Location:' .PAGE_URL);
		exit();
	}

	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	}

	// Export listings
	if(ACTION == '' && isset($_GET['export'])){
		include('exports/export-form-submissions.php');
	}

}

?>