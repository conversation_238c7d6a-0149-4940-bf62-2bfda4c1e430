@charset "utf-8";
/*
	critical.less

	Project: Theme 2

*/
/*------ imports ------*/
/*
	global.less

*/
/*------ imports ------*/
/*
	core.less

*/
/*------ responsive ------*/
/*------ imports ------*/
/* 
	mixins.less

*/
/*------ utilities ------*/
/*------ typography ------*/
.light {
  font-weight: 300;
}
.regular {
  font-weight: 400;
}
.medium {
  font-weight: 500;
}
.semibold {
  font-weight: 600;
}
.bold {
  font-weight: 700;
}
.extrabold {
  font-weight: 800;
}
.black {
  font-weight: 900;
}
.uppercase {
  text-transform: uppercase;
}
.capitalize {
  text-transform: capitalize;
}
.strikethrough {
  text-decoration: line-through;
}
.underline {
  text-decoration: underline;
}
/*------ forms ------*/
/*------ display ------*/
.full {
  width: 100%;
}
.half {
  width: 50%;
}
.auto {
  width: auto;
}
.auto-width {
  width: auto !important;
}
.auto-height {
  height: auto !important;
}
.sr-only {
  position: absolute;
  margin: 0;
  padding: 0;
  border: 0;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  background: none;
  overflow: hidden;
}
.f_right {
  float: right;
  display: block;
}
.f_left {
  float: left;
  display: block;
}
.clear {
  display: block;
  clear: both;
}
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}
.right {
  text-align: right;
}
.left {
  text-align: left;
}
.center {
  text-align: center;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.block {
  display: block !important;
}
.inline-block {
  display: inline-block !important;
}
.inline {
  display: inline !important;
}
.hidden {
  display: none !important;
}
@media all and (max-width: 480px) {
  .show-tablet-p {
    display: none !important;
  }
}
@media all and (max-width: 768px) {
  .show-tablet-l {
    display: none !important;
  }
}
@media all and (max-width: 1024px) {
  .show-notebook {
    display: none !important;
  }
}
@media all and (max-width: 1366px) {
  .show-desktop {
    display: none !important;
  }
}
@media all and (max-width: 1920px) {
  .show-widescreen {
    display: none !important;
  }
}
@media all and (min-width: 481px) {
  .hide-tablet-p {
    display: none !important;
  }
}
@media all and (min-width: 769px) {
  .hide-tablet-l {
    display: none !important;
  }
}
@media all and (min-width: 1025px) {
  .hide-notebook {
    display: none !important;
  }
}
@media all and (min-width: 1367px) {
  .hide-desktop {
    display: none !important;
  }
}
@media all and (min-width: 1921px) {
  .hide-widescreen {
    display: none !important;
  }
}
.noborder {
  border: 0 !important;
}
.nobg {
  background: none !important;
}
.nomargin {
  margin: 0 !important;
}
.nomargin-v,
.nomargin-t {
  margin-top: 0 !important;
}
.nomargin-h,
.nomargin-r {
  margin-right: 0 !important;
}
.nomargin-v,
.nomargin-b {
  margin-bottom: 0 !important;
}
.nomargin-h,
.nomargin-l {
  margin-left: 0 !important;
}
.nopadding {
  padding: 0 !important;
}
.nopadding-v,
.nopadding-t {
  padding-top: 0 !important;
}
.nopadding-h,
.nopadding-r {
  padding-right: 0 !important;
}
.nopadding-v,
.nopadding-b {
  padding-bottom: 0 !important;
}
.nopadding-h,
.nopadding-l {
  padding-left: 0 !important;
}
/*------ gradients ------*/
/*------ flexbox ------*/
/*------ columns ------*/
/* For elements within a .multi-column element. Prevents elements from breaking into multiple columns */
/*------ filters ------*/
/*------ transformations ------*/
/*------ animations ------*/
/*------ reset ------*/
* {
  margin: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
body,
html {
  width: 100%;
  height: 100%;
  text-align: left;
}
main,
article,
aside,
details,
figcaption,
figure,
picture,
footer,
header,
hgroup,
menu,
nav,
section,
label {
  display: block;
}
input,
select,
textarea,
button {
  color: inherit;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
input,
select,
textarea,
button,
th,
td {
  font-size: inherit;
  font-family: inherit;
  line-height: normal;
  letter-spacing: inherit;
}
th,
td {
  text-align: inherit;
  line-height: inherit;
}
button {
  background: none;
  border: 0;
  padding: 0;
  cursor: pointer;
  -webkit-transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, color 0.3s ease 0s, opacity 0.3s ease 0s;
  transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, color 0.3s ease 0s, opacity 0.3s ease 0s;
}
select {
  background: none;
  text-overflow: ellipsis;
}
fieldset {
  padding: 0;
  border: 0;
}
/*------ typography ------*/
body,
input,
select,
textarea,
button,
th,
td {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
p,
ul,
ol {
  padding: 0 0 20px;
  margin: 0;
}
ul,
ol {
  margin: 0 0 0 40px;
}
ul ul,
ul ol,
ol ul,
ol ol {
  padding-bottom: 0;
}
ol ol {
  list-style-type: lower-alpha;
}
ol ol ol {
  list-style-type: lower-roman;
}
a {
  text-decoration: none;
  cursor: pointer;
  -webkit-transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, color 0.3s ease 0s, opacity 0.3s ease 0s;
  transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, color 0.3s ease 0s, opacity 0.3s ease 0s;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  padding: 0;
}
h1 {
  margin: 0;
}
h2 {
  margin: 0 0 30px;
}
h3 {
  margin: 0 0 25px;
}
h4 {
  margin: 0 0 20px;
}
h5 {
  margin: 0 0 15px;
}
h6 {
  margin: 0 0 10px;
}
p + h2,
ul + h2,
ol + h2,
table + h2,
blockquote + h2 {
  margin-top: 20px;
}
p + h3,
ul + h3,
ol + h3,
table + h3,
blockquote + h3 {
  margin-top: 15px;
}
p + h4,
ul + h4,
ol + h4,
table + h4,
blockquote + h4 {
  margin-top: 10px;
}
p + h5,
ul + h5,
ol + h5,
table + h5,
blockquote + h5 {
  margin-top: 5px;
}
small {
  display: inline-block;
}
blockquote p {
  padding: 0;
}
/*------ interface ------*/
img,
a.embed-media {
  display: inline-block;
  border: 0;
  max-width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
img:where([width][height]) {
  height: auto;
}
iframe {
  max-width: 100%;
}
hr {
  height: 0;
  margin: 0 0 20px 0;
  padding: 0;
  border: 1px solid;
  border-width: 1px 0 0;
}
table {
  border-collapse: collapse;
  border-style: solid;
  margin: 10px 0 30px;
}
table tbody,
table thead,
table tr,
table th,
table td {
  text-align: inherit;
  border-color: inherit;
  border-style: inherit;
  border-collapse: inherit;
  border-width: inherit;
}
table:where(:not([border])) {
  border-width: 1px;
}
table.responsive .table-header {
  display: none;
  margin: 0;
}
table.column {
  width: calc(100% + 20px) !important;
  margin: 0;
  padding: 0;
  table-layout: fixed;
}
table.column,
table.column td,
table.column th {
  background-color: transparent !important;
  height: auto !important;
}
table.column:not(.mce-item-table) td,
table.column:not(.mce-item-table) th,
table.column {
  border: none !important;
}
table.column td {
  padding: 0;
  vertical-align: top;
}
@media all and (min-width: 769px) {
  table.column:not(.mce-item-table) {
    margin: 0 -10px;
  }
  table.column td {
    padding: 0 10px;
  }
}
@media all and (max-width: 768px) {
  table.column ul:only-child,
  table.column ol:only-child {
    padding-bottom: 0px;
  }
  table.column td:last-child ul:only-child,
  table.column td:last-child ol:only-child {
    padding-bottom: 20px;
  }
  table {
    width: 100% !important;
    border: 1px solid;
  }
  table.responsive tr.header-row,
  table.responsive th {
    display: none;
  }
  table.responsive tr:not(:last-child) td:last-child,
  table.responsive tr:not(:last-child) th:last-child {
    border-bottom-width: 0px;
  }
  table.responsive td {
    display: block;
    width: auto !important;
    text-align: left;
  }
  table.responsive .table-header {
    display: inline-block;
  }
}
/*---- embed media ----*/
a.embed-media {
  position: relative;
  display: inline-block;
  max-width: 100%;
}
a.embed-media img {
  display: block;
}
a.embed-media .play {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 36px;
  font-weight: normal;
  font-style: normal;
  text-align: center;
  line-height: 1;
  z-index: 1;
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
  -webkit-transition: background-color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
a.embed-media .play::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f04b";
  text-shadow: 0 0 8px rgba(0, 0, 0, 0.16);
}
a.embed-media:hover .play {
  background-color: rgba(0, 0, 0, 0.5);
}
iframe.embed-media {
  display: inline-block;
  background-color: #0f0f0f;
}
/*---- dialog ----*/
.ui-dialog {
  position: absolute;
  visibility: hidden;
  overflow: hidden;
  top: 0;
  left: 0;
}
/*---- light gallery ----*/
.lg-outer img {
  -o-object-fit: cover;
     object-fit: cover;
}
/*---- page structure ----*/
#page-wrapper {
  position: relative;
  width: 100%;
  height: auto;
}
#seo-wrapper {
  position: relative;
  z-index: 1;
}
#sitemap .menu-header {
  display: none;
}
.panel-text:after {
  content: "";
  display: table;
  clear: both;
}
.panel-text > :where(:last-child) {
  margin-bottom: 0;
  padding-bottom: 0;
}
/*
	definitions.less

*/
:root {
  --container-width: 930px;
  --container-width-lg: 1240px;
  --container-width-xl: 1560px;
  --container-margin: max(0px, (100% - var(--container-width) - var(--container-padding)*2) / 2);
  --container-margin-lg: max(0px, (100% - var(--container-width-lg) - var(--container-padding)*2) / 2);
  --container-margin-xl: max(0px, (100% - var(--container-width-xl) - var(--container-padding)*2) / 2);
  --container-padding: 20px;
  --container-padding: clamp(20px, 3.90625vw - 10px, 30px);
  --font-h1: 48px;
  --font-h1: clamp(48px, 2.17028vw + 31.33222px, 61px);
  --font-h2: 40px;
  --font-h2: clamp(40px, 1.5025vw + 28.46077px, 49px);
  --font-h3: 33px;
  --font-h3: clamp(33px, 1.00167vw + 25.30718px, 39px);
  --font-h4: 28px;
  --font-h4: clamp(28px, 0.50083vw + 24.15359px, 31px);
  --font-h5: 23px;
  --font-h5: clamp(23px, 0.33389vw + 20.43573px, 25px);
  --font-h6: 19px;
  --font-h6: clamp(19px, 0.16694vw + 17.71786px, 20px);
  --font-blockquote: 22px;
  --font-blockquote: clamp(22px, 0.66778vw + 16.87145px, 26px);
  --font-paragraph: 16px;
  --font-paragraph: clamp(16px, 0vw + 16px, 16px);
  --font-caption: 13px;
  --font-caption: clamp(13px, 0vw + 13px, 13px);
  --font-footnote: 11px;
  --line-height-thin: 1.2;
  --line-height-normal: 1.4;
  --line-height-thick: 1.7;
}
/*------ typography ------*/
body {
  font-family: "Poppins", sans-serif;
  color: #020202;
  line-height: var(--line-height-thick);
  letter-spacing: -0.02em;
  background: #FFFFFF;
}
.font-footnote {
  font-size: var(--font-footnote);
}
.font-caption,
small,
.button.simple,
#page-contact-top .page-contact,
#breadcrumbs {
  font-size: var(--font-caption);
}
.font-paragraph,
body,
.button {
  font-size: var(--font-paragraph);
}
.font-blockquote,
blockquote {
  font-size: var(--font-blockquote);
}
.font-h6,
h6,
.panel h1 {
  font-size: var(--font-h6);
}
.font-h5,
h5,
#page-hero .page-title-wrapper .page-subtitle {
  font-size: var(--font-h5);
}
.font-h4,
h4 {
  font-size: var(--font-h4);
}
.font-h3,
h3,
body.landing-page #page-hero #landing-form .landing-form-title {
  font-size: var(--font-h3);
}
.font-h2,
h2 {
  font-size: var(--font-h2);
}
.font-h1,
h1,
#slideshow .slide-wrapper .slide-content .slide-title h2,
.panel h1.cms {
  font-size: var(--font-h1);
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Lora", sans-serif;
  word-break: break-word;
}
h1 {
  font-weight: 800;
  line-height: var(--line-height-thin);
  letter-spacing: -0.05em;
}
h2 {
  margin-bottom: 20px;
  font-weight: 700;
  line-height: var(--line-height-thin);
  letter-spacing: -0.05em;
}
h3 {
  font-weight: 700;
  color: #16212F;
  line-height: var(--line-height-thin);
  letter-spacing: -0.05em;
}
h4 {
  font-weight: 700;
  color: #EF3E34;
  line-height: var(--line-height-normal);
  letter-spacing: -0.05em;
}
h5 {
  font-weight: 700;
  color: #16212F;
  line-height: var(--line-height-normal);
  letter-spacing: -0.02em;
}
h6 {
  font-weight: 700;
  color: #16212F;
  line-height: var(--line-height-normal);
  letter-spacing: -0.02em;
}
small {
  font-family: "Lora", sans-serif;
  color: #999999;
}
/*------ interface ------*/
:target {
  scroll-margin-top: 100px;
}
::-moz-selection {
  color: #EF3E34;
}
::selection {
  color: #EF3E34;
}
hr {
  border-color: #DDDDDD;
}
a {
  word-break: break-word;
}
blockquote {
  position: relative;
  margin-block: 20px;
  margin-block: clamp(20px, 6.6778vw - 31.28548px, 60px);
  padding-left: 0px;
  padding-left: clamp(0px, 8.34725vw - 64.10684px, 50px);
  color: #16212F;
  line-height: var(--line-height-thick);
  letter-spacing: -0.02em;
  font-family: 'contralto-big';
}
blockquote::before {
  content: '';
  background-image: url("../../images/quote.png");
  background-repeat: no-repeat;
  position: absolute;
  top: -10px;
  left: 5px;
  left: clamp(5px, 0.83472vw - 1.41068px, 10px);
  font-size: 1250px;
  font-style: normal;
  font-weight: 700;
  color: #B3C6BB;
  line-height: 0.6;
  letter-spacing: -0.05em;
  z-index: 2;
  width: 74px;
  height: 150px;
}
blockquote small {
  font-weight: 400;
  font-style: normal;
}
p + blockquote {
  margin-top: 0px;
  margin-top: clamp(0px, 6.6778vw - 51.28548px, 40px);
}
table th,
table td {
  color: #020202;
  padding: 10px 20px;
}
table.responsive label,
table th {
  color: #16212F;
  font-weight: 700;
}
@-webkit-keyframes slideInTopBorder {
  0% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@keyframes slideInTopBorder {
  0% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@-webkit-keyframes slideInBottomBorderDouble {
  0% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  40% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  41% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@keyframes slideInBottomBorderDouble {
  0% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  40% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  41% {
    width: 0%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@-webkit-keyframes slideInTopBorderContinue {
  0% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@keyframes slideInTopBorderContinue {
  0% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@-webkit-keyframes slideInBottomBorderDoubleContinue {
  0% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  40% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  41% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
@keyframes slideInBottomBorderDoubleContinue {
  0% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  40% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  41% {
    width: 65%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  100% {
    width: 100%;
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
}
/* Left border animation (bottom to top, twice) */
@-webkit-keyframes leftBorderAnimation {
  0% {
    height: 0%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  40% {
    height: 100%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  41% {
    height: 0%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  100% {
    height: 100%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}
@keyframes leftBorderAnimation {
  0% {
    height: 0%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  40% {
    height: 100%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  41% {
    height: 0%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  100% {
    height: 100%;
    bottom: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}
/* Right border animation (top to bottom, once) */
@-webkit-keyframes rightBorderAnimation {
  0% {
    height: 0%;
    top: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  100% {
    height: 100%;
    top: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}
@keyframes rightBorderAnimation {
  0% {
    height: 0%;
    top: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
  100% {
    height: 100%;
    top: 0;
    -webkit-transform: scaleX(1);
            transform: scaleX(1);
  }
}
@-webkit-keyframes leftBorderSlideUp {
  60% {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  40% {
    height: 100%;
    bottom: 0;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  41% {
    height: 0%;
    bottom: 0;
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes leftBorderSlideUp {
  60% {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  40% {
    height: 100%;
    bottom: 0;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  41% {
    height: 0%;
    bottom: 0;
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@-webkit-keyframes rightBorderSlideDown {
  0% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes rightBorderSlideDown {
  0% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
.button {
  --text-hover: #FFFFFF;
  --bg: #EF3E34;
  --bg-hover: #16212F;
  --border: var(--bg);
  --border-hover: var(--bg-hover);
  display: inline-block;
  width: auto;
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: var(--line-height-thin);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  overflow: hidden;
  padding: 18px 30px;
  -webkit-box-shadow: none;
  box-shadow: none;
  text-shadow: none;
  -webkit-transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, opacity 0.3s ease 0s, color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s, border-color 0.3s ease 0s, opacity 0.3s ease 0s, color 0.3s ease 0s;
  --text: #EF3E34;
  --border-color: #000000;
  --border-color-light: #FFFFFF;
  --border-color-dark: #000000;
  position: relative;
  padding: 15px 30px 12px;
  background: transparent;
  border: none;
  color: var(--text);
  text-transform: uppercase;
}
.button .fa,
.button .far,
.button .fab,
.button .fas,
.button span.mailto::before,
.button span.phone::before {
  line-height: 1;
  margin-right: 8px;
}
.button span.phone::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f3cd";
}
.button span.mailto::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f0e0";
}
.button.small {
  padding-block: 13px;
  padding-block: clamp(13px, 0.33389vw + 10.43573px, 15px);
  padding-inline: 15px;
  padding-inline: clamp(15px, 0.83472vw + 8.58932px, 20px);
}
.button.simple {
  --border: #B3C6BB;
  --bg: #FFFFFF;
  --text: #EF3E34;
  --bg-hover: #EF3E34;
  --text-hover: #FFFFFF;
  font-weight: 700;
  padding-block: 13px;
  padding-block: clamp(13px, 0.33389vw + 10.43573px, 15px);
  padding-inline: 15px;
  padding-inline: clamp(15px, 0.83472vw + 8.58932px, 20px);
  text-transform: none;
}
.button.light,
#main-navigation {
  --bg: #FFFFFF;
  --text: #72AA32;
}
.button.outline,
#page-hero .page-buttons .button,
#slideshow .slide-wrapper .slide-content .slide-buttons .button ~ .button {
  --border: #B3C6BB;
  --bg: transparent;
  --text: #FFFFFF;
}
.button.hover-light,
#page-hero .page-buttons .button,
#slideshow .slide-wrapper .slide-content .slide-buttons .button {
  --bg-hover: #FFFFFF;
  --text-hover: #72AA32;
}
.button.hover-theme4 {
  --bg-hover: #72AA32;
  --text-hover: #FFFFFF;
}
.button:disabled {
  background-color: #EEEEEE;
  border-color: #EEEEEE;
  color: #CCCCCC;
  cursor: default;
}
.button.normal {
  border: 3px solid var(--border-color);
}
.button.primary {
  --border-width: 3px;
  position: relative;
  color: #000000;
  border-left: none;
  border-right: none;
}
.button.primary .left-border,
.button.primary .right-border {
  position: absolute;
  top: 0;
  bottom: 0;
  width: var(--border-width);
  background-color: var(--border-color);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.button.primary .left-border {
  left: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
}
.button.primary .right-border {
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
}
.button.primary .left-border,
.button.primary .right-border {
  background: none;
  border-left: var(--border-width) solid var(--border-color);
}
.button.primary::before,
.button.primary::after {
  content: '';
  position: absolute;
  width: 10%;
  height: var(--border-width);
  background-color: var(--border-color);
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
  will-change: width, transform;
}
.button.primary::before {
  top: 0;
  left: 0;
  -webkit-transform-origin: left;
          transform-origin: left;
}
.button.primary::after {
  bottom: 0;
  right: 0;
  -webkit-transform-origin: right;
          transform-origin: right;
}
.button.primary:hover,
.button.primary:focus {
  --border-color: #EF3E34;
  color: #EF3E34;
}
.button.primary:hover::before,
.button.primary:focus::before {
  width: 0%;
  -webkit-animation: slideInTopBorderContinue 0.3s ease 0.3s forwards;
          animation: slideInTopBorderContinue 0.3s ease 0.3s forwards;
}
.button.primary:hover::after,
.button.primary:focus::after {
  width: 0%;
  -webkit-animation: slideInBottomBorderDoubleContinue 0.3s ease 0.3s forwards;
          animation: slideInBottomBorderDoubleContinue 0.3s ease 0.3s forwards;
}
.button.primary:not(:hover):not(:focus)::before,
.button.primary:not(:hover):not(:focus)::after {
  -webkit-animation: none;
          animation: none;
  width: 10%;
  -webkit-transition: width 0.3s ease;
  transition: width 0.3s ease;
}
.button.primary.light {
  --border-color-light: #FFFFFF;
  --border-color: #FFFFFF;
  color: #FFFFFF;
}
.button.primary.light .left-border,
.button.primary.light .right-border {
  background-color: #FFFFFF;
  color: #FFFFFF;
}
.button.primary.black {
  --border-color-light: #000000;
  --border-color: #000000;
  color: #000000;
}
.button.primary.black .left-border,
.button.primary.black .right-border {
  background-color: #000000;
  color: #000000;
}
.button.primary.red {
  --border-color-light: #EF3E34;
  --border-color: #EF3E34;
  color: #EF3E34;
}
.button.primary.red .left-border,
.button.primary.red .right-border {
  background-color: #000000;
  color: #000000;
}
.button.secondary {
  --text-color: #EF3E34;
  padding: 10px 20px 10px 0;
  font-size: 18px;
  letter-spacing: -20;
  border: none;
  background: transparent;
  color: var(--text-color);
  position: relative;
  display: inline-block;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  border-left: none;
  border-right: none;
}
.button.secondary::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10px;
  width: 20px;
  height: 3px;
  background-color: var(--text-color);
  -webkit-transition: width 0.2s ease-in-out;
  transition: width 0.2s ease-in-out;
}
.button.secondary::after {
  content: '>';
  display: inline-block;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  opacity: 0;
  font-size: 18px;
  font-family: "Poppins", sans-serif;
  line-height: 1;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.button.secondary:hover {
  -webkit-transform: translateX(-8px);
          transform: translateX(-8px);
}
.button.secondary:hover::before {
  width: calc(100% - 31px);
}
.button.secondary:hover::after {
  opacity: 1;
  right: 0;
}
.button.secondary.light {
  --text-color: #FFFFFF;
}
.button.ternary {
  --border-width: 3px;
  position: relative;
  color: #000000;
  border-left: none;
  border-right: none;
}
.button.ternary .top-border,
.button.ternary .bottom-border {
  position: absolute;
  width: var(--border-width);
  background-color: var(--border-color-dark);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  opacity: 0;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.button.ternary .top-border {
  top: 0;
  left: 0;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.button.ternary .bottom-border {
  bottom: 0;
  right: 0;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.button.ternary .top-border,
.button.ternary .bottom-border {
  background: none;
  border-left: var(--border-width) solid var(--border-color-dark);
}
.button.ternary .left-border,
.button.ternary .right-border {
  position: absolute;
  width: var(--border-width);
  height: 100%;
  background-color: var(--border-color-dark);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  visibility: hidden;
}
.button.ternary .left-border {
  top: 0;
  left: 0;
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
}
.button.ternary .right-border {
  top: 0;
  right: 0;
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
}
.button.ternary::before,
.button.ternary::after {
  content: '';
  position: absolute;
  width: 40%;
  height: var(--border-width);
  background-color: var(--border-color-dark);
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
  will-change: width, transform;
}
.button.ternary::before {
  top: 0;
  left: 0;
  -webkit-transform-origin: left;
          transform-origin: left;
}
.button.ternary::after {
  bottom: 0;
  right: 0;
  -webkit-transform-origin: right;
          transform-origin: right;
}
.button.ternary:hover,
.button.ternary:focus {
  --border-color-light: #000;
  color: #000;
}
.button.ternary:hover::before,
.button.ternary:focus::before {
  width: 65%;
  -webkit-animation: slideInTopBorderContinue 0.1s ease 0.1s forwards;
          animation: slideInTopBorderContinue 0.1s ease 0.1s forwards;
}
.button.ternary:hover::after,
.button.ternary:focus::after {
  width: 65%;
  -webkit-animation: slideInBottomBorderDoubleContinue 0.2s ease 0.2s forwards;
          animation: slideInBottomBorderDoubleContinue 0.2s ease 0.2s forwards;
}
.button.ternary:hover .left-border,
.button.ternary:focus .left-border {
  opacity: 1;
  visibility: visible;
  -webkit-animation: leftBorderSlideUp 0.3s ease forwards;
          animation: leftBorderSlideUp 0.3s ease forwards;
}
.button.ternary:hover .right-border,
.button.ternary:focus .right-border {
  opacity: 1;
  visibility: visible;
  -webkit-animation: rightBorderSlideDown 0.3s ease forwards;
          animation: rightBorderSlideDown 0.3s ease forwards;
}
.button.ternary:not(:hover):not(:focus) {
  border-left: none;
  border-right: none;
  border-color: #000;
}
.button.ternary:not(:hover):not(:focus)::before,
.button.ternary:not(:hover):not(:focus)::after {
  -webkit-animation: none;
          animation: none;
  width: 65%;
  -webkit-transition: width 0.3s ease;
  transition: width 0.3s ease;
}
.button.ternary:not(:hover):not(:focus) .left-border,
.button.ternary:not(:hover):not(:focus) .right-border {
  opacity: 0;
  visibility: hidden;
  -webkit-transition: opacity 0.3s ease, visibility 0s 0.3s;
  transition: opacity 0.3s ease, visibility 0s 0.3s;
}
.button.ternary.light {
  --border-color-light: #FFFFFF;
  --border-color: #FFFFFF;
  color: #FFFFFF;
}
.button.ternary.light .left-border,
.button.ternary.light .right-border {
  background-color: #FFFFFF;
  color: #FFFFFF;
}
.button.ternary.light .top-border,
.button.ternary.light .bottom-border {
  border-color: var(--border-color-light);
  background-color: var(--border-color-light);
}
.button.ternary.light:not(:hover):not(:focus)::before,
.button.ternary.light:not(:hover):not(:focus)::after {
  background-color: var(--border-color-light);
}
.button.ternary.light:hover,
.button.ternary.light:focus {
  border-color: var(--border-color-light);
}
.button.ternary.light:hover::before,
.button.ternary.light:focus::before,
.button.ternary.light:hover::after,
.button.ternary.light:focus::after {
  border-color: var(--border-color-light);
  background-color: var(--border-color-light);
}
.panel-text {
  word-break: break-word;
  max-width: var(--text-wrap, 100%);
}
.panel-text .button {
  margin: 0 10px 10px 0;
}
.panel-text [style*="text-align: center;"] .button,
.panel-text .center .button {
  margin-inline: 5px;
}
.panel-text [style*="text-align: right;"] .button,
.panel-text .right .button {
  margin-right: 0;
  margin-left: 10px;
}
.gradient-text {
  color: #16212F;
  background-clip: text;
  background-size: 200% auto;
  -webkit-background-clip: text;
  text-fill-color: transparent;
  -webkit-text-fill-color: transparent;
  background-image: -webkit-gradient(linear, left top, right top, from(#16212F), color-stop(#EF3E34), to(#16212F));
  background-image: linear-gradient(to right, #16212F, #EF3E34, #16212F);
  padding-bottom: 0.15ch;
}
/*------ email template ------*/
body.email-template {
  background: #EEEEEE;
  max-width: none;
}
body.email-template #email-wrapper {
  margin: 0 auto;
}
body.email-template #email-wrapper #email-header,
body.email-template #email-wrapper #email-content,
body.email-template #email-wrapper #email-footer {
  border: 0;
}
body.email-template #email-wrapper #email-header {
  padding: 30px 0;
  background: none;
}
body.email-template #email-wrapper #email-header img {
  display: block;
}
body.email-template #email-wrapper #email-content {
  padding: 0 0 30px;
  background: none;
}
body.email-template #email-wrapper #email-content #email-content-inner {
  background: #FFFFFF;
  padding: 30px 30px 10px;
  border-radius: 10px;
}
body.email-template #email-wrapper #email-footer {
  padding: 20px 0;
  background: none;
  border-top: 1px solid #CCCCCC;
  text-align: center;
}
/*
	animations.less

*/
/*
	animations.less

*/
/*---- animation classes ----*/
.animate {
  --animation-play-state: paused;
}
.animated {
  --animation-play-state: running;
}
/*---- fades ----*/
:root {
  --animation-trans-length: 40px;
}
@-webkit-keyframes fade-in-ltr {
  0% {
    opacity: 0;
    -webkit-transform: translateX(calc(-1 * var(--animation-trans-length)));
    transform: translateX(calc(-1 * var(--animation-trans-length)));
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes fade-in-ltr {
  0% {
    opacity: 0;
    -webkit-transform: translateX(calc(-1 * var(--animation-trans-length)));
    transform: translateX(calc(-1 * var(--animation-trans-length)));
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@-webkit-keyframes fade-out-ltr {
  0% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateX(var(--animation-trans-length));
    transform: translateX(var(--animation-trans-length));
  }
}
@keyframes fade-out-ltr {
  0% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateX(var(--animation-trans-length));
    transform: translateX(var(--animation-trans-length));
  }
}
@-webkit-keyframes fade-in-rtl {
  0% {
    opacity: 0;
    -webkit-transform: translateX(var(--animation-trans-length));
    transform: translateX(var(--animation-trans-length));
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes fade-in-rtl {
  0% {
    opacity: 0;
    -webkit-transform: translateX(var(--animation-trans-length));
    transform: translateX(var(--animation-trans-length));
  }
  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@-webkit-keyframes fade-out-rtl {
  0% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateX(calc(-1 * var(--animation-trans-length)));
    transform: translateX(calc(-1 * var(--animation-trans-length)));
  }
}
@keyframes fade-out-rtl {
  0% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateX(calc(-1 * var(--animation-trans-length)));
    transform: translateX(calc(-1 * var(--animation-trans-length)));
  }
}
@-webkit-keyframes fade-in-ttb {
  0% {
    opacity: 0;
    -webkit-transform: translateY(calc(-1 * var(--animation-trans-length)));
    transform: translateY(calc(-1 * var(--animation-trans-length)));
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes fade-in-ttb {
  0% {
    opacity: 0;
    -webkit-transform: translateY(calc(-1 * var(--animation-trans-length)));
    transform: translateY(calc(-1 * var(--animation-trans-length)));
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@-webkit-keyframes fade-out-ttb {
  0% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(var(--animation-trans-length));
    transform: translateY(var(--animation-trans-length));
  }
}
@keyframes fade-out-ttb {
  0% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(var(--animation-trans-length));
    transform: translateY(var(--animation-trans-length));
  }
}
@-webkit-keyframes fade-in-btt {
  0% {
    opacity: 0;
    -webkit-transform: translateY(var(--animation-trans-length));
    transform: translateY(var(--animation-trans-length));
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes fade-in-btt {
  0% {
    opacity: 0;
    -webkit-transform: translateY(var(--animation-trans-length));
    transform: translateY(var(--animation-trans-length));
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@-webkit-keyframes fade-out-btt {
  0% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(calc(-1 * var(--animation-trans-length)));
    transform: translateY(calc(-1 * var(--animation-trans-length)));
  }
}
@keyframes fade-out-btt {
  0% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(calc(-1 * var(--animation-trans-length)));
    transform: translateY(calc(-1 * var(--animation-trans-length)));
  }
}
@-webkit-keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-webkit-keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/*---- SVG path draw ----*/
@-webkit-keyframes draw {
  0% {
    stroke-dashoffset: var(--draw-length);
  }
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes draw {
  0% {
    stroke-dashoffset: var(--draw-length);
  }
  100% {
    stroke-dashoffset: 0;
  }
}
@-webkit-keyframes draw-reverse {
  0% {
    stroke-dashoffset: var(--draw-length);
  }
  100% {
    stroke-dashoffset: calc(2px * var(--draw-length));
  }
}
@keyframes draw-reverse {
  0% {
    stroke-dashoffset: var(--draw-length);
  }
  100% {
    stroke-dashoffset: calc(2px * var(--draw-length));
  }
}
/*---- slides ----*/
@-webkit-keyframes slide-in-ltr {
  0% {
    -webkit-transform: translateX(calc(-1 * var(--animation-trans-length)));
    transform: translateX(calc(-1 * var(--animation-trans-length)));
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes slide-in-ltr {
  0% {
    -webkit-transform: translateX(calc(-1 * var(--animation-trans-length)));
    transform: translateX(calc(-1 * var(--animation-trans-length)));
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@-webkit-keyframes slide-out-ltr {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(var(--animation-trans-length));
    transform: translateX(var(--animation-trans-length));
  }
}
@keyframes slide-out-ltr {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(var(--animation-trans-length));
    transform: translateX(var(--animation-trans-length));
  }
}
@-webkit-keyframes slide-in-rtl {
  0% {
    -webkit-transform: translateX(var(--animation-trans-length));
    transform: translateX(var(--animation-trans-length));
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes slide-in-rtl {
  0% {
    -webkit-transform: translateX(var(--animation-trans-length));
    transform: translateX(var(--animation-trans-length));
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@-webkit-keyframes slide-out-rtl {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-1 * var(--animation-trans-length)));
    transform: translateX(calc(-1 * var(--animation-trans-length)));
  }
}
@keyframes slide-out-rtl {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-1 * var(--animation-trans-length)));
    transform: translateX(calc(-1 * var(--animation-trans-length)));
  }
}
@-webkit-keyframes slide-in-ttb {
  0% {
    -webkit-transform: translateY(calc(-1 * var(--animation-trans-length)));
    transform: translateY(calc(-1 * var(--animation-trans-length)));
  }
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes slide-in-ttb {
  0% {
    -webkit-transform: translateY(calc(-1 * var(--animation-trans-length)));
    transform: translateY(calc(-1 * var(--animation-trans-length)));
  }
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@-webkit-keyframes slide-out-ttb {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(var(--animation-trans-length));
    transform: translateY(var(--animation-trans-length));
  }
}
@keyframes slide-out-ttb {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(var(--animation-trans-length));
    transform: translateY(var(--animation-trans-length));
  }
}
@-webkit-keyframes slide-in-btt {
  0% {
    -webkit-transform: translateY(var(--animation-trans-length));
    transform: translateY(var(--animation-trans-length));
  }
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes slide-in-btt {
  0% {
    -webkit-transform: translateY(var(--animation-trans-length));
    transform: translateY(var(--animation-trans-length));
  }
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@-webkit-keyframes slide-out-btt {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(calc(-1 * var(--animation-trans-length)));
    transform: translateY(calc(-1 * var(--animation-trans-length)));
  }
}
@keyframes slide-out-btt {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(calc(-1 * var(--animation-trans-length)));
    transform: translateY(calc(-1 * var(--animation-trans-length)));
  }
}
/*---- daneden.github.io - animate.css ----*/
@-webkit-keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}
@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}
@-webkit-keyframes rubber-band {
  0% {
    background-clip: padding-box;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes rubber-band {
  0% {
    background-clip: padding-box;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@-webkit-keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  100% {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
@keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  100% {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
@-webkit-keyframes tada {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%,
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%,
  60%,
  80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes tada {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%,
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%,
  60%,
  80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
/*---- leadin animations ----*/
@media (prefers-reduced-motion: no-preference) {
  .leadin-popup.open {
    --animation-play-state: running;
  }
  .leadin-popup.open.type-corner,
  .leadin-popup.open.type-bar.position-bottom {
    -webkit-animation: fade-up 0.4s ease-out;
    animation: fade-up 0.4s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  .leadin-popup.open .button.bounce {
    -webkit-animation: bounce 0.8s ease-out 0.8s;
    animation: bounce 0.8s ease-out 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  .leadin-popup.open .button.rubber-band {
    -webkit-animation: rubber-band 1s ease-out 0.8s;
    animation: rubber-band 1s ease-out 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  .leadin-popup.open .button.swing {
    -webkit-animation: swing 0.6s ease-out 0.8s;
    animation: swing 0.6s ease-out 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  .leadin-popup.open .button.tada {
    -webkit-animation: tada 1s ease-out 0.8s;
    animation: tada 1s ease-out 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
}
/*---- landing page animations ----*/
@media all and (min-width: 769px) and (prefers-reduced-motion: no-preference) {
  html.no-touch body.landing-page .landing-form-wrapper {
    -webkit-animation: fade-in 0.4s ease-out 0.5s;
    animation: fade-in 0.4s ease-out 0.5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
}
@-webkit-keyframes clip-in-ttb {
  0% {
    clip-path: inset(0 0 100% 0);
  }
  100% {
    clip-path: inset(-100%);
  }
}
@keyframes clip-in-ttb {
  0% {
    clip-path: inset(0 0 100% 0);
  }
  100% {
    clip-path: inset(-100%);
  }
}
@-webkit-keyframes clip-in-ltr {
  0% {
    clip-path: inset(-100% 100% -100% 0);
  }
  100% {
    clip-path: inset(-100% -100% -100% -100%);
  }
}
@keyframes clip-in-ltr {
  0% {
    clip-path: inset(-100% 100% -100% 0);
  }
  100% {
    clip-path: inset(-100% -100% -100% -100%);
  }
}
@media all and (min-width: 769px) and (prefers-reduced-motion: no-preference) {
  :root {
    --animation-trans-length: 20px;
  }
  html.no-touch #slideshow .slide.swiper-slide-active,
  html.no-touch #slideshow .slide:where(#slideshow:not(.swiper-initialized) .slide) {
    --animation-play-state: running;
  }
  html.no-touch #slideshow .slide.swiper-slide-active .slide-title,
  html.no-touch #slideshow .slide:where(#slideshow:not(.swiper-initialized) .slide) .slide-title,
  html.no-touch #slideshow .slide.swiper-slide-active .slide-text,
  html.no-touch #slideshow .slide:where(#slideshow:not(.swiper-initialized) .slide) .slide-text {
    -webkit-animation: fade-in 0.6s ease-out 0.3s;
    animation: fade-in 0.6s ease-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #slideshow .slide.swiper-slide-active .fancy-text,
  html.no-touch #slideshow .slide:where(#slideshow:not(.swiper-initialized) .slide) .fancy-text {
    -webkit-animation: fade-in 0.6s ease-out 0.45s;
    animation: fade-in 0.6s ease-out 0.45s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #slideshow .slide.swiper-slide-active .button:nth-child(1),
  html.no-touch #slideshow .slide:where(#slideshow:not(.swiper-initialized) .slide) .button:nth-child(1) {
    -webkit-animation: fade-in-ltr 0.6s ease-out 0.6s;
    animation: fade-in-ltr 0.6s ease-out 0.6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #slideshow .slide.swiper-slide-active .button:nth-child(2),
  html.no-touch #slideshow .slide:where(#slideshow:not(.swiper-initialized) .slide) .button:nth-child(2) {
    -webkit-animation: fade-in-rtl 0.6s ease-out 0.6s;
    animation: fade-in-rtl 0.6s ease-out 0.6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #slideshow .slide.swiper-slide-active .button:only-child,
  html.no-touch #slideshow .slide:where(#slideshow:not(.swiper-initialized) .slide) .button:only-child {
    -webkit-animation: fade-in-btt 0.6s ease-out 0.6s;
    animation: fade-in-btt 0.6s ease-out 0.6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #page-hero .page-title-wrapper::before {
    -webkit-animation: clip-in-ttb 0.6s ease-in-out 0.3s;
    animation: clip-in-ttb 0.6s ease-in-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #page-hero .page-title,
  html.no-touch #page-hero .page-subtitle {
    -webkit-animation: fade-in-btt 0.6s ease-out;
    animation: fade-in-btt 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #page-hero .fancy-text {
    -webkit-animation: fade-in 0.6s ease-out 0.15s;
    animation: fade-in 0.6s ease-out 0.15s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #page-hero .page-buttons {
    -webkit-animation: fade-in-btt 0.6s ease-out 0.3s;
    animation: fade-in-btt 0.6s ease-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.standard .panel-header h2,
  html.no-touch .panel.promo .panel-header h2,
  html.no-touch .panel.mini-promo .panel-header h2,
  html.no-touch .panel.gallery .panel-header h2 {
    -webkit-animation: fade-in-ltr 0.6s ease-out;
    animation: fade-in-ltr 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.standard .panel-header .fancy-text,
  html.no-touch .panel.promo .panel-header .fancy-text,
  html.no-touch .panel.mini-promo .panel-header .fancy-text,
  html.no-touch .panel.gallery .panel-header .fancy-text {
    -webkit-animation: fade-in 0.6s ease-out 0.15s;
    animation: fade-in 0.6s ease-out 0.15s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.cta .panel-title::before {
    -webkit-animation: clip-in-ttb 0.6s ease-in-out 0.3s;
    animation: clip-in-ttb 0.6s ease-in-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.cta .panel-subtitle {
    -webkit-animation: fade-in-ltr 0.6s ease-out;
    animation: fade-in-ltr 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.cta .button {
    -webkit-animation: fade-in-rtl 0.6s ease-out;
    animation: fade-in-rtl 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.cta:last-child::before {
    -webkit-animation: clip-in-ttb 0.6s ease-in-out 0.3s;
    animation: clip-in-ttb 0.6s ease-in-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.cta:last-child .panel-title h2,
  html.no-touch .panel.cta:last-child .panel-subtitle {
    -webkit-animation: fade-in-btt 0.6s ease-out;
    animation: fade-in-btt 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.cta:last-child .button:nth-child(1) {
    -webkit-animation: fade-in-ltr 0.6s ease-out 0.3s;
    animation: fade-in-ltr 0.6s ease-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.cta:last-child .button:nth-child(2) {
    -webkit-animation: fade-in-rtl 0.6s ease-out 0.3s;
    animation: fade-in-rtl 0.6s ease-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.cta:last-child .button:only-child {
    -webkit-animation: fade-in-btt 0.6s ease-out 0.3s;
    animation: fade-in-btt 0.6s ease-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.parallax::before {
    -webkit-animation: clip-in-ttb 0.6s ease-in-out 0.3s;
    animation: clip-in-ttb 0.6s ease-in-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.parallax .panel-header,
  html.no-touch .panel.parallax .panel-content {
    -webkit-animation: fade-in 0.6s ease-out 0.3s;
    animation: fade-in 0.6s ease-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.parallax .fancy-text {
    -webkit-animation: fade-in 0.6s ease-out 0.45s;
    animation: fade-in 0.6s ease-out 0.45s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.side .panel-content {
    -webkit-animation: fade-in 0.6s ease-out;
    animation: fade-in 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.side .panel-header::before {
    -webkit-animation: clip-in-ttb 0.6s ease-in-out 0.6s;
    animation: clip-in-ttb 0.6s ease-in-out 0.6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.side .fancy-text {
    -webkit-animation: fade-in 0.6s ease-out 0.15s;
    animation: fade-in 0.6s ease-out 0.15s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .promo-box {
    -webkit-animation: fade-in 0.6s ease-out;
    animation: fade-in 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .mini-promo-box {
    -webkit-animation: fade-in 0.6s ease-out;
    animation: fade-in 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .gal-item {
    -webkit-animation: fade-in 0.6s ease-out;
    animation: fade-in 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .content-tabs .tabs-nav::after {
    -webkit-animation: clip-in-ltr 0.6s ease-in-out 0.3s;
    animation: clip-in-ltr 0.6s ease-in-out 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
}
@media all and (min-width: 1025px) and (prefers-reduced-motion: no-preference) {
  html.no-touch #page-hero .page-title,
  html.no-touch #page-hero .page-subtitle {
    -webkit-animation: fade-in-ltr 0.6s ease-out;
    animation: fade-in-ltr 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch #page-hero .page-buttons {
    -webkit-animation: fade-in-rtl 0.6s ease-out;
    animation: fade-in-rtl 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.side .panel-right {
    -webkit-animation: fade-in-ltr 0.6s ease-out;
    animation: fade-in-ltr 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
  html.no-touch .panel.side .panel-left {
    -webkit-animation: fade-in-rtl 0.6s ease-out;
    animation: fade-in-rtl 0.6s ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: var(--animation-play-state, running);
            animation-play-state: var(--animation-play-state, running);
  }
}
body.landing-page #page-hero {
  --content-gap: 40px;
  --column-width: calc((100% - var(--content-gap)) / 2);
  position: relative;
  background: var(--theme-bg);
  text-align: left;
}
body.landing-page #page-hero .page-hero-wrapper {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column;
          flex-flow: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: var(--content-gap);
  padding-block: 30px;
  text-align: inherit;
}
body.landing-page #page-hero #page-hero-image {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
}
body.landing-page #page-hero #page-hero-image .overlay {
  height: 100%;
  max-height: none;
  -webkit-mask-image: none;
          mask-image: none;
  opacity: 0.5;
}
body.landing-page #page-hero #page-header .page-subtitle p:last-child {
  padding-bottom: 0;
}
body.landing-page #page-hero #landing-form {
  --form-columns: 1;
  --field-border: #CCCCCC;
  --field-border-hover: #CCCCCC;
  --field-border-width: 1px;
  --field-placeholder: transparent;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
  background: #FFFFFF;
}
body.landing-page #page-hero #landing-form label {
  text-align: left;
}
body.landing-page #page-hero #landing-form .landing-form-title {
  margin: 0;
}
body.landing-page #page-hero #landing-form .landing-form-title + fieldset,
body.landing-page #page-hero #landing-form .landing-form-description + fieldset {
  margin-top: 20px;
}
@media all and (min-width: 769px) {
  body.landing-page #page-hero #landing-form {
    padding: 30px;
  }
}
@media all and (min-width: 1025px) {
  body.landing-page #page-hero .page-hero-wrapper {
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
            flex-flow: row wrap;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  body.landing-page #page-hero #page-logo {
    width: 100%;
  }
  body.landing-page #page-hero #page-header,
  body.landing-page #page-hero #landing-form {
    width: var(--column-width);
    -webkit-box-flex: 1 0 auto;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
  }
  body.landing-page #page-hero #landing-form {
    max-width: 600px;
  }
}
/*------ utilities ------*/
.container {
  --container-max-width: var(--container-width);
  width: 100%;
  max-width: calc(var(--container-max-width) + var(--container-padding) * 2);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}
.container.container-lg,
#page-hero .container {
  --container-max-width: var(--container-width-lg);
}
.container.container-xl,
#page-navbar .container {
  --container-max-width: var(--container-width-xl);
}
.container .container {
  padding: 0;
}
a.sneaky-link:not(:hover) {
  color: inherit;
}
img.lazy-load:not(.loaded) {
  background-color: #CCCCCC;
}
.responsive-bg {
  --src: var(--bg-src, none);
  --src-tablet-p: var(--bg-src-tablet-p, var(--src));
  --src-tablet-l: var(--bg-src-tablet-l, var(--src-tablet-p));
  --src-notebook: var(--bg-src-notebook, var(--src-tablet-l));
  --src-desktop: var(--bg-src-desktop, var(--src-notebook));
  --pos-desktop: var(--bg-pos, center);
  --pos-mobile: var(--bg-pos-mobile, var(--pos-desktop));
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: var(--pos-mobile);
  z-index: -1;
}
.responsive-bg.visible {
  background-image: var(--src);
}
@media all and (min-width: 481px) {
  .responsive-bg.visible {
    background-image: var(--src-tablet-p);
  }
}
@media all and (min-width: 769px) {
  .responsive-bg.visible {
    background-image: var(--src-tablet-l);
  }
}
@media all and (min-width: 1025px) {
  .responsive-bg.visible {
    background-image: var(--src-notebook);
  }
}
@media all and (min-width: 1367px) {
  .responsive-bg.visible {
    background-image: var(--src-desktop);
  }
}
.touch .responsive-bg {
  background-attachment: scroll !important;
}
@media all and (min-width: 769px) {
  .responsive-bg {
    background-position: var(--pos-desktop);
  }
}
video.video-bg {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: auto;
  display: none;
  -o-object-fit: cover;
     object-fit: cover;
  z-index: -1;
}
@media all and (min-width: 1025px) {
  .no-touch video.video-bg {
    display: block;
  }
}
.theme-theme1,
.overlay.overlay-theme1 {
  --theme-bg: #16212F;
}
.theme-theme2,
.overlay.overlay-theme2 {
  --theme-bg: #EF3E34;
}
.theme-gradient,
.overlay.overlay-gradient {
  --theme-bg: linear-gradient(var(--theme-gradient-deg, 90deg), rgba(22, 33, 47, 0.75), rgba(239, 62, 52, 0.75));
}
.theme-black,
.overlay.overlay-black {
  --theme-bg: #000000;
}
.overlay {
  background: var(--theme-bg, none);
  opacity: 0.5;
  z-index: -1;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  -webkit-transition: background-color 0.3s ease 0s, opacity 0.3s ease 0s;
  transition: background-color 0.3s ease 0s, opacity 0.3s ease 0s;
}
.overlay.solid {
  opacity: 1;
}
.fancy-text {
  font-family: inherit;
  color: #EF3E34;
  font-weight: inherit;
}
.title-decor,
#page-hero .page-title-wrapper,
.panel.standard .panel-header .container,
.panel.promo .panel-header .container,
.panel.mini-promo .panel-header .container,
.panel.gallery .panel-header .container {
  --line-width: 12px;
  --line-width: clamp(12px, 0.89286vw + 2.85714px, 20px);
  --line-gap: 8px;
  --line-gap: clamp(8px, 2.45536vw - 17.14286px, 30px);
  --line-top: -30px;
  --line-bottom: 0;
  --line-left: calc((var(--line-width) + var(--line-gap)) * -1);
  position: relative;
}
/*------ navigation ------*/
.nav-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.nav-menu li,
.nav-menu a {
  position: relative;
  display: block;
}
.nav-menu > ul {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.nav-menu > ul > li {
  -webkit-box-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
.nav-menu ul ul {
  display: none;
  position: absolute;
  white-space: normal;
  background-color: #16212F;
  -webkit-box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.16);
  box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.16);
}
.nav-menu ul ul a {
  width: 250px;
}
.nav-menu .menu-header {
  display: none;
}
.nav-menu .more-icon::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f141";
}
:root {
  --logo-width: 67.48px;
}
:root {
  --logo-width: 85px;
}
#page-logo,
#page-logo img,
#page-logo svg {
  display: inline-block;
  width: var(--logo-width);
  height: auto;
  vertical-align: top;
}
#page-logo svg {
  -webkit-transition: fill 0.3s ease 0s;
  transition: fill 0.3s ease 0s;
}
@media all and (min-width: 1025px) {
  :root {
    --logo-width: 130px;
  }
  #page-logo {
    margin-left: 0px;
    margin-left: clamp(0px, 3.34821vw - 34.28571px, 30px);
  }
}
:root {
  --navbar-height: 85px;
}
#page-navbar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding-top: var(--container-padding);
  z-index: 100;
  margin-top: 50px;
  margin-top: clamp(50px, 18.38235vw - 38.23529px, 150px);
}
#page-navbar .navbar-wrapper {
  padding-block: 10px;
  padding-block: clamp(10px, 0.91912vw + 5.58824px, 15px);
  padding-inline: 10px;
  padding-inline: clamp(10px, 1.83824vw + 1.17647px, 20px);
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 20px;
  border-radius: 3px;
  background-color: transparent;
}
#page-navbar .navbar-menu {
  -webkit-box-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}
#page-navbar.sticky {
  margin-top: 0;
}
#page-navbar.theme-transparent .navbar-wrapper,
#page-navbar.sticky .navbar-wrapper {
  padding-inline: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
#page-navbar.theme-transparent #page-logo svg,
#page-navbar.sticky #page-logo svg {
  fill: #FFFFFF;
}
#page-navbar.theme-transparent .navbar-wrapper {
  background-color: transparent;
}
#page-navbar.theme-transparent:not(.sticky) {
  --logo-width: 96.42px;
}
#page-navbar.theme-transparent:not(.sticky) .navbar-wrapper {
  padding: 0;
}
body:has(#page-hero) #page-navbar {
  margin-top: 0;
}
body:has(#page-hero.noimage) #page-navbar {
  margin-top: 150px;
}
@media all and (max-width: 768px) {
  body:has(#page-hero.noimage) #page-navbar {
    margin-top: 100px;
  }
}
@media all and (max-width: 480px) {
  body:has(#page-hero.noimage) #page-navbar {
    margin-top: 75px;
  }
}
body:has(#page-hero.noimage) #page-navbar.sticky {
  margin-top: 0;
}
body:has(#slideshow) #page-navbar {
  margin-top: 0;
}
@media all and (min-width: 1025px) {
  :root {
    --navbar-height: 145px;
  }
  #page-navbar {
    background-color: transparent;
    -webkit-transition: background-color 0.3s ease 0s;
    transition: background-color 0.3s ease 0s;
  }
  #page-navbar.theme-transparent #page-logo,
  #page-navbar.sticky #page-logo {
    margin-left: 0;
  }
  #page-navbar.theme-transparent:not(.sticky) {
    --logo-width: 153.2px;
  }
  #page-navbar.theme-transparent:not(.sticky) .container {
    position: relative;
  }
  #page-navbar.theme-transparent:not(.sticky) #page-logo img,
  #page-navbar.theme-transparent:not(.sticky) #page-logo svg {
    position: absolute;
    top: 50%;
    left: var(--container-padding);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  #page-navbar.sticky {
    position: fixed;
    height: auto;
    padding: 0;
    background-color: #16212F;
    border-bottom: 1px solid #FFFFFF;
    -webkit-box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.16);
    box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.16);
    -webkit-transition: transform 0.3s ease 0s, background-color 0.3s ease 0s, box-shadow 0.3s ease 0s;
    -webkit-transition: background-color 0.3s ease 0s, -webkit-transform 0.3s ease 0s, -webkit-box-shadow 0.3s ease 0s;
    transition: background-color 0.3s ease 0s, -webkit-transform 0.3s ease 0s, -webkit-box-shadow 0.3s ease 0s;
    transition: transform 0.3s ease 0s, background-color 0.3s ease 0s, box-shadow 0.3s ease 0s;
    transition: transform 0.3s ease 0s, background-color 0.3s ease 0s, box-shadow 0.3s ease 0s, -webkit-transform 0.3s ease 0s, -webkit-box-shadow 0.3s ease 0s;
  }
  #page-navbar.sticky .navbar-wrapper {
    margin-top: 0;
    background-color: transparent;
  }
  #page-navbar.sticky #page-contact-top {
    -webkit-transform: translateY(-50px);
    transform: translateY(-50px);
    opacity: 0;
    height: 0;
    margin: 0;
  }
  #page-navbar.hide {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
  }
}
#main-navigation {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  z-index: 200;
}
@media all and (max-width: 1024px) {
  #main-navigation {
    display: none !important;
  }
}
#main-navigation ul {
  gap: 5px;
}
#main-navigation a,
#main-navigation .more-icon,
#main-navigation li.highlight > a {
  padding: 12px 20px;
}
#main-navigation a,
#main-navigation .more-icon {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  line-height: var(--line-height-thin);
  font-weight: 700;
  text-transform: uppercase;
}
#main-navigation a {
  color: #FFFFFF;
}
#main-navigation .more-icon {
  color: #EF3E34;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
}
#main-navigation a:hover,
#main-navigation a:focus,
#main-navigation .more-icon:hover,
#main-navigation .more-icon:focus,
#main-navigation li.active:not(.highlight) > a {
  color: #EF3E34;
}
#main-navigation li.highlight > a {
  background-color: #72AA32;
  color: #FFFFFF;
}
#main-navigation li.highlight > a:hover,
#main-navigation li.highlight > a:focus {
  background-color: #16212F;
}
#main-navigation li.highlight.active > a {
  background-color: #16212F;
}
#main-navigation > ul > li.highlight > a {
  border-radius: 3px;
}
:where(.sticky, .theme-transparent) #main-navigation li.highlight.active > a,
:where(.sticky, .theme-transparent) #main-navigation li.highlight > a:hover,
:where(.sticky, .theme-transparent) #main-navigation li.highlight > a:focus {
  background-color: #FFFFFF;
  color: #72AA32;
}
:where(.sticky) #main-navigation > ul > li:not(.highlight) > a,
:where(.sticky) #main-navigation > ul > li:not(.highlight) .more-icon {
  color: #B3C6BB;
}
:where(.sticky) #main-navigation > ul > li:not(.highlight) > a:hover,
:where(.sticky) #main-navigation > ul > li:not(.highlight) > a:focus,
:where(.sticky) #main-navigation > ul > li:not(.highlight).active > a {
  color: #FFFFFF;
}
:where(.theme-transparent) #main-navigation > ul > li:not(.highlight) > a,
:where(.theme-transparent) #main-navigation > ul > li:not(.highlight) .more-icon {
  color: #DDDDDD;
}
:where(.theme-transparent) #main-navigation > ul > li:not(.highlight) > a:hover,
:where(.theme-transparent) #main-navigation > ul > li:not(.highlight) > a:focus,
:where(.theme-transparent) #main-navigation > ul > li:not(.highlight) .more-icon:hover,
:where(.theme-transparent) #main-navigation > ul > li:not(.highlight) .more-icon:focus,
:where(.theme-transparent) #main-navigation > ul > li:not(.highlight).active > a {
  color: #FFFFFF;
}
#menu-toggle {
  --bar-height: 2px;
  --bar-gap: 7px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  gap: 7px;
  width: 25px;
  cursor: pointer;
  outline: none;
}
@media all and (min-width: 1025px) {
  #menu-toggle {
    display: none !important;
  }
}
#menu-toggle span {
  display: block;
  width: 100%;
  height: var(--bar-height);
  border-radius: 5px;
  background-color: #FFFFFF;
  -webkit-transition: width 0.3s ease 0s, background-color 0.3s ease 0s, transform 0.3s ease 0s;
  -webkit-transition: width 0.3s ease 0s, background-color 0.3s ease 0s, -webkit-transform 0.3s ease 0s;
  transition: width 0.3s ease 0s, background-color 0.3s ease 0s, -webkit-transform 0.3s ease 0s;
  transition: width 0.3s ease 0s, background-color 0.3s ease 0s, transform 0.3s ease 0s;
  transition: width 0.3s ease 0s, background-color 0.3s ease 0s, transform 0.3s ease 0s, -webkit-transform 0.3s ease 0s;
}
#menu-toggle span:nth-child(1) {
  -webkit-transform-origin: 0% 100%;
          transform-origin: 0% 100%;
}
#menu-toggle.close {
  display: none;
}
#menu-toggle:hover span,
#menu-toggle:focus span {
  background-color: #16212F;
}
#menu-toggle.open:hover span:nth-child(3),
#menu-toggle.open:focus span:nth-child(3) {
  width: 100%;
}
:where(.theme-transparent) #menu-toggle span,
:where(.theme-transparent) #menu-toggle:hover span,
:where(.theme-transparent) #menu-toggle:focus span {
  background-color: #FFFFFF;
}
#mobile-navigation {
  display: none;
}
/*------ header ------*/
#page-contact-top {
  margin-bottom: 20px;
}
@media all and (max-width: 1024px) {
  #page-contact-top {
    display: none !important;
  }
}
#page-contact-top .page-contact {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  gap: 5px 15px;
  color: #FFFFFF;
  line-height: var(--line-height-thin);
}
#page-contact-top .page-contact .label,
#page-contact-top .page-contact .tollfree,
#page-contact-top .page-contact .fax,
#page-contact-top .page-contact .mailto {
  display: none;
}
#page-contact-top .page-contact li::before {
  -webkit-transition: color 0.3s ease 0s;
  transition: color 0.3s ease 0s;
}
#page-contact-top .page-contact li:hover,
#page-contact-top .page-contact li:focus {
  color: #CCCCCC;
}
#page-contact-top .page-contact a {
  color: #FFFFFF;
}
:where(#page-navbar.theme-transparent) #page-contact-top .page-contact {
  color: #CCCCCC;
}
:where(#page-navbar.theme-transparent) #page-contact-top .page-contact li:hover,
:where(#page-navbar.theme-transparent) #page-contact-top .page-contact li:focus {
  color: #FFFFFF;
}
#page-contact-top .page-contact .link,
#page-contact-top .page-contact .search {
  vertical-align: middle;
  padding: 15px 10px;
  font-size: 16px;
  font-size: clamp(16px, 0.33389vw + 13.43573px, 18px);
  color: #FFC302;
  font-weight: 700;
}
#page-contact-top .page-contact .link:hover,
#page-contact-top .page-contact .search:hover,
#page-contact-top .page-contact .link:focus,
#page-contact-top .page-contact .search:focus {
  color: #FFFFFF;
}
#page-contact-top .page-contact .search {
  color: #FFFFFF;
}
@media all and (min-width: 1025px) {
  #page-contact-top {
    -webkit-transition: height 0.3s ease 0s, margin 0.3s ease 0s, opacity 0.3s ease 0s, transform 0.3s ease 0s;
    -webkit-transition: height 0.3s ease 0s, margin 0.3s ease 0s, opacity 0.3s ease 0s, -webkit-transform 0.3s ease 0s;
    transition: height 0.3s ease 0s, margin 0.3s ease 0s, opacity 0.3s ease 0s, -webkit-transform 0.3s ease 0s;
    transition: height 0.3s ease 0s, margin 0.3s ease 0s, opacity 0.3s ease 0s, transform 0.3s ease 0s;
    transition: height 0.3s ease 0s, margin 0.3s ease 0s, opacity 0.3s ease 0s, transform 0.3s ease 0s, -webkit-transform 0.3s ease 0s;
  }
}
#page-hero {
  position: relative;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-height: 500px;
  min-height: clamp(500px, 30.51471vw + 353.52941px, 666px);
  background: url('/images/svg/banner.svg') no-repeat center center;
  background-position: center center;
  z-index: -1;
  background-size: cover;
  min-height: 700px;
  margin-left: calc(-50vw + 50%);
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  overflow-x: hidden;
}
#page-hero .page-hero-wrapper {
  width: 100%;
}
#page-hero #page-header {
  margin-top: 55px;
  margin-top: clamp(55px, 8.27206vw + 15.29412px, 100px);
}
#page-hero .container {
  position: relative;
}
#page-hero .page-title-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  text-shadow: 0 0 25px rgba(0, 0, 0, 0.25);
}
#page-hero .page-title-wrapper h1 {
  margin-bottom: 0;
}
#page-hero .page-title-wrapper .fancy-text {
  color: #EF3E34;
}
#page-hero .page-title-wrapper .page-title {
  color: #FFFFFF;
}
#page-hero .page-title-wrapper .page-subtitle {
  margin-top: 5px;
  font-family: "Lora", sans-serif;
  font-weight: 800;
  color: #FFFFFF;
  line-height: var(--line-height-normal);
  -webkit-box-ordinal-group: 0;
      -ms-flex-order: -1;
          order: -1;
}
#page-hero .page-buttons {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 10px;
  margin-top: 20px;
  margin-top: 15px;
  margin-top: clamp(15px, 0.83472vw + 8.58932px, 20px);
}
#page-hero.noimage {
  margin-top: -80px;
  margin-top: clamp(-150px, -12.86765vw - 18.23529px, -80px);
  min-height: 550px;
  min-height: clamp(550px, 40.12346vw + 269.1358px, 680px);
  padding: calc(var(--navbar-height) + 40px) 0 40px;
}
#page-hero.noimage .page-title-wrapper {
  text-shadow: none;
}
@media all and (min-width: 1025px) {
  #page-hero .container {
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
            flex-flow: row nowrap;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: stretch;
        -ms-flex-align: stretch;
            align-items: stretch;
    gap: 40px;
  }
  #page-hero .page-title-wrapper {
    width: 65%;
  }
  #page-hero .page-buttons {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-flow: column nowrap;
            flex-flow: column nowrap;
    -webkit-box-align: stretch;
        -ms-flex-align: stretch;
            align-items: stretch;
    max-width: 25%;
  }
}
#slideshow {
  position: relative;
  overflow: hidden;
}
#slideshow .slide-wrapper {
  position: relative;
  min-height: 100vh;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column;
          flex-flow: column;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
#slideshow .slide-wrapper .slide-media {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  background-color: #DDDDDD;
}
#slideshow .slide-wrapper .slide-media .overlay {
  z-index: 1;
}
#slideshow .slide-wrapper .slide-content {
  position: relative;
  --padding-block: 70px;
  --padding-block: clamp(70px, 5.00835vw + 31.53589px, 100px);
  width: 40%;
  padding: calc(var(--navbar-height) + var(--padding-block)) 0 var(--padding-block);
  text-align: center;
  margin: auto;
  z-index: 1;
}
@media all and (max-width: 1024px) {
  #slideshow .slide-wrapper .slide-content {
    width: 65%;
  }
}
@media all and (max-width: 768px) {
  #slideshow .slide-wrapper .slide-content {
    width: 75%;
  }
}
#slideshow .slide-wrapper .slide-content .slide-title,
#slideshow .slide-wrapper .slide-content .slide-text {
  text-shadow: 0 0 25px rgba(0, 0, 0, 0.25);
}
#slideshow .slide-wrapper .slide-content .slide-title h2 {
  color: #FFFFFF;
  margin-bottom: 0;
}
#slideshow .slide-wrapper .slide-content .slide-title .fancy-text {
  color: inherit;
}
#slideshow .slide-wrapper .slide-content .slide-text {
  font-family: "Poppins", sans-serif;
  margin-top: 10px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: var(--line-height-thin);
}
#slideshow .slide-wrapper .slide-content .slide-buttons {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 10px;
  margin-top: 40px;
}
#slideshow .slideshow-navigation {
  display: none;
}
#slideshow .slideshow-pagination {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: absolute;
  bottom: 0;
  padding-block: 20px;
  padding-block: clamp(20px, 5.00835vw - 18.46411px, 50px);
  z-index: 1;
}
#slideshow .slideshow-pagination .swiper-pagination-bullet {
  width: 12px;
  width: clamp(12px, 0.50083vw + 8.15359px, 15px);
  height: 12px;
  height: clamp(12px, 0.50083vw + 8.15359px, 15px);
  margin: 5px 2.5px;
  background: #DDDDDD;
  opacity: 0.5;
  -webkit-transition: background-color 0.3s ease 0s, opacity 0.3s ease 0s;
  transition: background-color 0.3s ease 0s, opacity 0.3s ease 0s;
}
#slideshow .slideshow-pagination .swiper-pagination-bullet-active,
#slideshow .slideshow-pagination .swiper-pagination-bullet:hover,
#slideshow .slideshow-pagination .swiper-pagination-bullet:focus {
  background: #B3C6BB;
  opacity: 0.75;
}
#slideshow:not(.swiper-initialized) .slide ~ .slide {
  display: none;
}
#breadcrumbs {
  margin-block: 10px;
  margin-block: clamp(10px, 1.66945vw - 2.82137px, 20px);
  font-family: "Lora", sans-serif;
  color: #999999;
  line-height: var(--line-height-normal);
}
#breadcrumbs li {
  display: inline-block;
  vertical-align: top;
}
#breadcrumbs a {
  color: inherit;
}
#breadcrumbs a:hover,
#breadcrumbs a:focus {
  color: #666666;
}
#breadcrumbs .arrow {
  padding: 0 2.5px;
}
/*------ body content ------*/
.panel {
  --panel-margin: 30px;
  --panel-margin: clamp(30px, 3.3389vw + 4.35726px, 50px);
  --panel-margin-sm: 30px;
  --panel-margin-sm: clamp(30px, 1.66945vw + 17.17863px, 40px);
  margin-top: var(--panel-margin);
}
.panel:first-child {
  margin-top: var(--panel-margin-sm);
}
.panel:last-child {
  margin-bottom: var(--panel-margin-sm);
}
.panel h1 {
  font-weight: 700;
  color: #EF3E34;
  line-height: var(--line-height-normal);
}
.panel.standard .panel-header,
.panel.promo .panel-header,
.panel.mini-promo .panel-header,
.panel.gallery .panel-header {
  margin: 0 0 20px;
}
.panel.standard .panel-header .container,
.panel.promo .panel-header .container,
.panel.mini-promo .panel-header .container,
.panel.gallery .panel-header .container {
  --line-left: calc((var(--line-width) + var(--line-gap) - var(--container-padding)) * -1);
}
.panel.standard .panel-header h2,
.panel.promo .panel-header h2,
.panel.mini-promo .panel-header h2,
.panel.gallery .panel-header h2 {
  margin: 0;
}
.panel.standard .panel-text {
  max-width: 775px;
}
.panel.standard .panel-text:has(#register-form),
.panel.standard .panel-text:has(.billing-profiles-list),
.panel.standard .panel-text:has(.invoices-container),
.panel.standard .panel-text:has(.invoice-details-container),
.panel.standard .panel-text:has(.review-payment-container),
.panel.standard .panel-text:has(.tournament-search-container),
.panel.standard .panel-text:has(#tournament-information),
.panel.standard .panel-text:has(#registration_content),
.panel.standard .panel-text:has(.dashboard-container) {
  max-width: 1800px;
}
.panel.standard.has-map {
  position: relative;
  z-index: 2;
}
.panel:where(:not(.standard:first-child)) {
  display: none;
}
.panel-tabs {
  display: none;
}
/*------ forms ------*/
form,
.input,
.select,
.textarea {
  display: none;
}
/*------ leadins ------*/
.leadin-popup {
  display: none;
}
/*------ widgets ------*/
.page-contact {
  list-style: none;
  margin: 0;
  padding: 0;
  line-height: var(--line-height-normal);
}
.page-contact li {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
}
.page-contact li .label {
  line-height: var(--line-height-thin);
  -webkit-box-ordinal-group: 0;
      -ms-flex-order: -1;
          order: -1;
  -webkit-box-flex: 0 0 100%;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
}
.page-contact li::before {
  margin-right: 8px;
  text-align: center;
}
.page-contact li .value {
  -webkit-box-flex: 1 0 1px;
  -ms-flex: 1 0 1px;
  flex: 1 0 1px;
}
.page-contact li.address .line1,
.page-contact li.address .line2,
.page-contact li.address .line3 {
  display: inline-block;
}
.page-contact li.address::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f3c5";
}
.page-contact li.tollfree::before,
.page-contact li.phone::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f3cd";
}
.page-contact li.fax::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f1ac";
}
.page-contact li.mailto::before,
.page-contact li.email::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f0e0";
}
/*------ footer ------*/
#page-footer {
  display: none;
}
