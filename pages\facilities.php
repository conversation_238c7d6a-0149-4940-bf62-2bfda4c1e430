<?php
// pages/facilities.php

// Ensure session is started if not already
if (session_status() === PHP_SESSION_NONE) { session_start(); }

// Make sure data fetched by the module is available
global $path, $db, $Account, $_sitepages, $facilities, $facility; // $facility is for detail
global $total_facilities, $current_page, $limit; // For list pagination

// Define MEMBER_ACCESS (adjust as per your system)
if (!defined('MEMBER_ACCESS')) {
    define('MEMBER_ACCESS', (isset($Account) && $Account->login_status()) ? true : false);
}

// Define image paths
$facility_logo_path_display = (isset($path) ? $path : '/') . 'images/logos/'; // Used for facility logo
$facility_banner_path_display = (isset($path) ? $path : '/') . 'images/heros/'; // Used for facility banner
$member_photo_path_display_thumb = (isset($path) ? $path : '/') . 'images/users/thumbs/'; // For member photos

// echo $facility_logo_path_display;

// echo $_SERVER['DOCUMENT_ROOT'] . $facility_logo_path_display . $facility['logo']; 

// Helper functions (ensure these are available in your included files)
// if (!function_exists('checkmail')) { function checkmail($email){ return filter_var($email, FILTER_VALIDATE_EMAIL); } }
// if (!function_exists('formatPhoneNumber')) { function formatPhoneNumber($num){ /* your logic */ return $num; } }
// if (!function_exists('formatIntlNumber')) { function formatIntlNumber($num){ /* your logic */ return $num; } }
// if (!function_exists('clean_url')) { function clean_url($str){ /* your logic */ return strtolower(preg_replace('/[^a-zA-Z0-9-]+/', '-', trim(trim($str),'-'))); } }

// --- Your Existing Code for LIST VIEW should be here, and will be skipped if $facility is set ---
// For example: if (PAGE_ID == $_sitepages['facilities']['page_id']) { ... list HTML ... }

// ========================================
// == Display Facility Profile Detail View ==
// This block is from your OLD pages/facilities.php
// We will replace its content.
// ========================================
if (PARENT_ID == $_sitepages['facilities']['page_id'] && empty(PAGE_ID) && !empty($facility)) : // Check if $facility is populated by module

    $facility_name_display = htmlspecialchars($facility['facility_name'] ?? 'Facility Details', ENT_QUOTES, 'UTF-8');
    $facility_city_province_display = '';
    if (!empty($facility['city'])) $facility_city_province_display .= htmlspecialchars($facility['city'], ENT_QUOTES, 'UTF-8');
    if (!empty($facility['province'])) {
        global $provinces, $states; // Assuming these are global arrays mapping CODE => Full Name
        $all_regions_map = ($provinces ?? []) + ($states ?? []);
        $province_display_name = $all_regions_map[$facility['province']] ?? $facility['province']; // Use full name if map exists
        if (!empty($facility_city_province_display)) $facility_city_province_display .= ', ';
        $facility_city_province_display .= htmlspecialchars($province_display_name, ENT_QUOTES, 'UTF-8');
    }
?>

<div class="container container-lg facility-detail-page-wrapper"> <?php // Added a main wrapper for detail page styling ?>

    <!-- <div class="facility-detail-header-banner"> -->
        <?php
        // $banner_src_display = null;
        // if (!empty($facility['image']) && file_exists($_SERVER['DOCUMENT_ROOT'] . $facility_banner_path_display . $facility['image'])) {
        //     $banner_src_display = htmlspecialchars($facility_banner_path_display . $facility['image'], ENT_QUOTES, 'UTF-8') . '?t=' . time();
        // }
        ?>
        <?php if ($banner_src_display): ?>
            <!-- <img src="<?php echo $banner_src_display; ?>" alt="<?php echo $facility_name_display; ?> Banner" class="facility-banner-hero-image"> -->
        <?php else: ?>
            <!-- <div class="facility-banner-hero-placeholder"></div> <?php // Placeholder if no banner ?> -->
        <?php endif; ?>
        <!-- <div class="facility-title-overlay">
            <h1><?php // echo $facility_name_display; ?></h1>
            <?php // if (!empty($facility_city_province_display)): ?>
                <p class="facility-location"><?php // echo $facility_city_province_display; ?></p>
            <?php // endif; ?>
        </div> -->
    <!-- </div> -->

    <div class="facility-detail-main-layout"> 
            <div class="facility-detail-logo-wrapper">
            <?php
            $logo_src_display = null;
            if (!empty($facility['logo']) && file_exists($_SERVER['DOCUMENT_ROOT'] . $facility_logo_path_display . $facility['logo'])) {
                $logo_src_display = htmlspecialchars($facility_logo_path_display . $facility['logo'], ENT_QUOTES, 'UTF-8') . '?t=' . time();
            }
            ?>
            <?php if ($logo_src_display): ?>
                <img src="<?php echo $logo_src_display; ?>" alt="<?php echo $facility_name_display; ?> Logo" class="facility-detail-logo-image">
            <?php else: ?>
                <img src="<?php echo $path.'images/pga-icon.jpg'; ?>" alt="<?php echo $facility_name_display; ?> Logo" class="facility-detail-logo-image">
                
            <?php endif; ?>
            </div>
            <!-- nl2br(htmlspecialchars($facility['content'], ENT_QUOTES, 'UTF-8')) -->
            <div class="facility-detail-content">       
                <h4 class="facility-section-title">Club Overview</h4>
                <div class="facility-description-content">
                    <?php echo !empty(trim($facility['content'] ?? '')) ? $facility['content'] : '<p><em>Details coming soon.</em></p>'; ?>
                </div>
                <div class="facility-detail-sidebar-column">
                    <div class="facility-contact-box">
                        <h4 class="facility-sidebar-title">Contact Details</h4>
                        <address>
                            <p class="title">Address</p>
                            <?php if (!empty($facility['address1'])): ?>
                                <?php echo htmlspecialchars($facility['address1'], ENT_QUOTES, 'UTF-8'); ?>
                            <?php endif; ?>
                            <?php if (!empty($facility['address2'])): ?>
                                <?php echo htmlspecialchars($facility['address2'], ENT_QUOTES, 'UTF-8'); ?>
                            <?php endif; ?>
                            <?php
                                $city_d = htmlspecialchars($facility['city'] ?? '', ENT_QUOTES, 'UTF-8');
                                $prov_code_d = $facility['province'] ?? '';
                                $postal_d = htmlspecialchars($facility['postal_code'] ?? '', ENT_QUOTES, 'UTF-8');
                                $prov_name_d = $all_regions_map[$prov_code_d] ?? $prov_code_d; // Use mapped name

                                if (!empty($city_d)) echo $city_d;
                                if (!empty($city_d) && !empty($prov_name_d)) echo ', ';
                                if (!empty($prov_name_d)) echo htmlspecialchars($prov_name_d, ENT_QUOTES, 'UTF-8');
                                if (!empty($postal_d)) echo ' ' . $postal_d;
                                if (!empty($city_d) || !empty($prov_name_d) || !empty($postal_d)) echo '<br>';
                            ?>
                            <?php // Country: echo htmlspecialchars($facility['country'] ?? '', ENT_QUOTES, 'UTF-8'); ?>
                        </address>
                        <?php if (!empty(trim($facility['phone'] ?? ''))): ?>
                            <p class="title">Phone</p><p><a href="tel:<?php echo formatIntlNumber($facility['phone']); ?>"><?php echo formatPhoneNumber($facility['phone']); ?></a></p>
                        <?php endif; ?>
                        <?php // if (checkmail($facility['email'] ?? '')): ?>
                            <!-- <p><strong>Email:</strong> <a href="mailto:<?php echo htmlspecialchars($facility['email'], ENT_QUOTES, 'UTF-8'); ?>"><?php echo htmlspecialchars($facility['email'], ENT_QUOTES, 'UTF-8'); ?></a></p> -->
                        <?php // endif; ?>
                        <?php if (!empty(trim($facility['website'] ?? '')) && $facility['website'] !== 'http://' && $facility['website'] !== 'https://'): ?>
                            <?php $website_url_f = strpos($facility['website'], '://') === false ? 'http://' . $facility['website'] : $facility['website']; ?>
                            <p class="title">Website</p><p><a href="<?php echo htmlspecialchars($website_url_f, ENT_QUOTES, 'UTF-8'); ?>" target="_blank" rel="noopener noreferrer"><?php echo htmlspecialchars($facility['website'], ENT_QUOTES, 'UTF-8'); ?></a></p>
                        <?php endif; ?>

                        <?php
                            $social_links = [
                                'instagram' => $facility->instagram ?? 'i',
                                'facebook' => $facility->facebook ?? 'i',
                                'linkedin' => $facility->linkedin ?? 'i',
                                // 'twitter' => $facility->twitter ?? '',
                            ];
                            $has_social = false;
                            foreach($social_links as $link) { if (!empty(trim($link))) { $has_social = true; break; } }
                        ?>
                        
                    </div>
                    <div class="social-icons-container facility-social">
                        <ul class="social-icons profile-social" itemscope itemtype="http://schema.org/Organization">
                            <link itemprop="url" href="<?php echo $siteurl; ?>"> 
                            <?php
                            foreach($social_links as $service=>$url){
                                // if($service == 'facebook' || $service == 'linkedin' || $service == 'instagram'){
                                echo (trim($url) != '' ? '<li><a itemprop="sameAs" href="'.$url.'" target="_blank"><span class="fa-brands fa-'.$service.'"></span>'.$service.'</a></li>' : '');
                                // }
                            }
                        ?>
                        </ul>
                    </div>
            </div>
        </div>
    </div>

    <?php // echo "<pre>"; print_r($facility);?>

    <?php // Associated Members Section ?>
    <?php if (!empty($facility['members']) && is_array($facility['members'])): ?>
        <div class="facility-members-section">
            <!-- <h4 class="facility-section-title">Professionals at <?php // echo $facility_name_display; ?></h4> -->
            <div class="members-grid">
                <?php foreach ($facility['members'] as $member):
                    $member_name_display = htmlspecialchars(($member['first_name'] ?? '') . ' ' . ($member['last_name'] ?? ''), ENT_QUOTES, 'UTF-8');
                    // Ensure your $_sitepages['directory']['page_url'] is correctly defined
                    $member_profile_url_display = ($_sitepages['directory']['page_url'] ?? $path.'directory/') . clean_url($member['first_name'].'-'.$member['last_name'].'-'.$member['profile_id']) . '/';
                    $member_photo_src_display = null;
                    if (!empty($member['photo']) && file_exists($_SERVER['DOCUMENT_ROOT'] . $member_photo_path_display_thumb . $member['photo'])) {
                        $member_photo_src_display = htmlspecialchars($member_photo_path_display_thumb . $member['photo'], ENT_QUOTES, 'UTF-8') . '?t=' . time();
                    }
                ?>
                <div class="member-card">
                    <a href="<?php echo htmlspecialchars($member_profile_url_display, ENT_QUOTES, 'UTF-8'); ?>">
                        <?php if($member_photo_src_display): ?>
                            <img src="<?php echo $member_photo_src_display; ?>" alt="<?php echo $member_name_display; ?>" class="member-photo">
                        <?php else: ?>
                            <!-- <div class="member-no-photo"><span><?php echo strtoupper(substr($member['first_name'] ?? ' ', 0, 1) . substr($member['last_name'] ?? ' ', 0, 1)); ?></span></div> -->

                            <img src="<?php echo $path.'images/pga-icon.jpg'; ?>" alt="pga-icon" class="member-photo">
                        <?php endif; ?>
                        <!-- <span class="member-name"><?php echo $member_name_display; ?></span> -->
                        <!-- <span class="member-classification"><?php echo htmlspecialchars($member['class_name'] ?? '', ENT_QUOTES, 'UTF-8'); ?></span> -->
                    </a>
                    <div class="staff-info-box">
                        <div class="staff-info">
                            <h5><?php echo $member_name_display; ?></h5>
                            <small class="member-classification"><?php echo htmlspecialchars($member['class_name'] ?? '', ENT_QUOTES, 'UTF-8'); ?></small>
                        </div>
                        <div class="location-scroll-arrow staff-link location-scroll-right"><a href="<?php echo htmlspecialchars($member_profile_url_display, ENT_QUOTES, 'UTF-8'); ?>"></a></div>
                    </div>

                </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- <div class="facility-detail-back-link">
        <a href="<?php // echo htmlspecialchars($_sitepages['facilities']['page_url'], ENT_QUOTES, 'UTF-8'); ?>" class="button secondary back-button">‹ Back to Facilities List</a>
    </div> -->

</div> <?php // End .facility-detail-page-wrapper ?>

<?php
// This elseif is for the LIST VIEW
elseif (PAGE_ID == $_sitepages['facilities']['page_id']):
    $search_term = trim($_GET['search'] ?? '');
?>
<?php // ... Your existing and working facilities LIST HTML ... ?>
<?php // Make sure to use $facilities (plural) for the list data ?>
    
<div class="container container-lg"> <?php // Main container for the page content ?>
    <h3><?php echo htmlspecialchars($page['page_title'] ?? 'Facilities', ENT_QUOTES, 'UTF-8'); ?></h3>

    <?php // Search Form ?>
    <form name="search-form" id="directory-search-bar" <?php // Reused ID, consider facility-search-bar ?>
          action="<?php echo htmlspecialchars($_sitepages['facilities']['page_url'], ENT_QUOTES, 'UTF-8'); ?>"
          method="get" class="clearfix directory-search-form" <?php // Reused class, consider .facility-search-form ?>
          style="margin-bottom: 20px;">

        <div class="search-input-container">
            <input type="text" name="search" class="input directory-search-input"
                   value="<?php echo htmlspecialchars($search_term, ENT_QUOTES, 'UTF-8'); ?>"
                   placeholder="Facility Search" />

            <?php if (!empty($search_term)): ?>
                <a href="<?php echo htmlspecialchars($_sitepages['facilities']['page_url'], ENT_QUOTES, 'UTF-8'); ?>"
                   title="Clear Search" class="clear-search-btn">×</a>
            <?php endif; ?>

            <button type="submit" class="button search-icon-btn" title="Search">
                <i class="fa fa-search"></i>
            </button>
        </div>
        <button type="submit" class="button primary visually-hidden">Search</button> <?php // Hidden text button ?>
    </form>

    <?php // --- A-Z Filter Bar --- ?>
    <div class="az-filter-bar">
        <ul class="az-filter-list">
            <?php
            // Get current active letter filter from URL (e.g., ?letter=A)
            $current_letter_filter = strtoupper(trim($_GET['letter'] ?? ''));
            $alphabet = range('A', 'Z');
            foreach ($alphabet as $letter):
                // Preserve existing search term if any
                $query_params = [];
                if (!empty($search_term)) {
                    $query_params['search'] = $search_term;
                }
                $query_params['letter'] = $letter; // Add/override letter param
                $letter_url = htmlspecialchars($_sitepages['facilities']['page_url'] . '?' . http_build_query($query_params), ENT_QUOTES, 'UTF-8');
                $is_active = ($current_letter_filter == $letter);
            ?>
                <li>
                    <a href="<?php echo $letter_url; ?>"
                       class="az-filter-letter <?php echo $is_active ? 'active' : ''; ?>"
                       title="Filter by facilities starting with <?php echo $letter; ?>">
                        <?php echo $letter; ?>
                    </a>
                </li>
            <?php endforeach; ?>
            <?php // Optionally, add an "All" or "Clear Filter" button
                $all_query_params = [];
                if (!empty($search_term)) { $all_query_params['search'] = $search_term; }
                $all_url = htmlspecialchars($_sitepages['facilities']['page_url'] . (!empty($all_query_params) ? '?' . http_build_query($all_query_params) : ''), ENT_QUOTES, 'UTF-8');
            ?>
            <li>
                 <a href="<?php echo $all_url; ?>" class="az-filter-letter <?php echo empty($current_letter_filter) ? 'active' : ''; ?>" title="Show All Facilities">All</a>
            </li>
        </ul>
    </div>
    <?php // --- End A-Z Filter Bar --- ?>

    <?php // Facilities Table ?>
    <table cellpadding="10" cellspacing="0" border="0" width="100%" class="directory-table"> <?php // Reused class, consider .facilities-table ?>
        <thead>
            <tr>
                <th class="left">Name</th>
                <th class="left">Region</th>
                <th class="left">Email</th>
                <th class="left">Phone</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($facilities) && is_array($facilities)): ?>
                <?php foreach ($facilities as $facility): ?>
                    <tr>
                        <td>
                            <a href="<?php echo htmlspecialchars($_sitepages['facilities']['page_url'] . ($facility['page'] ?? '') . '-' . ($facility['facility_id'] ?? ''), ENT_QUOTES, 'UTF-8'); ?>/">
                                <?php echo htmlspecialchars($facility['facility_name'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                            </a>
                        </td>
                        <td><?php echo htmlspecialchars($facility['region'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></td>
                        <td>
                            <?php if (checkmail($facility['email'] ?? '')): ?>
                                <a href="mailto:<?php echo htmlspecialchars($facility['email'], ENT_QUOTES, 'UTF-8'); ?>">
                                    <?php echo htmlspecialchars($facility['email'], ENT_QUOTES, 'UTF-8'); ?>
                                </a>
                            <?php else: echo ' '; endif; ?>
                        </td>
                        <td>
                            <?php if (!empty(trim($facility['phone'] ?? ''))): ?>
                                <a href="tel:<?php echo formatIntlNumber($facility['phone']); ?>">
                                    <?php echo formatPhoneNumber($facility['phone']); ?>
                                </a>
                            <?php else: echo ' '; endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="4" class="nobg" style="text-align: center; padding: 20px;">
                        No facilities found<?php echo !empty($search_term) ? ' matching <strong>`' . htmlspecialchars($search_term, ENT_QUOTES, 'UTF-8') . '`</strong>' : ''; ?>.
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <?php

    // --- PAGINATION ---
    // Make sure these variables are set by modules/facilities.php
    // global $total_facilities, $current_page, $limit, $_sitepages;
    if (isset($total_facilities, $current_page, $limit) && $total_facilities > 0 && $limit > 0) {
        $total_pages = ceil($total_facilities / $limit);

        if ($total_pages > 1) {
            $range = 2; // Number of links to show around the current page
            $start_range = max(1, $current_page - $range);
            $end_range = min($total_pages, $current_page + $range);

            // Preserve existing GET parameters (search, letter)
            $query_args = $_GET; // Get all current GET params
            unset($query_args['pg']); // Remove 'pg' as we'll add it specifically for each link

            $base_url = htmlspecialchars($_sitepages['facilities']['page_url'], ENT_QUOTES, 'UTF-8');
    ?>
        <div class="pagination-container">
            <div class="pagination-summary">
                <?php
                    $start_item = (($current_page - 1) * $limit) + 1;
                    $end_item = min($start_item + $limit - 1, $total_facilities);
                    echo "Showing " . $start_item . " - " . $end_item . " of " . $total_facilities . " Results";
                ?>
            </div>
            <ul class="pagination-list">
                <?php // First Page Link ?>
                <?php if ($current_page > 1): ?>
                    <?php $query_args['pg'] = 1; ?>
                    <li><a href="<?php echo $base_url . '?' . http_build_query($query_args); ?>" title="First Page">«</a></li>
                <?php else: ?>
                    <li class="disabled"><span>«</span></li>
                <?php endif; ?>

                <?php // Previous Page Link ?>
                <?php if ($current_page > 1): ?>
                    <?php $query_args['pg'] = $current_page - 1; ?>
                    <li><a href="<?php echo $base_url . '?' . http_build_query($query_args); ?>" title="Previous Page">‹</a></li>
                <?php else: ?>
                    <li class="disabled"><span>‹</span></li>
                <?php endif; ?>

                <?php // Numbered Page Links ?>
                <?php if ($start_range > 1): ?>
                    <?php $query_args['pg'] = 1; ?>
                    <li><a href="<?php echo $base_url . '?' . http_build_query($query_args); ?>">1</a></li>
                    <?php if ($start_range > 2): ?>
                        <li class="disabled"><span>…</span></li>
                    <?php endif; ?>
                <?php endif; ?>

                <?php for ($i = $start_range; $i <= $end_range; $i++): ?>
                    <?php $query_args['pg'] = $i; ?>
                    <?php if ($i == $current_page): ?>
                        <li class="active"><span><?php echo $i; ?></span></li>
                    <?php else: ?>
                        <li><a href="<?php echo $base_url . '?' . http_build_query($query_args); ?>"><?php echo $i; ?></a></li>
                    <?php endif; ?>
                <?php endfor; ?>

                <?php if ($end_range < $total_pages): ?>
                    <?php if ($end_range < $total_pages - 1): ?>
                        <li class="disabled"><span>…</span></li>
                    <?php endif; ?>
                    <?php $query_args['pg'] = $total_pages; ?>
                    <li><a href="<?php echo $base_url . '?' . http_build_query($query_args); ?>"><?php echo $total_pages; ?></a></li>
                <?php endif; ?>

                <?php // Next Page Link ?>
                <?php if ($current_page < $total_pages): ?>
                    <?php $query_args['pg'] = $current_page + 1; ?>
                    <li><a href="<?php echo $base_url . '?' . http_build_query($query_args); ?>" title="Next Page">›</a></li>
                <?php else: ?>
                    <li class="disabled"><span>›</span></li>
                <?php endif; ?>

                <?php // Last Page Link ?>
                <?php if ($current_page < $total_pages): ?>
                    <?php $query_args['pg'] = $total_pages; ?>
                    <li><a href="<?php echo $base_url . '?' . http_build_query($query_args); ?>" title="Last Page">»</a></li>
                <?php else: ?>
                    <li class="disabled"><span>»</span></li>
                <?php endif; ?>
            </ul>
        </div>
<?php
    } // End if ($total_pages > 1)
} // End if (isset($total_facilities ...))
?>

</div> <?php // End .container ?>

<?php
else:
    // Fallback if neither detail nor list (e.g., error or direct access to page file without context)
    if (!isset($facility) || empty($facility)) { // Only show if $facility wasn't populated for detail view
        echo "<p>The facility information could not be displayed. Please use the <a href='" . htmlspecialchars($_sitepages['facilities']['page_url'], ENT_QUOTES, 'UTF-8') . "'>main facilities list</a>.</p>";
    }
endif; // End of main if/elseif for detail vs list view
?>