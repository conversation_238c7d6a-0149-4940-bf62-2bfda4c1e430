<?php

//System files
include("config/config.php");
include("config/database.php");
include("includes/functions.php");
// sanitize_form_data();

// error_reporting(-1);
// ini_set('display_errors', 'on');

//Get vars
$file = (isset($_GET['file']) ? urlencode($_GET['file']) : '');
$dir = (isset($_GET['dir']) ? urlencode($_GET['dir']) : '');

//Check for login
require_once($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Account.class.php");
$Account = new Account();
if($Account->login_status()){
	
	if($dir == 'resources'){
		$access = false;
		$get_file = $db->query("SELECT * FROM `resources` WHERE `file_location` = ?",array($_GET['file']));
		if($get_file && !$db->error()){
			$result = $db->fetch_array();
			$check_file = $result[0];
			
			//check if account committees or board match folder permissions
			//Account folder permissions
			$permission_qry = "";
			$account_committees = array();
			$query = $db->query("SELECT `committee_id` FROM `account_committees` WHERE `account_id` = ?",array($Account->account_id));
			if($query && !$db->error()){
				$committee_result = $db->fetch_array();
				foreach($committee_result as $committee){
					$account_committees[] = $committee['committee_id'];
				}
			}
			
			// if(!empty($account_committees) || $Account->board_member != 0){
			// 	if(!empty($account_committees)){
			// 		$permission_qry .= " AND (`category_id` IN (SELECT category_id FROM resource_category_committees WHERE committee_id IN (".implode(",",$account_committees).")";
			// 		if($Account->board_member != 0){
			// 			$permission_qry .= " UNION SELECT category_id FROM resource_category_boards WHERE board_id = ".$Account->board_member;
			// 		}
			// 		$permission_qry .= ") OR `category_id` NOT IN (SELECT category_id FROM resource_category_committees))";
			// 	} else if($Account->board_member != 0){
			// 		$permission_qry = " AND (`category_id` IN (SELECT category_id FROM resource_category_boards WHERE board_id = ".$Account->board_member.")";
			// 	}
			// } else {
				$permission_qry .= " AND `category_id` NOT IN (SELECT category_id FROM resource_category_committees)";
			// }
			
			//Get folders
			$query = $db->query("SELECT `category_id` FROM `resource_categories` WHERE `showhide` = 0 $permission_qry");
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $row){
					if($check_file['category_id'] == $row['category_id']){
						$access = true;
						break;
					}
				}
			}	
		}
	} //resource permissions access

	// if user is admin then grant all access
	if($Account->account_has_role(1)){
		// echo "admin user";
		$access = true;
	}
	//  else {
	// 	echo "not admin user";
	// }

	//Restrict directory access
	if($dir == 'resumes' || ($dir == 'resources' && $access) || $dir == 'temp' || ($dir == 'attachments' && $Account->account_has_role(1))){
	
		//Serve file
		$filepath = 'docs/'.$dir.'/'.$file;
		if(file_exists($filepath)){

			$filetype = pathinfo($filepath, PATHINFO_EXTENSION);

			header('Content-Type: application/'.$filetype);
			header('Content-Disposition: inline; filename='.$file);
			header('Cache-Control: no-store, no-cache, must-revalidate');
			header('Cache-Control: pre-check=0, post-check=0, max-age=0');
			header('Pragma: anytextexeptno-cache', true);
			header('Cache-control: private');
			header('Expires: 0');

			readfile($filepath); 

		}else{
			die('File not found.');
		}
		
	}else{
		die('Access Denied.');
	}
	
//Not logged in
}else{
	die('Access Denied. Please login.');
}

?>