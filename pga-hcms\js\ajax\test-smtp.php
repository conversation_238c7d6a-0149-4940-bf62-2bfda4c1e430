<?php

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");
require_once "Mail.php";
require_once "Mail/mime.php";

// Send test email using custom creds
if(isset($_POST) && USER_LOGGED_IN){

	$errors = '';
	if (empty($_POST['host'])) {
		$errors .= '<br>- Please enter a valid SMTP host';
	}
	if (empty($_POST['email'])) {
		$errors .= '<br>- Please enter a valid SMTP email';
	}
	if (empty($_POST['password'])) {
		$errors .= '<br>- Please enter a valid SMTP password';
	}
	if ($_POST['xssid'] != $_COOKIE['xssid']) {
		$errors .= '</br>- Please make sure cookies are enabled in your browser then try again';
	}


	if (!$errors) {
		$company_name = str_replace(['.', '&rsquo;'], ['', "'"], $global['company_name']);
		$from  = $company_name.' <'.$_POST['email'].'>';
		$reply = $company_name.' <'.$global['contact_email'].'>';
		$body  = '<p>Testing 1 2 3...</p>';
		$to    = '<EMAIL>'; //$_POST['to'];

		$headers = [
			'From' => $from,
			'To' => $to, 
			'Reply-To' => $reply,
			'Subject' => 'Testing 123',
			'X-Priority' => '3',
			'Importance' => 'Normal',
			'MIME-Version' => '1.0',
			'Content-Type' => 'text/html;charset=UTF-8',
			'Content-Transfer-Encoding' => '8bit'
		];

		@$smtp = Mail::factory('smtp', [
			'host' => $_POST['host'],
			'auth' => true,
			'debug' => true,
			'username' => $_POST['email'],
			'password' => $_POST['password'],
		]);

		ob_start();
		@$mail = $smtp->send($to, $headers, $body);

		// Log debug to console
		echo '<script>console.log('.json_encode(ob_get_clean()).');</script>'; 

		// Log message as alert
		if(@PEAR::isError($mail)){
			$code = $mail->getCode();
			echo $CMSBuilder->mini_alert("<p><b>SMTP Failed".($code ? " [".$code."]" : "").": </b> ".$mail->getMessage()."</p>", false);
		}else{
			echo $CMSBuilder->mini_alert("<p><b>SMTP test successful!</b></p>", true);
		}

	} else {
		echo $CMSBuilder->mini_alert("<p><b>Test Failed: </b> ".$errors."</p>", false);
	}
	
} else {
	echo 'error';
}
	
?>