<?php  
$limit = 100; //Number of emails per page

// Tabs
echo "<div class='page-content'>";
	echo "<div class='tabs tab-ui'>";
		echo "<ul>";
			echo "<li><a href='#statsbyemail' id='stats-tab'>Stats by Email</a></li>";
			echo "<li><a href='#popularurls'>Popular Clicks</a></li>";
		echo "</ul>";

		// Stats by Email
		echo "<div id='statsbyemail' class='nopadding'>";
			echo "<div class='panel-content'>";
				echo "<form id='search-form' method='post' action='#statsbyemail'>";
					echo "<p class='clear'><input type='text' name='search' class='input' value='' placeholder='Search Emails' />";
					echo "<button type='button' class='button'><i class='fa fa-search'></i></button></p>";

					echo "<input type='checkbox' name='searchfilters[]' id='filter_processed' class='checkbox' value='processed' /><label for='filter_processed'>Processed</label>";
					echo "<input type='checkbox' name='searchfilters[]' id='filter_delivered' class='checkbox' value='delivered' /><label for='filter_delivered'>Delivered</label>";
					echo "<input type='checkbox' name='searchfilters[]' id='filter_opens' class='checkbox' value='opens' /><label for='filter_opens'>Opened</label>";
					echo "<input type='checkbox' name='searchfilters[]' id='filter_clicks' class='checkbox' value='clicks' /><label for='filter_clicks'>Clicked</label>";
					echo "<input type='checkbox' name='searchfilters[]' id='filter_complaints' class='checkbox' value='complaints' /><label for='filter_complaints'>Complained</label>";
					echo "<input type='checkbox' name='searchfilters[]' id='filter_unsubscribes' class='checkbox' value='unsubscribes' /><label for='filter_unsubscribes'>Unsubscribes</label>";
				
					echo "<input type='hidden' name='xssid' value='".$_COOKIE['xssid']."' />";	
				echo "</form>";
			echo "</div>";

			echo "<div id='newsletter-table-wrap'>"; 
				// table will be displayed here
			echo "</div>";
		echo "</div>"; // Stats by Email

		// Popular URLs
		echo "<div id='popularurls' class='nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";
				echo "<thead>";
				echo "<th>URLs</th>";
				echo "<th class='center' width='120px'>Clicks <small>(Unique)</small></th>";
				echo "</thead>";

				echo "<tbody>";
					if(!empty($statistics['urls'])) {
						foreach($statistics['urls'] as $stat) {
							$url = explode("utm_source=sendgrid.com", $stat['url']);
							if(substr($url[0], -1) == "?") {
							    $url[0] = substr($url[0], 0, -1);
							}
							echo "<tr>";
								echo "<td><a href='".$url[0]."' target='_blank'>".(strlen($url[0]) > 100 ? wordwrap($url[0], 100, "...<br/>", true) : $url[0])."</a></td>";
								echo "<td class='center'>".($stat['clicks'] > 0 ? $stat['clicks'] : '1')." <small>(".($stat['uniqueClicks'] > 0 ? $stat['uniqueClicks'] : "1").")</small></td>";
							echo "</tr>";
						}
					}
				echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager();
		echo "</div>"; // Popular URLs
	echo "</div>";
echo "</div>"; // Tabs
?>

<script>
	$(function() {
		loadEmailStats(0, <?php echo $limit; ?>);

		// disable form submission
		$(document).on('click', '#statsbyemail #search-form button', function() {
			loadEmailStats(0, <?php echo $limit; ?>);
			return false;
		});		
	});

	function loadEmailStats(start, limit) {
		var el = $('#newsletter-table-wrap');
		var searchterm = $('#search-form input[name="search"]').val();
		var searchfilters = '';
		$('#search-form .checkbox').each(function(){
			if($(this).is(':checked')){
				searchfilters += $(this).val()+'::';
			}
		});

		$(el).html('<table cellpadding="0" cellspacing="0" border="0"><tr><td colspan="9"><p class="stats-loader">Loading...</p></td></tr></table>');
		$.ajax({
			url: '../../js/ajax/emailstats.php',
			data: 'newsletter_id=<?php echo (isset($records_arr[ITEM_ID]['campaign_id']) ? $records_arr[ITEM_ID]['campaign_id'] : 0); ?>&item_id=<?php echo (isset($records_arr[ITEM_ID]['item_id']) ? $records_arr[ITEM_ID]['item_id'] : 0); ?>&limit='+<?php echo $limit; ?>+'&start='+start+'&searchterm='+searchterm+'&searchfilters='+searchfilters+'&xssid=<?php echo $_COOKIE["xssid"]; ?>',
			type: 'POST',
			success: function(data){
				$(el).html(data);
			},
			error: function(){
				$(el).html('<div class="panel-content"><p>Unable to load email stats.</p></div>');
			}
		});
	}

	function loadEmailEvents(email, row){
		if($('#expand-'+row+' div').html() == ''){
			$.ajax({
				url: '../../js/ajax/emailstats.php',
				data: 'newsletter_id=<?php echo (isset($records_arr[ITEM_ID]['campaign_id']) ? $records_arr[ITEM_ID]['campaign_id'] : 0); ?>&item_id=<?php echo (isset($records_arr[ITEM_ID]['item_id']) ? $records_arr[ITEM_ID]['item_id'] : 0); ?>&email='+email+'&xssid=<?php echo $_COOKIE["xssid"]; ?>',
				type: 'POST',
				success: function(data){
					$('#expand-'+row+' div').html(data).slideToggle(300);
				},
				error: function(){
					$('#expand-'+row+' div').html('Unable to load email stats.').slideToggle(300);
				}
			});
		}else{
			$('#expand-'+row+' div').slideToggle(300);	
		}
	}
</script>

<?php

//Sticky footer
echo "<footer id='cms-footer' class='resize'>";		
	echo "<a href='" .PAGE_URL. "' class='cancel'>Cancel</a>";
echo "</footer>";

?>