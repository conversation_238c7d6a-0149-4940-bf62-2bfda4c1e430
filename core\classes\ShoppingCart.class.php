<?php

/*-----------------------------------/
* Extension of Registration.class.php 
* Deals with all functionality associated with registration shopping cart and checkout
* <AUTHOR> Army
* @date		16-05-04
* @file		ShoppingCart.class.php
*/

class ShoppingCart extends Registration{
	
	/*-----------------------------------/
	* @var cartname
	* Assign a session name for the cart
	*/
	private $cartname;
	
	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @return	ShoppingCart	New ShoppingCart object
	*/
	public function __construct($cartname='regcart'){	
		parent::__construct(); 
		$this->cartname = $cartname;
		
		//Set cart session
		if(!isset($_SESSION[$this->cartname]) || empty($_SESSION[$this->cartname])){
			$_SESSION[$this->cartname] = array();
		}
		
		//Load settings
		$this->reg_settings = $this->get_reg_settings();
		if(empty($this->reg_settings)){
			throw new Exception('ShoppingCart::construct - Unable to load settings.');			
		}
		
    }
	
	/*-----------------------------------/
	* Gets cart for current session
	*
	* <AUTHOR> Army
	* @return	Array of shopping cart items
	*/
	public function get_cart(){
		return $_SESSION[$this->cartname];
	}
	
	/*-----------------------------------/
	* Updates specific cart item
	*
	* <AUTHOR> Army
	* @param	$key	Key of selected item in cart array
	* @return	Boolean
	*/
	public function get_cart_item($key){
		if(isset($_SESSION[$this->cartname][$key]) && !empty($_SESSION[$this->cartname][$key])){
			return $_SESSION[$this->cartname][$key];
		}
		return NULL;
	}
	
	/*-----------------------------------/
	* Add item to cart
	*
	* <AUTHOR> Army
	* @param	$data	Array of registration data
	* @return	Boolean
	* @throws	Exception
	*/
	public function add_to_cart($data=array(), $key=NULL){
		if(!empty($data)){
			
			//Updating
			if(!is_null($key) && $key != "" && array_key_exists($key, $_SESSION[$this->cartname])){
				$_SESSION[$this->cartname][$key] = $data;

			//Adding
			}else{
				$key = $data['occurrence_id'];
				$_SESSION[$this->cartname][$key] = $data;
			}
			
			//Calculate totals
			$subtotal = 0;
			foreach($data['attendees'] as $attendee){
				$subtotal += $attendee['ticket_price'];
				
				if(isset($attendee['addons'])){
					foreach($attendee['addons'] as $addon){
						$subtotal += $addon['price_adjustment'];
					}
				}
			}
			
			//Discount
			$subtotal = $subtotal-$data['discount'];
			
			//Fees
			$fees = $data['fees'];
		
			//Taxes
			$taxes = $this->get_taxes($subtotal, 'AB');
			$taxes = $taxes['taxes'];
			$total = $subtotal+$taxes+$fees;
			
			//Set cart item totals
			$_SESSION[$this->cartname][$key]['subtotal'] = $subtotal;
			$_SESSION[$this->cartname][$key]['taxes'] = $taxes;
			$_SESSION[$this->cartname][$key]['fees'] = $fees;
			$_SESSION[$this->cartname][$key]['total'] = $total;
			
		
		}else{
			throw new Exception('ShoppingCart::add_to_cart - No data provided.');	
		}
		
		return false;
	}
	
	/*-----------------------------------/
	* Updates specific cart item
	*
	* <AUTHOR> Army
	* @param	$data	Array of registration data
	* @param	$key	Key of selected item in cart array
	* @return	Boolean
	*/
	public function update_cart_item($data=array(), $key){
		if(array_key_exists($key, $_SESSION[$this->cartname])){
			return $this->add_to_cart($data, $key);
		}else{
			throw new Exception('Cart item not found.');	
		}
		return false;
	}
	
	/*-----------------------------------/
	* Updates specific cart item
	*
	* <AUTHOR> Army
	* @param	$key	Key of selected item in cart array
	* @return	Boolean
	*/
	public function delete_cart_item($key){
		if(isset($_SESSION[$this->cartname][$key])){			
			unset($_SESSION[$this->cartname][$key]);
		}
		return true;
	}
	
	/*-----------------------------------/
	* Clears full cart
	*
	* <AUTHOR> Army
	* @return	Boolean
	*/
	public function clear_cart(){
		$_SESSION[$this->cartname] = array();
		return true;
	}
	
	/*-----------------------------------/
	* Inserts order and cart into database
	*
	* <AUTHOR> Army
	* @param	$params			Array of fields to be inserted
	* Array( [0] =>
	* 		Array(
	* 			'param' => 'first_name', 'value' => 'John', 'required' => true,
	*			'param' => 'last_name', 'value' => 'Smith', 'required' => true
	* 		)
	* )
	* @param	$cart_items		Array of cart item ids to be inserted
	* @returns	Integer			Autoincrement ID
	* @throws	Exception
	*/
	public function insert_registration($params=array(), $cart_items=array()){
		if(is_array($params) && !empty($params) && is_array($cart_items) && !empty($cart_items)){			
			
			$validated = true;
			$sql_params = array();
			$fields = "";
			$values = "";
			
			//Validate parameters
			foreach($params as $param){
				if($param['required'] == true && $param['value'] == ""){
					$validated = false;
					break;
				}
				if($param['value'] != ""){
					$fields .= "`" .$param['param']. "`, ";
					$values .= "?, ";
					$sql_params[] = $param['value'];
				}
			}
			
			//Insert data
			if($validated){
				
				$this->db->new_transaction();
				
				//Insert registration
				$sql_params[] = date("Y-m-d H:i:s");
				$query = $this->db->query("INSERT INTO `reg_registrations`(".$fields."`registration_date`) VALUES(".$values."?)", $sql_params);
				$registration_id = $this->db->insert_id();
				
				//Insert attendees
				foreach($cart_items as $item_id){
					$item = $this->get_cart_item($item_id);
					if(!empty($item) && !empty($item['attendees'])){
						foreach($item['attendees'] as $attendee){
							
							$sql_params = array($registration_id, $item['event_id'], $item['occurrence_id']);
							$fields = "";
							$values = "";
							
							foreach(array_keys($attendee) as $field){
								if(!is_array($attendee[$field])){
									$fields .= "`" .$field. "`, ";
									$values .= "?,";
									$sql_params[] = (trim($attendee[$field]) != "" ? $attendee[$field] : NULL);	
									
								}else{
									if($field == 'attendee_fields'){
										foreach($attendee['attendee_fields'] as $afield=>$avalue){
											$fields .= "`" .$afield. "`, ";
											$values .= "?,";
											$sql_params[] = (trim($avalue) != "" ? $avalue : NULL);
										}
									}
								}
							}
							
							$sql_params[] = date("Y-m-d H:i:s");
							$insert = $this->db->query("INSERT INTO `reg_attendees`(`registration_id`, `event_id`, `occurrence_id`, ".$fields."`date_added`) VALUES(?,?,?,".$values."?)", $sql_params);
							if($insert && !$this->db->error()){
								$attendee_id = $this->db->insert_id();
								
								//Insert partner as linked attendee
								if(isset($attendee['partner']) && is_array($attendee['partner']) && !empty($attendee['partner'])){
									$sql_params = array($registration_id, $item['event_id'], $item['occurrence_id'], $attendee_id);
									$fields = "";
									$values = "";

									foreach(array_keys($attendee['partner']) as $field){
										if(!is_array($attendee['partner'][$field])){
											$fields .= "`" .$field. "`, ";
											$values .= "?,";
											$sql_params[] = (trim($attendee['partner'][$field]) != "" ? $attendee['partner'][$field] : NULL);	
										}
									}

									$sql_params[] = date("Y-m-d H:i:s");
									$insert = $this->db->query("INSERT INTO `reg_attendees`(`registration_id`, `event_id`, `occurrence_id`, `partner_id`, ".$fields."`date_added`) VALUES(?,?,?,?,".$values."?)", $sql_params);					
								}
								
								//Insert addons
								if(isset($attendee['addons']) && !empty($attendee['addons'])){
									foreach($attendee['addons'] as $addon){
											
										$name = $addon['name'];
										$value = $addon['value'];
										$price = $addon['price_adjustment'];

										if($value != ""){
											$sql_params2 = array($attendee_id,$name,$value,$price);
											$insert = $this->db->query("INSERT INTO `reg_attendee_options` (`attendee_id`,`name`, `value`,`price_adjustment`) VALUES(?,?,?,?)",$sql_params2);

											if($insert && !$this->db->error()){
												//successful update
											} else {
												throw new Exception('ShoppingCart::insert_registration - Error inserting attendee addons. '.$this->db->error());
											}		
										}
									}	
								}
								
							}else{
								throw new Exception('ShoppingCart::insert_registration - Error inserting attendee. '.$this->db->error());
							}
						}
												
					}
				}
				
				//Successful
				if(!$this->db->error()){
					$this->db->commit();
					return $registration_id;
				}else{
					throw new Exception('ShoppingCart::insert_registration - '.$this->db->error());
				}
								
			}else{
				throw new Exception('ShoppingCart::insert_registration - Missing parameters. Cannot insert registration.');
			}
			
		}else{
			throw new Exception('ShoppingCart::insert_registration - Incorrect format or missing parameters.');	
		}
		
		return false;
	}
	
	/*-----------------------------------/
	* Updates registration information in database
	*
	* <AUTHOR> Army
	* @param	$registration_id	ID of registration to be updated
	* @param	$params				Array of fields to be updated
	* Array( [0] =>
	* 		Array(
	* 			'param' => 'first_name', 'value' => 'John', 'required' => true,
	*			'param' => 'last_name', 'value' => 'Smith', 'required' => true
	* 		)
	* )
	* @param	$reg_status			New status of registration attendees
	* @returns	True on successful update
	* @throws	Exception
	*/
	public function update_registration($registration_id, $params=array(), $reg_status=NULL){
		if(is_array($params) && !empty($params)){
			
			$validated = true;
			$sql_params = array();
			$query_str = "";
			
			foreach($params as $param){
				if($param['required'] == true && $param['value'] == ""){
					$validated = false;
					break;
				}
				if($param['value'] != ""){
					$query_str .= "`" .$param['param']. "` = ?, ";
					$sql_params[] = $param['value'];
				}
			}
			$query_str = substr($query_str, 0, -2);
			
			if($validated){
			
				$this->db->new_transaction();
				
				//Update registration
				$sql_params[] = $registration_id;
				$query = $this->db->query("UPDATE `reg_registrations` SET " .$query_str. "  WHERE `registration_id` = ?", $sql_params);
				
				//Update attendee status
				if(!is_null($reg_status)){
					$query = $this->db->query("UPDATE `reg_attendees` SET `reg_status` = ? WHERE `registration_id` = ?", array($reg_status, $registration_id));
				}
				
				if(!$this->db->error()){
					$this->db->commit();
					return true;
				}else{
					throw new Exception('ShoppingCart::update_registration - '.$this->db->error());
				}				
				
			}else{
				throw new Exception('ShoppingCart::update_registration - Missing parameters. Cannot update order.');
			}
			
		}else{
			throw new Exception('ShoppingCart::update_registration - Incorrect format or missing parameters.');	
		}
		
		return false;
	}
	
	/*-----------------------------------/
	* Inserts payment information to a given registration
	*
	* <AUTHOR> Army
	* @param	$params			Array of fields to be inserted
	* Array( [0] =>
	* 		Array(
	* 			'param' => 'first_name', 'value' => 'John', 'required' => true,
	*			'param' => 'last_name', 'value' => 'Smith', 'required' => true
	* 		)
	* )
	* @returns	Integer			Autoincrement ID
	* @throws	Exception
	*/
	public function insert_registration_payment($registration_id, $params=array()){
		if(is_array($params) && !empty($params)){			
			
			$validated = true;
			$sql_params = array();
			$fields = "";
			$values = "";
			
			//Validate parameters
			foreach($params as $param){
				if($param['required'] == true && $param['value'] == ""){
					$validated = false;
					break;
				}
				if($param['value'] != ""){
					$fields .= "`" .$param['param']. "`, ";
					$values .= "?, ";
					$sql_params[] = $param['value'];
				}
			}
			
			//Insert data
			if($validated){
				
				$sql_params[] = $registration_id;
				$sql_params[] = date("Y-m-d H:i:s");
				$query = $this->db->query("INSERT INTO `payments`(".$fields."`registration_id`, `payment_date`) VALUES(".$values."?,?)", $sql_params);
				$payment_id = $this->db->insert_id();
				
				if($query && !$this->db->error()){
					return $payment_id;
				}else{
					throw new Exception('ShoppingCart::insert_registration_payment - '.$this->db->error());
				}
								
			}else{
				throw new Exception('ShoppingCart::insert_registration_payment - Missing parameters. Cannot insert payment data.');
			}
			
		}else{
			throw new Exception('ShoppingCart::insert_registration_payment - Incorrect format or missing parameters.');	
		}
		
		return false;
	}
	
	/*-----------------------------------/
	* Updates payment information in database
	*
	* <AUTHOR> Army
	* @param	$registration_id	ID of payment to be updated
	* @param	$params				Array of fields to be updated
	* Array( [0] =>
	* 		Array(
	* 			'param' => 'first_name', 'value' => 'John', 'required' => true,
	*			'param' => 'last_name', 'value' => 'Smith', 'required' => true
	* 		)
	* )
	* @returns	True on successful update
	* @throws	Exception
	*/
	public function update_registration_payment($payment_id, $params=array()){
		if(is_array($params) && !empty($params)){
			
			$validated = true;
			$sql_params = array();
			$query_str = "";
			
			foreach($params as $param){
				if($param['required'] == true && $param['value'] == ""){
					$validated = false;
					break;
				}
				if($param['value'] != ""){
					$query_str .= "`" .$param['param']. "` = ?, ";
					$sql_params[] = $param['value'];
				}
			}
			$query_str = substr($query_str, 0, -2);
			
			//Update data
			if($validated){
			
				$sql_params[] = $payment_id;
				$query = $this->db->query("UPDATE `payments` SET " .$query_str. "  WHERE `payment_id` = ?", $sql_params);
				if($query && !$this->db->error()){
					return true;
				}else{
					throw new Exception('ShoppingCart::update_registration_payment - '.$this->db->error());
				}				
				
			}else{
				throw new Exception('ShoppingCart::update_registration_payment - Missing parameters. Cannot update order.');
			}
			
		}else{
			throw new Exception('ShoppingCart::update_registration_payment - Incorrect format or missing parameters.');	
		}
		
		return false;
	}
	
	/*-----------------------------------/
	* Get the total taxes for the registration order
	*
	* <AUTHOR> Army
	* @param	$subtotal	The subtotal of the order
	* @param	$province	The ship to province code
	* @return	Integer		Total taxes and rate percentages
	*/
	public function get_taxes($subtotal, $province){
		$taxes['taxes'] = 0.00;
		
		$gst = $this->reg_settings['federal_gst'];
		
		$params = array($province);
		$query = $this->db->query("SELECT * FROM `taxes` WHERE `state` = ?", $params);
		
		$pst = 0;
		$ishst = 0;
		if($query && !$this->db->error() && $this->db->num_rows() > 0){
			$taxes_sql = $this->db->fetch_array();
			$this_tax = $taxes_sql[0];
			$pst = $this_tax['rate'];
			$ishst = $this_tax['hst'];
		}
				
		$taxes['taxes'] = $subtotal*(($gst/100)+($pst/100));
		$taxes['gst_rate'] = $gst;
		if($ishst){
			$taxes['pst_rate'] = 0;
			$taxes['hst_rate'] = ($gst+$pst);
		}else{
			$taxes['pst_rate'] = $pst;
			$taxes['hst_rate'] = 0;
		}
		
		return $taxes;
	}
	
	/*-----------------------------------/
	* Validate and get discount of promo code
	*
	* <AUTHOR> Army
	* @param	$code			The promo code being applied
	* @param	$account_id		Account ID of user logged in
	* @param	$event_id		Evenet ID of cart item
	* @return	Integer			Discount amount (percentage)
	*/
	public function get_discount($code, $account_id=NULL, $event_id=NULL){
		$response = array();
		$params = array(date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), strtolower($code), $account_id, $event_id);
		$query = $this->db->query("SELECT `discount`, `discount_type` FROM `reg_promo_codes` WHERE"
		." `begin_date` <= ? &&"
		." `expiry` >= ? &&"
		." LOWER(`code`) = ? &&"
		." (`account_id` IS NULL || `account_id` = ?) &&"
		." (`event_id` IS NULL || `event_id` = ?) &&"
		." (`promo_max_count` > `promo_use_count` || `promo_max_count` = -1)"
		, $params);
		if($query && !$this->db->error() && $this->db->num_rows()){
			$result = $this->db->fetch_array();
			$response = $result[0];
		}
		return $response;
	}
	
	/*-----------------------------------/
	* Adds to promo count usage count
	*
	* <AUTHOR> Army
	* @param	$code		The promo code being applied
	* @return	True/False
	*/
	public function update_promo_count($code){
		
		//Remove promo code from other cart items in case limit was reached
		$cart_items = $this->get_cart();
		if(!empty($cart_items)){
			foreach($cart_items as $item_id=>$item){
				if(strtoupper($item['promocode']) == strtoupper($code)){
					$item['promocode'] = '';
					$item['discount'] = 0;
					try{
						$this->update_cart_item($item, $item_id);
					}catch(Exceptin $e){
						//Unable to remove
					}
				}
			}	
		}
		
		//Update promo count
		$query = $this->db->query("UPDATE `reg_promo_codes` SET `promo_use_count` = `promo_use_count`+1 WHERE LOWER(`code`) = ?", array(strtolower($code)));
		return ($query && !$this->db->error());
	}
}

?>