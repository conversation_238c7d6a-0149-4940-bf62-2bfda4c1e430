<?php
// modules/account/profile.php - Handles POST requests from the Edit Profile form

	// --- Environment Setup ---
	// Ensure session is started BEFORE any output
	if (session_status() === PHP_SESSION_NONE) {
		session_start();
	}

//Profile
// if(PAGE_ID == $_sitepages['profile']['page_id'] && $_SERVER['REQUEST_METHOD'] === 'GET'){
// --- PRIMARY CHECK: Only execute if this is the profile page AND a POST request ---

//

// }



if (
    PAGE_ID == $_sitepages['profile']['page_id'] &&     // Is it the profile page?
    $_SERVER['REQUEST_METHOD'] === 'POST'
   )
{
	// echo "<pre>";
	// print_r($_POST);
	// echo "</pre>";

	//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
		exit();
	}


	// Ensure necessary classes/functions are loaded
	// ... (load Account.class.php, Database.class.php etc.)

	// Ensure $Account object exists and user is logged in.
	if (!isset($Account) || !$Account->login_status()) {
		$_SESSION['profile_error_message'] = 'Access Denied. Please log in.';
		$login_url = (isset($Account) && isset($path)) ? $path . 'login/' : '/login/';
		// header('Location: ' . $login_url);
		// exit;
	}



	// --- Define URLs and Paths ---
	// $profile_form_url = $_sitepages['profile']['page_url'] ?? '/account/profile/'; // URL of the form page itself
	$upload_dir_relative = 'images/profiles/'; // Relative path from webroot
	$upload_dir_absolute = $_SERVER['DOCUMENT_ROOT'] . (isset($path) ? $path : '/') . $upload_dir_relative; // Absolute server path

	// --- Process form submission ---
	if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile']) && $_POST['update_profile'] == '1') {

		//Session cookie
		if(!empty($_POST) && ($_POST['xid'] != $_COOKIE['xid'] || !isset($_POST['xid']))){
			$errors[] = 'Invalid session cookie. Please make sure cookies are enabled in your browser then try again.';
			unset($_POST);
		}


		// --- Security Check (Example: CSRF Token / Old XID if you keep it) ---
		/*
		if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) { // New CSRF example
		// OR if keeping old method:
		// if (!isset($_POST['xid']) || $_POST['xid'] != ($_COOKIE['xid'] ?? '')) {
			$_SESSION['profile_error_message'] = 'Invalid security token or session. Please try again.';
			header('Location: ' . $profile_form_url);
			exit;
		}
		*/

		// Clear any previous session messages
		unset($_SESSION['profile_success_message']);
		unset($_SESSION['profile_error_message']);
		unset($_SESSION['profile_form_data']);

		unset($_SESSION['profile_error_message']);

		// --- Variable Initialization ---
		$new_photo_filename = null; // Will hold the filename if upload is successful
		// $delete_photo_requested = isset($_POST['delete_photo']) && $_POST['delete_photo'] == '1';

		$old_photo_filename = $_POST['old_photo'] ?? null;
		$photo_update_action = 'keep'; // 'upload', 'delete', 'keep'

		// --- Handle Photo Upload / Deletion ---
		// Include the profile image handler
		require_once('ProfileImageHandler.php');

		$photo_update_action = 'keep'; // Default action
		$new_photo_filename = null;
		$old_photo_filename = $_POST['old_photo'] ?? null;

		// Handle photo deletion (for form submission fallback)
		// This is still needed for non-JavaScript browsers or if AJAX fails
		if (isset($_POST['delete_photo']) && $_POST['delete_photo'] == '1') {
			$photo_update_action = 'delete';
			if (!empty($old_photo_filename)) {
				// Try to delete the file
				delete_profile_image($old_photo_filename);

				// Even if file deletion fails, we should still update the database
				// to remove the reference to the file
			}
		}

		// Handle photo upload
		if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
			error_log('------------------------------------');
			error_log("DEBUG: Detected \$_FILES['photo']"); // Add this log
			error_log('------------------------------------');

			$photo_update_action = 'upload';

			// Process the image upload
			$upload_result = process_profile_image(
				$_FILES['photo'],
				$old_photo_filename, // Will handle deletion of old file
				$Account->account_id
			);

			if ($upload_result['success']) {
				$new_photo_filename = $upload_result['filename'];
			} else {
				$_SESSION['profile_error_message'] = 'Error: ' . $upload_result['message'];
				$photo_update_action = 'keep'; // Revert action on failure
			}
		}
		 // Check for delete checkbox only needed if JS fails or is off
		 elseif (isset($_POST['delete_photo']) && $_POST['delete_photo'] == '1') {
			error_log("DEBUG: Detected \$_POST['delete_photo'] checkbox"); // Add this log
			$photo_update_action = 'delete';
			 if (!empty($old_photo_filename)) {
				 delete_profile_image($old_photo_filename);
			 }
	   } else {
			error_log("DEBUG: No new file uploaded and delete checkbox not checked.");
			$photo_update_action = 'keep'; // Explicitly keep if no action specified
	   }
	   // --- END OF PHOTO HANDLING WITHIN POST ---


		// If there was an upload/processing error, redirect back now
		if (isset($_SESSION['profile_error_message'])) {
			$_SESSION['profile_form_data'] = $_POST; // Store submitted text data
			// header('Location: ' . $profile_form_url);
			// exit;
		}


		// --- Prepare the parameters array for update_profile ---
		// Only proceed if photo handling was successful or not needed
		$params_to_update = [];

		// Address Fields (account_profiles)
		$params_to_update[] = ['param' => 'address1', 'value' => trim($_POST['address1'] ?? ''), 'label' => 'Street Address', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ];
		$params_to_update[] = ['param' => 'address2', 'value' => trim($_POST['address2'] ?? ''), 'label' => 'Unit No.', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ];
		$params_to_update[] = ['param' => 'city', 'value' => trim($_POST['city'] ?? ''), 'label' => 'City/Town', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ];
		$params_to_update[] = ['param' => 'province', 'value' => $_POST['province'] ?? '', 'label' => 'Province/State', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ];
		$params_to_update[] = ['param' => 'postalcode', 'value' => trim($_POST['postalcode'] ?? ''), 'label' => 'Postal/Zip Code', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ]; // Add specific validation if needed
		$params_to_update[] = ['param' => 'country', 'value' => $_POST['country'] ?? '', 'label' => 'Country', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ];

		// Social Media Fields (account_profiles) - Basic URL validation might be good
		$params_to_update[] = ['param' => 'facebook', 'value' => trim($_POST['facebook'] ?? ''), 'label' => 'Facebook URL', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ]; // Add 'url' validation?
		$params_to_update[] = ['param' => 'linkedin', 'value' => trim($_POST['linkedin'] ?? ''), 'label' => 'LinkedIn URL', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ]; // Add 'url' validation?
		$params_to_update[] = ['param' => 'instagram', 'value' => trim($_POST['instagram'] ?? ''), 'label' => 'Instagram URL', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ]; // Add 'url' validation?
		$params_to_update[] = ['param' => 'twitter', 'value' => trim($_POST['twitter'] ?? ''), 'label' => 'Twitter URL', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ]; // Add 'url' validation?

		// Biography Fields (account_profiles)
		$params_to_update[] = ['param' => 'pga_member_since', 'value' => (!empty($_POST['pga_member_since']) ? $_POST['pga_member_since'] : NULL), 'label' => 'PGA Member Since', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ];
		$params_to_update[] = ['param' => 'website', 'value' => trim($_POST['website'] ?? ''), 'label' => 'Website', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ]; // Add 'url' validation?
		$params_to_update[] = ['param' => 'profile', 'value' => trim($_POST['profile'] ?? ''), 'label' => 'Profile', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ];
		$params_to_update[] = ['param' => 'education', 'value' => trim($_POST['education'] ?? ''), 'label' => 'Education Background', 'required' => false, 'unique' => false, 'validate' => false, 'hash' => false ];

		// Add the photo parameter to the update array if it changed
		if ($photo_update_action === 'upload') {
			$params_to_update[] = [
				'param' => 'photo',
				'value' => $new_photo_filename,
				'label' => 'Profile Photo',
				'required' => false,
				'unique' => false,
				'validate' => false,
				'hash' => false
			];
		} elseif ($photo_update_action === 'delete') {
			// Make sure we set the photo value to NULL in the database
			$params_to_update[] = [
				'param' => 'photo',
				'value' => NULL,
				'label' => 'Profile Photo',
				'required' => false,
				'unique' => false,
				'validate' => false,
				'hash' => false
			];
		}

		// --- Attempt Database Update ---
		try {
			// Check if there was an upload error previously stored
			if (isset($_SESSION['profile_error_message'])) {
				// Throw exception here to prevent DB update if photo upload failed
				throw new Exception($_SESSION['profile_error_message']);
			}
			// USER_LOGGED_IN constant seems specific to your old code, using Account object property instead
			if ($Account->update_profile($params_to_update, $Account->account_id)) {
				$_SESSION['profile_success_message'] = "Profile updated successfully!";
				// header('Location: ' . $profile_form_url);
				// exit;
			} else {
				// Should be caught by exception if DB update fails
				throw new Exception("An unknown error occurred while saving the profile.");
			}

			// delete and inser question answers

			$qna_error = false; // Flag for errors within Q&A loop

			// Delete existing answers first
			$delete_old_answers = $db->query("DELETE FROM `account_profile_answers` WHERE `account_id` = ?", [$Account->account_id]);
			if (!$delete_old_answers || $db->error()) {
				 error_log("Q&A Update: Failed to delete old answers. Error: " . $db->error());
				 $qna_error = true;
			} else {
				 error_log("Q&A Update: Deleted old answers successfully.");
				 // Loop through submitted answers and INSERT non-empty ones
				 $submitted_answers = $_POST['q_answer'] ?? [];
				 foreach ($submitted_answers as $qid => $answer) {
					  $qid = (int)$qid;
					  $answer = trim($answer);

					  if (!empty($answer)) { // Only insert if answer is not empty
						   error_log("Q&A Update: INSERTING answer for qid: " . $qid);
						   $insert = $db->query(
							   "INSERT INTO `account_profile_answers` (account_id, question_id, answer) VALUES (?, ?, ?)",
							   [$Account->account_id, $qid, $answer]
						   );
						   if (!$insert || $db->error()) {
								$qna_error = true;
								error_log("Q&A INSERT failed for qid $qid: " . $db->error());
								break; // Stop inserting on first error
						   }
					  }
				 } // End foreach loop
			}
			//

		} catch (Exception $e) {
			// FAILURE: Store error message and submitted data, redirect back
			$_SESSION['profile_error_message'] = "Profile update failed: " . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
			$_SESSION['profile_form_data'] = $_POST; // Store text data
			// **Important**: If the photo upload *succeeded* but the DB update *failed*,
			// you might want to delete the newly uploaded file here to avoid orphans.
			if ($photo_update_action === 'upload' && $new_photo_filename !== null && file_exists($upload_dir_absolute . $new_photo_filename)) {
				@unlink($upload_dir_absolute . $new_photo_filename);
				error_log("Deleted orphaned profile photo due to DB error: " . $upload_dir_absolute . $new_photo_filename);
			}
			// header('Location: ' . $profile_form_url);
			// exit;
		}

	} else {
		// If not a valid POST request for this action, just redirect to the form
		// header('Location: ' . $profile_form_url);
		// exit;
	}
}
?>