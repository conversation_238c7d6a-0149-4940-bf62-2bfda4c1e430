<?php

//System files
include("../../config/config.php");
include('../../config/database.php');
include('../../includes/functions.php');
include('../../includes/utils.php');

//Get event
$occurrence_id = (isset($_POST['id']) ? $_POST['id'] : NULL);
$event = $Registration->get_occurrence($occurrence_id);
if(!empty($event) && USER_LOGGED_IN){
	
	//Get overdue invoice count for user
	$overdue_invoices = $Account->overdue_invoices();

	# TODO testing only - remove for production
	$overdue_invoices = false;
	
	//Double-check user can register
	if($event['reg_available'] && !$overdue_invoices){

		//Format event data
		$data = array(
			'event_type' => 2,
			'event_id' => $event['event_id'],
			'occurrence_id' => $event['occurrence_id'],
			'category_id' => $event['category_id'],
			'team_event' => $event['team_event'],
			'event_name' => $event['event_name'],
			'category_name' => $event['category_name'],
			'event_url' => $_sitepages['tournaments']['page_url'].$event['page'].'-'.$event['occurrence_id'].'/',
			'start_date' => $event['start_date'],
			'end_date' => $event['end_date'],
			'discount' => 0,
			'fees' => $event['tournament_fee'],
			'admin_fee' => (!is_null($event['admin_fee']) ? $event['admin_fee'] : $reg_settings['admin_fee']),
			'admin_fee_type' => (!is_null($event['admin_fee']) ? $event['admin_fee_type'] : $reg_settings['admin_fee_type']),
			'promocode' => '',
			'payment_deadline' => $event['payment_deadline'],
			'attendees' => array()	
		);

		//Set attendee info to current user
		$data['attendees'][0] = array(
			'account_id' => $Account->account_id,
			'first_name' => $Account->first_name,
			'last_name' => $Account->last_name,
			'email' => $Account->email,
			'phone' => $Account->phone,
			'gender' => $Account->gender,
			'attendee_sharing' => 1,
			'ticket_type' => $event['pricing'][0]['price_type'],
			'ticket_price' => $event['pricing'][0]['price'],
			'tournament_fee' => $event['tournament_fee'],
			'addons' => array()
		);

		//Add to cart
		try{
			$ShoppingCart->add_to_cart($data);
			echo 'success';

		}catch(Exception $e){
			echo 'Unable to add tournament to cart. '.$e->getMessage();
		}
		
	//Overdue invoices
	}else if($overdue_invoices > 0){
		echo 'Registration is locked. Account has ' .($overdue_invoices == 1 ? 'an overdue invoice that requires' : 'overdue invoices that require'). ' payment. <a href="' .$sitemap[76]['page_url']. '">View Invoices &rsaquo;</a>';
		
	//Unable to register user
	}else{
		echo 'Selected tournament is unavailable for registration.';
	}

//No active login
}else if(!USER_LOGGED_IN){
	echo 'login';
	
//Event not found
}else{
	echo 'Selected tournament was not found.';
}

?>