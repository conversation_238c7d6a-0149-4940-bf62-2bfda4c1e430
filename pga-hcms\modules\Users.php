<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('accounts');
	$CMSBuilder->set_widget($_cmssections['users'], 'Total Users', $total_records);
}

if(SECTION_ID == $_cmssections['account'] || SECTION_ID == $_cmssections['users'] || SECTION_ID == $_cmssections['manage_users']){

	//Define vars
	$record_db   = 'accounts';
	$record_id   = 'account_id';
	$record_name = 'User';

	$records_arr = [];
	$roles 		 = [];
	$permissions = [];
	$master 	 = false;
	// $avatar		 = false;
	$avatar		 = true;

	//Get all roles
	$account_roles = $Account->get_account_roles();

	//Validation
	$errors   	 = false;
	$required 	 = [];
	$required_fields = [
		'username',
		'first_name',
		'last_name',
		'email'
	];
	if(ITEM_ID == ''){
		$required_fields[] = 'password';
		$required_fields[] = 'password2';
	}

	$imagedir    = '../images/users/';

	//Image Uploader
	// if ($_FILES['image']['error'] === UPLOAD_ERR_OK && $_FILES['image']['size'] > 0) {
	// 	// File uploaded successfully and has content
	// 	$avatar = true;
	// 	$CMSUploader = new CMSUploader('avatar', $imagedir);
	// }

	if($avatar){
		$imagedir    = '../images/users/';
		$CMSUploader = new CMSUploader('avatar', $imagedir);
	}

	//Filtering
	$where  = "";
	$params = ['', ' ', '']; //Fullname concat
	$searchable_fields = [
		"$record_db.account_id",
		"$record_db.username",
		"$record_db.email",
		"$record_db.status",
		"account_roles.role_name"
	];

	//
	//Member classes
	$member_classes = array();
	$query = $db->query("SELECT * FROM `membership_classes` ORDER BY `ordering`, `class_id` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$member_classes[$row['class_id']] = $row;
		}
	}

	//Member types
	$member_types = array();
	$query = $db->query("SELECT `membership_id`, `membership_name` FROM `membership_types` ORDER BY `membership_id` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $mem){
			$member_types[$mem['membership_id']] = $mem;
		}
	}

	//Member categories
	$member_categories = array();
	$query = $db->query("SELECT `category_id`, `name` FROM `membership_categories` ORDER BY `name` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $cat){
			$member_categories[$cat['category_id']] = $cat;
		}
	}

	//Account groups
	$account_groups = array();
	$query = $db->query("SELECT * FROM `account_groups` ORDER BY `group_name` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $acc){
			$account_groups[$acc['group_id']] = $acc;
		}
	}

	//Facilities
	$facilities = array();
	$query = $db->query("SELECT * FROM `facilities` ORDER BY `facility_name` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$facilities[$row['facility_id']] = $row;
		}
	}

	//Committees
	$committees = array();
	$query = $db->query("SELECT * FROM `committees` ORDER BY `name` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$committees[$row['committee_id']] = $row;
		}
	}

	//Profile questions
	$profile_questions = array();
	$query = $db->query("SELECT `question_id`, `question` FROM `account_profile_questions` WHERE `showhide` = 0 ORDER BY `ordering`");
	if($query && !$db->error()){
		$profile_questions = $db->fetch_array();
	}
	//

	//Enum values
	$statuses = $db->get_enum_vals('accounts', 'status');
	$gender_types = $db->get_enum_vals('account_profiles', 'gender');
	$board_roles = $db->get_enum_vals('account_profiles', 'board_member_role');

	// $statuses = get_enum_vals('accounts', 'status');
	// $gender_types = get_enum_vals('account_profiles', 'gender');
	// $board_roles = get_enum_vals('account_profiles', 'board_member_role');

	//Current user access
	$fullaccess = ($CMSBuilder->check_permissions($_cmssections['users']) || MASTER_USER);

	//Can only access self through account section
	if(SECTION_ID == $_cmssections['account'] && (ACTION != 'edit' || ITEM_ID != USER_LOGGED_IN)){
		header('Location: '.PAGE_URL.'?action=edit&item_id='.USER_LOGGED_IN);
		exit();
	}

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where .= ($where ? "AND " : "WHERE ")." $record_db.$record_id = ? ";
		$params[] = ITEM_ID;

	//Build search query
	}else{

		if(SECTION_ID == $_cmssections['users'] && $searchterm){

			$where .= ($where ? "AND " : "WHERE ")." (";

			//Search full name
			$where .= "CONCAT(IFNULL(account_profiles.first_name, ?), ?, IFNULL(account_profiles.last_name, ?)) LIKE ? ";
			$params[] = '';
			$params[] = ' ';
			$params[] = '';
			$params[] = '%' .$searchterm. '%';

			//Searchable fields
			if(!empty($searchable_fields)){
				foreach($searchable_fields as $key=>$field){
					$searchable_fields[$key] = "$field LIKE ?";
					$params[] = '%'.$searchterm.'%';
				}
				$where .= "OR ".implode(' OR ', $searchable_fields);
			}
			$where .= ") ";
		}
	}

	//Get records
	$db->query("SELECT $record_db.*, account_profiles.*, CONCAT(IFNULL(account_profiles.first_name, ?), ?, IFNULL(account_profiles.last_name, ?)) AS fullname , `facilities`.`facility_name`, `membership_classes`.`class_name`, `membership_types`.`membership_name`, `membership_categories`.`name` AS `category_name`, `account_groups`.`group_name`, `account_permissions`.`role_id`, `account_roles`.`role_name`
	FROM $record_db
	INNER JOIN account_profiles ON $record_db.$record_id = account_profiles.$record_id
	LEFT JOIN account_permissions ON $record_db.$record_id = account_permissions.$record_id
	LEFT JOIN account_roles ON account_permissions.role_id = account_roles.role_id
	LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id`
	LEFT JOIN `membership_classes` ON `account_profiles`.`class_id` = `membership_classes`.`class_id`
	LEFT JOIN `membership_types` ON `account_profiles`.`membership_id` = `membership_types`.`membership_id`
	LEFT JOIN `membership_categories` ON `account_profiles`.`category_id` = `membership_categories`.`category_id`
	LEFT JOIN `account_groups` ON `account_profiles`.`group_id` = `account_groups`.`group_id` ".
	$where."
	GROUP BY $record_db.$record_id
	ORDER BY $record_db.$record_id", $params);
	if(!$db->error()){
		$result = $db->fetch_array();

		foreach($result as $row){
			$row['roles']  = array_keys($Account->get_account_roles($row[$record_id]));
			$row['master'] = in_array(3, $row['roles']);
			$row['locked'] = !empty($row['lockout_expiry']) && strtotime($row['lockout_expiry']) > strtotime('now');
			if($avatar){
				$row['photo']  = check_file($row['photo'], $imagedir);
			}
			//
			$query_user_committees = $db->query(
						"SELECT `committee_id` FROM `account_committees` WHERE `account_id` = ?",
						[ITEM_ID]
					);
			if ($query_user_committees && !$db->error()) {
				$user_committee_rows = $db->fetch_array($query_user_committees);
				if ($user_committee_rows) {
					foreach ($user_committee_rows as $uc_row) {
						$row['committees'][] = (int)$uc_row['committee_id']; // Store as array of integer IDs
					}
				}
			// } else {
			// 	error_log("CMS User Edit: Failed to fetch user committees for account " . ITEM_ID . ". Error: " . ($db->error() ?? "Query failed"));
			}
			//
			//
			// --- >>> NEW: FETCH USER'S Q&A ANSWERS <<< ---
			$row['answers'] = []; // Initialize as empty array for this specific user
			if (isset($db) && is_object($db)) {
				$query_user_answers = $db->query(
					"SELECT `question_id`, `answer` FROM `account_profile_answers` WHERE `account_id` = ?",
					[ITEM_ID] // Use ITEM_ID which is the account_id of the user being edited
				);
				if ($query_user_answers && !$db->error()) {
					$user_answer_rows = $db->fetch_array($query_user_answers);
					if ($user_answer_rows) {
						foreach ($user_answer_rows as $ua_row) {
							// Store answers keyed by question_id for easy lookup in the template
							$row['answers'][$ua_row['question_id']] = $ua_row['answer'];
						}
					}
				// } else {
				// 	error_log("CMS User Edit: Failed to fetch user Q&A answers for account " . ITEM_ID . ". Error: " . ($db->error() ?? "Query failed"));
				}
			}
			// error_log("CMS User Edit: Fetched Q&A answers for user " . ITEM_ID . ": " . print_r($row['answers'], true));
			// --- >>> END OF FETCHING USER'S Q&A ANSWERS <<< ---
			//

			//
			$row['master'] = $Account->account_has_role('Master', $row['account_id']);
			$row['last_login'] = NULL;
			$loginqry = $db->query("SELECT `visit_time` FROM `account_session_log` WHERE `account_id` = ? ORDER BY `visit_time` DESC LIMIT 1", array(ITEM_ID));
			if($loginqry && !$db->error() && $db->num_rows()){
				$row['last_login'] = $db->fetch_array()[0]['visit_time'];
			}

			$row['changelog'] = [];
			$query = $db->query("SELECT `id`, `comments`, `updated_on`, `updated_by` FROM `account_change_log` WHERE `account_id` = ? ORDER BY `updated_on` DESC, `id` DESC", array(ITEM_ID));
			if($query && !$db->error()){
				$row['changelog'] = $db->fetch_array();
			}
			//
			$records_arr[$row[$record_id]] = $row;

		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);
	}

	// echo "<pre>";
	// print_r($row);
	// echo "</pre>";
	// exit;

	//Selected record
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){

			//Get user permissions
			$records_arr[ITEM_ID]['permissions'] = array_keys($Account->get_account_permissions(ITEM_ID));

			$row = $records_arr[ITEM_ID];

		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}

	//Delete item
	if(isset($_POST['delete'])){

		//Cannot delete master accounts
		if($records_arr[ITEM_ID]['master']){
			$CMSBuilder->set_system_alert('Unable to delete record. Master accounts cannot be deleted.', false);

		//Cannot delete own account
		}else if(USER_LOGGED_IN == ITEM_ID){
			$CMSBuilder->set_system_alert('Unable to delete record. You cannot delete your own account.', false);

		}else{

			//Delete from table, foreign key constraints will cascade
			$db->query("DELETE FROM $record_db WHERE $record_id = ?", array(ITEM_ID));
			if(!$db->error()){
				if ($avatar) $CMSUploader->bulk_delete(['photo' => $records_arr[ITEM_ID]['photo']]);
				$CMSBuilder->set_system_alert('User was successfully deleted.', true);

			}else{
				$CMSBuilder->set_system_alert('Unable to delete record.', false);
			}

			//Send to main
			header("Location: " .PAGE_URL);
			exit();

		}

	//Unlock account
	}else if(isset($_POST['unlock'])){
		$db->query("UPDATE $record_db SET lockout_expiry = NULL WHERE $record_id = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert('Account has been successfully unlocked.', true);
			$records_arr[ITEM_ID]['locked'] = false;
		}else{
			$CMSBuilder->set_system_alert('Unable to update record.', false);
		}

	//Save item
	}else if(isset($_POST['save'])){

		//Can only update status, roles and permissions you have access
		if($fullaccess){

			//If updating a master account
			if($Account->account_has_role('Master', ITEM_ID)){
				$status = 'Active';
				$roles = array(1, 3); //Admin, Master

			//Get roles and permissions
			}else{
				$status = $_POST['status'];
				$roles = $_POST['roles'] ?? [];

				//Admin
				if(in_array(1, $roles)){
					$permissions = $_POST['permissions'] ?? [];

				//Front-end User
				}else{
					$permissions = [];
				}
			}
		}else{
			$status = $allusers[ITEM_ID]['status'];
			$roles = $allusers[ITEM_ID]['roles'];
			$permissions = $allusers[ITEM_ID]['permissions'];
		}

		//Validate required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Specific validation
		if(empty($status)){
			$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
			$required[] = 'status';
		}
		if(!checkmail($_POST['email'])){
			$errors[] = 'Please enter a valid email address.';
			$required[] = 'email';
		}
		if($_POST['password'] != $_POST['password2']){
			$errors[] = 'Your passwords do not match. Please try again.';
			$required[] = 'password';
			$required[] = 'password2';
		}

		//Image validation
		if(!empty($_FILES['photo']['size']) && $_FILES['photo']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large. Cannot exceed ' .$_max_filesize['megabytes']. '.';
			$required[] = 'photo';
		}

		if(!$errors){

			// When checkbox is checked (value=1), set showhide=0 (blue/visible)
			// When checkbox is unchecked (not set), set showhide=1 (red/hidden)
			$_POST['showhide'] = isset($_POST['showhide']) ? 0 : 1;

			//Set params
			$params = [
				[
					'param' 	=> 'username',
					'value' 	=> $_POST['username'] ?? NULL,
					'label'		=> 'Username',
					'required' 	=> in_array('username', $required_fields),
					'unique' 	=> true,
					'validate' 	=> false,
					'hash' 		=> false
				],
				[
					'param' 	=> 'email',
					'value'		=> $_POST['email'],
					'label' 	=> 'Email Address',
					'required' 	=> in_array('email', $required_fields),
					'unique' 	=> true,
					'validate' 	=> 'email',
					'hash' 		=> false
				],
				[
					'param' 	=> 'email_alt',
					'value'		=> $_POST['email_alt'],
					'label' 	=> 'Alternate Email Address',
					'required' 	=> false,
					'unique' 	=> true,
					'validate' 	=> false,
					'hash' 		=> false
				],
				[
					'param' 	=> 'dob',
					'value'		=> $_POST['dob'],
					'label' 	=> 'Date of Birth',
					'required' 	=> false,
					'unique' 	=> false,
					'validate' 	=> false,
					'hash' 		=> false
				],
				[
					'param' 	=> 'title',
					'value'		=> $_POST['title'],
					'label' 	=> 'Title',
					'required' 	=> false,
					'unique' 	=> false,
					'validate' 	=> false,
					'hash' 		=> false
				],
				[
					'param' 	=> 'company',
					'value'		=> $_POST['company'],
					'label' 	=> 'Company',
					'required' 	=> false,
					'unique' 	=> false,
					'validate' 	=> false,
					'hash' 		=> false
				],
				[
					'param' 	=> 'gender',
					'value'		=> $_POST['gender'],
					'label' 	=> 'Gender',
					'required' 	=> false,
					'unique' 	=> false,
					'validate' 	=> false,
					'hash' 		=> false
				],
				[
                    'param'     => 'group_id',
                    'value'     => (!empty($_POST['group_id']) ? (int)$_POST['group_id'] : NULL),
                    'label'     => 'Account Group',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
				[
					'param' 	=> 'first_name',
					'value' 	=> $_POST['first_name'],
					'label' 	=> 'First Name',
					'required' 	=> in_array('first_name', $required_fields),
					'unique' 	=> false,
					'validate' 	=> false,
					'hash' 		=> false
				],
				[
					'param' 	=> 'last_name',
					'value' 	=> $_POST['last_name'],
					'label' 	=> 'Last Name',
					'required' 	=> in_array('last_name', $required_fields),
					'unique' 	=> false,
					'validate' 	=> false,
					'hash'	 	=> false
				],
				[
					'param' 	=> 'status',
					'value' 	=> $status,
					'label' 	=> 'Account Status',
					'required' 	=> true,
					'unique' 	=> false,
					'validate' 	=> false,
					'hash' 		=> false
				],
				[
					'param' => 'company',
					'label' => 'Company',
					'value' => ($_POST['company'] ?? ''),
					'required' => (in_array('company', $required_fields)),
					'unique' => false,
					'validate' => false,
					'hash' => false
				],
				[
                    'param'     => 'showhide',
                    'value'     => $_POST['showhide'],
                    'label'     => 'Show User',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
				 // NEW: Add Member Detail parameters
                [
                    'param'     => 'pga_number',
                    'value'     => trim($_POST['pga_number'] ?? ''),
                    'label'     => 'PGA Membership Number',
                    'required'  => in_array('pga_number', $required_fields ?? []),
                    'unique'    => false, // Typically not unique across all users, but could be system-wide unique
                    'validate'  => false,
                    'hash'      => false
                ],
                [
                    'param'     => 'membership_id',
                    'value'     => (!empty($_POST['membership_id']) ? (int)$_POST['membership_id'] : NULL),
                    'label'     => 'Membership Type',
                    'required'  => in_array('membership_id', $required_fields ?? []),
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
                [
                    'param'     => 'class_id',
                    'value'     => (!empty($_POST['class_id']) ? (int)$_POST['class_id'] : NULL),
                    'label'     => 'Member Classification',
                    'required'  => in_array('class_id', $required_fields ?? []), // Often required
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
                [
                    'param'     => 'pga_member_since',
                    'value'     => (!empty($_POST['pga_member_since']) ? date('Y-m-d', strtotime($_POST['pga_member_since'])) : NULL),
                    'label'     => 'Member Since',
                    'required'  => in_array('pga_member_since', $required_fields ?? []),
                    'unique'    => false,
                    'validate'  => false, // Could add date validation if needed
                    'hash'      => false
                ],
				// NEW: Tournament Eligibility (category_id)
                [
                    'param'     => 'category_id',
                    'value'     => (!empty($_POST['category_id']) ? (int)$_POST['category_id'] : NULL),
                    'label'     => 'Tournament Eligibility',
                    'required'  => false, // Adjust if required
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
                // NEW: Board Member
                [
                    'param'     => 'board_member',
                    'value'     => $_POST['board_member'] ?? '0', // Default to '0' (No)
                    'label'     => 'Board Member',
                    'required'  => true, // ENUM NOT NULL usually means it needs a value
                    'unique'    => false,
                    'validate'  => false, // DB ENUM handles valid values
                    'hash'      => false
                ],
                // NEW: Board Member Role
                [
                    'param'     => 'board_member_role',
                    // Only save role if they are a board member (value > 0)
                    'value'     => (isset($_POST['board_member']) && $_POST['board_member'] != '0' && !empty($_POST['board_member_role']) ? $_POST['board_member_role'] : NULL),
                    'label'     => 'Board Member Role',
                    'required'  => false, // Might be conditionally required by JS
                    'unique'    => false,
                    'validate'  => false, // DB ENUM handles values
                    'hash'      => false
                ],
                // NEW: Reward Card No.
                [
                    'param'     => 'rewards_number',
                    'value'     => trim($_POST['rewards_number'] ?? ''),
                    'label'     => 'Reward Card No.',
                    'required'  => false,
                    'unique'    => false, // Or true if it must be unique system-wide
                    'validate'  => false,
                    'hash'      => false
                ],
				[
					'param' => 'phone',
					'label' => 'Phone Number',
					'value' => format_phone_number(($_POST['phone'] ?? '')),
					'required' => (in_array('phone', $required_fields)),
					'unique' => false,
					'validate' => 'phone',
					'hash' => false
				],
				[
					'param' => 'address1',
					'label' => 'Street Address',
					'value' => ($_POST['address1'] ?? ''),
					'required' => (in_array('address1', $required_fields)),
					'unique' => false,
					'validate' => false,
					'hash' => false
				],
				[
					'param' => 'address2',
					'label' => 'Address Line 2',
					'value' => ($_POST['address2'] ?? ''),
					'required' => (in_array('address2', $required_fields)),
					'unique' => false,
					'validate' => false,
					'hash' => false
				],
				[
					'param' => 'city',
					'label' => 'City',
					'value' => ($_POST['city'] ?? ''),
					'required' => (in_array('city', $required_fields)),
					'unique' => false,
					'validate' => false,
					'hash' => false
				],
				[
					'param' => 'province',
					'label' => 'Province/State',
					'value' => ($_POST['province'] ?? ''),
					'required' => (in_array('province', $required_fields)),
					'unique' => false,
					'validate' => false,
					'hash' => false
				],
				[
					'param' => 'postalcode',
					'label' => 'Postal/Zip Code',
					'value' => ($_POST['postalcode'] ?? ''),
					'required' => (in_array('postalcode', $required_fields)),
					'unique' => false,
					'validate' => false,
					'hash' => false
				],
				[
					'param' => 'country',
					'label' => 'Country',
					'value' => ($_POST['country'] ?? ''),
					'required' => (in_array('country', $required_fields)),
					'unique' => false,
					'validate' => false,
					'hash' => false
				],
				// NEW: Add Social Media parameters
                [
                    'param'     => 'twitter',
                    'value'     => trim($_POST['twitter'] ?? ''),
                    'label'     => 'Twitter URL',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
                [
                    'param'     => 'facebook',
                    'value'     => trim($_POST['facebook'] ?? ''),
                    'label'     => 'Facebook URL',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
                [
                    'param'     => 'linkedin',
                    'value'     => trim($_POST['linkedin'] ?? ''),
                    'label'     => 'LinkedIn URL',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
                [
                    'param'     => 'instagram',
                    'value'     => trim($_POST['instagram'] ?? ''),
                    'label'     => 'Instagram URL',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
				[
                    'param'     => 'education',
                    'value'     => trim($_POST['education'] ?? ''),
                    'label'     => 'Education',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
				[
                    'param'     => 'profile',
                    'value'     => trim($_POST['profile'] ?? ''),
                    'label'     => 'Profile',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
				 // NEW: Add Privacy Settings parameters
                [
                    'param'     => 'show_email',
                    'value'     => $_POST['show_email'] ?? '0', // Default to Private if not set
                    'label'     => 'Email Privacy',
                    'required'  => true, // These should always have a value
                    'unique'    => false,
                    'validate'  => false, // DB ENUM handles valid values
                    'hash'      => false
                ],
                [
                    'param'     => 'show_phone',
                    'value'     => $_POST['show_phone'] ?? '0', // Default to Private
                    'label'     => 'Phone Privacy',
                    'required'  => true,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
                [
                    'param'     => 'show_profile',
                    'value'     => $_POST['show_profile'] ?? '1', // Default to Public
                    'label'     => 'Profile Privacy',
                    'required'  => true,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
                [
                    'param'     => 'show_qa',
                    'value'     => $_POST['show_qa'] ?? '1', // Default to Public
                    'label'     => 'Q&A Privacy',
                    'required'  => true,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
				 [
                    'param'     => 'notes',
                    'value'     => trim($_POST['notes'] ?? ''),
                    'label'     => 'Notes',
                    'required'  => false,
                    'unique'    => false,
                    'validate'  => false,
                    'hash'      => false
                ],
			];


			// if ($_FILES['image']['error'] === UPLOAD_ERR_OK && $_FILES['image']['size'] > 0) {
			// 	// File uploaded successfully and has content
			// 	$avatar = true;
			// 	$CMSUploader = new CMSUploader('avatar', $imagedir);
			// }
			//Image handling
			if($avatar){

				//Delete current images
				if(isset($_POST['deleteimage'])){
					$CMSUploader->bulk_delete(['photo' => $records_arr[ITEM_ID]['photo']]);
				}

				//Upload new images
				try{
					$images = $CMSUploader->bulk_upload('', $records_arr[ITEM_ID] ?? []);
				}catch(Exception $e){
					$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
				}

				//Set photo param
				$params[] = [
					'param' 	=> 'photo',
					'value' 	=> ($images['photo'] ?? NULL),
					'label' 	=> 'Profile Image',
					'required' 	=> false,
					'unique' 	=> false,
					'validate' 	=> false,
					'hash' 		=> false
				];

			}

			//Create new account
			if(ITEM_ID == ""){

				//Set password param
				$params[] = [
					'param' 	=> 'password',
					'value' 	=> $_POST['password'],
					'label' 	=> 'Password',
					'required' 	=> true,
					'unique' 	=> false,
					'validate' 	=> false,
					'hash' 		=> true
				];

				//Notification settings
				$confirm_email = ($status == 'Pending');
				$welcome_email = ($status == 'Active');

				//Register account
				try{
					$item_id = $Account->register($params, $roles, $confirm_email, $welcome_email, false);

					//Insert account permissions
					$db->new_transaction();
					foreach($permissions as $perm){
						$db->query("INSERT INTO cms_permissions(section_id, $record_id) VALUES(?,?)", [$perm, $item_id]);
					}

					//
					//Save profile answers
					foreach($profile_questions as $q){
						if(isset($_POST['question-'.$q['question_id']])){
							$params = array($account_id, $q['question_id'], $_POST['question-'.$q['question_id']], date("Y-m-d H:i:s"));
							$insert = $db->query("INSERT INTO `account_profile_answers`(`account_id`, `question_id`, `answer`, `last_updated`) VALUES(?,?,?,?)", $params);
						}
					}

					//Save account committees
					if(!empty($acc_committees)){
						foreach($acc_committees as $committee_id){
							$params = array($account_id, $committee_id);
							$insert = $db->query("INSERT IGNORE INTO `account_committees` (`account_id`, `committee_id`) VALUES(?,?)", $params);
						}
					}
					//

					if(!$db->error()){
						$db->commit();
					}else{
						$errors[] = 'Unable to insert account permissions. '.$db->error();
					}

				//Register error
				}catch(Exception $e){

					$errors[] = 'Unable to update user. ' .$e->getMessage();

					//Clean up images
					if(isset($images['photo'])){
						$CMSUploader->bulk_delete(['photo' => $images['photo']]);
					}
				}

			//Update account
			}else{
				try{
					$Account->update_profile($params, ITEM_ID);

					//If updating password
					if($_POST['password']){
						try{
							$Account->reset_password($_POST['password'], $_POST['password2'], ITEM_ID);
						}catch(Exception $e){
							$errors[] = 'Unable to update password. '.$e->getMessage();
						}
					}

					//Update account roles
					try{
						$Account->update_account_roles($roles, ITEM_ID);
					}catch(Exception $e){
						$errors[] = 'Unable to update account roles. '.$e->getMessage();
					}

					//Update account permissions
					$db->new_transaction();
					$db->query("DELETE FROM cms_permissions WHERE $record_id = ?", [ITEM_ID]);
					foreach($permissions as $perm){
						$db->query("INSERT INTO cms_permissions(section_id, $record_id) VALUES(?,?)", [$perm, ITEM_ID]);
					}

					//
					//Save profile answers
					$delete = $db->query("DELETE FROM `account_profile_answers` WHERE `account_id` = ?", array(ITEM_ID));
					foreach($profile_questions as $q){
						if(isset($_POST['question-'.$q['question_id']])){
							$params = array(ITEM_ID, $q['question_id'], $_POST['question-'.$q['question_id']], date("Y-m-d H:i:s"));
							$insert = $db->query("INSERT INTO `account_profile_answers`(`account_id`, `question_id`, `answer`, `last_updated`) VALUES(?,?,?,?)", $params);
						}
					}
					//

					if(!$db->error()){
						$db->commit();
					}else{
						$errors[] = 'Unable to update account permissions. '.$db->error();
					}

				//Update error
				}catch(Exception $e){

					$errors[] = 'Unable to update user. '.$e->getMessage();

					//Clean up images
					if($avatar && isset($records_arr[ITEM_ID]['photo'])){
						$CMSUploader->bulk_delete(['photo' => $records_arr[ITEM_ID]['photo']]);
					}
				}
			}

			//
			// --- After $Account->update_profile() or $Account->register() succeeds ---
            // --- Handle Saving Committee Memberships ---
            // This needs to be done *after* the main account/profile is saved, especially for new users.
            // Let's assume $item_id holds the account_id (either $new_account_id or ITEM_ID)

            $account_id_for_committees = (ITEM_ID == "") ? ($item_id ?? null) : ITEM_ID; // $item_id from register, ITEM_ID from edit

            if ($account_id_for_committees && isset($db) && is_object($db)) {
                $submitted_committees = $_POST['committees'] ?? []; // Array of selected committee_ids
                $committee_save_error = false;

                $db->new_transaction();
                // 1. Delete existing committee memberships for this user
                $delete_old_comm = $db->query("DELETE FROM `account_committees` WHERE `account_id` = ?", [$account_id_for_committees]);
                if (!$delete_old_comm || $db->error()) {
                    error_log("CMS User Update: Failed to delete old committee memberships for account " . $account_id_for_committees . ". Error: " . $db->error());
                    $committee_save_error = true;
                } else {
                    // 2. Insert new committee memberships
                    if (is_array($submitted_committees) && !empty($submitted_committees)) {
                        foreach ($submitted_committees as $committee_id) {
                            if (empty(trim($committee_id))) continue; // Skip empty values if any
                            $committee_id = (int)$committee_id;
                            $insert_comm = $db->query("INSERT INTO `account_committees` (account_id, committee_id) VALUES (?, ?)",
                                [$account_id_for_committees, $committee_id]);
                            if (!$insert_comm || $db->error()) {
                                error_log("CMS User Update: Failed to insert committee membership " . $committee_id . " for account " . $account_id_for_committees . ". Error: " . $db->error());
                                $committee_save_error = true;
                                break; // Stop on first error
                            }
                        }
                    }
                }

                if ($committee_save_error) {
                    $db->rollback();
                    // Add to $errors array or set a specific session error for committees
                    $errors[] = 'Failed to update committee memberships.';
                } else {
                    $db->commit();
                }
            }
            // --- End Committee Memberships Handling ---
			//

			//Success
			if(!$errors && (!$avatar || !$CMSUploader->crop_queue())){
				$CMSBuilder->set_system_alert('User was successfully saved'.(ITEM_ID == '' && ($confirm_email || $welcome_email) ? ' and a confirmation email has been sent' : '').'.', true);
				header("Location: " .PAGE_URL. (SECTION_ID == $_cmssections['account'] ? "?action=edit&item_id=".ITEM_ID : ""));
				exit();
			}
		}

		//Errors
		if($errors){
			foreach($_POST as $key=>$data){
				$row[$key] = $data;
			}
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}

	//Handle images
	}else{
		if($avatar){
			$record_db = 'account_profiles';
			include('modules/CropImages.php');
			// include('includes/jcropimages.php');
		}
	}

}

?>