<?php
// pages/directory.php - Displays Directory List or Profile Detail

// Ensure session is started if not already (needed for MEMBER_ACCESS check)
if (session_status() === PHP_SESSION_NONE) { session_start(); }

// Make sure data fetched by the module is available
// These should be set by modules/account/directory.php if relevant
global $path, $db, $Account, $_sitepages;
global $is_directory_list_page, $is_profile_detail_page, $directory_list, $profile_detail;

// Define MEMBER_ACCESS constant based on Account object (adjust logic as needed)
// This should ideally be defined earlier in your application bootstrap
if (!defined('MEMBER_ACCESS')) {
    define('MEMBER_ACCESS', (isset($Account) && $Account->login_status() /* && $Account->hasRole('Member') // Add role check if needed */) ? true : false);
}

// Define image paths
$profile_image_path = (isset($path) ? $path : '/') . 'images/users/'; // Path for main profile image
// $profile_thumb_path = (isset($path) ? $path : '/') . 'images/users/thumbs/'; // Path for thumbnail (if used)
$profile_thumb_path = (isset($path) ? $path : '/') . 'images/users/'; // Path for thumbnail (if used)

// ========================================
// == Display Member Profile Detail View ==
// ========================================
if ($is_profile_detail_page && isset($profile_detail) && is_object($profile_detail)):
    
    // echo "<pre>";
    // print_r($profile_detail);
    // echo "</pre>";
    // exit;

    // Use profile name for main heading if available
    $page_title = htmlspecialchars(($profile_detail->first_name ?? '') . ' ' . ($profile_detail->last_name ?? ''), ENT_QUOTES, 'UTF-8');
?>
    <div class="container container-lg member-detail-container">
    <!-- <h3>Member Profile</h3> -->
    <div class="container profile-detail-container"> 
        <div class="profile-header form-grid" style="">
            <?php // Left: Photo ?>
            <div class="profile-photo-column">
                <?php
                $photo_src = null;
                if (!empty($profile_detail->photo) && file_exists($_SERVER['DOCUMENT_ROOT'] . $profile_image_path . $profile_detail->photo)) {
                    $photo_src = htmlspecialchars($profile_image_path . $profile_detail->photo, ENT_QUOTES, 'UTF-8') . '?t=' . time();
                } elseif (!empty($profile_detail->photo) && file_exists($_SERVER['DOCUMENT_ROOT'] . $profile_thumb_path . $profile_detail->photo)) {
                     // Fallback to thumb if main doesn't exist but thumb does
                    $photo_src = htmlspecialchars($profile_thumb_path . $profile_detail->photo, ENT_QUOTES, 'UTF-8') . '?t=' . time();
                }
                ?>
                <?php if ($photo_src): ?>
                    <img src="<?php echo $photo_src; ?>" alt="<?php echo $page_title; ?>" class="profile-detail-photo" style="" />
                <?php else: ?>
                    <div class="profile-detail-no-photo"></div>
                <?php endif; ?>
            </div>

            <?php // Right: Profile Info and Social ?>
            <div class="profile-info-column">
                <h2><?php echo $page_title; ?></h2>
                <!-- <p style="margin-top: 0; margin-bottom: 5px; font-style: italic; color: #555;"><?php echo htmlspecialchars($profile_detail->class_name ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></p> -->
                <div class="profile-info-container"> 
                <div class="info-box-container left"> 
                <div class="info-container"> 
                 <?php if(!empty($profile_detail->facility_name)): ?>
                <div class="static-box"> 
                    <p>Current Golf Facility</p><p> <?php echo htmlspecialchars($profile_detail->facility_name, ENT_QUOTES, 'UTF-8'); ?></p>
                 </div>
                 <?php endif; ?>
                 <?php if(!empty($profile_detail->membership_name)): ?>
                 <div class="static-box">
                    <p>Member Classification</p><p> <?php echo htmlspecialchars($profile_detail->membership_name, ENT_QUOTES, 'UTF-8'); ?></p>
                 </div>
                 <?php endif; ?>
                 </div>
                 </div>
                 <div class="info-box-container"> 
                <div class="info-container"> 
                <?php if(checkmail($profile_detail->email ?? '') && ($profile_detail->show_email == '1' || ($profile_detail->show_email == '-1' && MEMBER_ACCESS))): ?>
                <div class="static-box"> 
                     <p style="margin-bottom: 5px;">Email Address</p><p><a href="mailto:<?php echo htmlspecialchars($profile_detail->email, ENT_QUOTES, 'UTF-8'); ?>"><?php echo htmlspecialchars($profile_detail->email, ENT_QUOTES, 'UTF-8'); ?></a></p>
                </div>
                 <?php endif; ?>

                <?php if(!empty($profile_detail->website)): ?>
                <div class="static-box">
                     <?php $website_url = strpos($profile_detail->website, '://') === false ? 'http://' . $profile_detail->website : $profile_detail->website; ?>
                    <p>Website</p>
                    <p>
                     <a href="<?php echo htmlspecialchars($website_url, ENT_QUOTES, 'UTF-8'); ?>" target="_blank" rel="noopener noreferrer"><?php echo htmlspecialchars($profile_detail->website, ENT_QUOTES, 'UTF-8'); ?></a>
                    </p>
                 </div>
                 <?php endif; ?>
                 </div>
                 </div>
                 </div>
                 <?php // Contact Info - Check privacy settings! ?>

                <?php // Social Media Icons ?>
                <?php
                    $social_links = [
                        'instagram' => $profile_detail->instagram ?? '',
                        'facebook' => $profile_detail->facebook ?? '',
                        'linkedin' => $profile_detail->linkedin ?? '',
                        // 'twitter' => $profile_detail->twitter ?? '',
                    ];
                    $has_social = false;
                    foreach($social_links as $link) { if (!empty(trim($link))) { $has_social = true; break; } }
                ?>
                <div class="social-icons-container">
                    <ul class="social-icons profile-social" itemscope itemtype="http://schema.org/Organization">
                        <link itemprop="url" href="<?php echo $siteurl; ?>"> 
                        <?php
                        foreach($social_links as $service=>$url){
                            // if($service == 'facebook' || $service == 'linkedin' || $service == 'instagram'){
                            echo (trim($url) != '' ? '<li><a itemprop="sameAs" href="'.$url.'" target="_blank"><span class="fa-brands fa-'.$service.'"></span>'.$service.'</a></li>' : '');
                            // }
                        }
                        ?>
                    </ul>
                    <?php //if ($has_social): ?>
                     <!-- <ul class="social-icons" style="list-style: none; padding: 0; margin: 0;">
                         <?php if(!empty(trim($social_links['facebook']))): ?><li style="display:inline-block; margin-right: 10px;"><a href="<?php echo htmlspecialchars($social_links['facebook'], ENT_QUOTES, 'UTF-8'); ?>" target="_blank" rel="noopener noreferrer" title="Facebook"><span class="fa fa-facebook-square fa-lg"></span></a></li><?php endif; ?>
                         <?php if(!empty(trim($social_links['twitter']))): ?><li style="display:inline-block; margin-right: 10px;"><a href="<?php echo htmlspecialchars($social_links['twitter'], ENT_QUOTES, 'UTF-8'); ?>" target="_blank" rel="noopener noreferrer" title="Twitter"><span class="fa fa-twitter-square fa-lg"></span></a></li><?php endif; ?>
                         <?php if(!empty(trim($social_links['linkedin']))): ?><li style="display:inline-block; margin-right: 10px;"><a href="<?php echo htmlspecialchars($social_links['linkedin'], ENT_QUOTES, 'UTF-8'); ?>" target="_blank" rel="noopener noreferrer" title="LinkedIn"><span class="fa fa-linkedin-square fa-lg"></span></a></li><?php endif; ?>
                         <?php if(!empty(trim($social_links['instagram']))): ?><li style="display:inline-block; margin-right: 10px;"><a href="<?php echo htmlspecialchars($social_links['instagram'], ENT_QUOTES, 'UTF-8'); ?>" target="_blank" rel="noopener noreferrer" title="Instagram"><span class="fa fa-instagram fa-lg"></span></a></li><?php endif; ?>
                     </ul> -->
                    <?php // endif; ?>
                    <div>
                        <img src="<?php echo $path; ?>images/top_100.png" />
                    </div>
                </div>

                <?php // Back Link ?>
                <div class="back-button-container">
                    <a href="<?php echo htmlspecialchars($_sitepages['directory']['page_url'], ENT_QUOTES, 'UTF-8'); ?>" class="button primary red back-button">Back to All<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
                </div>
            </div>
        </div> <?php // end profile-header grid ?>
        <?php // Other sections like PD Points can be added here ?>
    </div> 
    </div> <?php // end profile-detail-container ?>

<?php
// ===========================
// == Display Directory List ==
// ===========================
else:
    // We assume if not detail page, it's the list page (module sets $is_directory_list_page)
    $search_term = trim($_GET['search'] ?? '');
?>
    
    <div class="container container-lg">
    <h3><?php echo $page['page_title'] ?? 'Find a Pro'; ?></h3>
    <!--  -->    
    <div class="directory-controls-container"> <?php // New container for search and login message ?>
        <?php // Search Form ?>
        <form name="search-form" id="directory-search-bar"
            action="<?php echo htmlspecialchars($_sitepages['directory']['page_url'], ENT_QUOTES, 'UTF-8'); ?>"
            method="get" class="clearfix directory-search-form">

            <div class="search-input-container">
                <input type="text" name="search" class="input directory-search-input"
                    value="<?php echo htmlspecialchars($search_term, ENT_QUOTES, 'UTF-8'); ?>"
                    placeholder="Search" />

                <?php if(!empty($search_term)): ?>
                    <a href="<?php echo htmlspecialchars($_sitepages['directory']['page_url'], ENT_QUOTES, 'UTF-8'); ?>"
                    title="Clear Search" class="clear-search-btn">×</a>
                <?php endif; ?>

                <button type="submit" class="button search-icon-btn" title="Search">
                    <i class="fa fa-search"></i> <?php // Font Awesome icon ?>
                </button>
            </div>
            <?php // Visually hidden text button for accessibility/fallback ?>
            <button type="submit" class="button primary visually-hidden">Search</button>
        </form>

        <?php if(!MEMBER_ACCESS): // Check if user is NOT a logged-in member ?>
            <div class="directory-login-prompt">
                <p><small>Members <a href="<?php echo htmlspecialchars($_sitepages['login']['page_url'] . '?redirect=' . urlencode($_SERVER['REQUEST_URI']), ENT_QUOTES, 'UTF-8'); ?>">Login here</a> to see full profiles.</small></p>
            </div>
        <?php endif; ?>

    </div> <?php // End .directory-controls-container ?>
    <!--  -->

    <?php // Directory Table ?>
    <table cellpadding="10" cellspacing="0" border="0" width="100%" class="directory-table" style="border-collapse: collapse; width: 100%;">
        <thead>
            <tr style="border-bottom: 0px solid #fff;">
                <th style="text-align: left; padding: 8px;">Name</th>
                <th style="text-align: left; padding: 8px;">Facility</th>
                <?php // if(MEMBER_ACCESS): // Show contact info only if logged in ?>
                <th style="text-align: left; padding: 8px;">Email</th>
                <th style="text-align: left; padding: 8px;">Phone</th>
                <?php // endif; ?>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($directory_list)): ?>
                <?php foreach($directory_list as $member): ?>
                    <?php
                        // Construct profile URL
                        $profile_url = $_sitepages['directory']['page_url'] . clean_url($member['first_name'].'-'.$member['last_name'].'-'.$member['profile_id']) . '/';
                    ?>
                    <tr style="">
                        <td style="padding: 8px;">
                            <a href="<?php echo htmlspecialchars($profile_url, ENT_QUOTES, 'UTF-8'); ?>">
                                <?php echo htmlspecialchars($member['last_name'] ?? '', ENT_QUOTES, 'UTF-8') . ', ' . htmlspecialchars($member['first_name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>
                            </a>
                            <?php // if (!empty($member['class_name'])): ?>
                                <small style="display: block; color: #666;"><?php echo htmlspecialchars($member['class_name'], ENT_QUOTES, 'UTF-8'); ?></small>
                            <?php // endif; ?>
                        </td>
                        <td style="padding: 8px;"><?php echo htmlspecialchars($member['facility_name'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></td>

                        <?php // if(MEMBER_ACCESS): // Show contact info only if logged in ?>
                            <td style="padding: 8px;">
                                <?php if(checkmail($member['email'] ?? '') && ($member['show_email'] == '1' || ($member['show_email'] == '-1' /* && MEMBER_ACCESS is already true */))): ?>
                                    <a href="mailto:<?php echo htmlspecialchars($member['email'], ENT_QUOTES, 'UTF-8'); ?>"><?php echo htmlspecialchars($member['email'], ENT_QUOTES, 'UTF-8'); ?></a>
                                <?php else: echo ' '; // Keep alignment ?>
                                <?php endif; ?>
                            </td>
                            <td style="padding: 8px;">
                                 <?php
                                    $display_phone = '';
                                    if(($member['show_phone'] ?? '0') == '1' || (($member['show_phone'] ?? '0') == '-1' /* && MEMBER_ACCESS */)) {
                                        if (!empty(trim($member['phone'] ?? ''))) { $display_phone = $member['phone']; }
                                        elseif (!empty(trim($member['phone_alt'] ?? ''))) { $display_phone = $member['phone_alt']; }
                                    }
                                 ?>
                                 <?php if(!empty($display_phone)): ?>
                                     <a href="tel:<?php echo formatIntlNumber($display_phone); ?>"><?php echo formatPhoneNumber($display_phone); ?></a>
                                 <?php else: echo ' '; // Keep alignment ?>
                                 <?php endif; ?>
                            </td>
                         <?php // endif; // End MEMBER_ACCESS check ?>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="<?php echo MEMBER_ACCESS ? '4' : '2'; ?>" style="padding: 15px; text-align: center;">
                        No members found<?php echo !empty($search_term) ? ' matching your search "' . htmlspecialchars($search_term, ENT_QUOTES, 'UTF-8') . '"' : ''; ?>.
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    </div>

    <?php // Optional: Add Pagination if needed ?>

<?php endif; // End List/Detail View Conditional ?>