<?php

//Dashboard widget
if(SECTION_ID == 4){
	$total_records = $db->get_record_count('blog_entries');
	$CMSBuilder->set_widget($_cmssections['blog_entries'], 'Total '.$sitemap[$_cmssections['blog']]['name'].' Entries', $total_records, $sitemap[$_cmssections['blog']]['icon']);
}

if(SECTION_ID == $_cmssections['blog_entries']){

	//Define vars
	$record_db = 'blog_entries';
	$record_id = 'entry_id';
	$record_name = 'Entry';
	$record_names = 'Entries';

	//Validation
	$errors   = false;
	$required = [];
	$required_fields = ['title', 'description', 'content'];

	if($CMSBuilder->get_section_status($_cmssections['blog_categories']) == 'Enabled'){
		$required_fields[] = 'category_id';
	}
	if($CMSBuilder->get_section_status($_cmssections['blog_authors']) == 'Enabled'){
		$required_fields[] = 'author_id';
	}

	//Image Uploader
	$imagedir = '../images/blog/entries/';
	$CMSUploader = new CMSUploader('blog_entry', $imagedir);
	
	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.title",
		"blog_categories.name"
	];

	//Redirects
	$seo_page_id     = $_sitepages['blog'];
	$blog_page_url   = get_page_url($seo_page_id);
	$cat_sectionurl  = $CMSBuilder->get_section($_cmssections['blog_categories'])['page_url'];
	$auth_sectionurl = $CMSBuilder->get_section($_cmssections['blog_authors'])['page_url'];


	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;

	//Build search query
	}else if(isset($_GET['search'])){
		
		if($searchterm){
			foreach($searchable_fields as $key => $field){
				$searchable_fields[$key] = "$field LIKE ?";
				$params[] = '%'.$searchterm.'%';
			}
			$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
		}

		if(!empty($_GET['start_date'])){
			$where .= ($where ? 'AND ' : 'WHERE ')."$record_db.post_date >= ? ";
			$params[] = $_GET['start_date'];
		}

		if(!empty($_GET['end_date'])){
			$where .= ($where ? 'AND ' : 'WHERE ')."$record_db.post_date <= ? ";
			$params[] = $_GET['end_date'];
		}

		if(!empty($_GET['category_id'])){
			$where .= ($where ? 'AND ' : 'WHERE ')."$record_db.category_id = ? ";
			$params[] = $_GET['category_id'];
		}

		if(!empty($_GET['author_id'])){
			$where .= ($where ? 'AND ' : 'WHERE ')."$record_db.author_id = ? ";
			$params[] = $_GET['author_id'];
		}

	}
	
	//Get Records
	$db->query("SELECT 
		`$record_db`.*, 
		`blog_categories`.`page` as `category_page`, 
		`blog_categories`.`name` as `category_name`, 
		`blog_authors`.`name` as `author_name`, 
		`blog_authors`.`page` as `author_page` 
	FROM `$record_db`
	LEFT JOIN `blog_categories` ON `blog_categories`.`category_id` = `$record_db`.`category_id` 
	LEFT JOIN `blog_authors` ON `blog_authors`.`author_id` = `$record_db`.`author_id`
	$where 
	ORDER BY `post_date` DESC", $params);
	$records_arr = $db->fetch_assoc($record_id);
	$today = new DateTime('today');
	foreach ($records_arr as $item_id => &$record) {
		$post = new DateTime($record['post_date']);
		$record['is_future']     = $post > $today;
		
		$record['image']         = check_file($record['image'], $imagedir.'thumbs/');
		$record['full_image']    = $record['image'] ? $imagedir.'thumbs/'.$record['image'] : false;
		
		$record['archive']       = date('mY', strtotime($record['post_date']));
		
		$record['cat_cms_url']   = $cat_sectionurl.'?action=edit&item_id='.$record['category_id'];
		$record['cat_page_url']  = $siteurl.$root.$blog_page_url.$record['category_page'].'-'.$record['category_id'].'/';

		$record['auth_cms_url']  = $auth_sectionurl.'?action=edit&item_id='.$record['author_id'];
		$record['auth_page_url'] = $siteurl.$root.$blog_page_url.$record['author_page'].'-'.$record['author_id'].'/';

		if($CMSBuilder->get_section_status($_cmssections['blog_categories']) == 'Enabled'){
			$record['page_url'] = $record['cat_page_url'].$record['page'].'-'.$item_id.'/';
		} else {
			$record['page_url'] = $siteurl.$root.$blog_page_url.$record['archive'].'/'.$record['page'].'-'.$item_id.'/';
		}

		unset($record);
	}

	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}
	
	
	//Get categories
	$db->query("SELECT * FROM blog_categories ORDER BY ordering");
	$blog_cats = $db->fetch_assoc('category_id');

	//Get authors
	$db->query("SELECT * FROM blog_authors ORDER BY ordering, name");
	$blog_authors = $db->fetch_assoc('author_id');
	
	
	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];
		}
	}
	
	//Delete item
	if(isset($_POST['delete'])){
		
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()) {

			//Delete images
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);

			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}

		header("Location: " .PAGE_URL);
		exit();
			
	//Save item
	}else if(isset($_POST['save'])){

		//Set default values
		$_POST['showhide'] = !isset($_POST['showhide']);
		
		//Format data
		$_POST['content'] = trim(str_replace("<p>&nbsp;</p>", "", $_POST['content']));
		$post_date = DateTime::createFromFormat('Y-m-d', $_POST['post_date']);

		//Set SEO tools if they don't exist
		$_POST['focus_keyword'] = $_POST['focus_keyword'] ?? $records_arr[ITEM_ID]['focus_keyword'] ?? NULL;
		
		//Required fields validation
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Date validation
		if($_POST['post_date'] && !$post_date) {
			$errors[] = 'Please enter a valid date.';
			$required[] = 'post_date';
		}

		//Image validation
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large.';
			$required[] = 'image';
		}
	
		if(!$errors){
		
			//Format validated data
			$is_future = $post_date > $today;
			$post_date = $post_date ? $post_date->format('Y-m-d') : date('Y-m-d');
			$pagename  = clean_url($_POST['title']);
			$archive   = date('mY', strtotime($_POST['post_date']));
			$content   = (trim($_POST['content']) != '' ? $_POST['content'] : NULL);
			
			//Delete image
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//Upload image
			try{
				$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}
	
			//Insert to db
			$params = array(
				$record_id => ITEM_ID,
				'category_id' => $_POST['category_id'] ?? 1,
				'author_id' => $_POST['author_id'] ?? NULL,
				'title' => $_POST['title'],
				'page' => $pagename,
				'description' => $_POST['description'],
				'content' => $content,
				'image' => $images['image'] ?? NULL,
				'image_alt' => $_POST['image_alt'],
				'meta_title' => $_POST['meta_title'],
				'meta_description' => $_POST['meta_description'],
				'focus_keyword' => $_POST['focus_keyword'],
				'post_date' => $post_date,
				'showhide' => $_POST['showhide'],
				'last_modified' => date("Y-m-d H:i:s"),
			);
			$db->insert($record_db, array_merge($params, ['date_added' => date('Y-m-d H:i:s')]), $params);
			if(!$db->error()){
				$item_id = (ITEM_ID != "" ? ITEM_ID : $db->insert_id());
				
				//Save sitemap
				sitemap_XML();
				
				//Save RSS feed
				blog_rss();

				//Save SEO score
				if(!$is_future && $cms_settings['enhanced_seo']) {

					//Set new page_url
					$page_url = $siteurl.$root.$blog_page_url.$archive."/".$pagename."-".$item_id."/";

					try{
						$save_score = $Analyzer->save_score($_POST['focus_keyword'], $page_url, $pagename, $_POST['title'], ($records_arr[ITEM_ID]['seo_score'] ?? NULL), $item_id, $record_db, $record_id);
						if(!empty($save_score) && (MASTER_USER || SEO_USER)){
							$seo_message = "<br/><small>".$save_score."</small>";
						}
					}catch(Exception $e){
						unset($e);
					}
				}
				
				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.' . (isset($seo_message) ? $seo_message : ''), true);
					header("Location: " .PAGE_URL);
					exit();
				}
				
			}else{
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}
			
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}

		foreach($_POST AS $key=>$data){
			$row[$key] = $data;
		}
	
	//Handle images
	}else{
		include('modules/CropImages.php');
	}

}

?>