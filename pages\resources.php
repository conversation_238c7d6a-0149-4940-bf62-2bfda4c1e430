<?php

// exit('resources');

//Check for valid login
// if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
// 	exit();
// }

// //Restrict to members
// if(!defined('MEMBER_ACCESS') || !MEMBER_ACCESS){
// 	exit();
// }

// Check for upload success message
if (isset($_SESSION['upload_success'])) {
    $success = $Account->alert($_SESSION['upload_success'], true);
    unset($_SESSION['upload_success']);
}

$html = '';

// Load Panel Content
if(!empty($page['page_panels'][$panel_id]['content'])){
	$html .= (trim($page['page_panels'][$panel_id]['content']) != '' ? $page['page_panels'][$panel_id]['content'].'<hr />' : '');
}

// Display alerts
if (!empty($success)) {
	$html .= '<div class="alert alert-success" style="background-color: #dff0d8; border: 1px solid #d6e9c6; color: #3c763d; padding: 15px; margin-bottom: 20px;">' . $success . '</div>';
}
if (!empty($alert)) {
	$html .= '<div class="alert alert-danger" style="background-color: #f2dede; border: 1px solid #ebccd1; color: #a94442; padding: 15px; margin-bottom: 20px;">' . $alert . '</div>';
}

$html .= '<div id="member-resources" class="container">';

// Search form
$html .= '<div id="resource-search-wrapper">
	<form name="search-form" id="search-bar" action="" method="get" class="clearfix">
		<input type="text" name="search" class="input search-input" placeholder="Search" value="' .(isset($_GET['search']) ? $_GET['search'] : ''). '" placeholder="Search Files" />
		<button type="submit" class="button solid"><i class="fa fa-search"></i></button>
		' .(isset($_GET['search']) && trim($_GET['search']) != '' ? '<a href="' .$page['page_url']. '" class="fa fa-times-circle"></a>' : ''). '
	</form>
</div>';

// Display folders
$count = 0;

// echo '<pre>folders - ';
// print_r ($folders);
// 		echo "</pre>";
// 		// exit;

if(!empty($folders)){
	$html .= '<div id="resource-folders-wrapper">';
	$html .= '<h4>Browse Folders</h4><hr />';

	foreach($folders as $folder){
		$html .= '<div class="resource-folder-card">
			<div class="folder-header" data-folder-id="'.$folder['category_id'].'">
				<div class="folder-info">
					<h5><i class="fa fa-folder' .(!isset($_POST['category_id']) || (isset($_POST['category_id']) && $folder['category_id'] != $_POST['category_id']) ? '' : '-open'). '"></i> ' .$folder['name']. '</h5>
					<small class="file-count">' .count($folder['files']). ' files</small>
				</div>
				<div class="folder-actions">
					<button type="button" class="button solid upload-btn" onclick="fileUploader(' .$folder['category_id']. ');">
						<i class="fa fa-upload"></i> Upload File
					</button>
					<button type="button" class="folder-toggle">
						<i class="fa fa-chevron-down"></i>
					</button>
				</div>
			</div>';

		$html .= '<div class="folder-content" style="display:' .(!isset($_POST['category_id']) || (isset($_POST['category_id']) && $folder['category_id'] != $_POST['category_id']) ? 'none' : 'block'). ';">';

		if(!empty($folder['files'])){
			$html .= '<div class="files-grid">';
			foreach($folder['files'] as $file){
				$html .= '<div class="file-card">
					<div class="file-icon">
						<i class="fa ' .($file['file_type'] != '' ? 'fa-file-'.$file['file_type'].'-o' : 'fa-file-o'). '"></i>
					</div>
					<div class="file-info">
						<h6><a href="' .$path.'download.php?file=' .$file['file_location']. '&dir=resources" target="_blank">' .$file['file_name']. '</a></h6>
						<small class="file-meta">
							<span class="file-size">' .$file['file_size']. '</span> • 
							<span class="file-author">Posted by ' .$file['posted_by']. '</span><br>
							<span class="file-date">' .date("M j, Y", strtotime($file['date_added'])). '</span>
						</small>
					</div>';

				//logged in user can delete their own files	
				// if($file['account_id'] == USER_LOGGED_IN){
				// 	$html .= '<div class="file-actions">
				// 		<form action="" method="post" class="delete-form">
				// 			<button type="button" name="button" class="delete-btn"><i class="fa fa-trash"></i></button>
				// 			<input type="hidden" name="delete" value="' .$file['file_id']. '" />
				// 			<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
				// 		</form>
				// 	</div>';	
				// }

				$html .= '</div>'; // Close file-card
			}
			$html .= '</div>'; // Close files-grid
		}else{
			$html .= '<div class="no-files">
				<p>No files in this folder yet.</p>
			</div>';
		}

		$html .= '</div>'; // Close folder-content
		$html .= '</div>'; // Close resource-folder-card
		$count++;
	}
	$html .= '</div>'; // Close resource-folders-wrapper
}

// Display search results or uncategorized files
if(!empty($files)){
	$html .= '<div id="resource-files-wrapper">';
	$html .= '<h4>' .($searchterm != '' ? 'Search Results' : 'Uncategorized Files'). '</h4><hr />';
	$html .= '<div class="files-grid">';

	foreach($files as $file){
		$html .= '<div class="file-card">
			<div class="file-icon">
				<i class="fa ' .($file['file_type'] != '' ? 'fa-file-'.$file['file_type'].'-o' : 'fa-file-o'). '"></i>
			</div>
			<div class="file-info">
				<h6><a href="' .$path.'download.php?file=' .$file['file_location']. '&dir=resources" target="_blank">' .$file['file_name']. '</a></h6>
				<small class="file-meta">
					<span class="file-size">' .$file['file_size']. '</span> • 
					<span class="file-author">Posted by ' .$file['posted_by']. '</span><br>
					<span class="file-date">' .date("M j, Y", strtotime($file['date_added'])). '</span>
				</small>
			</div>';

		// if($file['account_id'] == USER_LOGGED_IN){
		// 	$html .= '<div class="file-actions">
		// 		<form action="" method="post" class="delete-form">
		// 			<button type="button" name="button" class="delete-btn"><i class="fa fa-trash"></i></button>
		// 			<input type="hidden" name="delete" value="' .$file['file_id']. '" />
		// 			<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
		// 		</form>
		// 	</div>';	
		// }

		$html .= '</div>'; // Close file-card
	}
	$html .= '</div>'; // Close files-grid
	$html .= '</div>'; // Close resource-files-wrapper
}

// No files found
if(empty($files) && empty($folders)){
	$html .= '<div class="no-resources">
		<div class="empty-state">
			<i class="fa fa-folder-open-o"></i>
			<h5>No files found' .($searchterm != '' ? ' matching <strong>`' .$searchterm. '`</strong>' : ''). '</h5>
			<p>Check back later or contact an administrator.</p>
		</div>
	</div>';
}

$html .= '</div>'; // Close container

// Upload modal
$html .= '<div class="hidden">
	<div id="file-uploader" class="hidden-modal" title="Upload File" data-modal-width="600">
		<div class="upload-modal-content">
			<p class="upload-info"><small>2MB maximum, ' .implode(', ', $imagetypes).', '.implode(', ', $filetypes). '.</small></p>
			<form name="upload-form" id="upload-form" action="" method="post" enctype="multipart/form-data">
				<div id="alert-msg"></div>

				<div class="form-field">
					<label>File Name</label>
					<input type="text" name="file_name" class="input" placeholder="Enter file name" />
				</div>

				<div class="form-field">
					<label>Select File</label>
					<div class="input-file-container">  
						<input class="input-file" id="file" name="file" type="file" value="" />
						<label tabindex="0" for="file" class="input-file-trigger"><i class="fa fa-upload"></i>Select a file...</label>
					</div>
				</div>

				<div class="form-buttons">
					<button type="button" name="upload" class="button primary red">
						<span class="left-border"></span><span class="right-border"></span>
						<i class="fa fa-upload"></i> Upload File
					</button>
				</div>

				<input type="hidden" name="upload" value="true" />
				<input type="hidden" name="category_id" value="" />
				<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
			</form>
		</div>
	</div>
</div>';

// // CSS Styles
// $html .= '<style>
// </style>';

// // JavaScript
// $html .= '<script>

// </script>';

// Set panel content
$page['page_panels'][$panel_id]['content'] = $html;

//Page panels
include("includes/pagepanels.php");
?>