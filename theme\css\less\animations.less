
@charset "utf-8";
/*
	animations.less

*/

@import "../../../core/less/animations.less";

.keyframes(clip-in-ttb, {
	0% 	 {clip-path: inset(0 0 100% 0); }
	100% {clip-path: inset(-100%); }
});

.keyframes(clip-in-ltr, {
	0% 	 {clip-path: inset(-100% 100% -100% 0); }
	100% {clip-path: inset(-100% -100% -100% -100%); }
});

@media @tablet-l and (prefers-reduced-motion: no-preference) {
	:root{--animation-trans-length: 20px;}

	
	html.no-touch{
		// Slideshow
		#slideshow{
			.slide{
				&.swiper-slide-active, &:where(#slideshow:not(.swiper-initialized) .slide){
					--animation-play-state: running;

					.slide-title, .slide-text{
						.animation(fade-in; 0.6s; ease-out; 0.3s;);
					}

					.fancy-text{
						.animation(fade-in; 0.6s; ease-out; 0.45s;);
					}

					.button:nth-child(1){
						.animation(fade-in-ltr; 0.6s; ease-out; 0.6s;);
					}

					.button:nth-child(2){
						.animation(fade-in-rtl; 0.6s; ease-out; 0.6s;);
					}

					.button:only-child{
						.animation(fade-in-btt; 0.6s; ease-out; 0.6s;);
					}
				}
			}
		}

		// Banner
		#page-hero{
			.page-title-wrapper::before{
				.animation(clip-in-ttb; 0.6s; ease-in-out; 0.3s;);
			}

			.page-title, .page-subtitle{
				.animation(fade-in-btt; 0.6s; ease-out;);
			}

			.fancy-text{
				.animation(fade-in; 0.6s; ease-out; 0.15s;);
			}

			.page-buttons{
				.animation(fade-in-btt; 0.6s; ease-out; 0.3s;);
			}
		}

		// Panels
		.panel{
			&.standard, &.promo, &.mini-promo, &.gallery{
				.panel-header{
					//stopped
					// .container::before{
					// 	.animation(clip-in-ttb; 0.6s; ease-in-out; 0.3s;);
					// }

					h2{
						.animation(fade-in-ltr; 0.6s; ease-out;);
					}

					.fancy-text{
						.animation(fade-in; 0.6s; ease-out; 0.15s;);
					}
				}
			}

			&.cta{
				.panel-title::before{
					.animation(clip-in-ttb; 0.6s; ease-in-out; 0.3s;);
				}

				.panel-subtitle{
					.animation(fade-in-ltr; 0.6s; ease-out;);
				}

				.button{
					.animation(fade-in-rtl; 0.6s; ease-out;);
				}

				&:last-child{
					&::before{
						.animation(clip-in-ttb; 0.6s; ease-in-out; 0.3s;);
					}

					.panel-title h2, .panel-subtitle{
						.animation(fade-in-btt; 0.6s; ease-out;);
					}

					.button:nth-child(1){
						.animation(fade-in-ltr; 0.6s; ease-out; 0.3s;);
					}

					.button:nth-child(2){
						.animation(fade-in-rtl; 0.6s; ease-out; 0.3s;);
					}

					.button:only-child{
						.animation(fade-in-btt; 0.6s; ease-out; 0.3s;);
					}
				}
			}

			&.parallax{
				&::before{
					.animation(clip-in-ttb; 0.6s; ease-in-out; 0.3s;);
				}

				.panel-header, .panel-content{
					.animation(fade-in; 0.6s; ease-out; 0.3s;);
				}

				.fancy-text{
					.animation(fade-in; 0.6s; ease-out; 0.45s;);
				}
			}

			&.side{
				.panel-content{
					.animation(fade-in; 0.6s; ease-out;);
				}

				.panel-header::before{
					.animation(clip-in-ttb; 0.6s; ease-in-out; 0.6s;);
				}

				.fancy-text{
					.animation(fade-in; 0.6s; ease-out; 0.15s;);
				}
			}
		}

		// Promo Boxes
		.promo-box{
			.animation(fade-in; 0.6s; ease-out;);
		}

		// Mini Promo Boxes
		.mini-promo-box{
			.animation(fade-in; 0.6s; ease-out;);
		}

		// Galleries
		.gal-item{
			.animation(fade-in; 0.6s; ease-out;);
		}

		// Tabs
		.content-tabs .tabs-nav::after{
			.animation(clip-in-ltr; 0.6s; ease-in-out; 0.3s;);
		}
	}
}

@media @notebook and (prefers-reduced-motion: no-preference) {
	html.no-touch{
		#page-hero{
			.page-title, .page-subtitle{
				.animation(fade-in-ltr; 0.6s; ease-out;);
			}

			.page-buttons{
				.animation(fade-in-rtl; 0.6s; ease-out;);
			}
		}

		.panel{
			&.side{
				.panel-right{
					.animation(fade-in-ltr; 0.6s; ease-out;);
				}

				.panel-left{
					.animation(fade-in-rtl; 0.6s; ease-out;);
				}
			}
		}
	}
}