<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN) {
	exit();
}

//Dashboard widget
if(SECTION_ID == 4) {
	$total_records = $db->get_record_count('partners');
	$CMSBuilder->set_widget($_cmssections['partners'], 'Total Partners', $total_records);
}

if(SECTION_ID == $_cmssections['partners']) {
	
	// Define vars
	$record_db    = 'partners';
	$record_id    = 'partner_id';
	$record_name  = 'Partner';
	$records_name = 'Partners';

	$cat_cms_url = $CMSBuilder->get_section($_cmssections['partner_categories'])['page_url'];
	$categories_enabled = $CMSBuilder->get_section_status($_cmssections['partner_categories']) == 'Enabled';
	
	// Validation
	$errors   = false;
	$required = [];
	$required_fields = ['name'];
	if ($categories_enabled) {
		$required_fields[] = 'category_id';
	}

	// Image cropping
	$imagedir    = "../images/partners/";
	$CMSUploader = new CMSUploader('partners', $imagedir);

	// Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.name",
		"$record_db.url",
		"partner_categories.name"
	];

	// Build search query
	if ($searchterm) {
		foreach ($searchable_fields as $key => $field) {
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	// Get category
	$db->query("SELECT * FROM partner_categories ORDER BY ordering");
	$categories = $db->fetch_assoc('category_id');


	// Get Records
	$db->query("SELECT 
		$record_db.*, 
		partner_categories.name as category_name 
	FROM $record_db 
	LEFT JOIN partner_categories ON partner_categories.category_id = $record_db.category_id 
	$where 
	ORDER BY $record_db.ordering", $params);
	$records_arr = $db->fetch_assoc($record_id);
	foreach ($records_arr as $item_id => &$record) {
		$record['cat_cms_url'] = $cat_cms_url.'?action=edit&item_id='.$record['category_id'];
		$record['image'] = check_file($record['image'], $imagedir);

		unset($record);
	}
	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	
	// Not found
	if(ACTION == 'edit' && empty($records_arr[ITEM_ID])) {
		if (empty($records_arr[ITEM_ID])) {
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}

	
	// Delete item
	if(isset($_POST['delete'])) {
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()) {
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

		} else {
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		
		header("Location: " .PAGE_URL);
		exit();


	// Save item
	} else if(isset($_POST['save'])) {
		
		// Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		// Validate image
		if(empty($records_arr[ITEM_ID]['image']) && empty($_FILES['image']['name'])) {
			$errors[] = 'Please upload an image.';
			$required[] = 'image';
		}
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > 2480000) {
			$errors[] = 'Image filesize is too large.';
			$required[] = 'image';
		}
	
		if(!$errors) {

			// Prepend with http
			$pagename     = clean_url($_POST['name']);
			$_POST['url'] = (substr($_POST['url'], 0, 4) === 'www.' ? 'http://' : '').$_POST['url'];

			//Delete old images
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//Upload new images
			try{
				$images = $CMSUploader->bulk_upload($_POST['name'], $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}


			// Insert to db
			$params = [
				'name'        => $_POST['name'],
				'category_id' => $_POST['category_id'] ?? NULL ?: NULL,
				'url'         => $_POST['url'],
				'image'       => $images['image'],
				'image_alt'   => $_POST['image_alt'],
				'ordering'    => $_POST['ordering'],
				'showhide'    => !isset($_POST['showhide']),
			];
			$db->insert($record_db, [$record_id => ITEM_ID] + $params, $params);
			if(!$db->error()) {
				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}
			} else {
				$CMSBuilder->set_system_alert('Unable to save record.', false);
			}
			
		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);	
		}

	//Handle images
	} else include('modules/CropImages.php');
}

?>