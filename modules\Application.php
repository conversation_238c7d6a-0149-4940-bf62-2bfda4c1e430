<?php  
// ini_set('display_errors', 1);
// 	ini_set('display_startup_errors', 1);
// 	error_reporting(E_ALL);
//Application Form
if(PAGE_ID == $_sitepages['application']['page_id']){
	
	$career_id = (isset($_GET['id']) ? $_GET['id'] : NULL);

	//Define vars
	$errors = array();
	$required = array();
	$required_fields = array('first_name', 'last_name', 'email', 'phone');
	$row = array();
	$panel_id = 55;
	//Load category data
	$category_query = $db->query("SELECT * FROM `career_categories` ORDER BY `category_name`");
	if($category_query && !$db->error()){
		if($db->num_rows() > 0){
			$categories = $db->fetch_array();
		}
	}

	$file_fields = [
		'resume'
	];

	$query = $db->query("SELECT careers.*, facility_name, category_name, city, province FROM `careers` ".
	"LEFT JOIN `career_categories` ON `career_categories`.`category_id` = `careers`.`category_id` ".
	"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `careers`.`facility_id` ".
	"WHERE `careers`.`showhide` = 0 AND (`careers`.`category_id` IS NULL || (`careers`.`category_id` IS NOT NULL AND `career_categories`.`showhide` = 0)) AND `career_id` = ? ".
	"ORDER BY `careers`.`date_added` DESC, `career_id` DESC", array($career_id));
	if($query && !$db->error() && $db->num_rows() > 0){
		$result = $db->fetch_array();
		if(date("U") > date("U", strtotime($result[0]['date_added'])) && date("U") < date("U", strtotime($result[0]['closing_date']))){
			$career = $result[0];
		}
	}

	if(!isset($career)){
		header('Location: '.$_sitepages['job-postings']['page_url']);
		exit();
	}
	
	//Pre-fill Account Information
	if(USER_LOGGED_IN){
		$row['first_name'] = $Account->first_name;
		$row['last_name'] = $Account->last_name;
		$row['email'] = $Account->email;
		$row['phone'] = $Account->phone;
	}

	//Submit application
	if(isset($_POST['apply'])){
		
		//XSS cookie validation
		if(empty($_POST['xid']) || $_POST['xid'] != $_COOKIE['xid']){
			$errors[] = 'Invalid session. Please ensure cookies are enabled in your browser.';
			unset($_POST);
			unset($_FILES);
			
		}else{		
			
			//Validation
			foreach($required_fields as $field){
				if(!isset($_POST[$field]) || empty($_POST[$field])){
					$errors[0] = 'Please fill out all the required fields.';
					$required[] = $field;
				}
				if($field == 'phone' && !detectPhone($_POST[$field])){
					$errors[] = 'Please enter a valid phone number.';
					$required[] = $field;
				}
				if($field == 'email' && !checkMail($_POST[$field])){
					$errors[] = 'Please enter a valid email.';
					$required[] = $field;
				}
			}
			if($_FILES['resume']['name'] != ''){
				$ext = strtolower(pathinfo($_FILES['resume']['name'], PATHINFO_EXTENSION));
				
				if($_FILES['resume']['error'] == 1){
					$errors[] = 'Unable to upload resume. Please try again.';
					$required[] = 'resume';
				}else if($_FILES['resume']['size'] > 2097152){
					$errors[] = 'Attached resume is too large. File size cannot exceed 2MB.';
					$required[] = 'resume';
				}else if($ext != 'pdf'){
					$errors[] = 'Invalid file type. Only PDF Documents are accepted.';
					$required[] = 'resume';
				}
			}
			

			if(!USER_LOGGED_IN){
				
				// //Public User Recaptcha
				// require_once("includes/plugins/recaptcha/recaptchalib.php");
				// $recaptcha_response = NULL;
				// $reCaptcha = new ReCaptcha($global['recaptcha_secret']);
				// $recaptcha_response = $reCaptcha->verifyResponse(
				// 	$_SERVER["REMOTE_ADDR"],
				// 	$_POST["g-recaptcha-response"]
				// );

				// if($recaptcha_response != NULL && $recaptcha_response->success){
				// 	//recaptcha validated
				// }else{
				// 	$errors[] = 'Please verify you are not a robot.';
				// 	$required[] = 'recaptcha';
				// }
				if(validate_recaptcha()) {
					$errors['errors'] = 'Please verify you are not a robot.';
				}
			}
			
			//Valid submission
			if(empty($errors)){
			
				//Upload file
				$filedir = 'docs/resumes/';
				$filename = ($_FILES['resume']['name'] != '' ? clean_url($_POST['first_name'].$_POST['last_name']).'-resume-'.date("ymdhis").'.'.$ext : NULL);
				// if(!is_null($filename)){
				// 	include("modules/classes/FileUpload.class.php");
				// 	$fileUpload = new FileUpload();
				// 	$fileUpload->load($_FILES['resume']['tmp_name']);
				// 	$fileUpload->save($filedir, $filename);
				// }

				foreach ($file_fields as $field) {
						$$field = NULL;
					
						if (!empty($_FILES[$field]['name'])) {
							// Check for errors during upload
							if ($_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
								$errors[] = "Error uploading file: " . $_FILES[$field]['name'] . " - " . $_FILES[$field]['error'];
								continue; // Skip this file and continue with the next
							}
					
							// Attempt to copy the file
							if (!@copy($_FILES[$field]['tmp_name'], $filedir . $filename)) {
								$errors[] = "Failed to upload the file: " . $_FILES[$field]['name'];
							} else {
								// Validate file exists
								$$field = check_file($filename, $filedir) ?: NULL;
					
								// Add to array for email attachment
								if ($$field) {
									$attachments[] = $filedir . $filename;
								}
							}
						}
						
				}
				
				if(is_null($filename) || file_exists($filedir.$filename)){
				
					//Insert data
					$params = array(
						$_POST['first_name'],
						$_POST['last_name'],
						formatPhoneNumber($_POST['phone']),
						$_POST['email'],
						$filename ?: NULL,
						$career_id,
						date("Y-m-d H:i:s")
					);
					// print_r($params);
					// exit;
					$db->query("INSERT INTO `applications`(`first_name`, `last_name`, `phone`, `email`, `resume`, `career_id`, `timestamp`) VALUES(?,?,?,?,?,?,?)", $params);
					
					//Format email
					$message = '<h3>Job Application</h3>
					<p><strong>Job Title:</strong> <br />' .$career['title']. '</p>
					<p><strong>Applicant Name:</strong> <br /> ' .$_POST['first_name'].' '.$_POST['last_name']. '</p>
					<p><strong>Email Address:</strong><br /> ' .$_POST['email']. '</p>
					<p><strong>Phone Number:</strong><br /> ' .formatPhoneNumber($_POST['phone']). '</p>';

					$attachments = (!is_null($filename) ? array($filedir.$filename) : array());

					//Send to admin
					$mail_sent = true;
					if($global['email_careers'] != ''){
						$mail_sent = send_email($global['email_careers'], 'Job Application', $message, $attachments);
					}
					if($career['email'] != ''){
						$mail_sent = send_email($career['email'], 'Job Application', $message, $attachments);
					}					
					
					//Display error if both operations have failed 
					if($db->error() && !$mail_sent){
						$errors[] = 'Unable to submit application. Please try again.';
					}else{
						$success = $Account->alert('Your application has been submitted.', true);
						unset($_POST);
						unset($_FILES);
					}
					
				
				//Upload error
				}else{
					$errors[] = 'Unable to upload resume. Please try again.';
					$required[] = 'resume';
				}
				
			} else {
				foreach($_POST as $key=>$data){
					$row[$key] = $data;
				}
			}
		}
		
		//Display errors
		if(!empty($errors)){
			$alert = $Account->alert(implode('<br />', $errors), false);
		}
		
	}
}

?>