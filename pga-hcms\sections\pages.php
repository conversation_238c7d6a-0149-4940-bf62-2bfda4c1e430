<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}
	
//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<a href="' .PAGE_URL. '?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	echo '<form action="" method="post" enctype="multipart/form-data">';

		//Display pages
		echo '<div class="panel">
			<div class="panel-header">Site Pages
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">
				<table cellpadding="0" cellspacing="0" border="0" id="site-pages" class="sortable saveable">
				
					<thead>
						<th width="1px" class="nopadding-r"></th>
						<th width="34px" class="nopadding-r"></th>
						<th width="40px" class="nopadding-r"></th>
						<th>Page Name</th>
						<th>Parent Page</th>
						<th width="125px" class="center nopadding">Status '.
							$CMSBuilder->tooltip('Status', '<p><i class=\'fas fa-eye\'></i> Show &nbsp; <i class=\'fas fa-link\'></i> Hide &nbsp; <i class=\'fas fa-eye-slash\'></i> Disable</p>If you <strong>hide</strong> a page, it will be hidden from the navigation but you can still navigate to it directly. If you <strong>disable</strong> a page, it will be hidden from the navigation and you will NOT be able to navigate to it.').'
						</th>
						<th width="1px" align="right">&nbsp;</th>
						<th width="1px" align="right">&nbsp;</th>
					</thead>
					
					<tbody>';
					foreach($pages as $row){

						//Display seo score
						$classes = [];
						$seo_class = "";
						$seo_tooltip = "";
						if($row['type'] != '1' && $cms_settings['enhanced_seo'] && (MASTER_USER || SEO_USER)){ //SEO and master permissions
							$seo_indicator = $Analyzer->score_tooltip($row['seo_score']);
							$seo_class = $seo_indicator['class'];
							$seo_tooltip = $seo_indicator['tooltip'];
						}
						
						//Set page type icon
						$icon_tooltip = "";
						if($row['type'] > 0){
							$icon_tooltip = $CMSBuilder->tooltip(($row['type'] == '1' ? 'Redirect Page' : 'Landing Page'), '', '<i class="fas '.($row['type'] == '1' ? 'fa-sm fa-share' : 'fa-parachute-box').' color-theme3"></i>', ['nopadding']);
						}

						//Add utility classes for first and last children for table rows
						if($row['parent_id']){
							$pages[$row['parent_id']]['sub_pages'] = $pages[$row['parent_id']]['sub_pages'] ?? [];
							$siblings = array_keys($pages[$row['parent_id']]['sub_pages']);

							//Top shadow for first child of a group of siblings
							if(reset($siblings) == $row['page_id']){
								$classes[] = 'first-child';
							}

							//Bottom shadow for last child of a group of siblings 
							//Exception if this is a parent page, or if this is last iteration of the loop
							$page_ids = array_keys($pages);
							if(end($siblings) == $row['page_id'] && empty($row['sub_pages']) && end($page_ids) != $row['page_id']){
								$classes[] = 'last-child';
							}
						}

						$classes[] = $seo_class;

						//Determine child level/parent status of page
						echo '<tr data-level="' .$row['lvl']. '" data-table="pages" data-column-name="page_id" data-name="' .$row['name']. '" data-id="' .$row['page_id']. '" data-system-page="' .($row['system_page'] ? 'true' : 'false'). '" class="' .implode(' ', $classes). '">
							<td class="handle nopadding-r">' .$seo_tooltip. '<span class="fas fa-arrows-alt"></span></td>
							<td class="nopadding-r">' .$icon_tooltip. '</td>
							<td class="nopadding-r">'.render_gravatar($row['image_thumb'], $row['image_full'], $row['name']).'</td>
							<td class="show-lvl">' .$row['name']. '</td>
							<td class="parent-page">' .($row['parent_page'] ?? ''). '</td>
							<td class="center">' .$CMSBuilder->status_toggle('pages', 'page_id', $row['page_id'], $row['showhide']). '</td>
							<td class="right nopadding-r nowrap">
								<a href="' .PAGE_URL. '?action=edit&item_id=' .$row['page_id']. '" class="button-sm"><i class="fas fa-edit"></i>Edit</a>
							</td>
							<td class="right"><a href="' .$row['page_url']. '" target="_blank"><i class="fas fa-external-link-alt"></i></a></td>
						</tr>';
					}
	
					echo '</tbody>
				</table>
				
			</div>
		</div>
		
		<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';
	

//Image cropping
}else if($CMSUploader->crop_queue()){
	include("includes/jcropimages.php");
	

//Display form	
}else{

	$image = '';		
	if(ACTION == 'edit'){
		$data = $pages[ITEM_ID];
		$image = $data['image'];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';

	if(ITEM_ID != ""){
		echo '<nav class="actions-nav">
			<div class="column left">
				<button type="button" name="duplicate" class="button-sm confirm-submit-btn" data-confirm="Are you sure you want to duplicate this entry?"><i class="fas fa-copy"></i>Duplicate Page</button>
			</div>
			<div class="column right"><small><strong>Link to Page:</strong> '.$row['page_url'].'</small> &nbsp; <a href="'.$row['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></div>
		</nav>';
	}

	if(ITEM_ID != ""){
		echo $CMSBuilder->important("<strong>Page Deletion:</strong> If you delete a page, all subpages under that section will also be deleted. <strong>This action is not undoable.</strong>");
	}

	//Page settings
	echo '<div class="panel">
		<div class="panel-header">Page Settings
			<span class="panel-toggle fas fa-chevron-up"></span>
			<div class="panel-switch">
				<label>Status '.$CMSBuilder->tooltip('Status', '<p><i class=\'fas fa-eye\'></i> Show &nbsp; <i class=\'fas fa-link\'></i> Hide &nbsp; <i class=\'fas fa-eye-slash\'></i> Disable</p>If you <strong>hide</strong> a page, it will be hidden from the navigation but you can still navigate to it directly. If you <strong>disable</strong> a page, it will be hidden from the navigation and you will NOT be able to navigate to it.'). '</label>'.
				$CMSBuilder->status_toggle('pages', 'page_id', NULL, $row['showhide'] ?? 0).'
			</div>
		</div>
		<div class="panel-content">
			<div class="flex-container">
			
				<div class="form-field">
					<label>Button Text <span class="required">*</span></label>
					<input id="button-text" type="text" name="name" value="' .($row['name'] ?? ''). '" class="input' .(in_array('name', $required) ? ' required' : ''). '" style="margin-bottom:5px;" />
					<p class="page-landing" data-anim-inverse data-anim="fade"'.($page_type == 2 ? ' style="display:none;"' : ''). '>
						<input type="checkbox" name="highlight" id="highlight" class="checkbox" value="1"' .(strstr($row['class'] ?? '', 'highlight') ? ' checked' : ''). ' />
						<label for="highlight"><small>Highlight Button</small>' .$CMSBuilder->tooltip('Highlight Button', 'Check this box if you would like this button to appear more prominent in the navigation.'). '</label>
					</p>
				</div>
				
				<div class="form-field">
					<label>Parent Page' .(($row['system_page'] ?? false) ? $CMSBuilder->tooltip('Parent Page', 'Current page is a dynamic system page and cannot be moved.') : ''). '</label>
					<select name="parent_id" class="select animation-control' .(in_array('parent_id', $required) ? ' required' : ''). '"' .(($row['system_page'] ?? false) ? ' disabled' : ''). '>
						<option value="0" data-toggle=".parent-page">-- None --</option>';

						//Get all pages
						foreach($pages as $parent){
							if($parent['page_id'] != ITEM_ID && $parent['parent_id'] == "" && $parent['page_id'] > 3){
								echo '<option value="' .$parent['page_id']. '"' .(($row['parent_id'] ?? '') == $parent['page_id'] ? ' selected' : ''). '>' .$parent['name']. '</option>';
								//Get children
								foreach($parent['sub_pages'] as $parent2){
									if($parent2['page_id'] != ITEM_ID){
										echo '<option value="' .$parent2['page_id']. '"' .(($row['parent_id'] ?? '') == $parent2['page_id'] ? ' selected' : ''). '>&nbsp;&nbsp; &rsaquo; ' .$parent2['name']. '</option>';
										foreach($parent2['sub_pages'] as $parent3){
											if($parent3['page_id'] != ITEM_ID){
												echo '<option value="' .$parent3['page_id']. '"' .(($row['parent_id'] ?? '') == $parent3['page_id'] ? ' selected' : ''). '> &nbsp;&nbsp;&nbsp;&nbsp; &rsaquo; ' .$parent3['name']. '</option>';
												foreach($parent3['sub_pages'] as $parent4){
													if($parent4['page_id'] != ITEM_ID){
														echo '<option value="' .$parent4['page_id']. '"' .(($row['parent_id'] ?? '') == $parent4['page_id'] ? ' selected' : ''). '> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &rsaquo; ' .$parent4['name']. '</option>';
													}
												}
											}
										}
									}
								}
							}
						}

					echo '</select>
				</div>
				
				<div class="form-field">
					<label>Page Type' .$CMSBuilder->tooltip('Page Type', '<b>Content</b> - Create and display page panels, headers, and footers.<br/><br/><b>Redirect</b> - Link users to another page<br/><br/><b>Landing</b> - Create and display page panels.  Hide the navigation bar and insert a form into the page banner.  This page does not appear in the sitemap or page navigation.'). '</label>
					<select name="type" class="select animation-control">
						<option data-toggle=".page-content,.page-contentonly" value="0"'.($page_type == 0 ? ' selected' : ''). '>Content</option>
						<option data-toggle=".page-url" value="1"' .($page_type == 1 ? ' selected' : ''). '>Redirect</option>';
						if($row['deletable'] ?? true){
							echo '<option data-toggle=".page-content,.page-landing" value="2"' .($page_type == 2 ? ' selected' : ''). '>Landing</option>';
						}
					echo '</select>
				</div>
				
				<div class="page-landing" data-anim-inverse' .($page_type == 2 ? ' style="display:none;"' : ''). '>
					<div class="form-field parent-page"' .(!empty($row['parent_id']) ? ' style="display:none;"' : ''). '>
						<label>Show in Navigation' .$CMSBuilder->tooltip('Show in Footer Only', 'Top-level pages can be displayed in the footer navigation. The status of the page will override this setting if it&rsquo;s set to Hidden or Disabled.'). '</label>
						<select name="navigation_menu" class="select">
							<option value="0">All</option>
							<option value="1"' .(($row['navigation_menu'] ?? '') == '1' ? ' selected' : ''). '>Header Only</option>
							<option value="2"' .(($row['navigation_menu'] ?? '') == '2' ? ' selected' : ''). '>Footer Only</option>
						</select>
					</div>
				</div>
				
				<div class="form-field">
					<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
					<select name="ordering" class="select">
						<option value="101">Default</option>';
						for($i=1; $i<101; $i++){
							echo '<option value="' .$i. '"' .(($row['ordering'] ?? '') == $i ? ' selected' : ''). '>' .$i. '</option>';	
						}
					echo '</select>
				</div>
			</div>';
		
		// Note: Footer Map toggle disabled by default
		$extras = $CMSBuilder->get_section_status($_cmssections['reviews']) == 'Enabled' || $CMSBuilder->get_section_status($_cmssections['leadins']) == 'Enabled';
		if ($extras) {
			echo '<div class="page-content"'.($page_type == 1 ? ' style="display:none;"' : ''). '>
				<hr />
				<div class="flex-container">

					<div class="form-field hidden">
						<label>Show/Hide Google Map</label>
						<select name="google_map" class="select">
							<option value="-1">Use Global Website Settings</option>
							<option value="1"' .(($row['google_map'] ?? -1) == 1 ? ' selected' : ''). '>Show</option>
							<option value="0"' .(($row['google_map'] ?? -1) == 0 ? ' selected' : ''). '>Hide</option>
						</select>
					</div>';

					if($CMSBuilder->get_section_status($_cmssections['reviews']) == 'Enabled'){
						echo '<div class="form-field">
							<label>Show/Hide Rating Badge</label>
							<select name="rating_badge" class="select">
								<option value="-1">Use Global Website Settings</option>
								<option value="1"' .(($row['rating_badge'] ?? -1) == 1 ? ' selected' : ''). '>Show</option>
								<option value="0"' .(($row['rating_badge'] ?? -1) == 0 ? ' selected' : ''). '>Hide</option>
							</select>
						</div>';
					}

					if($CMSBuilder->get_section_status($_cmssections['leadins']) == 'Enabled'){
						echo '<div class="form-field">
							<label>Attention Box</label>
							<select name="leadin_id" class="select">
								<option value="">None</option>';
								foreach($leadin_arr as $leadin){
									echo '<option value="' .$leadin['leadin_id']. '"' .(($row['leadin_id'] ?? '') == $leadin['leadin_id'] ? ' selected' : ''). '>' .$leadin['title'].($leadin['showhide'] > 0 ? ' (Hidden)' : ''). '</option>';
								}
							echo '</select>
						</div>';
					}

				echo '</div>
			</div>';
		}
			
		echo '</div>
	</div>'; //Page settings

	//External url
	echo '<div class="panel page-url"' .($page_type == 1 ? '' : ' style="display:none;"'). '>
		<div class="panel-header">Page Redirect
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">
			
				<div class="form-field">
					<label>Full Page/Site URL <span class="required">*</span>' .$CMSBuilder->tooltip('Sitemap Reference', 'Click on the icon to view the sitemap. You can use the sitemap pop up as your reference when entering links.', '<span class="fas fa-sitemap"></span>', array('sitemap-reference')). '</label>
					<input type="text" name="url" value="' .($row['url'] ?? ''). '" class="input' .(in_array('url', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Open Link in</label>
					<select name="urltarget" class="select">
						<option value="0"' .(!($row['urltarget'] ?? 0) ? ' selected' : ''). '>Same Window</option>
						<option value="1"' .(($row['urltarget'] ?? 0) ? ' selected' : ''). '>New Window</option>
					</select>
				</div>
				
			</div>
		</div>
	</div>';

	//Header
	echo '<div class="panel page-content"' .($page_type == 1 ? ' style="display:none;"' : ''). '>
		<div class="panel-header">Page Header
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		
		<div class="panel-content">
			<div class="flex-container">
			
				<div class="form-field">
					<label>Main Header <span class="required">*</span>' .$CMSBuilder->tooltip('Main Header', 'Put special emphasis on a word or phrase by enclosing it in {curly brackets}.'). '</label>
					<input type="text" name="page_title" value="' .($row['page_title'] ?? ''). '" class="input' .(in_array('page_title', $required) ? ' required' : ''). '" />
				</div>
			
				<div class="form-field">
					<label>Button Link <small>(URL, Phone, or Email)</small>' .
						$CMSBuilder->tooltip('Button Link', 'If a link is provided, a clickable button will be displayed.<br /><br /><strong>URL</strong> - User will be taken to this link.<br /><strong>Phone</strong> - User will be prompted to call the phone number provided.<br />Example: (*************.<br /><strong>Email</strong> - User will be prompted to emaill the address provided.').
						$CMSBuilder->tooltip('Sitemap Reference', 'Click on the icon to view the sitemap. You can use the sitemap pop up as your reference when entering links.', '<span class="fas fa-sitemap"></span>', array('sitemap-reference')). '</label>
					<input type="text" name="button_url" value="' .($row['button_url'] ?? ''). '" class="input" />
				</div>

				<div class="form-field">
					<label>Button Text' .$CMSBuilder->tooltip('Button Text', 'Button will be displayed with this text. Defaults to &quot;Learn More&quot;.'). '</label>
					<input type="text" name="button_text" value="' .($row['button_text'] ?? ''). '" class="input" />
				</div>

				<div class="form-field">
					<label>Open Button Link in</label>
					<select name="button_target" class="select">
						<option value="0"' .(!($row['button_target'] ?? 0) ? ' selected' : ''). '>Same Window</option>
						<option value="1"' .(($row['button_target'] ?? 0) ? ' selected' : ''). '>New Window</option>
					</select>
				</div>				
			</div>
			
			<div class="page-landing" data-anim="none"' .($page_type != 2 ? ' style="display:none;"' : ''). '>
				<label>Short Description</label>
				<textarea name="landing_description" class="textarea tinymceMini">' .($row['description'] ?? ''). '</textarea>
			</div>
			
			<div class="form-field page-contentonly" data-anim="none"' .($page_type == 2 ? ' style="display:none;"' : ''). '>
				<label>Short Description</label>
				<textarea name="description" class="textarea input_lg">' .strip_data($row['description'] ?? ''). '</textarea>
			</div>
			
		</div>
	</div>'; //Header
	
	//Landing Page Form
	echo '<div class="panel page-landing"' .($page_type == 2 ? '' : ' style="display:none;"'). '>
		<div class="panel-header">Page Form Settings
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content">
			<div class="flex-container">
		
				<div class="form-field">
					<label>Form Title</label>
					<input type="text" name="form_title" value="' .($row['form_title'] ?? ''). '" class="input' .(in_array('form_title', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Submit Button Text' .$CMSBuilder->tooltip('Button Text', 'Button will be displayed with this text. Defaults to &quot;Submit&quot;.'). '</label>
					<input type="text" name="form_button_text" value="' .($row['form_button_text'] ?? ''). '" class="input' .(in_array('form_button_text', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Send To' .$CMSBuilder->tooltip('Send To', 'This is the address all form submissions will send to. If left blank, submissions will send to the head office email address.'). '</label>
					<input type="text" name="form_recipient" value="' .($row['form_recipient'] ?? ''). '" class="input' .(in_array('form_recipient', $required) ? ' required' : ''). '" />
				</div>
				
			</div>

			<div class="form-field auto-width">
				<label>Form Description</label>
				<textarea name="form_description" class="textarea input_lg' .(in_array('form_description', $required) ? ' required' : ''). '">' .($row['form_description'] ?? ''). '</textarea>
			</div>
			
		</div>
	</div>';
	
	//Landing Page Form Fields
	echo '<div class="panel page-landing"' .($page_type == 2 ? '' : ' style="display:none;"'). '>
		<div class="panel-header">Page Form Fields
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="sortable">
				<thead>
					<th width="1px"></th>
					<th width="1px">Field Label <span class="required">*</span></th>
					<th>Menu Options <span class="required">*</span>' .$CMSBuilder->tooltip('Add Options', 'Enter options for the select menu, separated with a pipe &quot;|&quot;. Click and drag options to rearrange them.'). '</th>
					<th width="1px" class="center">Required</th>
					<th width="1px" class="center">Visible</th>
				</thead>
				<tbody>';
				foreach($page_form as $name => $field){
					echo '<tr>
						<td class="handle"><i class="fas fa-arrows-alt"></i></td>
						<td><input type="text" class="input nomargin' .(in_array($name.'-label', $required) ? ' required' : ''). '" name="page_form[' .$name. '][label]" value="' .$field['label']. '" placeholder="Enter '.(ucwords(str_replace(['-', '_'], ' ', $field['name']))). ' Label"' .(array_key_exists($name, $field_labels) ? ' disabled' : ''). ' /></td>
						<td>';
	
						if($field['type'] == 'select'){
							echo '<textarea name="page_form[' .$name. '][options]" class="input tagEditor nomargin' .(in_array($name.'-options', $required) ? ' required' : ''). '" style="height: 30px;">' .$field['options']. '</textarea>';
						}else{
							echo '<input type="hidden" name="page_form[' .$name. '][options]" value="' .$field['options']. '">
							<small>N/A</small>';
						}

						echo '</td>
						<td class="center">
							<div class="onoffswitch">
								<input type="checkbox" name="page_form[' .$name. '][required]" id="form-required-' .$name. '" value="1"' .($field['required'] == 1 ? ' checked' : ''). '>
								<label for="form-required-' .$name. '">
									<span class="inner"></span>
									<span class="switch"></span>
								</label>
							</div>
						</td>
						<td class="center">
							<div class="onoffswitch">
								<input type="checkbox" name="page_form[' .$name. '][showhide]" id="form-showhide-' .$name. '" value="0"' .($field['showhide'] == 0 ? ' checked' : ''). '>
								<label for="form-showhide-' .$name. '">
									<span class="inner"></span>
									<span class="switch"></span>
								</label>
							</div>
						</td>
					</tr>';
				}
				echo '</tbody>
			</table>
		</div>
	</div>';

	//Page banner
	echo '<div class="panel page-content"' .($page_type == 1 ? ' style="display:none;"' : ''). '>
		<div class="panel-header">Page Banner
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">'.
				$CMSBuilder->img_holder($image, $imagedir.'1920/', $imagedir.'1024/').

				'<div class="form-field">
					<label class="page-landing"' .($page_type == 2 ? '' : ' style="display:none;"'). '>
						Upload Image' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploader::size_label('landing', 'image'). ' and file size must be smaller than ' .$_max_filesize['megabytes']. '.'). '
					</label>
					<label class="page-contentonly"' .($page_type == 0 ? '' : ' style="display:none;"'). '>
						Upload Image' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploader::size_label('banner', 'image'). ' and file size must be smaller than ' .$_max_filesize['megabytes']. '.'). '
					</label>
					<input type="file" class="input' .(in_array('image', $required) ? ' required' : ''). '" name="image" value="" />
				</div>

				<div class="form-field">
					<label>Alt Text <small>(SEO)</small>' .$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.'). '</label>
					<input type="text" name="image_alt" value="' .($row['image_alt'] ?? ''). '" class="input" />
				</div>

				<div class="form-field">
					<label>Colour Overlay' .$CMSBuilder->tooltip('Colour Overlay', 'This will be used as the overlay colour for the banner image.'). '</label>
					<select name="theme" class="select">
						<option value="">Default</option>';
					
					foreach($_themes as $theme_key => $theme_name){
						echo '<option value="' .$theme_key. '"' .(($row['theme'] ?? false) == $theme_key ? ' selected' : ''). '>' .$theme_name. '</option>';
					}

					echo '</select>
				</div>
			
			</div>
		</div>
	</div>'; //Page banner

	//Page content
	echo '<div class="panel page-content"' .($page_type == 1 ? ' style="display:none;"' : ''). '>
		<div class="panel-header">Page Panels
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="sortable">
			
				<thead>
					<th width="1px" class="nopadding-r"></th>
					<th width="30px" class="nopadding-r"></th>
					<th width="40px" class="nopadding-r"></th>
					<th width="auto">Panel Title</th>
					<th width="auto">Panel Type</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" class="right nopadding"></th>
				</thead>
				
				<tbody>';
				if(!empty($row['page_panels'])){
					foreach($row['page_panels'] as $panel){

						$full_img_link = (check_file($panel['image'], $paneldir) ? $paneldir.$panel['image'] : '');
						$thumb_img_link = $full_img_link;
						if(check_file($panel['image'], $paneldir.'480/')){
							$thumb_img_link = $paneldir.'480/'.$panel['image'];
							$full_img_link  = $paneldir.'1920/'.$panel['image'];

						}else if(check_file($panel['image'], $paneldir.'side/')){
							$thumb_img_link = $paneldir.'side/'.$panel['image'];
							$full_img_link  = $thumb_img_link;
						}
						
						//Set panel labels
						$panel_labels = [
							'promo'  => 'Promo Boxes',
							'cta'    => 'Call to Action',
							'faqs'   => 'FAQs',
							'side'   => 'Side by Side'
						];
						
						$panel_label = $panel_labels[$panel['panel_type']] ?? ucwords($panel['panel_type']);
						
						echo '<tr data-table="pages_panels" data-column-name="panel_id" data-name="' .$panel['title']. '" data-id="' .$panel['panel_id']. '">
							<td class="handle nopadding-r"><span class="fas fa-arrows-alt"></span></td>
							<td class="nopadding-r">' .(!($panel['deletable'] ?? true) ? $CMSBuilder->tooltip('Dynamic Panel', 'This panel contains dynamically created content.', '<i class="fas fa-info-circle color-theme3"></i>', ['nopadding']) : ''). '</td>
							<td class="nopadding-r">'.render_gravatar($thumb_img_link, $full_img_link, $panel['title']).'</td>
							<td>' .$panel['title']. '</td>
							<td>' .$panel_label. '</td>
							<td>'.$CMSBuilder->showhide_toggle('pages_panels', 'panel_id', $panel['panel_id'], $panel['showhide']). '</td>
							<td class="right"><a href="' .$panelpage['page_url']. '?page_id=' .ITEM_ID. '&action=edit&item_id=' .$panel['panel_id']. '" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
						</tr>';
					}
				}else{
					echo '<tr><td colspan="6" height="40px">No panels have been created yet.</td></tr>';	
				}
				echo '</tbody>
			</table>
			
			<div class="pager">
				<div class="right">';
				if(ITEM_ID == ""){
					echo '<button type="submit" class="button" name="save" value="newpanel"><i class="fas fa-plus"></i>Add New</button>'; //Save new page first
				}else{
					echo '<a href="' .$panelpage['page_url']. '?page_id=' .ITEM_ID. '&action=add" class="button"><i class="fas fa-plus"></i>Add New</a>';
				}
				echo '</div>
			</div>
			
		</div>
	</div>'; //Page panels

	include 'includes/widgets/seotabs.php';

	//Sticky footer
	echo '<footer id="cms-footer">
		<div class="flex-container">
			<div class="flex-column right">
				<button type="submit" class="button" name="save" value="save"><i class="fas fa-check"></i>Save Changes</button>
			</div>
			<div class="flex-column left">';
				if(ITEM_ID != ""){
					echo (!$row['deletable'] ? $CMSBuilder->tooltip('Delete Page', 'Due to the dynamic nature of this page, it cannot be deleted. If you wish to remove this page, hide or disable it instead.').'&nbsp;' : '');
					echo '<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn"' .(!$row['deletable'] ? ' disabled' : ''). '><i class="fas fa-trash-alt"></i>Delete</button>';
				}
				echo '<a href="' .PAGE_URL. '" class="cancel">Cancel</a>
			</div>
		</div>
	</footer>';

	//Fields with tags
	echo '<input type="hidden" name="keep_tags[]" value="TINYMCE_Editor" />
	<input type="hidden" name="keep_tags[]" value="panel_content" />
	<input type="hidden" name="keep_tags[]" value="landing_description" />';
	
	echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

}

?>