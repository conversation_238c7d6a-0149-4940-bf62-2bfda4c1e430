<div class="login-container">
<?php if(isset($displayform) && $displayform){ ?>
	<form name="reset-form" id="reset-form" method="post" action="">
		<div class="form-field">
			<label>New Password</label>
			<input class="input <?php echo (isset($required) && in_array('new_password', $required) ? ' error' : ''); ?>" type="password" name="new_password" value="" autocomplete="off" />
		</div>
		<div class="form-field">
			<label>Confirm Password</label>
			<input class="input <?php echo (isset($required) && in_array('confirm_password', $required) ? ' error' : ''); ?>" type="password" name="confirm_password" value="" autocomplete="off" />
		</div>
		<!-- <button type="submit" name="reset" class="button" value="Reset">Reset</button> -->

		<div class="form-field button-wrapper reset">
			<button type="submit" name="reset" class="button primary red" value="Reset">
				Reset
				<span class="top-border"></span>
				<span class="bottom-border"></span> <span class="left-border"></span>
				<span class="right-border"></span>
			</button>
		</div>

		<input type="hidden" name="xid" value="<?php echo $_COOKIE['xid']; ?>" />		
	</form>
<?php } ?>

<div class="hidden">
	<div id="forgot-password-modal" class="hidden-modal" title="Forgot Password" data-width="500">
		<?php include("pages/account/forgot.php"); ?>
	</div>
</div>
</div>
