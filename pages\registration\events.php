<?php

$html = '';
$sidebar = '';

//Event listings
if(!isset($event)){
	
	if(!empty($events)){
		$html .= '<div class="event-boxes">';
	
		foreach($events as $event){			
			$event['sponsors'] = $Registration->get_occurrence_sponsors($event['occurrence_id']);
			$html .= '<div class="event-box">
				<div class="date">
					<span class="day">' .date('d', strtotime($event['start_date'])). '</span>
					<small class="month">' .date('F', strtotime($event['start_date'])). '</small>
				</div>
				<div class="content">
					<small>' .$event['category_name']. '</small>
					<h6><a href="' .$page['page_url'].$event['page'].'-'.$event['occurrence_id']. '/">' .$event['event_name']. '</a></h6>'.
					($event['location_name'] != '' ? '<small>Location: ' .$event['location_name']. '</small>' : '').
					(!empty($event['sponsors']) ? '<small>Sponsored By: '.$event['sponsors'][0]['name'].'</small>' : '').'
				</div>
			</div>';
		}
	
		$html .= '</div>';
		
	}else{
		$html .= '<table width="100%" cellspacing="0" cellpadding="10" border="0">
			<tr><td class="nobg">No events found' .(isset($_GET['search']) && $_GET['search'] != '' ? ' matching `' .$_GET['search']. '`' : ''). '.</td></tr>
		</table>';
	}
	
	//Set panel content
	$page['page_panels'][$panel_id]['content'] .= $html;
	
	
//Selected event	
}else{
	
	//Sent back from register page
	if(isset($_SESSION['reg_error'])){
		$html .= $Account->alert($_SESSION['reg_error'], false);
		unset($_SESSION['reg_error']);
	}
	
	if(USER_LOGGED_IN){
		
		//Check for completed form submissions
		$forms_required = '';
		if(!empty($event['required_forms'])){
			foreach($event['required_forms'] as $form){
				if(empty($form['completed'])){
					$forms_required .= '<a href="' .$sitemap[$form['page_id']]['page_url']. '">' .$form['form_name']. '</a><br />';
				}
			}
		}
		if(!empty($forms_required)){
			$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> You must complete the following form(s) before you can register for this event:');
			$html .= '<p>'.$forms_required.'</p><br />';
			
			
		}else{
		
			//Restrict hio accounts
			if(!HIO_ACCESS){
			
				//Register
				if($event['reg_available']){
					$html .= '<form name="register-form" action="' .$_sitepages['registration']['page_url']. '" method="post">
						<p><button type="submit" class="button solid primary red">Register Now<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button></p>
						<input type="hidden" name="event_id" value="' .$event['event_id']. '" />
						<input type="hidden" name="occurrence_id" value="' .$event['occurrence_id']. '" />
						<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
					</form>';

				//Waitlist
				}else if($event['reg_waitlist']){

					//Get user waitlists
					$account_waitlists = $Registration->get_user_wait_lists();
					if(array_key_exists($event['occurrence_id'], $account_waitlists)){
						$html .= $Account->important('This event is currently at capacity and you are subscribed to the waiting list.');	
					}else{
						$html .= $Account->important('This event is currently at capacity.');	
						$html .= '<p><button type="button" class="button solid waitlist" onclick="waitList(this);" data-id="' .$event['occurrence_id']. '" data-name="' .$event['event_name']. '"><i class="fa"></i>Waiting List</button></p>';
					}

				//Closed
				}else{
					if(strtotime($event['reg_open'].' '.$reg_settings['reg_open_time']) > strtotime('now')){
						$html .= $Account->important('Registration opens on ' .date("F j, Y @g:iA", strtotime($event['reg_open'].' '.$reg_settings['reg_open_time'])). '.');
					}else if($event['occurrence_status'] != 'Open' || $event['started'] || (strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time']) <= strtotime('now'))){
						$html .= $Account->important('Registration is now closed.');
					}else if(!$event['eligible'] || !$event['isgender']){
						$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> You do not meet the eligibility requirements to register for this event.');
					}else if($event['full']){
						$html .= $Account->important('This event is currently at capacity.');
					}else{
						$html .= $Account->important('Registration is currently unavailable.');
					}
				}
				
			}else{
				$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> You do not meet the eligibility requirements to register for this event.');
			}
		}
	
	//Login prompt
	}else{
		$html .= $Account->important('<i class="fa fa-lock"></i> You must <a href="' .$_sitepages['login']['page_url']. '?redirect=' .base64_encode($_SERVER['REQUEST_URI']). '">login</a> to access event registration.');
	}	
		
	//Event details
	$html .= '<section id="event-information">';
	
		$html .= '<header><h4>Event Information</h4></header>
		<table cellpadding="0" cellspacing="0" border="0" width="100%" class="noborder nobgs event-table">
			<tr>
				<td width="250px"><strong>Event Date:</strong></td>
				<td>' .format_date_range($event['start_date'], $event['end_date']). '</td>
			</tr>';
			if(!empty($event['role_id'])){
				$html .= '<tr>
					<td><strong>Event Eligibility:</strong></td>
					<td>' .($event['role_id'] == 2 ? 'Members Only' : ($event['role_id'] == 7 ? 'Non-Members Only' : 'Non-member')). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td><strong>Attendance:</strong></td>
				<td>';
					$field_term = 'attendee';
					if($event['attendee_sharing'] == '1' || ($event['attendee_sharing'] == '-1' && $reg_settings['attendee_sharing'] == '1')){
						$html .= '<a onclick="dialogModal(\'#attendee-list\');">' .$event['attendance']. ' '.$field_term.($event['attendance'] == 1 ? '' : 's'). '</a>';

						$attendee_list = $Registration->get_current_attendance($event['occurrence_id']);
						$html .= '<div class="hidden">
							<div id="attendee-list" class="hidden-modal" title="Current Attendance" data-modal-width="500" data-modal-class="dialog-scroll">';
							$noattendees = true;	
							if(!empty($attendee_list)){
								foreach($attendee_list as $attendee){
									if($attendee['attendee_sharing']){
										$html .= '<strong>' .$attendee['last_name']. ', ' .$attendee['first_name']. '</strong>';
										$html .= (!empty($attendee['facility_name']) ? ' - '.$attendee['facility_name'] : '');
										$html .= '<hr class="nomargin" />';
										$noattendees = false;
									}
								}
							}
							if($noattendees){
								$html .= 'No registered '.$field_term.'s found.';
							}
							$html .= '</div>
						</div>';

					}else{
						$html .= $event['attendance']. ' '.$field_term.($event['attendance'] == 1 ? '' : 's');
					}
				$html .= '</td>
			</tr>
			<tr>
				<td><strong>Capacity:</strong></td>
				<td>' .(!is_null($event['max_capacity']) ? $event['max_capacity'] : 'Unlimited'). ' '.$field_term.'s</td>
			</tr>';
			if($event['waiting_list'] != '0'){
				$waiting_list = $Registration->get_current_wait_list($event['occurrence_id']);
				$html .= '<tr>
					<td><strong>Waiting List:</strong></td>
					<td>';
						if($event['attendee_sharing'] == '1' || ($event['attendee_sharing'] == '-1' && $reg_settings['attendee_sharing'] == '1')){
							$html .= '<a onclick="dialogModal(\'#wait-list\');">' .count($waiting_list). ' '.$field_term .(count($waiting_list) == 1 ? '' : 's'). '</a>';
							$html .= '<div class="hidden">
								<div id="wait-list" class="hidden-modal" title="Current Waiting List" data-modal-width="500" data-modal-class="dialog-scroll">';
								if(!empty($waiting_list)){
									foreach($waiting_list as $wait){
										$html .= '<strong>' .$wait['last_name']. ', ' .$wait['first_name']. '</strong>';
										$html .= (!empty($wait['facility_name']) ? ' - '.$wait['facility_name'] : ''). '<hr class="nomargin" />';
									}
								}else{
									$html .= 'Waiting list is empty.';
								}
								$html .= '</div>
							</div>';

						}else{
							$html .= count($waiting_list). ' '.$field_term .(count($waiting_list) == 1 ? '' : 's');
						}
					$html .= '</td>
				</tr>';
			}
			if(!empty($location)){
				$html .= '<tr>
					<td valign="top"><strong>Location:</strong></td>
					<td valign="top">' .$location['name'];
					if($location['full_address'] != ''){
						$html .= '<br /><a href="https://maps.google.com/maps?q='.urlencode($location['name'].' '.$location['full_address']).'">';
							$html .= (trim($location['address2']) != '' ? $location['address2'].' - ' : '').$location['address'].'<br />';
							$html .= (trim($location['city']) != '' ? $location['city'].', ' : '');
							$html .= (trim($location['province']) != '' ? $location['province'].' ' : '');
							$html .= (trim($location['postal_code']) != '' ? $location['postal_code'] : '');
						$html .= '</a>';
					}
					$html . '</td>
				</tr>';
			}
		$html .= '</table>';
	
		//Pricing
		$html .= '<h4>Event Pricing</h4>
		<table cellpadding="0" cellspacing="0" border="0" width="100%" class="noborder nobgs event-table">';
		foreach($event['pricing'] as $ptype){
			$html .= '<tr>
				<td valign="top" width="250px"><strong>' .$ptype['price_type']. ':</strong></td>
				<td valign="top">$' .number_format($ptype['price'], 2). '</td>
			</tr>';
		}
		$html .= '</table>';
	
		//Deadlines
		$html .= '<h4>Event Deadlines</h4>
		<table cellpadding="0" cellspacing="0" border="0" width="100%" class="noborder nobgs event-table">
			<tr>
				<td valign="top" width="250px"><strong>Payment Deadline:</strong></td>
				<td valign="top">' .date("F j, Y @g:iA", strtotime($event['payment_deadline'].' '.$reg_settings['reg_close_time'])). '</td>
			</tr>
			<tr>
				<td valign="top"><strong>Payment Deadline:</strong></td>
				<td valign="top">' .date("F j, Y @g:iA", strtotime($event['payment_deadline'].' '.$reg_settings['reg_close_time'])). ' &nbsp; <small>Deadline to withdraw before penalty</small></td>
			</tr>
			<tr>
				<td valign="top"><strong>Registration Deadline:</strong></td>
				<td valign="top">' .date("F j, Y @g:iA", strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time'])). '</td>
			</tr>
		</table>';
	
	
	$html .= '</section>';
	
	// $html .= '<hr />';
	
	//Event content
	$html .= $event['description'];
	
	//Sponsors
	if(!empty($event['sponsors'])){
		krsort($event['sponsors']);
		
		foreach($event['sponsors'] as $type=>$type_sponsors){
			$sidebar .= '<h3 class="center">Thank You to Our<br />'.$type.' Sponsors</h3>';
			$sidebar .= '<ul class="sponsors">';
			foreach($type_sponsors as $sponsor){
				$sponsor['website'] = str_replace('http://', '', str_replace('https://', '', $sponsor['website']));
				$sidebar .= '<li class="sponsor">
					<a' .(trim($sponsor['website']) != '' ? ' href="http://' .$sponsor['website']. '"' : ''). ' class="img-holder">';
						if($sponsor['image'] != '' && file_exists('images/logos/'.$sponsor['image'])){
							$sidebar .= '<div><img src="' .$path.'images/logos/' .$sponsor['image']. '" alt="' .(trim($sponsor['image_alt']) != '' ? $sponsor['image_alt'] : $sponsor['name']). '" /></div>';
						}
					$sidebar .= '</a>
					<h4>' .$sponsor['name']. '</h4>';
					if(trim($sponsor['website']) != ''){
						$sidebar .= '<p><a href="http://' .$sponsor['website']. '">' .$sponsor['website']. '</a></p>';
					} 
				$sidebar .= '</li>';
			}
			$sidebar .= '</ul>';
		}
	}

	//Set panel content
	$page['page_panels'][$panel_id]['title'] = $event['event_name'];
	$page['page_panels'][$panel_id]['show_title'] = true;
	$page['page_panels'][$panel_id]['show_title_first'] = true;
	$page['page_panels'][$panel_id]['content'] = $html;
	// $page['page_panels'][$panel_id]['sidebar'] = $sidebar;
	$page['page_panels'][$panel_id]['sidebar_border'] = false;


	
}

//Upcoming events
$page['page_panels']['events'] = array(
	'panel_id' => 'events',
	'panel_type' => 'events',
	'title' => 'More Upcoming Events',
	'show_title' => 1,
	'content' => ''
);

//Page panels
include("includes/pagepanels.php");

?>