<?php

//System files
include("config/config.php");
include("config/database.php");
include("includes/functions.php");
include("includes/utils.php");

//Include modules
include("modules/account/Account.php");
include("modules/Gallery.php");
include("modules/Partners.php");
include("modules/Staff.php");
include("modules/Blog.php");
// if(USER_LOGGED_IN){
// }

include("modules/Directory.php");
include("modules/Facilities.php");
include("modules/JobPosting.php");
include("modules/Careers.php");
include("modules/Application.php");
include("modules/Motm.php");
include("modules/Resources.php");
include("modules/Classifieds.php");
include("modules/Awards.php");
include("modules/ScholarshipBursary.php");

//Registration system
include("modules/registration/Events.php");
include("modules/registration/Tournaments.php");
include("modules/registration/Register.php");
include("modules/registration/Checkout.php");
include("modules/registration/Confirm.php");

//Search module
include("modules/Search.php");

include("modules/Pages.php"); //should always load last

//Send headers
if($page['type'] == 1 && !$error404){
	header("HTTP/1.1 301 Moved Permanently");
	header("Location: " .$page['url']);
	exit();
}
if(!empty($page['redirect_to_slug'])){
	header("HTTP/1.1 301 Moved Permanently");
	header("Location: ".$page['page_url'].(!empty($_SERVER['QUERY_STRING']) ? '?'.$_SERVER['QUERY_STRING'] : ''));
	exit();
}
if($error404){
	header("HTTP/1.0 404 Not Found");
}

//Header
include("includes/header.php");

//404 error
if($error404){
	include("includes/pagepanels.php");

//Pages
}else{
	switch((!is_null(PAGE_ID) ? PAGE_ID : PARENT_ID)){

		//Contact
		case $_sitepages['contact']['page_id']:
			include("pages/contact.php");
		break;

		// Partners
		case $_sitepages['partners']['page_id']:
			include("pages/partners.php");
		break;

		// Staff
		case $_sitepages['staff']['page_id']:
			include("pages/staff.php");
		break;

		//Blog
		case $_sitepages['blog']['page_id']:
			include("pages/blog.php");
		break;

		//Account
		case $_sitepages['register']['page_id']:
		case $_sitepages['login']['page_id']:
		case $_sitepages['account']['page_id']:
		case $_sitepages['reset']['page_id']:
		case $_sitepages['profile']['page_id']:
		case $_sitepages['account-settings']['page_id']:
		case $_sitepages['change-password']['page_id']:
		case $_sitepages['edit-facility']['page_id']:
        case $_sitepages['my-job-postings']['page_id']:
		case $_sitepages['my_classifields']['page_id']:
		case $_sitepages['billing-profiles']['page_id']:
		case $_sitepages['invoices']['page_id']:
		case $_sitepages['payments']['page_id']:
		case $_sitepages['hole-in-one']['page_id']:	
		case $_sitepages['my-registrations']['page_id']:
		case $_sitepages['withdrawal']['page_id']:
		case $_sitepages['message-centre']['page_id']:
			include("pages/account/account.php");
		break;

		case $_sitepages['directory']['page_id']:
			include("pages/directory.php");
		break;

		case $_sitepages['facilities']['page_id']:
			include("pages/facilities.php");
		break;

        case $_sitepages['post-job']['page_id']:
			include("pages/jobposting.php");
		break;

		case $_sitepages['job-postings']['page_id']:
			include("pages/careers.php");
		break;
		case $_sitepages['application']['page_id']:
			include("pages/application.php");
		break;

		case $_sitepages['motm']['page_id']:
			include("pages/motm.php");
		break;

		case $_sitepages['member-resources']['page_id']:
			include("pages/resources.php");
		break;

		case $_sitepages['classifields']['page_id']:
			include("pages/classifieds.php");
		break;

		case $_sitepages['award-winners']['page_id']:
			include("pages/awards.php");
		break;

		//pdpoints
		case $_sitepages['top-100-program']['page_id']:
			include("pages/pdpoints.php");
		break;
		
		//events
		case $_sitepages['events']['page_id']:
			include("pages/registration/events.php");
		break;
			
		//tournaments
		case $_sitepages['tournaments']['page_id']:
			include("pages/registration/tournaments.php");
		break;

		case $_sitepages['past-winners']['page_id']:
			include("pages/scholarship-bursary.php");
		break;
			
		//online registration
		case $_sitepages['registration']['page_id']:
		case $_sitepages['reg_cart']['page_id']:
		case $_sitepages['reg_checkout']['page_id']:
		case $_sitepages['reg_confirm']['page_id']:
		case $_sitepages['reg_success']['page_id']:
		case $_sitepages['reg_error']['page_id']:
			include("pages/registration/online-registration.php");
		break;

		case $_sitepages['search']['page_id']:
			include("pages/search.php");
		break;

		//Standard
		default:
			include("includes/pagepanels.php");
		break;
	}
}

//Footer
include("includes/footer.php");

?>