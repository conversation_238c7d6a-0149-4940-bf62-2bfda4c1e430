<?php  

//System files
include("../../includes/config.php");
include ('../../config/database.php');
include('../../includes/functions.php');
include('../../includes/utils.php');

$errors = array();
$response = array();
$response['errors'] = false;
$response['msg_validation'] = '';
$response['html'] = '';

if($_POST['xid'] == $_COOKIE['xid']){
	
	//Login
	$login_id = (isset($_POST['login_id']) ? $_POST['login_id'] : '');
	$user_login = (isset($_POST['user_login']) ? $_POST['user_login'] : '');
	$user_password = (isset($_POST['user_password']) ? $_POST['user_password'] : '');
	$user_reme = (isset($_POST['user_reme']) ? $_POST['user_reme'] : 0);
	$credentials = array(
		'unique_id' => array('param' => 'email', 'value' => $user_login),
		'password' => array('param' => 'password', 'value' => $user_password)
	);

	//Make sure user logging in is the same user kicked out
	$sameuser = false;
	$db->query("SELECT `accounts`.`email`, `account_session_log`.`account_id` FROM `account_session_log` LEFT JOIN `accounts` ON `accounts`.`account_id` = `account_session_log`.`account_id` WHERE `account_session_log`.`login_id` = ? AND `accounts`.`email` = ?", array($login_id, $user_login));
	if(!$db->error() && $db->num_rows()){
		$sameuser = true;
	}else{
		$errors[] = 'Please login with the same account you used earlier.';
	}

	//Same user is already logged in
	if(USER_LOGGED_IN && $sameuser){
		//No errors so close login modal

	//Not logged in or different user
	}else{

		//Logout
		try{
			$Account->logout();
		}catch(Exception $e){
		}

		//Login if same user
		if($sameuser){
			try{
				$Account->login($credentials, $user_reme);
				$Account = new Account();
			}catch(Exception $e){
				$errors[] = $e->getMessage();
			}
		}
	}	

//Cookie error
}else{
	$errors[] = 'Please make sure cookies are enabled in your browser then try again.';
}

if($errors){
	$response['errors'] = 1;
	$response['msg_validation'] = implode('<br/>', $errors);
}

print json_encode($response);

?>