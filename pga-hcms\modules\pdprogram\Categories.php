<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('pd_categories');
	$CMSBuilder->set_widget($_cmssections['top-100-categories'], 'Total PD Categories', $total_records);
}

if(SECTION_ID == $_cmssections['top-100-categories']){
	
	//Define vars
	$record_db = 'pd_categories';
	$record_id = 'category_id';
	$record_name = 'Category';

	$errors = false;
	$required = array();
	$required_fields = array('name' => 'Title'); // for validation

	//Get Records
	$records_arr = array();
	$params = array();

	$query = $db->query("SELECT * FROM `$record_db` WHERE `status` = ? ORDER BY `ordering`", array('Active'));
	if($query && !$db->error()) {
		$result = $db->fetch_array();
		foreach($result as $row) {
			$records_arr[$row[$record_id]] = $row;
			$records_arr[$row[$record_id]]['sub_items'] = array();
		}

		$lvl = 1; //tracking depth of array
		$records_arr = build_hierarchy($records_arr, $record_id);
		foreach($records_arr as $record_key => &$record){
			if($record['parent_id'] && array_key_exists($record['parent_id'], $records_arr)){
				$records_arr[$record['parent_id']]['sub_items'][$record_key] = &$record;
				$records_arr[$record['parent_id']]['sub_items'][$record_key]['parent_name'] = $records_arr[$record['parent_id']]['name'];
			}
		}
		
		//Search
		if(ACTION == ''){
			$_GET['search'] = $CMSBuilder->system_search(SECTION_ID);
			if(isset($_GET['search']) && $_GET['search'] != '') {
				$search_result = array();
				foreach($records_arr as $key => $search_record) {
					if(stripos($search_record['name'],$_GET['search']) !== false){
						$search_result[$key] = $search_record;
					}
				}
				$records_arr = $search_result;
			}
		}
		
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}
	
	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		if($records_arr[ITEM_ID]['deletable']){
		
			//Multiple queries so utilize transactions
			$db->new_transaction();

			//Trash categories and sub categories
			$update1 = $db->query("UPDATE `$record_db` SET `status` = ? WHERE `$record_id` = ?", array('Trashed', ITEM_ID));
			if(isset($records_arr[ITEM_ID]['sub_items']) && !empty($records_arr[ITEM_ID]['sub_items'])) {
				$update2 = $db->query("UPDATE $record_db SET `status` = ? WHERE `$record_id` IN (".implode(",", array_keys($records_arr[ITEM_ID]['sub_items'])).")", array('Trashed'));
				foreach($records_arr[ITEM_ID]['sub_items'] as $subitem) {
					if(isset($subitem['sub_items']) && !empty($subitem['sub_items'])) {
						$update3 = $db->query("UPDATE `$record_db` SET `status` = ? WHERE `$record_id` IN (".implode(",", array_keys($subitem['sub_items'])).")", array('Trashed'));
					}
				}
			}

			if(!$db->error()) {
				$db->commit();
				$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			} else {
				$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(), false);	
			}
			
		}else{
			$CMSBuilder->set_system_alert('Dynamic categories cannot be deleted.', false);	
		}

		header("Location: " .PAGE_URL);
		exit();
		
	
	//Save item
	}else if(isset($_POST['save'])){

		//Validate
		$required_missing = false;
		if(!empty($required_fields)) {
			foreach($required_fields as $field_key => $field_name){
				if(isset($_POST[$field_key])){
					if(trim($_POST[$field_key]) == ''){
						$required_missing = true;
						array_push($required, $field_key);
					}
				}else{
					$required_missing = true;
					array_push($required, $field_key);
				}
			}
		}
		if($required_missing){
			$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
		}
		if($_POST['points'] == ""){
			$_POST['points'] = 0;
		}
		if($_POST['max_points'] != ""){
			if($_POST['max_points'] < $_POST['points']){
				$errors[] = 'Maximum points cannot be less than points.';
				$required[] = 'max_points';
			}
		}

		if(!$errors){
			
			//Insert to db
			$params = array(
				ITEM_ID, 
				(isset($_POST['parent_id']) ? $_POST['parent_id'] : NULL),
				$_POST['name'], 
				$_POST['points'], 
				$_POST['unit'], 
				(!empty($_POST['max_points']) ? $_POST['max_points'] : NULL),
				$_POST['ordering'], 
				date("Y-m-d H:i:s"),
				date("Y-m-d H:i:s"),
				(isset($_POST['parent_id']) ? $_POST['parent_id'] : NULL),
				$_POST['name'], 
				$_POST['points'],
				$_POST['unit'], 
				(!empty($_POST['max_points']) ? $_POST['max_points'] : NULL),
				$_POST['ordering'], 
				date("Y-m-d H:i:s")
			);
			$insert = $db->query("INSERT INTO `$record_db` (`$record_id`, `parent_id`, `name`, `points`, `unit`, `max_points`, `ordering`, `date_added`, `last_updated`) VALUES (?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `parent_id` = ?, `name` = ?, `points` = ?, `unit` = ?, `max_points` = ?, `ordering` = ?, `last_updated` = ?", $params);
			if($insert && !$db->error()) {
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();

			}else{
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	}

}

?>