<?php

// Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

// Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">Photo Galleries
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter stickyheader sortable nowrap">

				<thead>
					<th width="1px" data-sorter="false"></th>
					<th width="1px" class="nopadding-h" data-sorter="false"></th>
					<th width="auto">Name</th>
					<th width="125px" class="center nopadding">Status '.
						$CMSBuilder->tooltip('Status', '<p><i class=\'fas fa-eye\'></i> Show &nbsp; <i class=\'fas fa-link\'></i> Hide &nbsp; <i class=\'fas fa-eye-slash\'></i> Disable</p>If you <strong>hide</strong> a gallery, it will be hidden from the navigation but you can still navigate to it directly. If you <strong>disable</strong> a gallery, it will be hidden from the navigation and you will NOT be able to navigate to it.').'
					</th>
					<th width="1px" class="nopadding-r" data-sorter="false">&nbsp;</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
				</thead>

				<tbody>';

				foreach($records_arr as $row){
					$seo_class = $seo_tooltip = '';
					if($cms_settings['enhanced_seo'] && (MASTER_USER || SEO_USER)){
						$seo_indicator = $Analyzer->score_tooltip($row['seo_score']);
						$seo_class     = $seo_indicator['class'];
						$seo_tooltip   = $seo_indicator['tooltip'];
					}

					echo '<tr class="'.$seo_class.'" data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-name="'.$row['name'].'" data-id="'.$row[$record_id].'">
						<td class="handle">'.$seo_tooltip.'<span class="fas fa-arrows-alt"></span></td>
						<td class="nopadding-h">'.render_gravatar($imagedir.'480/'.$row['image'], $imagedir.'1920/'.$row['image'], $row['name']).'</td>
						<td>'.$row['name'].' &nbsp;<small>('.$row['photos'].' Photo'.($row['photos'] != 1 ? 's' : '').')</small></td>
						<td>'.$CMSBuilder->status_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td class="right nopadding-r"><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
						<td class="right"><a target="_blank" href="'.$row['page_url'].'"><i class="fas fa-external-link-alt"></i></a></td>
					</tr>';
				}

				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';

//Image cropping
}else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');

//Display form
}else{
	$image = '';

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = check_file($data['image'], $imagedir);
		if(!isset($_POST['save'])){
			$row = $data;
		}

		echo '<div class="actions-nav flex-container">
			<div class="flex-column right">
				<small><b>Link to '.$record_name.':</b> '.$row['page_url'].' <a href="'.$row['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td></small>
			</div>
		</div>';

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">

		<div class="panel">
			<div class="panel-header">Gallery Details
				<span class="panel-toggle fas fa-chevron-up"></span>

				<div class="panel-switch">
					<label>Status'.$CMSBuilder->tooltip('Status', '<p><i class=\'fas fa-eye\'></i> Show &nbsp; <i class=\'fas fa-link\'></i> Hide &nbsp; <i class=\'fas fa-eye-slash\'></i> Disable</p>If you <strong>hide</strong> a gallery, it will be hidden from the navigation but you can still navigate to it directly. If you <strong>disable</strong> a gallery, it will be hidden from the navigation and you will NOT be able to navigate to it.').'</label>'.
					$CMSBuilder->status_toggle($record_db, $record_id, NULL, $row['showhide'] ?? 0).
				'</div>
			</div>

			<div class="panel-content">
				<div class="flex-container">
					<div class="form-field">
						<label>Gallery Name'.(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Gallery Name', 'Put special emphasis on a word or phrase by enclosing it in {curly brackets}.'). '</label>
						<input type="text" name="name" id="button-text" value="'.($row['name'] ?? '').'" class="input'.(in_array('name', $required) ? ' required' : '').'" /><br />
					</div>

					<div class="form-field">
						<label>Short Description'.(in_array('content', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="content" value="'.($row['content'] ?? '').'" class="input'.(in_array('content', $required) ? ' required' : '').'" />
					</div>

					<div class="form-field">
						<label>Numerical Order'.(in_array('ordering', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.').'</label>
						<select name="ordering" class="select">
							<option value="101">Default</option>';
							for($i = 1; $i < 101; $i++){
								echo '<option value="'.$i.'" '.($row['ordering'] == $i ? 'selected' : '').'>'.$i.'</option>';
							}
						echo '</select>
					</div>
				</div>
			</div>
		</div>

		<div class="panel page-content">
			<div class="panel-header">Gallery Banner
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">';

					// Upload Image
					echo $CMSBuilder->img_holder($image, $imagedir.'1920/', $imagedir.'1024/');

					[$max_W, $max_H] = CMSUploader::max_size('gallery', 'image');
					echo '<div class="form-field">
						<label>Upload Image '.(in_array('image', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Upload Image', 'Image must be smaller than '.$_max_filesize['megabytes'].' and larger than '.$max_W.' x '.$max_H.'.').'</label>
						<input type="file" class="input'.(in_array('image', $required) ? ' required' : '').'" name="image" value="" />
					</div>
				</div>
			</div>
		</div>

		<div class="panel page-content">';

			[$max_W, $max_H] = CMSUploader::max_size('gallery-photo', 'image');
			echo '<div class="panel-header">Image Uploader '.
					$CMSBuilder->tooltip('Image Uploader', 'Click or drag files into the bordered box to upload multiple photos at once. Photos should be larger than '.$max_W.' x '.$max_H.' pixels and under 2MB.<br><br><i class=\'fas fa-arrows-alt\'></i> Rearrange the order of photos<br/><i class=\'fas fa-trash-alt\'></i> Permenantly delete a photo<br/><i class=\'fas fa-external-link-alt\'></i> View full-size image<br/><i class=\'fas fa-edit\'></i> Edit the image in more detail<br/><br/>Select or deselect images to toggle visibility.').
					'<span class="panel-toggle fas fa-chevron-up"></span>
				</div>

				<div class="panel-content">';

				if(ITEM_ID == ''){
					echo '<p>You must save the gallery before uploading photos</p>';
				}else{
					$uploader_params = array(
						$record_id => ITEM_ID,
						'photo_name' => ($row['name'] ?? $data['name']),
						'edit_url' => $photos_section['page_url'].'?action=edit&gallery_id='.ITEM_ID.'&item_id=%item_id%'
					);

					echo '<div id="gallery-images-form" class="dz-dropzone dropzone" data-params=\''.json_encode($uploader_params).'\'>
						<input type="file" name="file" />
						<div class="dz-default dz-message">
							Drop Image(s) Here
							<small class="clear">or</small>
							<button type="button" class="button-sm"><i class="fas fa-upload"></i>Click to Upload</button>
						</div>
					</div>
					<ul class="dz-gallery sortable-container">';
						if(!empty($data['gallery_photos'])){
							foreach($data['gallery_photos'] as $photo){
								echo '<li class="dz-uploaded-image">
									<input type="checkbox" name="photo_id[]" value="'.$photo['photo_id'].'" id="gallery-image-'.$photo['photo_id'].'" class="container-checkbox"'.($photo['showhide'] ? '' : ' checked').' />
									<label for="gallery-image-'.$photo['photo_id'].'">
										<img src="'.$path.$photosdir.'thumbs/'.$photo['image'].'" alt="Photo" />
									</label>
									<a href="#" class="sort-handler"><i class="fas fa-arrows-alt"></i></a>
									<div class="buttons">
										<a href="'.$path.$photosdir.$photo['image'].'" class="light-gallery"><i class="fas fa-expand"></i></a>
										<a href="'.$photos_section['page_url'].'?action=edit&gallery_id='.ITEM_ID.'&item_id='.$photo['photo_id'].'" class="edit-btn"><i class="fas fa-edit"></i></a>
										<a href="#" class="delete-btn confirm-submit-btn" data-confirm="Are you sure you want to delete this image?" data-confirm-callback="deleteGalleryImage" data-id="'.$photo['photo_id'].'"><i class="fas fa-trash-alt"></i></a>
									</div>
								</li>';
							}
						}
					echo '</ul>';
				}

			echo '</div>
		</div>';

		//SEO Content/Analysis
		include('includes/widgets/seotabs.php');

		//Sticky footer
		include('includes/widgets/formbuttons.php');

		echo '<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'].'" />
	</form>';

}

?>