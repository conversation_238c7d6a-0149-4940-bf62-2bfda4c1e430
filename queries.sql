
CREATE TABLE `account_groups` (
  `group_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `role_id` int(11) DEFAULT NULL,
  `group_name` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`group_id`),
  <PERSON>EY `role_id` (`role_id`),
  CONSTRAINT `account_groups_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `account_roles` (`role_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- pga_old_info_only.facilities definition

CREATE TABLE `facilities` (
  `facility_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `facility_name` varchar(100) DEFAULT NULL,
  `page` varchar(100) DEFAULT NULL,
  `type` enum('Public','Range/Teaching Facility','Semi-Private','Resort','Private') DEFAULT NULL,
  `affiliation` enum('All','CPGA','Non-CPGA') DEFAULT NULL,
  `region` enum('Northern Alberta','Edmonton & Area','Central Alberta','Calgary & Area','Southern Alberta','Other') DEFAULT NULL,
  `address1` varchar(100) DEFAULT NULL,
  `address2` varchar(100) DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `province` varchar(50) DEFAULT NULL,
  `postal_code` varchar(10) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'CA',
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `website` varchar(200) DEFAULT NULL,
  `content` text DEFAULT NULL,
  `image` varchar(100) DEFAULT NULL,
  `image_alt` tinytext DEFAULT NULL,
  `logo` varchar(100) DEFAULT NULL,
  `logo_alt` tinytext DEFAULT NULL,
  `gallery_id` int(11) DEFAULT NULL,
  `google_map` int(1) DEFAULT 1,
  `gpslat` varchar(50) DEFAULT NULL,
  `gpslong` varchar(50) DEFAULT NULL,
  `zoom` int(2) DEFAULT NULL,
  `meta_title` varchar(200) DEFAULT NULL,
  `meta_description` varchar(500) DEFAULT NULL,
  `focus_keyword` varchar(100) DEFAULT NULL,
  `seo_score` decimal(10,2) NOT NULL DEFAULT 0.00,
  `showhide` int(1) DEFAULT 0,
  `date_added` datetime DEFAULT NULL,
  `last_updated` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `ownership` enum('Community','Member Owned','Privately Owned','Non Profit Society','Town Owned','City Owned','Municipal','City Leased','Country Owned','Enoch Cree Nation','Government','High Level Golf Society','Joint','Member Equity','Membership','Other','RCGA Group','Sherwood Park Golf Course Inc.') DEFAULT NULL,
  `NUMBER_OF_HOLES` int(10) DEFAULT NULL,
  `TOTAL_YARDS` text NOT NULL,
  `TOTAL_PAR` text NOT NULL,
  `SUPERINTENDENT` text NOT NULL,
  `OWNER` text NOT NULL,
  `FOOD_BEVERAGE` text NOT NULL,
  `PRESIDENT` text NOT NULL,
  `PRO_SHOP_PHONE` text NOT NULL,
  `PRO_SHOP_NAME` text NOT NULL,
  `YEAR_ESTABLISHED` text NOT NULL,
  `DRIVING_RANGE` text NOT NULL,
  `MAIN_CONTACT` varchar(50) NOT NULL,
  `CPGA` text NOT NULL,
  `NGCOA` text NOT NULL,
  `FAX` text NOT NULL,
  PRIMARY KEY (`facility_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1184 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

_____________________________________________
ALTER TABLE account_profiles
  -- Add the columns (placing them logically, e.g., after address/contact info)
  ADD COLUMN facility_id INT(11) UNSIGNED DEFAULT NULL AFTER phone,
  ADD COLUMN facility_access INT(1) NOT NULL DEFAULT 0 AFTER facility_id,
  ADD COLUMN group_id INT(11) UNSIGNED DEFAULT NULL AFTER facility_access,
  ADD COLUMN notes TEXT DEFAULT NULL AFTER group_id,

  -- Add the indexes for faster lookups on these new columns
  ADD INDEX `Facility ID` (`facility_id`),
  ADD INDEX `group_id` (`group_id`),

  -- Add the foreign key constraints referencing the other tables
  -- Using the constraint names from your target schema
  ADD CONSTRAINT `account_profiles_ibfk_4`
    FOREIGN KEY (`facility_id`)
    REFERENCES `facilities` (`facility_id`)
    ON DELETE SET NULL  -- If a facility is deleted, set facility_id to NULL here
    ON UPDATE CASCADE,  -- If a facility_id changes, update it here too

  ADD CONSTRAINT `account_profiles_ibfk_6`
    FOREIGN KEY (`group_id`)
    REFERENCES `account_groups` (`group_id`)
    ON DELETE SET NULL  -- If an account group is deleted, set group_id to NULL here
    ON UPDATE CASCADE;  -- If a group_id changes, update it here too


--    ALTER TABLE `account_profiles` 
--    ADD CONSTRAINT `account_profiles_ibfk_6`
--     FOREIGN KEY (`group_id`) 
--     REFERENCES `account_groups`(`group_id`) 
--     ON DELETE SET NULL 
--     ON UPDATE CASCADE; 


ALTER TABLE account_profiles
  -- Add columns in the specified order with comments
  ADD COLUMN phone_alt varchar(15) DEFAULT NULL AFTER phone,
  ADD COLUMN nick_name varchar(50) DEFAULT NULL AFTER last_name,
  ADD COLUMN title varchar(50) DEFAULT NULL AFTER name_suffix,
  ADD COLUMN gender enum('Male','Female') DEFAULT NULL AFTER title,
  ADD COLUMN dob date DEFAULT NULL AFTER gender,

  ADD COLUMN show_email enum('0','1','-1') NOT NULL DEFAULT '0'
    COMMENT 'Visibility: 0 = Private, 1 = Public, -1 = Members only' AFTER photo,

  ADD COLUMN show_qa enum('0','1','-1') NOT NULL DEFAULT '1'
    COMMENT 'Visibility: 0 = Private, 1 = Public, -1 = Members only' AFTER show_email,

  ADD COLUMN show_profile enum('0','1','-1') NOT NULL DEFAULT '1'
    COMMENT 'Visibility: 0 = Private, 1 = Public, -1 = Members only' AFTER show_qa,

  ADD COLUMN show_phone enum('0','1','-1') NOT NULL DEFAULT '0'
    COMMENT 'Visibility: 0 = Private, 1 = Public, -1 = Members only' AFTER show_profile;
_____________________________________________

-- Add columns after 'tollfree'
ALTER TABLE account_profiles
  ADD COLUMN education varchar(200) DEFAULT NULL AFTER tollfree,
  ADD COLUMN profile text DEFAULT NULL AFTER education,
  ADD COLUMN website varchar(100) DEFAULT NULL AFTER profile,
  ADD COLUMN twitter varchar(100) DEFAULT NULL AFTER website,
  ADD COLUMN facebook varchar(100) DEFAULT NULL AFTER twitter,
  ADD COLUMN instagram varchar(100) DEFAULT NULL AFTER instagram,
  ADD COLUMN linkedin varchar(100) DEFAULT NULL AFTER instagram;

-- Add columns after 'group_id'
ALTER TABLE account_profiles
  ADD COLUMN pga_number varchar(50) DEFAULT NULL AFTER group_id,
  ADD COLUMN pga_member_since date DEFAULT NULL AFTER pga_number;

-- Optional: Add Indexes for potentially searchable fields
-- ALTER TABLE account_profiles
  -- ADD INDEX `website` (`website`),
  -- ADD INDEX `pga_number` (`pga_number`);


INSERT INTO `facilities` (`facility_id`, `facility_name`, `page`, `type`, `affiliation`, `region`, `address1`, `address2`, `city`, `province`, `postal_code`, `country`, `email`, `phone`, `website`, `content`, `image`, `image_alt`, `logo`, `logo_alt`, `gallery_id`, `google_map`, `gpslat`, `gpslong`, `zoom`, `meta_title`, `meta_description`, `focus_keyword`, `seo_score`, `showhide`, `date_added`, `last_updated`, `ownership`, `NUMBER_OF_HOLES`, `TOTAL_YARDS`, `TOTAL_PAR`, `SUPERINTENDENT`, `OWNER`, `FOOD_BEVERAGE`, `PRESIDENT`, `PRO_SHOP_PHONE`, `PRO_SHOP_NAME`, `YEAR_ESTABLISHED`, `DRIVING_RANGE`, `MAIN_CONTACT`, `CPGA`, `NGCOA`, `FAX`) VALUES (NULL, 'TEST', NULL, 'Public', NULL, 'Northern Alberta', 'TBD', 'TBD', 'edmonton', 'Alberta', NULL, 'CA', NULL, NULL, NULL, 'Lorem ipdum sfEWf sfdf sfgndsgn dsgbg fdgfdgdfg dgfdgdg', NULL, NULL, NULL, NULL, NULL, '1', '53.563967', '-113.490357', NULL, NULL, NULL, NULL, '0.00', '0', NULL, current_timestamp(), 'City Leased', NULL, '', '', '', '', '', '', '', '', '', '', '', '', '', '')


______________________________________________________________  

ALTER TABLE accounts
  -- Add email_alt after email
  ADD COLUMN email_alt varchar(100) DEFAULT NULL AFTER email,

  -- Add activate_time after register_time
  ADD COLUMN activate_time datetime DEFAULT NULL AFTER register_time,

  -- Add showhide after secret
  ADD COLUMN showhide int(1) NOT NULL DEFAULT 0 AFTER secret;


////////////////////////
-- old accounts_profile table for reference
-- pga_old_info_only.account_profiles definition

CREATE TABLE `account_profiles` (
  `profile_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `class_id` int(11) unsigned DEFAULT NULL,
  `membership_id` int(11) unsigned DEFAULT NULL,
  `category_id` int(10) unsigned DEFAULT NULL,
  `facility_id` int(11) unsigned DEFAULT NULL,
  `facility_access` int(1) NOT NULL DEFAULT 0,
  `group_id` int(11) unsigned DEFAULT NULL,
  `pga_number` varchar(50) DEFAULT NULL,
  `pga_member_since` date DEFAULT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `middle_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `nick_name` varchar(50) DEFAULT NULL,
  `name_prefix` varchar(50) DEFAULT NULL,
  `name_suffix` varchar(50) DEFAULT NULL,
  `title` varchar(50) DEFAULT NULL,
  `gender` enum('Male','Female') DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `board_member` enum('0','1','2','3','4') NOT NULL DEFAULT '0',
  `board_member_role` enum('President','Vice President','Past President','Advisor') DEFAULT NULL,
  `address1` varchar(255) DEFAULT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `province` varchar(50) DEFAULT NULL,
  `postal_code` varchar(10) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'CA',
  `phone` varchar(15) DEFAULT NULL,
  `phone_alt` varchar(15) DEFAULT NULL,
  `fax` varchar(15) DEFAULT NULL,
  `tollfree` varchar(15) DEFAULT NULL,
  `education` varchar(200) DEFAULT NULL,
  `profile` text DEFAULT NULL,
  `website` varchar(100) DEFAULT NULL,
  `twitter` varchar(100) DEFAULT NULL,
  `facebook` varchar(100) DEFAULT NULL,
  `instagram` varchar(100) DEFAULT NULL,
  `linkedin` varchar(100) DEFAULT NULL,
  `photo` tinytext DEFAULT NULL,
  `rewards_number` varchar(50) DEFAULT NULL,
  `rewards_updated` datetime DEFAULT NULL,
  `show_email` enum('0','1','-1') NOT NULL DEFAULT '0',
  `show_qa` enum('0','1','-1') NOT NULL DEFAULT '1',
  `show_profile` enum('0','1','-1') NOT NULL DEFAULT '1',
  `show_phone` enum('0','1','-1') NOT NULL DEFAULT '0',
  `mc_subscribe` int(1) NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`profile_id`),
  UNIQUE KEY `Account ID` (`account_id`),
  KEY `Class ID` (`class_id`),
  KEY `Membership ID` (`membership_id`),
  KEY `Facility ID` (`facility_id`),
  KEY `category_id` (`category_id`),
  KEY `group_id` (`group_id`),
  CONSTRAINT `account_profiles_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`account_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `account_profiles_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `membership_classes` (`class_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `account_profiles_ibfk_3` FOREIGN KEY (`membership_id`) REFERENCES `membership_types` (`membership_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `account_profiles_ibfk_4` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`facility_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `account_profiles_ibfk_6` FOREIGN KEY (`group_id`) REFERENCES `account_groups` (`group_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `account_profiles_ibfk_7` FOREIGN KEY (`category_id`) REFERENCES `membership_categories` (`category_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5226 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

---------------------------------------------------
to add on dev portal db - 9 May 2025
---------------------------------------------------
ALTER TABLE `account_profiles`
  ADD COLUMN `class_id` INT(11) UNSIGNED DEFAULT NULL AFTER `group_id`, -- Or choose another column to place it after
  ADD COLUMN `membership_id` INT(11) UNSIGNED DEFAULT NULL AFTER `class_id`,
  ADD COLUMN `category_id` INT(10) UNSIGNED DEFAULT NULL AFTER `membership_id`;

ALTER TABLE `account_profiles`
  ADD CONSTRAINT `account_profiles_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `membership_classes` (`class_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `account_profiles_ibfk_3` FOREIGN KEY (`membership_id`) REFERENCES `membership_types` (`membership_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `account_profiles_ibfk_7` FOREIGN KEY (`category_id`) REFERENCES `membership_categories` (`category_id`) ON DELETE SET NULL ON UPDATE CASCADE;



----------------------------------------  
to run on dev portal db - 22 May 2025
----------------------------------------
ALTER TABLE `account_profiles`
  ADD COLUMN `board_member` ENUM('0','1','2','3','4') NOT NULL DEFAULT '0' AFTER `company`,
  ADD COLUMN `board_member_role` ENUM('President','Vice President','Past President','Advisor') DEFAULT NULL AFTER `board_member`,
  ADD COLUMN `rewards_number` VARCHAR(50) DEFAULT NULL AFTER `photo`,
  ADD COLUMN `rewards_updated` DATETIME DEFAULT NULL AFTER `rewards_number`,
  ADD COLUMN `mc_subscribe` INT(1) NOT NULL DEFAULT 1 AFTER `show_phone`,

  -- Modify Existing Columns (if needed, e.g., default for country)
  MODIFY COLUMN `country` VARCHAR(50) DEFAULT 'CA';

ALTER TABLE `account_profiles` 
DROP FOREIGN KEY `account_profiles_ibfk_4`; 
ALTER TABLE `account_profiles` 
ADD CONSTRAINT `account_profiles_ibfk_4` 
FOREIGN KEY (`facility_id`) 
REFERENCES `facilities`(`facility_id`) 
ON DELETE SET NULL 
ON UPDATE CASCADE;

-- pga_old_info_only.committees definition

CREATE TABLE `committees` (
  `committee_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `showhide` int(1) NOT NULL DEFAULT 0,
  `ordering` int(3) NOT NULL DEFAULT 101,
  `date_added` datetime DEFAULT NULL,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`committee_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

INSERT INTO committees (name,showhide,`ordering`,date_added,last_updated) VALUES
  ('Board of Directors Minutes',0,1,'2020-02-11 12:00:32','2021-12-04 01:13:53'),
  ('Assistants&rsquo; Board of Directors Minutes',0,2,'2020-02-11 12:18:44','2021-12-04 01:13:53'),
  ('Education Committee Minutes',0,3,'2020-02-11 12:18:57','2021-12-04 01:13:53'),
  ('Awards Committee Minutes',0,4,'2020-02-11 12:19:08','2021-12-04 01:13:53'),
  ('Buying Show Committee Minutes',0,5,'2020-02-11 12:19:26','2021-12-04 01:13:53'),
  ('Tournament Committee Minutes',0,6,'2020-02-11 12:19:40','2021-12-04 01:13:53'),
  ('Membership & Employment Committee Minutes',0,7,'2020-02-11 12:20:23','2021-12-04 01:13:53'),
  ('Promotions & Communications Committee Minutes',0,8,'2020-02-11 12:20:48','2021-12-04 01:13:53');

-- pga_old_info_only.account_committees definition
CREATE TABLE `account_committees` (
  `account_id` bigint(20) NOT NULL,
  `committee_id` int(11) NOT NULL,
  PRIMARY KEY (`account_id`,`committee_id`),
  KEY `committee_id` (`committee_id`),
  CONSTRAINT `account_committees_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`account_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `account_committees_ibfk_2` FOREIGN KEY (`committee_id`) REFERENCES `committees` (`committee_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- pga_old_info_only.account_change_log definition
CREATE TABLE `account_change_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `field_name` varchar(100) DEFAULT NULL,
  `current_value` text DEFAULT NULL,
  `previous_value` text DEFAULT NULL,
  `comments` text DEFAULT NULL,
  `showhide` int(1) NOT NULL DEFAULT 0,
  `updated_on` date NOT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_id` (`account_id`),
  CONSTRAINT `account_change_log_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`account_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3102 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

below sql not working on dev db - so ignored
ALTER TABLE account_change_log MODIFY COLUMN updated_on DATE DEFAULT CURRENT_DATE;

facilities_cms_sections.sql run on dev portal db 

___________________
awards and registration  cms_sections added on dev db on 5 June 2025

data to be imported from below tables into dev portal
-1.reg_settings
-2.reg_categories
-3.reg_waivers
4.reg_waiting_list
5.reg_promo_codes
6.reg_events
7.reg_occurrences
-8.gl_accounts
9.refunds
10.payments
11.refund_attendees
13.invoices
14.account_billing_profiles
15.account_survey



-----------------------------------
RAN on dev portal - 16 june 2025
-----------------------------------
CREATE TABLE `reg_attendee_changelog` (
  `changelog_id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `attendee_id` BIGINT(20) UNSIGNED NOT NULL,
  `account_id` BIGINT(22) NULL,
  `description` TEXT NULL,
  `date_added` DATETIME NULL,
  `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`changelog_id`),
  KEY `idx_attendee_id` (`attendee_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_date_added` (`date_added`),

  -- Uncomment the lines below if you want to enforce foreign key constraints
  CONSTRAINT `fk_changelog_attendee` FOREIGN KEY (`attendee_id`) REFERENCES `reg_attendees`(`attendee_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_changelog_account` FOREIGN KEY (`account_id`) REFERENCES `accounts`(`account_id`) ON DELETE SET NULL ON UPDATE CASCADE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


ALTER TABLE `reg_occurrence_sponsors` DROP FOREIGN KEY `reg_occurrence_sponsors_ibfk_2`; 

ALTER TABLE `reg_occurrence_sponsors` 
MODIFY COLUMN `sponsor_id` INT(11) UNSIGNED NOT NULL;

ALTER TABLE `reg_occurrence_sponsors` ADD CONSTRAINT `reg_occurrence_sponsors_ibfk_2` FOREIGN KEY (`sponsor_id`) REFERENCES `partners`(`partner_id`) ON DELETE CASCADE ON UPDATE CASCADE;
