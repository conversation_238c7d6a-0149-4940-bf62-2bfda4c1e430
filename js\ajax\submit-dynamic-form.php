<?php  

//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");
require_once("../../includes/plugins/recaptcha/recaptchalib.php");

error_reporting(-1);
ini_set('display_errors', 'off');

$response = array();
$response['errors'] = false;
$response['error_fields'] = array();
$response['msg_validation'] = '';

$filedir = '../../uploads/files/';

//Cookie validation
if(!isset($_POST['xid']) || $_POST['xid'] != $_COOKIE['xid']){
	$response['errors'][] = 'Please make sure cookies are enabled on your browser then try again.';
}else{

	//Fetch form details
	$form = $SiteBuilder->get_attached_form($_POST['form_id']);
	if(empty($form)){
		$response['errors'][] = 'Unable to retrieve form data. Please try again.';
	}else{

		//Set form vars
		$to = $form['send_to'];
		$ccto = ($form['send_copy'] && USER_LOGGED_IN ? array($Account->email) : array());
		$subject = $form['form_name'];
		$emailbody = '<h3>' .$subject. '</h3>';

		$referer_url = get_referer_url();
		$referer_full_url = $siteurl.($referer_url['path'] ?? '').(($referer_url['query'] ?? '') ? '?'.$referer_url['query'] : '');
		$ip_address = get_ip();

		//Field validation
		foreach($form['form_fields'] as $fieldset){
			foreach($fieldset['form_fields'] as $field){				
				$field_name = 'field-'.$form['form_id'].'-'.$field['field_id'];
				if(is_array($_POST[$field_name])){
					$_POST[$field_name] = implode(", ", $_POST[$field_name]);
				}
				
				if($field['required']){
					if($field['type'] == 'file'){
						if(!isset($_FILES[$field_name]['name']) || empty($_FILES[$field_name]['name'])) {
								//echo "if";exit;
								$response['error_fields'][] = $field_name;
								$response['errors'][0] = 'Please fill in all required fields.';
							
							}
					}else{
						if(!isset($_POST[$field_name]) || trim($_POST[$field_name]) == '' || preg_match('/^\s+$/', $_POST[$field_name]) || $_POST[$field_name] == $field['label']){
							$response['error_fields'][] = $field_name;
							$response['errors'][0] = 'Please fill in all required fields.';
						}
					}
				}
				if($field['type'] == 'email'){
					if(isset($_POST[$field_name]) && trim($_POST[$field_name]) != '' && !checkmail($_POST[$field_name])){
						$response['error_fields'][] = $field_name;
						$response['errors'][1] = 'Email address is invalid.';
					}
				}
				if($field['type'] == 'phone'){
					if(isset($_POST[$field_name]) && trim($_POST[$field_name]) != '' && !detect_phone($_POST[$field_name])){
						$response['error_fields'][] = $field_name;
						$response['errors'][2] = 'Phone number is invalid.';
					}
					$_POST[$field_name] = format_phone_number($_POST[$field_name]);
				}

				if($field['type'] == 'file'){
					
					if(isset($_FILES[$field_name]['name']) && !empty($_FILES[$field_name]['name'])) {
						$ext = pathinfo($_FILES[$field_name]['name'], PATHINFO_EXTENSION);
						
						
						$finfo = finfo_open(FILEINFO_MIME_TYPE);
						$mime = strtolower(finfo_file($finfo, $_FILES[$field_name]['tmp_name']));
						//$valid_files_values = implode(', ',  array_filter(array_values($valid_files)));
						
						$tempFile =	$_FILES[$field_name]['tmp_name'];
						$filename = $_FILES[$field_name]['name'];
						$filename_full = $filedir.$filename;
						
						// Validate file size
						if($_FILES[$field_name]['size'] > 20480000) {
							$response['error_fields'][] = $field_name;
							$response['errors'][] = 'File is too large. Please make sure the file is under 20MB.';
						}elseif($ext != 'docx' && $ext != 'pdf' &&  $ext != 'jpg' &&  $ext != 'doc' &&  $ext != 'png'){
							$response['error_fields'][] = $field_name;
							$response['errors'][] = 'Only .jpg,.png,.pdf and .docx files are allowed for upload.';
						}else{
							move_uploaded_file($tempFile,$filename_full);
						}
					}
				}

				if($field['type'] == 'email' && $field['send_copy'] && !in_array(trim($_POST[$field_name]), $ccto)){
					$ccto[] = trim($_POST[$field_name]);
				}

				if($field['type'] == 'facilities' && $field['send_copy_facility']){
							
					//Get head professional (2) and executive professional (3) emails and send a copy
					$db->query("SELECT `accounts`.`email` FROM `accounts` ".
					"INNER JOIN `account_profiles` ON `accounts`.`account_id` = `account_profiles`.`account_id` ".
					"INNER JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
					"WHERE (`account_profiles`.`class_id` = 2 || `account_profiles`.`class_id` = 3) && `facilities`.`facility_name` = ?", array($_POST[$field_name]));
					if(!$db->error() && $db->num_rows()){
						$facilities = $db->fetch_array();
						foreach($facilities as $facility){
							if(!in_array($facility['email'], $ccto)){
								$ccto[] = $facility['email'];
							}
						}
					}
				}
			}
		}

		//Recaptcha
		$recaptcha_response = NULL;
		$reCaptcha = new ReCaptcha($global['recaptcha_secret']);
		$recaptcha_response = $reCaptcha->verifyResponse(
			$_SERVER["REMOTE_ADDR"],
			$_POST["g-recaptcha-response"]
		);
		$response['test'] = $recaptcha_response;
		if($recaptcha_response != NULL && $recaptcha_response->success){
			//recaptcha validated
		}else{
			$response['errors'][] = 'Please verify you are not a robot.';
		}

		//All valid
		if(!$response['errors']){

			//Insert to db
			$db->new_transaction();
			
			$submission_id = $db->insert('form_submissions', [
				'form_id' => $form['form_id'],
				'subject' => $subject,
				'url' => $referer_full_url,
				'ip_address' => $ip_address,
				'hostname' => gethostbyaddr($ip_address),
				'session_id' => session_id(),
				'timestamp' => date("Y-m-d H:i:s")
			]);
			
			foreach($form['form_fields'] as $fieldset_id=>$fieldset){
				
				$submission_fieldset_id = NULL;
				if($fieldset_id > 0 && !empty($fieldset['form_fields'])){
					$emailbody .= '<hr /><h6>' .$fieldset['legend']. '</h6>';
					$params = array($submission_id, $fieldset['legend']);
					$db->query("INSERT INTO `form_submission_fieldsets`(`submission_id`, `legend`) VALUES(?,?)", $params);
					$submission_fieldset_id = $db->insert_id();
				}
				
				$emailbody .= '<p>';
				foreach($fieldset['form_fields'] as $field){
					$field_name = 'field-'.$form['form_id'].'-'.$field['field_id'];
					//$params = array($submission_id, $submission_fieldset_id, $field['label'], $_POST[$field_name],$field["type"]);
					if($field['type'] == 'file'){
						$params = array($submission_id, $submission_fieldset_id, $field['label'],$_FILES[$field_name]['name'],$field["type"]);
					}else{
						$params = array($submission_id, $submission_fieldset_id, $field['label'], $_POST[$field_name],$field["type"]);
					}
					$db->query("INSERT INTO `form_submission_fields`(`submission_id`, `fieldset_id`, `label`, `value`, `type`) VALUES(?,?,?,?,?)", $params);
					$emailbody .= '<strong>' .$field['label']. ':</strong><br />' .(!isset($_POST[$field_name]) || trim($_POST[$field_name]) == '' ? '-' : nl2br($_POST[$field_name])). '<br />';

				}
				$emailbody .= '</p>';
			}

			if(!$db->error()){
				$db->commit();
				$response['msg_validation'] = 'Your submission has been sent.';
				
				//Send email notification
				if(trim($to) != ''){
					send_email($to, $subject, $emailbody);
				}

				if(!empty($ccto)){
					send_email(implode(',', $ccto), $subject, $emailbody);
				}

			}else{
				$response['errors'][] = 'Unable to submit form. '.$db->error();
			}
		}

	}
}

//Has Errors
if($response['errors']){
	$response['msg_validation'] = implode('<br />', $response['errors']);
}

print json_encode($response);

?>