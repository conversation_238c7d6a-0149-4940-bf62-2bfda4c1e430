<?php

//Config
include("config.php");

//Get vars
$id = (isset($_GET['id']) ? $_GET['id'] : NULL);
$format = (isset($_GET['format']) ? $_GET['format'] : 'html');
$columns = array();
$csv_rows = array();

//Get event
$event = $Registration->get_occurrence($id);
if(empty($event)){
	header('Location: '.$_sitepages['tournaments']['page_url']);
	exit();
}else{
	
	//Check report permissions
	if((USER_LOGGED_IN && $Account->account_has_role(1)) || (isset($event['report_money']) && $event['report_money'])){
		
		//Get tournament field size
		$query = $db->query("SELECT `attendee_id` FROM `reg_attendees` WHERE `occurrence_id` = ? && `reg_status` = ? && `partner_id` IS NULL", array($id, 'Registered'));
		$field_size = $db->num_rows();
		
		//Skins
		$skinscount1 = 0;
		$skinscount2 = 0;
		
		//Set data array
		$records = array();

		//Loop through attendees and partners
		for($i=0; $i<=1; $i++){

			//Get all tournament attendees
			//Attendee is registered
			//Attendee is account holder
			//Attendee is not a partner
			//Event date in selected year
			$params = array('Registered');
			if(!empty($id)){
				$params[] = $id;
			}
			$query = $db->query("SELECT ".
				"`reg_tournament_field`.*, ".
				"`reg_attendees`.`first_name`, `reg_attendees`.`last_name`, `reg_attendees`.`account_id`, `reg_attendees`.`partner_id`, ".
				"`account_profiles`.`profile_id`, ".
				"`facilities`.`facility_name`, ".
				"`reg_event_categories`.`category_id`, ".
				"(IFNULL(`reg_tournament_field`.`r1_score`, 0)+IFNULL(`reg_tournament_field`.`r2_score`, 0)) AS `final_score`, ".
				"(IFNULL(`reg_tournament_field`.`r1_team_gross`, 0)+IFNULL(`reg_tournament_field`.`r2_team_gross`, 0)) AS `team_gross`, ".
				"(IFNULL(`reg_tournament_field`.`r1_team_net`, 0)+IFNULL(`reg_tournament_field`.`r2_team_net`, 0)) AS `team_net`, ".
				"IFNULL(`reg_tournament_field`.`prize`, 0) AS `prize`, ".
				"IFNULL(`reg_tournament_field`.`team_prize`, 0) AS `team_prize`, ".
				"IFNULL(`reg_tournament_field`.`r1_score`, 0) AS `r1_score`, ".
				"IFNULL(`reg_tournament_field`.`r2_score`, 0) AS `r2_score` ".
			"FROM `reg_attendees` ".
			"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
			"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
			"LEFT JOIN `reg_event_categories` ON `reg_attendees`.`event_id` = `reg_event_categories`.`event_id` ".
			"LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ".
			"LEFT JOIN `reg_occurrences` ON `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` ".
			"LEFT JOIN `reg_tournament_field` ON `reg_attendees`." .($i==0 ? "`attendee_id`" : "`partner_id`"). " = `reg_tournament_field`.`attendee_id` ".
			"WHERE `reg_attendees`.`reg_status` = ? && `reg_attendees`.`account_id` IS NOT NULL && `reg_attendees`.`partner_id` " .($i==0 ? "IS NULL" : "IS NOT NULL"). " && `reg_events`.`role_id` != 8 ".
			(!empty($id) ? "&& `reg_occurrences`.`occurrence_id` = ? " : "").
			"ORDER BY `reg_tournament_field`.`points` DESC, `reg_attendees`.`last_name` ASC, `reg_attendees`.`first_name` ASC", $params);
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $row){

					//Team prize amount should be divided between team members, except for Pro-Ams
					if($row['category_id'] != 8){
						$row['team_prize'] = ($row['team_prize']/2);
					}else{
						$row['team_prize'] = 0;
					}
					
					//Number of skins
					$r1_skins = array();
					foreach(explode(',', $row['r1_skins']) as $hole){
						$hole = str_replace('#', '', trim($hole));
						if($hole != ''){
							$r1_skins[] = $hole;
						}
					}
					$r2_skins = array();
					foreach(explode(',', $row['r2_skins']) as $hole){
						$hole = str_replace('#', '', trim($hole));
						if($hole != ''){
							$r2_skins[] = $hole;
						}
					}

					//Push to records array
					if(array_key_exists($row['account_id'], $records)){

						if(empty($row['partner_id'])){
							$records[$row['account_id']]['score'] += ($row['r1_score']+$row['r2_score']);
							$records[$row['account_id']]['prize'] += $row['prize'];
							$records[$row['account_id']]['r1_skins'] .= $row['r1_skins'];
							$records[$row['account_id']]['r2_skins'] .= $row['r2_skins'];
							$records[$row['account_id']]['r1_skins_count'] += count($r1_skins);
							$records[$row['account_id']]['r2_skins_count'] += count($r2_skins);
							$skinscount1 += count($r1_skins);
							$skinscount2 += count($r2_skins);
						}
						$records[$row['account_id']]['team_prize'] += $row['team_prize'];
						$records[$row['account_id']]['total_prize'] = ($records[$row['account_id']]['prize']+$records[$row['account_id']]['team_prize']);
						$records[$row['account_id']]['overall_money'] = $records[$row['account_id']]['total_prize'];


					}else{
						$records[$row['account_id']] = array(
							'profile_id' => $row['profile_id'],
							'first_name' => $row['first_name'],
							'last_name' => $row['last_name'],
							'facility_name' => $row['facility_name'],
							'score' => (empty($row['partner_id']) ? $row['r1_score']+$row['r2_score'] : 0),
							'prize' => (empty($row['partner_id']) ? $row['prize'] : 0),
							'r1_skins' => $row['r1_skins'],
							'r2_skins' => $row['r2_skins'],
							'r1_skins_count' => (empty($row['partner_id']) ? count($r1_skins) : 0),
							'r2_skins_count' => (empty($row['partner_id']) ? count($r2_skins) : 0),
							'r1_skins_prize' => 0,
							'r2_skins_prize' => 0,
							'team_prize' => $row['team_prize'],
							'total_prize' => $row['prize']+$row['team_prize'],
							'overall_money' => $row['prize']+$row['team_prize']
						);
						if(empty($row['partner_id'])){
							$skinscount1 += count($r1_skins);
							$skinscount2 += count($r2_skins);
						}
					}
					
					if(!empty($row['prize']) && $row['prize'] > 0){
						//$columns[] = 'prize';
					}
					if(!empty($row['team_prize']) && $row['team_prize'] > 0){
						//$columns[] = 'team_prize';
					}
					if(!empty($row['r1_skins'])){
						$columns[] = 'r1_skins';
					}
					if(!empty($row['r2_skins'])){
						$columns[] = 'r2_skins';
					}
					
				}
			}

		}
		
		//Get tournament sponsors
		$sponsors = $Registration->get_occurrence_sponsors($id);
		
		//Set html
		$header = '<table cellpadding="0" cellspacing="0" width="100%" border="0">
			<tr>
				<td width="120px"><img src="' .$logo. '" width="120px" /></td>
				<td align="center" style="font-size:12px;">
					<strong>' .$event['event_name']. '</strong><br />'.
					(trim($event['facility_name']) != '' ? $event['facility_name'] : 'TBD'). '<br />'.
					format_date_range($event['start_date'], $event['end_date']). '<br />
					Field Size: ' .$field_size. '<br />'.
					(!empty($event['purse']) && $event['purse'] > 0 ? 'Total Purse: $'.number_format($event['purse'], 2) : '').'
				</td>
				<td width="120px" align="right">';
				if(!empty($sponsors)){
					foreach($sponsors as $sponsor){
						if($sponsor['image'] != '' && file_exists('../images/logos/'.$sponsor['image'])){
							$header .= '<img src="' .$imagepath.'logos/'.$sponsor['image']. '" width="80px" style="margin-bottom:10px;" />';	
						}
					}
				}
				$header .= '</td>
			</tr>
		</table><br />';
		
		//Determine skins amounts
		$skins1prize = $event['skins_pot1']/$skinscount1;
		$skins2prize = $event['skins_pot2']/$skinscount2;
		foreach($records as $key=>$record){
			$records[$key]['r1_skins_prize'] = number_format($skins1prize*$record['r1_skins_count'], 2, '.', '');	
			$records[$key]['r2_skins_prize'] = number_format($skins2prize*$record['r2_skins_count'], 2, '.', '');	
			$records[$key]['overall_money'] += $records[$key]['r1_skins_prize']+$records[$key]['r2_skins_prize'];
		}

		//Sort by column
		foreach($records as $key=>$row){
			$prize[$key] = $row['total_prize'];
			$money[$key] = $row['overall_money'];
			$lname[$key] = $row['last_name'];
			$fname[$key] = $row['first_name'];
		}
		array_multisort($prize, SORT_DESC, $money, SORT_DESC, $lname, SORT_ASC, $fname, SORT_ASC, $records);

		//Html
		$html = $header;
		$html .= '<table cellpadding="5" cellspacing="1" width="100%" border="0"><tr>
				<th style="' .$css['th']. '">Player</th>
				<th style="' .$css['th']. '">Facility</th>
				' .(in_array('prize', $columns) ? '<th style="' .$css['th']. '" width="100px">Individual Money</th>' : ''). '
				' .(in_array('team_prize', $columns) ? '<th style="' .$css['th']. '" width="100px">Team Money</th>' : ''). '
				<th style="' .$css['th']. '" width="100px">Money</th>
				' .(in_array('r1_skins', $columns) ? '<th style="' .$css['th']. '" width="100px">' .(in_array('r2_skins', $columns) ? 'R1 ' : ''). 'Skins</th>' : ''). '
				' .(in_array('r2_skins', $columns) ? '<th style="' .$css['th']. '" width="100px">' .(in_array('r1_skins', $columns) ? 'R2 ' : ''). 'Skins</th>' : ''). '
				<th style="' .$css['th']. '" width="100px">Total Money</th>
			</tr>';
		
			//CSV
			$csv_rows['headers'] = array('Player', 'Facility');
			if(in_array('prize', $columns)){
				$csv_rows['headers'][] = 'Individual Money';
			}
			if(in_array('team_prize', $columns)){
				$csv_rows['headers'][] = 'Team Money';
			}
			$csv_rows['headers'][] = 'Money';
			if(in_array('r1_skins', $columns)){
				$csv_rows['headers'][] = (in_array('r2_skins', $columns) ? 'R1 ' : ''). 'Skins';
			}
			if(in_array('r2_skins', $columns)){
				$csv_rows['headers'][] = (in_array('r1_skins', $columns) ? 'R2 ' : ''). 'Skins';
			}
			$csv_rows['headers'][] = 'Total Money';		
		
			//Html
			$count = 0;
			foreach($records as $record){

				//Only display rows with a total > 0
				if($record['total_prize'] > 0 || $record['r1_skins_count'] > 0 || $record['r2_skins_count'] > 0){

					$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
					$html .= '<tr>
						<td style="' .$css_td. '">' .$record['last_name'].', '.$record['first_name']. '</td>
						<td style="' .$css_td. '">' .$record['facility_name']. '</td>
						' .(in_array('prize', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['prize'], 2). '</td>' : ''). '
						' .(in_array('team_prize', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['team_prize'], 2). '</td>' : ''). '
						<td style="' .$css_td. '" align="right">$' .number_format($record['total_prize'], 2). '</td>
						' .(in_array('r1_skins', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['r1_skins_prize'], 2). '</td>' : ''). '
						' .(in_array('r2_skins', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['r2_skins_prize'], 2). '</td>' : ''). '
						<td style="' .$css_td. '" align="right">$' .number_format($record['overall_money'], 2). '</td>
					</tr>';
					$count++;
					
					//CSV
					$csv_data = array(
						$record['last_name']. ', '.$record['first_name'],
						$record['facility_name']
					);
					if(in_array('prize', $columns)){
						$csv_data[] = '$'.number_format($record['prize'], 2);
					}
					if(in_array('team_prize', $columns)){
						$csv_data[] = '$'.number_format($record['team_prize'], 2);
					}
					$csv_data[] = '$'.number_format($record['total_prize'], 2);
					if(in_array('r1_skins', $columns)){
						$csv_data[] = '$'.number_format($record['r1_skins_prize'], 2);
					}
					if(in_array('r2_skins', $columns)){
						$csv_data[] = '$'.number_format($record['r2_skins_prize'], 2);
					}
					$csv_data[] = '$'.number_format($record['overall_money'], 2);
					$csv_rows['rows'][] = $csv_data;
					
				}
			}
		$html .= '</table>';
		if($count == 0){
			$html .= '<p>No records found.</p>';
		}
		
		//Generate csv document to download
		if($format == 'csv'){
			
			//Output csv
			header("Pragma: public");
			header("Expires: 0");
			header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
			header("Content-Type: application/force-download");
			header("Content-Type: text/csv");
			header("Content-Type: application/octet-stream");
			header("Content-Type: application/download");;
			header("Content-Disposition: attachment;filename=".$event['page']."-money.csv");
			header("Content-Transfer-Encoding: binary ");

			$fp = fopen('php://output', 'w');
			
			if($count == 0){
				fputcsv($fp, array('No results found.'));
				
			}else{
				
				fputcsv($fp, array($event['event_name']));
				fputcsv($fp, array((trim($event['facility_name']) != '' ? $event['facility_name'] : 'TBD')));
				fputcsv($fp, array(format_date_range($event['start_date'], $event['end_date'])));
				fputcsv($fp, array('Field Size: '.$field_size));
				fputcsv($fp, array((!empty($event['purse']) && $event['purse'] > 0 ? 'Total Purse: $'.number_format($event['purse'], 2) : '')));
				fputcsv($fp, array(''));
				
				if(!empty($csv_rows)){
					fputcsv($fp, array($event['event_name'].' Money'));
					fputcsv($fp, str_replace("&rsquo;", "'", $csv_rows['headers']));
					fputcsv($fp, array(''));
					foreach($csv_rows['rows'] as $row){
						fputcsv($fp, str_replace("&rsquo;", "'", $row));
					}
					fputcsv($fp, array(''));
				}
			}
			
			fclose($fp);
			
			
		//Generate pdf and send to browser	
		}else{
			$mpdf->WriteHTML($html, 2);
			$mpdf->Output($event['event_name'].' Money.pdf','I');
		}
		
	//Access denied
	}else{
		header('Location: '.$_sitepages['tournaments']['page_url'].$id.'/');
		exit();
	}
	
}

?>