<?php  

if(PAGE_ID == $_sitepages['reg_confirm']['page_id']){

	//If missing session or not posted, redirect
	if(!isset($_SESSION['reg']['checkout']) || empty($_POST)){
		header('Location: '.$_sitepages['reg_checkout']['page_url']);
		exit();
	}
	
	//If missing session cookie, redirect
	if(empty($_POST['xid']) || $_POST['xid'] != $_COOKIE['xid']){
		header('Location: '.$_sitepages['reg_cart']['page_url']);
		exit();
	}
	
	//Define vars
	$panel_id = 63;
	$errors = array();
	$unavailable = array();
	$event_category = NULL;
	$payment = $_SESSION['reg']['checkout']['payment']; //Pay now or pay later
	$promocode = '';
	$has_admin_fee = false;
	
	//Compile checkout cart items
	$discount = 0;
	$subtotal = 0;
	$fees = 0;
	$admin_fee = 0;
	$taxes = 0;
	$regtotal = 0;
	$ordertotal = 0;
	$checkout_cart = array();
	if(isset($_SESSION['reg']['checkout']['cart_items']) && !empty($_SESSION['reg']['checkout']['cart_items'])){
		foreach($_SESSION['reg']['checkout']['cart_items'] as $item_id){
			$item = $ShoppingCart->get_cart_item($item_id);
			if(!empty($item)){
				$checkout_cart[$item_id] = $item;
				$event_category = $item['category_id'];
				$promocode = $item['promocode'];
				
				//Set event type
				if(!defined('EVENT_TYPE')){
					define('EVENT_TYPE', $item['event_type']);
				}
				
				$discount += $item['discount'];
				$subtotal += $item['subtotal'];
				$fees += $item['fees'];
				$taxes += $item['taxes'];
				$ordertotal += $item['total'];
				
				//Check for admin fee (only applies to online credit card payments)
				if($item['admin_fee'] > 0){
					$has_admin_fee = true;
					if($payment){
						if($item['admin_fee_type'] == 'Percent'){
							$admin_fee += ($item['total']*($item['admin_fee']/100));
						}else{
							$admin_fee += $item['admin_fee'];
						}
					}
				}
				
				//Double check event is still available for registration
				$event = $Registration->get_occurrence($item['occurrence_id']);
				if(empty($event) || !$event['reg_available']){
					header('Location: '.$_sitepages['reg_cart']['page_url']);
					exit();
				}
				
				//Event capacity
				if($item['event_type'] != 2 && $event){
					if(!is_null($event['spots_available']) && count($item['attendees']) > $event['spots_available']){
						header('Location: '.$_sitepages['reg_cart']['page_url']);
						exit();
					}
				}
			}
		}
	}
	
	//Must be logged in
	if(!USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.base64_encode($_SERVER['REQUEST_URI']));
		exit();
	}
		
	//If cart is empty, redirect
	if(empty($checkout_cart)){
		header('Location: '.$_sitepages['reg_cart']['page_url']);
		exit();
	}
	
	//Retrive sensitive info
	$ccnumber = (isset($_POST['ccnumber']) ? $_POST['ccnumber'] : '');
	$cvv = (isset($_POST['cvv']) ? $_POST['cvv'] : '');
	if(!empty($_SESSION['reg']['checkout']['billing_id']) && !empty($_SESSION['reg']['checkout']['ccnumber'])){
		$ccnumber = $_SESSION['reg']['checkout']['ccnumber']; //Last 4 digits only
	}
	
	//Current tax rates 
	$taxrates = $ShoppingCart->get_taxes(0, 'AB');
	$gst_rate = $taxrates['gst_rate'];
	$pst_rate = $taxrates['pst_rate'];
	$hst_rate = $taxrates['hst_rate'];
			
	//Process registration
	if(isset($_POST['process'])){
		$success = false;
		$receipt_items = array();	
			
		//Validate Recaptcha
		require_once("includes/plugins/recaptcha/recaptchalib.php");
		$recaptcha_response = NULL;
		$reCaptcha = new ReCaptcha($global['recaptcha_secret']);
		$recaptcha_response = $reCaptcha->verifyResponse(
			$_SERVER["REMOTE_ADDR"],
			$_POST["g-recaptcha-response"]
		);
		if($recaptcha_response != NULL && $recaptcha_response->success){
			//Valid
		}else{
			//Invalid
			$_SESSION['reg']['checkout']['error'] = 'Unable to validate recaptcha';
			header('Location: '.$sitemap[57]['page_url']);
			exit();
		}
		
		//If pay later, insert registrations separately so they can be paid independently
		if(!$payment && $ordertotal > 0){
			
			//Save new billing profile first if necessary
			if($_SESSION['reg']['checkout']['billing_id'] == '' && !empty($ccnumber) && USER_LOGGED_IN){
				
				//Format request
				$request = array(
					'type' => 'profileAddRequest',
					'name' => $_SESSION['reg']['checkout']['ccname'],
					'email' => $_SESSION['reg']['checkout']['email'],
					'phone' => $_SESSION['reg']['checkout']['phone'],
					'bill_address1' => $_SESSION['reg']['checkout']['bill_address1'],
					'bill_address2' => $_SESSION['reg']['checkout']['bill_address2'],
					'bill_city' => $_SESSION['reg']['checkout']['bill_city'],
					'bill_province' => $_SESSION['reg']['checkout']['bill_province'],
					'bill_country' => $_SESSION['reg']['checkout']['bill_country'],
					'bill_postalcode' => $_SESSION['reg']['checkout']['bill_postalcode'],
					'exp_month' => $_SESSION['reg']['checkout']['exp_month'],
					'exp_year' => $_SESSION['reg']['checkout']['exp_year'],
					'ref_number' => '',
					'ccnumber' => $ccnumber
				);

				//Process request
				include("includes/orbital/request.php");
				
				//Save to database
				if($trxnResponse['status'] == 1 && !empty($trxnResponse['ref_number'])){
					$params = array(
						USER_LOGGED_IN,
						$_SESSION['reg']['checkout']['bill_address1'],
						$_SESSION['reg']['checkout']['bill_address2'],
						$_SESSION['reg']['checkout']['bill_city'],
						$_SESSION['reg']['checkout']['bill_province'],
						$_SESSION['reg']['checkout']['bill_postalcode'],
						$_SESSION['reg']['checkout']['bill_country'],
						$_SESSION['reg']['checkout']['ccname'],
						$_SESSION['reg']['checkout']['cctype'],
						substr($ccnumber, -4, 4),
						$_SESSION['reg']['checkout']['exp_month'].$_SESSION['reg']['checkout']['exp_year'],
						date("Y-m-d H:i:s"),
						date("Y-m-d H:i:s"),
						$trxnResponse['ref_number']
					);
					$query = $db->query("INSERT INTO `account_billing_profiles`(`account_id`, `bill_address1`, `bill_address2`, `bill_city`, `bill_province`, `bill_postalcode`, `bill_country`, `ccname`, `cctype`, `ccnumber`, `ccexpiry`, `date_added`, `last_updated`, `ref_number`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
					if($query && !$db->error()){
						$_SESSION['reg']['checkout']['billing_id'] = $db->insert_id();
					}
				}
			}
			
			//Insert registrations
			foreach($checkout_cart as $item_id=>$item){
				
				$params = array();
				$params[] = array('param' => 'first_name', 'value' => $_SESSION['reg']['checkout']['first_name'], 'required' => true);
				$params[] = array('param' => 'last_name', 'value' => $_SESSION['reg']['checkout']['last_name'], 'required' => true);
				$params[] = array('param' => 'email', 'value' => $_SESSION['reg']['checkout']['email'], 'required' => true);
				$params[] = array('param' => 'phone', 'value' => $_SESSION['reg']['checkout']['phone'], 'required' => true);
				$params[] = array('param' => 'company', 'value' => $_SESSION['reg']['checkout']['company'], 'required' => false);
				$params[] = array('param' => 'facility', 'value' => $_SESSION['reg']['checkout']['facility'], 'required' => false);
				$params[] = array('param' => 'address1', 'value' => $_SESSION['reg']['checkout']['address1'], 'required' => false);
				$params[] = array('param' => 'address2', 'value' => $_SESSION['reg']['checkout']['address2'], 'required' => false);
				$params[] = array('param' => 'city', 'value' => $_SESSION['reg']['checkout']['city'], 'required' => false);
				$params[] = array('param' => 'province', 'value' => $_SESSION['reg']['checkout']['province'], 'required' => false);
				$params[] = array('param' => 'postal_code', 'value' => $_SESSION['reg']['checkout']['postal_code'], 'required' => false);
				$params[] = array('param' => 'country', 'value' => $_SESSION['reg']['checkout']['country'], 'required' => false);
				$params[] = array('param' => 'fees', 'value' => $item['fees'], 'required' => false);
				$params[] = array('param' => 'taxes', 'value' => $item['taxes'], 'required' => false);
				$params[] = array('param' => 'gst_rate', 'value' => $gst_rate, 'required' => false);
				$params[] = array('param' => 'pst_rate', 'value' => $pst_rate, 'required' => false);
				$params[] = array('param' => 'hst_rate', 'value' => $hst_rate, 'required' => false);
				$params[] = array('param' => 'discount', 'value' => $item['discount'], 'required' => false);
				$params[] = array('param' => 'promocode', 'value' => $item['promocode'], 'required' => false);
				$params[] = array('param' => 'registration_total', 'value' => $item['total'], 'required' => false);
				$params[] = array('param' => 'account_id', 'value' => (USER_LOGGED_IN ? USER_LOGGED_IN : NULL), 'required' => false);
				$params[] = array('param' => 'billing_id', 'value' => $_SESSION['reg']['checkout']['billing_id'], 'required' => false); //Save billing profile id for processing later
								
				try{
					$reg_id = $ShoppingCart->insert_registration($params, array($item_id));
					$reg_num = 'PGA'.str_pad($reg_id, 5, '0', STR_PAD_LEFT).'-0'.$item['event_type'];
					
					$response = array();
					$response[] = array('param' => 'status', 'value' => '1', 'required' => true);
					$response[] = array('param' => 'paid', 'value' => '0', 'required' => true);
					$response[] = array('param' => 'registration_number', 'value' => $reg_num, 'required' => false);
					
					//Update registration with response
					try{
						$ShoppingCart->update_registration($reg_id, $response, 'Registered');
						$success = true;
						
						$receipt_item = $item;
						$receipt_item['registration_number'] = $reg_num;
						$receipt_items[] = $receipt_item;
						
						//Remove attendees from waiting lists
						foreach($item['attendees'] as $attendee){
							if(!empty($attendee['account_id'])){
								try{
									$Registration->wait_list_unsubscribe($item['occurrence_id'], $attendee['account_id']);
								}catch(Exception $e){
									$errors[] = $e->getMessage();
								}
							}
						}	
						
						//Remove registrant from waiting list (for events)
						if(USER_LOGGED_IN){
							try{
								$Registration->wait_list_unsubscribe($item['occurrence_id'], USER_LOGGED_IN);
							}catch(Exception $e){
								$errors[] = $e->getMessage();
							}
						}

					}catch(Exception $e){
						$errors[] = $e->getMessage();
					}
					
				//Failed to insert order
				}catch(Exception $e){
					$errors[] = $e->getMessage();
				}
				
			}
			
		
		//If pay now, insert one transaction all together
		}else{
			
			//Insert registration
			$params = array();
			$params[] = array('param' => 'first_name', 'value' => $_SESSION['reg']['checkout']['first_name'], 'required' => true);
			$params[] = array('param' => 'last_name', 'value' => $_SESSION['reg']['checkout']['last_name'], 'required' => true);
			$params[] = array('param' => 'email', 'value' => $_SESSION['reg']['checkout']['email'], 'required' => true);
			$params[] = array('param' => 'phone', 'value' => $_SESSION['reg']['checkout']['phone'], 'required' => true);
			$params[] = array('param' => 'company', 'value' => $_SESSION['reg']['checkout']['company'], 'required' => false);
			$params[] = array('param' => 'facility', 'value' => $_SESSION['reg']['checkout']['facility'], 'required' => false);
			$params[] = array('param' => 'address1', 'value' => $_SESSION['reg']['checkout']['address1'], 'required' => false);
			$params[] = array('param' => 'address2', 'value' => $_SESSION['reg']['checkout']['address2'], 'required' => false);
			$params[] = array('param' => 'city', 'value' => $_SESSION['reg']['checkout']['city'], 'required' => false);
			$params[] = array('param' => 'province', 'value' => $_SESSION['reg']['checkout']['province'], 'required' => false);
			$params[] = array('param' => 'postal_code', 'value' => $_SESSION['reg']['checkout']['postal_code'], 'required' => false);
			$params[] = array('param' => 'country', 'value' => $_SESSION['reg']['checkout']['country'], 'required' => false);
			$params[] = array('param' => 'fees', 'value' => $fees, 'required' => false);
			$params[] = array('param' => 'taxes', 'value' => $taxes, 'required' => false);
			$params[] = array('param' => 'gst_rate', 'value' => $gst_rate, 'required' => false);
			$params[] = array('param' => 'pst_rate', 'value' => $pst_rate, 'required' => false);
			$params[] = array('param' => 'hst_rate', 'value' => $hst_rate, 'required' => false);
			$params[] = array('param' => 'discount', 'value' => $discount, 'required' => false);
			$params[] = array('param' => 'promocode', 'value' => $promocode, 'required' => false);
			$params[] = array('param' => 'registration_total', 'value' => $ordertotal, 'required' => false);
			$params[] = array('param' => 'account_id', 'value' => (USER_LOGGED_IN ? USER_LOGGED_IN : NULL), 'required' => false);
			$params[] = array('param' => 'billing_id', 'value' => $_SESSION['reg']['checkout']['billing_id'], 'required' => false);
					
			try{
				$reg_id = $ShoppingCart->insert_registration($params, $_SESSION['reg']['checkout']['cart_items']);
				$reg_num = 'PGA'.str_pad($reg_id, 5, '0', STR_PAD_LEFT).'-0'.EVENT_TYPE;

				//Payment required
				if($ordertotal > 0){
					
					//Insert payment
					$params = array();
					$params[] = array('param' => 'bill_address1', 'value' => $_SESSION['reg']['checkout']['bill_address1'], 'required' => true);
					$params[] = array('param' => 'bill_address2', 'value' => $_SESSION['reg']['checkout']['bill_address2'], 'required' => false);
					$params[] = array('param' => 'bill_city', 'value' => $_SESSION['reg']['checkout']['bill_city'], 'required' => true);
					$params[] = array('param' => 'bill_province', 'value' => $_SESSION['reg']['checkout']['bill_province'], 'required' => true);
					$params[] = array('param' => 'bill_postalcode', 'value' => $_SESSION['reg']['checkout']['bill_postalcode'], 'required' => true);
					$params[] = array('param' => 'bill_country', 'value' => $_SESSION['reg']['checkout']['bill_country'], 'required' => true);
					$params[] = array('param' => 'ccname', 'value' => $_SESSION['reg']['checkout']['ccname'], 'required' => true);
					$params[] = array('param' => 'cctype', 'value' => $_SESSION['reg']['checkout']['cctype'], 'required' => true);
					$params[] = array('param' => 'ccnumber', 'value' => substr($ccnumber, -4, 4), 'required' => true);
					$params[] = array('param' => 'ccexpiry', 'value' => $_SESSION['reg']['checkout']['exp_month'].$_SESSION['reg']['checkout']['exp_year'], 'required' => true);
					$params[] = array('param' => 'admin_fee', 'value' => $admin_fee, 'required' => false);
					$params[] = array('param' => 'amount', 'value' => $ordertotal, 'required' => true);
					$params[] = array('param' => 'processed_by', 'value' => (USER_LOGGED_IN ? USER_LOGGED_IN : NULL), 'required' => false);
					
					try{
						$order_id = $ShoppingCart->insert_registration_payment($reg_id, $params);
						$ordernum = 'A'.date("ymd").'-'.str_pad($order_id, 5, '0', STR_PAD_LEFT);
						
						//Format request using billing profile
						if(!empty($_SESSION['reg']['checkout']['billing_id'])){
							$request = array(
								'type' => 'newOrderRequest',
								'ordernum' => $ordernum,
								'ordertotal' => ($ordertotal+$admin_fee),
								'taxes' => $taxes,
								'ref_number' => $_SESSION['reg']['checkout']['ref_number'],
								'ccsave' => 0
							);
							
						//Format request with new billing data
						}else{
							$request = array(
								'type' => 'newOrderRequest',
								'name' => $_SESSION['reg']['checkout']['first_name'].' '.$_SESSION['reg']['checkout']['last_name'],
								'email' => $_SESSION['reg']['checkout']['email'],
								'phone' => $_SESSION['reg']['checkout']['phone'],
								'bill_address1' => $_SESSION['reg']['checkout']['bill_address1'],
								'bill_address2' => $_SESSION['reg']['checkout']['bill_address2'],
								'bill_city' => $_SESSION['reg']['checkout']['bill_city'],
								'bill_province' => $_SESSION['reg']['checkout']['bill_province'],
								'bill_postalcode' => $_SESSION['reg']['checkout']['bill_postalcode'],
								'bill_country' => $_SESSION['reg']['checkout']['bill_country'],
								'ccnumber' => $ccnumber,
								'exp_month' => $_SESSION['reg']['checkout']['exp_month'],
								'exp_year' => $_SESSION['reg']['checkout']['exp_year'],
								'cvv' => $cvv,
								'ordernum' => $ordernum,
								'ordertotal' => ($ordertotal+$admin_fee),
								'taxes' => $taxes,
								'ccsave' => $_SESSION['reg']['checkout']['ccsave']
							);
						}
						
						//Send request
						include("includes/orbital/request.php");
						
						//Success response
						if($trxnResponse['status'] == 1 && $trxnResponse['approved'] == 1){
							$success = true;
							$status = '1';
							$paid = '1';
						}else{
							$success = false;
							$status = '0';
							$paid = '0';
							$errors[] = $trxnResponse['message'];
						}
						
						//Update payment response
						$response = array();
						$response[] = array('param' => 'status', 'value' => $status, 'required' => true);
						$response[] = array('param' => 'response_code', 'value' => $trxnResponse['response_code'], 'required' => false);
						$response[] = array('param' => 'txn_num', 'value' => $trxnResponse['txn_num'], 'required' => false);
						$response[] = array('param' => 'txn_tag', 'value' => $trxnResponse['txn_tag'], 'required' => false);
						$response[] = array('param' => 'auth_code', 'value' => $trxnResponse['auth_code'], 'required' => false);
						$response[] = array('param' => 'cvd_code', 'value' => $trxnResponse['cvd_code'], 'required' => false);
						$response[] = array('param' => 'message', 'value' => $trxnResponse['message'], 'required' => false);
						$response[] = array('param' => 'payment_number', 'value' => $ordernum, 'required' => false);
						try{
							$ShoppingCart->update_registration_payment($order_id, $response);
						}catch(Exception $e){
							$errors[] = $e->getMessage();
						}
						
					//Failed to insert payment	
					}catch(Exception $e){
						$errors[] = $e->getMessage();
					}

				//No payment required
				}else{
					$success = true;
					$status = '1';
					$paid = '1';
				}
				
				//Update registration with response
				$response = array();
				$response[] = array('param' => 'status', 'value' => $status, 'required' => true);
				$response[] = array('param' => 'paid', 'value' => $paid, 'required' => true);
				$response[] = array('param' => 'registration_number', 'value' => $reg_num, 'required' => false);
				try{
					$ShoppingCart->update_registration($reg_id, $response, ($status == '1' ? 'Registered' : NULL));
				}catch(Exception $e){
					$errors[] = $e->getMessage();
				}
				
				//Successful
				if($success){
					foreach($checkout_cart as $item_id=>$item){
						
						//Compile rows for receipt
						$receipt_item = $item;
						$receipt_item['registration_number'] = $reg_num;
						$receipt_items[] = $receipt_item;
						
						//Remove attendees from waiting lists
						foreach($item['attendees'] as $attendee){
							if(!empty($attendee['account_id'])){
								try{
									$Registration->wait_list_unsubscribe($item['occurrence_id'], $attendee['account_id']);
								}catch(Exception $e){
									$errors[] = $e->getMessage();
								}
							}
						}	
						
						//Remove registrant from waiting list (for events)
						if(USER_LOGGED_IN){
							try{
								$Registration->wait_list_unsubscribe($item['occurrence_id'], USER_LOGGED_IN);
							}catch(Exception $e){
								$errors[] = $e->getMessage();
							}
						}
					}
					
					//Save billing profile to database if necessary
					if($_SESSION['reg']['checkout']['ccsave'] && !empty($ccnumber) && USER_LOGGED_IN){
						$params = array(
							USER_LOGGED_IN,
							$_SESSION['reg']['checkout']['bill_address1'],
							$_SESSION['reg']['checkout']['bill_address2'],
							$_SESSION['reg']['checkout']['bill_city'],
							$_SESSION['reg']['checkout']['bill_province'],
							$_SESSION['reg']['checkout']['bill_postalcode'],
							$_SESSION['reg']['checkout']['bill_country'],
							$_SESSION['reg']['checkout']['ccname'],
							$_SESSION['reg']['checkout']['cctype'],
							substr($ccnumber, -4, 4),
							$_SESSION['reg']['checkout']['exp_month'].$_SESSION['reg']['checkout']['exp_year'],
							date("Y-m-d H:i:s"),
							date("Y-m-d H:i:s")
						);

						//Profile was already created with order processing
						if(isset($trxnResponse['ref_number']) && !empty($trxnResponse['ref_number'])){
							$params[] = $trxnResponse['ref_number'];

						//Profile has not been created so send the request now	
						}else{

							//Format request
							$request = array(
								'type' => 'profileAddRequest',
								'name' => $_SESSION['reg']['checkout']['ccname'],
								'email' => $_SESSION['reg']['checkout']['email'],
								'phone' => $_SESSION['reg']['checkout']['phone'],
								'bill_address1' => $_SESSION['reg']['checkout']['bill_address1'],
								'bill_address2' => $_SESSION['reg']['checkout']['bill_address2'],
								'bill_city' => $_SESSION['reg']['checkout']['bill_city'],
								'bill_province' => $_SESSION['reg']['checkout']['bill_province'],
								'bill_country' => $_SESSION['reg']['checkout']['bill_country'],
								'bill_postalcode' => $_SESSION['reg']['checkout']['bill_postalcode'],
								'ccnumber' => $ccnumber,
								'exp_month' => $_SESSION['reg']['checkout']['exp_month'],
								'exp_year' => $_SESSION['reg']['checkout']['exp_year'],
								'ref_number' => ''
							);

							//Process request
							include("includes/orbital/request.php");
							if($trxnResponse['status'] == 1){
								$params[] = $trxnResponse['ref_number'];
							}
						}

						//Save to database
						if(!empty($trxnResponse['ref_number'])){
							$query = $db->query("INSERT INTO `account_billing_profiles`(`account_id`, `bill_address1`, `bill_address2`, `bill_city`, `bill_province`, `bill_postalcode`, `bill_country`, `ccname`, `cctype`, `ccnumber`, `ccexpiry`, `date_added`, `last_updated`, `ref_number`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
							if(!$query || $db->error()){
								$errors[] = 'Error inserting billing profile: '.$db->error();
							}
						}
					}
				}
				
			//Failed to insert order
			}catch(Exception $e){
				$errors[] = $e->getMessage();
			}

		}
			
		//Registration was successful
		if($success){

			//Format receipt data
			$order_data = $_SESSION['reg']['checkout'];
			$order_data['registration_date'] = date('Y-m-d H:i:s');
			$order_data['discount'] = $discount;
			$order_data['promocode'] = $promocode;
			$order_data['fees'] = $fees;
			$order_data['taxes'] = $taxes;
			$order_data['admin_fee'] = $admin_fee;
			$order_data['registration_total'] = ($ordertotal+$admin_fee);
			$order_data['payment_number'] = (isset($ordernum) ? $ordernum : '');
			$order_data['ccnumber'] = (!empty($ccnumber) ? substr($ccnumber, -4, 4) : '');
			$order_data['ccexpiry'] = $order_data['exp_month'].$order_data['exp_year'];
			$order_data['response_code'] = (isset($trxnResponse['response_code']) ? $trxnResponse['response_code'] : '');
			$order_data['txn_num'] = (isset($trxnResponse['txn_num']) ? $trxnResponse['txn_num'] : '');
			$order_data['auth_code'] = (isset($trxnResponse['auth_code']) ? $trxnResponse['auth_code'] : '');
			$order_data['message'] = (isset($trxnResponse['message']) ? $trxnResponse['message'] : '');

			//Payment notice
			$receiptmsg = '';
			if(EVENT_TYPE != 2 && !$payment){
				if(!$has_admin_fee && $ordertotal > $reg_settings['max_payment_amount'] && !empty($reg_settings['max_payment_amount'])){
					$receiptmsg = '<p>Please send payment by cheque to the PGA of Alberta Office, ' .$global['full_address']. '. Cheques are to be made payable to the PGA of Alberta. If you require assistance making a payment, please contact our office.</p><p>To view and manage your registrations, <a href="' .$siteurl.$_sitepages['my_registrations']['page_url']. '">login to your account</a>.</p>';
				}else{
					$receiptmsg = '<p>Please submit your payment before ' .date('F j, Y', strtotime($event['payment_deadline'])). ' to secure your spot. If you require assistance making a payment, please contact our office.</p><p>To view and manage your registrations, <a href="' .$siteurl.$_sitepages['my_registrations']['page_url']. '">login to your account</a>.</p>';
				}
			}
			
			//Send receipt
			$receipt = $Registration->registration_receipt($order_data, $receipt_items, $receiptmsg);
			$_SESSION['purchase_receipt'] = $receipt;
			send_email($_SESSION['reg']['checkout']['email'], 'Registration Confirmation', $receipt);
			
			//Send admin email
			$emailtype = (EVENT_TYPE == 2 ? 'tournaments' : ($event_category == 3 ? 'conferences' : 'events'));
			$admin_email = (trim($reg_settings['email_'.$emailtype]) != '' ? $reg_settings['email_'.$emailtype] : $global['contact_email']);
			send_email($admin_email, 'Registration Confirmation', $receipt);	
			
			//Send notification to attendees
			foreach($checkout_cart as $item){
				
				//Event attendee notification
				if($item['event_type'] == 1){
					foreach($item['attendees'] as $attendee){
						if(isset($attendee['attendee_fields']['email']) && trim($attendee['attendee_fields']['email']) != '' && $attendee['attendee_fields']['email'] != $_SESSION['reg']['checkout']['email']){
							$to = $attendee['attendee_fields']['email'];
							$message = '<h3>Registration Confirmation</h3>
							<p>This email is to inform you that <strong>' .$_SESSION['reg']['checkout']['first_name'].' '.$_SESSION['reg']['checkout']['last_name'].'</strong> has registered you for the following event:</p>
							<p><strong>' .$item['event_name']. ' on ' .format_date_range($item['start_date'], $item['end_date']). '.</strong></p>
							<p>If you have any questions or concerns regarding this email, please contact us.</p>';
							send_email($to, 'Registration Confirmation', $message);
						}
					}
				}
				
				//Partner notification
				if($item['event_type'] == 2){
					if(isset($item['attendees'][0]['partner']['email']) && $item['attendees'][0]['partner']['email'] != ''){
						$to = $item['attendees'][0]['partner']['email'];
						$message = '<h3>Registration Confirmation</h3>
						<p>This email is to inform you that <strong>' .$_SESSION['reg']['checkout']['first_name'].' '.$_SESSION['reg']['checkout']['last_name'].'</strong> has registered you as his/her partner for the following tournament:</p>
						<p><strong>' .$item['event_name']. ' on ' .format_date_range($item['start_date'], $item['end_date']). '.</strong></p>
						<p>To view and manage your registrations, login to your account by clicking the link below:</p>
						<p><a href="' .$_sitepages['account']['page_url']. '" class="button" target="_blank">User Login</a></p>';
						send_email($to, 'Registration Confirmation', $message);
					}
				}
			}			
			
			//Update promo code count
			if($discount > 0 && $promocode != ''){
				$ShoppingCart->update_promo_count($promocode);
			}

			//Clear sessions
			unset($ccnumber);
			unset($cvv);
			unset($_SESSION['reg']['checkout']);
			foreach($checkout_cart as $item_id=>$item){
				$cart = $ShoppingCart->delete_cart_item($item_id);
			}
			
			//Redirect to success 
			if(EVENT_TYPE != 2 && !$payment){
				if(!$has_admin_fee && $ordertotal > $reg_settings['max_payment_amount'] && !empty($reg_settings['max_payment_amount'])){
					$_SESSION['reg']['checkout']['success'] = 'Please send payment by cheque to the PGA of Alberta Office, ' .$global['full_address']. '. Cheques are to be made payable to the PGA of Alberta. If you require assistance making a payment, please <a href="' .$_sitepages['contact']['page_url']. '">contact our office</a>.';
				}else{
					$_SESSION['reg']['checkout']['success'] = 'Please submit your payment before ' .date('F j, Y', strtotime($event['payment_deadline'])). ' to secure your spot. If you require assistance making a payment, please <a href="' .$_sitepages['contact']['page_url']. '">contact our office</a>.';
				}
			}
			header('Location: '.$sitemap[56]['page_url']);
			exit();
			
			
		//Redirect to error	
		}else{
			$_SESSION['reg']['checkout']['error'] = implode('<br />', $errors);
			header('Location: '.$sitemap[57]['page_url']);
			exit();
		}
		
	}
	
}

?>