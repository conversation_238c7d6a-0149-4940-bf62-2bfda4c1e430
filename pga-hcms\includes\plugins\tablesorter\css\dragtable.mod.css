/*
 * dragtable
 * @Version 2.0.14 MOD
 * default css
 */
.dragtable-sortable {
	list-style-type: none;
	margin: 0;
	padding: 0;
	-moz-user-select: none;
	z-index: 10;
}
.dragtable-sortable li {
	margin: 0;
	padding: 0;
	float: left;
	font-size: 1em;
}
.dragtable-sortable table {
	margin-top: 0;
}
.dragtable-sortable th, .dragtable-sortable td {
	border-left: 0px;
}
.dragtable-sortable li:first-child th, .dragtable-sortable li:first-child td {
	border-left: 1px solid #CCC;
}
.dragtable-handle-selected {
	/* table-handle class while actively dragging a column */
}
.ui-sortable-helper {
	opacity: 0.7;
	filter: alpha(opacity=70);
}
.ui-sortable-placeholder {
	-moz-box-shadow: 4px 5px 4px rgba(0,0,0,0.2) inset;
	-webkit-box-shadow: 4px 5px 4px rgba(0,0,0,0.2) inset;
	box-shadow: 4px 5px 4px rgba(0,0,0,0.2) inset;
	border-bottom: 1px solid rgba(0,0,0,0.2);
	border-top: 1px solid rgba(0,0,0,0.2);
	visibility: visible !important;
	/* change the background color here to match the tablesorter theme */
	background: #EFEFEF;
}
.ui-sortable-placeholder * {
	opacity: 0.0;
	visibility: hidden;
}
.table-handle, .table-handle-disabled {
	/* background-image: url(images/dragtable-handle.png); */
	/* background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAANAQMAAAC5Li2yAAAABlBMVEUAAAAzMzPI8eYgAAAAAnRSTlMAzORBQ6MAAAAOSURBVAjXYwABByyYAQAQWgFBLN2RnwAAAABJRU5ErkJggg=='); */
	background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyIiBoZWlnaHQ9IjEzIj48cmVjdCBzdHlsZT0iZmlsbDojMzMzO2ZpbGwtb3BhY2l0eTouODsiIHdpZHRoPSIxIiBoZWlnaHQ9IjEiIHg9IjEiIHk9IjIiLz4JPHJlY3Qgc3R5bGU9ImZpbGw6IzMzMztmaWxsLW9wYWNpdHk6Ljg7IiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB4PSIxIiB5PSI0Ii8+CTxyZWN0IHN0eWxlPSJmaWxsOiMzMzM7ZmlsbC1vcGFjaXR5Oi44OyIgd2lkdGg9IjEiIGhlaWdodD0iMSIgeD0iMSIgeT0iNiIvPjxyZWN0IHN0eWxlPSJmaWxsOiMzMzM7ZmlsbC1vcGFjaXR5Oi44OyIgd2lkdGg9IjEiIGhlaWdodD0iMSIgeD0iMSIgeT0iOCIvPjxyZWN0IHN0eWxlPSJmaWxsOiMzMzM7ZmlsbC1vcGFjaXR5Oi44OyIgd2lkdGg9IjEiIGhlaWdodD0iMSIgeD0iMSIgeT0iMTAiLz48L3N2Zz4=);
	background-repeat: repeat-x;
	height: 13px;
	margin: 0 1px;
	cursor: move;
}
.table-handle-disabled {
	opacity: 0;
	cursor: not-allowed;
}
.dragtable-sortable table {
	margin-bottom: 0;
}
