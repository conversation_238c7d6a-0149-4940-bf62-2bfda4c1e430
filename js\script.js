// @prepros-append ../theme/js/theme.js
/*------ init ------*/


//Trigger custom event on first and last trigger of a group of resize events
window.addEventListener("resize", debounce(e => $(window).trigger("resize-start"), 200, true));
window.addEventListener("resize", debounce(e => $(window).trigger("resize-end"), 200));

//First user interaction
$(window).on("resize scroll mousemove click focus blur", e => $(window).trigger("interact"));

window.observers = {};

//Don't force caching-busting
$.ajaxSetup({cache: true});

//Detect touch devices (export to window object)
$(function () {
	window.touch = 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;
	$('html').toggleClass('touch', window.touch).toggleClass('no-touch', !window.touch);
});

//Init functions
$(document).ready(function(){
	embedMedia();
	responsiveItems();
	responsiveTables();
	sanitizeHrefs(); //use setTimeout(sanitizeHrefs, 3000); to execute after external widgets load custom html/links
	
	// Add container-lg class to the container of tournament-information element
	$('#tournament-information').closest('.container').addClass('container-lg');
});


/*------ utility functions ------*/


//Get min and max breakpoints for screen size
function getBreakpoint(){
	let window_width = window.innerWidth;
	window_width = (window_width == undefined ? $(window).innerWidth() : window_width);

	let new_minwidth;
	let new_maxwidth;

	if(window_width > 1366){
		new_minwidth = 1366+1;
		new_maxwidth = 1920;
	}else if(window_width > 1024){
		new_minwidth = 1024+1;
		new_maxwidth = 1366;
	}else if(window_width > 768){
		new_minwidth = 768+1;
		new_maxwidth = 1024;
	}else if(window_width > 480){
		new_minwidth = 480+1;
		new_maxwidth = 768;
	}else{
		new_minwidth = 0;
		new_maxwidth = 480;
	}

	return {'minwidth':new_minwidth, 'maxwidth':new_maxwidth};
}

/**
 * Load and evaluate scripts by passing a one or many string identifiers and an optional callback function.
 * Callbacks will either be executed after all scripts have loaded (asynchronously), or if all
 * scripts are already loaded, immediately (synchronously). "callback" is not passed any
 * parameters.  Note that while scripts are requested consecutivley, there is no guarentee they are evaluated
 * as such.
 *
 * Add new plugins to the scripts object with an unique identifier string to lazy load. eg:
 * 	<identifier>: {
 *		exists: <true/false check if plugin exists>
 *		src: <path or URL to script file>
 *	}
 */
function loadScript(types, callback) {
	types = Array.isArray(types) ? types : [types]; //Wrap type in array
	callback = typeof callback == 'function' ? callback : false;

	let count = types.length,
		loaded = 0,

		//Increment iterator and execute callback if it matches total types
		exec = () => callback && ++loaded >= count && callback(),

		//Add scripts to lazy load
		scripts = {
			'recaptcha': {
				exists: !!window.recaptcha,
				src: '//www.google.com/recaptcha/api.js?onload=recaptchaCallback&render=explicit'
				//Note - this script requests and evaluates further asyncronous scripts (window.grecaptcha)
			},

			'savvior': {
				exists: !!window.savvior,
				src: path+'js/savvior.min.js'
			},

			'gmapsapi': {
				exists: !!(window.google && window.google.maps),
				src: '//maps.googleapis.com/maps/api/js?key='+google_api_key
			},

			'jqueryui': {
				exists: !!($ && $.ui),
				src: path+'js/jquery-ui.min.js'
			},

			'navmenu': {
				exists: typeof NavMenu == 'function',
				src: path+'js/navmenu.js'
			},

			'multilevelpushmenu': {
				exists: !!($ && $.fn.multilevelpushmenu),
				src: path+'js/jquery.multilevelpushmenu.min.js'
			},

			'lightgallery': {
				exists: !!($ && $.fn.lightGallery),
				src: path+'core/plugins/light-gallery/js/lightgallery-all.min.js'
			},

			'swiper': {
				exists: !!window.Swiper,
				src: path+'includes/plugins/swiper/swiper-bundle.min.js'
			},

			// TODO: add your ShareThis Property ID
			'sharethis': {
				exists: !!window.__sharethis__,
				src: '//platform-api.sharethis.com/js/sharethis.js#property=REPLACE_ME&source=platform'
			},
		};

	//Loop through scripts
	for (let type, i = 0; i < types.length; i++) {
		type = types[i];

		//Unidentified script
		if (scripts[type] == undefined) {
			console.warn('Lazy Load Script: "'+type+'" is not a valid identifier and will be skipped.');
			count--;

		//Load script
		} else if (!scripts[type].exists) {
			// console.log('Lazy Load Script: loaded "'+type+'" ('+(i+1)+'/'+count+')');
			$.getScript(scripts[type].src, exec);

		//Script already exists
		} else {
			// console.log('Lazy Load Script: "'+type+'" is already loaded ('+(i+1)+'/'+count+')');
			exec();
		}
	}
}

/**
 * Execute a callback whenever targets are observed by IntersectionObserver.  Targets parameter can be anything
 * acceptable by jQuery().  Optionally pass new options to extend and overwrite defaults.
 * Returns the observer object.
 *
 * callback(entries, observer) is passed:
 *  entries  - an array of IntersectionObserverEntrys
 *  observer - IntersectionObserver object
 */
function observe(targets, callback, options) {
	callback = typeof callback == 'function' ? callback : $.noop;
	let observer = new IntersectionObserver(callback, $.extend({rootMargin: '200px', threshold: 0}, options));

	$(targets).each((i, el) => {observer.observe(el); });

	return observer;
}

/**
 * Execute a callback once, when any of the targets intersect.  Params of this function are identical to observe().
 * Returns the observer object
 */
function observeOnce(targets, callback, options) {
	return observe(targets, function (entries, observer) {
		let intersecting = false;
		entries.forEach(function (entry, index) {
			if (entry.isIntersecting) {
				intersecting = true;
			}
		});

		if (intersecting) {
			callback(entries, observer);
			observer.disconnect();
		}
	}, options);
}

/**
 * Execute a callback, once for each target, when it intersects.  Params of this function are identical to observe().
 *
 * callback(entry, index, entries, observer) is passed:
 *  (context is the intersecting element node)
 *  entry    - the intersecting entry.
 *  index    - index of the intersecting entry in the entries array
 *  entries  - an array of IntersectionObserverEntrys
 *  observer - IntersectionObserver object
 * Returns the observer object
 */
function observeEach(targets, callback, options) {
	return observe(targets, function (entries, observer) {
		entries.forEach(function (entry, index) {
			if (entry.isIntersecting) {
				callback.call(entry.target, entry, index, entries, observer);
				observer.unobserve(entry.target);
			}
		});
	}, options);
}

/**
 * Only call a function at the beginning / end of a group of firings.  Returns a function, so
 * use like "$(window).resize(debounce(function(){ ... }));"
 * For further reading, see here: https://css-tricks.com/debouncing-throttling-explained-examples/
 *
 *  callback  - the function to debounce.  Will keep context and arguments intact.
 *  wait 	  - how long, in ms, is considered a gap between separate groups of firings (default 200)
 *  immediate - true/false to execute callback at the beginning/end of a group of firings, respectivey (default false)
 */
function debounce(callback, wait, immediate) {
	let timeout;
	return function() {
		let context = this,
			args    = arguments,
			callNow = immediate && !timeout,
			later   = function() {
				timeout = null;
				!immediate && callback.apply(context, args);
			};

		clearTimeout(timeout);
		timeout = setTimeout(later, wait || 200);
		callNow && callback.apply(context, args);
	};
}

/**
 * Only call a function after all selected images have loaded.  Images parameter can be anything
 * acceptable by jQuery().
 */
function imagesLoaded(images, fn) {
	let $imgs = $(images).find('img').addBack('img'),
		i     = 0,
		exec  = () => {++i >= $imgs.length && fn(); };

	typeof fn == 'function' && $imgs.each(function(index, el) {
		if (this.complete) {
			exec();
		} else {
			this.addEventListener('load', exec);
			this.addEventListener('error', exec);
		}
	});
}

/**
 * Returns a boolean true/false if child is out of bounds of parent. Parameters
 * can be anything acceptable by jQuery().
 */
function outOfBounds(child, parent) {
	let a      = $(parent)[0].getBoundingClientRect(),
		b      = $(child)[0].getBoundingClientRect(),
		left   = Math.round(a.left) > Math.round(b.left),
		right  = Math.round(a.right) < Math.round(b.right),
		top    = Math.round(a.top) > Math.round(b.top),
		bottom = Math.round(a.bottom) < Math.round(b.bottom);
	return left || right || top || bottom;
};

/*------ navigation ------*/

//Main Menu
$(function(){
	observeOnce('#main-navigation', () => loadScript('navmenu', () => {
		new NavMenu('#main-navigation', {
			concatenated: API => API.$el.addClass('concatenated')
		});
	}));
});

// Sticky Nav
$(function(){
	const $nav = $('#page-navbar');
	const leadinH = () => $('.leadin-popup.position-top.type-bar:visible').outerHeight() || 0;

	// Record last scroll position
	window.prevScroll = $(window).scrollTop();
	window.addEventListener('scroll', debounce(e => {
		window.prevScroll = $(window).scrollTop();
	}, 50, true));

	// Init sticky if page is pre-scrolled
	if ($(window).scrollTop() > (150 + leadinH())) {
		$nav.addClass('sticky');
	}

	// Toggle sticky class
	$(window).on('scroll resize sticky-header', function () {
		let navTop    = leadinH(),						// top position of nav when not sticky
			navBottom = $nav.outerHeight() + navTop,	// bottom position of nav when not sticky

			scrollPos  = $(window).scrollTop(),			// viewport scrollY position at top

			up   = scrollPos - window.prevScroll < -50,	// True if minimum scroll of 50px
			down = scrollPos - window.prevScroll > 50,

			desktop = innerWidth > 1024;				// on desktop breakpoint

		// Default actions if below minimum scroll
		let hide  = scrollPos > navBottom && $nav.hasClass('hide'),
			stick = scrollPos > navTop && $nav.hasClass('sticky');

		// Sticky if hidden by default or below the nav, remove at top pos
		stick = (hide || stick) ? scrollPos > navTop : (up && scrollPos > navBottom);
		$nav.toggleClass('sticky', desktop && stick);

		// Apply hide class on 'down', remove it on 'up'
		hide = scrollPos > navBottom ? down || (!up && hide) : false;
		$nav.toggleClass('hide', desktop && hide);
	}).trigger('sticky-header');
});

//Push menu
$(function(){
	let $menuel = $('#mobile-navigation');
	let $menubtn = $('#menu-toggle');
	let $pushobj = $('#page-wrapper');

	if($menuel.length && $menuel.hasClass('push-menu')){

		$menubtn.one('click', function () {
			loadScript('multilevelpushmenu', function () {

				$menuel.multilevelpushmenu({
					containersToPush: [$pushobj],
					wrapperClass: 'mblmenu',
					menuInactiveClass: 'mblmenu_inactive',
					direction: 'rtl',
					collapsed: true,
					fullCollapse: true,
					menuWidth: 300,
					overlapWidth: 0,
					backItemIcon: 'hit-area fas fa-angle-left',
					backText: 'Back',
					groupIcon: 'more-icon fas fa-angle-right',
					swipe: 'desktop',
					preventItemClick: false,
					preventGroupItemClick: true,
					onMenuReady: function (a, b, c) {
						$('div.levelHolderClass').stop(true, true); //expand is prevented if elements are animated
						openMenu();
					}
				});

				//Menu toggle
				$menubtn.bind('click', function(){
					if($(this).hasClass('open')){ openMenu(); }else{ closeMenu(); }
				});
				$('#close-menu').bind('click', function(){ closeMenu(); });

				//Open menu
				function openMenu(){
					$menubtn.removeClass('open').addClass('close');
					$menuel.multilevelpushmenu('expand');
					$pushobj.css({height:window.innerHeight, overflow:'hidden'}).addClass('pushed'); //prevents scrolling of body content
				}

				//Close menu
				function closeMenu(){
					$menuel.multilevelpushmenu('collapse');
					$menubtn.removeClass('close').addClass('open');
					$pushobj.css({height: 'auto'}).delay(200).removeClass('pushed').animate({overflow: 'visible'}, 1, function(){ //allow scrolling again
						$(this).removeAttr('style');
					});
				}

				//Close menu on resize
				if($('html').hasClass('no-touch')){
					$(window).resize(function(){
						if($menuel.multilevelpushmenu('visiblemenus') !== false) {
							$menubtn.removeClass('close').addClass('open');
							$menuel.multilevelpushmenu('redraw');
							$menuel.multilevelpushmenu('collapse');
							$pushobj.removeAttr('style').removeClass('pushed'); //allow scrolling again
						}
					});
				}

			});
		});
	}
});


/*------ responsive elements ------*/


// Responsive embed elements
function responsiveItems() {
	let $content = $('.panel-text');
	$content.find('iframe').not('[src*=wmode]').each((i, el) => {
		el.src += (el.src.indexOf("?") != -1 ? '&' : '?')+'wmode=transparent';
	});
	$content.find('iframe, object, embed').each(function () {
		let $this = $(this),
			w = $this.attr('width'),
			h = $this.attr('height');

		$this.css({
			'aspect-ratio': w && h ? w+' / '+h : '0.6',
			'height': 'auto'
		});
	});
}

//Responsive tables
function responsiveTables(){
	$('#body-content table').not('.noresponsive').each(function(){
		let $tbl = $(this);
		let tblheaders = [];
		$tbl.addClass('responsive');
		$tbl.find('tr').each(function(){
			let thcount=0;
			if($(this).find('th').length){
				$(this).addClass('header-row');
				$(this).find('th').each(function(){
					tblheaders[thcount] = $(this).html();
					thcount++;
				});
			}
			let tdcount=0;
			$(this).find('td').each(function(){
				if(tblheaders[tdcount] != undefined && tblheaders[tdcount] != " " && tblheaders[tdcount] != "&nbsp;"){
					$(this).prepend(`<span class="table-header">${tblheaders[tdcount]}:&nbsp;</span>`);
				}
				tdcount++;
			});
		});
	});
}

// Autoplay video backgrounds
$(function () {
	const play = () => $('.video-bg:visible video, video.video-bg:visible').each((i, video) => video.play());
	$(window).on('resize-end', play);
	play();
});

//Lazy load responsiveBGs
$(function(){
	observeEach('.responsive-bg', entry => {
		let $target = $(entry.target);

		if ($target.closest('#slideshow .slide').index() < 1) {
			$target.addClass('visible');
		}
	});
});

//Lazy Loaded embed media - replace poster images with an iframe
function embedMedia() {
	$('.embed-media').each(function () {
		let $this   = $(this),
			$img    = $this.find('> img');

		$img.length && $(this).append('<span class="play"></span>');
		$img.attr('style') && $this.attr('style', $img.attr('style')) && $img.removeAttr('style');
		$this.css({width: '', height: ''});
	});
}
$(function () {
	$('.embed-media').click(function (e) {
		e.preventDefault();

		let $this   = $(this),
			href    = $this.attr('href'),
			$img    = $this.find('> img'),
			$iframe = $('<iframe></iframe>'),
			src     = href;

		//Detect youtube links and re-format src
		matches = href.match(/^(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/);
		if (matches && matches[1]) {
			src = 'https://www.youtube-nocookie.com/embed/'+matches[1]+'?rel=0&autoplay=1';
		}

		//Detect vimeo links and re-format src
		matches = href.match(/^.*vimeo\.com\/(channels\/[A-z]+\/|groups\/[A-z]+\/videos\/)?([0-9]+)/);
		if (matches && matches[2]) {
			src = 'https://player.vimeo.com/video/'+matches[2]+'?autoplay=1';
		}

		//Replace anchor tag with a generated iframe
		if (src) {
			$iframe.attr({
				width: $img.width() || $this.attr('width') || 560,
				height: $img.height() || $this.attr('height') || 315,
				src: src,
				frameborder: 0,
				allow: "autoplay; encrypted-media; fullscreen",
				allowfullscreen: '',
				style: $this.attr('style'),
			}).insertAfter($this).addClass(this.className);

			$this.remove();
		}
	});
});

/*------ standard ui elements ------*/


//External links
let external_bits = location.host.split('.');
external_bits.shift();
external_bits = external_bits.join('.');

let external = RegExp('^((f|ht)tps?:)?\/\/(?!(([a-z]*\.)?'+external_bits+'))');
$("a:not([target])").each(function(){
	//check to see if external link
	if(external.test($(this).attr('href'))){
		$(this).attr('target','_blank');
	}
});

//Sanitize all external target="_blank" hrefs
function sanitizeHrefs(){
	$('a[target="_blank"]').each(function(){
		let a = $(this);
		if(location.hostname !== this.hostname){
			let originalRel = (this.rel === undefined) ? '' : this.rel.toLowerCase();
			let newRel = originalRel.split(" ");
			if(originalRel.indexOf('noopener') === -1){
				newRel.push('noopener');
			}
			if(originalRel.indexOf('noreferrer') === -1){
				newRel.push('noreferrer');
			}
			a.attr('rel', newRel.join(" ").trim());
		}
	});
}

//Slideshow
$(function(){
	let $slideshow = $('#slideshow');

	if ($slideshow.find('.slide').length > 1) {
		observeOnce($slideshow, () => loadScript('swiper', () => {

			// Animation classes
			const setClasses = swiper => {
				let dir = swiper.previousIndex - swiper.activeIndex < 0 ? 'right' : 'left';
				$slideshow.removeClass('slide-left slide-right').addClass('slide-'+dir);
			};

			//Pause/play videos
			const playVideo = swiper => {
				let $curr   = $(swiper.slides[swiper.activeIndex]),
					currVid = $curr.find('video:visible')[0];

				$(swiper.slides).find('video').not(currVid).each((i, video) => video.pause());
				currVid?.play();
			};

			// Show BGs as slide comes into focus
			const showBGs = swiper => {
				let $curr = $(swiper.slides[swiper.activeIndex]);
				$curr.next().addBack().find('.responsive-bg').addClass('visible');
			};

			//Auto-play if screen is wider than 1024
			const toggleAutoplay = () => window.innerWidth > 1024 ? swiper.autoplay.start() : swiper.autoplay.stop();

			// Default options (maybe overwritten by theme)
			let swiperOptions = {
				loop: true,
				speed: 800,
				autoHeight: true,
				simulateTouch: false,

				fadeEffect: {
					crossFade: true
				},

				autoplay: {
					delay: 10000,
				},

				pagination: {
					el: $slideshow.find('.slideshow-pagination')[0],
					type: 'bullets',
					clickable: true,
				},

				navigation: {
					nextEl: $slideshow.find('.slideshow-button-next')[0],
					prevEl: $slideshow.find('.slideshow-button-prev')[0],
				},

				on: {
					slideChange: swiper => {
						playVideo(swiper);
						setClasses(swiper);
					},
					activeIndexChange: swiper => {
						showBGs(swiper);
					},
					resize: swiper => {
						playVideo(swiper);
					},
				}
			};

			//Init slideshow
			const swiper = new Swiper($slideshow[0], $.extend(true, swiperOptions, window?.extendSlideshowOpts));

			// Start/stop videos on mobile or mouseover
			$slideshow.hover(() => swiper.autoplay.stop(), toggleAutoplay);
			$(window).on('resize-end', toggleAutoplay);
			toggleAutoplay();

			// Load all BGs after user interaction
			$(window).one('interact', () => $slideshow.find('.responsive-bg').addClass('visible'));
		}));
	}
});

//jQuery UI elements
$(function(){

	//Wait to initialize until ui items scroll into view to prevent TBT
	observeOnce('.content-tabs, .content-accordion', function () {
		loadScript('jqueryui', function () {

			//Init tabs
			$('.content-tabs').tabs({
				show: {effect: "fadeIn", duration: 300},
				hide: {effect: "fadeOut", duration: 300},

				// Trigger responsive items
				activate: function () {
					responsiveItems();
				}
			});

			//Init accordion
			$('.content-accordion').accordion({
				header: '.accordion-header',
				active: true,
				collapsible: true,
				animate: 200,
				heightStyle: 'content',
				icons: {
					'header': 'fas fa-chevron-down',
					'activeHeader': 'fas fa-chevron-down rotated',
				},
				create: function(event, ui) {
					//Automatically expand first item, if has the class "expanded"
					if($(this).hasClass('expanded')) {
						$(this).accordion('option', 'active', 0);
					}
				}
			});

		});
	});
});

//Lazy Loaded images
$(function () {
	window.lazyLoaderIO = observeEach('.lazy-load', function (entry, index, entries, observer) {
		let image  = entry.target,
			$image = $(image),
			isImg  = $image.is('img'),
			oldSrc = isImg ? image.src : $image.css('background-image').slice(4, -1).replace(/["']/g, ""),
			newSrc = $image.data('src');

		oldSrc = oldSrc.replace(window.location.origin, '');

		if(newSrc && oldSrc != newSrc){
			if(isImg){
				image.src = newSrc;
			}else{
				$image.css('background-image', 'url("'+newSrc+'")');
			}

			$image.removeData('src').addClass('loaded');
		}
	});
});

// Responsive tabs
$(function () {

	// Toggle responsive class when tabs overflow containers
	const RO = new ResizeObserver(entries => {
		for (let entry of entries) {
			let $this    = $(entry.target),
				$tabs    = $this.children('li').toArray(),
				overflow = $tabs.reduce((bool, el) => bool || outOfBounds(el, $this), false);

			$this.closest('.content-tabs').toggleClass('responsive-tabs', overflow);
		}
	});

	// Create select box for selecting tab panels
	$('.content-tabs').each(function(){
		let $this   = $(this),
			$select = $(`<select/>`).addClass('select tabs-select'),
			options;

		// Create options HTML for each tab
		options = $this.find('.tabs-nav-wrapper a').toArray().reduce((str, el, i) => {
			return str+`<option value="${i}">${el.innerText}</option>`;
		}, '');

		// Bind select change to tab change
		$select.html(options).change(function () {
			$this.tabs('option', 'active', $(this).val());
		});

		// Update select on tabs change
		$this.on('tabsbeforeactivate', (e, ui) => $select.val(ui.newTab.index()));

		// Attach select
		$this.find('.tabs-nav-wrapper').append($select);

		// Observe tabs nav
		RO.observe($this.find('ul.tabs-nav')[0]);
	});

	// $('.content-tabs .tabs-nav').css('background','#16212F');

});

//Load CSS animations elements as they come into view
$(function () {
	let animate = el => $(el).addClass('animated');
	window.animatorIO = observeEach('.animate', function (entry, index, entries, observer) {
		let data  = $(entry.target).data(),
			delay = data.delay || 200;

		animate(entry.target).find(data.animate).addClass('animate').each(function (i, child) {
			setTimeout(() => {animate(child); }, delay*i);
		});
	}, {rootMargin: '-100px'});
});

//Lightbox
$(function(){
	observeOnce('.light-video, .light-gallery, .light-iframe', function () {
		loadScript('lightgallery', function () {
			let lGVideoOpts = {counter: false, speed: 800, videoMaxWidth: '1280px', youtubePlayerParams: { modestbranding: 1, showinfo: 0, controls: 0 }};
			$('.light-video').lightGallery(lGVideoOpts);
			$('.light-gallery').lightGallery({selector: '.gal-item', exThumbImage: 'data-exthumbimage'});
			$('.light-iframe').lightGallery({selector: 'this', iframeMaxWidth: '90%'});
		});
	});
});

// Gallery images
$(function(){
	observeOnce('.gallery-listings .light-gallery', () => {
		loadScript(['savvior', 'lightgallery'], () => {
			window.addEventListener('savvior:redraw', () => {
				$('.light-gallery').lightGallery({selector: '.gal-item'});
			});

			// Init plugin
			savvior.init('.gallery-listings .light-gallery', {
				"screen and (max-width: 480px)":  { columns: 2 },
				"screen and (min-width: 481px) and (max-width: 769px)":  { columns: 3 },
				"screen and (min-width: 769px) and (max-width: 1024px)": { columns: 4 },
				"screen and (min-width: 1025px)": { columns: 5 }
			});
		});
	});
});

//Lazy load responsiveBGs
$(function(){
	observeEach('.responsive-bg', entry => {
		let $target = $(entry.target);

		if ($target.closest('#slideshow .slide').index() < 1) {
			$target.addClass('visible');
		}
	});
});

//Lazy Loaded embed media - replace poster images with an iframe
function embedMedia() {
	$('.embed-media').each(function () {
		let $this   = $(this),
			$img    = $this.find('> img');

		$img.length && $(this).append('<span class="play"></span>');
		$img.attr('style') && $this.attr('style', $img.attr('style')) && $img.removeAttr('style');
		$this.css({width: '', height: ''});
	});
}
$(function () {
	$('.embed-media').click(function (e) {
		e.preventDefault();

		let $this   = $(this),
			href    = $this.attr('href'),
			$img    = $this.find('> img'),
			$iframe = $('<iframe></iframe>'),
			src     = href;

		//Detect youtube links and re-format src
		matches = href.match(/^(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/);
		if (matches && matches[1]) {
			src = 'https://www.youtube-nocookie.com/embed/'+matches[1]+'?rel=0&autoplay=1';
		}

		//Detect vimeo links and re-format src
		matches = href.match(/^.*vimeo\.com\/(channels\/[A-z]+\/|groups\/[A-z]+\/videos\/)?([0-9]+)/);
		if (matches && matches[2]) {
			src = 'https://player.vimeo.com/video/'+matches[2]+'?autoplay=1';
		}

		//Replace anchor tag with a generated iframe
		if (src) {
			$iframe.attr({
				width: $img.width() || $this.attr('width') || 560,
				height: $img.height() || $this.attr('height') || 315,
				src: src,
				frameborder: 0,
				allow: "autoplay; encrypted-media; fullscreen",
				allowfullscreen: '',
				style: $this.attr('style'),
			}).insertAfter($this).addClass(this.className);

			$this.remove();
		}
	});
});

/*------ forms ------*/

//Google Recaptcha
$(function(){

	//Forms that aren't a child of a dialog
	let $forms = $('form[data-recaptcha]').filter(function () {
		return !$(this).closest('.hidden-modal, .ui-dialog').length;
	});

	//When forms in body content scroll into view
	let observer = observeOnce($forms, function(entries, observer) {
		loadScript('recaptcha');
	});

	//When modals with forms are opened
	$(document).on('dialogopen', function (e) {
		$(e.target).dialog('widget').find('form[data-recaptcha]').length && loadScript('recaptcha');
		observer.disconnect(); //cancel observer
	});
});

recaptchaCallback = function(){
	$('.g-recaptcha').each(function(index, el) {
		let el_id = $(el).attr('id');

		let grecaptcha_id = grecaptcha.render(el_id, {
			'sitekey': recaptcha_sitekey,
			'callback': function(response) {
				let parent_form = $('form[data-recaptcha="#'+el_id+'"]');
				parent_form.find('input[name="g-recaptcha-response"]').val(response);
				parent_form.submit(); //trigger post form again
			}
		});
		$(el).data('id', grecaptcha_id);
	});
};

//Validate email
function checkmail(email) {
	let email_regex = /^[^\s()<>@,;:\"\/\[\]?=]+@\w[\w-]*(\.\w[\w-]*)*\.[a-z]{2,}$/i;
	return email_regex.test(email);
}

//Form submission
function submitForm(form, ajaxFile, callback) {
	let $form           = $(form),
		$submit         = $form.find('[name="submitform"]'),
		$recaptchaField = $($form.data('recaptcha')),
		$recaptchaModal = $recaptchaField.closest('.recaptcha-modal'),
		validated       = true,
		postAjax        = false,
		errormsg        = '';

		console.log('form data ===- '+ $form.serialize());

	//Validate fields
	$errors = $form.find('.jsvalidate').filter((i, el) => $(el).val().trim() == '');
	if ($errors.length) {
		$errors.addClass('error');
		validated = false;
		errormsg += 'Please fill out all the required fields.<br />';
	}

	//Validate email fields
	$errors = $form.find('input[type="email"]').filter((i, el) => $(el).val().trim() != '' && !checkmail($(el).val()));
	if ($errors.length) {
		$errors.addClass('error');
		validated = false;
		errormsg += 'Please enter a valid email address.<br />';
	}

	//Form validated
	if(validated) {

		//Form requires recaptcha validation
		if($recaptchaField.length > 0) {
			if(grecaptcha.getResponse($recaptchaField.data('id')) != '') {
				postAjax = true;

			//Recaptcha not yet validated - open dialog box that has recaptcha
			} else {
				$recaptchaModal.trigger('modal-open');
			}

		//No recaptcha required
		} else {
			postAjax = true;
		}

		if(postAjax) {
			if(ajaxFile != '') {
				//Disable submit button temporarily to prevent double submissions
				$submit.attr('disabled', true).addClass('loading');

				//Close recaptcha dialog box if it was open
				$recaptchaField.length > 0 && $recaptchaModal.dialog('close');

				$.post(path + ajaxFile, $form.serialize(), function(result) {

					//No errors - display success alert
					if(result.errors < 1) {
						$form[0].reset();
						$form.find(':input.error').removeClass('error');

						if(!$form.hasClass('leadin-form')){
							dialogAlert('Success!', result['msg_validation'], 'success '+$form.attr('id')+'-success');
						}

					//Has errors - display error alert
					} else {
						$(result.error_fields.map(fieldname => ':input[name="'+fieldname+'"]').join(',')).addClass('error');
						dialogAlert('Error!', result['msg_validation'], 'error');
					}

					//Re-enable submit button
					$submit.removeAttr('disabled').removeClass('loading');

					//Execute callback function
					callback(result);

					//Reset recaptcha
					$recaptchaField.length > 0 && grecaptcha.reset($recaptchaField.data('id'));
				}, 'json');

			//Execute callback function
			}else{
				callback();
			}
		}
	}else{
		dialogAlert('Error!', errormsg, 'error');
	}
}

//Clear/Set error class
$(function(){
	$(document).on('blur', '.jsvalidate:input', function() {
		let $this = $(this);
		$this.toggleClass('error', !$this.val());
		$this.is(':not(.error)[type="email"]') && $this.toggleClass('error', !checkmail($this.val()));
	});
});

//Ajax Form
$(function() {
	$("#contact-form, #landing-form").on('submit', function() {
		submitForm(this, 'js/ajax/submit-form.php', function(){});
		return false;
	});
});

/*------ dialog modals ------*/

//Modals
$(function() {
	let $modals = $('.hidden-modal'),

		//Init modals with a function
		modal = function (opts) {
			let $this   = $(this),
				data    = $this.data();

			// Extend default opts with those provided
			opts = typeof opts == 'object' ? opts : {};
			opts = $.extend({
				modal: true,
				classes: data.classes,
				title: $this.attr('title') || data.title,
				autoOpen: data.open !== undefined,
				width: data.width || 315,
				maxWidth: window.innerWidth - 40,
				minHeight: 0,
				// maxHeight: window.innerHeight - 40, // Uncomment to prevent modals extending below viewport
				resizable: data.resizable !== undefined,
				draggable: data.draggable !== undefined,
				closeOnEscape: true,
				closeText: '',
				buttons: data.buttons || undefined, // Can pass in an object through the data attributes of the modal
				show: {effect: "drop", direction: "down", duration: 300},
				hide: {effect: "drop", direction: "up", duration: 300},

				//Remove titlebar if no title is given
				create: function () {
					let $widget = $this.dialog('widget');
					opts.title && $this.attr('title', opts.title);
					!opts.title && $widget.find('.ui-dialog-titlebar').addClass('hidden');
				},

				// Format links on open
				open: sanitizeHrefs
			}, opts);

			// If a single string is provided then set ui-dialog class
			opts.classes = typeof opts.classes == 'string' ? {'ui-dialog': opts.classes} : {};

			// Destroy previous instance
			$this.hasClass('ui-dialog-content') && $this.dialog('destroy');

			// Init dialog
			$this.addClass('hidden-modal').appendTo('body').dialog(opts);
		},

		//Fit dialogs to viewport width
		fit = function () {
			$(this).each(function () {
				$modal = $(this);

				if($modal.is('.ui-dialog-content:visible')) {
					$modal.dialog('option', 'maxWidth', window.innerWidth - 40);
					$modal.dialog('option', 'position', {
						my: 'center',
						at: 'center',
						of: window
					});
				}
			});
		};

	//Init/reinit modals
	$(document).on('modal-reinit', function (e, opts) {
		loadScript('jqueryui', function () {
			modal.call(e.target, opts);
		});
	});

	//Open a modal
	$(document).on('modal-open', function (e) {
		let $modal = $(e.target);

		if ($modal.is('.ui-dialog-content')) {
			$modal.dialog('open');

		//Init and auto-open if not yet initialized
		} else {
			$modal.trigger('modal-reinit', {autoOpen: true});
		}
	});

	// Automatically open dialogs
	$('.hidden-modal[data-open]').trigger('modal-reinit');

	//Fit to bounds
	$(document).on('modal-fit', function (e) {
		fit.call(e.target);
	});
	$(window).on('resize', function (e) {
		$('.ui-dialog:visible .ui-dialog-content').each(fit);
	});

	//On open
	$(document).on('dialogopen', function (e, ui) {
		let $modal = $(e.target);

		//Move to top of stack on open
		$modal.dialog('moveToTop');

		//Close if overlay is clicked
		if ($modal.data('disable-overlay-click') === undefined) {
			$modal.dialog('instance').overlay.one('click', function () {
				e.preventDefault();
				e.stopPropagation();
				$modal.dialog('close');
			});
		}
	});

	//Open modal
	$(document).on('click', '[data-open-hidden-modal]', function (e) {
		let $modal = $($(this).data('open-hidden-modal'));

		if ($modal.length) {
			e.preventDefault();

			//Create a reference back to the link and open modal
			$modal.data('opened-by', this).trigger('modal-open');
		}
	});

	//Close alerts, confirmations, and recaptcha if another dialog is focused
	$(document).on('click', '.ui-dialog', function (e) {
		let $modal = $(e.target).closest('.ui-dialog').find('.ui-dialog-content'),
			$alert = $('#dialog-box, #confirm-box, .recaptcha-modal').filter('.ui-dialog-content').not($modal);

		$alert.length && $alert.dialog('close');
	});
});

//Confirm dialog
$(function() {
	let confirm = function (e) {
		let $this = $(this);
		e.preventDefault();
		e.stopImmediatePropagation(); //Prevent other handlers

		//Remove handler and re-click
		dialogConfirm($this.data('content'), function () {
			$this.removeClass('confirm-click').click();
		});
	};

	//Confirm a click
	$(document).on('click.confirm', '.confirm-click', confirm).on('confirm-reinit', confirm);
});

//Modal alerts
function dialogAlert(title, message, status) {

	//Grab or create modal
	let $modal = $('#dialog-box');
	$modal = $modal.length ? $modal : $('<div id="dialog-box"></div>').appendTo('body');

	//Set status if not explicitly set
	status = typeof status != 'string' ? (status ? 'success' : 'error') : status;

	//Set html and opts for modal creation
	$modal.html(message).trigger('modal-reinit', {
		width: 350,
		classes: status ? 'dialog-alert dialog-'+status : 'dialog-alert',
		title: title,
		autoOpen: true
	});
}

//Modal confirm
function dialogConfirm(message, confirm, cancel) {

	//Grab or create modal
	let $modal = $('#confirm-box');
	$modal = $modal.length ? $modal : $('<div id="confirm-box"></div>').appendTo('body');

	//Set html and opts for modal creation
	$modal.html(message).trigger('modal-reinit', {
		width: 400,
		title: 'Confirm',
		classes: 'dialog-alert dialog-confirm',
		autoOpen: true,
		buttons: [
			{
				text: 'Cancel',
				class: 'button',
				click: function() {
					typeof cancel == 'function' && cancel();
					$modal.dialog("close");
				}
			},
			{
				text: 'Confirm',
				class: 'button',
				click: function() {
					typeof confirm == 'function' && confirm();
					$modal.dialog("close");
				}
			}
		]
	});
}

/*------ leadins ------*/

//Set Leadin
$(function(){
	if($('.leadin-popup').length){
		let leadin = $('.leadin-popup');
		let leadin_delay = leadin.data('delay');
		let leadin_delay_type = leadin.data('delay-type');

		//Show leadin based on delay settings
		if(leadin_delay_type == 'time'){
			let leadin_show_countdown = setTimeout(function(){
				showLeadIn(leadin);
				clearTimeout(leadin_show_countdown);
			},(leadin_delay*1000));

		}else if(leadin_delay_type == 'scroll'){
			let scroll_percent = ($(window).scrollTop()) / ($(document).height() - $(window).height());
				scroll_percent = Math.round(scroll_percent*100);

			$(window).on('scroll', function(){
				scroll_percent = ($(window).scrollTop()) / ($(document).height() - $(window).height());
				scroll_percent = Math.round(scroll_percent*100);

				if(isNaN(scroll_percent) || scroll_percent >= leadin_delay){
					showLeadIn(leadin);
				}
			});

			if(isNaN(scroll_percent) || scroll_percent >= leadin_delay){
				showLeadIn(leadin);
			}
		}

		//Close leadin
		$(document).on('click', '.leadin-popup .close-button', function(){
			hideLeadIn(leadin);
			return false;
		});
		$(window).resize(() => {
			if(window.innerWidth <= 768 && leadin.is('.show-tablet-l.ui-dialog-content')) {
				leadin.dialog('close');
			}
		});
		$('.leadin-popup.hidden-modal').on('dialogclose', function(){
			hideLeadIn(leadin);
		});
	}
});

//Display LeadIn Popup
function showLeadIn(leadin_el){
	if(!leadin_el.hasClass('displayed')){

		//Popup
		if(leadin_el.hasClass('type-popup')){

			//Check to ensure it should be shown on mobile
			let breakpoint = getBreakpoint(),
				breakpoint_min = breakpoint.minwidth,
				breakpoint_max = breakpoint.maxwidth;

			if(breakpoint_max > 768 || !leadin_el.hasClass('show-tablet-l')){
				leadin_el.trigger('modal-open');
				leadin_el.addClass('displayed');
				leadin_el.addClass('open');
			}

		//Inline
		}else{
			leadin_el.addClass('displayed');
			leadin_el.addClass('open');
		}

		//Track opens
		$.post(path + 'js/ajax/leadin-events.php', 'id='+leadin_el.data('id')+'&event=open&xid='+c_xid);
	}
}

//Hide LeadIn
function hideLeadIn(leadin_el){
	if(leadin_el.hasClass('type-popup')){
		leadin_el.dialog('close');
	}else{
		leadin_el.removeClass('open');
	}

	//Remember user closed popup
	$.post(path + 'js/ajax/hide-leadin.php', 'id='+leadin_el.data('id')+'&xid='+c_xid);

	//Track closes (unless there's a success message)
	if(leadin_el.find('.leadin-content:visible').length){
		$.post(path + 'js/ajax/leadin-events.php', 'id='+leadin_el.data('id')+'&event=close&xid='+c_xid);
	}

	leadin_el.remove();
}

//LeadIn Form
$(function(){
	$('.leadin-form').on('submit', function(){
		submitForm(this, 'js/ajax/leadin-form.php', function(result){
			if(result != undefined && result.errors < 1){
				if($('.leadin-form.hidden-modal').length){
					$('.leadin-form.hidden-modal').dialog('close');
				}
				$('.leadin-popup').find('.leadin-content, .leadin-form').fadeOut(300, function(){
					$('.leadin-popup .leadin-success').show().html('<div id="leadin-success-message">'+result.msg_validation+'</div>').fadeIn(300);
				});
			}
		});

		return false;
	});
});

//Leadin Button
$(function(){
	//link clicked
	$(document).on('click', '.leadin-popup a.button:not([data-open-hidden-modal])', function(){
		let leadin_el = $(this).closest('.leadin-popup');
		$.post(path + 'js/ajax/leadin-events.php', 'id='+leadin_el.data('id')+'&event=link-click&xid='+c_xid);
	});
	//form manually opened
	$(document).on('dialogopen', '.leadin-form', function(){
		let leadin_id = $(this).attr('id').replace('leadin-form-', '');
		$.post(path + 'js/ajax/leadin-events.php', 'id='+leadin_id+'&event=form-open&xid='+c_xid);
	});
});

/*------ google maps ------*/

//Display Map
function initMap (el, lat, lng, zoom) {
	const styles = [{"stylers":[{"visibility":"simplified"},{"saturation":20},{"weight":3.2},{"lightness":25}]}];

	let $map = $(el),
		center;

	el     = $map[0];
	lat    = lat ?? $map.data('lat') ?? 53.5400000;
	lng    = lng ?? $map.data('lng') ?? -113.49928080000001;
	zoom   = zoom ?? $map.data('zoom') ?? 15;
	center = new google.maps.LatLng(lat, lng);

	const map = new google.maps.Map(el, {
		scrollwheel: false,
		draggable: true,
		center,
		zoom,
		styles,

		disableDefaultUI: true,
		mapTypeId: google.maps.MapTypeId.ROADMAP,
		navigationControl: true,
		mapTypeControl: false,
		scaleControl: true,

		panControl: true,
		panControlOptions: {
			position: google.maps.ControlPosition.LEFT_TOP
		},

		zoomControl: true,
		zoomControlOptions: {
			style: google.maps.ZoomControlStyle.SMALL,
			position: google.maps.ControlPosition.LEFT_TOP
		}
	});

	// Center on resize
	google.maps.event.addDomListener(window, 'resize', () => map.setCenter(center));

	return map;
}

//Geocoder
function placeMapMarker(map, lat, lng, href) {
	const marker = new google.maps.Marker({
		animation: google.maps.Animation.DROP,
		position: new google.maps.LatLng(lat, lng),
		href,
		map,

		icon: {
			url: path+'images/svg/maps-marker.svg?v=1.2',
			size: new google.maps.Size(30, 40),
			origin: new google.maps.Point(0, 0),
			anchor: new google.maps.Point(15, 40),
			scaledSize: new google.maps.Size(30, 40)
		},
	});

	// Open url
	if (marker.href) {
		google.maps.event.addListener(marker, 'click', () => window.open(marker.href, "_blank"));
	}

	return marker;
}

//Lazy load contact map
$(function () {
	const buildURL = (query, lat, lng) => `https://maps.google.com/?q=${encodeURIComponent(query)}&latlng=${lat},${lng}`;

	// Init visible maps using current map element data
	let $maps = $('.contact-map');
	observeOnce($maps, () => loadScript('gmapsapi', () => {
		$maps.each((i, el) => {
			let data = $(el).data(),
				href = buildURL(data.search, data.lat, data.lng);

			const map    = initMap(el);
			const marker = placeMapMarker(map, data.lat, data.lng, href);

			$(el).data({googleMap: map, markers: [marker]});
		});
	}));

	// Change map location on tab click
	$maps.length && $('.content-tabs').on('tabsbeforeactivate', function(e, ui) {
		let $el  = $(ui.newTab).find('a'),
			$map = $('.contact-map'),
			data = $el.data(),
			href = buildURL(data.search, data.lat, data.lng);

		if(data.map !== undefined) {
			if(data.map) {
				let marker = $map.data('markers') ? $map.data('markers')[0] : false,
					map    = $map.data('googleMap'),
					center = { lat: data.lat, lng: data.lng };

				$map.data(data);
				$map.closest('.panel').addClass('has-map');

				if (map && marker) {
					marker.setPosition(center);
					marker.href = href;
					map.panTo(center);
					map.setZoom(data.zoom);
				}

				$map.fadeIn(300);

			} else {
				$map.fadeOut(300, function () {
					$map.closest('.panel').removeClass('has-map');
				});
			}
		}
	});
});

// Defer requesting font awesome until page as loaded
$(function() {
	var l = document.createElement('link'),
		h = document.getElementsByTagName('head')[0];

	l.rel = 'stylesheet';
	l.href = path+'core/plugins/font-awesome/css/all.min.css';
	h.parentNode.append(l);
});

// Add curve overlay image to parallax sections
$(function() {
    // Add the SVG image to the panel itself
    $('.panel.parallax').each(function() {
        // Create a container for the curve
        const $curveContainer = $('<div>', {
            class: 'curve-container'
        });

        // Create an image element for the SVG
        const $img = $('<img>', {
            src: path + 'images/svg/parllax-bg.svg?v=1.0',
            alt: '',
            class: 'curve-shape'
        });

        // Append the image to the curve container
        $curveContainer.append($img);

        // Append the curve container to the panel itself
        $(this).append($curveContainer);
    });
});

// Add curve overlay image to parallax sections
// $(function() {
//     // Add the SVG image to the panel itself
//     $('.panel.parallax').each(function() {
//         // Create a container for the curve
//         const $curveContainer = $('<div>', {
//             class: 'curve-container'
//         });

//         // Create an image element for the SVG
//         const $img = $('<img>', {
//             src: '/pga/images/svg/parllax-bg.svg?v=1.9', // Add version to prevent caching
//             alt: '',
//             class: 'curve-shape'
//         });

//         // Append the image to the curve container
//         $curveContainer.append($img);

//         // Insert the curve container at the beginning of the panel
//         // This ensures it's behind all other content
//         $(this).prepend($curveContainer);
//     });
// });

$(document).ready(function() {
	// Get all location tabs and map element
	const $locationTabs = $('.location-tab');
	const $contactMap = $('#contact-map');
	const $sliderWrapper = $('.location-slider-wrapper');
	const $tabsNav = $('.location-tabs-nav');
	const $scrollUp = $('.location-scroll-up');
	const $scrollDown = $('.location-scroll-down');

	// Initialize map variables
	let map = null;
	let marker = null;
	let currentIndex = 0;
	let tabHeight = 0;
	let visibleItems = 1; // Maximum number of visible items

	// Function to initialize Google Map
	function initializeMap() {
		if (window.google && window.google.maps && $contactMap.length) {
			const lat = parseFloat($contactMap.attr('data-lat')) || 0;
			const lng = parseFloat($contactMap.attr('data-lng')) || 0;
			const zoom = parseInt($contactMap.attr('data-zoom')) || 12;

			// Create map
			map = new google.maps.Map($contactMap[0], {
				center: { lat: lat, lng: lng },
				zoom: zoom,
				scrollwheel: false
			});

			// Create marker
			marker = new google.maps.Marker({
				position: { lat: lat, lng: lng },
				map: map,
				animation: google.maps.Animation.DROP
			});

			// Store map and marker references
			$contactMap.data('map', map);
			$contactMap.data('marker', marker);
		}
	}

	// Function to update map
	function updateMap(lat, lng, zoom) {
		if (map && marker) {
			const position = { lat: lat, lng: lng };
			marker.setPosition(position);
			map.setCenter(position);
			map.setZoom(zoom);
		}
	}

	// Function to update slider position
	function updateSliderPosition() {
		console.log('updateSliderPosition');

		if ($locationTabs.length > 0) {
			tabHeight = $locationTabs.first().outerHeight();
			const maxTranslateY = Math.max(0, ($locationTabs.length - visibleItems) * tabHeight);
			const translateY = -currentIndex * tabHeight;

			$tabsNav.css('transform', `translateY(${translateY}px)`);

			// Update arrow states
			$scrollUp.toggleClass('disabled', currentIndex === 0);
			$scrollDown.toggleClass('disabled', currentIndex >= $locationTabs.length - visibleItems);
		}
	}

	// Scroll up function
	function scrollUp() {
		console.log('scroll up');
		if (currentIndex > 0) {
			currentIndex--;
			updateSliderPosition();

			// Select the newly visible tab
			const visibleTab = $locationTabs.eq(currentIndex);

			// Trigger click on the tab to update location
			visibleTab.trigger('click');
		}
	}

	// Scroll down function
	function scrollDown() {
		if (currentIndex < $locationTabs.length - visibleItems) {
			currentIndex++;
			updateSliderPosition();

			// Select the newly visible tab
			const visibleTab = $locationTabs.eq(currentIndex);

			// Trigger click on the tab to update location
			visibleTab.trigger('click');
		}
	}

	// Initialize slider
	function initSlider() {
		// Calculate tab height and update slider position
		updateSliderPosition();

		// Debounced scroll functions
		const debouncedScrollUp = debounce(function() {
			if (!$(this).hasClass('disabled')) {
				scrollUp();
			}
		}, 600, true);

		const debouncedScrollDown = debounce(function() {
			if (!$(this).hasClass('disabled')) {
				scrollDown();
			}
		}, 600, true);

		// Add click events to scroll arrows
		$scrollUp.on('click', function(e) {
			console.log('Up arrow clicked via delegation');
			e.preventDefault();
			e.stopPropagation();

			// Call the debounced function, preserving 'this' context
			debouncedScrollUp.call(this);
			// if (!$(this).hasClass('disabled')) {
			// 	scrollUp();
			// }
		});

		$scrollDown.on('click', function(e) {
			console.log('Down arrow clicked via delegation');
			e.preventDefault();
			e.stopPropagation();

			// Call the debounced function, preserving 'this' context
			debouncedScrollDown.call(this);
			// if (!$(this).hasClass('disabled')) {
			// 	scrollDown();
			// }
		});

		// Add keyboard navigation
		$(document).on('keydown', function(e) {
			if ($sliderWrapper.is(':visible')) {
				if (e.which === 38) { // Up arrow
					scrollUp();
					e.preventDefault();
				} else if (e.which === 40) { // Down arrow
					scrollDown();
					e.preventDefault();
				}
			}
		});

		// Add mousewheel navigation
		$sliderWrapper.on('wheel', function(e) {
			if (e.originalEvent.deltaY < 0) {
				scrollUp();
			} else {
				scrollDown();
			}
			e.preventDefault();
		});
	}

	// Check if Google Maps API is already loaded
	if (window.google && window.google.maps) {
		initializeMap();
	} else {
		// If Google Maps API is loaded later, initialize map when it's ready
		window.initMap = initializeMap;
	}

	if ($locationTabs.length > 0) {
		// Initialize slider
		initSlider();

		// Add click event to each location tab
		$locationTabs.on('click', function() {
			// Remove active class from all tabs
			$locationTabs.removeClass('active');

			// Add active class to clicked tab
			$(this).addClass('active');

			// Get location ID
			const locationId = $(this).data('location-id');

			// Hide all location details
			$('.location-details').hide();

			// Show selected location details
			$('#location-details-' + locationId).show();

			// Update map with new location data
			const lat = parseFloat($(this).attr('data-lat')) || 0;
			const lng = parseFloat($(this).attr('data-lng')) || 0;
			const zoom = parseInt($(this).attr('data-zoom')) || 12;

			// Update map
			updateMap(lat, lng, zoom);
		});

		// Handle window resize
		$(window).on('resize', function() {
			updateSliderPosition();
		});
	}
});

// // Replace the current click handlers with this approach
// $(document).on('click', '.location-scroll-up', function(e) {
//     console.log('Up arrow clicked via delegation');
//     e.preventDefault();
//     e.stopPropagation();

//     // Only proceed if not disabled
//     if (!$(this).hasClass('disabled')) {
//         // Get the current active tab index
//         const activeIndex = $('.location-tab.active').index();

//         // Only proceed if not at the first item
//         if (activeIndex > 0) {
//             // Select the previous tab
//             $('.location-tab').eq(activeIndex - 1).trigger('click');

//             // Update disabled states
//             if (activeIndex - 1 === 0) {
//                 $(this).addClass('disabled');
//             }
//             $('.location-scroll-down').removeClass('disabled');
//         }
//     }
// });

// $(document).on('click', '.location-scroll-down', function(e) {
//     console.log('Down arrow clicked via delegation');
//     e.preventDefault();
//     e.stopPropagation();

//     // Only proceed if not disabled
//     if (!$(this).hasClass('disabled')) {
//         // Get the current active tab index and total tabs
//         const activeIndex = $('.location-tab.active').index();
//         const totalTabs = $('.location-tab').length;

//         // Only proceed if not at the last item
//         if (activeIndex < totalTabs - 1) {
//             // Select the next tab
//             $('.location-tab').eq(activeIndex + 1).trigger('click');

//             // Update disabled states
//             if (activeIndex + 1 === totalTabs - 1) {
//                 $(this).addClass('disabled');
//             }
//             $('.location-scroll-up').removeClass('disabled');
//         }
//     }
// });


// conflictng with other scripts
// // In script.js - Update the click handlers for the arrows
// $('.location-scroll-up').on('click', function(e) {
// 	e.preventDefault();
// 	e.stopPropagation();

// 	// Only proceed if not disabled
// 	if (!$(this).hasClass('disabled')) {
// 		// Get the current active tab index
// 		const activeIndex = $('.location-tab.active').index();

// 		// Only proceed if not at the first item
// 		if (activeIndex > 0) {
// 			// Select the previous tab
// 			$('.location-tab').eq(activeIndex - 1).trigger('click');

// 			// Update disabled states
// 			$('.location-scroll-up').toggleClass('disabled', activeIndex - 1 === 0);
// 			$('.location-scroll-down').removeClass('disabled');
// 		}
// 	}
// });

// $('.location-scroll-down').on('click', function(e) {
// 	e.preventDefault();
// 	e.stopPropagation();

// 	// Only proceed if not disabled
// 	if (!$(this).hasClass('disabled')) {
// 		// Get the current active tab index and total tabs
// 		const activeIndex = $('.location-tab.active').index();
// 		const totalTabs = $('.location-tab').length;

// 		// Only proceed if not at the last item
// 		if (activeIndex < totalTabs - 1) {
// 			// Select the next tab
// 			$('.location-tab').eq(activeIndex + 1).trigger('click');

// 			// Update disabled states
// 			$('.location-scroll-up').removeClass('disabled');
// 			$('.location-scroll-down').toggleClass('disabled', activeIndex + 1 === totalTabs - 1);
// 		}
// 	}
// });

// // Add this at the end of your document.ready function
// // Initialize arrow states on page load
// const activeIndex = $('.location-tab.active').index();
// const totalTabs = $('.location-tab').length;
// $('.location-scroll-up').toggleClass('disabled', activeIndex === 0);
// $('.location-scroll-down').toggleClass('disabled', activeIndex === totalTabs - 1);

// $(document).ready(function () {
// 	const $main = $('main');
// 	const $lastSection = $main.find('section:last-of-type');
// 	const $footer = $('#page-footer');

// 	if ($lastSection.hasClass('cta')) {
// 	  // If the last section has the 'cta' class
// 	  $footer.css({
// 		background: "url('../../images/footer-cta.png')",
// 		'background-size': 'cover',
// 		'background-repeat': 'no-repeat',
// 	  });
// 	} else {
// 	  // If the last section does NOT have the 'cta' class
// 	  $footer.css({
// 		background: "url('../../images/svg/footer.svg') no-repeat 100% 100%",
// 		'background-size': 'cover',
// 		minHeight: '100vw',
// 	  });
// 	}
//   });

$(document).ready(function () {
	const $main = $('main');
	const $lastSection = $main.find('section:last-of-type');
	const $footer = $('#page-footer');

	function applyFooterStyles() {
		// if(window.matchMedia('(max-width: 550px)').matches) {
		// 	// If the screen size is 550px or less
		// 	const sharedBackground = "url('../../images/svg/shared-footer.svg')"; // Single shared image for both parts

		// 	// Apply the same background to both the last section and footer
		// 	$lastSection.css({
		// 	  background: `${sharedBackground} no-repeat center top`,
		// 	  'background-size': 'cover',
		// 	//   minHeight: '50vh', // Adjust height as needed
		// 	//   'background-attachment': 'scroll',
		// 	  minHeight:'auto',
		// 	});

		// 	$footer.css({
		// 	  background: `${sharedBackground} no-repeat center bottom`,
		// 	  'background-size': 'cover',
		// 	//   'background-attachment': 'scroll',
		// 	//   minHeight: '25vh', // Adjust height as needed
		// 	  minHeight:'auto',
		// 	});
		//   }
	  	// else

		if (window.matchMedia('(max-width: 768px)').matches) {
		// If the screen size is 768px or less
			$footer.css({
					// background: "url('../../images/footer-m.png') no-repeat center bottom",
					// background: "url('../../images/footer-m.png') no-repeat center bottom",
					background: "url('/images/footer-m.png') no-repeat center bottom",
					'background-size': 'cover',
					minHeight: '100vw',
					'background-position': '100% 100%'
				});

			if ($lastSection.hasClass('cta')) {
				$lastSection.css({
					// background: "url('../../images/cta-m.png') no-repeat center bottom",
					// background: "url('../../images/cta-m.png') no-repeat center bottom",
					background: "url('/images/cta-m.png') no-repeat center bottom",
					'background-size': 'cover',
					minHeight: '100vw',
					'background-position': '100% 100%'
				});
			}
		} else {
			// If the screen size is greater than 768px
			if ($lastSection.hasClass('cta')) {
				// If the last section has the 'cta' class
				$footer.css({
					// background: "url('../../images/footer-cta.png')",
					// background: "url('../../images/footer-cta.png')",
					background: "url('/images/footer-cta.png')",
					'background-size': 'cover',
					'background-repeat': 'no-repeat',
				});
			} else {
			// If the last section does NOT have the 'cta' class
			$footer.css({
				// background: "url('../../images/svg/footer.svg') no-repeat center bottom",
				// background: "url('../../images/svg/footer.svg') no-repeat center bottom",
				background: "url('/images/svg/footer.svg') no-repeat center bottom",
			});
			}
		}
	}

	// Apply styles on page load
	applyFooterStyles();

	// Reapply styles on window resize
	$(window).resize(function () {
	  applyFooterStyles();
	});
  });

// Blog

// Lazy load and track social sharing
$(function(){
	observeOnce('#blog-share', () => loadScript('sharethis', () => {
		$('.sharethis-buttons').each(function(){

			// Custom options
			var id = $(this).attr('id'),
				url = $(this).data('url') || null,
				title = $(this).data('title') || null,
				description = $(this).data('description') || null,
				image = $(this).data('image') || null;

			// Inline share buttons
			window.__sharethis__.load('inline-share-buttons', {
				enabled: true,
				id,
				url,
				title,
				description,
				image,
				networks: ['facebook', 'twitter', 'linkedin', 'email', 'sharethis'],
				show_mobile_buttons: true,
				onLoad: function(){
					$('.st-btn').on('click', function(e){
						$.ajax({
							url: path+'js/ajax/track-shares.php',
							data:{service: $(this).data('network'), url: url || window.__sharethis__.href, xid: c_xid},
							type: 'POST'
						});
					});
				}
			});

			/*
			// Sticky share buttons
			window.__sharethis__.load('sticky-share-buttons', {
				enabled: true,
				networks: ['facebook', 'twitter', 'linkedin', 'pinterest', 'sharethis'],
				alignment: 'right', //left, right, center, justified
				labels: 'cta', //cta, counts, or none
				min_count: 10, //minimum amount of shares before showing the count
				use_native_counts: true, //uses native facebook counts from the open graph api
				hide_desktop: false,
				show_toggle: true,
				show_total: true,
				show_mobile: true,
				mobile_breakpoint: 1024,
				top: 200,
				padding: 10, //small = 8, medium = 10, large = 12
				radius: 3,
				background_color: "#B581A3",
				text_color: '#FFF',
				color: 'social',
				size: 40, //small = 32, medium = 40, large = 48
				size_label: 'medium',
				font_size: 12, //small = 11, medium = 12, large = 16
				onLoad: function(){
					$('.st-btn').on('click', function(e){
						$.ajax({
							url: path+'js/ajax/track-shares.php',
							data:{service: $(this).data('network'), url: window.__sharethis__.href, xid: c_xid},
							type: 'POST'
						});
					});
				}

			});
			*/

		});
	}));
});

//Comment Form
$(function() {
	$("#blog-comment-form").on('submit', function() {
		submitForm('blog-comment-form', 'js/ajax/post-comment.php', function(data) {
			if(typeof(data) !== 'undefined' && data.new_comment != ''){
				$('#blog-comments').remove('.no-comments');
				$('#blog-comments').prepend(data.new_comment);
			}
		});
		return false;
	});
});

// Blog Swiper Implementation
$(function() {
    // Initialize only if we're on the blog page and elements exist
    if ($('.blog-swiper-container').length) {

        const $blogContainer = $('.blog-swiper-container');
        const $blogEntries = $('.blog-entry');
        const $allSlides = $('.swiper-slide', $blogContainer);
        const $prevButton = $('.swiper-button-prev', $blogContainer);
        const $nextButton = $('.swiper-button-next', $blogContainer);
        const $searchInput = $('.blog-search input');
        const $categorySelect = $('.blog-category-filter select');

        const visibleItems = 3; // Number of items to show in the sidebar
        let currentIndex = 0;   // Index of the first visible slide in the current set
        let $currentSlideSet = $allSlides; // The collection of slides being navigated (all, searched, or category-filtered)

        // --- Helper Functions ---

        // Shows the content for a specific blog entry ID
        function showEntryContent(entryId) {
            // Hide all entries
            $blogEntries.hide();

            // Find the entry to show
            const $entryToShow = $blogEntries.filter('[data-entry-id="' + entryId + '"]');
            if ($entryToShow.length) {
                // Show the selected entry
                $entryToShow.show();

                // Find the corresponding slide to get title and date
                const $slide = $allSlides.filter('[data-entry-id="' + entryId + '"]');
                if ($slide.length) {
                    // Update the title and date at the top of the content area
                    const slideTitle = $slide.find('.post-title').text();
                    const slideDate = $slide.find('.post-date').text();

                    // Update the title and date elements
                    $('#blog-rightcol .blog-title').text(slideTitle);
                    $('#blog-rightcol .blog-date').text('Posted on ' + slideDate);
                }

                // Optional: Scroll to top of entry on mobile
                if (window.innerWidth <= 768) {
                    $('html, body').animate({
                        scrollTop: $entryToShow.offset().top - 100 // Adjust offset as needed
                    }, 300);
                }
            }
        }

        // Updates *only* the visible slides in the sidebar and navigation buttons
        function updateDisplay(newSlideSet, newCurrentIndex = 0) {
            $currentSlideSet = newSlideSet;
            currentIndex = Math.max(0, newCurrentIndex);

            // Ensure currentIndex is valid within the bounds of the new set
            if ($currentSlideSet.length > 0) {
                 currentIndex = Math.min(currentIndex, $currentSlideSet.length - 1);
                 // Adjust index if scrolling would show fewer than 'visibleItems' at the end
                 if (currentIndex + visibleItems > $currentSlideSet.length) {
                    currentIndex = Math.max(0, $currentSlideSet.length - visibleItems);
                 }
            } else {
                currentIndex = 0; // No slides, reset index
            }

            // Hide all slides first (prep for showing the correct slice)
            $allSlides.hide();

            // Determine which slides should be visible based on index and visibleItems count
            const $visibleSlides = $currentSlideSet.slice(currentIndex, currentIndex + visibleItems);
            $visibleSlides.show();

            // Update navigation button states based on the *current slide set*
            $prevButton.toggleClass('swiper-button-disabled', currentIndex <= 0 || $currentSlideSet.length <= visibleItems);
            $nextButton.toggleClass('swiper-button-disabled', currentIndex + visibleItems >= $currentSlideSet.length || $currentSlideSet.length <= visibleItems);
            $blogContainer.toggleClass('few-posts', $currentSlideSet.length <= visibleItems);

            // DO NOT update active class or show content here. That's handled by clicks/filters.
        }

        // --- Initialization ---
        loadScript('swiper', function() { // Assuming swiper might provide base CSS or other needs
            // Initial display setup (sidebar only)
            updateDisplay($allSlides, 0);

            // Set first slide active and show its content initially
            if ($allSlides.length > 0) {
                const $firstSlide = $allSlides.first();
                $firstSlide.addClass('active');
                showEntryContent($firstSlide.data('entry-id'));
            } else {
                $blogEntries.hide(); // Hide all content if no slides exist
            }
        });

        // --- Event Handlers ---

        // Mobile dropdown change handler
        $('#mobile-blog-select').on('change', function() {
            const entryId = $(this).val();
            if (entryId) {
                // Update active class in sidebar (even though it's hidden on mobile)
                $allSlides.removeClass('active');
                const $matchingSlide = $allSlides.filter('[data-entry-id="' + entryId + '"]');
                $matchingSlide.addClass('active');

                // Show the corresponding content
                showEntryContent(entryId);
            }
        });

		// $('#mobile-blog-select').on('change', function() {
        //     const entryId = $(this).val();
        //     if (entryId) {
        //         // Update active class in sidebar (even though it's hidden on mobile)
        //         $allSlides.removeClass('active');
        //         const $matchingSlide = $allSlides.filter('[data-entry-id="' + entryId + '"]');
        //         $matchingSlide.addClass('active');

        //         // Show the corresponding content
        //         showEntryContent(entryId);
        //     }
        // });

        // Slide Click: Sets active state AND loads content
        $allSlides.on('click', function(e) {
            e.preventDefault(); // Prevent potential default link behavior if slides are anchors
            const $this = $(this);

            // Update active class
            $allSlides.removeClass('active');
            $this.addClass('active');

            // Show the corresponding content immediately
            showEntryContent($this.data('entry-id'));

            // Update mobile dropdown to match selected slide (for consistency)
            $('#mobile-blog-select').val($this.data('entry-id'));
        });

        // Search Input: Filters sidebar ONLY, does not load content
        $searchInput.on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase().trim();
            $categorySelect.val(''); // Reset category filter when searching

            let $matchingSlides;
            if (searchTerm === '') {
                $matchingSlides = $allSlides; // Show all if search is cleared
            } else {
                $matchingSlides = $allSlides.filter(function() {
                    // Strict check within specific elements
                    const title = $(this).find('.post-title').text().toLowerCase().trim();
                    const category = $(this).find('.post-category').text().toLowerCase().trim();
                    const date = $(this).find('.post-date').text().toLowerCase().trim();
                    return title.includes(searchTerm) || category.includes(searchTerm) || date.includes(searchTerm);
                });
            }
            // Update sidebar view ONLY
            updateDisplay($matchingSlides, 0);

            // DO NOT automatically set active or load content here.
            // The currently displayed content remains until a slide is clicked.
            // If no slides match, the sidebar will be empty, but content area is unaffected by search itself.
        });

        // Category Filter: Filters sidebar ONLY, does not load content
        $categorySelect.on('change', function() {
            const categoryId = $(this).val();
            $searchInput.val(''); // Reset search input when filtering category

            let $matchingSlides;
            if (categoryId === '') {
                $matchingSlides = $allSlides; // Show all if category is reset
            } else {
                $matchingSlides = $allSlides.filter(function() {
                    // Use == for loose comparison as data attributes might be strings or numbers
                    return $(this).data('category-id') == categoryId;
                });
            }
            // Update sidebar view ONLY
            updateDisplay($matchingSlides, 0);

             // DO NOT automatically set active or load content here.
            // The currently displayed content remains until a slide is clicked.
            // If no slides match, the sidebar will be empty, but content area is unaffected by filter itself.
        });

        // Previous Button: ONLY scrolls sidebar
        $prevButton.on('click', function(e) {
             e.preventDefault();
            if (!$(this).hasClass('swiper-button-disabled')) {
                updateDisplay($currentSlideSet, currentIndex - 1); // Navigate sidebar ONLY
            }
        });

        // Next Button: ONLY scrolls sidebar
        $nextButton.on('click', function(e) {
             e.preventDefault();
            if (!$(this).hasClass('swiper-button-disabled')) {
                updateDisplay($currentSlideSet, currentIndex + 1); // Navigate sidebar ONLY
            }
        });

        // Optional: Update display on resize if needed (e.g., if visibleItems changes)
        // $(window).on('resize', debounce(() => updateDisplay($currentSlideSet, currentIndex), 150));

    } // End if blog-swiper-container exists
}); // End document ready

// Toggle light class on CTA buttons based on viewport width
$(function() {
    function toggleCTALightClass() {
        let $ctaButtons = $('.button.primary');
        if (window.innerWidth > 768) {
            $ctaButtons.addClass('light');
        } else {
            $ctaButtons.removeClass('light');
        }
    }

    // Initial check
    toggleCTALightClass();

    // Check on resize
    $(window).on('resize', debounce(toggleCTALightClass, 200));
});

$(function(){
	if (window.location.href.endsWith('/staff/') || window.location.href.endsWith('/staff')) {

		$('main section').first().find('.container').addClass('container-lg');

		$('main section').first().find('.container .panel-text').css({
			'width': '100%',
			'max-width': '100%'
		});
	}
	// need new coniditon inlcudes /staff/ but not endswith
	else if (window.location.href.includes('/staff/')) {

		$('main section').first().find('.container .panel-text').css({
			'width': '100%',
			'max-width': '100%'
		});
	}

	$('.staff-link').on('click', function(e) {
		// e.preventDefault();
		// e.stopPropagation();
		window.location.href = $(this).find('a').attr('href');

	})
})

// // Conflict with other scripts
// $(document).on('click', '.staff-link', function(e) {
// 	e.preventDefault();
// 	e.stopPropagation();
// 	window.location.href = $(this).find('a').attr('href');
// })

// // Fix relative URLs based on page depth for staff detail page - page-header/banner and footer images
// $(function() {
//     // Function to fix relative URLs based on URL depth
//     function fixRelativeUrls() {
//         // Parse the current URL path
//         const urlPath = window.location.pathname;

//         // Count the number of directory levels in the path
//         const pathDepth = urlPath.split('/').filter(Boolean).length;

//         // If we're on a staff detail page (3 or more levels deep, contains /staff/)
//         if (urlPath.includes('/staff/') && pathDepth >= 3) {
//             console.log('Fixing URLs for staff detail page:', urlPath);

//             // Fix footer background
//             $('#page-footer').css('background-image', 'url("../../../images/svg/footer.svg")');

//             // Fix any banner elements
//             $('[id*="page-hero"]').css('background-image', 'url("../../../images/svg/banner.svg")');

//             // Fix footer CTA if it exists
//             $('.footer-cta').css('background-image', 'url("../../../images/footer-cta.png")');

//             // Fix any other elements with background images pointing to the images directory
//             $('[style*="../../images"]').each(function() {
//                 const $this = $(this);
//                 const currentBg = $this.css('background-image');
//                 if (currentBg && currentBg.includes('../../images')) {
//                     const newBg = currentBg.replace('../../images', '../../../images');
//                     $this.css('background-image', newBg);
//                 }
//             });
//         }
//     }

//     // Run on page load with a slight delay to ensure all elements are rendered
//     setTimeout(fixRelativeUrls, 100);

//     // Also run after any AJAX content is loaded
//     $(document).ajaxComplete(function() {
//         setTimeout(fixRelativeUrls, 100);
//     });
// });

// // Blog dropdown search filtering
// // Blog search autocomplete functionality
// $(function() {
//     const $blogSelect = $('#mobile-blog-select');
//     const $searchInput = $('.blog-search input');
//     const $searchResults = $('.search-results-dropdown');

//     // Store all blog entries for search
//     const blogEntries = [];
//     $blogSelect.find('option').each(function() {
//         if ($(this).val()) { // Skip the placeholder option
//             blogEntries.push({
//                 id: $(this).val(),
//                 text: $(this).text()
//             });
//         }
//     });

//     // Show search results when typing
//     $searchInput.on('keyup focus', function() {
//         const searchTerm = $(this).val().toLowerCase().trim();

//         // Hide dropdown if search is empty
//         if (searchTerm === '') {
//             $searchResults.empty().hide();
//             return;
//         }

//         // Filter entries based on search term
//         const matchingEntries = blogEntries.filter(entry =>
//             entry.text.toLowerCase().includes(searchTerm)
//         );

//         // Build and show results dropdown
//         if (matchingEntries.length > 0) {
//             $searchResults.empty();

//             matchingEntries.forEach(entry => {
//                 $('<div>')
//                     .addClass('search-result-item')
//                     .text(entry.text)
//                     .attr('data-entry-id', entry.id)
//                     .appendTo($searchResults);
//             });

//             $searchResults.show();
//         } else {
//             $searchResults.html('<div class="no-results">No matching posts found</div>').show();
//         }
//     });

//     // Hide dropdown when clicking outside
//     $(document).on('click', function(e) {
//         if (!$(e.target).closest('.blog-search').length) {
//             $searchResults.hide();
//         }
//     });

//     // Handle search result selection
//     $(document).on('click', '.search-result-item', function() {
//         const entryId = $(this).data('entry-id');
//         const entryText = $(this).text();

//         // Update search input with selected text
//         $searchInput.val(entryText);

//         // Hide the dropdown
//         $searchResults.hide();

//         // Show the corresponding content
//         showEntryContent(entryId);

//         // Update mobile dropdown to match selected item
//         $blogSelect.val(entryId);
//     });
// });

// Blog dropdown search filtering
// Blog search autocomplete functionality
$(function() {
    const $blogSelect = $('#mobile-blog-select');
    const $searchInput = $('.blog-search input');
    const $searchResults = $('.search-results-dropdown');
    const $blogEntries = $('.blog-entry');

    // Store all blog entries for search
    const blogEntries = [];
    $blogSelect.find('option').each(function() {
        if ($(this).val()) { // Skip the placeholder option
            blogEntries.push({
                id: $(this).val(),
                text: $(this).text()
            });
        }
    });

    // Function to show the selected blog entry content - defined in parent scope
    function showEntryContent(entryId) {
        // Hide all entries
        $blogEntries.hide();

        // Find the entry to show
        const $entryToShow = $blogEntries.filter('[data-entry-id="' + entryId + '"]');
        if ($entryToShow.length) {
            // Show the selected entry
            $entryToShow.show();

            // Find the corresponding slide or option to get title and date
            const $slide = $('.swiper-slide').filter('[data-entry-id="' + entryId + '"]');
            const $option = $blogSelect.find('option[value="' + entryId + '"]');

            let slideTitle, slideDate;

            if ($slide.length) {
                slideTitle = $slide.find('.post-title').text();
                slideDate = $slide.find('.post-date').text();
            } else if ($option.length) {
                const optionText = $option.text();
                const parts = optionText.split(' - ');
                slideTitle = parts[0] || '';
                slideDate = parts[1] || '';
            }

            // Update the title and date elements
            $('#blog-rightcol .blog-title').text(slideTitle || '');
            $('#blog-rightcol .blog-date').text(slideDate ? 'Posted on ' + slideDate : '');

            // Optional: Scroll to top of entry on mobile
            if (window.innerWidth <= 768) {
                $('html, body').animate({
                    scrollTop: $entryToShow.offset().top - 100 // Adjust offset as needed
                }, 300);
            }
        }
    }

    // Show search results when typing
    $searchInput.on('keyup focus', function() {
        const searchTerm = $(this).val().toLowerCase().trim();

        // Hide dropdown if search is empty
        if (searchTerm === '') {
            $searchResults.empty().hide();
            return;
        }

        // Filter entries based on search term
        const matchingEntries = blogEntries.filter(entry =>
            entry.text.toLowerCase().includes(searchTerm)
        );

        // Build and show results dropdown
        if (matchingEntries.length > 0) {
            $searchResults.empty();

            matchingEntries.forEach(entry => {
                $('<div>')
                    .addClass('search-result-item')
                    .text(entry.text)
                    .attr('data-entry-id', entry.id)
                    .appendTo($searchResults);
            });

            $searchResults.show();
        } else {
            $searchResults.html('<div class="no-results">No matching posts found</div>').show();
        }
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.blog-search').length) {
            $searchResults.hide();
        }
    });

    // Handle search result selection
    $(document).on('click', '.search-result-item', function() {
        const entryId = $(this).data('entry-id');
        const entryText = $(this).text();

        // Update search input with selected text
        $searchInput.val(entryText);

        // Hide the dropdown
        $searchResults.hide();

        // Show the corresponding content
        showEntryContent(entryId);

        // Update mobile dropdown to match selected item
        // $blogSelect.val(entryId);
		// Reset dropdown to default "Select a blog post" value
        // This prevents confusion when both search and dropdown show the same value
        $blogSelect.val('');
    });
});


/**
 * Blog Custom Slider Initialization
 * This script initializes the custom blog slider with varying shapes
 */
$(document).ready(function() {
    // Make sure Swiper is loaded
    if (typeof Swiper === "undefined") {
        if (typeof loadScript === "function") {
            loadScript('swiper', initBlogSlider);
        } else {
            console.error("Swiper is not loaded and loadScript function is not available");
        }
    } else {
        initBlogSlider();
    }

    function initBlogSlider() {
        // Wait for DOM to be fully ready
        setTimeout(function() {
            // Check if the slider exists on the page
            if ($('.blog-custom-slider').length === 0) return;

            console.log('Initializing blog slider...');

            // Initialize the blog slider with custom slide widths
            var blogSwiper = new Swiper(".blog-custom-slider", {
                slidesPerView: 'auto', // Desktop: custom width slides
                spaceBetween: 20,
                loop: false,
                cssMode: true,
                resistance: true,
                resistanceRatio: 0,
                grabCursor: true,
                watchOverflow: true,
                centerInsufficientSlides: true,
                roundLengths: true, // Prevents the "1px gap" issue
                // preventInteractionOnTransition: true,
                pagination: {
                    el: ".blog-swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: ".blog-swiper-button-next",
                    prevEl: ".blog-swiper-button-prev",
                },
                breakpoints: {
                    // Mobile: 1 slide at a time
                    0: {
                        slidesPerView: 1,
                        spaceBetween: 20
                    },
                    // Tablet: 2 slides at a time
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 20
                    },
                    // Desktop: custom width slides
                    992: {
                        slidesPerView: 'auto',
                        spaceBetween: 20
                    },
					1100: {
                        slidesPerView: 'auto',
                        spaceBetween: 25
                    }
                }
            });

            // Add click event listeners to navigation buttons
            var prevBtn = document.querySelector(".blog-swiper-button-prev");
            var nextBtn = document.querySelector(".blog-swiper-button-next");

            if (prevBtn) {
                prevBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Prev button clicked');
                    blogSwiper.slidePrev();
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Next button clicked');
                    blogSwiper.slideNext();
                });
            }

            console.log('Blog slider initialized successfully');
        }, 500); // Small delay to ensure DOM is fully loaded
    }
});

//
/**
 * Blog Slider Responsive Behavior
 * Handles the responsive behavior of the blog slider at different breakpoints
 */
// document.addEventListener('DOMContentLoaded', function() {
//     // Function to handle responsive layout changes
//     function handleBlogSliderResponsive() {
//         const stackedSlides = document.querySelectorAll('.blog-slide-stacked');
//         const isMobileBreakpoint = window.innerWidth <= 850;

//         if (stackedSlides.length) {
//             stackedSlides.forEach(function(stackedSlide) {
//                 const entries = stackedSlide.querySelectorAll('.blog-entry');
//                 const parentWrapper = stackedSlide.parentNode;

//                 if (isMobileBreakpoint) {
//                     // At 1024px and below, remove the stacked container and add each entry as a separate slide
//                     if (!stackedSlide.classList.contains('responsive-processed')) {
//                         entries.forEach(function(entry) {
//                             // Create a new slide for each entry
//                             const newSlide = document.createElement('div');
//                             newSlide.className = 'swiper-slide blog-slide-individual';
//                             newSlide.appendChild(entry.cloneNode(true));

//                             // Insert the new slide after the stacked slide
//                             parentWrapper.insertBefore(newSlide, stackedSlide.nextSibling);
//                         });

//                         // Hide the original stacked slide
//                         stackedSlide.style.display = 'none';
//                         stackedSlide.classList.add('responsive-processed');
//                     }
//                 } else {
//                     // Above 1024px, restore the original stacked layout
//                     if (stackedSlide.classList.contains('responsive-processed')) {
//                         // Show the original stacked slide
//                         stackedSlide.style.display = '';

//                         // Remove the individual slides we created
//                         const individualSlides = parentWrapper.querySelectorAll('.blog-slide-individual');
//                         individualSlides.forEach(function(slide) {
//                             parentWrapper.removeChild(slide);
//                         });

//                         stackedSlide.classList.remove('responsive-processed');
//                     }
//                 }
//             });

//             // If using Swiper, update it after DOM changes
//             if (typeof Swiper !== 'undefined' && window.blogCustomSlider instanceof Swiper) {
//                 window.blogCustomSlider.update();
//             }
//         }
//     }

//     // Run on page load
//     handleBlogSliderResponsive();

//     // Run on window resize
//     let resizeTimer;
//     window.addEventListener('resize', function() {
//         clearTimeout(resizeTimer);
//         resizeTimer = setTimeout(handleBlogSliderResponsive, 250);
//     });

//     // Initialize the blog custom slider with Swiper if it exists
//     const blogSliderContainer = document.querySelector('.blog-custom-slider');
//     if (blogSliderContainer && typeof Swiper !== 'undefined') {
//         window.blogCustomSlider = new Swiper('.blog-custom-slider', {
//             slidesPerView: 'auto',
//             spaceBetween: 20,
//             navigation: {
//                 nextEl: '.blog-swiper-button-next',
//                 prevEl: '.blog-swiper-button-prev',
//             },
//             breakpoints: {
//                 // When window width is <= 768px
//                 768: {
//                     slidesPerView: 1,
//                     spaceBetween: 10
//                 },
//                 // When window width is <= 992px
//                 992: {
//                     slidesPerView: 2,
//                     spaceBetween: 20
//                 },

//             },
//             on: {
//                 init: function() {
//                     handleBlogSliderResponsive();
//                 }
//             }
//         });
//     }
// });

//


// staff panel slider
// $(function () {
// 	let $blogs = $(".blog-entries-container");

// 	observeOnce($blogs, () =>
// 		loadScript(["swiper"], () => {
// 			$blogs.each(function () {
// 				let $this = $(this);
// 				let $panel = $this.closest(".panel");
// 				let $cnt = $this.closest(".swiper-container-wrapper");
// 				let total_slides = $this.children().length;

// 				$panel.addClass("has-swiper");
// 				$this.addClass("swiper");
// 				$this.children().wrap(`<div class="swiper-slide" />`);
// 				$this.children().wrapAll(`<div class="swiper-wrapper" />`);
// 				// Create a wrapper div to contain both the swiper and navigation
// 				$this.wrap('<div class="swiper-container-wrapper"></div>');
// 				// Add navigation after the swiper but inside the wrapper
// 				$this.after(
// 					`<div class="swiper-navigation"><div class="swiper-button-prev"></div><div class="swiper-button-next"></div></div>`
// 				);

// 				// $cnt.append(
// 				// 	`<div class="swiper-navigation"><div class="swiper-button-prev"></div><div class="swiper-button-next"></div></div>`
// 				// );

// 				const maxRows = (n) =>
// 					$this.find(".swiper-slide").length < n * 2 ? 1 : 2;

// 				let checkNavStatus = function (s) {
// 					$this.toggleClass(
// 						"swiper-nav-disabled",
// 						!s.allowSlideNext && !s.allowSlidePrev
// 					);
// 				};

// 				// if ($(this).hasClass("partner-listings")) {
// 				// 	var opt_breakpoints = {
// 				// 		1024: {
// 				// 			slidesPerView: 5,
// 				// 		},
// 				// 		900: {
// 				// 			slidesPerView: 4,
// 				// 		},
// 				// 		769: {
// 				// 			slidesPerView: 3,
// 				// 			autoHeight: false,
// 				// 			scrollbar: {
// 				// 				dragSize: 150,
// 				// 			},
// 				// 		},
// 				// 		480: {
// 				// 			slidesPerView: 1,
// 				// 			autoHeight: false,
// 				// 		},
// 				// 	};
// 				// } else if ($(this).hasClass("staff-listing")) {
// 					var opt_breakpoints = {
// 						1024: {
// 							slidesPerView: 3,
// 							slidesPerGroup: 1,
// 							spaceBetween: 50,
// 							autoHeight: false,
// 							centerInsufficientSlides: false,
// 							loopFillGroupWithBlank: true,
// 							grid: { rows: 1, fill: 'row' }
// 						},
// 						768: {
// 							slidesPerView: 2,
// 							slidesPerGroup: 1,
// 							spaceBetween: 20,
// 							autoHeight: false,
// 							loopFillGroupWithBlank: true,
// 							grid: { rows: 1, fill: 'row' }
// 						},
// 						480: {
// 							slidesPerView: 1,
// 							slidesPerGroup: 1,
// 							spaceBetween: 20,
// 							autoHeight: false,
// 							grid: { rows: 1, fill: 'row' }
// 						},
// 						80: {
// 							slidesPerView: 1,
// 							slidesPerGroup: 1,
// 							spaceBetween: 15,
// 							autoHeight: false,
// 							grid: { rows: 1, fill: 'row' }
// 						}
// 					};
// 				// } else {
// 				// 	var opt_breakpoints = {
// 				// 		769: {
// 				// 			slidesPerView: 3,
// 				// 			autoHeight: false,
// 				// 			scrollbar: {
// 				// 				dragSize: 150,
// 				// 			},
// 				// 		},
// 				// 		480: {
// 				// 			slidesPerView: 2,
// 				// 			autoHeight: false,
// 				// 		},
// 				// 		360: {
// 				// 			slidesPerView: 1,
// 				// 			autoHeight: false,
// 				// 		}
// 				// 	};
// 				// }

// 				let swiper_params = {
// 					direction: 'horizontal',
// 					autoHeight: true,
// 					slidesPerView: 3,
// 					slidesPerGroup: 3, // Move exactly 3 slides at a time
// 					spaceBetween: 10,
// 					watchSlidesProgress: true,
// 					centerInsufficientSlides: false, // Don't center slides
// 					watchOverflow: true, // Disable navigation when all slides are visible
// 					preventInteractionOnTransition: true, // Prevent interaction during transition
// 					loopFillGroupWithBlank: true, // Fill group with blank slides
// 					normalizeSlidesGrid: true, // Normalize slide positions
// 					grid: {
// 						rows: 1,
// 						fill: 'row'
// 					},

// 					navigation: {
// 						nextEl: $this.next(".swiper-navigation").find(".swiper-button-next").get(0),
// 						prevEl: $this.next(".swiper-navigation").find(".swiper-button-prev").get(0),
// 					},
// 					simulateTouch: false,

// 					// 				slidesPerView: 2,
// 					// 				slidesPerGroup: 2,
// 					// 				roundLengths: true,
// 					// 				centerInsufficientSlides: true,

// 					// grid: {
// 					// 	fill: "row",
// 					// 	rows: maxRows(2),
// 					// },

// 					mousewheel: {
// 						forceToAxis: true,
// 						releaseOnEdges: true,
// 					},

// 					freeMode: {
// 						enabled: true,
// 						sticky: false,
// 					},

// 					// 				scrollbar: {
// 					// 					el: ".swiper-scrollbar",
// 					// 					draggable: true,
// 					// 					hide: false,
// 					// 					dragSize: 150,
// 					// 				},

// 					// breakpoints: {
// 					// 	769: {
// 					// 		slidesPerView: 3,
// 					// 		autoHeight: false,
// 					// 	},
// 					// },

// 					breakpoints: opt_breakpoints,

// 					on: {
// 						init: checkNavStatus,
// 						update: checkNavStatus,
// 					},
// 				};

// 				const swiper = new Swiper(this, swiper_params);
// 				$this.data({ swiper });

// 				// autoHeight changes from one breakpoint to another and breaks
// 				// fix by removing the inline height when changing viewports
// 				swiper.on("resize", function (swiper_obj) {
// 					if (!swiper_obj.params.autoHeight) {
// 						swiper_obj.$wrapperEl.css("height", "");
// 					}
// 				});
// 			});
// 		})
// 	);
// });

// // Find the section that handles staff swiper initialization
// $(function() {
//     let $blogs = $(".staff-listing.blog-entries-container");

//     observeOnce($blogs, () =>
//         loadScript(["swiper"], () => {
//             $blogs.each(function () {
//                 let $this = $(this);
//                 let $panel = $this.closest(".panel");

//                 // Ensure proper initialization
//                 $panel.addClass("has-swiper");
//                 $this.addClass("swiper");

//                 // Wrap slides if not already wrapped
//                 if (!$this.find('.swiper-wrapper').length) {
//                     $this.children().wrap(`<div class="swiper-slide" />`);
//                     $this.children().wrapAll(`<div class="swiper-wrapper" />`);
//                 }

//                 // Create a wrapper div if not already wrapped
//                 if (!$this.parent().hasClass('swiper-container-wrapper')) {
//                     $this.wrap('<div class="swiper-container-wrapper"></div>');
//                 }

//                 // Add navigation after the swiper but inside the wrapper
//                 if (!$this.next('.swiper-navigation').length) {
//                     $this.after(
//                         `<div class="swiper-navigation"><div class="swiper-button-prev"></div><div class="swiper-button-next"></div></div>`
//                     );
//                 }

//                 let checkNavStatus = function (s) {
//                     $this.toggleClass(
//                         "swiper-nav-disabled",
//                         !s.allowSlideNext && !s.allowSlidePrev
//                     );
//                 };

//                 // Default breakpoints
//                 let opt_breakpoints = {
//                     0: {
//                         slidesPerView: 1,
//                         slidesPerGroup: 1,
//                         spaceBetween: 10,
//                     },
// 					480:{
// 						slidesPerView: 1,
//                         slidesPerGroup: 1,
//                         spaceBetween: 10,
// 					},
//                     768: {
//                         slidesPerView: 2,
//                         slidesPerGroup: 2,
//                         spaceBetween: 15,
//                     },
//                     992: {
//                         slidesPerView: 3,
//                         slidesPerGroup: 3,
//                         spaceBetween: 20,
//                     }
//                 };

//                 let swiper_params = {
//                     direction: 'horizontal',
//                     autoHeight: true,
//                     slidesPerView: 3,
//                     slidesPerGroup: 3,
//                     spaceBetween: 10,
//                     watchSlidesProgress: true,
//                     centerInsufficientSlides: false,
//                     watchOverflow: true,
//                     preventInteractionOnTransition: true,
//                     loopFillGroupWithBlank: true,
//                     normalizeSlidesGrid: true,
//                     grid: {
//                         rows: 1,
//                         fill: 'row'
//                     },

//                     navigation: {
//                         nextEl: $this.next(".swiper-navigation").find(".swiper-button-next").get(0),
//                         prevEl: $this.next(".swiper-navigation").find(".swiper-button-prev").get(0),
//                     },
//                     simulateTouch: false,

//                     mousewheel: {
//                         forceToAxis: true,
//                         releaseOnEdges: true,
//                     },

//                     freeMode: {
//                         enabled: true,
//                         sticky: false,
//                     },

//                     breakpoints: opt_breakpoints,

//                     on: {
//                         init: function() {
//                             checkNavStatus(this);
//                             console.log('Swiper initialized with navigation');
//                         },
//                         update: checkNavStatus,
//                     },
//                 };

//                 const swiper = new Swiper(this, swiper_params);
//                 $this.data({ swiper });

//                 // Force update to ensure navigation is properly initialized
//                 setTimeout(() => {
//                     if (swiper) {
//                         swiper.update();
//                     }
//                 }, 100);

//                 // Fix for autoHeight issues
//                 swiper.on("resize", function (swiper_obj) {
//                     if (!swiper_obj.params.autoHeight) {
//                         swiper_obj.$wrapperEl.css("height", "");
//                     }
//                 });
//             });
//         })
//     );
// });

// Find the section that handles staff swiper initialization
$(function() {
    let $blogs = $(".staff-listing.blog-entries-container");

    observeOnce($blogs, () =>
        loadScript(["swiper"], () => {
            $blogs.each(function () {
                let $this = $(this);
                let $panel = $this.closest(".panel");

                // Ensure proper initialization
                $panel.addClass("has-swiper");
                $this.addClass("swiper");

                // Wrap slides if not already wrapped
                if (!$this.find('.swiper-wrapper').length) {
                    $this.children().wrap(`<div class="swiper-slide" />`);
                    $this.children().wrapAll(`<div class="swiper-wrapper" />`);
                }

                // Create a wrapper div if not already wrapped
                if (!$this.parent().hasClass('swiper-container-wrapper')) {
                    $this.wrap('<div class="swiper-container-wrapper"></div>');
                }

                // Add navigation after the swiper but inside the wrapper
                if (!$this.next('.swiper-navigation').length) {
                    $this.after(
                        `<div class="swiper-navigation"><div class="swiper-button-prev location-scroll-arrow location-scroll-left"></div><div class="swiper-button-next location-scroll-arrow location-scroll-right"></div></div>`
                    );
                }

                let checkNavStatus = function (s) {
                    $this.toggleClass(
                        "swiper-nav-disabled",
                        !s.allowSlideNext && !s.allowSlidePrev
                    );
                };

                // Default breakpoints - fixed to ensure proper responsive behavior
                let opt_breakpoints = {
                    0: {
                        slidesPerView: 1,
                        slidesPerGroup: 1,
                        spaceBetween: 10,
                        centeredSlides: true, // Center the single slide
                    },
                    // 481: { // Changed from 480 to 481 to avoid edge case
                    //     slidesPerView: 1,
                    //     slidesPerGroup: 1, // Changed to 1 for smoother navigation
                    //     spaceBetween: 15,
                    //     centeredSlides: true,
                    // },
                    // 768: { // Changed from 768 to 769 to avoid edge case
                    //     slidesPerView: 1,
                    //     slidesPerGroup: 1, // Changed to 1 for smoother navigation
                    //     spaceBetween: 20,
                    //     centeredSlides: true,
                    // },
					700: { // Changed to 767 to show 1 slide from 0-766px
						slidesPerView: 2,
						slidesPerGroup: 1,
						spaceBetween: 25,
						centeredSlides: true,
					},
					800: { // Changed from 768 to 769 to avoid edge case
                        slidesPerView: 3,
                        slidesPerGroup: 1, // Changed to 1 for smoother navigation
                        spaceBetween: 20,
                        centeredSlides: false,
                    }
                };

                let swiper_params = {
                    direction: 'horizontal',
                    autoHeight: true,
                    slidesPerView: 3,
                    slidesPerGroup: 1, // Changed to 1 for smoother navigation
                    spaceBetween: 10,
                    watchSlidesProgress: true,
                    centerInsufficientSlides: true, // Center slides when there are fewer than slidesPerView
                    watchOverflow: true,
                    preventInteractionOnTransition: true,
                    loopFillGroupWithBlank: false, // Changed to false to avoid blank slides
                    normalizeSlidesGrid: true,
                    grid: {
                        rows: 1,
                        fill: 'row'
                    },

                    navigation: {
                        nextEl: $this.next(".swiper-navigation").find(".swiper-button-next").get(0),
                        prevEl: $this.next(".swiper-navigation").find(".swiper-button-prev").get(0),
                    },
                    simulateTouch: true, // Enable touch for mobile

                    mousewheel: {
                        forceToAxis: true,
                        releaseOnEdges: true,
                    },

                    freeMode: {
                        enabled: false, // Disable freeMode for more controlled sliding
                        sticky: false,
                    },

                    breakpoints: opt_breakpoints,

                    on: {
                        init: function() {
                            checkNavStatus(this);
                            console.log('Swiper initialized with navigation');
							this.slideTo(0, 0);
                            // Force update layout after initialization
                            setTimeout(() => {
                                this.update();
                            }, 100);
                        },
                        update: checkNavStatus,
						resize: function() {
							// Reset to first slide on resize
							this.slideTo(0, 0);
                            this.update();
							console.log('Staff slider resized, resetting to first slide');
						}
						//
                    },
                };

                const swiper = new Swiper(this, swiper_params);
                $this.data({ swiper });

                // Force update to ensure navigation is properly initialized
                setTimeout(() => {
                    if (swiper) {
                        swiper.update();
                    }
                }, 100);

                // Check window width and update swiper on resize
                $(window).on('resize', function() {
                    if (swiper) {
                        setTimeout(() => {
                            swiper.update();
                        }, 100);
                    }
                });
            });
        })
    );
});


// account related js
//Logout
$(function() {
	$(document).on('click', '.logout-btn', function() {
		dialogConfirm('Are you sure you want to logout?', function () {
			window.location = path+'modules/account/Logout.php';
		});
		return false;
	});
});

//Register Form
$(function() {
	// $("#contact-form, #landing-form").on('submit', function() {
	// 	submitForm(this, 'js/ajax/submit-form.php', function(){});
	// 	return false;
	// });
	// $("#register-btn").on('click', function() {
		$("#register-form").on('submit', function() {
		// e.preventDefault();
		// submitForm('register-form', '', function(){});

		// submitForm('#register-form', 'js/ajax/register.php', function(){});
		submitForm(this, 'js/ajax/register.php', function(){});
		return false;

		// submitForm('register-form', 'modules/account/Register.php', function(){});
	});
});

//Forgot password
$(function(){
	$("#forgot-form").on('submit', function(){
		var this_form = $(this);
		this_form.find('*[name="submitform"]').attr({'disabled':true});
		this_form.find('.alert').remove();

		$.ajax({
			url: path+'js/ajax/forgot-password.php',
			data: this_form.serialize(),
			method: 'post'
		}).done(function(data){
			this_form.find('*[name="submitform"]').removeAttr('disabled');
			if(data == 'success'){
				this_form.prepend('<div class="alert success"><p>Success! Password reset instructions have been sent.</p></div>');
			}else{
				this_form.prepend('<div class="alert error"><p>Error! '+data+'</p></div>');
			}
		}).fail(function(){
			this_form.find('*[name="submitform"]').removeAttr('disabled');
			this_form.prepend('<div class="alert error"><p>Error! Unable to submit request. Please try again.</p></div>');
		});

		return false;
	});
});

// Ensure this is inside a document ready block
$(function() { // This is a shorthand for $(document).ready()

    // Check if the USER_LOGGED_IN constant is available and is true
    // Use typeof check for robustness in case the constant wasn't defined by PHP
    if (typeof USER_LOGGED_IN !== 'undefined' && USER_LOGGED_IN === true) {

        // User is logged in. Find the 'Register' and 'Login' menu items
        // within the #main-navigation and remove them.
        $('#main-navigation ul li').each(function() {
            var $li = $(this);
            // Find the <span> element inside the link and get its text
            var linkText = $li.find('a span').text().trim();

            // Check if the text matches 'Register' or 'Login' exactly
            if (linkText === 'Register' || linkText === 'Login') {
                // Remove the entire <li> element
                $li.remove();
            }
        });

        // Optional: If your menu structure places Account/Register/Login
        // under a parent like 'Account', you might want to hide the 'Account'
        // parent if Register and Login are the *only* sub-items shown when logged out.
        // However, based on the screenshot, Register and Login seem to be
        // within a submenu of "...", so hiding just those items is sufficient.

    }

});
// end account related js

// // Profile photo deletion
// $(function(){
//     // Handle profile photo delete button click
//     $(document).on('click', '#delete-profile-photo-btn', function(e){
//         e.preventDefault();


//         var $this = $(this);
//         var filename = $this.data('filename');
//         var $photoContainer = $this.closest('.current-photo');
// 		alert('delete2');

//         // Confirm deletion
//         var this_dialog_params = default_dialog_params;
//         this_dialog_params.title = 'Confirm';
//         this_dialog_params.buttons = {
//             "Cancel": function(){
//                 $(this).dialog("close");
//             },
//             "Delete Photo": function(){
//                 var thisdialog = $(this);

//                 // Show loading state
//                 $this.addClass('loading').prop('disabled', true);

//                 // Send AJAX request
//                 $.ajax({
//                     method: "POST",
//                     url: path + "js/ajax/delete-profile-image.php",
//                     data: {
//                         filename: filename,
//                         xssid: c_xssid
//                     },
//                     dataType: 'json'
//                 }).done(function(data){
//                     thisdialog.dialog("close");

//                     if(!data.errors) {
//                         // Remove the photo container and show "no photo" message
//                         $photoContainer.fadeOut(300, function(){
//                             $photoContainer.after('<p class="no-photo-message">No current photo.</p>');
//                             $photoContainer.remove();

//                             // Update hidden field
//                             $('input[name="old_photo"]').val('');

//                             // Remove delete checkbox if it exists
//                             $('#delete_photo').prop('checked', false);
//                         });

//                         // Show success message
//                         setMiniAlert({
//                             content: data.content || '<p>Photo deleted successfully.</p>',
//                             type: 'success'
//                         });
//                     } else {
//                         // Show error message
//                         setMiniAlert({
//                             content: '<p>Error: ' + (data.message || 'Failed to delete photo.') + '</p>',
//                             type: 'error'
//                         });

//                         // Reset button state
//                         $this.removeClass('loading').prop('disabled', false);
//                     }
//                 }).fail(function(){
//                     thisdialog.dialog("close");

//                     // Show error message
//                     setMiniAlert({
//                         content: '<p>Error: Failed to connect to server.</p>',
//                         type: 'error'
//                     });

//                     // Reset button state
//                     $this.removeClass('loading').prop('disabled', false);
//                 });
//             }
//         };


//         $('<div id="dialog-box"></div>').appendTo('body')
//             .html('Are you sure you want to delete your profile photo? This cannot be undone.')
//             .dialog(this_dialog_params);
//     });
// });

// // Profile photo deletion - simplified version
// $(function(){
//     // Handle profile photo delete button click
//     $(document).on('click', '#delete-profile-photo-btn', function(e){
//         e.preventDefault();
//         console.log("Delete button clicked");

//         var $this = $(this);
//         var filename = $this.data('filename');
//         var $photoContainer = $this.closest('.current-photo');

//         // Use browser's built-in confirm dialog
//         if (confirm('Are you sure you want to delete your profile photo? This cannot be undone.')) {
//             console.log("Confirmed deletion for file:", filename);

//             // Show loading state
//             $this.addClass('loading').prop('disabled', true);
//             $this.text('Deleting...');

//             // Send AJAX request
//             $.ajax({
//                 method: "POST",
//                 url: path + "js/ajax/delete-profile-image.php",
//                 data: {
//                     filename: filename,
//                     // xssid: c_xssid
//                 },
//                 // dataType: 'json'
//             }).done(function(data){
//                 console.log("AJAX response:", data);

//                 if(!data.errors) {
//                     // Remove the photo container and show "no photo" message
//                     $photoContainer.fadeOut(300, function(){
//                         $photoContainer.after('<p class="no-photo-message">No current photo.</p>');
//                         $photoContainer.remove();

//                         // Update hidden field
//                         $('input[name="old_photo"]').val('');
//                     });

//                     // Show success message
//                     alert('Photo deleted successfully.');
//                 } else {
//                     // Show error message
//                     alert('Error: ' + (data.message || 'Failed to delete photo.'));

//                     // Reset button state
//                     $this.removeClass('loading').prop('disabled', false);
//                     $this.html('<i class="fas fa-trash-alt"></i> Delete Photo');
//                 }
//             }).fail(function(xhr, status, error){
//                 console.error("AJAX Error:", status, error);

//                 // Show error message
//                 alert('Error: Failed to connect to server.');

//                 // Reset button state
//                 $this.removeClass('loading').prop('disabled', false);
//                 $this.html('<i class="fas fa-trash-alt"></i> Delete Photo');
//             });
//         }
//     });
// });

// Profile photo deletion - simplified version
$(function(){
    $(document).on('click', '#delete-profile-photo-btn', function(e){
        e.preventDefault();
        console.log("Delete button clicked");

        var $this = $(this);
        var filename = $this.data('filename');
        var $photoContainer = $this.closest('.current-photo');

		// Find the main container for the current photo display area
		var $photoArea = $this.closest('.photo-upload-area'); // Or find the specific container holding img and button
		var $currentPhotoDiv = $this.closest('.current-photo'); // The div holding the img and button


        // Use browser's built-in confirm dialog
        if (confirm('Are you sure you want to delete your profile photo? This cannot be undone.')) {
            console.log("Confirmed deletion for file:", filename);

            // Show loading state
            $this.addClass('loading').prop('disabled', true);
            $this.text('Deleting...');

            // Send AJAX request
            $.ajax({
                method: "POST",
                url: path + "js/ajax/delete-profile-image.php",
                data: {
                    filename: filename,
                    // xssid: c_xssid
                },
                dataType: 'json'
            }).done(function(data){
                console.log("AJAX response:", data);

                if(!data.errors) {
                    // Show success message
                    // alert('Photo deleted successfully.');

                    // Force page reload to ensure everything is refreshed
                    // window.location.reload();

					// $this.removeClass('loading').prop('disabled', false);
                    // $this.html('<i class="fas fa-trash-alt"></i> Delete Photo');

					 // SUCCESS - Remove photo elements from the page visually
					 console.log("AJAX success, removing photo elements...");

					 if ($currentPhotoDiv.length) {
						  // Remove the specific container for the image and delete button
						 $currentPhotoDiv.fadeOut(400, function() { $(this).remove(); });
					 } else {
						 // Fallback if structure is different - try removing img and button directly
						 $photoArea.find('.profile-thumbnail').remove();
						 $this.remove(); // Remove the button itself
					 }

					 // Optionally display a temporary success message near the upload area
					 $photoArea.find('.upload-field').before('<p class="photo-success-message" style="color: green;">Photo deleted.</p>');
					 // Hide the message after a few seconds
					 setTimeout(function() { $photoArea.find('.photo-success-message').fadeOut().remove(); }, 3000);

					  // IMPORTANT: Update the hidden 'old_photo' field to empty
					  // This prevents the main form submission from thinking the old photo still exists
					  $photoArea.find('input[name="old_photo"]').val('');

					 // We no longer reload the page here
					 // alert('Photo deleted successfully.'); // Optional: Keep alert or use inline message
                } else {
                    // Show error message
                    alert('Error: ' + (data.message || 'Failed to delete photo.'));

                    // Reset button state
                    $this.removeClass('loading').prop('disabled', false);
                    $this.html('<i class="fas fa-trash-alt"></i> Delete Photo');
                }
            }).fail(function(xhr, status, error){
                console.error("AJAX Error:", status, error);

                // Show error message
                alert('Error: Failed to connect to server.');

                // Reset button state
                $this.removeClass('loading').prop('disabled', false);
                $this.html('<i class="fas fa-trash-alt"></i> Delete Photo');
            });
        }
    });
});

// // Add this to help debug any auto-upload functionality
// $(function() {
//     console.log("Checking for auto-upload functionality");

//     // Log any file input change events
//     $('input[type="file"]').on('change', function() {
//         console.log("File input changed:", this.name);
// 		// alert("Checking for auto-upload functionality");
//     });

//     // Log any form submissions
//     $('form').on('submit', function() {
//         console.log("Form submitted:", this.id || this.name || "unnamed form");
//     });
// });

// Add these handlers within your main $(function(){ ... }); block

// --- Facility Logo & Banner Deletion ---
$(document).on('click', '#delete-facility-logo-btn, #delete-facility-banner-btn', function(e) {
    e.preventDefault();
    console.log("Facility image delete button clicked");

    var $button = $(this); // The clicked button (logo or banner)
    var filename = $button.data('filename');
    var imageType = $button.data('imagetype'); // 'logo' or 'banner'
    var facilityId = $button.data('facilityid');
    var $photoArea = $button.closest('.photo-upload-area');
    var $currentPhotoDiv = $button.closest('.current-photo');
    var hiddenInputName = (imageType === 'logo') ? 'old_logo' : 'old_banner'; // Name of the hidden field to clear

    // Confirmation message
    var confirmMsg = 'Are you sure you want to delete this ' + imageType + '? This cannot be undone.';

    if (confirm(confirmMsg)) {
        console.log("Confirmed deletion for type:", imageType, "file:", filename, "facility ID:", facilityId);

        // Basic validation
        if (!filename || !imageType || !facilityId) {
             alert('Error: Missing data needed for deletion. Cannot proceed.');
             console.error("Missing data:", {filename, imageType, facilityId});
             return;
        }

        // Show loading state
        var originalButtonText = $button.html(); // Store original content
        $button.addClass('loading').prop('disabled', true).text('Deleting...');

        // Send AJAX request to the NEW endpoint
        $.ajax({
            method: "POST",
            url: path + "js/ajax/delete-facility-image.php", // New PHP handler
            data: {
                filename: filename,
                imagetype: imageType,     // Send the type (logo/banner)
                facilityid: facilityId,   // Send the facility ID
                // xssid: c_xssid           // Send security token if needed
            },
            dataType: 'json'
        }).done(function(data) {
            console.log("Facility Delete AJAX response:", data);

            if (!data.errors) {
                // SUCCESS - Remove elements visually
                console.log("AJAX success, removing facility", imageType, "elements...");

                if ($currentPhotoDiv.length) {
                    $currentPhotoDiv.fadeOut(400, function() { $(this).remove(); });
                } else {
                    // Fallback
                    $photoArea.find('.profile-thumbnail').remove(); // Less specific
                    $button.remove();
                }

                // Show temporary success message
                $photoArea.find('.upload-field').before('<p class="photo-success-message" style="color: green; margin-top: 5px;">' + imageType.charAt(0).toUpperCase() + imageType.slice(1) + ' deleted.</p>');
                setTimeout(function() { $photoArea.find('.photo-success-message').fadeOut().remove(); }, 3500);

                // IMPORTANT: Clear the correct hidden field
                $photoArea.find('input[name="' + hiddenInputName + '"]').val('');
                console.log("Cleared hidden input:", hiddenInputName);

            } else {
                // ERROR from server
                alert('Error deleting ' + imageType + ': ' + (data.message || 'Failed to delete.'));
                $button.removeClass('loading').prop('disabled', false).html(originalButtonText); // Restore original button text/icon
            }
        }).fail(function(xhr, status, error) {
            console.error("Facility Delete AJAX Error:", status, error, xhr.responseText);
            alert('Error: Failed to connect to server for deletion.');
            $button.removeClass('loading').prop('disabled', false).html(originalButtonText); // Restore original button text/icon
        });
    }
});

// image path changes for find facility and find pro detail pages
$(document).ready(function() {
    let currentPath = window.location.pathname;
    let $pageHero = $('#page-hero');
    let $pageFooter = $('#page-footer'); // Correctly selected

    // // Check for Hero
    // if (!$pageHero.length) {
    //     console.log("#page-hero element not found.");
	//   } else {
    //     console.log("#page-hero element Found.");
    // }

    // // Check for Footer
    // if (!$pageFooter.length) {
    //     console.log("#page-footer element not found.");
    //     // Decide if you want to return or continue for hero if footer is missing
    // } else {
    //     console.log("#page-footer element Found.");
    // }

    let urlPattern = /(\/find-facility\/|\/find-pro\/)(.+?-\d+\/)/i;

    if (urlPattern.test(currentPath)) {
        let matchedParts = currentPath.match(urlPattern);
        console.log("URL matches pattern. Base: " + matchedParts[1] + ", Dynamic part: " + matchedParts[2]);

        // --- HERO SECTION ---
        if ($pageHero.length) { // Process hero only if it exists
            let currentHeroBackgroundStyle = $pageHero.css('background');
            console.log("Original #page-hero background style:", currentHeroBackgroundStyle);

            let stringToFindInHeroStyle = "our-members/images/svg/banner.svg";
            let stringToReplaceWithInHeroStyle = "images/svg/banner.svg";
            let newHeroBackgroundValue = "";

            if (currentHeroBackgroundStyle && currentHeroBackgroundStyle.includes(stringToFindInHeroStyle)) {
                if (currentHeroBackgroundStyle.includes("url(") && currentHeroBackgroundStyle.includes(stringToFindInHeroStyle)) {
                    newHeroBackgroundValue = currentHeroBackgroundStyle.replace(stringToFindInHeroStyle, stringToReplaceWithInHeroStyle);
                    $pageHero.css('background', newHeroBackgroundValue);
                    console.log("Hero Background style changed. New value:", newHeroBackgroundValue);
                } else {
                    console.warn("For Hero: '" + stringToFindInHeroStyle + "' found, but not clearly within a url(). No change applied to be safe.");
                }
            } else {
                console.warn("For Hero: Key string part '" + stringToFindInHeroStyle + "' not found in current style. No change applied.");
            }
        }

        // --- FOOTER SECTION ---
        if ($pageFooter.length) { // Process footer only if it exists
            let currentFooterBackgroundStyle = $pageFooter.css('background'); // Get FOOTER's style
            console.log("Original #page-footer background style:", currentFooterBackgroundStyle);

            let stringToFindInFooterStyle = "our-members/images/svg/footer.svg";
            let stringToReplaceWithInFooterStyle = "images/svg/footer.svg";
            let newFooterBackgroundValue = "";

            if (currentFooterBackgroundStyle && currentFooterBackgroundStyle.includes(stringToFindInFooterStyle)) {
                if (currentFooterBackgroundStyle.includes("url(") && currentFooterBackgroundStyle.includes(stringToFindInFooterStyle)) {
                    // Use currentFooterBackgroundStyle for replacement
                    newFooterBackgroundValue = currentFooterBackgroundStyle.replace(stringToFindInFooterStyle, stringToReplaceWithInFooterStyle);
                    $pageFooter.css('background', newFooterBackgroundValue);
                    console.log("Footer Background style changed. New value:", newFooterBackgroundValue);
                } else {
                    console.warn("For Footer: '" + stringToFindInFooterStyle + "' found, but not clearly within a url(). No change applied to be safe.");
                }
            } else {
                console.warn("For Footer: Key string part '" + stringToFindInFooterStyle + "' not found in current style. No change applied.");
            }
        }

    } else {
        console.log("URL does not match the simplified pattern. No background change needed.");
    }
});


// member resource page
$(document).ready(function() {
	// Folder toggle functionality
	$(".folder-header, .folder-toggle").click(function(e) {
		if ($(e.target).closest(".folder-actions").length) return;

		var content = $(this).next(".folder-content");
		var icon = $(this).find(".folder-toggle i");

		content.slideToggle();
		icon.toggleClass("fa-chevron-down fa-chevron-up");
	});

	// File upload modal
	window.fileUploader = function(categoryId) {
		$("#file-uploader input[name=category_id]").val(categoryId);
		$("#file-uploader").dialog({
			modal: true,
			width: 600,
			height: 'auto',
			resizable: false,
			title: 'Upload File'
		});
	};

	// Upload form submission
	$("#upload-form button[name=upload]").click(function() {
		$("#upload-form").submit();
	});

	// Delete confirmation
	$(".delete-btn").click(function() {
		if (confirm("Are you sure you want to delete this file?")) {
			$(this).closest("form").submit();
		}
	});

	// File input change
	$("#file").change(function() {
		var fileName = $(this).val().split("\\").pop();
		$(this).next("label").html("<i class=\"fa fa-file\"></i> " + fileName);

		if (!$("input[name=file_name]").val()) {
			var nameWithoutExt = fileName.replace(/\.[^/.]+$/, "");
			$("input[name=file_name]").val(nameWithoutExt);
		}
	});
});

//single invoice payment/refund search
$(document).ready(function() {
    // Handle search for payments/refunds
    $('.search-bar-invoice input').on('keyup', function() {
		if($(this).val().length < 3){
			 // Clear search length < 3
			 $('.search-bar-invoice .fa-times-circle').on('click', function(e) {
				e.preventDefault();
				$('.search-bar-invoice input').val('').trigger('keyup');
			});
			return;
		}

		var searchTerm = $(this).val().toLowerCase();

        // Determine which tab is active
        var activeTab = $('.tabs-panel:visible');

        if (activeTab.length) {
            // If search is empty, show all rows
            if (searchTerm === '') {
                activeTab.find('table tr').show();
            } else {
                // Hide the header row initially
                var headerRow = activeTab.find('table tr.header-row');
                headerRow.show(); // Always show header

                // Filter other rows
                activeTab.find('table tr:not(.header-row)').each(function() {
                    var rowText = $(this).text().toLowerCase();
                    if (rowText.indexOf(searchTerm) > -1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });

                // If no results found, show a message
                if (activeTab.find('table tr:visible:not(.header-row)').length === 0) {
                    // Check if we already have a "no results" row
                    if (activeTab.find('.no-results-row').length === 0) {
                        activeTab.find('table').append('<tr class="no-results-row"><td colspan="5" class="nobg">No results found matching "' + searchTerm + '"</td></tr>');
                    } else {
                        activeTab.find('.no-results-row td').html('No results found matching "' + searchTerm + '"');
                    }
                } else {
                    // Remove the "no results" row if it exists
                    activeTab.find('.no-results-row').remove();
                }
            }
        }
    });

    // Clear search when clicking the X button
    $('.search-bar-invoice .fa-times-circle').on('click', function(e) {
        e.preventDefault();
        $('.search-bar-invoice input').val('').trigger('keyup');
    });

    // Update search when switching tabs
    $('.tabs-nav a').on('click', function() {
        // Trigger search on the newly activated tab
        setTimeout(function() {
            $('.search-bar-invoice input').trigger('keyup');
        }, 100);
    });
});

//Toggle billing profiles
function billingToggle(bool){
	if(bool == true){
	   $('#billing-profile').slideDown();
	}else{
	   $('#billing-profile').slideUp();
	}
}

//Duplicate mailing address
$(function(){
	$('#use_mailing').bind('click', function(){
		if($(this).is(':checked')){
			var address1 = $('input[name="address1"]').val();
			var address2 = $('input[name="address2"]').val();
			var city = $('input[name="city"]').val();
			var province = $('select[name="province"]').val();
			var state = $('select[name="state"]').val();
			var region = $('input[name="region"]').val();
			var postalcode = $('input[name="postal_code"]').val();
			var country = $('select[name="country"]').val();
			
			$('input[name="bill_address1"]').val(address1);
			$('input[name="bill_address2"]').val(address2);
			$('input[name="bill_city"]').val(city);
			$('select[name="bill_province"]').val(province);
			$('select[name="bill_state"]').val(state);
			$('input[name="bill_region"]').val(region);
			$('input[name="bill_postalcode"]').val(postalcode);
			$('select[name="bill_country"]').val(country);
			
			if($('select[name="province"]').is(':visible')){
				$('select[name="bill_province"]').parents('.form-field').show();
				$('select[name="bill_state"]').parents('.form-field').hide();
				$('input[name="bill_region"]').parents('.form-field').hide();
			}else if($('select[name="state"]').is(':visible')){
				$('select[name="bill_province"]').parents('.form-field').hide();
				$('select[name="bill_state"]').parents('.form-field').show();
				$('input[name="bill_region"]').parents('.form-field').hide();		 
			}else if($('input[name="region"]').is(':visible')){
				$('select[name="bill_province"]').parents('.form-field').hide();
				$('select[name="bill_state"]').parents('.form-field').hide();
				$('input[name="bill_region"]').parents('.form-field').show();
			}
			
		}else{
			$('input[name="bill_address1"]').val('');
			$('input[name="bill_address2"]').val('');
			$('input[name="bill_city"]').val('');
			$('select[name="bill_province"]').val('');
			$('select[name="bill_state"]').val('');
			$('input[name="bill_region"]').val('');
			$('input[name="bill_postalcode"]').val('');
			$('select[name="bill_country"]').val('');
		}
	});
});

//Subscribe to waiting list
function waitList(el){
	var id = $(el).data('id');
	var name = $(el).data('name');
	
	$(el).find('.fa').addClass('fa-spinner fa-spin');
	$('<div id="dialog-box"></div>').appendTo('body')
	.html('Subscribe to the waiting list for '+name+'?')
	.dialog({
		modal: true, 
		title: 'Confirm',
		autoOpen: true,
		width: 300,
		resizable: false,
		closeOnEscape: true,
		closeText: "<i class='fa fa-close'></i>",
		beforeClose: function(event, ui){
			$(el).find('.fa').removeClass('fa-spinner fa-spin');
		},
		buttons: {
			"Confirm": function(){
				$(this).dialog("close");
				$(el).find('.fa').addClass('fa-spinner fa-spin');
				$.ajax({
					url: path+'js/ajax/waitinglist.php',
					data: 'id='+id,
					method: 'post'
				}).done(function(data){
					if(data == 'success'){
						dialogAlert('Success!', 'You have been successfully added to the waiting list.', 'success');	
					}else if(data == 'login'){
						window.location = window.location.href;	 
					}else{
						dialogAlert('Error!', data, 'error');	 
					}
					$(el).find('.fa').removeClass('fa-spinner fa-spin');
				}).fail(function(){
					dialogAlert('Error!', 'Unknown error. Please try again.', 'error');
					$(el).find('.fa').removeClass('fa-spinner fa-spin');
				});
			},
			Cancel: function(){
				$(this).dialog("close");
			}
		},
		show:{effect:"drop", direction:"up", duration:200},
		hide:{effect:"drop", direction:"up", duration:200}
	});	
}

//Strip all non-numeritical characters and convert to a number
function numberfy(val){
	val = val || "0";
	return parseFloat(val.replace(/[^0-9.]/g,'')) || 0;
}

//Format number to 2 decimal points and add a dollar sign
function monify(val){
	return "$" + parseFloat(val).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
}

//Compensation values
$('#compensation-survey').on('input blur', 'input, select', function(){

	var salaryTotal = 0;
	var vacationTotal = 0;
	var benefitsTotal = 0;

	if($('#monthly_salary').val() != "" && $('#months').val() != ""){
	   salaryTotal = numberfy($('#monthly_salary').val()) * numberfy($('#months').val());
	}
	if($('#vacation_days').val() != ""){
	   vacationTotal = (salaryTotal/365) * numberfy($('#vacation_days').val());
	   benefitsTotal = benefitsTotal+vacationTotal;
	}
	if($('#lesson_total').val() != ""){
	   salaryTotal += numberfy($('#lesson_total').val());
	}
	$('input.sum').each(function(){
		if($(this).val() != ""){
			benefitsTotal = benefitsTotal+numberfy($(this).val());
		}
	});

	$('#vacation_total').html(monify(vacationTotal));
	$('#salary_total').html(monify(salaryTotal));
	$('#benefits_total').html(monify(benefitsTotal));
	$('#total').html(monify(salaryTotal+benefitsTotal));

});

//Hole in one values
$('#hio-form').on('input.sum blur, input.golfers blur, input.futuredatepicker change', 'input.sum, input.golfers, input.futuredatepicker', function(){

	var prize = 0;
	var prizes = '';
	$('input.sum').each(function(){
		if($(this).val() != ""){
			prize = prize+numberfy($(this).val());
			prizes = prizes+numberfy($(this).val())+';';
		}
	});

	var dates = 0;
	$('input.futuredatepicker').each(function(){
		if($(this).val() != ""){
		   dates++;
		}
	});

	$('#prize_total').html(monify(prize));

	var golfers = $('input.golfers').val();
	if(golfers == ''){
		golfers = 0;
	}

	if(prize > 0){
		$.ajax({
			url: path+'js/ajax/hiopremium.php',
			data: {'prizes': prizes, 'field': golfers},
			method: 'post'
		}).done(function(data){
			$('#premium_per_date').html(monify(data));
			$('#premium_total').html(monify(data*dates));
		});
	}else{
		$('#premium_per_date').html(monify(0));
		$('#premium_total').html(monify(0));
	}

});
function hioTemplate(){
	var template = $('#hio-template').html();
	$('#hio-add').before('<fieldset class="hio-course hio-new" style="display:none;">'+template+'</fieldset>');
	$('.hio-new input.number').val('');
	$('.hio-new').removeClass('hio-new').slideDown();
}
function hioDate(){
// 	$('#hio-date').before("<input type='text' name='dates[]' class='input clear futuredatepicker' value='' />");
// 	$('.input.futuredatepicker').each(function(){
// 		$(this).datepicker({
// 			dateFormat: 'MM d, yy',
// 			defaultDate: 1,
// 			minDate: 1
// 		});
// 	});

    $("#hio-date").before("<input type='text' name='dates[]' class='input clear futuredatepicker' value='' autocomplete='off' />");

    // Initialize datepicker on the new field
    $(".input.futuredatepicker").not('.hasDatepicker').each(function() {
        $(this).datepicker({
            dateFormat: "MM d, yy",
            defaultDate: 1,
            minDate: 1,
            showAnim: "slideDown",
            changeMonth: true,
            changeYear: true
        });
    });
}


//Datepicker
$('.input.datepicker').each(function () {
	$(this).datepicker({
		dateFormat: 'MM d, yy',
		defaultDate: 0,
	});
});
// $('.input.futuredatepicker').each(function () {
// 	$(this).datepicker({
// 		dateFormat: 'MM d, yy',
// 		defaultDate: 1,
// 		minDate: 1
// 	});
// });
$('.input.dateselector').each(function () {
	$(this).datepicker({
		dateFormat: 'MM d, yy',
		defaultDate: 0,
		changeMonth: true,
		changeYear: true,
		yearRange: "-100:+0",
		onChangeMonthYear:function(y, m, i){
			var d = i.selectedDay;
			$(this).datepicker('setDate', new Date(y, m-1, d));
		}
	});
});

//Delete confirm
$(function(){
	$('button.delete').bind('click', function(){
		var formel = this.form;
		$('<div id="dialog-box"></div>').appendTo('body')
		.html('Are you sure you want to permanently delete this item?')
		.dialog({
			modal: true,
			title: 'Confirm',
			autoOpen: true,
			width: 300,
			resizable: false,
			closeOnEscape: true,
			closeText: "<i class='fa fa-close'></i>",
			buttons: {
				"Confirm": function() {
					$(this).dialog("close");
					$(formel).submit();
				},
				Cancel: function() {
					$(this).dialog("close");
				}
			 },
			show:{effect:"drop", direction:"up", duration:200},
			hide:{effect:"drop", direction:"up", duration:200}
		});
	});
	$('.delete-button.confirm').on('click', function () {
		var _this = this;

		$('<div id="dialog-box"></div>').appendTo('body')
		.html('Are you sure you want to permanently delete this item?')
		.dialog({
			modal: true,
			title: 'Confirm',
			autoOpen: true,
			width: 300,
			resizable: false,
			closeOnEscape: true,
			closeText: "<i class='fa fa-close'></i>",
			buttons: {
				"Confirm": function() {
					$(this).dialog("close");
					$('<input type="hidden" name="delete" value="delete">').appendTo($(_this).closest('form'));
					$(_this).closest('form').submit();
				},
				Cancel: function() {
					$(this).dialog("close");
				}
			},
			show:{effect:"drop", direction:"up", duration:200},
			hide:{effect:"drop", direction:"up", duration:200}
		});
	});
});


$(document).ready(function($) {
    // Explicitly initialize datepickers on page load
    $(".input.futuredatepicker").each(function() {
        if (!$(this).hasClass("hasDatepicker")) {
            $(this).datepicker({
                dateFormat: "MM d, yy",
                defaultDate: 1,
                minDate: 1,
                showAnim: "slideDown",
                changeMonth: true,
                changeYear: true
            });
        }
    });

    // Re-initialize hioDate function to ensure it works
    window.hioDate = function() {
        $("#hio-date").before("<input type='text' name='dates[]' class='input clear futuredatepicker' value='' />");
        $(".input.futuredatepicker").each(function() {
            if (!$(this).hasClass("hasDatepicker")) {
                $(this).datepicker({
                    dateFormat: "MM d, yy",
                    defaultDate: 1,
                    minDate: 1,
                    showAnim: "slideDown",
                    changeMonth: true,
                    changeYear: true
                });
            }
        });
    };
});
//

$(document).ready(function() {
    if (window.location.href.includes('account/hole-one/')) {
        // $('.container').addClass('container-lg');
        $('.container .panel-text').css('max-width', '100%');
    }
});


//Add to cart
function addCartItem(el){
	var id = $(el).data('id');
	var name = $(el).data('name');

	$(el).find('.fa').addClass('fa-spinner fa-spin');
	$.ajax({
		url: path+'js/ajax/addcartitem.php',
		data: 'id='+id,
		method: 'post'
	}).done(function(data){
		$(el).find('.fa').removeClass('fa-spinner fa-spin');
		if(data == 'success'){
			loadCart();
		}else if(data == 'login'){
			window.location = window.location.href;
		}else{
			dialogAlert('Error!', data, 'error');
		}
	}).fail(function(){
		dialogAlert('Error!', 'Unable to add item to cart. Please try again.', 'error');
		$(el).find('.fa').removeClass('fa-spinner fa-spin');
	});
}

//Load shopping cart
function loadCart(){
	$.ajax({
		url: path+'js/ajax/loadcart.php',
		data: '',
		method: 'post',
		dataType: 'json'
	}).done(function(data){
		$('<div id="dialog-box" class="add-to-cart-dialog"></div>').appendTo('body')
		.html(data.html)
		.dialog({
			modal: true,
			title: 'Shopping Cart',
			autoOpen: true,
			width: 750,
			resizable: false,
			closeOnEscape: true,
			closeText: "<i class='fa fa-close'></i>",
			show:{effect:"drop", direction:"up", duration:200},
			hide:{effect:"drop", direction:"up", duration:200}
		});
		$('span.cart-total').text(data.cart_total);
	}).fail(function(){
		dialogAlert('Error!', 'Unable to load shopping cart. Please try again.', 'error');
	});
}

//Delete from cart
// function deleteCartItem(id){
// 	$('<div id="dialog-box"></div>').appendTo('body')
// 	.html('Are you sure you want to delete this item from your cart?')
// 	.dialog({
// 		modal: true,
// 		title: 'Confirm',
// 		autoOpen: true,
// 		width: 300,
// 		resizable: false,
// 		closeOnEscape: true,
// 		closeText: "<i class='fa fa-close'></i>",
// 		buttons: {
// 			"Confirm": function() {
// 				$.ajax({
// 					url: path+'js/ajax/deletecartitem.php',
// 					data: 'id='+id,
// 					method: 'post'
// 				}).done(function(data){
// 					window.location = window.location.href;
// 				}).fail(function(){
// 					dialogAlert('Error!', 'Unable to delete cart item. Please try again.', 'error');
// 				});
// 			},
// 			Cancel: function() {
// 				$(this).dialog("close");
// 			}
// 		 },
// 		show:{effect:"drop", direction:"up", duration:200},
// 		hide:{effect:"drop", direction:"up", duration:200}
// 	});
// }

//Delete from cart - IMPROVED VERSION with better dialog management
function deleteCartItem(id){
    // Check if delete dialog already exists and remove it
    if ($('#delete-confirm-dialog').length) {
        $('#delete-confirm-dialog').dialog('destroy').remove();
    }
    
    var $deleteDialog = $('<div id="delete-confirm-dialog" title="Confirm Delete"></div>')
        .html('<p><i class="fa fa-warning" style="color: #f0ad4e; margin-right: 10px;"></i>Are you sure you want to delete this item from your cart?</p>')
        .appendTo('body');
    
    $deleteDialog.dialog({
        modal: true,
        autoOpen: true,
        width: 400,
        height: 'auto',
        resizable: false,
        closeOnEscape: true,
        closeText: "<i class='fa fa-close'></i>",
        dialogClass: 'delete-cart-confirmation',
        buttons: [
            {
                text: "Delete Item",
                class: "btn btn-danger secondary",
                click: function() {
                    var $this = $(this);
                    // Show loading state
                    $this.closest('.ui-dialog').find('.btn').prop('disabled', true);
                    
                    $.ajax({
                        url: path+'js/ajax/deletecartitem.php',
                        data: 'id='+id,
                        method: 'post'
                    }).done(function(data){
                        $this.dialog("close");
                        // Refresh the cart dialog instead of reloading entire page
                        $('.add-to-cart-dialog').dialog('destroy').remove();
                        loadCart(); // Reload the cart
                    }).fail(function(){
                        $this.dialog("close");
                        dialogAlert('Error!', 'Unable to delete cart item. Please try again.', 'error');
                    });
                }
            },
            {
                text: "Cancel",
                class: "btn btn-default",
                click: function() {
                    $(this).dialog("close");
                }
            }
        ],
        close: function() {
            $(this).dialog('destroy').remove();
        },
        show: {effect: "fadeIn", duration: 200},
        hide: {effect: "fadeOut", duration: 200}
    });
}

//Add attendees
function addAttendee(){
	var template = $('.reg-attendee').html();
	$('.form-buttons').before('<div class="reg-attendee new-attendee" style="display:none;">'+template+'</div>');
	$('.new-attendee input, .new-attendee select').each(function(){
		$(this).removeClass('required').val('');
	});
	$('.new-attendee fieldset').append('<a onclick="deleteAttendee(this);" class="delete">x Remove Attendee</a>');
	$('.new-attendee').removeClass('new-attendee').slideDown(300);
}
function deleteAttendee(el){
	$(el).parents('.reg-attendee').slideUp(300, function(){
		$(this).remove();
	});
}