<?php  

$permission = $cms_settings['enhanced_seo'] && SEO_USER;
$on_pages = SECTION_ID == $_cmssections['pages'];

//SEO and master permissions
if(($seo_page_id ?? false) || $on_pages){
	
	//Set SEO variables for use in seosummary.php
	$title              = (isset($seo_field) ? ($row[$seo_field] ?? '') : ($row['name'] ?? $row['title'] ?? ''));
	$page_url           = ($row['page_url'] ?? $siteurl.$root.get_page_url($seo_page_id ?? ''));
	$default_meta_title = $on_pages ? $title.' | '.$global['company_name'] : generate_seo_title($seo_page_id, $title);

	echo '<div class="panel">
		<div class="panel-header">SEO Information
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content" id="seo-information">
			<div class="flex-container no-wrap">
				<div class="flex-column">';

				if ($permission) {
					echo '<div class="form-field">
						<label>Focus Keyword' .$CMSBuilder->tooltip('Focus Keyword', 'The focus keyword will be used to determine the SEO ranking in the SEO Analysis tab. It is recommended to use a unique keyword that is not common to any other page.'). '</label>
						<input type="text" name="focus_keyword" value="' .($row['focus_keyword'] ?? ''). '" class="input" placeholder="None" />
					</div>';
				}

					echo '<div class="form-field">
						<label class="flex-container">
							<span class="flex-column left">
								SEO Title' .$CMSBuilder->tooltip('SEO Title', 'A keyword-rich page title that will appear at the very top of your browser tab.<br/><br/><small>Optimal SEO Titles should begin with the <i>focus keyword</i> and be within a range of 55 - 70 characters.</small>').
							'</span>

							<small class="flex-column right">
								<span id="count-seo-title"' .(strlen($row['meta_title'] ?? '') > 70 ? ' class="error"' : ''). '>' .strlen($row['meta_title'] ?? ''). '</span>/70
							</small>
						</label>

						<input id="seo-title" type="text" name="meta_title" value="' .($row['meta_title'] ?? ''). '" class="input char-count-70" placeholder="Use default" />
					</div>';

				if ($permission && $on_pages) {
					echo '<div class="form-field">
						<label>Page Slug'.$CMSBuilder->tooltip("Page Slug", "A <i>page slug</i> is the part of a URL that identifies a particular page on a website. Enter a custom slug for the page".(($row['deletable'] ?? 1) ? ", or leave blank to generate your URL based on the <i>Button Text</i> field" : "").". <br><br><small>EG: <span class='inline-block'>\"".$siteurl.$root."</span><b>custom-slug-here</b>/\".<br/><br/></small>").'</label>
						<input id="seo-slug" type="text" name="slug" value="' .($row['slug'] ?? ''). '" class="input char-count-70" placeholder="'.(($row['deletable'] ?? 1) ? 'Use Button Text' : 'Use original slug').'" />
					</div>

					<div class="form-field">
						<label>Canonical URL'.$CMSBuilder->tooltip("Canonical URL", "A <i>canonical URL</i> tells search engines that several versions of the same page exist.<br/><br/>If you have pages with duplicate content, pick a primary \"preferred\" version and enter the page URL here.").'</label>
						<input id="seo-canonical" autocomplete="new-password" type="text" name="meta_canonical" value="' .($row['meta_canonical'] ?? ''). '" class="input" placeholder="Use current URL" />
					</div>';
				}

					echo '<div class="form-field">
						<label class="flex-container">
							<span class="flex-column left">
								SEO Description' .$CMSBuilder->tooltip('SEO Description', 'A keyword-rich description of the page used for search engine optimization. Will default to global website settings description if left blank.<br/><br/><small>Optional SEO descriptions should contain the <i>focus keyword</i>, and be within a range of 110 - 160 characters.</small>').
							'</span>

							<small class="flex-column right"><span id="count-seo-description"' .(strlen($row['meta_description'] ?? '') > 160 ? ' class="error"' : ''). '>' .strlen($row['meta_description'] ?? ''). '</span>/160</small>
						</label>

						<textarea id="seo-description" name="meta_description" class="textarea char-count-160" placeholder="Use default">' .($row['meta_description'] ?? ''). '</textarea>
					</div>
				</div>';

				echo '<div class="flex-column grow">';
					include("includes/widgets/seosummary.php");
				echo '</div>';

			echo '</div>
		</div>
	</div>';
}

?>