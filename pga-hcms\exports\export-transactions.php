<?php  

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../includes/utils.php");

error_reporting(E_ALL);
ini_set('display_errors', 'off');

if(isset($_GET) && USER_LOGGED_IN){

	//Define vars
	$records_arr = array();
	$csv_rows = array();
	
	$db_columns = array(); //for SELECT in query
	$table_columns = array(); //for listing label
	$alias_columns = array(); //for listing value
	$rearrange_columns = false;
	
	//Get GL Accounts
	$glaccounts = array();
	$gldefaults = array();
	$query = $db->query("SELECT * FROM `gl_accounts` ORDER BY `gl_number`");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$glaccounts[$row['gl_id']] = $row;
			if(!empty($row['item_no'])){
				$gldefaults[$row['item_no']] = $row;
			}
		}
	}

	//Set columns to get
	$table_columns = array(
		'Transaction No.',
		'Transaction Date',
		'GL No.',
		'Registration No.',
		'Invoice No.',
		'First Name',
		'Last Name',
		'Email Address',
		'Phone No.',
		'Billing Address 1',
		'Billing Address 2',
		'Billing City',
		'Billing Province',
		'Billing Postal Code',
		'Billing Country',
		'Total Amount',
		'Service Fee',
		'Payment Type',
		'Cardholder',
		'Card Type',
		'Card No.',
		'Card Expiry',
		'Response Code',
		'Transaction No.',
		'Authorization Code',
		'CVD Response',
		'Message',
		'Processed'
	);
	$alias_columns = array(
		'record_number',
		'record_date',
		'gl_account',
		'registration_number',
		'invoice_number',
		'first_name',
		'last_name',
		'email',
		'phone',
		'bill_address1',
		'bill_address2',
		'bill_city',
		'bill_province',
		'bill_postalcode',
		'bill_country',
		'amount',
		'admin_fee',
		'record_type',
		'ccname',
		'cctype',
		'ccnumber',
		'ccexpiry',
		'response_code',
		'txn_num',
		'auth_code',
		'cvd_code',
		'message',
		'status'
	);
	
	//Payments and Refunds
	$record_types = array('payment', 'refund');
	foreach($record_types as $record_type){
		
		//Define vars
		$record_db = $record_type.'s';
		$record_id = $record_type.'_id';
		$record_name = ucwords($record_type);
		
		//Get records
		$params = array();
		$wheretxt = "";

		//Filters
		$date_range = '';
		if(isset($_GET['start_date']) && $_GET['start_date'] != '') {
			$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`".$record_type."_date` >= ?";
			$params[] = date('Y-m-d 00:00:00', strtotime($_GET['start_date']));
			$date_range .= date('M j, Y', strtotime($_GET['start_date']));
		}
		if(isset($_GET['end_date']) && $_GET['end_date'] != '') {
			$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`".$record_type."_date` <= ?";
			$params[] = date('Y-m-d 23:59:59', strtotime($_GET['end_date']));
			$date_range .= (!empty($date_range) ? ' - ' : '').date('M j, Y', strtotime($_GET['end_date']));
		}
		if(isset($_GET['status']) && $_GET['status'] != '') {
			$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`status` = ?";
			$params[] = $_GET['status'];
		}

		//Create query
		if($record_type == 'payment'){
			$querytxt = "SELECT `$record_db`.*, `$record_db`.`".$record_type."_number` AS `record_number`, `$record_db`.`".$record_type."_date` AS `record_date`, `$record_db`.`".$record_type."_type` AS `record_type`, `reg_attendees`.`tournament_fee`, `reg_events`.`gl_id_fees`, `reg_registrations`.`registration_number`, `invoices`.`invoice_number`, IFNULL(`reg_events`.`gl_id`, `invoices`.`gl_id`) AS `gl_id`, IFNULL(`reg_registrations`.`account_id`, `invoices`.`account_id`) AS `account_id`, IFNULL(`reg_registrations`.`first_name`, `invoices`.`first_name`) AS `first_name`, IFNULL(`reg_registrations`.`last_name`, `invoices`.`last_name`) AS `last_name`, IFNULL(`reg_registrations`.`email`, `invoices`.`email`) AS `email`, IFNULL(`reg_registrations`.`phone`, `invoices`.`phone`) AS `phone` ";
			$querytxt .= "FROM $record_db ";
			$querytxt .= "LEFT JOIN `reg_registrations` ON `$record_db`.`registration_id` = `reg_registrations`.`registration_id` ";
			$querytxt .= "LEFT JOIN `reg_attendees` ON `reg_registrations`.`registration_id` = `reg_attendees`.`registration_id` ";
			$querytxt .= "LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ";
			$querytxt .= "LEFT JOIN `invoices` ON `$record_db`.`invoice_id` = `invoices`.`invoice_id`";
			$querytxt .= $wheretxt;
			$querytxt .= " GROUP BY `$record_db`.`$record_id`";
			$querytxt .= " ORDER BY `$record_db`.`".$record_type."_date`";
		}else{
			$querytxt = "SELECT `$record_db`.*, `$record_db`.`".$record_type."_number` AS `record_number`, `$record_db`.`".$record_type."_date` AS `record_date`, `$record_db`.`".$record_type."_type` AS `record_type`, `reg_attendees`.`tournament_fee`, `reg_events`.`gl_id_fees`, `reg_registrations`.`registration_number`, `invoices`.`invoice_number`, IFNULL(`reg_events`.`gl_id`, `invoices`.`gl_id`) AS `gl_id`, IFNULL(`reg_registrations`.`account_id`, `invoices`.`account_id`) AS `account_id`, IFNULL(`reg_registrations`.`first_name`, `invoices`.`first_name`) AS `first_name`, IFNULL(`reg_registrations`.`last_name`, `invoices`.`last_name`) AS `last_name`, IFNULL(`reg_registrations`.`email`, `invoices`.`email`) AS `email`, IFNULL(`reg_registrations`.`phone`, `invoices`.`phone`) AS `phone`, `payments`.`payment_number`, `payments`.`ccname`, `payments`.`cctype`, `payments`.`ccnumber`, `payments`.`ccexpiry` ";
			$querytxt .= "FROM $record_db ";
			$querytxt .= "LEFT JOIN `reg_registrations` ON `$record_db`.`registration_id` = `reg_registrations`.`registration_id` ";
			$querytxt .= "LEFT JOIN `reg_attendees` ON `reg_registrations`.`registration_id` = `reg_attendees`.`registration_id` ";
			$querytxt .= "LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ";
			$querytxt .= "LEFT JOIN `invoices` ON `$record_db`.`invoice_id` = `invoices`.`invoice_id` ";
			$querytxt .= "LEFT JOIN `payments` ON `$record_db`.`payment_id` = `payments`.`payment_id`";
			$querytxt .= $wheretxt;
			$querytxt .= " GROUP BY `$record_db`.`$record_id`";
			$querytxt .= " ORDER BY `$record_db`.`".$record_type."_date`";
		}
		
		$query = $db->query($querytxt, $params);
		if($query && !$db->error() && $db->num_rows() > 0){
			$result = $db->fetch_array();
			foreach($result as $row){
				
				//Determine total amount
				$row['amount'] = $row['amount']+$row['admin_fee']-$row['tournament_fee'];
				
				//Determine gl account
				$row['gl_account'] = NULL;
				if(!empty($row['gl_id'])){
					$row['gl_account'] = $glaccounts[$row['gl_id']]['gl_number'];
				}else{
					if(!empty($row['registration_id'])){
						$item_no = substr($row['registration_number'], -1);
						$row['gl_account'] = $gldefaults[$item_no]['gl_number'];
					}else if(!empty($row['invoice_number'])){
						$item_no = substr($row['invoice_number'], -1);
						$row['gl_account'] = $gldefaults[$item_no]['gl_number'];
					}
				}
				
				//Determine skins row
				$skins = $row;
				$skins['amount'] = $skins['tournament_fee'];
				$skins['service_fee'] = '0.00';
				if(!empty($skins['gl_id_fees'])){
					$skins['gl_account'] = $glaccounts[$skins['gl_id_fees']]['gl_number'];
				}

				//Filter by gl account
				if(isset($_GET['gl_account']) && $_GET['gl_account'] != '') {
					if($_GET['gl_account'] == $row['gl_account']){
						$records_arr[$row['record_number']] = $row;
					}else if($_GET['gl_account'] == $skins['gl_account']){
						$records_arr[$row['record_number'].'-skins'] = $skins;
					}
				}else{
					$records_arr[$row['record_number']] = $row;
					
					//Skins row
					if($row['tournament_fee'] > 0){
						$records_arr[$row['record_number'].'-skins'] = $skins;
					}
				}
							
			}
		}else{
			echo $db->error();
		}
		
	}
	

	if (!empty($records_arr)) {
		$date = [];
	
		//Sort by date
		foreach($records_arr as $key => $row){
			$date[$key] = $row['record_date'];
		}
		array_multisort($date, SORT_ASC, $records_arr);
		
		//Compile records
		foreach($records_arr as $row) {
			$data = array();
			foreach($alias_columns as $key => $column) {
				if($column == 'record_date') {
					$data[] = ($row[$column] != '' && $row[$column] != '0000-00-00' && $row[$column] != '0000-00-00 00:00:00' ? date('Y-m-d', strtotime($row[$column])) : "");
				} else if($column == 'admin_fee' || $column == 'amount') {
					$data[] = '$'.number_format($row[$column],2);
				} else if($column == 'status') {
					$data[] = ($row[$column] == '1' ? 'Yes' : 'No');
				} else if($column == 'ccexpiry' && !empty($row[$column])) {
					$data[] = substr($row[$column], 0, 2).'/'.substr($row[$column], -2, 2);
				} else {
					$data[] = (isset($row[$column]) ? $row[$column] : '');
				}
			}
			$csv_rows[] = $data;
		}
	}

	//Output CSV
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=transactions-".date("Ymd").".csv");
	header("Content-Transfer-Encoding: binary ");
	
	$fp = fopen('php://output', 'w');
	
	//Data
	fputcsv($fp, str_replace("&rsquo;", "'", $table_columns));
	foreach($csv_rows as $row) {
		fputcsv($fp, str_replace("&rsquo;", "'", $row));
	}
	fputcsv($fp, array(''));
	
	//Footer
	$footer = array('Date Exported: ', date('M j, Y'));
	fputcsv($fp, $footer);
	if(!empty($date_range)){
		$footer = array('Date Filter: ', $date_range);
		fputcsv($fp, $footer);
	}
	
	fclose($fp);

} 
?>