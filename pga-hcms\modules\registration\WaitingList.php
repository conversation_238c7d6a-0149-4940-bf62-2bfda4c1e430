<?php
	
//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == 4) {
	$get_total = $db->query("SELECT * FROM `reg_waiting_list`");
	if($get_total && !$db->error()) {
		$CMSBuilder->set_widget($_cmssections['registration-waiting-list'],  'Waiting List Total', $db->num_rows());
	}
}

if(SECTION_ID == $_cmssections['registration-waiting-list']){
	
	//Define vars
	$record_db = 'reg_waiting_list';
	$record_id = 'wait_id';
	$record_name = 'Waiting List';

	$errors = false;
	$required = array();
	
	//Get event info
	if(!empty($_GET['occurrence_id'])){
		$event = $Registration->get_occurrence($_GET['occurrence_id'], ($_POST['account_id'] ?? NULL));
	}

	//Get Records
	$records_arr = array();
	$params = array('Registered');
	$where = "";

	if($searchterm != ""){
		$where .= ($where != '' ? "AND " : "WHERE ")."`accounts`.`email` LIKE ? OR CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ? OR `reg_events`.`name` LIKE ? ";
		$params[] = '%' .$searchterm. '%';
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
	}
	if(isset($_GET['event_id']) && $_GET['event_id'] != '') {
		$where .= ($where != '' ? " AND " : " WHERE ")."`$record_db`.`event_id` = ? ";
		$params[] = $_GET['event_id'];
	}
	if(isset($_GET['occurrence_id']) && $_GET['occurrence_id'] != '') {
		$where .= ($where != '' ? " AND " : " WHERE ")."`$record_db`.`occurrence_id` = ? ";
		$params[] = $_GET['occurrence_id'];
	}

	$query = $db->query("SELECT `$record_db`.*, `$record_db`.`date_added` AS `date_requested`, `reg_events`.`name` AS `event_name`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`, `reg_occurrences`.`max_capacity`, `accounts`.`email`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `membership_categories`.`name` AS `member_category`, ".
	"(SELECT COUNT(*) FROM `reg_attendees` WHERE `reg_attendees`.`occurrence_id` = `$record_db`.`occurrence_id` AND `reg_attendees`.`reg_status` = ? AND `reg_attendees`.`partner_id` IS NULL) AS `current_capacity` ".
	"FROM `$record_db` ".
	"LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `$record_db`.`event_id` ".
	"LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `$record_db`.`occurrence_id` ".
	"LEFT JOIN `accounts` ON `accounts`.`account_id` = `$record_db`.`account_id` ".
	"LEFT JOIN `account_profiles` ON `account_profiles`.`account_id` = `$record_db`.`account_id` ".
	"LEFT JOIN `membership_categories` ON `account_profiles`.`category_id` = `membership_categories`.`category_id` ".
	$where. "GROUP BY `$record_db`.`$record_id` ORDER BY -`$record_db`.`priority` DESC, `$record_db`.`date_added` ASC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}
	
	//Update priority numbers
	if(isset($_POST['save'])){
		
		$db->new_transaction();
		foreach($_POST['priority'] as $wait_id=>$num){
			$db->query("UPDATE `$record_db` SET `priority` = ?, `last_updated` = ? WHERE `$record_id` = ?", array(($num ?: NULL), date('Y-m-d H:i:s'), $wait_id));
		}	
		if(!$db->error()){
			$db->commit();
			$CMSBuilder->set_system_alert('Priority numbers were successfully updated.', true);	
			header('Location: '.PAGE_URL.(!empty($_SERVER['QUERY_STRING']) ? '?'.$_SERVER['QUERY_STRING'] : ''));
			exit();
		}else{
			$CMSBuilder->set_system_alert('Unable to update priority numbers. '.$db->error(), false);		
		}
		
	//Subscribe to list
	}else if(isset($_POST['subscribe'])){
		$attendee_name = $_POST['first_name'].' '.$_POST['last_name'].' (No. '.$_POST['account_id'].')';
		
		//No account selected
		if(empty($_POST['account_id'])){
			$CMSBuilder->set_system_alert('No attendee selected.', false);
			
		//Already registered
		}else if($event['isregistered']){
			$CMSBuilder->set_system_alert($attendee_name.' is already registered.', false);
			
		//Ineligible gender
		}else if(!$event['isgender'] || ($event['event_type'] == 2 && !$event['eligible'])){
			$CMSBuilder->set_system_alert($attendee_name.' is not eligible.', false);
		
		//Subscribe to waitlist
		}else{
			try{
				$Registration->wait_list_subscribe($event['event_id'], $event['occurrence_id'], $_POST['account_id']);
				$CMSBuilder->set_system_alert($attendee_name.' was successfully added to waiting list.', true);	
				header('Location: '.PAGE_URL.(!empty($_SERVER['QUERY_STRING']) ? '?'.$_SERVER['QUERY_STRING'] : ''));
				exit();
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Unable to subscribe. '.$e->getMessage(), false);
			}
		}
	}
	
	
}
?>