<?php

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

$response = array();
$response['errors'] = false;
$response['content'] = '';

if(isset($_POST) && USER_LOGGED_IN){
	extract($_POST); //get plain variables

	if(isset($item_status) && $item_status != '' && isset($item_col) && $item_col != '') {
		$db->query("UPDATE $table SET $item_col = ? WHERE $table_id = ?", array($item_status, $item_id));
	} else {
		$db->query("DELETE FROM $table WHERE $table_id = ?", array($item_id));
	}

	if(!$db->error()){
		$response['content'] = $CMSBuilder->mini_alert("<p>Item was successfully deleted!</p>",true);
	} else {
		$response['errors'] = true;
		$response['content'] = $CMSBuilder->mini_alert("<p>There was an error deleting this item: ".$db->error()."</p>",false);
	}
	
} else {
	$response['errors'] = true;
	$response['content'] = 'Unable to delete item. No data received or user is not logged in.';
}

print json_encode($response);
	
?>