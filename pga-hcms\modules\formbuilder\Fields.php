<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['form_fields']){
	
	//Define vars
	$record_db = 'form_fields';
	$record_id = 'field_id';
	$record_name = 'Field';
	
	$formpage = $CMSBuilder->get_section($_cmssections['forms']);
	define('FORM_ID', (isset($_GET['form_id']) ? $_GET['form_id'] : NULL));
	
	//Validation
	$errors = false;
	$required = array();
	$required_fields = array('label', 'type');
	
	//Form not found
	$db->query("SELECT `form_id` FROM `forms` WHERE `form_id` = ?", array(FORM_ID));
	if($db->error() || !$db->num_rows()){
		$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
		header('Location:' .$formpage['page_url']);
		exit();	
	}
	
	//Get fieldsets
	$fieldsets = array();
	$db->query("SELECT `$record_id`, `label` FROM `$record_db` WHERE `type` = ? && `form_id` = ?", array('fieldset', FORM_ID));
	if(!$db->error()){
		$fieldsets = $db->fetch_array();
	}
		
	//Get field types
	$field_types = $db->get_enum_vals('form_fields', 'type');
	$field_has_options = array('dropdown', 'checkbox', 'radio');
	
	//Get record
	if(ACTION == 'edit'){
		
		$params = array(FORM_ID, ITEM_ID);
		$db->query("SELECT `$record_db`.* FROM `$record_db` LEFT JOIN `forms` ON `$record_db`.`form_id` = `forms`.`form_id` WHERE `forms`.`form_id` = ? && `$record_db`.`$record_id` = ?", $params);
		if(!$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$row = $result[0];
			
			//Get field options
			$db->query("SELECT * FROM `form_field_options` WHERE `$record_id` = ? ORDER BY `ordering`", array($row[$record_id]));
			if(!$db->error() && $db->num_rows()){
				$row['field_options'] = $db->fetch_array();
			}
			
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .$formpage['page_url']. '?action=edit&item_id=' .FORM_ID);
			exit();
		}
		
	}
	
	//Delete item
	if(isset($_POST['delete'])){
		
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(), false);	
		}
			
		//Send back to form page
		header("Location: " .$formpage['page_url']. "?action=edit&item_id=" .FORM_ID);
		exit();	
	
	//Save item
	}else if(isset($_POST['save'])){
		
		//Validate
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}
		
		//Field options
		$_POST['field_options'] = array();

		//Standard fields
		if(in_array($_POST['type'], $field_has_options)){
			if(isset($_POST['option_name']) && is_array($_POST['option_name']) && !empty($_POST['option_name'])){
				foreach($_POST['option_name'] as $key=>$option){
					$_POST['field_options'][] = array(
						'option_name' => $_POST['option_name'][$key],
						'option_id' => $_POST['option_id'][$key],
						'ordering' => $_POST['option_ordering'][$key]
					);

					//Option name is required
					if(empty($option)){
						$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
						array_push($required, 'option_name_'.$key);
					}
				}
			}else{
				$errors[] = 'You must add at least one field option.';	
			}

		}
	
		if(!$errors){
			 			 
			//Start transaction
			$db->new_transaction();

			$params = [
				'parent_id' => ($_POST['parent_id'] ?: NULL),
				'type' => $_POST['type'],
				($_POST['type'] == 'textarea' && !empty($_POST['max_chars']) ? $_POST['max_chars'] : NULL),
				'label' => $_POST['label'],
				'required' => $_POST['required'],
				'description' => $_POST['description'],
				'send_copy' => ($_POST['type'] == 'email' && isset($_POST['send_copy']) ? 1 : 0),
				'send_copy_facility' => ($_POST['type'] == 'facilities' && isset($_POST['send_copy_facility']) ? 1 : 0),
				'ordering' => ($_POST['ordering'] ?: NULL),
				'last_updated' => date('Y-m-d H:i:s'),
			];

			$item_id = $db->insert($record_db, array_merge([
				$record_id => ITEM_ID,
				'form_id' => FORM_ID,
				'date_added' => date('Y-m-d H:i:s')
			], $params), $params);

			//Delete field options
			$option_ids = array_filter(array_column(($_POST['field_options'] ?? [] ?: []), 'option_id'));
			$filter_option_ids = implode(",", array_fill_keys(array_keys($option_ids), "?"));
			$db->query("DELETE FROM `form_field_options` WHERE `$record_id` = ?".($option_ids ? " AND `option_id` NOT IN ($filter_option_ids)" : ""), array_merge([$item_id], $option_ids));

			//Enter field options
			if(is_array($_POST['field_options'])){
				foreach($_POST['field_options'] as $option){
					$params = [
						'option_name' => $option['option_name'],
						'ordering' => ($option['ordering'] ?: 101),
						'last_updated' => date('Y-m-d H:i:s'),
					];

					$db->insert('form_field_options', array_merge([
						'option_id' => ($option['option_id'] ?: NULL),
						'field_id' => $item_id,
						'date_added' => date('Y-m-d H:i:s'),
					], $params), $params);
				}
			}
							
			//Commit changes
			if(!$db->error()){
				$db->commit();
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .$formpage['page_url']. "?action=edit&item_id=" .FORM_ID);
				exit();
			}else{
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}
	
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}
	}

}

?>