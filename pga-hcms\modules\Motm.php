<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN) {
	exit();
}

//Dashboard widget
if(SECTION_ID == 4) {
	$total_records = $db->get_record_count('account_change_log', 'showhide = 0');
	$CMSBuilder->set_widget($_cmssections['motm'], 'Members on the Move', $total_records);
}

if(SECTION_ID == $_cmssections['motm']) {

	// Define vars
	$record_db    = 'account_change_log';
	$record_id    = 'id';
	$record_name  = 'Entry';
	$records_name = 'Members on the Move';

	// Validation
	$errors   = false;
	$required = [];
	$required_fields = ['updated_on', 'comments'];

	// Facilities
	$facilities = [];
	$db->query("SELECT facility_id, facility_name AS name FROM facilities ORDER BY facility_name ASC");
	$facilities = $db->fetch_assoc('facility_id');

	// Member classes
	$member_classes = [];
	$db->query("SELECT class_id, class_name AS name FROM membership_classes ORDER BY class_name ASC");
	$member_classes = $db->fetch_assoc('class_id');

	// Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.comments",
		"account_profiles.first_name",
		"account_profiles.last_name"
	];

	// Build search query
	if ($searchterm) {
		foreach ($searchable_fields as $key => $field) {
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		// $where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' OR ', $searchable_fields).')';
	}

	// Get Records
	$db->query("SELECT
		$record_db.*,
		account_profiles.first_name,
		account_profiles.last_name
	FROM $record_db
	LEFT JOIN account_profiles ON $record_db.account_id = account_profiles.account_id
	$where
	ORDER BY $record_db.updated_on DESC, $record_db.$record_id DESC", $params);
	$records_arr = $db->fetch_assoc($record_id);

	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);
	}


	// Not found
	if(ACTION == 'edit' && empty($records_arr[ITEM_ID])) {
		$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
		header('Location:' .PAGE_URL);
		exit();
	}

	// Get updated by name for edit
	if(ACTION == 'edit' && !empty($records_arr[ITEM_ID]['updated_by'])){
		$records_arr[ITEM_ID]['updated_by_name'] = 'Admin';
		$db->query("SELECT first_name, last_name FROM account_profiles WHERE account_id = ?", [$records_arr[ITEM_ID]['updated_by']]);
		if(!$db->error() && $db->num_rows()){
			$admin = $db->fetch_array();
			$records_arr[ITEM_ID]['updated_by_name'] = $admin[0]['first_name'].' '.$admin[0]['last_name'];
		}
	}


	// Delete item
	if(isset($_POST['delete'])) {
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()) {
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		} else {
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);
		}

		header("Location: " .PAGE_URL);
		exit();


	// Save item
	} else if(isset($_POST['save'])) {

		// Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		// Validate member for new entries
		if(ACTION == 'add' && empty($_POST['account_id'])){
			$errors[] = 'Member not found. Please try another search.';
			$required[] = 'member_name';
		}

		if(!$errors) {

			// Insert to db
			$params = [
				'account_id'     => $_POST['account_id'] ?? $records_arr[ITEM_ID]['account_id'] ?? NULL,
				'field_name'     => $_POST['field_name'] ?? NULL,
				'current_value'  => $_POST['current_value'] ?? NULL,
				'previous_value' => $_POST['previous_value'] ?? NULL,
				'comments'       => $_POST['comments'],
				'showhide'       => !isset($_POST['showhide']),
				'updated_on'     => $_POST['updated_on'],
				'updated_by'     => USER_LOGGED_IN,
			];
			$db->insert($record_db, [$record_id => ITEM_ID] + $params, $params);
			if(!$db->error()) {
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			} else {
				$CMSBuilder->set_system_alert('Unable to save record.', false);
			}

		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}

	}
}

?>