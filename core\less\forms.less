/*------ forms ------*/

:root{

	// form fields
	--field-color: @font-color;
	--field-color-hover: var(--field-color);

	--field-border: @color-gray;
	--field-border-hover: @color-gray-darker;

	--field-bg: @color-light;
	--field-bg-hover: var(--field-bg);

	--field-height: 50px;
	--field-border-radius: 0;
	--field-border-width: 1px;
	--field-placeholder: @color-gray-light;
	--field-padding-inline: 20px;
	--field-padding-block: 10px;
	--field-padding: var(--field-padding-block) var(--field-padding-inline);

	// check boxes/radio
	--box-active: currentColor;
	--box-size: 1.15em;

	// form structure
	--form-row-gap: 10px;
	--form-col-gap: 10px;
	--form-columns: 1;
	
	@media @tablet-l{
		--form-columns: 2;
	}
}

label{
	&:extend(.font-caption); 
}

// Fields
.input, .select, .textarea{
	display: block;
	width: 100%;
	padding: var(--field-padding);
	color: var(--field-color);
	border-width: var(--field-border-width);
	border-style: solid;
	border-color: var(--field-border);
	border-radius: var(--field-border-radius);
	background-color: var(--field-bg);
	.trans(background-color);
	.trans(border-color);
	.trans(color);

	.placeholder({
		color: var(--field-placeholder);
	});

	&:hover,
	&:focus{
		color: var(--field-color-hover);
		background-color: var(--field-bg-hover);
		border-color: var(--field-border-hover);
	}

	&.error{
		color: @color-error;
		border-color: @color-error;

		.placeholder({
			color: @color-error;
		});
	}

	&:disabled{
		background-color: @color-gray-lighter;
		color:  @color-gray;
	}
}

.input, .select{
	height: var(--field-height);
}

.select{
	.select-arrow();
	background-size: 13px;
	background-position: right var(--field-padding-inline) center;
	background-repeat: no-repeat;
}

.textarea{
	resize: none;
	overflow: auto;
}

// Checkboxes
.checkbox,
.radio{
	.sr-only();

	&:disabled + label{
		--field-color: @color-gray;
	}

	+ label{
		width: fit-content;
		cursor: pointer;
		color: var(--field-color);
		line-height: var(--line-normal);
		.inline-flexbox(row nowrap; flex-start; flex-start;);

		&::before,
		&::after{
			width: var(--box-size);
			height: var(--box-size);
			line-height: var(--box-size);
			text-align: center;
			box-sizing: content-box;
			order: -1;
			content: '';
			.flex(0 0 auto);
			.trans(transform);
			.trans(opacity);
		}

		&::before{
			background-color: var(--field-bg);
			outline: var(--field-border-width) solid var(--field-border);
		}

		&::after{
			color: var(--field-active);
			margin: 0 0.4em 0 calc(-1 * var(--box-size)); 
			opacity: 0;
		}
	}

	&:focus + label::before{
		--field-border-width: 2px;
	}

	&:disabled + label{
		text-decoration: line-through;
		cursor: not-allowed;
	}
}
.radio{
	+ label::before,
	+ label::after{
		border-radius: 50%; 
	}

	+ label::after{
		background-color: currentColor;
		.scale(0);
	}

	&:checked + label::after{
		opacity: 1;
		.scale(0.5);
	}
}
.checkbox{
	+ label::after{
		.scale(0.5);
		.font-awesome(f00c);
	}
	&:checked + label::after{
		opacity: 1;
		.scale(0.8);
	}
}

// Structure
form{
	display: block;

	.required{
		color: @color-error;
	}

	// Flexbox structure
	.form-field{
		width: 100%;
		margin: 0 0 var(--form-row-gap);
	}

	.form-row{
		.flexbox(row wrap; flex-start; flex-end;);
		margin: 0 calc(var(--form-col-gap) / -2);

		.form-column,
		.form-field{
			.flex(0 0 auto);
			padding: 0 calc(var(--form-col-gap) / 2);
		}

		.form-column,
		.form-field:where(:not(.full)){
			width: calc(100% / var(--form-columns));
		}

		&.padded{
			padding-top: 40px;
		}
	}

	.form-column{
		.form-field{
			width: 100%;
			margin: 0;
			padding: 0 0 var(--form-row-gap);
		}
	}
}

.button.loading{
	pointer-events: none; 

	&::before{
		all: initial;
		display: inline-block;
		color: inherit;
		margin-right: 1.5ch;
		.font-awesome(f110);
		.animation(fa-spin, 2s, linear infinite);
	}
}

/*---- recaptcha ----*/

.g-recaptcha{
	transform-origin: 0 0;
	.scale(0.9);

	div{
		margin: 0 auto;
	}
}