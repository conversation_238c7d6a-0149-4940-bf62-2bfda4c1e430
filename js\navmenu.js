class NavMenu {
	el;  			// Nav element node
	$el; 			// Nav element jQuery object
	#$headers; 		// LI parents of sub menus
	#$bin; 			// Collection bin for overflown navitems
	#$concatenated; // Currently hidden overflown navitems

	// Valid callbacks
	#eventTypes = [
		'complete',
		'dropdown-in',
		'position',
		'dropdown-out',
		'concatenated'
	];

	// Default options and documentation
	#default = {
		transition: 200, 					// Animation transition time

		dropdownOffset: 0, 					/* Offset top-level dropdowns left or 
											 * right depending on positioning */

		zIndex: 100, 						// Starting z-index

		bin: '#more-link', 					/* LI to collect overflown navitems.  Leave
											 * blank to disable concatenation. */

		/* Callbacks: all are passed the API as the first parameter */
		complete: undefined,				// Called when menu has been initialized.

		position: undefined,				/* Called before a dropdown menu is activated.  
											 * Passed offset {top, left}, and stats 
											 * {lvl (page hierarchy, dir (-1 = left / 1 = right), el (element node)}. 
											 * May return {top, left} to manipulate 
											 * calculated values. */

		concatenated: undefined, 			/* Called when a navitem is concatenated into
											 * the collection bin.  Passed array of hidden
											 * top-level navitems  */

		'dropdown-in': undefined,	 		/* Called when a dropdown menu is activated,
											 * before animation.  Passed dropdown element.
											 * Return false to cancel animation. */

		'dropdown-out': undefined,	 		/* Called when a dropdown menu is hidden,
											 * before animation.  Passed dropdown element.
											 * Return false to cancel animation. */
	};

	// API options hash
	opts = {};

	/**
	 * CustomMap
	 * @constructor
	 *
	 * @param {object|string} 		container	The element containing the map; can be either a selector string, element object, or jQuery object
	 * @param {Object|undefined}	options		configuration data
	 */
	constructor(el, options = {}) {
		this.$el  = $(el);
		this.el   = this.$el[0];
		this.opts  = {...this.#default, ...options, ...this.$el.data()};
		this.#init();
		this.$el.data('NavMenu', this);
	};

	/**
	 * init
	 * @private
	 */
	#init = function () {
		this.refresh();

		let $bin = this.$el.find(this.opts.bin);
		this.#$bin = $bin.length ? $bin : undefined;
		if (this.#$bin) {
			new ResizeObserver(() => this.concat()).observe(this.el); // container is resized
		}

		this.$el.parent().addClass('navmenu-initialized');
		this.#callback('complete');
	};

	/**
	 * callback
	 * @private
	 * Execute a callback with an array of parameters, using the map
	 * element as the function context. Map object is automatically
	 * prepended as the first argument of the callback.
	 *
	 * @param {string}		eventType	Event type, which should be a key of the options object and must exist in the defined events array
	 * @param {array}		params		Array of parameters to pass the callback, to which the map object is prepended
	 */
	#callback = function (eventType, params) {
		if (this.#eventTypes.includes(eventType) && typeof this.opts[eventType] == 'function') {
			return this.opts[eventType].apply(this.el, [this].concat(params));
		}
	};

	/**
	 * positionDropdown
	 * @private
	 * Calculate and set the position of a dropdown in realtion to it's parent
	 * and the borders of the viewport.  Nav can either be position to the
	 * left, right, or bottom of it's parent.
	 *
	 * @param {ElementNode}		dropdownEl	dropdown element to position
	 */
	#positionDropdown = function (dropdownEl) {
		const $dropdown = $(dropdownEl)
		const lvl       = $dropdown.parents('ul').length;

		// Reset and grab current positions
		$dropdown.css({top: '', left: ''}).show();
		const header   = $dropdown.parent()[0].getBoundingClientRect();
		const dropdown = $dropdown[0].getBoundingClientRect();
		$dropdown.hide();

		// Calculate intended position without setting it
		const virtual = {
			right: Math.ceil(dropdown.right + header.width),
			left: Math.ceil(dropdown.left - header.width),
		};

		// Top left of dropdown
		let left = 0, top = 0, dir = 0;

		// Bottom left of header
		if (lvl == 1) {
			top = '';
			left = -this.opts.dropdownOffset;
			dir = 1;

			// Top left of dropdown to bottom left of header
			if (virtual.right >= window.innerWidth) {
				left = -dropdown.width + header.width + this.opts.dropdownOffset;
				dir = -1;
			}

		// Top Right side of header
		} else if (virtual.right < window.innerWidth) {
			left = header.width;
			dir = 1;

		// Top Left side of header
		} else if (virtual.left >= 0) {
			left = -dropdown.width;
			dir = -1;

		// Bottom center of header
		} else {
			top = header.height;
		}

		// Callback may return edits to calculated values, or false to cancel
		let position = this.#callback('position', [{top, left}, {lvl, dir, el: dropdownEl}]);
		if (position?.top !== undefined && position?.left !== undefined) {
			top = position.top;
			left = position.left;
		}

		$dropdown.css({top, left});
	};

	/**
	 * positionDropdown
	 * @private
	 * Calculate and set the position of a dropdown in relation to it's parent
	 * and the borders of the viewport.  Nav can either be position to the
	 * left, right, or bottom of it's parent.
	 *
	 * @param {ElementNode}		dropdownEl	dropdown element to position
	 */
	#toggleDropdown = function (dropdown, toggle) {
		if (toggle) {
			$(dropdown).stop().animate({
				height: 'show',
				opacity: 'show'
			}, this.opts.transition);
		} else {
			$(dropdown).stop().animate({
				height: 'hide',
				opacity: 'hide'
			}, this.opts.transition);
		}
	};

	/**
	 * refresh
	 * @public
	 * Look for new navigation items and re-hook event listeners
	 */
	refresh = function () {
		this.#$headers = this.$el.find('ul').parent('li');
		this.#$headers.children('ul').css('height', '');
		this.#$headers.off('mouseleave.navmenu mouseenter.navmenu');

		this.#$headers.each((i, header) => {
			let $header   = $(header),
				$dropdown = $header.children('ul'),
				dropdown  = $dropdown[0];

			if (dropdown) {
				$dropdown.css('z-index', this.opts.zIndex+i);
				$header.on('mouseenter.navmenu', () => {
					this.#positionDropdown(dropdown);
					if (this.#callback('dropdown-in', dropdown) !== false) {
						this.#toggleDropdown(dropdown, true);
					}

				}).on('mouseleave.navmenu', () => {
					if (this.#callback('dropdown-out', dropdown) !== false) {
						this.#toggleDropdown(dropdown, false);
					}
				});
			}
		});
	};

	/**
	 * outOfBounds
	 * @public
	 * Returns a boolean true/false if child is out of bounds of parent.
	 *
	 * @param {ElementNode}		child	element to check
	 * @param {ElementNode}		parent	element to check against
	 */
	outOfBounds = function (child, parent) {
		let a      = parent.getBoundingClientRect(),
			b      = child.getBoundingClientRect(),
			left   = Math.round(a.left) > Math.round(b.left),
			right  = Math.round(a.right) < Math.round(b.right),
			top    = Math.round(a.top) > Math.round(b.top),
			bottom = Math.round(a.bottom) < Math.round(b.bottom);
		return left || right || top || bottom;
	};

	/**
	 * concat
	 * @public
	 * Hides overflown top-level nav items and clones them into the collection bin.
	 * TODO: Ignore certain links (my account, login, mega menus, highlighted items, etc)
	 */
	concat = function () {
		const $ul = this.$el.find('> ul');
		const $li = $ul.children().not(this.#$bin);

		// Toggle element visibility
		const toggle = (el, bool) => bool ? $(el).show() : $(el).hide();

		// Check if any li are outside of the ul, starting from the back
		const isOverflown = () => $ul
			.children().filter(':visible')
			.toArray().reverse().reduce((totalWidth, li) => $(li).outerWidth(true) + totalWidth, 0) > this.$el.innerWidth();

		// Hide LIs until they fit in the UL
		$li.show();
		while ($li.not(':visible').length != $li.length && isOverflown()) {
			$li.filter(':visible').last().hide();
			this.#$bin.show();
		}

		// Check for differences and update
		let $hidden = $li.not(':visible');
		if (this.#$concatenated?.length != $hidden.length || this.#$concatenated.length != this.#$concatenated.filter($hidden).length) {
			this.#$concatenated = $hidden;
			
			// Append a copy of collected items into the collection bin
			this.#$bin.find('ul').html($hidden.clone().removeAttr('style'));
			toggle(this.#$bin, $hidden.length);

			this.refresh();
			this.#callback('concatenated', [$hidden.toArray()]);
		}
	};
}