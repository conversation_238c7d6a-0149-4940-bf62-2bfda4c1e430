<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('galleries');
	$CMSBuilder->set_widget($_cmssections['galleries'], 'Total Photo Galleries', $total_records);
}


if(SECTION_ID == $_cmssections['galleries']){

	//Define vars
	$record_db = 'galleries';
	$record_id = 'gallery_id';
	$record_name = 'Photo Gallery';

	//Validation
	$errors = false;
	$required = [];
	$required_fields = ["name"];

	//Image Uploader
	$imagedir = '../images/heroes/';
	$photosdir = '../images/galleries/';
	$CMSUploader = new CMSUploader('gallery', $imagedir);

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = ["$record_db.name"];

	//Redirects/SEO
	$seo_page_id = $_sitepages['gallery'];
	$gallery_page_url = get_page_url($seo_page_id);
	$photos_section = $CMSBuilder->get_section($_cmssections['photos']);

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;

	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get Records
	$db->query("SELECT $record_db.*, COUNT(galleries_photos.photo_id) AS photos
	FROM $record_db LEFT JOIN galleries_photos ON $record_db.$record_id = galleries_photos.$record_id
	$where
	GROUP BY $record_db.$record_id
	ORDER BY $record_db.ordering", $params);
	$records_arr = $db->fetch_assoc($record_id);
	foreach ($records_arr as $item_id => &$record) {
		$record['page_url'] = $siteurl.$root.$gallery_page_url.$record['page'].'-'.$record[$record_id].'/';
		unset($record);
	}

	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);
	}


	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];

			//Get photos
			$db->query("SELECT * FROM `galleries_photos` WHERE `gallery_id` = ? ORDER BY `ordering`", [ITEM_ID]);
			$row['gallery_photos'] = $db->fetch_assoc('photo_id');

			$records_arr[ITEM_ID] = $row;
		}
	}

	//Delete item
	if(isset($_POST['delete'])){

		//Delete from table
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", [ITEM_ID]);
		if(!$db->error()){

			//Delete banner image
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);

			//Delete gallery photos
			$CMSUploader->set_crop_type('gallery-photo', $photosdir);
			foreach($records_arr[ITEM_ID]['gallery_photos'] as $photo) {
				$CMSUploader->bulk_delete($photo);
			}

			sitemap_XML();

			$CMSBuilder->set_system_alert('Gallery was successfully deleted.', true);
			header("Location: " .PAGE_URL);
			exit();

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);
		}

	//Save item
	}else if(isset($_POST['save'])){

		//Set SEO tools if they don't exist
		$_POST['focus_keyword'] = $_POST['focus_keyword'] ?? $records_arr[ITEM_ID]['focus_keyword'] ?? NULL;

		//Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large.';
		}

		if(!$errors){

			//Clean name
			$pagename = clean_url($_POST['name']);

			//Delete banner image
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//Upload banner image
			try{
				$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			$db->new_transaction();

			//Insert to db
			$params = [

				//Insert
				ITEM_ID,
				$_POST['name'],
				$pagename,
				$_POST['content'],
				$images['image'] ?? NULL,
				$_POST['image_alt'] ?? NULL,
				$_POST['meta_title'],
				$_POST['meta_description'],
				$_POST['focus_keyword'],
				$_POST['showhide'] ?? 1,
				$_POST['ordering'],
				date("Y-m-d H:i:s"),

				//Update
				$_POST['name'],
				$pagename,
				$_POST['content'],
				$images['image'] ?? NULL,
				$_POST['image_alt'] ?? NULL,
				$_POST['meta_title'],
				$_POST['meta_description'],
				$_POST['focus_keyword'],
				$_POST['ordering'],
				date("Y-m-d H:i:s")
			];

			$db->query("INSERT INTO `$record_db`(`$record_id`, `name`, `page`, `content`, `image`, `image_alt`, `meta_title`, `meta_description`, `focus_keyword`, `showhide`, `ordering`, `last_updated`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `name` = ?, `page` = ?, `content` = ?, `image` = ?, `image_alt` = ?, `meta_title`=?, `meta_description`=?, `focus_keyword`=?, `ordering` = ?, `last_updated` = ?", $params);
			if(!$db->error()){
				$item_id = ITEM_ID ?: $db->insert_id();

				//Photos hide and ordering
				$photo_ids = $_POST['photo_id'] ?? [];

				$params = [1, 101, date('Y-m-d H:i:s'), $item_id];
				if(!empty($photo_ids)){
					$params[] = implode(",", $photo_ids);
				}
				$db->query("UPDATE `galleries_photos` SET `showhide` = ?, `ordering` = ?, `last_updated` = ? WHERE `$record_id` = ?".(!empty($photo_ids) ? " AND `photo_id` NOT IN (?)" : ""), $params);
				foreach($photo_ids as $ordering => $photo_id){
					$params = [0, ($ordering+1), date('Y-m-d H:i:s'), $item_id, $photo_id];
					$db->query("UPDATE `galleries_photos` SET `showhide` = ?, `ordering` = ?, `last_updated` = ? WHERE `$record_id` = ? AND `photo_id` = ?", $params);
				}
			}

			if(!$db->error()){
				$db->commit();

				//Update sitemap
				sitemap_XML();

				//Save SEO score
				if($cms_settings['enhanced_seo']){

					//Set new page_url
					$page_url = $siteurl.$root.$gallery_page_url.$pagename."-".$item_id."/";

					try{
						$save_score = $Analyzer->save_score($_POST['focus_keyword'], $page_url, $pagename, $_POST['name'], ($records_arr[ITEM_ID]['seo_score'] ?? NULL), $item_id, $record_db, $record_id);
						if(!empty($save_score) && (MASTER_USER || SEO_USER)){
							$seo_message = "<br/><small>".$save_score."</small>";
						}
					}catch(Exception $e){
						unset($e);
					}
				}

				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.'. (isset($seo_message) ? $seo_message : ''), true);
					header("Location: " .PAGE_URL.(ACTION == 'add' ? '?action=edit&item_id='.$item_id : ''));
					exit();
				} else if (ACTION == 'add') {
					$redirect = PAGE_URL.'?action=edit&item_id='.$item_id;
				}

			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}
		}

	//Handle images
	}else{
		include('modules/CropImages.php');
	}

}

?>