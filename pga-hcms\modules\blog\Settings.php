<?php

if(SECTION_ID == $_cmssections['blog_settings']){
	
	//Define vars
	$record_db = 'blog_settings';
	$record_id = 'blog_id';
	$record_name = $sitemap[PARENT_ID]['name'].' settings';
	
	$errors = false;
	$required = array();
	
	//Get blog settings
	$db->query("SELECT * FROM `$record_db` WHERE `$record_id` = 1");
	$row = $db->fetch_array()[0] ?? array();

	if($db->num_rows() <= 0 || $db->error()) {
		$CMSBuilder->set_system_alert('Unable to fetch settings. '.$db->error(), false);
	}

	//Save changes
	if(isset($_POST['save'])){
		
		if(!$errors){
					
			//Update blog settings
			$params = array(
				$_POST['social_sharing'],
				$_POST['comments'] ?? 0,
				$_POST['comment_approval'] ?? 1,
				$_POST['show_author'] ?? 0,
				$_POST['empty_categories'] ?? 0
			);
			$db->query("UPDATE `$record_db` SET `social_sharing` = ?, `comments` = ?, `comment_approval` = ?, `show_author` = ?, `empty_categories` = ? WHERE $record_id = 1", $params);
						
			if(!$db->error()){
				//Save sitemap
				sitemap_XML();
				
				$CMSBuilder->set_system_alert($record_name.' have been updated.', true);
				header('Location: '.PAGE_URL);
				exit();
			}else{
				$CMSBuilder->set_system_alert('Unable to update settings. '.$db->error(), false);
			}
			
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);	
		}
		
	}
}

?>