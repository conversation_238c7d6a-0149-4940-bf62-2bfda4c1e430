<?php

if(PAGE_ID == $_sitepages['registration']['page_id']){	
	
	//Define vars
	$errors = array();
	$panel_id = 58;
	
	$steps = array(1);
	define('STEP', (isset($_GET['step']) && in_array($_GET['step'], $steps) ? $_GET['step'] : 1));
	
	//Get event details
	$event = array();
	if(isset($_POST['occurrence_id'])){
		$event = $Registration->get_occurrence($_POST['occurrence_id']);
		
	//No event selected
	}else{
		header('Location: '.$_sitepages['reg_cart']['page_url']);
		exit();
	}
	
	//Event found
	if(!empty($event)){
		
		//Format data
		$data = array(
			'event_type' => $event['event_type'],
			'event_id' => $event['event_id'],
			'occurrence_id' => $_POST['occurrence_id'],
			'category_id' => $event['category_id'],
			'team_event' => $event['team_event'],
			'event_name' => $event['event_name'],
			'category_name' => $event['category_name'],
			'event_url' => ($event['event_type'] == 2 ? $_sitepages['tournaments']['page_url'] : $_sitepages['events']['page_url']).$event['page'].'-'.$_POST['occurrence_id'].'/',
			'start_date' => $event['start_date'],
			'end_date' => $event['end_date'],
			'discount' => 0,
			'fees' => $event['tournament_fee'], //Skins
			'admin_fee' => (!is_null($event['admin_fee']) ? $event['admin_fee'] : $reg_settings['admin_fee']),
			'admin_fee_type' => (!is_null($event['admin_fee']) ? $event['admin_fee_type'] : $reg_settings['admin_fee_type']),
			'promocode' => '',
			'payment_deadline' => $event['payment_deadline'],
			'attendees' => array()	
		);
	
		//Tournament registration
		if($event['event_type'] == 2){
			
			//Get overdue invoice count for user
			$overdue_invoices = $Account->overdue_invoices();
			
			//Double-check user can register
			if($event['reg_available'] && !$overdue_invoices){

				//Set attendee info to current user
				$data['attendees'][0] = array(
					'account_id' => $Account->account_id,
					'first_name' => $Account->first_name,
					'last_name' => $Account->last_name,
					'email' => $Account->email,
					'phone' => $Account->phone,
					'gender' => $Account->gender,
					'attendee_sharing' => 1,
					'ticket_type' => $event['pricing'][0]['price_type'],
					'ticket_price' => $event['pricing'][0]['price'],
					'tournament_fee' => $event['tournament_fee'],
					'discount' => 0,
					'addons' => array()
				);
				
				//Add to cart
				try{
					$ShoppingCart->add_to_cart($data);
					
					//Send to checkout
					header('Location: '.$_sitepages['reg_cart']['page_url']);
					exit();
					
				}catch(Exception $e){
					$_SESSION['reg_error'] = 'Unable to add tournament to cart. '.$e->getMessage();
				}
				
			//Overdue invoices
			}else if($overdue_invoices > 0){
				$_SESSION['reg_error'] = 'Registration is locked. Account has ' .($overdue_invoices == 1 ? 'an overdue invoice that requires' : 'overdue invoices that require'). ' payment. <a href="' .$sitemap[76]['page_url']. '">View Invoices &rsaquo;</a>';

			//Unable to register user
			}else{
				$_SESSION['reg_error'] = 'Unable to register for tournament.';
			}
			
			//Send back to tournament page
			header('Location: '.$_sitepages['tournaments']['page_url'].$event['page'].'-'.$event['occurrence_id'].'/');
			exit();
			

		//Event registration	
		}else{
			
			//Editing cart item
			$cartitem = $ShoppingCart->get_cart_item($event['occurrence_id']);
			if(!empty($cartitem) && !isset($_POST['continue'])){
				$data = $cartitem;
			}
						
			//Set capacities
			if(!is_null($event['max_attendees']) && !is_null($event['spots_available'])){
				if($event['max_attendees'] > $event['spots_available']){
					$event['max_attendees'] = $event['spots_available'];
				}
			}
			
			//Get attendee fields
			$event['attendee_information'] = $Registration->get_attendee_information($event['event_id']);
			
			//Default attendee (must have at least one)
			if(!isset($data['attendees']) || empty($data['attendees'])){
				$data['attendees'][0] = array(
					'account_id' => NULL,
					'attendee_sharing' => 1,
					'ticket_type' => NULL,
					'ticket_price' => 0,
					'discount' => 0,
					'attendee_fields' => array(),
					'addons' => array()
				);
				foreach($event['attendee_information'] as $field=>$optional){
					$data['attendees'][0]['attendee_fields'][$field] = '';
				}
			}
			
			//Get form data
			if(isset($_POST['continue'])){
				
				//Save attendees
				if(STEP == 1){

					//Validate post data
					foreach($_POST['ticket_id'] as $key=>$post){
						
						//Ticket
						if(empty($_POST['ticket_id'][$key])){
							$required[] = 'ticket_id_'.$key;
							$errors[0] = 'Please fill out all the required fields.';
						}else{
							foreach($event['pricing'] as $price){
								if($price['pricing_id'] == $_POST['ticket_id'][$key]){
									$ticket_type = $price['price_type'];
									$ticket_price = $price['price'];
									break;
								}
							}
						}
						
						//Add to data array
						if(!array_key_exists($key, $data['attendees'])){
							$data['attendees'][$key] = array(
								'account_id' => ($_POST['account_id'][$key] != '' ? $_POST['account_id'][$key] : NULL),
								'attendee_sharing' => 1,
								'ticket_type' => $ticket_type,
								'ticket_price' => $ticket_price,
								'discount' => 0,
								'attendee_fields' => array(),
								'addons' => array()
							);
						}else{
							$data['attendees'][$key]['account_id'] = ($_POST['account_id'][$key] != '' ? $_POST['account_id'][$key] : NULL);
							$data['attendees'][$key]['ticket_type'] = $ticket_type;
							$data['attendees'][$key]['ticket_price'] = $ticket_price;
						}
						
						//Attendee fields
						foreach($event['attendee_information'] as $field=>$optional){
							
							if($optional == 'Required' && trim($_POST[$field][$key]) == ''){
								$required[] = $field.'_'.$key;
								$errors[0] = 'Please fill out all the required fields.';
							}
							if($field == 'email' && trim($_POST[$field][$key]) != '' && !checkmail($_POST[$field][$key])){
								$required[] = $field.'_'.$key;
								$errors[1] = 'Please enter a valid email address.';
							}
							if($field == 'phone' && trim($_POST[$field][$key]) != ''){
								if(!detectPhone($_POST[$field][$key])){
									$required[] = $field.'_'.$key;
									$errors[2] = 'Please enter a valid phone number.';
								}else{
									$_POST[$field][$key] = formatPhoneNumber($_POST[$field][$key]);
								}
							}
							
							$data['attendees'][$key]['attendee_fields'][$field] = $_POST[$field][$key];
						}
						
						//Addons
						if(!empty($event['addons'])){
							foreach($event['addons'] as $addon){
								$data['attendees'][$key]['addons'][$addon['addon_id']] = array(
									'addon_id' => $addon['addon_id'],
									'name' => $addon['name'],
									'option_id' => $_POST['addon_'.$addon['addon_id']][$key],
									'value' => $addon['options'][$_POST['addon_'.$addon['addon_id']][$key]]['name'],
									'price_adjustment' => $addon['options'][$_POST['addon_'.$addon['addon_id']][$key]]['price_adjustment']
								);
								
								if($addon['required'] == 'Required' && trim($_POST['addon_'.$addon['addon_id']][$key]) == ''){
									$required[] = 'addon_'.$addon['addon_id'].'_'.$key;
									$errors[0] = 'Please fill out all the required fields.';
								}
							}
						}
					}
					
					//Check against capacity
					if(!is_null($event['spots_available'])){
						if(count($data['attendees']) > $event['spots_available']){
							$errors[] = 'Number of attendees exceeds the available spots for this event ('.$event['spots_available'].' available).';
						}
					}
					if(!is_null($event['max_attendees'])){
						if(count($data['attendees']) > $event['max_attendees']){
							$errors[] = 'Number of attendees exceeds the maximum allowed per registration ('.$event['max_attendees'].' maximum).';
						}
					}
					
					//Validated
					if(!$errors){
						
						//Clear old
						$ShoppingCart->delete_cart_item($event['occurrence_id']);
						
						//Add to cart
						try{
							$ShoppingCart->add_to_cart($data);

							//Send to checkout
							header('Location: '.$_sitepages['reg_cart']['page_url']);
							exit();

						}catch(Exception $e){
							$errors[] = 'Unable to add event to cart. '.$e->getMessage();
						}
							
					}
					
				}
				
			}
		
		}
	
	}
	
	//Errors
	if(!empty($errors)){
		$alert = $Account->alert(implode('<br />', $errors), false);
	}
}

?>