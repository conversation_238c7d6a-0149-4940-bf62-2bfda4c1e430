<?php

//Locations
if(!empty($global['locations'])){
	$contact_tabs = [];

	foreach($global['locations'] as $loc){
		$html = '';

		//Contact info
		$html .= '<div class="location-panel">
			<div class="contact-information">
				<h3>'.$loc['location_name'].'</h3>';

				ob_start();
				include 'includes/templates/contact-info.php';
				$html .= ob_get_clean().
				
			'</div>';

		//Business hours
		if ($loc['show_hours']) {
			$html .= '<div class="contact-hours">
				<h4 class="hours-title">Hours of Operation</h4>';

				$today   = $loc['location_hours'][date('N')-1];
				$now     = strtotime('now');
				$open    = strtotime($today['start_time']);
				$closed  = strtotime($today['end_time']);
				$closing = strtotime($today['end_time'].' - 1 hour');
				
				// Current status
				if ($today['closed'] || $now < $open || $now >= $closed) {
					$html .= '<p class="open-text closed">'.($loc['closed_text'] ?: 'Currently Closed').'</p>';
				} else if ($now >= $closing) {
					$html .= '<p class="open-text closing">'.($loc['closing_text'] ?: 'Closing Soon').'</p>';
				} else {
					$html .= '<p class="open-text open">'.($loc['open_text'] ?: 'Currently Open').'</p>';
				}

				$html .= '<table class="hours-table noresponsive">';

				foreach ($loc['location_hours'] as $hours) {
					$html .= '<tr'.($hours['day'] == date('l') ? ' class="today"' : '').'>
						<th>'.$hours['day'].'</th>
						<td>'.($hours['closed'] ? 'Closed' : date("g:ia", strtotime($hours['start_time'])).' - '.date("g:ia", strtotime($hours['end_time']))).'</td>
					</tr>';
				}

				$html .= '</table>'.
				($loc['hours_disclaimer'] ? '<small class="hours-disclaimer">' .$loc['hours_disclaimer']. '</small>' : '').
			'</div>';
		}

		$html .= '</div>';

		// Set tab attributes
		if (GOOGLE_MAP && $loc['google_map']) {
			$attrs = ' data-search="'.$loc['full_address'].'"';
			$attrs .= ' data-lat="'.$loc['gpslat'].'"';
			$attrs .= ' data-lng="'.$loc['gpslong'].'"';
			$attrs .= ' data-zoom="'.$loc['zoom'].'"';
			$attrs .= ' data-map="1"';
		} else {
			$attrs = ' data-map="0"';
		}

		$contact_tabs[] = [
			'tab_id' => $loc['location_id'],
			'title' => $loc['location_name'],
			'content' => $html,
			'page' => 'location',
			'attrs' => $attrs,
		];

		unset($loc);
	}

	//Add to panel
	if(isset($page['page_panels'][2])){
		//Set panel tab content
		if(count($global['locations']) > 1){
			$page['page_panels'][2]['panel_tabs_id'] = 'contact-locations';
			$page['page_panels'][2]['panel_tabs'] = array_merge($page['page_panels'][2]['panel_tabs'], $contact_tabs);

		//Set panel content
		}else{
			$page['page_panels'][2]['content'] .= '<div id="contact-locations">'.$contact_tabs[0]['content'].'</div>';
		}
	}
}

//Contact form
include(include_path('includes/templates/contact-form.php'));

//Set panel content
if(isset($page['page_panels'][1])){
	$page['page_panels'][1]['append_content'] = $html;
}

?>