<?php

//Global functions
include($_SERVER['DOCUMENT_ROOT'].$root."core/functions.php");

//Groups different days with the same hours together in an array
if(!function_exists('group_hours')){
	function group_hours($location_hours = []){
		$grouped_hours = [];
		$prev_hours = '';

		foreach($location_hours as $key => $hours){
			$this_hours = $hours['closed'] ? 'closed' : $hours['start_time'].' '.$hours['end_time'];

			//Days have different hours, create new group
			if($this_hours != $prev_hours){
				$prev_hours = $this_hours;
				$hours['start_day'] = $hours['day'];
				$hours['end_day'] = NULL;

				//Collect relevant data into array
				$grouped_hours[] = array_intersect_key($hours, array_flip(['start_day', 'end_day', 'start_time', 'end_time', 'closed']));
			
			//Days have same hours, update existing group
			}else{
				$grouped_hours[count($grouped_hours)-1]['end_day'] = $hours['day'];
			}
		}
		
		return $grouped_hours;
	}
}

//Load and validate recaptcha
if(!function_exists('validate_recaptcha')){
	function validate_recaptcha(){
		global $global;
		global $root;
		require_once($_SERVER['DOCUMENT_ROOT'].$root."includes/plugins/recaptcha/recaptchalib.php");
		
		$recaptcha_response = NULL;
		if(class_exists('ReCaptcha')){
			$reCaptcha = new ReCaptcha($global['recaptcha_secret']);
			$recaptcha_response = $reCaptcha->verifyResponse($_SERVER["REMOTE_ADDR"], $_POST["g-recaptcha-response"]);
		}

		return !$recaptcha_response || !$recaptcha_response->success;
	}
}

//Include file
if(!function_exists('include_path')){
	function include_path($filepath){
		global $root;
		
		//Use theme file
		if(file_exists($_SERVER['DOCUMENT_ROOT'].$root.'theme/'.$filepath)){
			return 'theme/'.$filepath;
			
		//Use standard file
		}else{
			return $filepath;
		}
	}
}

//Recursive function for nav creation
if(!function_exists('build_menu')){
	function build_menu(array $nav, int $nav_menu, array $skip_pages = [], array $extends = []){
		global $_sitepages;
		$menu = '';

		// Set defaults
		$nav['page_id']         = $nav['page_id'] ?? '';
		$nav['page']            = $nav['page'] ?? '';
		$nav['slug']            = $nav['slug'] ?? '';
		$nav['name']            = $nav['name'] ?? '';
		$nav['type']            = $nav['type'] ?? 0;
		$nav['url']             = $nav['url'] ?? '#';
		$nav['urltarget']       = $nav['urltarget'] ?? 0;
		$nav['icon']            = $nav['icon'] ?? '';
		$nav['class']           = $nav['class'] ?? '';
		$nav['page_url']        = $nav['page_url'] ?? '#';
		$nav['sub_pages']       = $nav['sub_pages'] ?? [];
		$nav['navigation_menu'] = $nav['navigation_menu'] ?? 0;

		// Extend $nav with custom data
		if($nav['page_id'] && !empty($extends[$nav['page_id']])){
			$nav = array_merge_recursive_distinct($nav, $extends[$nav['page_id']]);
		}

		// Page is in proper menu (0 = all, 1 = header, 2 = footer)
		$match_menu = $nav['navigation_menu'] == $nav_menu || $nav['navigation_menu'] == 0;
		$match_menu = $match_menu && ($nav_menu != 2 || empty($nav['parent_id'])); // Footer is top level only

		// Page is to be skipped (landing pages are always skipped)
		$skipped = in_array($nav['page_id'], $skip_pages) || $nav['type'] == 2;

		// Page is valid
		if($match_menu && !$skipped){

			// Define attrs and html
			$dropdown = array_diff(array_keys($nav['sub_pages']), $skip_pages); // gives non-empty array if there is a difference and sub_pages has value
			$active   = $nav['page_id'] && ($nav['page_id'] == PAGE_ID || $nav['page_id'] == PARENT_ID);
			$classes  = trim($nav['class'].($active ? ' active' : '').($dropdown ? ' sub-menu' : ''));
			$class    = $classes ? ' class="'.$classes.'"' : '';
			$target   = $nav['urltarget'] ? ' target="_blank" rel="noopener noreferrer"' : ' target="_self"';
			$href     = $nav['type'] ? $nav['url'] : $nav['page_url'];
			$icon     = $nav['icon'] ? '<i class="'.trim($nav['icon']).'"></i>' : '';

			// Home page - strip /home
			if($nav['page_id'] == $_sitepages['home']['page_id'] && !$nav['slug'] && $nav['page'] == 'home'){
				$nav['page_url'] = '/';
			}

			// Build menu
			$menu = '<li'.$class.'>
				<a href="'.$href.'"'.$target.'>'.$icon.'<span>'.$nav['name'].'</span></a>';

			// Recursive function
			if($dropdown){
				$menu .= '<ul>
					<li class="'.trim('menu-header '.$classes).'">
						<a href="'.$href.'"'.$target.'><span>'.$nav['name'].'</span></a>
					</li>';

				foreach($nav['sub_pages'] as $subnav){
					$menu .= build_menu($subnav, $nav_menu, $skip_pages, $extends);
				}

				$menu .= '</ul>';
			}

			$menu .= '</li>';
		}

		return $menu;
	}
}

//Format text within curly brackets
if(!function_exists('fancy_text')){
	function fancy_text($string){
		preg_match_all('/{(.*?)}/', $string, $matches);
		foreach($matches[0] as $index => $match){
			$string = str_replace($matches[0][$index], '<span class="fancy-text">'.$matches[1][$index].'</span>', $string);
		}

		return $string;
	}
}

//Create button
if(!function_exists('create_button')){
	function create_button($url='', $target=0, $text='', $class='button'){
		if($text == strip_tags($text)){
			$text = str_replace('<< ', '&laquo;', $text);
			$text = str_replace(' >>', '&raquo;', $text);
			$text = str_replace(' >', '&rsaquo;', $text);
			$text = str_replace('< ', '&lsaquo;', $text);
		}
		$text = trim($text) ?: 'Learn More';
		$mime = get_mime(pathinfo($url, PATHINFO_EXTENSION));
		
		if(detect_phone($url)){
			$url = 'tel://'.format_intl_number($url);
			$text = '<span class="phone">'.$text.'</span>';
		}else if(checkmail($url)){
			$url = 'mailto:'.$url;
			$text = '<span class="mailto">'.$text.'</span>';
		}else if($mime){
			$target = 1;
			$text = '<span class="'.$mime.'">'.$text.'</span>';
		}else if($text == strip_tags($text)){
			$text = '<span>'.$text.'</span>';
		}
		
		if(trim($url) != ''){
			$string = '<a href="'.$url.'" '.($target ? 'target="_blank" rel="noopener noreferrer"' : 'target="_self"').' class="'.$class.'">'.$text.'</a>';
		}else{
			$string = '<div class="'.$class.'">'.$text.'</div>';
		}
		
		return $string;
	}
}

//Parse referer URL (useful for AJAX requests) 
if(!function_exists('get_referer_url')){
	function get_referer_url(){
		global $path;

		$pageurl = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_PATH);
		$query_string = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_QUERY);
		if($pageurl == $path || $pageurl == $path.'index.php'){
			$pageurl = $path.'home/';
		}
		if(empty($pageurl)){
			$pathbits = array('');
		}else{
			$pathbits = explode("/",  $pageurl);
		}
			
		$shifts = explode("/", $path);
		for($i=0; $i<count($shifts)-2; $i++){
			array_shift($pathbits);
		}
			
		foreach($pathbits as $key => $bit){
			$pathbits[$key] = strip_data($bit);
			if($pathbits[$key] == "?".$query_string){
				$pageurl = str_replace($pathbits[$key], '', $pageurl);
				$pathbits[$key] = "";
			}
		}

		return array(
			'path' => $pageurl,
			'pathbits' => $pathbits,
			'query' => $query_string
		);
	}
}

if(!function_exists('empty_src')){
	function empty_src($width=NULL, $height=NULL){
		return "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'" .($width ? " width='$width'" : "").($height ? " height='$height'" : ""). "/>";
	}
}

/**
 * Returns a string of CSS vars for each file found in the provided directories.  To be used with ".responsive-bg" class
 *
 * @param	string 			$dir 			Root directory to search for files in
 * @param	string|array 	$files 			One or more filenames.  If multiple are provided, the last existing file will overwrite
 * @param	array 			$breakpoints 	Crop directories to search for files inside of $dir.  Breakpoint labels (see core.less) should be used as keys.  Default to banner config
 * @return	string 		String of css vars for each file found within cropping directories.
 */
if(!function_exists('responsive_bg')){
	function responsive_bg($dir, $files, $breakpoints=[]){
		global $path;

		$return = '';
		$files = !is_array($files) ? [$files] : array_reverse($files);

		$breakpoints = $breakpoints ?: [
			'default' => '480/',
			'tablet-p' => '768/',
			'tablet-l' => '1024/',
			'notebook' => '1366/',
			'desktop' => '1920/'
		];

		foreach($breakpoints as $key => $subdir){
			foreach($files as $file){
				if($filepath = check_file($dir.$subdir.$file)){
					$cssvar = '--bg-src'.($key != 'default' ? '-'.$key : '');
					$return .= $cssvar.': url(\''.$path.$filepath.'\');';
					break;
				}
			}
		}

		return $return;
	}
}

/**
 * Returns a string of <source/> tags for each file found in the provided directories.  To be used with the <picture/> tag
 * See responsive_bg() for parameter documentation
 * @return	string 	String of <source/> tags for each file found within cropping directories.
 */
if(!function_exists('img_sources')){
	function img_sources($dir, $files, $breakpoints=[]){
		global $path;

		$sources = '';
		$files   = !is_array($files) ? [$files] : array_reverse($files);

		$queries = [
			'tablet-p' => '(min-width: 481px)',
			'tablet-l' => '(min-width: 769px)',
			'notebook' => '(min-width: 1025px)',
			'desktop' => '(min-width: 1367px)'
		];

		$breakpoints = $breakpoints ?: [
			'desktop'  => '1920/',
			'notebook' => '1366/',
			'tablet-l' => '1024/',
			'tablet-p' => '768/',
		];

		foreach($breakpoints as $breakpoint => $subdir){
			if($query = $queries[$breakpoint] ?? ''){
				foreach($files as $file){
					if($filepath = check_file($dir.$subdir.$file)){
						$sources .= '<source srcset="'.$path.$filepath.'" media="'.$query.'">';
						break;
					}
				}
			}
		}

		return $sources;
	}
}

// Returns a <link rel="preload"/> tag for the provided file or URL.  If $as is not defined, uses the file extenstion to determine preload type.
if(!function_exists('preload')){
	function preload(string $href, string $as = '', bool $defer_css = true){
		$ext = pathinfo(preg_replace('/\?.*/', '', $href), PATHINFO_EXTENSION);

		if(!$as){
			switch ($ext){
				case 'css':
					$as = 'style';
					break;

				case 'js':
					$as = 'script';
					break;
				
				case 'jpg': case 'jpeg': case 'png': case 'gif': 
					$as = 'image';
					break;
				
				case 'eot': case 'woff2': case 'woff': case 'ttf': 
					$as = 'font';
					break;
			}
		}

		$noscript = $as == 'style' && $defer_css ? '<noscript><link rel="stylesheet" href="'.$href.'"></noscript>' : '';
		$attrs = $as == 'style' ? ' rel="stylesheet"' : ' rel="preload" as="'.$as.'"';
		$attrs .= $as == 'style' && $defer_css ? ' media="print" onload="this.onload=null;this.media=\'all\'"' : '';
		$attrs .= $as == 'font' ? ' type="font/'.$ext.'" crossorigin' : '';

		echo $as && $href ? '<link href="'.$href.'"'.$attrs.'>'.$noscript : '';
	}
}

// Modify img and iframes attributes for lazy loading
if(!function_exists('lazy_load_html')){
	function lazy_load_html(string $html){
		if ($html && class_exists('DOMDocument')) {
			libxml_use_internal_errors(true); // Comment if debugging
			$meta = '<meta http-equiv="Content-Type" content="text/html; charset=utf-8">';

			$dom = new DOMDocument();
			$dom->loadHTML($meta.$html, LIBXML_HTML_NODEFDTD);

			foreach ($dom->getElementsByTagName('img') as $img) {
				$class = $img->getAttribute('class');
				if (strstr($class, 'lazy-load') === false) {
					$img->setAttribute('class', trim($img->getAttribute('class').' lazy-load'));
					$img->setAttribute('data-src', $img->getAttribute('src'));
					$img->setAttribute('src', empty_src());
				}
			}

			$html = trim(preg_replace('/<\/?(html|body)>/', '', $dom->saveHTML()));
			$html = str_replace($meta, '', $html);
		}

		return $html;
	}
}


// if (!function_exists('checkmail')) { function checkmail($email) { return filter_var($email, FILTER_VALIDATE_EMAIL); } }
// if (!function_exists('formatPhoneNumber')) { function formatPhoneNumber($num) { /* ... your formatting logic ... */ return $num; } }
// if (!function_exists('formatIntlNumber')) { function formatIntlNumber($num) { /* ... your formatting logic ... */ return $num; } }

//Format phone number for display
if(!function_exists('formatPhoneNumber')){
	function formatPhoneNumber($number){
			
		//get ext
		$ext = trim(substr(strrchr($number, 'ext'), 4));
		
		//remove ext. if exists
		if($ext != ""){
			$number = strstr($number, 'ext', true);
		}
				
		//format number for blank canvas
		$number = stripPhoneNumber($number);
		
		//check if number starts with 1
		if(substr($number, 0, 1) == '1'){
			//remove it
			$number = substr($number, 1);
		}
		
		//grab last 7 digits (for canada/us numbers)
		$number = substr($number, 0, 3)."-".substr($number, 3, 3)."-".substr($number, 6, 4).($ext != "" ? ";ext=".$ext : "");
		
		return trim($number);
	}
}

//Format international phone number for display
if(!function_exists('formatIntlNumber')){
	function formatIntlNumber($number){
		return "+1-".formatPhoneNumber($number);
	}
}

if (!function_exists('clean_url')) { function clean_url($str) { /* ... your slug generation logic ... */ return strtolower(preg_replace('/[^a-zA-Z0-9-]+/', '-', $str)); } }

//Detect if string is a phone number
if(!function_exists('detectPhone')){
	function detectPhone($string) {
		if(!preg_match("/[a-z]/i", $string)){
			preg_match("/\+?[0-9][0-9()\-\s+]{4,20}[0-9]/i", $string, $matches);
			if(isset($matches) && !empty($matches)){
				return $matches[0];
			}
		}
		return false;
	}
}


//Format file size
if(!function_exists('formatBytes')){
	function formatBytes($size){ 
		$base = log($size) / log(1024);
		$suffix = array("", "KB", "MB", "GB", "TB");
		$f_base = floor($base);
		return round(pow(1024, $base - floor($base)), 1).' '.$suffix[$f_base];
	} 
}

//Format personnel
if(!function_exists('format_personnel')){
	function format_personnel($first, $last, $title, $url='', $photo=''){
		global $path;
		
		$html = '<div class="personnel">'.
			($url != '' ? '<a href="' .$url. '" class="circular">' : '<div class="circular">');
			if($photo != '' && file_exists('images/users/thumbs/'.$photo)){
				$html .= '<img src="' .$path.'images/users/thumbs/'.$photo. '" alt="' .$first.' '.$last. '" />';
			}else{
				$html .= '<div class="default-photo"><span>' .substr($first, 0, 1).substr($last, 0, 1). '</span></div>';
			}
			$html .= ($url != '' ? '</a>' : '</div>'). '
			<div class="details">
				<h4>' .($url != '' ? '<a href="' .$url. '">' .$first.' '.$last. '</a>' : $first.' '.$last). '</h4>
				<span>' .$title. '</span>
			</div>
		</div>';
		
		return $html;
	}
}

//Format date range
if(!function_exists('format_date_range')){
	function format_date_range($start_date, $end_date, $format_m='F', $format_d='j', $format_y='Y'){
		
		if(trim($end_date) == ''){ $end_date = $start_date; }
		
		$start_day = date("d", strtotime($start_date));
		$start_month = date("m", strtotime($start_date));
		$start_year = date("Y", strtotime($start_date));
		
		$end_day = date("d", strtotime($end_date));
		$end_month = date("m", strtotime($end_date));
		$end_year = date("Y", strtotime($end_date));
		
		if($format_y != ''){
			$format_y = ', '.$format_y;
		}
		
		//if same day
		if($start_date == $end_date){
			return date("$format_m $format_d$format_y", strtotime($start_date));
		}
		
		//if same month/year
		if($start_month == $end_month && $start_year == $end_year){
			return date("$format_m $format_d", strtotime($start_date)). ' - ' .date("$format_d$format_y", strtotime($end_date));
		}
		
		//if same year
		if($start_year == $end_year){
			return date("$format_m $format_d", strtotime($start_date)). ' - ' .date("$format_m $format_d$format_y", strtotime($end_date));
		}
		
		//basic format
		return date("$format_m $format_d$format_y", strtotime($start_date)). ' - ' .date("$format_m $format_d$format_y", strtotime($end_date));
		
	}
}

//Detect credit card type from number
if(!function_exists('get_card_type')){
	function get_card_type($str, $format='string'){
		if(empty($str)){
			return false;
		}
		$matchingPatterns = array(
			'VISA' => '/^4[0-9]{0,}$/',																			
			'MC' => '/^(5[1-5]|222[1-9]|22[3-9]|2[3-6]|27[01]|2720)[0-9]{0,}$/',
			'AMEX' => '/^3[47][0-9]{0,}$/', 
			'Diners' => '/^3(?:0[0-59]{1}|[689])[0-9]{0,}$/',
			'Discover' => '/^(6011|65|64[4-9]|62212[6-9]|6221[3-9]|622[2-8]|6229[01]|62292[0-5])[0-9]{0,}$/',
			'JCB' => '/^(?:2131|1800|35)[0-9]{0,}$/',
			'Maestro' => '/^(5[06789]|6)[0-9]{0,}$/'
		);
		$ctr = 1;
		foreach($matchingPatterns as $key=>$pattern){
			if(preg_match($pattern, $str)){
				return $format == 'string' ? $key : $ctr;
			}
			$ctr++;
		}
		
		//No match
		return ($format == 'string' ? 'Unknown' : false);
	}
}

//Detect if string is a phone number
if(!function_exists('detectPhone')){
	function detectPhone($string) {
		if(!preg_match("/[a-z]/i", $string)){
			preg_match("/\+?[0-9][0-9()\-\s+]{4,20}[0-9]/i", $string, $matches);
			if(isset($matches) && !empty($matches)){
				return $matches[0];
			}
		}
		return false;
	}
}

//Detect credit card type from number
if(!function_exists('get_card_type')){
	function get_card_type($str, $format='string'){
		if(empty($str)){
			return false;
		}
		$matchingPatterns = array(
			'VISA' => '/^4[0-9]{0,}$/',																			
			'MC' => '/^(5[1-5]|222[1-9]|22[3-9]|2[3-6]|27[01]|2720)[0-9]{0,}$/',
			'AMEX' => '/^3[47][0-9]{0,}$/', 
			'Diners' => '/^3(?:0[0-59]{1}|[689])[0-9]{0,}$/',
			'Discover' => '/^(6011|65|64[4-9]|62212[6-9]|6221[3-9]|622[2-8]|6229[01]|62292[0-5])[0-9]{0,}$/',
			'JCB' => '/^(?:2131|1800|35)[0-9]{0,}$/',
			'Maestro' => '/^(5[06789]|6)[0-9]{0,}$/'
		);
		$ctr = 1;
		foreach($matchingPatterns as $key=>$pattern){
			if(preg_match($pattern, $str)){
				return $format == 'string' ? $key : $ctr;
			}
			$ctr++;
		}
		
		//No match
		return ($format == 'string' ? 'Unknown' : false);
	}
}

//Strip special chars from phone numbers
if(!function_exists('stripPhoneNumber')){
	function stripPhoneNumber($number){
		$return = str_replace(array('+','-','.',')','(',' '), "", stripslashes($number));		
		return trim($return);
	}
}

//generate xid
if(!function_exists('gen_random_string')){
	function gen_random_string(){
		$length = 50;
		$characters = '0123456789abcdefghijklmnopqrstuvwxyz';
		$string = '';    
		for ($p = 0; $p < $length; $p++) {
			@$string .= $characters[mt_rand(0, strlen($characters))];
		}
		return md5($string);
	}
}

//Limit words function
if(!function_exists('string_limit_words')){
	function string_limit_words($string, $word_limit) {
		 $words = explode(' ', $string);
		 return implode(' ', array_slice($words, 0, $word_limit));
	}
}

//Highlight search text
if(!function_exists('highlight')){
	function highlight($text, $words) {
		$highlighted = preg_filter('/' . preg_quote($words, '/') . '/i', '<span class="found-text">$0</span>', $text);
		if (!empty($highlighted)) {
			$text = $highlighted;
		}
		return $text;
	}
}
?>