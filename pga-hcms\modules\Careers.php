<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']) {
	$total_records = $db->get_record_count('careers');
	$CMSBuilder->set_widget($_cmssections['careers'], 'Total Job Postings', $total_records);
}

if(SECTION_ID == $_cmssections['careers']) {
	
	// Define vars
	$record_db = 'careers';
	$record_id = 'career_id';
	$record_name = 'Job Posting';
	
	$filedir = "../uploads/files/";          
	$tempdir = "../docs/temp/";
	$filetypes = array('pdf');

	$attachments = [];

	$file_fields = [
		'file'
	];


	
	$seo_page_id      = $_sitepages['careers'];
	$careers_page_url = get_page_url($seo_page_id);

	$errors = false;
	$required = array();
	$required_fields = array(
		'title' => 'Title',
		'posted_date' => 'Posting Date',
		'closing_date' => 'Closing Date',
		'salary' => 'Salary Range',
		'duration' => 'Duration',
		'facility_id' => 'Facility',
		'category_id' => 'Category',
		'content' => 'Job Description',
		'first_name' => 'First Name',
		'last_name' => 'Last Name',
		'phone' => 'Phone',
		'email' => 'Email'
	); // for validation

	// Get Records
	$records_arr = array();
	$records_open = array();
	$records_closed = array();
	$params = array(' ');
	$where = "";
	
	if($searchterm != ""){
		$where .= " WHERE `careers`.`title` LIKE ? || `facilities`.`facility_name` LIKE ? || CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ? || CONCAT(`careers`.`first_name`, ?, `careers`.`last_name`) LIKE ?";
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
	}
	if(!isset($_SESSION['search_start_date'][SECTION_ID]) || isset($_POST['clear-search'])){
		$_SESSION['search_start_date'][SECTION_ID] = '';
	}
	if(!isset($_SESSION['search_end_date'][SECTION_ID]) || isset($_POST['clear-search'])){
		$_SESSION['search_end_date'][SECTION_ID] = '';
	}
	if(isset($_GET['start_date'])){
		$_SESSION['search_start_date'][SECTION_ID] = $_GET['start_date'];
	}
	if(isset($_GET['end_date'])){
		$_SESSION['search_end_date'][SECTION_ID] = $_GET['end_date'];
	}

	if(isset($_SESSION['search_start_date'][SECTION_ID]) && $_SESSION['search_start_date'][SECTION_ID] != '') {
		$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`posted_date` >= ?";
		$params[] = date('Y-m-d 00:00:00', strtotime($_SESSION['search_start_date'][SECTION_ID]));
	}
	if(isset($_SESSION['search_end_date'][SECTION_ID]) && $_SESSION['search_end_date'][SECTION_ID] != '') {
		$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`posted_date` <= ?";
		$params[] = date('Y-m-d 23:59:59', strtotime($_SESSION['search_end_date'][SECTION_ID]));
	}
	
	$query = $db->query("SELECT `careers`.*, `facilities`.`facility_name`, CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) AS `posted_by` FROM `$record_db` ".
	"LEFT JOIN `facilities` ON `careers`.`facility_id` = `facilities`.`facility_id` ".
	"LEFT JOIN `account_profiles` ON `careers`.`account_id` = `account_profiles`.`account_id` ".
	$where. " ORDER BY `careers`.`posted_date` DESC, `$record_id` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
			
			if(date("Ymd", strtotime($row['closing_date'])) >= date("Ymd")){
				$records_open[$row[$record_id]] = $row;
			}else{
				$records_closed[$row[$record_id]] = $row;
			}
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	$classes = array();
	$classes_query = $db->query("SELECT * FROM `membership_classes` WHERE `job_filter` = 1");
	if($classes_query && !$db->error() && $db->num_rows() > 0) {
		$classes = $db->fetch_array();
	}
	
	$categories = array();
	$categories_query = $db->query("SELECT * FROM `career_categories`");
	if($categories_query && !$db->error() && $db->num_rows() > 0) {
		$categories = $db->fetch_array();
	}

	$facilities = array();
	$facilities_query = $db->query("SELECT * FROM `facilities`");
	if($facilities_query && !$db->error() && $db->num_rows() > 0) {
		$facilities = $db->fetch_array();
	}


	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{	
			
			//Get classes
			$records_arr[ITEM_ID]['class_id'] = array();
			$query = $db->query("SELECT `class_id` FROM `career_classes` WHERE `career_id` = ?", array(ITEM_ID));
			if($query && !$db->error()){
				$class_result = $db->fetch_array();
				foreach($class_result as $class_row){
					$records_arr[ITEM_ID]['class_id'][] = $class_row['class_id'];
				}
			}
			
			//Get applications
			$records_arr[ITEM_ID]['applications'] = array();
			$query = $db->query("SELECT * FROM `applications` WHERE `career_id` = ? ORDER BY `timestamp` DESC", array(ITEM_ID));
			if($query && !$db->error()){
				$records_arr[ITEM_ID]['applications'] = $db->fetch_array();
			}
			
			$row = $records_arr[ITEM_ID];
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		// $db->new_transaction();
		$query = $db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		$query = $db->query("SELECT `resume` FROM `applications` WHERE `resume` IS NOT NULL && `$record_id` = ?", array(ITEM_ID));
		if($db->num_rows()){
			$result = $db->fetch_array();
			foreach($result as $file){
				if(file_exists('../docs/resumes/'.$file['resume'])){
					unlink('../docs/resumes/'.$file['resume']);
				}
			}
		}
		if(!$db->error()){
			$db->commit();	
			if($_POST['old_file'] != ''){
				if(file_exists($filedir.$_POST['old_file'])) {
					unlink($filedir.$_POST['old_file']);
				}
			}
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			//Save sitemap
			sitemap_XML();
			careers_XML($seo_page_id);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	
	//Save item
	} else if(isset($_POST['save'])){

		// Validate
		$required_missing = false;
		if(!empty($required_fields)) {
			foreach($required_fields as $field_key => $field_name) {
				if(isset($_POST[$field_key])) {
					if(trim($_POST[$field_key]) == '') {
						$required_missing = true;
						array_push($required, $field_key);
					}
				} else {
					$required_missing = true;
					array_push($required, $field_key);
				}
			}
		}
		if($required_missing) {
			$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
		}
		if(!empty($_FILES['file']['size']) && $_FILES['file']['size'] > 20480000){
			$errors[] = 'File size is too large.';
		}
		if(!empty($_FILES['file']['name'])){
			$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
			if(!in_array($ext, $filetypes)){
				$errors[] = 'File type `' .$ext. '` is restricted. Allowed file types include '.implode(', ', $filetypes).'.';
			}
		}
		if(!isset($_POST['showhide'])){
			$_POST['showhide'] = 1;
		}
		
		//If post has been approved, move temp file to public folder
		if(isset($_POST['approved']) && $_POST['old_file'] != '' && file_exists($tempdir.$_POST['old_file'])){
			if(rename($tempdir.$_POST['old_file'], $filedir.$_POST['old_file'])){
				//copied!
			}else{
				$errors[] = 'Unable to copy file to public directory. Please try again.';
			}
		}
		
		$pagename = clean_url($_POST['title']);

		if(!$errors) {
			
			//Upload file
			$file = ($_POST['old_file'] != '' ? $_POST['old_file'] : NULL);

			foreach ($file_fields as $field) {
						$$field = NULL;
					
						if (!empty($_FILES[$field]['name'])) {
							// Check for errors during upload
							if ($_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
								$errors[] = "Error uploading file: " . $_FILES[$field]['name'] . " - " . $_FILES[$field]['error'];
								continue; // Skip this file and continue with the next
							}
					
							$newname = clean_url($_POST['first_name'] . '_' . $_POST['last_name'] . '_' . $field) . '_' . date('U') . '.' . pathinfo($_FILES[$field]['name'], PATHINFO_EXTENSION);
					
							// Attempt to copy the file
							if (!@copy($_FILES[$field]['tmp_name'], $filedir . $newname)) {
								$errors[] = "Failed to upload the file: " . $_FILES[$field]['name'];
							} else {
								// Validate file exists
								$$field = check_file($newname, $filedir) ?: NULL;
					
								// Add to array for email attachment
								if ($$field) {
									$attachments[] = $filedir . $newname;
								}
							}
						}else{
							if(isset($_POST['deletefile']) && $_POST['old_file'] != ''){
								if(file_exists($filedir.$_POST['old_file'])) {
									unlink($filedir.$_POST['old_file']);
								}
								$file = NULL;
							}
						}
						
					}
			// if(!empty($_FILES['file']['name'])){
			// 	$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
			// 	$newname = $pagename.'-'.date("ymdhis").'.'.$ext;
				
			// 	// $fileUpload = new FileUpload();
			// 	// $fileUpload->load($_FILES['file']['tmp_name']);
			// 	// $fileUpload->save($filedir, $newname);
			// 	//Upload new images
				
			// 	if(file_exists($filedir.$newname)){
			// 		$file = $newname;
			// 		if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
			// 			unlink($filedir.$_POST['old_file']);
			// 		}
			// 	}
				
			// }else{
			// 	if(isset($_POST['deletefile']) && $_POST['old_file'] != ''){
			// 		if(file_exists($filedir.$_POST['old_file'])) {
			// 			unlink($filedir.$_POST['old_file']);
			// 		}
			// 		$file = NULL;
			// 	}
			// }
			
			// $db->new_transaction();
			
			//Insert to DB
			$params = array(
				ITEM_ID, 
				USER_LOGGED_IN,
				$_POST['title'], 
				$pagename, 
				$_POST['category_id'], 
				$_POST['facility_id'], 
				$_POST['content'], 
				$_POST['salary'],
				$_POST['duration'],
				$file,
				$_POST['showhide'], 
				$_POST['posted_date'],
				$_POST['closing_date'],
				$_POST['meta_title'], 
				$_POST['meta_description'],
				$_POST['focus_keyword'],
				$_POST['public'],
				isset($_POST['approved']) ? 1 : 0,
				$_POST['first_name'],
				$_POST['last_name'],
				$_POST['phone'],
				$_POST['email'],
				date("Y-m-d H:i:s"),
				date("Y-m-d H:i:s"),
				
				$_POST['title'], 
				$pagename, 
				$_POST['category_id'], 
				$_POST['facility_id'], 
				$_POST['content'], 
				$_POST['salary'],
				$_POST['duration'],
				$file,
				$_POST['showhide'], 
				$_POST['posted_date'],
				$_POST['closing_date'],
				$_POST['meta_title'], 
				$_POST['meta_description'],
				$_POST['focus_keyword'],
				$_POST['public'],
				isset($_POST['approved']) ? 1 : 0,
				$_POST['first_name'],
				$_POST['last_name'],
				$_POST['phone'],
				$_POST['email'],
				date("Y-m-d H:i:s")
			);
			$insert = $db->query("INSERT INTO `$record_db` (`$record_id`, `account_id`, `title`, `page`, `category_id`, `facility_id`, `content`, `salary`, `duration`, `file_name`, `showhide`, `posted_date`, `closing_date`, `meta_title`, `meta_description`, `focus_keyword`, `public`, `approved`, `first_name`, `last_name`, `phone`, `email`, `date_added`, `last_updated`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `title` = ?, `page` = ?, `category_id` = ?, `facility_id` = ?, `content` = ?, `salary` = ?, `duration` = ?, `file_name` = ?, `showhide` = ?, `posted_date` = ?, `closing_date` = ?, `meta_title` = ?, `meta_description` = ?, `focus_keyword` = ?, `public` = ?, `approved` = ?, `first_name` = ?, `last_name` = ?, `phone` = ?, `email` = ?, `last_updated` = ?", $params);			
			$item_id = (ITEM_ID != "" ? ITEM_ID : $db->insert_id());

			//Save class
			$delete = $db->query("DELETE FROM `career_classes` WHERE `career_id` = ?", array($item_id));
			if(isset($_POST['class_id']) && !empty($_POST['class_id'])){
				foreach($_POST['class_id'] as $class_id){
					$query = $db->query("INSERT INTO `career_classes`(`career_id`, `class_id`) VALUES(?,?) ON DUPLICATE KEY UPDATE `class_id` = ?", array($item_id, $class_id, $class_id));
				}
			}
			
			if(!$db->error()) {
				$db->commit();
				
				// sitemapXML();
				sitemap_XML();
				
				//save SEO score
				// if($cms_settings['enhanced_seo']){
				// 	//set new page_url
				// 	$page_url = $siteurl.$root.$careers_page.$pagename."-".$item_id."/";
				// 	try{
				// 		$Analyzer->set_page($_POST['focus_keyword'], $page_url, $pagename, $_POST['title'], $item_id, $record_db, $record_id);
				// 		$Analyzer->analyze_page();
				// 	}catch(Exception $e){
				// 		unset($e);
				// 	}
				// 	$Analyzer->save_score();
				// 	if(array_key_exists(3, $Account->roles) || array_key_exists(4, $Account->roles)){
				// 		$new_score = $Analyzer->get_score();
				// 		if(ITEM_ID != '' && $records_arr[ITEM_ID]['seo_score'] != $new_score){
				// 			$seo_message = "<br/><small>Page SEO score has been updated from ".(ITEM_ID == "" ? 0 : number_format($records_arr[ITEM_ID]['seo_score'],1))." to <strong>".$new_score."</strong>.</small>";
				// 		} else if(ITEM_ID != '') {
				// 			$seo_message = "<br/><small>Page SEO score has not changed from <strong>".$new_score."</strong>.</small>";
				// 		} else {
				// 			$seo_message = "<br/><small>Page SEO score is <strong>".$new_score."</strong>.</small>";
				// 		}
				// 	}
				// }

				// Save SEO score
				if($cms_settings['enhanced_seo']){
					// Set new page_url
					$page_url = $siteurl.$root.$careers_page_url.$pagename."-".$item_id."/";
		
					try {
						$save_score = $Analyzer->save_score($_POST['focus_keyword'], $page_url, $pagename, $_POST['title'], ($records_arr[ITEM_ID]['seo_score'] ?? NULL), $item_id, $record_db, $record_id);
						if(!empty($save_score) && (MASTER_USER || SEO_USER)){
							$seo_message = "<br/><small>".$save_score."</small>";
						}
					} catch(Exception $e) {
						unset($e);
					}
				}

				$CMSBuilder->set_system_alert($record_name.' was successfully saved.'. (isset($seo_message) ? $seo_message : ''), true);
				header("Location: " .PAGE_URL);
				exit();

			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	}

}

?>