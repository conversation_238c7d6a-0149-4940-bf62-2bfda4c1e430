<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

/**
 * Create URL-friendly slug from string
 */
function create_slug($string) {
    $string = preg_replace('/[^\p{L}\p{N}\s-]/u', '', $string); // Remove special chars
    $string = preg_replace('/\s+/', '-', trim($string)); // Replace spaces with -
    $string = preg_replace('/-+/', '-', $string); // Replace multiple - with single -
    return strtolower($string);
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('facilities');
	$CMSBuilder->set_widget($_cmssections['facilities'], 'Total facilities', $total_records, 'fas fa-flag');
}

if(SECTION_ID == $_cmssections['facilities']){

    //Define vars
    $record_db = 'facilities';
    $record_id = 'facility_id';
    $record_name = 'Facility';
    $record_names = 'Facilities';
	$seo_page_id     = $_sitepages['facilities'];

    //Validation
    $errors = false;
    $required = [];
    $required_fields = ['facility_name', 'type', 'region'];

	// --- Define Image Directories ---
	$logo_imagedir = "../images/logos/"; // Specific base directory for LOGOS
	$banner_imagedir = "../images/heroes/"; // Base for BANNERS
	//
	    // --- CMSUploader Instances ---
    // One for the logo, using the 'facility_logo' crop type
    // $CMSUploader = new CMSUploader('facility_logo', $logo_imagedir);

	// Two separate CMSUploader instances
	$CMSUploaderLogo = new CMSUploader('facility_logo', $logo_imagedir);
	$CMSUploaderBanner = new CMSUploader('banner', $banner_imagedir);

    // One for the banner, using a 'facility_banner' crop type (or 'banner' if reusing)
    // Assuming you create 'facility_banner' in _croptypes for the field 'image'
    // $CMSUploaderBanner = new CMSUploader('facility_banner', $banner_imagedir_base);

	//

    //Image Uploader
    // $imagedir = '../images/logos/';
    // $CMSUploader = new CMSUploader('partners', $imagedir);

    //Filtering
    $where = '';
    $params = [];
    $searchable_fields = [
        "$record_db.facility_name",
        "$record_db.region"
    ];

    //Selected item
    if(ACTION == 'edit' && ITEM_ID != ''){
        $where = "WHERE $record_db.$record_id = ? ";
        $params[] = ITEM_ID;
    }

    //Build search query
    else if(isset($_GET['search'])){
        if($searchterm){
            foreach($searchable_fields as $key => $field){
                $searchable_fields[$key] = "$field LIKE ?";
                $params[] = '%'.$searchterm.'%';
            }
            $where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
        }
    }

    //Get Records
    $db->query("SELECT * FROM `$record_db` $where ORDER BY `facility_name` ASC", $params);
    $records_arr = $db->fetch_assoc($record_id);

    // echo "<pre>";
    // print_r($records_arr);
    // echo "</pre>";
    // exit("test");

    //Process records
    // foreach($records_arr as $id => $record){
	foreach ($records_arr as $id => &$record) {

        // Format any data if needed

        // $record['page_url'] = $siteurl.$root.$blog_page_url.$record['archive'].'/'.$record['page'].'-'.$item_id.'/';
        $facility_page_url = get_page_url($_sitepages['facilities']);
        // echo $facility_page_url;
        $record['page_url'] = $fp_url = $siteurl.$root.$facility_page_url.$record['page'].'-'.$record[$record_id].'/';
        // echo "<br>";
        // echo $record['page_url'];
        // exit;
		unset($record);
    }

    //Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);

		//Delete images
		if(!$db->error()){
			$CMSUploaderLogo->bulk_delete($records_arr[ITEM_ID]);
			$CMSUploaderBanner->bulk_delete($records_arr[ITEM_ID]);
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

			//Save sitemap
			sitemap_XML();

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);
		}

		header("Location: " .PAGE_URL);
		exit();


	//Save item
	}else
    if(isset($_POST['save'])){

		//Set toggles
		// $_POST['showhide'] = !isset($_POST['showhide']);
        $_POST['showhide'] = isset($_POST['showhide']) ? 0 : 1;
        $_POST['google_map'] = isset($_POST['google_map']) ? 0 : 1;

		//Set SEO tools if they don't exist
		$_POST['focus_keyword'] = $_POST['focus_keyword'] ?? $records_arr[ITEM_ID]['focus_keyword'] ?? NULL;

		//Required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Validate image
		if(!empty($_FILES['logo']['size']) && $_FILES['logo']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large.';
			$required[] = 'logo';
		}

		//Specific validation
		if($_POST['email'] && !checkmail($_POST['email'])){
			$errors[] = 'Please enter a valid email.';
			$required[] = 'email';
		}

		$final_logo_filename = $records_arr[ITEM_ID]['logo'] ?? null; // Start with current DB value
        $final_banner_filename = $records_arr[ITEM_ID]['image'] ?? null; // Start with current DB value


		if(!$errors){

			//Format validated data
			$pagename = clean_url($_POST['facility_name']);
			$content  = trim(str_replace(['&nbsp;'], '', strip_tags($_POST['content']))) ? $_POST['content'] : NULL;

			// //Delete old images
			// if(isset($_POST['delete_logo']) && $_POST['delete_logo'] == '1'){
			// 	// $CMSUploader->bulk_delete(['logo' => $records_arr[ITEM_ID]['logo']]);

			// 	$CMSUploader->bulk_delete(['logo' => $final_logo_filename]); // Pass correct fieldname 'logo'
            //     $final_logo_filename = null;
			// }

            // if(isset($_POST['delete_image'])){
			// // 	$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			// }

			if(isset($_POST['delete_logo']) && $_POST['delete_logo'] == '1'){
				$CMSUploaderLogo->bulk_delete(['logo' => $final_logo_filename]);
				$final_logo_filename = null;
			}

			if(isset($_POST['delete_banner']) && $_POST['delete_banner'] == '1'){
				$CMSUploaderBanner->bulk_delete(['image' => $final_banner_filename]);
				$final_banner_filename = null;
			}

            //

				// //Upload new images
				// try{
				// 	$images = $CMSUploader->bulk_upload('', $records_arr[ITEM_ID] ?? []);
				// }catch(Exception $e){
				// 	$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
				// }

            //

			//Upload new images
			// try{
			// 	$images = $CMSUploader->bulk_upload('', $records_arr[ITEM_ID] ?? []);
			// }catch(Exception $e){
			// 	$CMSBuilder->set_system_alert('Logo Image upload failed. Please try again.', false);
			// }

			//  // Check if a new logo file was uploaded
			//  if (!empty($_FILES['logo']['name']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            //     try {
            //         // bulk_upload expects a base name (e.g., facility name) and current images array
            //         // It will process based on the 'logo' field defined in $_croptypes['facility_logo']
            //         $uploaded_images = $CMSUploader->bulk_upload(
            //             $_POST['facility_name'] ?? 'facilityName',
            //             ['logo' => $final_logo_filename] // Pass current logo for potential old file deletion
            //         );
            //         if (isset($uploaded_images['logo'])) {
            //             $final_logo_filename = $uploaded_images['logo'];
            //         }
            //     } catch (Exception $e) {
            //         $errors[] = 'Logo upload failed: ' . $e->getMessage();
            //         $CMSBuilder->set_system_alert('Logo upload failed. Please try again.', false); // Old way
            //     }
            // }

			// Handle logo upload
			if (!empty($_FILES['logo']['name']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
				try {
					$uploaded_images = $CMSUploaderLogo->bulk_upload(
						$_POST['facility_name'] ?? 'facilityName',
						['logo' => $final_logo_filename]
					);
					if (isset($uploaded_images['logo'])) {
						$final_logo_filename = $uploaded_images['logo'];
					}
				} catch (Exception $e) {
					$errors[] = 'Logo upload failed: ' . $e->getMessage();
					$CMSBuilder->set_system_alert('Logo upload failed. Please try again.', false);
				}
			}

			// Handle banner upload
			if (!empty($_FILES['image']['name']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
				try {
					$uploaded_images = $CMSUploaderBanner->bulk_upload(
						$_POST['facility_name'] ?? 'facilityName',
						['image' => $final_banner_filename]
					);
					if (isset($uploaded_images['image'])) {
						$final_banner_filename = $uploaded_images['image'];
					}
				} catch (Exception $e) {
					$errors[] = 'Banner upload failed: ' . $e->getMessage();
					$CMSBuilder->set_system_alert('Banner upload failed. Please try again.', false);
				}
			}
			//

			// echo "<h1>final logo name - ".$final_logo_filename.'</h1>';

			$db->new_transaction();

			//Insert to db
			$params = [
				ITEM_ID,
				$_POST['facility_name'],
				$pagename,
				$_POST['type'],
				$_POST['affiliation'],
				$_POST['region'],
				$_POST['ownership'],
				$_POST['content'],
				$final_logo_filename,
				$final_banner_filename,
				$_POST['address1'],
				$_POST['address2'],
                $_POST['gpslat'],
                $_POST['gpslong'],
				$_POST['phone'],
				$_POST['email'],
				$_POST['city'],
				$_POST['province'],
				$_POST['postal_code'],
                $_POST['country'],
				// $images['image'] ?? NULL,
				$_POST['image_alt'],
				$_POST['showhide'],
                $_POST['google_map'],
				$_POST['website'],
				$_POST['meta_title'],
				$_POST['meta_description'],
				$_POST['focus_keyword'],
				date("Y-m-d H:i:s"),

				//update
				$_POST['facility_name'],
				$pagename,
				$_POST['type'],
				$_POST['affiliation'],
				$_POST['region'],
				$_POST['ownership'],
				$_POST['content'],
				$final_logo_filename,
				$final_banner_filename,
				$_POST['address1'],
				$_POST['address2'],
                $_POST['gpslat'],
                $_POST['gpslong'],
				$_POST['phone'],
				$_POST['email'],
				$_POST['city'],
                $_POST['province'],
				$_POST['postal_code'],
                $_POST['country'],
				// $images['image'] ?? NULL,
				$_POST['image_alt'],
				$_POST['showhide'],
                $_POST['google_map'],
				$_POST['website'],
				$_POST['meta_title'],
				$_POST['meta_description'],
				$_POST['focus_keyword'],
				date("Y-m-d H:i:s")
			];
			// $db->query("INSERT INTO `$record_db`(`$record_id`, `facility_name`, `page`, `type`, `affiliation`, `region`, `ownership`, `city`, `phone`, `email`, `city`, `image`, `image_alt`, `showhide`, `website`, `meta_title`, `meta_description`, `focus_keyword`, `last_updated`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `facility_name` = ?, `page` = ?, `type` = ?, `affiliation` = ?, `region` = ?, `ownership` = ?, `city` = ?, `phone` = ?, `email` = ?, `city` = ?, `image` = ?, `image_alt` = ?, `showhide` = ?, `website` = ?, `meta_title` = ?, `meta_description` = ?, `focus_keyword` = ?, `last_updated` = ?", $params);

            $db->query("
    INSERT INTO `$record_db`(
        `$record_id`,
        `facility_name`,
        `page`,
        `type`,
        `affiliation`,
        `region`,
        `ownership`,
        `content`,
        `logo`,
        `image`,
        `address1`,
        `address2`,
        `gpslat`,
        `gpslong`,
        `phone`,
        `email`,
        `city`,
        `province`,
        `postal_code`,
        `country`,
        `image_alt`,
        `showhide`,
        `google_map`,
        `website`,
        `meta_title`,
        `meta_description`,
        `focus_keyword`,
        `last_updated`
    ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
    ON DUPLICATE KEY UPDATE
        `facility_name` = ?,
        `page` = ?,
        `type` = ?,
        `affiliation` = ?,
        `region` = ?,
        `ownership` = ?,
        `content` = ?,
        `logo` = ?,
        `image` = ?,
        `address1` = ?,
        `address2` = ?,
        `gpslat` = ?,
        `gpslong` = ?,
        `phone` = ?,
        `email` = ?,
        `city` = ?,
        `province` = ?,
        `postal_code` = ?,
        `country` = ?,
        `image_alt` = ?,
        `showhide` = ?,
        `google_map` = ?,
        `website` = ?,
        `meta_title` = ?,
        `meta_description` = ?,
        `focus_keyword` = ?,
        `last_updated` = ?
", $params);

			$item_id = (ITEM_ID != '' ? ITEM_ID : $db->insert_id());

			//Insert/Update new socials
			foreach($social_services as $service){
				$url    = $_POST['social'][$service] ?? false ?: NULL;
				$params = [$item_id, $service, $url, $url];

				$db->query("INSERT INTO `staff_social` (`$record_id`, `service`, `url`) VALUES (?,?,?) ON DUPLICATE KEY UPDATE `url` = ?", $params);
			}

			if(!$db->error()){
				$db->commit();

				//Save sitemap
				sitemap_XML();

				//Save SEO score
				if($cms_settings['enhanced_seo']){

					//Set new page_url
					$page_url = $siteurl.$root.$staff_page_url.$pagename."-".$item_id."/";

					try{
						$save_score = $Analyzer->save_score($_POST['focus_keyword'], $page_url, $pagename, $_POST['name'], ($records_arr[ITEM_ID]['seo_score'] ?? NULL), $item_id, $record_db, $record_id);
						if(!empty($save_score) && (MASTER_USER || SEO_USER)){
							$seo_message = "<br/><small>".$save_score."</small>";
						}
					}catch(Exception $e){
						unset($e);
					}
				}

				// if(!$CMSUploader->crop_queue()){
				// // if(!$CMSUploaderLogo->crop_queue()){
				// // if(!$CMSUploaderLogo->crop_queue() && !$CMSUploaderBanner->crop_queue()){ // Check both queues
				// 	$CMSBuilder->set_system_alert($record_name.' was successfully saved.'.($seo_message ?? ''), true);
				// 	header("Location: " .PAGE_URL);
				// 	exit();
				// }

				// Check if any crops are needed
				$logo_crops_needed = $CMSUploaderLogo->crop_queue();
				$banner_crops_needed = $CMSUploaderBanner->crop_queue();

				if(!$logo_crops_needed && !$banner_crops_needed){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.'.($seo_message ?? ''), true);
					header("Location: " .PAGE_URL);
					exit();
				}

			}else{
				$db->rollback();
				$CMSBuilder->set_system_alert('Unable to update record. ', false);

				// // Clean up newly uploaded files on DB error
				// if ($logo_action_taken && $logo_action == 'upload' && !empty($final_logo_filename)) { @unlink($abs_logo_dir . $final_logo_filename); }
				// if ($banner_action_taken && $banner_action == 'upload' && !empty($final_banner_filename)) {
				// 	// Delete all banner crops
				// 	foreach ($cropsizes as $dir_suffix => $size) { if (file_exists($abs_banner_dir_base . $dir_suffix . '/' . $final_banner_filename)) @unlink($abs_banner_dir_base . $dir_suffix . '/' . $final_banner_filename); }
				// }
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}
		}

	//Handle images
	}else{
		include('modules/CropImagesFacility.php');
	}


}