/* Tablesorter Custom Bootstrap LESS Theme by <PERSON>

To create your own theme, modify the code below and run it through
a LESS compiler, like this one: http://leafo.net/lessphp/editor.html
or download less.js from http://lesscss.org/

Test out these custom less files live
 Basic Theme : http://codepen.io/Mottie/pen/eqBbn
 Bootstrap   : http://codepen.io/Mottie/pen/Ltzpi
 Metro Style : http://codepen.io/Mottie/pen/gCslk

*/
/*** theme ***/
/*** fonts ***/
/*** color definitions ***/
/* for best results, only change the hue (240),
   leave the saturation (60%) and luminosity (80%) alone
   pick the color from here: http://hslpicker.com/#99E699 */
/* darken(@headerBackground, 10%); */
/* desaturate(@headerAsc, 5%); */
/* it might be best to match the document body background color here */
/* ajax error message (added to thead) */
/* becomes height using padding (so it's divided by 2) */
/* 20px should be slightly wider than the icon width to avoid overlap */
/* url(icons/loading.gif); */
/* zebra striping */
.allRows {
  background-color: #fff;
  color: #000;
}
.evenRows {
  background-color: #ffffff;
}
.oddRows {
  background-color: #f7f7fd;
}
/* hovered rows */
.oddHovered {
  background-color: #cccccc;
}
.evenHovered {
  background-color: #e6e6e6;
}
/* Columns widget */
/* saturate( darken( desaturate(@headerBackground, 10%), 10% ), 30%); */
/* Filter widget transition */
.filterWidgetTransition {
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
/*** icon block ***/
.iconPosition {
  font-size: 11px;
  position: absolute;
  right: 2px;
  top: 50%;
  margin-top: -7px;
  /* half the icon height; older IE doesn't like this */
  width: 14px;
  height: 14px;
  background-repeat: no-repeat;
  line-height: 14px;
}
/* black */
/* white */
/* automatically choose the correct arrow/text color */
/* variable theme name - requires less.js 1.3+;
   or just replace (!".@{theme}") with the contents of @theme
*/
.tablesorter-bootstrap {
  font: 14px "Helvetica Neue", Helvetica, Arial, sans-serif;
  background-color: #cdcdcd;
  width: 100%;
  /* style th's outside of the thead */
  /* style header */
  /* bootstrap uses <i> for icons */
  /* tfoot */
  /* optional disabled input styling */
  /* body */
  /* hovered row colors
	you'll need to add additional lines for
	rows with more than 2 child rows
	*/
  /* table processing indicator - indeterminate spinner */
  /* Column Widget - column sort colors */
  /* caption (non-theme matching) */
  /* filter widget */
  /* hidden filter row */
  /* rows hidden by filtering (needed for child rows) */
  /* ajax error row */
}
.tablesorter-bootstrap th,
.tablesorter-bootstrap thead td {
  font: 14px bold Arial, Sans-serif;
  font-weight: bold;
  background-color: #adadeb;
  color: #000;
  border-collapse: collapse;
  margin: 0 0 18px;
  padding: 4px;
}
.tablesorter-bootstrap tbody td,
.tablesorter-bootstrap tfoot th,
.tablesorter-bootstrap tfoot td {
  padding: 4px;
  vertical-align: top;
}
.tablesorter-bootstrap .tablesorter-header {
  cursor: pointer;
}
.tablesorter-bootstrap .tablesorter-header-inner {
  position: relative;
  padding: 4px 20px 4px 4px;
}
.tablesorter-bootstrap .tablesorter-header-inner i.tablesorter-icon {
  font-size: 11px;
  position: absolute;
  right: 2px;
  top: 50%;
  margin-top: -7px;
  /* half the icon height; older IE doesn't like this */
  width: 14px;
  height: 14px;
  background-repeat: no-repeat;
  line-height: 14px;
}
.tablesorter-bootstrap .tablesorter-header.sorter-false {
  background-image: none;
  cursor: default;
}
.tablesorter-bootstrap .tablesorter-headerAsc {
  background-color: #8c85e0;
}
.tablesorter-bootstrap .tablesorter-headerDesc {
  background-color: #d6d9f5;
}
.tablesorter-bootstrap .bootstrap-icon-unsorted {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAOCAMAAADOvxanAAAAVFBMVEUAAABCQkJZWVkZGRnJyckgICAZGRkZGRn8/PweHh4dHR0aGhoaGhpUVFQbGxvQ0NDc3NxMTExSUlIbGxvr6+s4ODhKSkogICAtLS00NDQzMzMnJydSEPrQAAAAGHRSTlMA1ssZRLgdAQbDyisqsZo8QdXUq0r9xPepSRwiAAAAX0lEQVQI13XHSQKAIAwEwQAKxn13Ev7/T2Pu9qmarJKPXIicI4PH4hxaKNrhm2S8bJK5h4YzKHrzJNtK6yYT/TdXzpS5zuYg4MSQYF6i4IHExdw1UVRi05HPrrvT53a+qyMFC9t04gcAAAAASUVORK5CYII=);
  color: #000;
}
.tablesorter-bootstrap tfoot .tablesorter-headerAsc,
.tablesorter-bootstrap tfoot .tablesorter-headerDesc {
  /* remove sort arrows from footer */
  background-image: none;
}
.tablesorter-bootstrap .disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: not-allowed;
}
.tablesorter-bootstrap tbody {
  /* Zebra Widget - row alternating colors */
}
.tablesorter-bootstrap tbody td {
  background-color: #fff;
  color: #000;
  padding: 4px;
  vertical-align: top;
}
.tablesorter-bootstrap tbody tr.odd > td {
  background-color: #f7f7fd;
}
.tablesorter-bootstrap tbody tr.even > td {
  background-color: #ffffff;
}
.tablesorter-bootstrap tbody > tr.hover > td,
.tablesorter-bootstrap tbody > tr:hover > td,
.tablesorter-bootstrap tbody > tr:hover + tr.tablesorter-childRow > td,
.tablesorter-bootstrap tbody > tr:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td,
.tablesorter-bootstrap tbody > tr.even.hover > td,
.tablesorter-bootstrap tbody > tr.even:hover > td,
.tablesorter-bootstrap tbody > tr.even:hover + tr.tablesorter-childRow > td,
.tablesorter-bootstrap tbody > tr.even:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td {
  background-color: #e6e6e6;
}
.tablesorter-bootstrap tbody > tr.odd.hover > td,
.tablesorter-bootstrap tbody > tr.odd:hover > td,
.tablesorter-bootstrap tbody > tr.odd:hover + tr.tablesorter-childRow > td,
.tablesorter-bootstrap tbody > tr.odd:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td {
  background-color: #cccccc;
}
.tablesorter-bootstrap .tablesorter-processing {
  background-image: url('data:image/gif;base64,R0lGODlhFAAUAKEAAO7u7lpaWgAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQBCgACACwAAAAAFAAUAAACQZRvoIDtu1wLQUAlqKTVxqwhXIiBnDg6Y4eyx4lKW5XK7wrLeK3vbq8J2W4T4e1nMhpWrZCTt3xKZ8kgsggdJmUFACH5BAEKAAIALAcAAAALAAcAAAIUVB6ii7jajgCAuUmtovxtXnmdUAAAIfkEAQoAAgAsDQACAAcACwAAAhRUIpmHy/3gUVQAQO9NetuugCFWAAAh+QQBCgACACwNAAcABwALAAACE5QVcZjKbVo6ck2AF95m5/6BSwEAIfkEAQoAAgAsBwANAAsABwAAAhOUH3kr6QaAcSrGWe1VQl+mMUIBACH5BAEKAAIALAIADQALAAcAAAIUlICmh7ncTAgqijkruDiv7n2YUAAAIfkEAQoAAgAsAAAHAAcACwAAAhQUIGmHyedehIoqFXLKfPOAaZdWAAAh+QQFCgACACwAAAIABwALAAACFJQFcJiXb15zLYRl7cla8OtlGGgUADs=');
  background-position: center center;
  background-repeat: no-repeat;
}
.tablesorter-bootstrap tr.odd td.primary {
  background-color: #b8adeb;
}
.tablesorter-bootstrap td.primary,
.tablesorter-bootstrap tr.even td.primary {
  background-color: #dbd6f5;
}
.tablesorter-bootstrap tr.odd td.secondary {
  background-color: #dbd6f5;
}
.tablesorter-bootstrap td.secondary,
.tablesorter-bootstrap tr.even td.secondary {
  background-color: #edebfa;
}
.tablesorter-bootstrap tr.odd td.tertiary {
  background-color: #edebfa;
}
.tablesorter-bootstrap td.tertiary,
.tablesorter-bootstrap tr.even td.tertiary {
  background-color: #ffffff;
}
.tablesorter-bootstrap caption {
  background-color: #fff;
}
.tablesorter-bootstrap .tablesorter-filter-row input,
.tablesorter-bootstrap .tablesorter-filter-row select {
  width: 98%;
  margin: 0;
  padding: 4px;
  color: #333;
  background-color: #fff;
  border: 1px solid #bbb;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
.tablesorter-bootstrap .tablesorter-filter-row {
  background-color: #eee;
}
.tablesorter-bootstrap .tablesorter-filter-row td {
  background-color: #eee;
  line-height: normal;
  text-align: center;
  /* center the input */
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
.tablesorter-bootstrap .tablesorter-filter-row.hideme td {
  padding: 2px;
  margin: 0;
  line-height: 0;
  cursor: pointer;
}
.tablesorter-bootstrap .tablesorter-filter-row.hideme * {
  height: 1px;
  min-height: 0;
  border: 0;
  padding: 0;
  margin: 0;
  /* don't use visibility: hidden because it disables tabbing */
  opacity: 0;
  filter: alpha(opacity=0);
}
.tablesorter-bootstrap .filtered {
  display: none;
}
.tablesorter-bootstrap .tablesorter-errorRow td {
  text-align: center;
  cursor: pointer;
  background-color: #e6bf99;
}
