<!DOCTYPE html>
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Honeycomb CMS | <?php echo $global["company_name"]; ?></title>
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="minimum-scale=1.0, width=device-width, maximum-scale=1.0, user-scalable=no">

<!--stylesheets-->
<link rel="stylesheet" href="<?php echo $root; ?>core/plugins/font-awesome/css/all.min.css" />
<link rel="stylesheet" href="<?php echo $root; ?>core/plugins/light-gallery/css/lightgallery.css" />
<link rel="stylesheet" href="<?php echo $root; ?>core/plugins/perfect-scrollbar/perfect-scrollbar.css" />
<link rel="stylesheet" href="<?php echo $path; ?>includes/plugins/cropperjs/cropper.min.css" />
<link rel="stylesheet" href="<?php echo $path; ?>includes/plugins/dropzone/dropzone.css" />
<link rel="stylesheet" href="<?php echo $path; ?>includes/plugins/fontawesome-iconpicker/css/fontawesome-iconpicker.min.css" />
<link rel="stylesheet" href="<?php echo $path; ?>css/base.css" />

<!--fonts-->
<link rel="preconnect" href="https://fonts.gstatic.com">
<link href="https://fonts.googleapis.com/css2?family=Exo:wght@400;500;800;900&family=Open+Sans:wght@400;700;800&display=swap" rel="stylesheet">

<!--jquery libs-->
<script type="text/javascript" src="<?php echo $root; ?>core/js/jquery.min.js"></script>
<script type="text/javascript" src="<?php echo $root; ?>core/js/jquery-ui.min.js"></script>

<?php if(SECTION_ID == $_cmssections['locations'] || SECTION_ID == $_cmssections['career_locations'] || SECTION_ID == $_cmssections['facilities'] || SECTION_ID == $_cmssections['registration-events'] || SECTION_ID == $_cmssections['registration-tournaments']){ ?>
<!--google maps-->
<script type="text/javascript" src="//maps.googleapis.com/maps/api/js?key=<?php echo $global['google_api_key']; ?>&libraries=places"></script>
<?php //} ?>
<?php // if(SECTION_ID == $_cmssections['locations'] || SECTION_ID == $_cmssections['facilities']) { ?>
<script type="text/javascript" src="<?php echo $path; ?>js/jquery-gmaps-latlon-picker.js"></script>
<?php } ?>

</head>

<body>

<?php if((defined('USER_LOGGED_IN') && USER_LOGGED_IN) && (SECTION_ID > 2 || SECTION_ID == '')){ ?>

<!--open cms-wrapper-->
<div id="cms-wrapper" class="<?php echo (($_COOKIE['cmsmenu'] ?? '') != 'hidden' ? 'menu-open' : '').(empty($navigation['grouped']) ? ' menu-simple' : ''); ?>">

	<!--open cms-menu-->
    <div id="cms-menu">

		<!--open menu header-->
		<div id="menu-header">
			<?php $dash = $sitemap[$_cmssections['dashboard']]; ?>

			<!--menu toggle-->
			<span id="menu-toggle" class="fas fa-bars"></span>

			<!--cms version-->
			<div id="cms-title">
				<a href="<?php echo $dash['page_url']; ?>">
					<img src="<?php echo $path; ?>images/icon.svg" alt="Honeycomb CMS" />
					<small>CMS V4.03</small>
				</a>
			</div>

			<!--dashboard-->
			<a href="<?php echo $dash['page_url']; ?>" class="<?php echo (SECTION_ID == $dash['section_id'] ? 'active' : ''); ?>">
				<i class="<?php echo $dash['icon']; ?>"></i><?php echo $dash['name']; ?>
			</a>

		</div><!--close menu-header-->

		<!--navigation-->
        <?php include("includes/navigation.php"); ?>

    </div><!--close cms-menu-->

	<!--open content-->
    <section id="cms-content">

		<!--mini alert container-->
	    <div id="system-mini-alerts"></div>

		<!--section title-->
        <header id="section-title">
            <h1><?php echo (!empty($section['icon']) ? '<i class="' .$section['icon']. '"></i>' : ''); ?><?php echo $section['name']; ?></h1>
			<small class="back-to-site"><a target="_blank" href="<?php echo $siteurl.$root; ?>">Back to site <i class="fas fa-external-link-alt"></i></a></small>
        </header>

		<!--system alerts-->
		<?php include("includes/widgets/alerts.php"); ?>

<?php } ?>