<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Promo Codes  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";
		
			echo "<thead>";
			echo "<th>Code</th>";
			echo "<th width='auto'>Restrictions</th>";
			echo "<th width='200px' class='center'>Discount</th>";
			echo "<th width='150px' class='{sorter:\"monthDayYear\"}'>Begins On</th>";
			echo "<th width='150px' class='{sorter:\"monthDayYear\"}'>Expires On</th>";
			echo "<th width='100px' class='center'>Use Count</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .$row['code']. "</td>";
					echo "<td>" .$row['event_name']. ($row['term'] != "" && $row['event_name'] != "" ? "<br />" : "") .$row['term']. "</td>";
					echo "<td class='center'>" .($row['discount_type'] == 'Percent' ? $row['discount'].'%' : '$'.$row['discount']). "</td>";
					echo "<td>" .date('M j, Y', strtotime($row['begin_date'])). "</td>";
					echo "<td>" .($row['expiry'] == "2999-01-01 00:00:00" ? "Never expires" : date('M j, Y', strtotime($row['expiry']))). "</td>";
					echo "<td class='center'>" .$row['promo_use_count']. "/".($row['promo_max_count'] == -1 ? "&infin;" : $row['promo_max_count'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo "</div>";	
	echo "</div>";

} else {

	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	} else if(ACTION == 'add' && !isset($_POST['save'])){	
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		//Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Promo Code <span class='required'>*</span>".(ITEM_ID == '' ? " <small><a href='#' id='generate_code_button'>Generate</a></small>" : $CMSBuilder->tooltip('Promo Code', 'Promo code cannot be changed once it&rsquo;s been created.'))."</label>
					<input type='text' name='code' value='" .(isset($row['code']) ? $row['code'] : ''). "' id='code' class='input" .(in_array('code', $required) ? ' required' : ''). "'".(ITEM_ID == '' ? "" : " disabled")." />
				</div>
				<div class='form-field auto-width'>
					<label>Discount <span class='required'>*</span>" .$CMSBuilder->tooltip('Discount', 'Choose either a discount percentage or dollar value. Discount will be applied before taxes.'). "</label>
					<input type='text' name='discount' value='" .(isset($row['discount']) ? $row['discount'] : ''). "' class='input input_sm num_only".(in_array('discount', $required) ? ' required' : '')."' />
					<select name='discount_type' class='select select_sm'>
						<option value='Percent'" .(isset($row['discount_type']) && $row['discount_type'] == 'Percent' ? ' selected' : ''). ">Percent</option>
						<option value='Dollar'" .(isset($row['discount_type']) && $row['discount_type'] == 'Dollar' ? ' selected' : ''). ">Dollars</option>
					</select>
				</div>
				
				<div class='clear'>
					<div class='form-field'>
						<label>Start Date <span class='required'>*</span></label>
						<input type='text' name='begin_date' value='" .(isset($row['begin_date']) && strtotime($row['begin_date']) > 0 ? date('Y-m-d', strtotime($row['begin_date'])) : ''). "' class='input datepicker".(in_array('begin_date', $required) ? ' required' : '')."' autocomplete='off' />
					</div>
					<div class='form-field'>
						<label>Expiry Date <span class='required'>*</span></label>
						<input type='text' name='expiry' value='" .(isset($row['expiry']) && strtotime($row['expiry']) > 0 && $row['expiry'] != '2999-01-01 00:00:00' ? date('Y-m-d', strtotime($row['expiry'])) : ''). "' id='end_date' class='input datepicker".(in_array('expiry', $required) ? ' required' : '')."'".(isset($row['expiry']) && $row['expiry'] == '2999-01-01 00:00:00' ? " disabled" : "")." style='margin-bottom:10px' autocomplete='off' />
						<p class='clearfix'>
							<small><input type='checkbox' name='no_expiry' value='1' id='no_end_date' class='checkbox'".(isset($row['expiry']) && $row['expiry'] == '2999-01-01 00:00:00' ? " checked" : "")." data-disable-input='#end_date' /><label for='no_end_date'>Never expires</label></small>
						</p>
					</div>					
					<div class='form-field'>
						<label>Usage Limit " .$CMSBuilder->tooltip('Usage Limit', 'Enter the number of times promo code can be used before it is no longer valid.'). "</label>
						<input type='text' name='promo_max_count' value='".(isset($row['promo_max_count']) && $row['promo_max_count'] != -1 ? $row['promo_max_count'] : "")."' id='limit_count' class='input input_sm num_only".(in_array('promo_max_count', $required) ? ' required' : '')."'".(isset($row['promo_max_count']) && $row['promo_max_count'] == -1 ? " disabled" : "")." style='margin-bottom:10px' />
						<p class='clearfix'>
							<small><input type='checkbox' name='unlimited_count' value='1' id='unlimited_count' class='checkbox'".(isset($row['promo_max_count']) && $row['promo_max_count'] == -1 ? " checked" : "")." data-disable-input='#limit_count' /><label for='unlimited_count'>No limit</label></small>
						</p>
					</div>
				</div>";
	
			echo "</div>";
		echo "</div>";
	
		//Restrictions
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Restrictions
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field ui-front'>
					<label>Restrict to User " .$CMSBuilder->tooltip('Restrict to User', 'Promo code can only be used by the selected user.')."</label>
					<input type='text' name='term' value='" .(isset($row['term']) ? $row['term'] : ''). "' class='account_suggest input' />
					<input type='hidden' name='account_id' value='" .(isset($row['account_id']) ? $row['account_id'] : ''). "' class='account_id' />
				</div>";
				echo "<div class='form-field'>
					<label>Restrict to Event " .$CMSBuilder->tooltip('Restrict to Event', 'Promo code can only be used for the selected event.')."</label>
					<select name='event_id' class='select'>
						<option value=''>- None - </option>";
						foreach($events as $event){
							echo "<option value='" .$event['event_id']. "'" .(isset($row['event_id']) && $row['event_id'] == $event['event_id'] ? ' selected' : ''). ">" .$event['name']. ", " .format_date_range($event['start_date'], $event['end_date']). "</option>";
						}
					echo "</select>
				</div>";
			echo "</div>
		</div>";

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";

}

?>