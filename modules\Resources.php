<?php

//Member Resources
if(PAGE_ID == $_sitepages['member-resources']['page_id']){

	//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){

		header('Location: '.$_sitepages['login']['page_url'].'?redirect='.urlencode($page['page_url']));
		exit();
	}
	// //Restrict to members
	if(!defined('MEMBER_ACCESS') || !MEMBER_ACCESS){
		exit();
	}

	// Define vars
	$errors = array();
	$required = array();
	$panel_id = 59; // Adjust panel ID as needed

	//Account folder permissions
	$permission_qry = "";
	$account_committees = array();
	$query = $db->query("SELECT `committee_id` FROM `account_committees` WHERE `account_id` = ?",array(USER_LOGGED_IN));
	if($query && !$db->error()){
		$committee_result = $db->fetch_array();
		foreach($committee_result as $committee){
			$account_committees[] = $committee['committee_id'];
		}
	}

	// echo "<pre>";
	// print_r($account_committees);
	// echo "<br> aid -";
	// echo $Account->account_id;
	// echo "<br> acc bm - ";
	// echo $Account->board_member;
	// echo "<br>";
	// print_r($Account);
	// echo "</pre>";

	// if(!empty($account_committees) || $Account->board_member != 0){
	// 	if(!empty($account_committees)){
	// 		$permission_qry .= " AND (`category_id` IN (SELECT category_id FROM resource_category_committees WHERE committee_id IN (".implode(",",$account_committees).")";
	// 		if($Account->board_member != 0){
	// 			$permission_qry .= " UNION SELECT category_id FROM resource_category_boards WHERE board_id = ".$Account->board_member;
	// 		}
	// 		$permission_qry .= ") OR `category_id` NOT IN (SELECT category_id FROM resource_category_committees))";
	// 	} else if($Account->board_member != 0){
	// 		$permission_qry = " AND (`category_id` IN (SELECT category_id FROM resource_category_boards WHERE board_id = ".$Account->board_member.")";
	// 	}
	// } else {
	// 	$permission_qry .= " AND `category_id` NOT IN (SELECT category_id FROM resource_category_committees)";
	// }

	/// new code for permission query
	$permission_qry_conditions = array();

	// 1. Check for committee-based permissions for the current user
	if (!empty($account_committees)) {
		$safe_committee_ids = array_map('intval', $account_committees);
		// Condition: The category_id is in the list of categories associated with the user's committees
		$permission_qry_conditions[] = "`rc`.`category_id` IN (SELECT `rcc`.`category_id` FROM `resource_category_committees` `rcc` WHERE `rcc`.`committee_id` IN (" . implode(",", $safe_committee_ids) . "))";
	}

	// 2. Check for board-based permissions for the current user
	if ($Account->board_member != 0) { // Assuming 0 means not a board member or no specific board role
		$safe_board_id = (int)$Account->board_member; // Ensure it's an integer
		// Condition: The category_id is in the list of categories associated with the user's board role
		$permission_qry_conditions[] = "`rc`.`category_id` IN (SELECT `rcb`.`category_id` FROM `resource_category_boards` `rcb` WHERE `rcb`.`board_id` = " . $safe_board_id . ")";
	}

	// 3. Define the condition for unrestricted folders
	// A folder is unrestricted if its category_id does NOT appear in committee restrictions
	// AND its category_id does NOT appear in board restrictions.
	$unrestricted_folder_condition = "(`rc`.`category_id` NOT IN (SELECT DISTINCT `rcc_any`.`category_id` FROM `resource_category_committees` `rcc_any`) AND `rc`.`category_id` NOT IN (SELECT DISTINCT `rcb_any`.`category_id` FROM `resource_category_boards` `rcb_any`))";

	// 4. Build the final $permission_qry string
	if (!empty($permission_qry_conditions)) {
		// If the user has specific committee/board affiliations, they can see folders matching those
		// OR folders that are unrestricted.
		$permission_qry = " AND ((" . implode(" OR ", $permission_qry_conditions) . ") OR " . $unrestricted_folder_condition . ")";
	} else {
		// If the user has no specific committee/board affiliations, they can only see unrestricted folders.
		$permission_qry = " AND " . $unrestricted_folder_condition;
	}
	// NEW REPLACEMENT BLOCK END

	// For debugging, you can uncomment this to see the generated SQL part:
	// echo "<pre>Generated permission_qry:\n" . htmlspecialchars($permission_qry) . "</pre>";

	// Your existing code to get folders will then use this $permission_qry:
	// Make sure to use the 'rc' alias for resource_categories in your main query as well.
	
	//Get folders
	$folders = array();
	$query = $db->query("SELECT `rc`.`name`, `rc`.`page`, `rc`.`category_id` 
			FROM `resource_categories` `rc` 
			WHERE `rc`.`showhide` = 0 $permission_qry 
			ORDER BY `rc`.`ordering`, `rc`.`category_id`");
	///
	// $query = $db->query("SELECT `name`, `page`, `category_id` FROM `resource_categories` WHERE `showhide` = 0 $permission_qry ORDER BY `ordering`, `category_id`");

	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$row['files'] = array();
			$folders[$row['category_id']] = $row;
		}
	} //folders available to user

	//Get files
	$files = array();
	$params = array(' ');
	$searchterm = ($_GET['search'] ?? '');
	if($searchterm != ''){
		$params[] = '%'.$_GET['search'].'%';
		$params[] = ' ';
		$params[] = '%'.$_GET['search'].'%';
	}
	$query = $db->query("SELECT `resources`.*, CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) AS `posted_by` FROM `resources` ".
	"LEFT JOIN `resource_categories` ON `resources`.`category_id` = `resource_categories`.`category_id` ".
	"LEFT JOIN `account_profiles` ON `resources`.`account_id` = `account_profiles`.`account_id` ".
	"WHERE IFNULL(`resource_categories`.`showhide`, 0) = 0 ".
	($searchterm != '' ? "&& (`resources`.`file_name` LIKE ? OR CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ?) " : "").
	"ORDER BY `resources`.`date_added` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();

		foreach($result as $row){
			if(array_key_exists($row['category_id'], $folders)){ //ensure permissions for this folder are visible to user
				//Add to files
				if(empty($row['category_id']) || $searchterm != ''){
				$files[] = $row;

				//Add to folder
				}else{
				$folders[$row['category_id']]['files'][] = $row;
				}
			}
		}
	} //files available to user

	//Define file vars
	$filedir = 'docs/resources/';
	$imagetypes = array('jpg', 'jpeg', 'pjpeg', 'png', 'gif');
	$filetypes = array('pdf', 'doc', 'docx', 'xls', 'xlsx');
	$searchterm = (isset($_GET['search']) && trim($_GET['search']) != '' ? $_GET['search'] : '');

	//Upload file
	if(isset($_POST['upload'])){

		// echo "<pre>";
		// print_r($_POST);
		// print_r($_FILES);
		// echo "</pre>";

		//XSS cookie validation
		if(empty($_POST['xid']) || $_POST['xid'] != $_COOKIE['xid']){
			$errors[] = 'Invalid session. Please ensure cookies are enabled in your browser.';
			unset($_POST);
			unset($_FILES);
		}else{

			//Validate
			if(trim($_POST['file_name']) == ''){
				$errors[] = 'No file name entered.';
			}
			if(empty($_FILES['file']['name'])){
				$errors[] = 'No file selected for upload.';
			}
			if(!empty($_FILES['file']['size']) && $_FILES['file']['size'] > 2097152){
				$errors[] = 'File size is too large. Cannot exceed 2MB.';
			}
			if(!empty($_FILES['file']['name'])){
				$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
				if(!in_array($ext, $imagetypes) && !in_array($ext, $filetypes)){
					$errors[] = 'File type `' .$ext. '` is restricted. Allowed file types include '.implode(', ', $imagetypes).', '.implode(', ', $filetypes).'.';
				}
			}
			if(!array_key_exists($_POST['category_id'], $folders)){
				$errors[] = 'Invalid folder access.';
			}

			//Upload it
			if(empty($errors)){
				$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
				$filename = clean_url($_POST['file_name']).'-'.date("ymdhis").'.'.$ext;

				// File upload using copy method like Application.php
				if (!@copy($_FILES['file']['tmp_name'], $filedir . $filename)) {
					$errors[] = "Failed to upload the file: " . $_FILES['file']['name'];
				} else {
					// Validate file exists
					if(file_exists($filedir.$filename)){

						//File type
						$filetype = NULL;
						switch($ext){
						case 'jpg':
						case 'jpeg':
						case 'pjepg':
						case 'png':
						case 'gif':
						$filetype = 'image';
						break;
						case 'pdf':
						$filetype = 'pdf';
						break;
						case 'doc':
						case 'docx':
						$filetype = 'word';
						break;
						case 'xls':
						case 'xlsx':
						$filetype = 'excel';
						break;
						}

						$params = array(
						USER_LOGGED_IN, 
						$_POST['category_id'], 
						$_POST['file_name'], 
						formatBytes($_FILES['file']['size']),
						$filetype,
						$filename, 
						date("Y-m-d H:i:s"),
						date("Y-m-d H:i:s")
						);
						$insert = $db->query("INSERT INTO `resources`(`account_id`, `category_id`, `file_name`, `file_size`, `file_type`, `file_location`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?,?,?)", $params);
						if($insert && !$db->error()){
							$_SESSION['upload_success'] = 'File was successfully uploaded.';
							// $success = $Account->alert('File was successfully uploaded.', true);
							echo '<script>
								setTimeout(function() {
									window.location.href = "' . $_SERVER['REQUEST_URI'] . '";
								}, 100);
							</script>';
							exit();
						}else{
						$errors[] = 'Unable to save file. Please try again. '.$db->error();
						}
					}else{
						$errors[] = 'Unable to upload file. Please try again.';
					}
				}
			}
		}

		//Display errors
		if(!empty($errors)){
			$alert = $Account->alert(implode('<br />', $errors), false);
		}
	}

	//Delete file
	if(isset($_POST['delete'])){

		//XSS cookie validation
		if(empty($_POST['xid']) || $_POST['xid'] != $_COOKIE['xid']){
			$errors[] = 'Invalid session. Please ensure cookies are enabled in your browser.';
		}else{

			if(empty($_POST['delete'])){
				$errors[] = 'Unable to delete file. Missing id.';
			}else{
				$query = $db->query("SELECT `file_id`, `file_location` FROM `resources` WHERE `file_id` = ? && `account_id` = ?", array($_POST['delete'], USER_LOGGED_IN));
				if($query && !$db->error()){
					if($db->num_rows()){
					$result = $db->fetch_array();
					$file = $result[0];
					$delete = $db->query("DELETE FROM `resources` WHERE `file_id` = ?", array($file['file_id']));
					if($delete && !$db->error()){
					if(file_exists($filedir.$file['file_location'])){
					unlink($filedir.$file['file_location']);
					}
					$success = $Account->alert('File was successfully deleted.', true);
					// Refresh page to remove deleted file
					header('Location: ' . $_SERVER['REQUEST_URI']);
					exit();
					}else{
					$errors[] = 'Unable to delete file. '.$db->error();
					}
					}else{
					$errors[] = 'Unable to delete file. Invalid permissions.';
					}
				}else{
					$errors[] = 'Unable to delete file. '.$db->error();
				}
			}
		}

		//Display errors
		if(!empty($errors)){
			$alert = $Account->alert(implode('<br />', $errors), false);
		}
	}

	if($searchterm != ''){ //hide folders from search view
		$folders = array();
	}

	
		// echo "<pre>";
	// print_r ($folders);
		// echo "</pre>";
}

?>