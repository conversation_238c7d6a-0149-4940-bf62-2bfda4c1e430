<?php

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">'.$records_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">

				<thead>
					<th width="1px" class="nopadding-r" data-sorter="false"></th>
					<th width="1px" class="nopadding-r" data-sorter="false"></th>
					<th>Name</th>
					<th>Category</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false"></th>
					<th width="1px" class="nopadding-l" data-sorter="false"></th>
				</thead>

				<tbody>';

				foreach($records_arr as $row){
					$seo_class = $seo_tooltip = '';
					if($cms_settings['enhanced_seo'] && (MASTER_USER || SEO_USER)){
						$seo_indicator = $Analyzer->score_tooltip($row['seo_score']);
						$seo_class     = $seo_indicator['class'];
						$seo_tooltip   = $seo_indicator['tooltip'];
					}

					$grav_thumb = $imagedir.'thumbs/'.($row['image'] ?: 'default.jpg');
					$grav_full  = $row['image'] ? $imagedir.'featured/'.$row['image'] : false;

					echo '<tr class="'.$seo_class.'" data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-id="'.$row[$record_id].'">
						<td class="handle nopadding-r">'.$seo_tooltip.'<i class="fas fa-arrows-alt"></i></td>
						<td class="nopadding-r">'.render_gravatar($grav_thumb, $grav_full, $row['name']).'</td>
						<td>'.$row['name'].($row['position'] ? '<br /><small>'.$row['position'].'</small>' : '').'</td>
						<td>'.($row['category'] ?: '<small>N/A</small>').'</td>
						<td class="center">'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
						<td class="nopadding-l"><a href="'.$row['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td>
					</tr>';
				}

				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';


//Image cropping
}else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');


//Display form
}else{
	$image = '';
	if(ACTION == 'edit'){
		$data  = $records_arr[ITEM_ID];
		$image = $data['image'];

		if(!isset($_POST['save'])){
			$row = $data;
		}

		echo '<div class="actions-nav flex-container">
			<div class="flex-column right">
				<small><b>Link to '.$record_name.':</b> '.$row['page_url'].'&nbsp;&nbsp;<a href="'.$row['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td></small>
			</div>
		</div>';

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';

		//Staff details
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show '.$record_name.'</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"'.(($row['showhide'] ?? 0) ? '' : ' checked').' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>

			<div class="panel-content flex-container">
				<div class="form-field">
					<label>Full Name'.(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="name" value="'.($row['name'] ?? '').'" id="button-text"" class="input'.(in_array('name', $required) ? ' required' : '').'" />
				</div>

				<div class="form-field">
					<label>Category'.(in_array('category_id', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<select name="category_id" class="select'.(in_array('category_id', $required) ? ' required' : '').'">
						<option value="">- Select -</option>';
						foreach($staff_categories as $category){
							echo '<option value="'.$category['category_id'].'"'.($row['category_id'] == $category['category_id'] ? ' selected' : '').'>'.$category['name'].'</option>';
						}
					echo '</select>
				</div>

				<div class="form-field">
					<label>Position'.(in_array('position', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="position" value="'.($row['position'] ?? '').'" class="input'.(in_array('position', $required) ? ' required' : '').'" />
				</div>

				<div class="form-field">
					<label>Phone Number'.(in_array('phone', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="phone" value="'.($row['phone'] ?? '').'" class="input'.(in_array('phone', $required) ? ' required' : '').'" />
				</div>

				<div class="form-field">
					<label>Email Address'.(in_array('email', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="email" value="'.($row['email'] ?? '').'" class="input'.(in_array('email', $required) ? ' required' : '').'" />
				</div>

				<div class="form-field">
					<label>Numerical Order'.(in_array('ordering', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.').'</label>
					<select name="ordering" class="select">
						<option value="101">Default</option>';
						
					for($i = 1; $i < 101; $i++){
						echo '<option value="'.$i.'" '.($row['ordering'] == $i ? 'selected' : '').'>'.$i.'</option>';
					}

					echo '</select>
				</div>
			</div>
		</div>'; //Staff details

		//Staff image
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Image
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">';
				
					echo $CMSBuilder->img_holder($image, $imagedir.'featured/', $imagedir.'thumbs/');

					[$max_W, $max_H] = CMSUploader::max_size('staff', 'image');
					echo '<div class="form-field">
						<label>Upload Image '.(in_array('image', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Upload Image', 'Image must be at least '.$max_W.' x '.$max_H.' and smaller than '.$_max_filesize['megabytes'].'.').'</label>
						<input type="file" class="input'.(in_array('image', $required) ? ' required' : '').'" name="image" value="" />
					</div>

					<div class="form-field">
						<label>Alt Text: <small>(SEO)</small> '.(in_array('image_alt', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.').'</label>
						<input type="text" name="image_alt" value="'.($row['image_alt'] ?? '').'" class="input" />
					</div>
				</div>
			</div>
		</div>'; //Staff image

		//Staff page content
		echo '<div class="panel page-content">
			<div class="panel-header">'.$record_name.' Bio'.(in_array('content', $required_fields) ? ' <span class="required">*</span>' : '').'
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">
				<textarea name="content" class="tinymceEditor">'.($row['content'] ?? '').'</textarea>
			</div>
		</div>'; //Staff page content

		//Social networking
		if($social_services){
			echo '<div class="panel">
				<div class="panel-header">Social Networking
					<span class="panel-toggle fas fa-chevron-up"></span>
				</div>
				<div class="panel-content">
					<label class="clear">Please Enter the Full URL:</label>
					<div class="flex-container">';
					
					foreach($social_services as $service){
						echo '<div class="form-field social">
							<i class="fab fa-'.$service.'"></i>
							<input type="text" name="social['.$service.']" class="input social" value="'.($row['social'][$service] ?? '').'" />
						</div>';
					}
					
					echo '</div>
				</div>
			</div>'; //Social networking
		}

		//SEO Content/Analysis
		include('includes/widgets/seotabs.php');

		//Sticky footer
		include('includes/widgets/formbuttons.php');

		echo '<input type="hidden" name="keep_tags[]" value="content" />
		<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'] .'" />
	</form>';

}

?>