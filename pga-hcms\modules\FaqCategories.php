<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('faq_categories');
	$CMSBuilder->set_widget($_cmssections['faq_categories'], 'Total FAQ Categories', $total_records);
}

if(SECTION_ID == $_cmssections['faq_categories']){

	//Define vars
	$record_db 	 = 'faq_categories';
	$record_id 	 = 'category_id';
	$record_name = 'Category';
	$records_name = 'Categories';

	//Validation
	$errors 	= false;
	$required 	= [];
	$required_fields = ['name'];

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.name"
	];

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;

	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get records
	$db->query("SELECT * FROM $record_db $where ORDER BY ordering, $record_id", $params);
	$records_arr = $db->fetch_assoc($record_id);
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);
	}

	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){
			$row = $records_arr[ITEM_ID];

		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}


	//Delete item
	if(isset($_POST['delete'])){

		//Delete from table, foreign key constraints will cascade
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);
		}
		header("Location: " .PAGE_URL);
		exit();


	//Save item
	}else if(isset($_POST['save'])){

		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);

		//Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		$pagename = clean_url($_POST['name']);

		if(!$errors){
			$content  = trim(str_replace(['&nbsp;'], '', strip_tags($_POST['content']))) ? $_POST['content'] : NULL;

			//Insert to db
			$params = array(

				//Insert
				ITEM_ID,
				$_POST['name'],
				$pagename,
				$content,
				$_POST['showhide'],
				$_POST['ordering'],
				date("Y-m-d H:i:s"),

				//Update
				$_POST['name'],
				$pagename,
				$content,
				$_POST['showhide'],
				$_POST['ordering'],
				date("Y-m-d H:i:s")
			);

			$db->query("INSERT INTO `$record_db`(`$record_id`, `name`, `page`, `content`, `showhide`, `ordering`, `last_modified`) VALUES(?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `name` = ?, `page` = ?, `content` = ?, `showhide` = ?, `ordering` = ?, `last_modified` = ?", $params);
			if(!$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}

	}

}

?>