<?php
// Recursively merges arrays without changing datatypes.  Arrays are appended, everything else is overwritten
if(!function_exists('array_merge_recursive_distinct')){
	function array_merge_recursive_distinct(array $array1, array $array2){
		foreach($array2 as $key => $value){
			if(is_array($value) && isset($array1[$key]) && is_array($array1[$key])){
				$array1[$key] = array_merge_recursive_distinct($array1[$key], $value);
			}else{
				$array1[$key] = $value;
			}
		}

		return $array1;
	}
}

// Build full address HTML, with appropriate text wrapping
if(!function_exists('prettify_address')){
	function prettify_address($addr, bool $structured = false, bool $snippets = false){
		$address = '';

		// Load pieces
		$addr['address'] = $addr['address'] ?? $addr['contact_address'] ?? '';
		$addr['address2'] = $addr['address2'] ?? $addr['contact_address2'] ?? '';
		$addr['city'] = $addr['city'] ?? $addr['contact_city'] ?? '';
		$addr['province'] = $addr['province'] ?? $addr['contact_province'] ?? '';
		$addr['postal_code'] = $addr['postal_code'] ?? $addr['contact_postal_code'] ?? '';

		// Insert snippets
		if($snippets){
			$addr['address'] = $addr['address'] ? '<span itemprop="streetAddress">'.trim($addr['address2'].' '.$addr['address']).'</span>' : '';
			$addr['address2'] = '';
			$addr['city'] = $addr['city'] ? '<span itemprop="addressLocality">'.$addr['city'].'</span>' : '';
			$addr['province'] = $addr['province'] ? '<span itemprop="addressRegion">'.$addr['province'].'</span>' : '';
			$addr['postal_code'] = $addr['postal_code'] ? '<span itemprop="postalCode">'.$addr['postal_code'].'</span>' : '';
		}

		// Concatenate pieces together
		$line1 = trim($addr['address2'].' '.$addr['address']);
		$line2 = $addr['city'].($addr['city'] && $addr['province'] ? ', ' : '').$addr['province'];
		$line3 = $addr['postal_code'];
		$line1 .= $line1 && ($line2 || $line3) ? ', ' : '';
		$line2 .= $line2 && $line3 ? ', ' : '';

		if(!$structured){
			$address = $line1.$line2.$line3;

		// Build html structure
		}else if($line1 || $line2 || $line3){
			for($i=1; $i<=3; $i++){
				if(${'line'.$i}){
					$address .= ' <span class="line'.$i.'">'.${'line'.$i}.'</span>';
				}
			}
			$address = trim($address);
		}

		// Include root schema
		if($snippets){
			$address = '<span itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">'.$address.'</span>';
		}

		return $address;
	}
}

// Define, overwrite, or echo alert messages in SESSION
if(!function_exists('session_alert')){
	function session_alert($name, $message = false){
		if($message){
			$_SESSION['session_alert'][$name] = $message;

		// Read and Delete
		}else if(isset($_SESSION['session_alert'][$name])){
			$message = $_SESSION['session_alert'][$name];
			unset($_SESSION['session_alert'][$name]);
		}

		return $message;
	}
}

// Validate date
if(!function_exists('validate_date')){
	function validate_date($date, $format = 'Y-m-d H:i:s'){
		$dt = DateTime::createFromFormat($format, $date);
		return $dt && $dt->format($format) == $date;
	}
}


// Searches a specificed column of a multi-dimesional array for a given value, and returns results with keys intact.  Returns FALSE if value was not found
if(!function_exists('array_search_assoc')){
	function array_search_assoc(array $array, $column, $value, $limit = false){
		$keys = array_keys(array_combine(array_keys($array), array_column($array, $column)), $value); // Search array columns and return an array of keys
		$keys = $limit ? array_slice($keys, 0, $limit) : $keys;

		// Filter out array data based on key.  Return single array if limit is 1
		$results = array_intersect_key($array, array_flip($keys));
		return $limit == 1 ? reset($results) : $results;
	}
}

//Generate clean url from string
if(!function_exists('clean_url')){
	function clean_url($str, $stopwords=true){
		$accents = [
			'à'=>'a', 'á'=>'a', 'â'=>'a', 'ã'=>'a', 'ä'=>'a', 'å'=>'a', 'æ'=>'a','À'=>'A', 'Á'=>'A', 'Â'=>'A', 'Ã'=>'A', 'Ä'=>'A', 'Å'=>'A', 'Æ'=>'A',
			'þ'=>'b', 'Þ'=>'B',
			'ç'=>'c', 'Ç'=>'C',
			'è'=>'e', 'é'=>'e', 'ê'=>'e', 'ë'=>'e', 'È'=>'E', 'É'=>'E', 'Ê'=>'E', 'Ë'=>'E',
			'ì'=>'i', 'í'=>'i', 'î'=>'i', 'ï'=>'i', 'Ì'=>'I', 'Í'=>'I', 'Î'=>'I', 'Ï'=>'I',
			'ð'=>'o', 'ñ'=>'n', 'ò'=>'o', 'ó'=>'o', 'ô'=>'o', 'õ'=>'o', 'ö'=>'o', 'ø'=>'o', 'Ð'=>'O', 'Ñ'=>'N', 'Ò'=>'O', 'Ó'=>'O', 'Ô'=>'O', 'Õ'=>'O', 'Ö'=>'O', 'Ø'=>'O',
			'ù'=>'u', 'ú'=>'u', 'û'=>'u', 'Ù'=>'U', 'Ú'=>'U', 'Û'=>'U',
			'ý'=>'y', 'ÿ'=>'y', 'Ý'=>'Y', 'Ÿ'=>'Y',
			'š'=>'s', 'ß'=>'ss', 'Š'=>'S',
			'ž'=>'z', 'Ž'=>'Z',
			'l’'=>'', 'l\''=>''
		];
		$clean = trim($str);
		$clean = str_replace("&rsquo;", "", $clean);
		$clean = str_replace("&quot;", "", $clean);
		$clean = str_replace(array_keys($accents), array_values($accents), $clean);
		$clean = preg_replace("/[^a-zA-z0-9\/_|+ -]/", '', $clean);
		$clean = strtolower(trim($clean, '-'));
		if($stopwords){
			$clean = remove_stop_words($clean);
		}
		$clean = preg_replace("/[\/_|+ -]+/", '-', $clean);
		$clean = strtolower(trim($clean, '-'));

		return $clean;
	}
}

//Remove stop words for SEO purposes
if(!function_exists('remove_stop_words')){
	function remove_stop_words($input){
		$stopwords = array("a", "am", "and", "any", "are", "at", "be", "but", "by", "can", "cant", "do", "else", "etc", "for", "go", "had", "has", "he", "her", "how", "ie", "if", "in", "is", "it", "its", "my", "not", "now", "of", "on", "or", "put", "so", "than", "that", "the", "then", "there", "this", "those", "to", "too", "us", "very", "was", "well", "what", "when", "who", "why", "with", "you", "your", "the");
		return preg_replace('/\b('.implode('|',$stopwords).')\b/','',$input);
	}
}

//Fetch the contents of an SVG file if it exists
if(!function_exists('load_svg')){
	function load_svg($filename, $svgdir="images/svg/"){
		global $root;
		$svgpath = $_SERVER['DOCUMENT_ROOT'].$root.$svgdir.str_replace('.svg', '', $filename).'.svg';
		echo file_exists($svgpath) ? str_replace(["\n", "\t"], "", file_get_contents($svgpath)) : '';
	}
}

/* Return $file back if exists, false if doesn't.  Optionally specify a directory to look inside.
 *
 * Assuming 'folder/file.php' exists, all these statements are truthy:
 * 	check_file('folder/') == 'folder/';
 * 	check_file('folder/file.php') == 'folder/file.php';
 * 	check_file('file.php', 'folder/') == 'file.php';
 * 	check_file('', 'folder/') === false;
 */
if(!function_exists('check_file')){
	function check_file($file, string $dir = ''){
		return is_string($file) && $file && file_exists($dir.$file) && !is_dir($dir.$file) ? $file : false;
	}
}

//Truncate a string by a length and append another string to the end
if(!function_exists('truncate')){
	function truncate($string, $length=55, $append="&hellip;"){
		$string = trim(strip_tags($string));

		if(strlen($string) > $length){
			$string = wordwrap($string, $length);
			$string = explode("\n", $string, 2);
			$string = preg_replace('/\W+$/', '', $string[0]).$append;
		}

		return $string;
	}
}

//Sanitize form data
if(!function_exists('sanitize_form_data')){
	function sanitize_form_data(){

		function sanitize_form_data_recurse(&$val, $key=NULL){
			if(is_array($val)){
				foreach($val as $key2 => &$val2){
					sanitize_form_data_recurse($val2, (is_numeric($key2) ? $key : $key2));
				}
			}else{
				$val = strip_data($val, in_array($key, ($_POST['keep_tags'] ?? [])));
			}
		}

		sanitize_form_data_recurse($_REQUEST);
		sanitize_form_data_recurse($_GET);
		sanitize_form_data_recurse($_POST);

		if(isset($_COOKIE['xid'])){
			$_COOKIE['xid'] = strip_data($_COOKIE['xid']);
		}
		if(isset($_COOKIE['xssid'])){
			$_COOKIE['xssid'] = strip_data($_COOKIE['xssid']);
		}
	}
}

//Strip harmful elements
if(!function_exists('strip_data')){
	function strip_data($string, $keeptags=false){
		$string = (trim($string) != '' ? str_replace("'", "&rsquo;", stripslashes(trim($string))) : '');
		$string = (trim($string) != '' ? str_replace("’", "&rsquo;", $string) : '');
		if(!$keeptags){
			$string = (trim($string) != '' ? str_replace('"', "&quot;", stripslashes(trim($string))) : '');
			$string = strip_tags($string);
		}
		return $string;
	}
}

//Generate hash
if(!function_exists('gen_random_string')){
	function gen_random_string(){
		$length = 50;
		$characters = '0123456789abcdefghijklmnopqrstuvwxyz';
		$string = '';
		for($p = 0; $p < $length; $p++){
			@$string .= $characters[mt_rand(0, strlen($characters))];
		}
		return md5($string);
	}
}

//Limit words function
if(!function_exists('string_limit_words')){
	function string_limit_words($string, $word_limit){
		 $words = explode(' ', $string);
		 return implode(' ', array_slice($words, 0, $word_limit));
	}
}

//Format phone numbers
if(!function_exists('format_phone_number')){
	function format_phone_number($number){
		//Remove common symbols found in phone numbers and convert all characters to lower case
		$return = str_replace(array('+','-','.',')','(',' '), "", stripslashes(strtolower($number)));

		//Convert letters to their respective number values (abc = 2, def = 3, etc)
		$letters = range('a', 'z');
		$letters_to_numbers_map = array(
			'letters' => $letters,
			'numbers' => array()
		);

		foreach($letters as $letter){
			if(in_array($letter, array('a', 'b', 'c'))){
				$letters_to_numbers_map['numbers'][] = 2;
			}else if(in_array($letter, array('d', 'e', 'f'))){
				$letters_to_numbers_map['numbers'][] = 3;
			}else if(in_array($letter, array('g', 'h', 'i'))){
				$letters_to_numbers_map['numbers'][] = 4;
			}else if(in_array($letter, array('j', 'k', 'l'))){
				$letters_to_numbers_map['numbers'][] = 5;
			}else if(in_array($letter, array('m', 'n', 'o'))){
				$letters_to_numbers_map['numbers'][] = 6;
			}else if(in_array($letter, array('p', 'q', 'r', 's'))){
				$letters_to_numbers_map['numbers'][] = 7;
			}else if(in_array($letter, array('t', 'u', 'v'))){
				$letters_to_numbers_map['numbers'][] = 8;
			}else if(in_array($letter, array('w', 'x', 'y', 'z'))){
				$letters_to_numbers_map['numbers'][] = 9;
			}
		}

		$return = str_replace($letters_to_numbers_map['letters'], $letters_to_numbers_map['numbers'], $return);

		return trim($return);
	}
}

//Format international phone number
if(!function_exists('format_intl_number')){
	function format_intl_number($number){

		//Remove extention
		$ext = trim(substr(strrchr($number, 'ext'), 4));
		if($ext != ""){
			$number = strstr($number, 'ext', true);
		}

		//Format number for blank canvas
		$number = format_phone_number($number);

		//Check if number starts with 1
		if(substr($number, 0, 1) == '1'){
			$number = substr($number, 1);
		}

		//Grab last 7 digits (for canada/us numbers)
		$number = "+1-".substr($number, 0, 3)."-".substr($number, 3, 3)."-".substr($number, 6, 4).($ext != "" ? ";ext=".$ext : "");

		return trim($number);
	}
}

//Detect if string is a phone number
if(!function_exists('detect_phone')){
	function detect_phone($string){
		if(!preg_match("/[a-z]/i", $string)){
			preg_match("/\+?[0-9][0-9()\-\s+]{4,20}[0-9]/i", $string, $matches);
			if(!empty($matches)){
				return $matches[0];
			}
		}
		return false;
	}
}

//Check email
if(!function_exists('checkmail')){
	function checkmail($string){
		return preg_match("/^[^\s()<>@,;:\"\/\[\]?=]+@\w[\w-]*(\.\w[\w-]*)*\.[a-z]{2,}$/i",$string);
	}
}

//Get notification template
if(!function_exists('get_notification_template')){
	function get_notification_template($label){
		global $db;

		$query = $db->query("SELECT * FROM `notification_templates` WHERE `label` = ?", array($label));
		$result = $db->fetch_assoc('label');
		$response = ($result[$label] ?? array());

		return $response;
	}
}

//Format email content
if(!function_exists('format_email')){
	function format_email($content, $placeholders=array(), $template='includes/emailtemplates/template.htm', $stylesheet='theme/css/global.css'){
		global $global;
		global $root;
		global $siteurl;
		global $cmspath;

		$address = array();
		if(!empty($global['contact_address2'])){
			$address[] = $global['contact_address2'];
		}
		if(!empty($global['contact_address'])){
			$address[] = $global['contact_address'];
		}
		if(!empty($global['contact_city'])){
			$address[] = $global['contact_city'];
		}
		$address = (!empty($address) ? implode(', ', $address) : '');

		if($template != ''){
			$new_content = file_get_contents($_SERVER['DOCUMENT_ROOT'].$root.$template);
		}else{
			$new_content = $content;
		}

		$new_content = str_replace("%email_content%", $content, $new_content);
		$new_content = str_replace("%company_name%", $global['company_name'], $new_content);
		$new_content = str_replace("%year%", date("Y"), $new_content);
		$new_content = str_replace("%company_address%", $address, $new_content);
		$new_content = str_replace("%company_phone%", $global['contact_phone'], $new_content);
		$new_content = str_replace("%company_fax%", $global['contact_fax'], $new_content);
		$new_content = str_replace("%company_email%", $global['contact_email'], $new_content);
		$new_content = str_replace("%website_url%", ($placeholders['website_url'] ?? $siteurl.$root), $new_content);
		$new_content = str_replace("%cms_slug%", ($placeholders['cms_slug'] ?? ltrim($cmspath, $root)), $new_content);
		$new_content = str_replace("%date%", ($placeholders['date'] ?? date('M j, Y')), $new_content);

		foreach($placeholders as $placeholder_key => $placeholder_value){
			$new_content = str_replace("%".$placeholder_key."%", $placeholder_value, $new_content);
		}

		//Add styles
		if(class_exists('Emogrifier') && $stylesheet != ""){
			$emogrifier = new Emogrifier($new_content, $_SERVER['DOCUMENT_ROOT'].$root.$stylesheet);
			$new_content = @$emogrifier->emogrify();
		}

		return $new_content;
	}
}

//Send formatted email
if(!function_exists('send_email')){
	function send_email($to, $subject='', $message='', $notification_template='', $placeholders=array(), $files=array(), $template='includes/emailtemplates/template.htm', $stylesheet='theme/css/global.css'){
		global $global;
		global $path;
		global $siteurl;

		//TODO COMMENTED OUT FOR LOCAL ONLY REMOVE ON SERVER
		return true;

		//Use a notification template for subject and content
		if($notification_template != ''){
			$content_template = get_notification_template($notification_template);

			//If no custom subject, use content template's
			if(trim($subject) == ''){
				$subject = $content_template['subject'];
			}

			//Append custom message to content template's
			$message = $content_template['email_content'].$message;
		}

		//Format content
		$subject = format_email(html_entity_decode($subject), $placeholders, NULL, NULL);
		$emailMessage = format_email($message, $placeholders, $template, $stylesheet);

		//Try to send via SendGrid
		if(class_exists('MassMail') && sendgrid_email($to, $subject, $emailMessage, $files)){
			return true;

		//Fallback to SMTP email
		}else if(function_exists('smtp_email') && smtp_email($to, $subject, $emailMessage, $files)){
			return true;

		//Fallback to PHP mail
		}else{

			$from = str_replace(".", "", str_replace("&rsquo;", "'", $global['company_name'])). " <" .$global['contact_email']. ">";

			$headers = "From: " .$from. " \n";
			$headers .= "Reply-To: " .$from. " \n";
			$headers .= "Return-Path: " .$from. " \n";
			$headers .= "X-Mailer: PHP v" .phpversion() ." \n";
			$headers .= "Mime-Version: 1.0 \n";
			$headers .= "Content-Type: text/html;charset=UTF-8 \n";
			$headers .= "Content-Transfer-Encoding: 8bit \n";
			$headers .= "X-Priority: 3 \n";
			$headers .= "Importance: Normal \n";

			return mail($to, $subject, $emailMessage, $headers);
		}
	}
}

//SendGrid email
if(!function_exists('sendgrid_email')){
	function sendgrid_email($sendto, $subject, $message, $files=array()){
		global $db;
		global $global;
		global $sendgrid;

		$recipients = array();
		$emails = explode(",", $sendto); //Allow for comma separated list of emails
		foreach($emails as $to){
			$to = trim($to);
			if($to != "" && !array_key_exists($to, $recipients)){

				//Insert to email notifications for tracking purposes
				$db->query("INSERT INTO `email_notifications`(`email`, `notification`, `date_sent`) VALUES(?,?,?)", array($to, $subject, date("Y-m-d H:i:s")));
				$item_id = $db->insert_id();

				//Format recipients
				$recipients[$to] = array("name"=>"","email"=>$to,"unique_args"=>array("item_id"=>$item_id));

			}
		}

		//Format attachments
		$attachments = array();
		if(!empty($files)){
			foreach($files as $filepath){
				$filebits = explode('/', $filepath);
				$filename = $filebits[count($filebits)-1];
				$filetype = pathinfo($filepath, PATHINFO_EXTENSION);
				$attachments[] = array('name'=>$filename, 'file'=>$filepath, 'mime_type'=>'application/'.$filetype);
			}
		}

		//Unset template
		$sendgrid->set_template(false);

		try{
			$sendgrid->sendit($recipients, $subject, $message, $subject, array(), $attachments);
			return true;
		}catch(Exception $e){
			return false;
		}

	}
}

//SMTP email
if(!function_exists('smtp_email')){
	function smtp_email($to, $subject, $message, $files=array()){
		require_once "Mail.php";
		require_once "Mail/mime.php";
		global $global, $root;
		
		
		$headers = [
			'From' => $from,
			'To' => $to,
			'Reply-To' => $reply,
			'Subject' => $subject,
			'MIME-Version' => '1.0', 
			'Content-Type' => 'text/html;charset=UTF-8', 
			'Content-Transfer-Encoding' => '8bit', 
			'X-Priority' => '3', 
			'Importance' => 'Normal'
		];
		
		if(!empty($files)){
			$mime = new Mail_mime();
			$mime->setHTMLBody($message);
			foreach($files as $file){
				$mime->addAttachment($file,'application/octet-stream');
			}

			$body = $mime->get(array('html_charset' => 'utf-8'));
			$headers = $mime->headers($headers);
		}
		
		@$smtp = Mail::factory('smtp', [
			'host' => $host,
			'auth' => true,
			'username' => $username,
			'password' => $password
		]);

		@$mail = $smtp->send($to, $headers, $body);

		// Write received data to log
		if(@PEAR::isError($mail)){
			$fh = fopen($_SERVER['DOCUMENT_ROOT'].$root.'logs/smtp_email.log', 'a+');
			fwrite($fh, "Date:       ".date('Y-m-d H:i:s').
					  "\nError:      ".$mail->getMessage().
					  "\nArgs:     \n".json_encode(['to' => $to, 'ip' => get_ip()], JSON_PRETTY_PRINT).
					  "\nBacktrace:\n".json_encode(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), JSON_PRETTY_PRINT).
					  "\n===========\n\n");
			fclose($fh);

			return false;
		}else{
			return true;
		}

		
	}
}

//Build parent/child array from given data
if(!function_exists('build_hierarchy')){
	function build_hierarchy($items_arr, $item_id, $nested=false, $parent_id=0, $lvl=1){
		$response = array();
		$sub_label = ($item_id == 'page_id' ? 'sub_pages' : 'sub_items');

		foreach($items_arr as $item){
			if($item['parent_id'] == $parent_id){

				$response[$item[$item_id]] = $item;
				$response[$item[$item_id]]['lvl'] = $lvl;

				$children = build_hierarchy($items_arr, $item_id, $nested, $item[$item_id], ($lvl+1));
				if($nested){
					$response[$item[$item_id]][$sub_label] = $children;
				}else{
					foreach($children as $child){
						$response[$child[$item_id]] = $child;
					}
				}
			}
		}

		return $response;
	}
}

//Get the user's IP address
if(!function_exists('get_ip')){
	function get_ip(){
		foreach(array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR') as $key){
			if(array_key_exists($key, $_SERVER) === true){
				foreach(explode(',', $_SERVER[$key]) as $ip){
					if(filter_var($ip, FILTER_VALIDATE_IP) !== false){
						return $ip;
					}
				}
			}
		}
	}
}

//Get file mime type from extension
if(!function_exists('get_mime')){
	function get_mime($ext){
		switch ($ext){
			case 'pdf':
				$mime = 'pdf';
			break;
			case 'wmv':
			case 'mov':
			case 'flv':
			case 'mpeg':
			case 'mpg':
			case 'mp4':
			case 'vid':
				$mime = 'video';
			break;
			case 'mp3':
			case 'wav';
				$mime = 'audio';
			break;
			case 'docx':
			case 'docm':
			case 'doc':
				$mime = 'word';
			break;
			case 'txt':
			case 'rtf':
			case 'log':
			case 'json':
				$mime = 'text';
			break;
			case 'html':
			case 'xml':
			case 'php':
			case 'asp':
			case 'css':
			case 'js':
				$mime = 'code';
			break;
			case 'ppt':
			case 'pptx':
				$mime = 'powerpoint';
			break;
			case 'jpg':
			case 'jpeg':
			case 'png':
			case 'gif':
			case 'tiff':
				$mime = 'image';
			break;
			case 'xls':
			case 'xlsx':
			case 'xlsm':
			case 'csv':
				$mime = 'excel';
			break;
			case 'zip':
			case 'rar':
			case '7zip':
				$mime = 'zip';
			break;
			default:
				$mime = '';
			break;
		}
		return $mime;
	}
}


// Get friendly URL
if(!function_exists('get_domain')) {
	function get_domain($url){
		if (!preg_match('#^http(s)?://#', trim($url, '/'))) {
			$url = 'http://' . $url;
		}

		$parts  = parse_url($url);
		$domain = preg_replace('/^www\./', '', $parts['host']); // remove www

		return $domain;
	}
}

?>