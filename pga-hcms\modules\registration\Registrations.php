<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('reg_registrations');
	$CMSBuilder->set_widget($_cmssections['registration-registrations'], 'Total Registrations', $total_records);
}

if(SECTION_ID == $_cmssections['registration-registrations']){
	
	//Define vars
	$record_db = 'reg_registrations';
	$record_id = 'registration_id';
	$record_name = 'Registration';
	$errors = false;

	//Get Records
	$records_arr = array();
	$params = array();
	$where = "";

	if(ITEM_ID != '' && ACTION == 'edit'){
		$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`$record_id` = ?";
		$params[] = ITEM_ID;

	}else{
		if($searchterm != ""){
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."(`registration_number` LIKE ? OR CONCAT(`first_name`, ?, `last_name`) LIKE ? OR `email` LIKE ? OR `phone` LIKE ?)";
			$params[] = '%' .$searchterm. '%';
			$params[] = ' ';
			$params[] = '%' .$searchterm. '%';
			$params[] = '%' .$searchterm. '%';
			$params[] = '%' .$searchterm. '%';
		}
		if(!isset($_SESSION['search_start_date'][SECTION_ID]) || isset($_POST['clear-search'])){
			$_SESSION['search_start_date'][SECTION_ID] = date('Y-m-d', strtotime('-1 year'));
		}
		if(!isset($_SESSION['search_end_date'][SECTION_ID]) || isset($_POST['clear-search'])){
			$_SESSION['search_end_date'][SECTION_ID] = date('Y-m-d');
		}
		if(isset($_GET['start_date'])){
			$_SESSION['search_start_date'][SECTION_ID] = $_GET['start_date'];
		}
		if(isset($_GET['end_date'])){
			$_SESSION['search_end_date'][SECTION_ID] = $_GET['end_date'];
		}
		
		if(isset($_SESSION['search_start_date'][SECTION_ID]) && $_SESSION['search_start_date'][SECTION_ID] != '') {
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`registration_date` >= ?";
			$params[] = date('Y-m-d 00:00:00', strtotime($_SESSION['search_start_date'][SECTION_ID]));
		}
		if(isset($_SESSION['search_end_date'][SECTION_ID]) && $_SESSION['search_end_date'][SECTION_ID] != '') {
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`registration_date` <= ?";
			$params[] = date('Y-m-d 23:59:59', strtotime($_SESSION['search_end_date'][SECTION_ID]));
		}
	}
	$query = $db->query("SELECT `$record_db`.*, ".
	"(SELECT SUM(`payments`.`admin_fee`) FROM `payments` WHERE `payments`.`$record_id` = `$record_db`.`$record_id` && `payments`.`status` = 1) AS `reg_admin_fee` ".
	"FROM `$record_db`" .$where. " ORDER BY `registration_date` DESC", $params);
	if($query && !$db->error()){
		$records_arr = $db->fetch_array();
		if(ITEM_ID != '' && ACTION == 'edit'){
			$records_arr[ITEM_ID] = $records_arr[0];
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}
	
	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];
			
			//Get events
			$records_arr[ITEM_ID]['events'] = array();
			$get_events = $db->query("SELECT `reg_events`.`name`, `reg_events`.`event_type`, `reg_occurrences`.*, `reg_attendees`.`occurrence_id` FROM `reg_attendees` LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `reg_attendees`.`occurrence_id` LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `reg_occurrences`.`event_id` WHERE `reg_attendees`.`$record_id` = ? GROUP BY `reg_attendees`.`occurrence_id`", array(ITEM_ID));
			if($get_events && !$db->error() && $db->num_rows()){
				$events = $db->fetch_array();
				foreach($events as $key=>$event){
					$events[$key]['attendees'] = array();
					$get_attendees = $db->query("SELECT * FROM `reg_attendees` WHERE `$record_id` = ? AND `occurrence_id` = ? ORDER BY `attendee_id`", array(ITEM_ID, $event['occurrence_id']));
					if($get_attendees && !$db->error() && $db->num_rows()){
						$events[$key]['attendees'] = $db->fetch_array();
						foreach($events[$key]['attendees'] as $akey=>$attendee){
							$events[$key]['attendees'][$akey]['options'] = array();
							$get_attendee_options = $db->query("SELECT * FROM reg_attendee_options WHERE attendee_id = ?", array($attendee['attendee_id']));
							if($get_attendee_options && !$db->error() && $db->num_rows()){
								$events[$key]['attendees'][$akey]['options'] = $db->fetch_array();
							}
							$events[$key]['attendees'][$akey]['addons'] = $events[$key]['attendees'][$akey]['options']; //needed for invoice
						}
						
					}
				}
				
				$records_arr[ITEM_ID]['events'] = $events;
			}
			
			//Get payments
			$records_arr[ITEM_ID]['total_paid'] = 0;
			$records_arr[ITEM_ID]['admin_fee'] = 0;
			$records_arr[ITEM_ID]['payments'] = array();
			$get_payments = $db->query("SELECT * FROM `payments` WHERE `$record_id` = ? ORDER BY `payment_date` DESC", array(ITEM_ID));
			if($get_payments && !$db->error() && $db->num_rows() > 0){
				$payment_result = $db->fetch_array();
				foreach($payment_result as $payment){
					if($payment['status'] == 1){
						$records_arr[ITEM_ID]['total_paid'] += $payment['amount'];
						$records_arr[ITEM_ID]['admin_fee'] += $payment['admin_fee'];
					}
					$records_arr[ITEM_ID]['payments'][] = $payment;
				}
			}
			
			//Get refunds
			$records_arr[ITEM_ID]['total_refunded'] = 0;
			$records_arr[ITEM_ID]['attendees_refunded'] = array();
			$records_arr[ITEM_ID]['refunds'] = array();
			$get_registration_refunds = $db->query("SELECT `refunds`.*, `payments`.`payment_number`, `payments`.`cctype`, `payments`.`ccnumber`, `payments`.`ccexpiry` FROM `refunds` LEFT JOIN `payments` ON `refunds`.`payment_id` = `payments`.`payment_id` WHERE `refunds`.`$record_id` = ?", array(ITEM_ID));
			if($get_registration_refunds && !$db->error() && $db->num_rows() > 0){
				$refund_result = $db->fetch_array();
				foreach($refund_result as $refund){
					if($refund['status'] == 1){
						$records_arr[ITEM_ID]['total_refunded'] += $refund['amount'];
					}
					$records_arr[ITEM_ID]['refunds'][] = $refund;
				}
			}			
		}
		
		//Save item
		if(isset($_POST['save'])){
			$row = $records_arr[ITEM_ID];
			$row['notes'] = (trim($_POST['notes']) != "" ? $_POST['notes'] : NULL);
			
			$update_attendees = array();
			$reg_attendees = array();
						
			//Update pricing
			if($row['status'] && $row['total_paid'] == 0 && isset($_POST['ticket_price']) && is_array($_POST['ticket_price'])){
				
				$subtotal = 0;
				$reg_subtotal = 0;
				$tournament_fees = (isset($_POST['fees']) ? $_POST['fees'] : 0);
				foreach($row['events'] as $e=>$event){
					foreach($event['attendees'] as $a=>$attendee){
						$price = (!empty($_POST['ticket_price'][$attendee['attendee_id']]) ? $_POST['ticket_price'][$attendee['attendee_id']] : 0);
						if(!is_numeric($price)){
							$errors[] = 'Invalid price. Please enter a number.';
							$required[] = 'ticket_price_'.$attendee['attendee_id'];
						}else{
							$subtotal += $price;
							$attendee_subtotal = $price;
							if(!empty($attendee['options'])){
								foreach($attendee['options'] as $att_addon){
									if($att_addon['value'] != ""){
										$subtotal += $att_addon['price_adjustment'];
										$attendee_subtotal += $att_addon['price_adjustment'];
									}
								}
							}
							if($attendee['reg_status'] == 'Registered'){
								$reg_subtotal += $attendee_subtotal; //Total used to distribute discount to registered attendees only
								$reg_attendees[$attendee['attendee_id']] = $attendee_subtotal;
							}
						}
						
						$row['events'][$e]['attendees'][$a]['ticket_price'] = $price;
						$update_attendees[$attendee['attendee_id']] = array(
							'ticket_price' => $price, 
							'discount' => 0, 
							'tournament_fees' => number_format($tournament_fees/count($event['attendees']), 2, '.', '')
						);
					}
				}

				//Discount calculations
				$discount = (isset($_POST['discount']) && !empty($_POST['discount']) ? $_POST['discount'] : 0);
				if(!is_numeric($discount)){
					$errors[] = 'Invalid discount. Please enter a number.';
					$required[] = 'discount';
				}
				if($discount > $subtotal){
					$discount = $subtotal;
					$percent_each = ($discount/$reg_subtotal);
					$subtotal = 0;
				}else{
					$percent_each = ($discount/$reg_subtotal);
					$subtotal = $subtotal-$discount;
				}

				//Evenly distribute discount amoung registered attendees
				foreach($reg_attendees as $attendee_id=>$attendee_subtotal){
					$attendee_discount = number_format($attendee_subtotal*$percent_each, 2, '.', '');								
					$update_attendees[$attendee_id]['discount'] = $attendee_discount;
				}

				//Recalculate prices
				$row['subtotal'] = $subtotal;
				$row['fees'] = (isset($_POST['fees']) ? $_POST['fees'] : 0);
				$row['discount'] = number_format($discount, 2, '.', '');
				if($discount == 0){
					$row['promocode'] = NULL;
				}
				$row['taxes'] = number_format($subtotal*(($row['gst_rate']+$row['pst_rate'])/100), 2, '.', '');
				$row['registration_total'] = $row['subtotal']+$row['taxes']+$row['fees'];

			}
				
										
			if(!$errors){
		
				$db->new_transaction();
				
				//Update registration
				$params = array(
					$row['notes'],
					$row['discount'],
					$row['promocode'],
					$row['taxes'],
					$row['fees'],
					$row['registration_total'],
					ITEM_ID
				);
				$query = $db->query("UPDATE `$record_db` SET `notes` = ?, `discount` = ?, `promocode` = ?, `taxes` = ?, `fees` = ?, `registration_total` = ? WHERE `$record_id` = ?", $params);
				
				//Update attendees
				foreach($update_attendees as $a=>$update){
					$params = array($update['ticket_price'], $update['discount'], $update['tournament_fees'], $a);
					$query = $db->query("UPDATE `reg_attendees` SET `ticket_price` = ?, `discount` = ?, `tournament_fees` = ? WHERE `attendee_id` = ?", $params);
				}
				
				if(!$db->error()){
					$db->commit();
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				}else{
					$CMSBuilder->set_system_alert('Unable to save '.$record_name.'. '.$db->error(), false);
				}
				
			}else{
				$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			}
			
		//Download
		}else if(isset($_POST['download'])){

			//Generate document
			$pdf = $Registration->generate_invoice($records_arr[ITEM_ID]);
			require_once("../includes/plugins/mpdf60/mpdf.php");
			if(class_exists('mPDF')){
				$filename = 'Registration-'.$records_arr[ITEM_ID]['registration_number'].'.pdf';
				$mpdf = new mPDF('utf-8',array(216,279.4),8,'Arial',20,20,16,16,5,7,'P');
				$mpdf->SetDisplayMode('fullpage');
				$mpdf->list_indent_first_level = 0;
				$mpdf->WriteHTML($pdf, 2);
				$mpdf->Output($filename,'D');
			}	
		}
	}

}

?>