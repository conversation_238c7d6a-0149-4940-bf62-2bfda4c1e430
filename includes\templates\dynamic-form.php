<?php  
echo '<div class="panel-content">
					<div class="container">';
echo '<form name="form-'.$panel['form']['form_id'].'" id="form-'.$panel['form']['form_id'].'" class="dynamic-form1" action="" method="post" data-recaptcha="#recaptcha-form-'.$panel['form']['form_id'].'">';
	//Login required
	$disableform = '';
	if($panel['form']['login'] && !USER_LOGGED_IN){
		$disableform = ' disabled';
		echo $Account->alert('<i class="fa fa-lock"></i> You must <a href="' .$_sitepages['login']['page_url']. '?redirect='.base64_encode($_SERVER['REQUEST_URI']). '">login</a> before you can complete this form.',false);
	}
	foreach($panel['form']['form_fields'] as $fieldset){
		echo (($fieldset['legend'] ?? '') ? '<h4 class="legend">'.$fieldset['legend'].'</h4>' : '');
		echo ($fieldset['description'] ?? '');
		if(!empty($fieldset['form_fields'])){
		echo '
			<div class="form-grid">';
				foreach($fieldset['form_fields'] as $field){
					$field_name = 'field-'.$panel['form']['form_id'].'-'.$field['field_id'];

					echo '<div class="form-field">
						<label for="form-'.$field_name.'">'.$field['label'].'
							'.($field['required'] ? ' <span class="required">*</span>' : '').'
							'.($field['description'] ? ' <small>'.$field['description'].'</small>' : '').'
						</label>';

						switch($field['type']){

							//Select
							case 'dropdown':
								echo '<select name="'.$field_name.'" id="form-'.$field_name.'" class="select'.($field['required'] ? ' jsvalidate' : '').'"'.($field['required'] ? ' required' : '').'>
									<option value="">- Select -</option>';
									foreach($field['field_options'] as $option){
										echo '<option value="'.$option['option_name'].'">'.$option['option_name'].'</option>';
									}
								echo '</select>';
							break;

							//Textarea
							case 'textarea':
								echo '<textarea name="'.$field_name.'" id="form-'.$field_name.'" class="textarea'.($field['required'] ? ' jsvalidate' : '').'"'.($field['required'] ? ' required' : '').$disableform.'></textarea>';
							break;

							//Checkbox and radio
							case 'checkbox':
							case 'radio':
								echo '<div id="form-'.$field_name.'" class="" role="radiogroup">';
									foreach($field['field_options'] as $option){
										echo '<input type="'.$field['type'].'" name="'.$field_name.($field['type'] == 'checkbox' ? '[]' : '').'" value="'.$option['option_name'].'" id="form-'.$field_name.'-'.$option['option_id'].'" class="'.$field['type'].($field['required'] ? ' jsvalidate' : '').'" '.$disableform.'/>
										<label for="form-'.$field_name.'-'.$option['option_id'].'">'.$option['option_name'].'</label><br/>';
									}
								echo '</div>';
							break;

							//File uploader
							case 'file':
								echo '<div class="input-file-container">  
									<input class="input-file" id="field-'.$panel['form']['form_id'].'-'.$field['field_id']. '" name="field-'.$panel['form']['form_id'].'-'.$field['field_id']. '" type="file" ' .$disableform. ' />
									<label for="field-'.$panel['form']['form_id'].'-'.$field['field_id']. '" class="input-file-trigger"><i class="fa fa-upload"></i>Select a file...</label>
								</div>';
							break;

							//File uploader
							case 'date':
								echo '<div class="input-file-container">  
									<input class="date_field input" id="field-'.$panel['form']['form_id'].'-'.$field['field_id']. '" name="field-'.$panel['form']['form_id'].'-'.$field['field_id']. '" type="date" value=""' .$disableform. ' />
									
								</div>';
							break;

							//Input
							default:
								$input_type = 'text';
								if($field['type'] == 'email') $input_type = 'email';
								if($field['type'] == 'phone') $input_type = 'tel';
								echo '<input type="'.$input_type.'" name="'.$field_name.'" id="form-'.$field_name.'" class="input '.$field['type'].($field['required'] ? ' jsvalidate' : '').'" '.($field['required'] ? ' required' : '').' '.$disableform.'/>';
							break;

						}
					echo '</div>';
				}
			echo '</div>
		';
		}
	}
	
	
	echo '<div class="form-buttons right">
		<button type="submit" name="submitform" class="button primary red " '.$disableform.'><span class="top-border"></span> <span class="bottom-border"></span> <span class="left-border"></span>
						<span class="right-border"></span><span>Submit</span></button>
	</div>

	<div id="recaptcha-modal" class="recaptcha-modal hidden-modal" title="Verify You&rsquo;re Not a Robot">
		<div class="recaptcha-wrapper">
			<div id="recaptcha-form-'.$panel['form']['form_id'].'" class="g-recaptcha" data-sitekey="'.$global['recaptcha_key'].'"></div>
		</div>
	</div>

	<input type="hidden" name="form_id" value="'.$panel['form']['form_id'].'" />
	<input type="hidden" name="xid" value="'.$_COOKIE['xid'].'" />
	<input type="hidden" name="g-recaptcha-response" />

</form>
</div></div>
';

?>