<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('pages');
	$CMSBuilder->set_widget($_cmssections['pages'], 'Total Page Count', $total_records, 'fas fa-file-alt');
}

if(SECTION_ID == $_cmssections['pages'] || SECTION_ID == $_cmssections['panels']){

	//Define vars
	$record_db   = 'pages';
	$record_id   = 'page_id';
	$record_name = 'Page';

	//Validation
	$errors   = false;
	$required = [];
	$required_fields = [
		'name'
	];
	
	//Redirects
	$mainpage   = $CMSBuilder->get_section($_cmssections['pages']);
	$panelpage  = $CMSBuilder->get_section($_cmssections['panels']);
	
	//Image uploading
	$paneldir	  		= "../images/panels/";
	$imagedir     		= "../images/heroes/";
	$panels_imagesizes	= [];
	$mobile_maxsize   	= 768;
	$CMSUploader  = new CMSUploader('banner', $imagedir);

	//Define croptypes for each panel (ensure panel name is crop type key)
	$panel_croptypes = ['parallax', 'side'];

	//Define croptypes for each page (type => crop_type)
	$page_croptypes = [
		0 => 'banner',
		2 => 'landing'
	];

	//Leadin library
	$db->query("SELECT * FROM `pages_leadins` ORDER BY `title` ASC");
	$leadin_arr = $db->fetch_assoc('leadin_id');
	
	//Get pages
	$db->query("SELECT * FROM `$record_db` WHERE `$record_id` > 2 ORDER BY `ordering`");
	$records_arr = $db->fetch_assoc('page_id');
	foreach($records_arr as $page_id => $row){
		$row['sub_pages']   = array();
		$row['unique_url']  = $row['slug'] ?: $row['page'];
		$row['page_url']    = $siteurl.$root.($row['unique_url']).'/';
		$row['image']       = check_file($row['image'], $imagedir) ? $row['image'] : false;
		$row['image_full']  = $row['image'] ? $imagedir.'1920/'.$row['image'] : false;
		$row['image_thumb'] = $row['image'] ? $imagedir.'480/'.$row['image'] : false;
		
		$records_arr[$page_id] = $row;
		unset($row);
	}

	//Format nested hierarchy
	$pages = build_hierarchy($records_arr, 'page_id');
	foreach($pages as $page_id => &$page){
		if($page['parent_id'] && array_key_exists($page['parent_id'], $pages)){
			$pages[$page['parent_id']]['sub_pages'][$page_id] = &$page;
			$pages[$page['parent_id']]['sub_pages'][$page_id]['parent_page'] = $pages[$page['parent_id']]['name'];
			$pages[$page['parent_id']]['sub_pages'][$page_id]['page_url'] = $pages[$page['parent_id']]['page_url'].($page['slug'] != NULL ? $page['slug'] : $page['page']).'/';
		}
	}

	//Display errors
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve page data.', false);	
	}

}

if(SECTION_ID == $_cmssections['pages']){

	//Set variables based on panel type (default type 0)
	$page_type = ($_POST['type'] ?? $records_arr[ITEM_ID]['type'] ?? 0);

	//Set specific form field names to non-text types (for initial default display, can modify in database if necessary)
	$page_form = $_POST['page_form'] ?? [];
	$field_types = [
		'email'   => 'email',
		'phone'   => 'tel',
		'subject' => 'select',
		'message' => 'textarea'
	];
	
	//Set non-editable field labels
	$field_labels = [
		'name'  => 'Your Name',
		'email' => 'Email Address',
		'phone' => 'Phone Number'
	];

	//Create defaults for form fields
	$field_names = $db->get_enum_vals('pages_form', 'name');
	foreach ($field_names as $name){
		$page_form[$name] = [
			'page_id'  => ITEM_ID,
			'name'     => $name,
			'type'     => $field_types[$name] ?? 'text',
			//Use prettified name as label, unless specified in non-editable array
			'label'    => $field_labels[$name] ?? ucwords(str_replace(['-','_'], ' ', $name)), 
			'options'  => '',
			'required' => 1,
			'showhide' => 0
		];
	}

	//Get pages related to search
	if(ACTION == ''){
		if($searchterm){
			$pages_result = array();

			//Search pages array
			foreach($pages as $key => $search_page){
				if(stripos($search_page['page_title'], $searchterm) !== false || 
					stripos($search_page['name'], $searchterm) !== false || 
					stripos($search_page['meta_title'], $searchterm) !== false || 
					stripos($search_page['content'], $searchterm) !== false){
					$pages_result[$key] = $search_page;
				}
			}
			$pages = $pages_result;
		}
	}

	
	//Selected page
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $pages)){
			//Set vars
			$row = $pages[ITEM_ID];

			//Set page panels
			$db->query("SELECT * FROM `pages_panels` WHERE `$record_id` = ? ORDER BY `ordering`, `panel_id` ASC", array(ITEM_ID));
			$pages[ITEM_ID]['page_panels'] = $records_arr[ITEM_ID]['page_panels'] = $db->fetch_array();

			//Overwrite default form fields
			$db->query("SELECT * FROM `pages_form` WHERE `$record_id` = ? ORDER BY `ordering` ASC", array(ITEM_ID));
			$page_form = $db->fetch_assoc('name') + $page_form;
		
		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}
	
	
	//Delete item
	if(isset($_POST['delete'])){

		//Must be deletable
		if($row['deletable'] && !$row['system_page']){

			//Multiple queries so utilize transactions
			$db->new_transaction();

			//Delete main page
			$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));

			//If subpages are system pages or aren't deletable, set to main level and hide
			$db->query("UPDATE `$record_db` SET `parent_id` = NULL, `showhide` = 1 WHERE `parent_id` = ? && (`deletable` = false || `system_page` = true)", array(ITEM_ID));

			//Loop through all subpages and delete applicable
			if(!empty($pages[ITEM_ID]['sub_pages'])){
				foreach($pages[ITEM_ID]['sub_pages'] as $sub_page){
					$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ? && `deletable` = true && `system_page` = false", array($sub_page['page_id']));
					
					//Tertiary pages
					if(!empty($sub_page['sub_pages'])){
						foreach($sub_page['sub_pages'] as $sub_page2){
							$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ? && `deletable` = true && `system_page` = false", array($sub_page2['page_id']));
							
							//Quaternary pages
							if(!empty($sub_page2['sub_pages'])){
								foreach($sub_page2['sub_pages'] as $sub_page3){
									$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ? && `deletable` = true && `system_page` = false", array($sub_page3['page_id']));
									
									//Quinary pages
									if(!empty($sub_page3['sub_pages'])){
										foreach($sub_page3['sub_pages'] as $sub_page4){
											$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ? && `deletable` = true && `system_page` = false", array($sub_page4['page_id']));
										}
									}
								}
							}
						}
					}
				}
			}

			if(!$db->error()){
				$db->commit();
				
				sitemap_XML();
				
				//Delete images
				if($records_arr[ITEM_ID]['image']){

					//Loop through all page types that have an image
					foreach($page_croptypes as $pgtype => $crop_type_key){
						$CMSUploader->set_crop_type($crop_type_key, $imagedir);
						$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
					}
				}

				//Delete panel images
				foreach($records_arr[ITEM_ID]['page_panels'] as $panel){

					//Loop through all panel types that have an image
					foreach($panel_croptypes as $crop_type_key){
						$CMSUploader->set_crop_type($crop_type_key, $paneldir);
						$CMSUploader->bulk_delete($panel);
					}
				}

				$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
				
			}else{
				$db->rollback();
				$CMSBuilder->set_system_alert('Unable to delete record.', false);	
			}

		//Not deletable
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. Page contains dynamic content.', false);	
		}
		
		header("Location: " .PAGE_URL);
		exit();
		

	//Save item
	}else if(isset($_POST['save'])){
		
		//Format landing page data for validation
		if($page_type == 2){
			//Overwrite page form with POST
			foreach($_POST['page_form'] as $name => $field){
				$field['required'] = +isset($field['required']);
				$field['showhide'] = +!isset($field['showhide']);
				$page_form[$name]  = array_merge($page_form[$name], $field);
			}

			//Re-order array according to order of POST
			$page_form = array_replace($_POST['page_form'], $page_form);

			//Set description
			$_POST['description'] = (isset($_POST['landing_description']) ? str_replace("<p>&nbsp;</p>", "", $_POST['landing_description']) : NULL);
		}

		//Required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Required dynamic fields
		if($page_type == 2){
			foreach($page_form as $name => $field){
				if(empty($field['label']) && !array_key_exists($name, $field_labels)){
					$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
					$required[] = $name.'-label';
				}

				if($field['type'] == 'select' && empty($field['options'])){
					$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
					$required[] = $name.'-options';
				}
			}
		}

		// Check for updates in page structure
		if (!empty($_POST['parent_id']) && ITEM_ID != "" && ITEM_ID == $records_arr[$_POST['parent_id']]['parent_id']) {
			$errors[] = 'Invalid Parent Page selected, please try again.';
			$required[] = 'parent_id';
		}
		
		//Image validation
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large. Cannot exceed ' .$_max_filesize['megabytes']. '.';
			$required[] = 'image';
		}

		//Set page title
		$_POST['page_title'] = $_POST['page_title'] ?: $_POST['name']; 

		// Set SEO tools if they don't exist
		$_POST['meta_canonical'] = $_POST['meta_canonical'] ?? $records_arr[ITEM_ID]['meta_canonical'] ?? NULL;
		$_POST['focus_keyword'] = $_POST['focus_keyword'] ?? $records_arr[ITEM_ID]['focus_keyword'] ?? NULL;

		// Strip site domain from meta_canonical
		if ($_POST['meta_canonical'] && substr($_POST['meta_canonical'], 0, strlen($siteurl)) == $siteurl) {
			$_POST['meta_canonical'] = substr($_POST['meta_canonical'], strlen($siteurl));
		}

		//Format content
		$content = (isset($_POST['TINYMCE_Editor']) ? str_replace("<p>&nbsp;</p>", "", $_POST['TINYMCE_Editor']) : NULL);

		//Create slug
		$pagename = clean_url($_POST['name']);

		//Pages that are not deletable cannot change slug
		if(ITEM_ID != "" && !$pages[ITEM_ID]['deletable']){
			$pagename = $pages[ITEM_ID]['page'];
		}

		//System pages cannot be moved
		if(ITEM_ID != "" && $pages[ITEM_ID]['system_page']){
			$_POST['parent_id'] = $pages[ITEM_ID]['parent_id'];
		}

		//Determine custom slug
		$pageslug   = !empty($_POST['slug']) ? clean_url($_POST['slug'], false) : NULL;
		$unique_url = $pageslug ?: $pagename;

		//Validate redirect URL
		if($page_type == 1 && $_POST['url'] == ""){
			$errors[] = 'Full Page/Site URL is required.';
			array_push($required, 'url');

		}else{
		
			//Collect all other unique urls for non-redirect pages
			$non_redirects = array_filter($records_arr, function ($val) {return ITEM_ID != $val['page_id'] && $val['type'] != 1; });
			$unique_urls   = array_column($non_redirects, 'unique_url', 'page_id');

			//Validate custom slug is unique
			if(in_array($unique_url, $unique_urls)){
				$errors[] = 'A page already exists with the same '.($pageslug ? 'slug' : 'name').'.';
				array_push($required, $pageslug ? 'slug' : 'name');
			}
		}
		
		//Highlight button class
		$_POST['class'] = str_replace('highlight', '', ($pages[ITEM_ID]['class'] ?? ''));
		if(isset($_POST['highlight'])){
			$_POST['class'] = 'highlight '.$_POST['class'];
		}

		//All validation passed
		if(!$errors){

			//Image processing
			$images = [];

			//Upload images
			$crop_type_key = $page_croptypes[$page_type] ?? '';
			if(!empty($_croptypes[$crop_type_key])){

				//Point class to correct parameters
				$CMSUploader->set_crop_type($crop_type_key, $imagedir);
			
				//Delete old images
				if(isset($_POST['deleteimage'])){
					$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
				}

				//Upload new images
				try{
					$images = $CMSUploader->bulk_upload($_POST['name'], $records_arr[ITEM_ID] ?? []);
				}catch(Exception $e){
					$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
				}

			}

			//Insert to db
			$params = array(
				
				//Insert
				(ITEM_ID ?: NULL), 
				$_POST['parent_id'] ?: NULL, 
				$_POST['name'], 
				$pagename, 
				$pageslug, 
				$page_type,
				$_POST['theme'] ?: NULL, 
				trim($_POST['class']),
				$_POST['page_title'], 
				$_POST['description'],
				$_POST['button_url'],
				$_POST['button_text'],
				$_POST['button_target'], 
				$_POST['meta_title'], 
				$_POST['meta_description'],
				$_POST['meta_canonical'], 
				$_POST['focus_keyword'],
				$content, 
				$images['image'] ?? NULL, 
				$_POST['image_alt'], 
				$_POST['url'], 
				$_POST['urltarget'], 
				$_POST['ordering'],
				$_POST['showhide'] ?? 0, 
				$_POST['navigation_menu'], 
				$_POST['google_map'], 
				$_POST['rating_badge'] ?? -1,
				$_POST['leadin_id'] ?? false ?: NULL,
				$_POST['form_title'],
				$_POST['form_recipient'],
				$_POST['form_description'],
				$_POST['form_button_text'],
				
				//Update
				$_POST['parent_id'] ?: NULL, 
				$_POST['name'], 
				$pagename, 
				$pageslug, 
				$page_type,
				$_POST['theme'] ?: NULL, 
				trim($_POST['class']),
				$_POST['page_title'], 
				$_POST['description'], 
				$_POST['button_url'],
				$_POST['button_text'],
				$_POST['button_target'],
				$_POST['meta_title'], 
				$_POST['meta_description'],
				$_POST['meta_canonical'], 
				$_POST['focus_keyword'], 
				$content, 
				$images['image'] ?? NULL, 
				$_POST['image_alt'], 
				$_POST['url'], 
				$_POST['urltarget'], 
				$_POST['ordering'], 
				$_POST['showhide'] ?? 0, 
				$_POST['navigation_menu'],
				$_POST['google_map'],
				$_POST['rating_badge'] ?? -1,
				$_POST['leadin_id'] ?? false ?: NULL,
				$_POST['form_title'],
				$_POST['form_recipient'],
				$_POST['form_description'],
				$_POST['form_button_text']
			);	

			$db->new_transaction();												
			$db->query("INSERT INTO `$record_db` (`$record_id`, `parent_id`, `name`, `page`, `slug`, `type`, `theme`, `class`, `page_title`, `description`, `button_url`, `button_text`, `button_target`, `meta_title`, `meta_description`, `meta_canonical`, `focus_keyword`, `content`, `image`, `image_alt`, `url`, `urltarget`, `ordering`, `showhide`, `navigation_menu`, `google_map`, `rating_badge`, `leadin_id`, `form_title`, `form_recipient`, `form_description`, `form_button_text`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `parent_id` = ?, `name` = ?, `page` = ?, `slug` = ?, `type` = ?, `theme` = ?, `class` = ?, `page_title` = ?, `description` = ?, `button_url` = ?, `button_text` = ?, `button_target` = ?, `meta_title` = ?, `meta_description` = ?, `meta_canonical` = ?, `focus_keyword` = ?, `content` = ?, `image` = ?, `image_alt` = ?, `url` = ?, `urltarget` = ?, `ordering` = ?, `showhide` = ?, `navigation_menu` = ?, `google_map` = ?, `rating_badge` = ?, `leadin_id` = ?, `form_title` = ?, `form_recipient` = ?, `form_description` = ?, `form_button_text` = ?", $params);
			if(!$db->error()){
				$item_id = (ITEM_ID != "" ? ITEM_ID : $db->insert_id());

				//Create default panels on new insert
				if(ITEM_ID == "" && $_POST['save'] != 'newpanel'){
					$content = '<h3>Header H3</h3>
					<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. </p>
					<h4>Header H4</h4>
					<p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Donec elementum ligula eu sapien consequat eleifend. </p>
					<h5>Header H5</h5>
					<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.</p>
					<h6>Header H6</h6>
					<p>Quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa</p>
					<hr>
					<p><small>Donec nec dolor erat, condimentum sagittis sem. Praesent porttitor porttitor risus, dapibus rutrum ipsum gravida et. Integer lectus nisi, facilisis sit amet eleifend nec, pharetra ut augue. Integer quam nunc, consequat nec egestas ac, volutpat ac nisi.</small></p>';

					$params = [$item_id, 'standard', 'Standard Panel', $content];
					$db->query("INSERT INTO `pages_panels` (`$record_id`, `panel_type`, `title`, `content`) VALUES (?,?,?,?)", $params);
				}

				//Landing page form
				if($page_type == 2){
					
					$ordering = 0;
					foreach($page_form as $name => $field){
						$type = $field_types[$name] ?? 'text';
						$ordering++;
						
						$params = [
							
							//Insert
							$item_id,
							$name,
							$type,
							($field_labels[$name] ?? false ?: ($field['label'] ?: NULL)),
							$field['options'] ?: NULL,
							$field['required'],
							$field['showhide'],
							$ordering,
							
							//Update
							($field_labels[$name] ?? false ?: ($field['label'] ?: NULL)),
							$field['options'] ?: NULL,
							$field['required'],
							$field['showhide'],
							$ordering,
						];

						$db->query("INSERT INTO `pages_form` (`$record_id`, `name`, `type`, `label`, `options`, `required`, `showhide`, `ordering`) VALUES (?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `label` = ?, `options` = ?, `required` = ?, `showhide` = ?, `ordering` = ?", $params);
					}
				}

				//Successful insert
				if(!$db->error()){
					$db->commit();												

					//Update sitemap
					sitemap_XML();

					//Update SEO score
					if($cms_settings['enhanced_seo'] && $page_type != 1){

						//Set new page_url
						if($_POST['meta_canonical'] != ""){
							$page_url = $_POST['meta_canonical'];
						}else{
							$page_url = $_SERVER['HTTP_HOST'];
							if(isset($_POST['parent_id']) && isset($pages[$_POST['parent_id']])){
								$page_url .= $pages[$_POST['parent_id']]['page_url'];
							}else{
								$page_url .= $root;
							}
							$page_url .= $unique_url."/";
						}

						try{
							$save_score = $Analyzer->save_score($_POST['focus_keyword'], $page_url, $unique_url, $_POST['page_title'], ($pages[ITEM_ID]['seo_score'] ?? NULL), $item_id);
							if(!empty($save_score) && (MASTER_USER || SEO_USER)){
								$seo_message = "<br/><small>".$save_score."</small>";
							}
						}catch(Exception $e){
							unset($e);
						}
					}

					if(isset($_POST['save']) && $_POST['save'] == 'newpanel'){
						$redirect = $panelpage['page_url']."?page_id=$item_id&action=add";
					}

					if(!$CMSUploader->crop_queue()){
						if(isset($redirect)){
							header("Location: " .$redirect);
							exit();
						}else{
							$CMSBuilder->set_system_alert($record_name.' was successfully saved.' . (isset($seo_message) ? $seo_message : ''), true);
							header("Location: " .PAGE_URL);
							exit();
						}
					}

				}else{
					$db->rollback();
					$CMSBuilder->set_system_alert('Unable to update record.', false);
				}

			}else{
				$db->rollback();
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}
		

	//Duplicate page
	}else if(isset($_POST['duplicate'])){
		$original_page = $duplicate_page = $records_arr[ITEM_ID];
		$uploads_arr = array();

		//Count number of duplicates to rename duplicate (avoid same name/page/slug's)
		$unique_url = (trim($original_page['slug']) != '' ? $original_page['slug'] : $original_page['page']);
		$unique_url_base = preg_replace('/(-copy(\-[0-9]*)?)$/', '', $unique_url); //get slug without "-copy-N"
		$name_base = preg_replace('/( \- Copy( \([0-9]+\))?){0,1}$/i', '', $original_page['name']); //get name without "- Copy (N)"

		$duplicate_count = 0;
		$check_duplicate = $pages;
		if(!is_null($original_page['parent_id'])){
			$check_duplicate = ($pages[$original_page['parent_id']]['sub_pages'] ?? array());
		}
		foreach($check_duplicate as $check_page){
			if($check_page['type'] != 1 && 
				($check_page['slug'] == $unique_url 
				|| $check_page['page'] == $unique_url 
				|| preg_match('/^('.$unique_url_base.')(\-copy(\-[0-9]*)?){0,1}$/', $check_page['slug']) 
				|| preg_match('/^('.$unique_url_base.')(\-copy(\-[0-9]*)?){0,1}$/', $check_page['page']))
			){
				$duplicate_count++;
			}
		}

		//Re-format properties
		if($duplicate_count > 0){
			$duplicate_page['name'] = $name_base.' - Copy'.($duplicate_count > 1 ? ' ('.$duplicate_count.')' : '');
		}
		$duplicate_page['page'] = clean_url($duplicate_page['name']);
		$duplicate_page['slug'] = (trim($original_page['slug']) != '' ? $original_page['slug'].'-copy'.($duplicate_count > 1 ? '-'.$duplicate_count : '') : '');
		$duplicate_page['showhide'] = ($original_page['showhide'] == 0 ? 1 : $original_page['showhide']);
		$duplicate_page['last_modified'] = date('Y-m-d H:i:s');

		//Queue image
		$duplicate_page['image'] = NULL;
		if($original_page['image'] != ''){
			$ext = pathinfo($imagedir.$imagesizes[0]['dir'].$original_page['image'], PATHINFO_EXTENSION);
			$duplicate_page['image'] = $duplicate_page['page'].'-'.date("ymdhis").'.'.$ext;
			
			$dirs = array($imagedir);
			foreach($imagesizes as $size){
				$dirs[] = $imagedir.$size['dir'];
			}
			$uploads_arr[] = array(
				'old_image' => $original_page['image'],
				'new_image' => $duplicate_page['image'],
				'dirs' => $dirs
			);
		}

		//Start new transaction
		$db->new_transaction();

		//Copy page
		$insert_columns = $db->get_db_columns('pages', ['page_id', 'deletable', 'system_page']);
		$params = array();
		foreach($insert_columns as $column){
			$params[] = ($duplicate_page[$column] ?? NULL);
		}
		$values = implode(',', array_fill_keys($insert_columns, '?'));
		$db->query("INSERT INTO `$record_db` (`".implode("` , `", $insert_columns)."`) VALUES (".$values.")", $params);
		$duplicate_page['page_id'] = $db->insert_id();

		//Copy panels
		$lookup['page_panels'] = array();
		foreach($original_page['page_panels'] as $panel){
			$panels_imagesizes = ($_cropsizes[$panel['panel_type']] ?? array());
			$dirs = array($paneldir);
			foreach($panels_imagesizes as $size){
				$dirs[] = $paneldir.$size['dir'];
			}

			//Queue image
			$panel_image = NULL;
			if($panel['image'] != ''){
				$ext = pathinfo($paneldir.$panel['image'], PATHINFO_EXTENSION);
				$panel_image = clean_url($panel['title']).'-'.date("ymdhis").'.'.$ext;
				$uploads_arr[] = array(
					'old_image' => $panel['image'],
					'new_image' => $panel_image,
					'dirs' => $dirs
				);
			}

			//Queue image
			$panel_image_mobile = NULL;
			if($panel['image_mobile'] != ''){
				$ext = pathinfo($paneldir.$panel['image_mobile'], PATHINFO_EXTENSION);
				$panel_image_mobile = clean_url($panel['title']).'-'.date("ymdhis").'-m.'.$ext;
				$uploads_arr[] = array(
					'old_image' => $panel['image_mobile'],
					'new_image' => $panel_image_mobile,
					'dirs' => $dirs
				);
			}

			//Insert panel
			$insert_columns = $db->get_db_columns('pages_panels', ['panel_id', 'page_id', 'image', 'image_mobile', 'deletable']);
			$params = array($duplicate_page['page_id'], $panel_image, $panel_image_mobile);
			foreach($insert_columns as $column){
				$params[] = ($panel[$column] ?? NULL);
			}
			$values = implode(',', array_fill_keys($insert_columns, '?'));
			$db->query("INSERT INTO `pages_panels` (`$record_id`, `image`, `image_mobile`, `".implode("` , `", $insert_columns)."`) VALUES (?,?,?,".$values.")", $params);
			$panel_id = $db->insert_id();
			$lookup['page_panels'][$panel['panel_id']] = $panel_id;

			//Copy panel tabs
			$insert_columns = $db->get_db_columns('pages_panels_tabs', ['panel_id', 'tab_id']);
			$params = array($panel_id, $panel['panel_id']);
			$db->query(
				"INSERT INTO `pages_panels_tabs`"
				." (`panel_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `pages_panels_tabs`"
				." WHERE `panel_id` = ?"
			, $params);

			//Copy panel promos
			$params = array($panel_id, $panel['panel_id']);
			$db->query("INSERT INTO `pages_panels_promo` (`panel_id`, `promo_id`, `ordering`) SELECT ?, `promo_id`, `ordering` FROM `pages_panels_promo` WHERE `panel_id` = ?", $params);

			//Copy panel staff
			$params = array($panel_id, $panel['panel_id']);
			$db->query("INSERT INTO `pages_panels_staff` (`panel_id`, `staff_id`, `ordering`) SELECT ?, `staff_id`, `ordering` FROM `pages_panels_staff` WHERE `panel_id` = ?", $params);

			//Copy panel reviews
			$params = array($panel_id, $panel['panel_id']);
			$db->query("INSERT INTO `pages_panels_reviews` (`panel_id`, `review_id`, `ordering`) SELECT ?, `review_id`, `ordering` FROM `pages_panels_reviews` WHERE `panel_id` = ?", $params);

			//Copy panel faqs
			$params = array($panel_id, $panel['panel_id']);
			$db->query("INSERT INTO `pages_panels_faqs` (`panel_id`, `faq_category_id`, `ordering`) SELECT ?, `faq_category_id`, `ordering` FROM `pages_panels_faqs` WHERE `panel_id` = ?", $params);
		}

		//Copy form
		$insert_columns = $db->get_db_columns('pages_form', ['field_id', 'page_id', 'date_added', 'last_updated']);
		$params = array($duplicate_page['page_id'], date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), $original_page['page_id']);
		$db->query(
			"INSERT INTO `pages_form`"
			." (`$record_id`, `date_added`, `last_updated`, `".implode("` , `", $insert_columns)."`)"
			." SELECT ?, ?, ?, `".implode("` , `", $insert_columns)."`"
			." FROM `pages_form`"
			." WHERE `$record_id` = ?"
		, $params);

		//No errors
		if(!$db->error()){
			$db->commit();

			//Copy images
			foreach($uploads_arr as $upload){
				foreach($upload['dirs'] as $dir){
					if(file_exists($dir.$upload['old_image'])){
						copy($dir.$upload['old_image'], $dir.$upload['new_image']);
					}
				}
			}

			$CMSBuilder->set_system_alert($record_name.' was successfully duplicated.', true);
			header('Location:'.PAGE_URL.'?action=edit&item_id='.$duplicate_page['page_id']);
			exit();

		}else{
			$db->rollback();
			$CMSBuilder->set_system_alert('Unable to duplicate page.', false);
		}

	//Determine crop type key before handling images
	}else{
		$crop_type_key = $page_croptypes[$page_type] ?? '';
		
		//Point class to correct parameters
		if(!empty($_croptypes[$crop_type_key])){
			$CMSUploader->set_crop_type($crop_type_key, $imagedir);
			include('modules/CropImages.php');
		}
	}
}

?>