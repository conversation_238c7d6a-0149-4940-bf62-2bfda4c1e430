<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('leadin_submissions');
	$CMSBuilder->set_widget($_cmssections['leadin_stats'], 'Total Attention Box Form Submissions', $total_records);
}

if(SECTION_ID == $_cmssections['leadin_stats']){

	//No listing page
	if(ITEM_ID == '' && PAGE_ID == ''){
		header('Location:'.$mainpage['page_url']);
		exit();
	}

	//Define vars
	$mainpage = $CMSBuilder->get_section($_cmssections['leadins']);
	$pagespage = $CMSBuilder->get_section($_cmssections['pages']);
	$leadin_id = PAGE_ID;
	$conversions = [];

	//Get leadin
	$db->query("SELECT * FROM `pages_leadins` WHERE `leadin_id` = ?", [$leadin_id]);
	$result = $db->fetch_array();
	$leadin = $result[0] ?? false;
	$leadin_id = $leadin['leadin_id'];

	//Get pages for submissions
	if(ITEM_ID == ''){
		$pages = [];
		$db->query("SELECT `page_id`, `parent_id`, `name`, `page`, `slug`, `type`, `url` FROM `pages`");
		$result = $db->fetch_array();
		foreach($result as $row){
			$row['sub_pages'] = [];
			$row['page_url'] = $root.($row['slug'] != NULL ? $row['slug'] : $row['page']).'/';
			$pages[$row['page_id']] = $row;
		}

		$pages = build_hierarchy($pages, 'page_id');
		foreach($pages as $page_id => &$page){
			if($page['parent_id'] && array_key_exists($page['parent_id'], $pages)){
				$pages[$page['parent_id']]['sub_pages'][$page_id] = &$page;
				$pages[$page['parent_id']]['sub_pages'][$page_id]['page_url'] = $pages[$page['parent_id']]['page_url'].($page['slug'] != NULL ? $page['slug'] : $page['page']).'/';
			}
		}
	}

	//Get submissions
	$submissions = [];
	$params = [ITEM_ID != '' ? ITEM_ID : $leadin_id];

	if(ITEM_ID != ''){
		$db->query("SELECT `leadin_submissions`.*, `pages`.`name` AS `page_name` FROM `leadin_submissions` LEFT JOIN `pages` ON `pages`.`page_id` = `leadin_submissions`.`page_id` WHERE `leadin_submissions`.`submission_id` = ? ORDER BY `leadin_submissions`.`timestamp` DESC", $params);
	}else{
		$db->query("SELECT * FROM `leadin_submissions` WHERE `leadin_id` = ? ORDER BY `timestamp` DESC", $params);
	}

	if(!$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$row['form_fields'] = [];
			$row['page_name'] = (ITEM_ID != '' ? $row['page_name'] : NULL);
			$row['page_url'] = NULL;
			$row['page_edit_url'] = (!empty($row['page_id']) ? $pagespage['page_url'].'?action=edit&item_id='.$row['page_id'] : NULL);
	
			if(!empty($row['page_id'])){
				if(ITEM_ID != ''){
					$row['page_url'] = $siteurl.$root.get_page_url($row['page_id']);
				}else if(array_key_exists($row['page_id'], $pages)){
					$this_page = $pages[$row['page_id']];
					$row['page_name'] = $this_page['name'];
					$row['page_url'] = ($this_page['type'] > 0 ? $this_page['url'] : $this_page['page_url']);
				}
			}

			//Add to correct array
			if(ITEM_ID != ''){
				$submissions[$row['submission_id']] = $row;
			}else{
				$conversions['submission-'.$row['submission_id']] = array_merge($row, ['event' => 'submission']);
			}
		}
	}

	//Get other conversions
	if(ITEM_ID == ''){
		$db->query("SELECT * FROM `leadin_events` WHERE `leadin_id` = ? AND (`event` = ? OR `event` = ?)", [$leadin['leadin_id'], 'link-click', 'open']);
		$result = $db->fetch_array();
		foreach($result as $row){
			$row['page_name'] = NULL;
			$row['page_url'] = NULL;
			$row['page_edit_url'] = (!empty($row['page_id']) ? $pagespage['page_url'].'?action=edit&item_id='.$row['page_id'] : NULL);
			if(!empty($row['page_id']) && array_key_exists($row['page_id'], $pages)){
				$this_page = $pages[$row['page_id']];
				$row['page_name'] = $this_page['name'];
				$row['page_url'] = ($this_page['type'] > 0 ? $this_page['url'] : $this_page['page_url']);
			}
			$conversions[$row['event'].'-'.$row['event_id']] = $row;
		}
	}

	//Reorder conversions by timestamp (desc)
	uasort($conversions, function ($a, $b){
		return strtotime($a['timestamp']) <= strtotime($b['timestamp']);
	});

	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $submissions)){
			//Get responses
			$form_fields = [];
			$db->query("SELECT * FROM `leadin_submission_fields` WHERE `submission_id` = ?", [ITEM_ID]);
			$result = $db->fetch_array();
			foreach($result as $row){
				$form_fields[$row['field_id']] = $row;
			}

			$submissions[ITEM_ID]['form_fields'] = $form_fields;

		//Not found
		}else{
			$CMSBuilder->set_system_alert('Unable to retrieve data.', false);
			header('Location:'.PAGE_URL."?page_id=".$leadin_id);
			exit();
		}
	}
}

?>