<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Change section title
$section['name'] = 'Register';
$section['icon'] = 'user-plus';

//Get event 
$account_id = (isset($_POST['account_id']) ? $_POST['account_id'] : (isset($_GET['account_id']) ? $_GET['account_id'] : NULL));
$occurrence_id = (isset($_GET['event']) ? $_GET['event'] : NULL);
$event = $Registration->get_occurrence($occurrence_id, $account_id);
if(empty($event)){
	
	//Not found
	$CMSBuilder->set_system_alert('Event not found. Please select from the list below.!!!', false);
	// header('Location: '.$sitemap[57]['page_url']);
	header('Location: '.$sitemap[$_cmssections['registration-events']]['page_url']);
	exit();	
}

//Define vars
define('EVENT_ID', $event['event_id']);
define('EVENT_TYPE', $event['event_type']);
define('EVENT_NAME', ($event['event_type'] == 2 ? 'Tournament' : 'Event'));
// define('EVENT_SECTION', ($event['event_type'] == 2 ? 63 : 57));
define('EVENT_SECTION', ($event['event_type'] == 2 ? $_cmssections['registration-tournaments'] : $_cmssections['registration-events']));

//Clear previous sessions
if(isset($_SESSION['cms']['reg']) && $_SESSION['cms']['reg']['occurrence_id'] != $event['occurrence_id']){
	unset($_SESSION['cms']['reg']);
}

//Alerts
$important = array();
if($event['started']){
	$important[] = EVENT_NAME. ' has already started.';
}
if($event['full']){
	$important[] = EVENT_NAME. ' is currently at capacity.';
}
if(!$event['open']){
	$important[] = EVENT_NAME. ' registration is closed.';
}

//Tournament eligibility
if(EVENT_TYPE == 2){
	if(!is_null($account_id)){
		if(!$event['eligible'] || !$event['isgender']){
			$important[] = 'Attendee is not eligible for this ' .strtolower(EVENT_NAME). '.';
		}
		if($event['isregistered']){
			$errors[] = 'Attendee is already registered for this ' .strtolower(EVENT_NAME). '.';
		}
	}
	
//Event attendees
}else{
	
	//Get attendee fields
	$event['attendee_information'] = $Registration->get_attendee_information($event['event_id']);
	
	//Default attendee (must have at least one)
	if(!isset($_SESSION['cms']['reg']['attendees']) || empty($_SESSION['cms']['reg']['attendees'])){
		$_SESSION['cms']['reg']['attendees'][0] = array(
			'account_id' => NULL,
			'attendee_sharing' => 1,
			'ticket_type' => NULL,
			'ticket_price' => 0,
			'attendee_fields' => array(),
			'addons' => array()
		);
		foreach($event['attendee_information'] as $field=>$optional){
			$data['attendees'][0]['attendee_fields'][$field] = '';
		}
	}
}

//Autofill profile
if(isset($_GET['account_id'])){
	try{
		$account_profile = $Account->get_account_profile($_GET['account_id']);
		if(!isset($_SESSION['cms']['reg']['account_id']) || (isset($_SESSION['cms']['reg']['account_id']) && $_SESSION['cms']['reg']['account_id'] != $_GET['account_id'])){
			$_SESSION['cms']['reg']['account_id'] = $account_profile['account_id'];
			$_SESSION['cms']['reg']['first_name'] = $account_profile['first_name'];
			$_SESSION['cms']['reg']['last_name'] = $account_profile['last_name'];
			$_SESSION['cms']['reg']['email'] = $account_profile['email'];
			$_SESSION['cms']['reg']['phone'] = $account_profile['phone'];
			$_SESSION['cms']['reg']['company'] = $account_profile['company'];
			$_SESSION['cms']['reg']['address1'] = $account_profile['address1'];
			$_SESSION['cms']['reg']['address2'] = $account_profile['address2'];
			$_SESSION['cms']['reg']['city'] = $account_profile['city'];
			$_SESSION['cms']['reg']['province'] = $account_profile['province'];
			$_SESSION['cms']['reg']['postal_code'] = $account_profile['postal_code'];
			$_SESSION['cms']['reg']['country'] = $account_profile['country'];
		}
	}catch(Exception $e){
		$errors[] = 'Unable to load account profile.';
	}
}

//Confirm page
if(isset($_POST['confirm'])){
			
	//Tournament
	if(EVENT_TYPE == 2){
		
		//Validate
		$required_fields = array('first_name', 'last_name', 'email', 'phone');
		foreach($required_fields as $field){
			if(!isset($_POST[$field]) || trim($_POST[$field]) == ""){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}
		
		$attendees[] = array(
			'first_name' => $_POST['first_name'],
			'last_name' => $_POST['last_name'],
			'email' => $_POST['email'],
			'phone' => $_POST['phone'],
			'gender' => NULL,
			'account_id' => $_POST['account_id'],
			'attendee_sharing' => 1,
			'ticket_type' => $event['pricing'][0]['price_type'],
			'ticket_price' => $event['pricing'][0]['price'],
			'tournament_fee' => $event['tournament_fee'],
			'addons' => array()
		);
	
		//Partner
		$partner = array();
		if($event['team_event']){
			if(!empty($_POST['partner_id'])){
				if($_POST['partner_id'] != $_POST['account_id']){
					try{
						$profile = $Account->get_account_profile($_POST['partner_id']);
						$partner = array(
							'first_name' => (isset($profile['first_name']) ? $profile['first_name'] : NULL),
							'last_name' => (isset($profile['last_name']) ? $profile['last_name'] : NULL),
							'email' => (isset($profile['email']) ? $profile['email'] : NULL),
							'phone' => (isset($profile['phone']) ? $profile['phone'] : NULL),
							'gender' => (isset($profile['gender']) ? $profile['gender'] : NULL),
							'account_id' => $_POST['partner_id'],
						);
					}catch(Exception $e){
						$errors[] = 'Unable to load partner profile: '.$e->getMessage();
					}
				}else{
					$errors[] = 'Cannot register self as partner.';
					$required[] = 'partner_id';
				}
			}else{
				if(trim($_POST['partner_first_name']) != ""){
					$partner = array(
						'first_name' => $_POST['partner_first_name'],
						'last_name' => $_POST['partner_last_name'],
						'email' => NULL,
						'phone' => NULL,
						'gender' => NULL,
						'account_id' => NULL,
					);
				}
			}
			
			if(!empty($partner)){
				$partner['handicap'] = (trim($_POST['partner_handicap']) != "" ? $_POST['partner_handicap'] : NULL);
				$partner['attendee_sharing'] = 1;
				$partner['ticket_type'] = 'Partner Fee';
				$partner['ticket_price'] = 0;
				$partner['tournament_fee'] = 0;
				$partner['addons'] = array();
				
				$attendees[0]['partner'] = $partner;
			}
		}
	
	//Event
	}else{
		
		//Validate contact
		$required_fields = array('first_name', 'last_name', 'email', 'phone');
		foreach($required_fields as $field){
			if(!isset($_POST[$field]) || trim($_POST[$field]) == ""){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}
		
		//Validate attendees
		$attendees = $_SESSION['cms']['reg']['attendees'];
		foreach($_POST['ticket_id'] as $key=>$post){

			//Ticket
			if(empty($_POST['ticket_id'][$key])){
				$required[] = 'ticket_id_'.$key;
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
			}else{
				foreach($event['pricing'] as $price){
					if($price['pricing_id'] == $_POST['ticket_id'][$key]){
						$ticket_type = $price['price_type'];
						$ticket_price = $price['price'];
						break;
					}
				}
			}
			
			//Add to data array
			if(!array_key_exists($key, $attendees)){
				$attendees[$key] = array(
					'account_id' => ($_POST['attendee_account_id'][$key] != '' ? $_POST['attendee_account_id'][$key] : NULL),
					'attendee_sharing' => 1,
					'ticket_type' => $ticket_type,
					'ticket_price' => $ticket_price,
					'attendee_fields' => array(),
					'addons' => array()
				);
			}else{
				$attendees[$key]['account_id'] = ($_POST['attendee_account_id'][$key] != '' ? $_POST['attendee_account_id'][$key] : NULL);
				$attendees[$key]['ticket_type'] = $ticket_type;
				$attendees[$key]['ticket_price'] = $ticket_price;
			}
			
			//Attendee fields
			foreach($event['attendee_information'] as $field=>$optional){

				if($optional == 'Required' && trim($_POST['attendee_'.$field][$key]) == ''){
					$required[] = $field.'_'.$key;
					$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				}

				$attendees[$key]['attendee_fields'][$field] = $_POST['attendee_'.$field][$key];
			}
			
			//Addons
			if(!empty($event['addons'])){
				foreach($event['addons'] as $addon){
					$attendees[$key]['addons'][$addon['addon_id']] = array(
						'addon_id' => $addon['addon_id'],
						'name' => $addon['name'],
						'option_id' => $_POST['addon_'.$addon['addon_id']][$key],
						'value' => $addon['options'][$_POST['addon_'.$addon['addon_id']][$key]]['name'],
						'price_adjustment' => $addon['options'][$_POST['addon_'.$addon['addon_id']][$key]]['price_adjustment']
					);

					if($addon['required'] == 'Required' && trim($_POST['addon_'.$addon['addon_id']][$key]) == ''){
						$required[] = 'addon_'.$addon['addon_id'].'_'.$key;
						$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
					}
				}
			}
		}
		
		//Check against capacity
		if(!is_null($event['spots_available'])){
			if(count($_SESSION['cms']['reg']['attendees']) > $event['spots_available']){
				$important[] = 'Number of attendees exceeds the available spots for this event ('.$event['spots_available'].' available).';
			}
		}
		
	}
	
	//Calculate totals
	$subtotal = 0;
	$fees = $event['tournament_fee'];
	foreach($attendees as $attendee){
		$subtotal += $attendee['ticket_price'];
		if(isset($attendee['addons']) && !empty($attendee['addons'])){
			foreach($attendee['addons'] as $addon){
				$subtotal += $addon['price_adjustment'];
			}
		}
	}

	$taxrates = $Registration->get_taxes($subtotal, 'AB'); //Only tax subtotal, not fees
	$taxes = $taxrates['taxes'];
	$total = $subtotal+$taxes+$fees;
	
	//Set session vars
	$_SESSION['cms']['reg']['occurrence_id'] = $event['occurrence_id'];
	$_SESSION['cms']['reg']['first_name'] = $_POST['first_name'];
	$_SESSION['cms']['reg']['last_name'] = $_POST['last_name'];
	$_SESSION['cms']['reg']['email'] = $_POST['email'];
	$_SESSION['cms']['reg']['phone'] = $_POST['phone'];
	
	$_SESSION['cms']['reg']['account_id'] = $_POST['account_id'];
	$_SESSION['cms']['reg']['attendees'] = $attendees;
	$_SESSION['cms']['reg']['partner'] = $partner;
	
	$_SESSION['cms']['reg']['company'] = (isset($_POST['company']) ? $_POST['company'] : '');
	$_SESSION['cms']['reg']['facility'] = (isset($_POST['facility']) ? $_POST['facility'] : '');
	$_SESSION['cms']['reg']['address1'] = (isset($_POST['address1']) ? $_POST['address1'] : '');
	$_SESSION['cms']['reg']['address2'] = (isset($_POST['address2']) ? $_POST['address2'] : '');
	$_SESSION['cms']['reg']['city'] = (isset($_POST['city']) ? $_POST['city'] : '');
	$_SESSION['cms']['reg']['province'] = (isset($_POST['province']) ? $_POST['province'] : '');
	$_SESSION['cms']['reg']['postal_code'] = (isset($_POST['postal_code']) ? $_POST['postal_code'] : '');
	$_SESSION['cms']['reg']['country'] = (isset($_POST['country']) ? $_POST['country'] : '');
	
	$_SESSION['cms']['reg']['subtotal'] = $subtotal;
	$_SESSION['cms']['reg']['taxes'] = $taxes;
	$_SESSION['cms']['reg']['fees'] = $fees;
	$_SESSION['cms']['reg']['gst_rate'] = $taxrates['gst_rate'];
	$_SESSION['cms']['reg']['pst_rate'] = $taxrates['pst_rate'];
	$_SESSION['cms']['reg']['hst_rate'] = $taxrates['hst_rate'];
	$_SESSION['cms']['reg']['registration_total'] = $total;
	

//Submit registration
}else if(isset($_POST['save'])){
	
	//Save notes
	$_SESSION['cms']['reg']['notes'] = (trim($_POST['notes']) != "" ? $_POST['notes'] : NULL);
		
	//Double check ticket prices
	$subtotal = 0;
	foreach($_POST['ticket_price'] as $key=>$price){
		if(!is_numeric($price)){
			$errors[] = 'Invalid price. Please enter a number.';
			$required[] = 'ticket_price_'.$key;
		}else{
			$subtotal += $price;
			if(isset($_SESSION['cms']['reg']['attendees'][$key]['addons']) && !empty($_SESSION['cms']['reg']['attendees'][$key]['addons'])){
				foreach($_SESSION['cms']['reg']['attendees'][$key]['addons'] as $addon){
					$subtotal += $addon['price_adjustment'];
				}
			}
			$_SESSION['cms']['reg']['attendees'][$key]['ticket_price'] = $price;
		}
	}

	if(!$errors){

		//Recalculate prices
		$_SESSION['cms']['reg']['subtotal'] = $subtotal;
		$_SESSION['cms']['reg']['fees'] = (isset($_POST['fees']) ? $_POST['fees'] : 0);
		$_SESSION['cms']['reg']['taxes'] = number_format($subtotal*(($_SESSION['cms']['reg']['gst_rate']+$_SESSION['cms']['reg']['pst_rate'])/100), 2, '.', '');
		$_SESSION['cms']['reg']['registration_total'] = $_SESSION['cms']['reg']['subtotal']+$_SESSION['cms']['reg']['taxes']+$_SESSION['cms']['reg']['fees'];
		
		//Insert registration
		$db->new_transaction();
		
		$params = array(
			$_SESSION['cms']['reg']['first_name'],
			$_SESSION['cms']['reg']['last_name'],
			$_SESSION['cms']['reg']['email'],
			$_SESSION['cms']['reg']['phone'],
			$_SESSION['cms']['reg']['company'],
			$_SESSION['cms']['reg']['facility'],
			$_SESSION['cms']['reg']['address1'],
			$_SESSION['cms']['reg']['address2'],
			$_SESSION['cms']['reg']['city'],
			$_SESSION['cms']['reg']['province'],
			$_SESSION['cms']['reg']['postal_code'],
			$_SESSION['cms']['reg']['country'],
			$_SESSION['cms']['reg']['taxes'],
			$_SESSION['cms']['reg']['gst_rate'],
			$_SESSION['cms']['reg']['pst_rate'],
			$_SESSION['cms']['reg']['hst_rate'],
			$_SESSION['cms']['reg']['fees'],
			$_SESSION['cms']['reg']['registration_total'],
			date('Y-m-d H:i:s'),
			(!empty($_SESSION['cms']['reg']['account_id']) ? $_SESSION['cms']['reg']['account_id'] : NULL),
			$_SESSION['cms']['reg']['notes'],
			1,
			($_SESSION['cms']['reg']['registration_total'] > 0 ? 0 : 1),
			USER_LOGGED_IN
		);
		$query = $db->query("INSERT INTO `reg_registrations`(`first_name`, `last_name`, `email`, `phone`, `company`, `facility`, `address1`, `address2`, `city`, `province`, `postal_code`, `country`, `taxes`, `gst_rate`, `pst_rate`, `hst_rate`, `fees`, `registration_total`, `registration_date`, `account_id`, `notes`, `status`, `paid`, `registered_by`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
		$reg_id = $db->insert_id();
		$reg_num = 'PGA'.str_pad($reg_id, 5, '0', STR_PAD_LEFT).'-0'.EVENT_TYPE;
		
		//Update registration number
		$query = $db->query("UPDATE `reg_registrations` SET `registration_number` = ? WHERE `registration_id` = ?", array($reg_num, $reg_id));

		//Insert attendees
		foreach($_SESSION['cms']['reg']['attendees'] as $attendee){
			
			if(isset($attendee['attendee_fields'])){
				$first_name = $attendee['attendee_fields']['first_name'];
				$last_name = $attendee['attendee_fields']['last_name'];
				$email = $attendee['attendee_fields']['email'];
				$phone = $attendee['attendee_fields']['phone'];
			}else{
				$first_name = $attendee['first_name'];
				$last_name = $attendee['last_name'];
				$email = $attendee['email'];
				$phone = $attendee['phone'];
			}
			
			$params = array(
				(!empty($attendee['account_id']) ? $attendee['account_id'] : NULL),
				$reg_id,
				$event['occurrence_id'],
				$event['event_id'],
				'Registered',
				(!empty($first_name) ? $first_name : NULL),
				(!empty($last_name) ? $last_name : NULL),
				(!empty($email) ? $email : NULL),
				(!empty($phone) ? $phone : NULL),
				(isset($attendee['attendee_fields']['company']) ? $attendee['attendee_fields']['company'] : NULL),
				(isset($attendee['attendee_fields']['facility']) ? $attendee['attendee_fields']['facility'] : NULL),
				(isset($attendee['attendee_fields']['position']) ? $attendee['attendee_fields']['position'] : NULL),
				(isset($attendee['attendee_fields']['comments']) ? $attendee['attendee_fields']['comments'] : NULL),				
				(isset($attendee['gender']) ? $attendee['gender'] : NULL),
				$attendee['attendee_sharing'],
				$attendee['ticket_type'],
				$attendee['ticket_price'],
				$_SESSION['cms']['reg']['fees'],
				date('Y-m-d H:i:s')
			);
			$query = $db->query("INSERT INTO `reg_attendees`(`account_id`, `registration_id`, `occurrence_id`, `event_id`, `reg_status`, `first_name`, `last_name`, `email`, `phone`, `company`, `facility`, `position`, `comments`, `gender`, `attendee_sharing`, `ticket_type`, `ticket_price`, `tournament_fee`, `date_added`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
			$attendee_id = $db->insert_id();
			
			//Insert addons
			if(isset($attendee['addons']) && !empty($attendee['addons'])){
				foreach($attendee['addons'] as $addon){
					if($addon['value'] != ""){
						$query = $db->query("INSERT INTO `reg_attendee_options`(`attendee_id`, `name`, `value`, `price_adjustment`) VALUES(?,?,?,?)", array($attendee_id, $addon['name'], $addon['value'], $addon['price_adjustment']));
					}
				}
			}

			//Remove from waiting list
			if(!empty($attendee['account_id'])){
				$query = $db->query("DELETE FROM `reg_waiting_list` WHERE `occurrence_id` = ? && `account_id` = ?", array($event['occurrence_id'], $attendee['account_id']));
			}

			//Insert partner as linked attendee
			if(EVENT_TYPE == 2 && !empty($attendee['partner'])){
				$params = array(
					$attendee_id,
					(!empty($attendee['partner']['account_id']) ? $attendee['partner']['account_id'] : NULL),
					$reg_id,
					$event['occurrence_id'],
					$event['event_id'],
					'Registered',
					$attendee['partner']['first_name'],
					$attendee['partner']['last_name'],
					$attendee['partner']['email'],
					$attendee['partner']['phone'],
					$attendee['partner']['gender'],
					$attendee['partner']['attendee_sharing'],
					$attendee['partner']['ticket_type'],
					$attendee['partner']['ticket_price'],
					date('Y-m-d H:i:s')
				);
				$query = $db->query("INSERT INTO `reg_attendees`(`partner_id`, `account_id`, `registration_id`, `occurrence_id`, `event_id`, `reg_status`, `first_name`, `last_name`, `email`, `phone`, `gender`, `attendee_sharing`, `ticket_type`, `ticket_price`, `date_added`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);

				//Remove partner from waiting list
				if(!empty($attendee['partner']['account_id'])){
					$query = $db->query("DELETE FROM `reg_waiting_list` WHERE `occurrence_id` = ? && `account_id` = ?", array($event['occurrence_id'], $attendee['partner']['account_id']));
				}
			}

		}
		
		//Remove registrat from waiting list
		if(!empty($_SESSION['cms']['reg']['account_id'])){
			$query = $db->query("DELETE FROM `reg_waiting_list` WHERE `occurrence_id` = ? && `account_id` = ?", array($event['occurrence_id'], $_SESSION['cms']['reg']['account_id']));
		}

		//Success
		if(!$db->error()){
			$db->commit();

			//Success alert
			$alertmsg = 'Registration was successfully submitted.';
			if($_SESSION['cms']['reg']['registration_total'] > 0){
				$alertmsg .= '<br /><br /><a href="' .$sitemap[73]['page_url']. '?action=process&item_id=' .$reg_id. '&record=registration" class="button-sm"><i class="fa fa-credit-card"></i> Pay Now</a>';
			}
			$CMSBuilder->set_system_alert($alertmsg, true);

			$login_url = $siteurl.$root.get_page_url(22);

			//Format receipt data
			$order_data = $_SESSION['cms']['reg'];
			$order_data['registration_date'] = date('Y-m-d H:i:s');
			$order_data['payment_number'] = '';
			$order_data['ccnumber'] = '';	
			$order_data['discount'] = 0;
			$order_data['promocode'] = '';
			$order_cart = array(
				0 => array(
					'event_name' => $event['event_name'],
					'event_type' => EVENT_TYPE,
					'start_date' => $event['start_date'],
					'end_date' => $event['end_date'],
					'team_event' => $event['team_event'],
					'registration_number' => $reg_num,
					'subtotal' => $_SESSION['cms']['reg']['subtotal'],
					'attendees' => $_SESSION['cms']['reg']['attendees']
				)
			);

			//Send receipt
			$receipt = $Registration->registration_receipt($order_data, $order_cart);
			// todo uncomment 4 send_email() for production
			// send_email($_SESSION['cms']['reg']['email'], 'Registration Confirmation', $receipt);

			//Send admin email
			$emailtype = (EVENT_TYPE == 2 ? 'tournaments' : ($event['category_id'] == 3 ? 'conferences' : 'events'));
			$admin_email = (trim($reg_settings['email_'.$emailtype]) != '' ? $reg_settings['email_'.$emailtype] : $global['contact_email']);
			// send_email($admin_email, 'Registration Confirmation', $receipt);		

			//Send attendee notification
			foreach($_SESSION['cms']['reg']['attendees'] as $attendee){
				if(isset($attendee['attendee_fields']['email']) && trim($attendee['attendee_fields']['email']) != '' && $attendee['attendee_fields']['email'] != $_SESSION['cms']['reg']['email']){
					$to = $attendee['attendee_fields']['email'];
					$message = '<h3>Registration Confirmation</h3>
					<p>This email is to inform you that <strong>' .$_SESSION['cms']['reg']['first_name'].' '.$_SESSION['cms']['reg']['last_name'].'</strong> has registered you for the following event:</p>
					<p><strong>' .$event['event_name'].($event['occurrence_name'] != '' ? ' - '.$event['occurrence_name'] : ''). ' on ' .format_date_range($event['start_date'], $event['end_date']). '.</strong></p>
					<p>To view and manage your registrations, account holders <a href="' .$login_url. '" target="_blank">login</a> to your account.</p>';
					// send_email($to, 'Registration Confirmation', $message);
				}
			}

			//Send partner notification
			if(!empty($_SESSION['cms']['reg']['partner'])){
				if(isset($_SESSION['cms']['reg']['partner']['email']) && $_SESSION['cms']['reg']['partner']['email'] != ''){
					$to = $_SESSION['cms']['reg']['partner']['email'];
					$message = '<h3>Registration Confirmation</h3>
					<p>This email is to inform you that <strong>' .$_SESSION['cms']['reg']['first_name'].' '.$_SESSION['cms']['reg']['last_name'].'</strong> has registered you as his/her partner for the following tournament:</p>
					<p><strong>' .$event['event_name']. ' on ' .format_date_range($event['start_date'], $event['end_date']). '.</strong></p>
					<p>To view and manage your registrations, <a href="' .$login_url. '" target="_blank">login</a> to your account.</p>';
					// send_email($to, 'Registration Confirmation', $message);
				}
			}
			
			//Clear session
			unset($_SESSION['cms']['reg']);

			//Redirect to registration page
			// header('Location: '.$sitemap[69]['page_url'].'?action=edit&item_id='.$reg_id);
			header('Location: '.$sitemap[$_cmssections['registration-registrations']]['page_url'].'?action=edit&item_id='.$reg_id);
			exit();

		}else{
			$errors[] = 'Unable to submit registration. '.$db->error();
		}
	
	}
	
}

//Error reporting
if(!empty($errors)){
	$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
}

?>