<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?> partner-gallary">
	<?php
	if($panel['title'] || $panel['include_h1'] || $panel['content']) {
		// echo '<div class="panel-wrapper">';

		

		// if($panel['content']) {
		// 	echo '<div class="panel-content">
		// 		<div class="container">
		// 			<div class="panel-text">'.nl2br($panel['content']).'</div>
		// 		</div>
		// 	</div>';
		// }

		// echo '</div>';
	}

	// if(!empty($panel['panel_partners'])){
	// 	echo '<div class="panel-partners">
	// 		<div class="container">
	// 			<div class="partner-listings animate" data-animate=".gal-item" data-delay="150">';

	// 			foreach ($panel['panel_partners'] as $partner_id => $partner) {
	// 				include('includes/templates/partner-listing.php');
	// 			}

	// 		echo '</div>
	// 	</div>';
	// }
	
	// $db->query("SELECT partners.*, partner_categories.name as category, partner_categories.page FROM partners INNER JOIN partner_categories ON partner_categories.category_id = partners.category_id WHERE partners.showhide = 0 AND partner_categories.showhide = 0 ORDER BY partners.ordering");
	// $partners = $db->fetch_assoc('partner_id');
	// echo'<pre>';
	// print_r(count($panel['panel_partners']));
	// exit;
	?>
	
	<div class="container">
		
		<?php
		
		// $panel['carousel_display'] = 1;
		// if($panel['title'] || $panel['include_h1']) {
		// 	echo '<header class="panel-header">
		// 		<div class="container">
		// 			'.($panel['include_h1'] ? '<h1>'.fancy_text($page['page_title']).'</h1>' : '').'
		// 			'.($panel['title'] ? '<div class="panel-title"><h2>'.fancy_text($panel['title']).'</h2></div>' : '').'
		// 		</div>
		// 	</header>';
		// }
		// if($panel['carousel_display'] && count($panel['panel_partners']) > 1) {
		// 	echo '<div class="slick-navigation">';
		// 		echo '<div class="slick-arrows">';
		// 			echo '<a class="slick-prev slick-arrow" href="#"></a>';
		// 			echo '<a class="slick-next slick-arrow" href="#"></a>';
		// 		echo '</div>'; // close .slick-arrows
		// 	echo '</div>'; // close .slick-navigation
		// }
		// echo ($panel['carousel_display'] ? '<div class="panel-carousel-wrapper">' : '');
		// 	echo ($panel['carousel_display'] ? '<div class="partner-listings panel-carousel">' : '<div class="partner-listings animate" data-animate=".partner-listing" data-delay="150">');

		// 	foreach ($panel['panel_partners'] as $partner) {
		// 		$partner_id= $partner['partner_id'];
		// 		echo ($panel['carousel_display'] ? '<div class="panel-slide partner-slide animate">' : '');
		// 			include('includes/templates/partner-listing.php');
		// 		echo ($panel['carousel_display'] ? '</div>' : '');
		// 	}

		// 	echo '</div>'; // close .panel-carousel

			
		// echo ($panel['carousel_display'] ? '</div>' : ''); // close .panel-carousel-wrapper
?>
	<?php if (!empty($panel['panel_partners'])): ?>
	<div class="panel-gallery">
		<div class="gallery-listings">
			<div class="light-gallery animate" data-animate=".gal-item" data-delay="150">
				<?php foreach ($panel['panel_partners'] as $partner):
					
					?>
					<?php
						$partner['img'] = '<img src="'.$path.'images/partners/'.$partner['image'].'" alt="'.($partner['image_alt'] ?: $partner['name']).'" />';
						$imagePath = 'images/partners/'.$partner['image'];
						
						if ($thumb = check_file($imagePath)):
							$imageAlt = htmlspecialchars($partner['image_alt'] ?: $partner['name'], ENT_QUOTES);
							$caption = htmlspecialchars($partner['caption'] ?? $partner['name'], ENT_QUOTES);
					?>
						<div class="gal-item"
							 data-src="<?= $imagePath ?>"
							 data-exthumbimage="<?= $path . $thumb ?>">
							<a href="<?= $imagePath ?>"
							   target="_blank"
							   class="gal-link"
							   title="<?= $caption ?>">
								<div class="overlay"></div>
								<?= $partner['img']?>
							</a>
						</div>
					<?php endif; ?>
				<?php endforeach; ?>
			</div>
		</div>
	</div>
<?php endif; ?>

	</div> <!-- close .container-lg -->
</section>