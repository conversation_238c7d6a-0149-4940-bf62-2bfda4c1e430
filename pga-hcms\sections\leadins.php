<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>

	<div class="panel">
		<div class="panel-header">'.$records_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">

				<thead>
					<th width="1px" class="nopadding-r" data-sorter="false"></th>
					<th width="350px">Title</th>
					<th>Type</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" class="nowrap" data-sorter="false">&nbsp;</th>
				</thead>

				<tbody>';
				
				foreach($records_arr as $row){
					echo '<tr>
						<td class="nopadding-r">'.render_gravatar($imagedir.'thumbs/'.$row['image'], $imagedir.'thumbs/'.$row['image'], $row['title']).'</td>
						<td>'.$row['title'].'</td>
						<td>'.$leadin_types[$row['type']].'</td>
						<td>'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td class="right nowrap">
							<a href="'.$statspage['page_url'].'?page_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-chart-bar"></i>Stats</a>
							<a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a>
						</td>
					</tr>';
				}

				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';


//Image cropping
}else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');


//Display form
}else{
	$image = '';

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = check_file($data['image'], $imagedir);
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add') {
		if (!isset($_POST['save'])){
			unset($row);
		}
		$row['form_fields'] = $row['form_fields'] ?? NULL ?: $default_form_fields;
	}
	
	$leadin_type = $row['type'] ?? '';
	$leadin_pos  = $row['position'] ?? '';
	$button_type = $row['button_type'] ?? '';

	// Default placeholders, used when adding a new form field.  Will be overridden with POST data
	$placeholders = [
		// regex => replacement
		'/\[INDEX\]/'                   => '',
		'/\[HIDDEN\]/'                  => 'style="display: none;"',
		'/\[VALUE:FIELD_ID\]/'          => '',
		'/\[SELECTED:FIELD_TYPE:\w+\]/' => '',
		'/\[ERROR:FIELD_TYPE\]/'        => '',
		'/\[VALUE:FIELD_LABEL\]/'       => '',
		'/\[ERROR:FIELD_LABEL\]/'       => '',
		'/\[CHECKED:FIELD_REQUIRED\]/'  => '',
		'/\[VALUE:FIELD_OPTIONS\]/'     => '',
		'/\[ERROR:FIELD_OPTIONS\]/'     => '',
		'/\[SHOWHIDE:FIELD_OPTIONS\]/'  => 'style="display: none;"',
		'/\[!SHOWHIDE:FIELD_OPTIONS\]/' => 'style="display: block;"',
	];

	//Form
	echo '<form action="" method="post" enctype="multipart/form-data">';

		//Details
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show '.$record_name.'</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"'.(($row['showhide'] ?? 0) ? '' : ' checked').' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>
			<div class="panel-content">
				<div class="flex-container">
					<div class="form-field">
						<label>Type'.(in_array('type', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<select name="type" id="leadin-type-selector" class="animation-control select'.(in_array('type', $required) ? ' required' : '').'">
							<option value="">- Select -</option>';
						
						foreach($leadin_types as $type => $type_name){
							echo '<option data-toggle=".type-'.$type.'" value="'.$type.'"'.($leadin_type == $type ? ' selected' : '').'>'.$type_name.'</option>';
						}

						echo '</select>
					</div>

					<div class="form-field">
						<label>'.$record_name.' Title'.(in_array('title', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="title" value="'.($row['title'] ?? '').'" class="input'.(in_array('title', $required) ? ' required' : '').'"/>
					</div>
				</div>
			</div>
		</div>'; //Details

		//Display Settings
		echo '<div class="panel">
			<div class="panel-header">Display Settings
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">
				<div class="flex-container">
					<div class="form-field type-corner type-bar"'.($leadin_type == 'popup' ? ' style="display: none;"' : '').'>
						<label>Position on Screen <span class="required">*</span></label>
						<select name="position" id="leadin-position-selector" class="select'.(in_array('position', $required) ? ' required' : '').'">
							<option value="">- Select -</option>
							<option value="top"'.($leadin_pos == 'top' ? ' selected' : '').($leadin_type == 'bar' ? '' : ' disabled').'>Top</option>
							<option value="bottom"'.($leadin_pos == 'bottom' ? ' selected' : '').($leadin_type == 'bar' ? '' : ' disabled').'>Bottom</option>
							<option value="left"'.($leadin_pos == 'left' ? ' selected' : '').($leadin_type == 'corner' ? '' : ' disabled').'>Bottom-Left Corner</option>
							<option value="right"'.($leadin_pos == 'right' ? ' selected' : '').($leadin_type == 'corner' ? '' : ' disabled').'>Bottom-Right Corner</option>
						</select>
					</div>

					<div class="form-field">
						<label>Colour Theme'.(in_array('theme', $required_fields) ? ' <span class="required">*</span>' : '').''.$CMSBuilder->tooltip('Colour Theme', 'This will be used as the background colour.').'</label>
						<select name="theme" class="select">';
						
						foreach($leadin_themes as $theme_key => $theme_name){
							echo '<option value="'.$theme_key.'"'.(($row['theme'] ?? '') == $theme_key ? ' selected' : '').'>'.$theme_name.'</option>';
						}

						echo '</select>
					</div>
				</div>

				<hr>

				<div class="flex-container">
					<div class="form-field">
						<label>Delay Display'.(in_array('delay', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="delay" value="'.($row['delay'] ?? '').'" class="delay-related-field input input_sm'.(in_array('delay', $required) ? ' required' : '').'" placeholder="0"'.($leadin_type == 'bar' && $leadin_pos == 'top' ? ' disabled' : '').' />
						<select name="delay_type" class="delay-related-field select select_sm'.(in_array('delay_type', $required) ? ' required' : '').'"'.($leadin_type == 'bar' && $leadin_pos == 'top' ? ' disabled' : '').'>
							<option value="time"'.(($row['delay_type'] ?? '') == 'time' ? ' selected' : '').'>seconds</option>
							<option value="scroll"'.(($row['delay_type'] ?? '') == 'scroll' ? ' selected' : '').'>scroll position (%)</option>
						</select>
					</div>

					<div class="form-field">
						<label>Display on Mobile'.(in_array('show_mobile', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<select name="show_mobile" class="select'.(in_array('show_mobile', $required) ? ' required' : '').'">
							<option value="0">Hide</option>
							<option value="1"'.(($row['show_mobile'] ?? '') == 1 ? ' selected' : '').'>Show</option>
						</select>
					</div>

					<div class="form-field">
						<label>Display Again After __ Days'.(in_array('show_again_days', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Display Again After __ Days', 'When closed by user, '.$record_name.' will not be displayed until __ days.').'</label>
						<input type="text" name="show_again_days" value="'.($row['show_again_days'] ?? 14).'" class="input'.(in_array('show_again_days', $required) ? ' required' : '').'" />
					</div>
				</div>
			</div>
		</div>'; //Display Settings

		//Image
		echo '<div class="panel type-corner type-popup"'.($leadin_type == 'bar' ? ' style="display: none;"' : '').'>
			<div class="panel-header">'.$record_name.' Image
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">
				<div class="flex-container">';

					// Upload Image
					echo $CMSBuilder->img_holder($image, $imagedir.'thumbs/', NULL, true);

					[$corner_max_W, $corner_max_H] = CMSUploader::max_size('leadin-corner', 'image');
					[$popup_max_W, $popup_max_H] = CMSUploader::max_size('leadin-popup', 'image');
					echo '<div class="form-field">
						<label>Upload Image'.$CMSBuilder->tooltip('Upload Image', 'Image dimensions must be at least <b>'.$corner_max_W.' x '.$corner_max_H.'</b> (if Corner) or <b>'.$popup_max_W.' x '.$popup_max_H.'</b> (if Intrusive Pop-up) and file size must be smaller than '.$_max_filesize['megabytes'].'.').'</label>
						<input type="file" class="input'.(in_array('image', $required) ? ' required' : '').'" name="image" value="" />
					</div>

					<div class="form-field">
						<label>Alt Text: <small>(SEO)</small>'.$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.').'</label>
						<input type="text" name="image_alt" value="'.($row['image_alt'] ?? '').'" class="input" />
					</div>
				</div>
			</div>
		</div>'; //Image

		//Content
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Content'.(in_array('content', $required_fields) ? ' <span class="required">*</span>' : '').'
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content nopadding">
				<textarea name="content" class="tinymceMini'.(in_array('content', $required) ? ' required' : '').'">'.($row['content'] ?? '').'</textarea>
			</div>
		</div>'; //Content

		//Button Settings
		echo '<div class="panel">
			<div class="panel-header">What Should this Button do?
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">
					<div class="form-field">
						<label>Button Type'.(in_array('button_type', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<select name="button_type" id="leadin-button-type-selector" class="animation-control select'.(in_array('button_type', $required) ? ' required' : '').'">
							<option value="">- Select -</option>
							<option data-toggle=".button-type-link" value="link"'.($button_type == 'link' ? ' selected' : '').'>Link to URL</option>
							<option data-toggle=".button-type-form" value="form"'.($button_type == 'form' ? ' selected' : '').'>Open Form</option>
						</select>
					</div>
				</div>

				<div class="button-fields" style="'.($leadin_type == 'popup' && $button_type == 'form' ? 'display:none;' : '').'">
					<hr />
					
					<div class="flex-container">
						<div id="button-text-wrapper" class="form-field">
							<label>Button Text'.$CMSBuilder->tooltip('Link Text', $record_name.' link will be displayed with this text. Defaults to &quot;Learn More&quot;.').''.(in_array('url_text', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<input type="text" name="url_text" value="'.($row['url_text'] ?? '').'" class="input'.(in_array('url_text', $required) ? ' required' : '').'" />
						</div>

						<div class="form-field button-type-link"'.($button_type == 'link' ? '' : ' style="display: none;"').'>
							<label>Button Link <small>(URL, Phone, or Email)</small>'.$CMSBuilder->tooltip($record_name.' Link', 'If a link is provided, a clickable button will be displayed.<br /><br /><strong>URL</strong> - User will be taken to this link.<br /><strong>Phone</strong> - User will be prompted to call the phone number provided.<br />Example: (*************.<br /><strong>Email</strong> - User will be prompted to emaill the address provided.').$CMSBuilder->tooltip('Sitemap Reference', 'Click on the icon to view the sitemap. You can use the sitemap pop up as your reference when entering links.', '<span class="fas fa-sitemap"></span>', array('sitemap-reference')).''.(in_array('url', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<input type="text" name="url" value="'.($row['url'] ?? '').'" class="input" />
						</div>

						<div class="form-field button-type-link"'.($button_type == 'link' ? '' : ' style="display: none;"').'>
							<label>Open Link in'.(in_array('url_target', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<select name="url_target" class="select">
								<option value="0" '.(($row['url_target'] ?? '') == '0' ? 'selected' : '').'>Same Window</option>
								<option value="1" '.(($row['url_target'] ?? '') == '1' ? 'selected' : '').'>New Window</option>
							</select>
						</div>

						<div class="form-field type-bar"'.($leadin_type == 'bar' ? '' : ' style="display: none;"').'>
							<label>Button Style'.(in_array('button_style', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<select name="button_style" class="select">
								<option value="button">Button</option>
								<option value="simple-text"'.(($row['button_style'] ?? '') == 'simple-text' ? ' selected' : '').'>Simple Text</option>
							</select>
						</div>

						<div class="form-field">
							<label>Button Animation'.(in_array('button_animation', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<select name="button_animation" class="select">
								<option value="">None</option>';
							
							foreach($button_animations as $animation_id => $animation){
								echo '<option value="'.$animation_id.'"'.(($row['button_animation'] ?? '') == $animation_id ? ' selected' : '').'>'.$animation.'</option>';
							}

							echo '</select>
						</div>
					</div>
				</div>

				<div class="button-type-form"'.($button_type == 'form' ? '' : ' style="display: none;"').'>
					<hr />

					<div class="flex-container">
						<div class="form-field">
							<label>Form Title'.(in_array('form_title', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<input type="text" name="form_title" value="'.($row['form_title'] ?? '').'" class="input'.(in_array('form_title', $required) ? ' required' : '').'" />
						</div>

						<div class="form-field">
							<label>Send To'.(in_array('form_recipient', $required_fields) ? ' <span class="required">*</span>' : '').''.$CMSBuilder->tooltip('Send To', '<p>This is the address that all form submissions will send to. If left blank, submissions will send to the Attention Box Form Email Address assigned under Settings.</p><small><strong>Note:</strong> Comma separate emails if sending to more than one email. (e.g. <EMAIL>, <EMAIL>)</small>').'</label>
							<input type="text" name="form_recipient" value="'.($row['form_recipient'] ?? '').'" class="input'.(in_array('form_recipient', $required) ? ' required' : '').'" />
						</div>

						<div class="form-field">
							<label>Submit Button Text'.(in_array('form_button_text', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<input type="text" name="form_button_text" value="'.($row['form_button_text'] ?? '').'" class="input'.(in_array('form_button_text', $required) ? ' required' : '').'" />
						</div>
					</div>
				</div>
			</div>
		</div>'; //Button Settings

		//Form Fields
		echo '<div class="panel button-type-form"'.($button_type == 'form' ? '' : ' style="display: none;"').'>
			<div class="panel-header">Form Fields
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding" id="leadin-fields-table">
				<table width="100%" cellspacing="0" cellpadding="0" border="0" class="sortable">
					<thead>
						<th class="nopadding-r"></th>
						<th class="nopadding-r">Type <span class="required">*</span></th>
						<th class="nopadding-r">Label <span class="required">*</span></th>
						<th class="nopadding-r">Options <small>(Dropdown Only)</small></th>
						<th class="nopadding-r center">Required</th>
						<th></th>
					</thead>

					<tbody>';
						$template = '<tr id="leadin-field-template" class="leadin-field-row" [HIDDEN]>
							<td width="1px" class="handle nopadding-r"><i class="fas fa-arrows-alt"></i></td>

							<td width="1px" class="nopadding-r">
								<select name="form_fields[[INDEX]][type]" class="select nomargin animation-control[ERROR:FIELD_TYPE]">';
								
								foreach($field_types as $type){
									$template .= '<option data-toggle=".field-type-'.$type.'-%index%" value="'.$type.'"[SELECTED:FIELD_TYPE:'.$type.']>'.ucwords($type).'</option>';
								}

								$template .= '</select>
							</td>

							<td width="1px" class="nopadding-r">
								<input type="text" name="form_fields[[INDEX]][label]" value="[VALUE:FIELD_LABEL]" class="input nomargin[ERROR:FIELD_LABEL]" />
							</td>

							<td class="nopadding-r">
								<div class="form-field field-type-dropdown-%index%"[SHOWHIDE:FIELD_OPTIONS]>
									<input type="text" name="form_fields[[INDEX]][options]" value="[VALUE:FIELD_OPTIONS]" class="input input_lg nomargin tagEditor[ERROR:FIELD_OPTIONS]" />
								</div>

								<div class="form-field field-type-dropdown-%index%" data-anim-inverse [!SHOWHIDE:FIELD_OPTIONS]>
									<small>N/A</small>
								</div>
							</td>

							<td width="1px" class="nopadding-r">
								<div class="onoffswitch">
									<input type="checkbox" name="form_fields[[INDEX]][required]" id="field-required-%index%" value="1" [CHECKED:FIELD_REQUIRED] />
									<label for="field-required-%index%">
										<span class="inner"></span>
										<span class="switch"></span>
									</label>
								</div>
							</td>

							<td width="1px" class="right">
								<a class="button-sm delete-sm delete-template-btn"><i class="fas fa-trash-alt nomargin"></i></a>
								<input type="hidden" name="form_fields[[INDEX]][field_id]" value="[VALUE:FIELD_ID]" />
							</td>
						</tr>';

						// Initial replacements
						$leadin_template = preg_replace(array_keys($placeholders), $placeholders, $template);
						echo '<tr class="hidden"></tr>';

					$count = 0;
					foreach($row['form_fields'] as $index => $field){
						$dropdown = ($field['type'] ?? '') == 'dropdown';
						$field_placeholders = $placeholders;

						// Overwrite placeholders with real data
						$field_placeholders['/id="leadin-field-template"/']  = '';
						$field_placeholders['/%index%/']                     = $count;
						$field_placeholders['/\[INDEX\]/']                   = $count;
						$field_placeholders['/\[HIDDEN\]/']                  = '';
						$field_placeholders['/\[VALUE:FIELD_ID\]/']          = $field['field_id'] ?? '';
						$field_placeholders['/\[ERROR:FIELD_TYPE\]/']        = in_array("form_fields[$index][type]", $required) ? ' required' : '';
						$field_placeholders['/\[VALUE:FIELD_LABEL\]/']       = $field['label'] ?? '';
						$field_placeholders['/\[ERROR:FIELD_LABEL\]/']       = in_array("form_fields[$index][label]", $required) ? ' required' : '';
						$field_placeholders['/\[CHECKED:FIELD_REQUIRED\]/']  = ($field['required'] ?? 0) ? ' checked' : '';
						$field_placeholders['/\[VALUE:FIELD_OPTIONS\]/']     = $field['options'] ?? '';
						$field_placeholders['/\[ERROR:FIELD_OPTIONS\]/']     = in_array("form_fields[$index][options]", $required) ? ' required' : '';
						$field_placeholders['/\[SHOWHIDE:FIELD_OPTIONS\]/']  = $dropdown ? 'style="display: block;"' : 'style="display: none;"';
						$field_placeholders['/\[!SHOWHIDE:FIELD_OPTIONS\]/'] = !$dropdown ? 'style="display: block;"' : 'style="display: none;"';

						// Prepend select placeholders
						if(!empty($field['type'])){
							$regex = '/\[SELECTED:FIELD_TYPE:'.$field['type'].'\]/';
							$field_placeholders = [$regex => ' selected'] + $field_placeholders;
						}

						echo preg_replace(array_keys($field_placeholders), $field_placeholders, $template);
						$count++;
					}

					echo '</tbody>
				</table>

				<div class="pager">
					<div class="right"><a href="#" class="copy-template-btn button" data-lastid="'.$count.'"><i class="fas fa-plus"></i>Add Field</a></div>
				</div>
			</div>
		</div>';

		//Form Content
		echo '<div class="page-content button-type-form"'.($button_type == 'form' ? '' : ' style="display: none;"').'">
			<div class="tabs tab-ui">
				<ul>
					<li><a href="#intro">Form Intro</a></li>
					<li><a href="#success">Form Success Message</a></li>
				</ul>

				<div id="intro" class="nopadding">
					<textarea name="form_content" class="tinymceMini'.(in_array('form_content', $required) ? ' required' : '').'">'.($row['form_content'] ?? '').'</textarea>
				</div>

				<div id="success" class="nopadding">
					<textarea name="form_success_content" class="tinymceMini'.(in_array('form_success_content', $required) ? ' required' : '').'">'.($row['form_success_content'] ?? '').'</textarea>
				</div>
			</div>
		</div>'; //Form

		//Sticky footer
		include('includes/widgets/formbuttons.php');

		echo '<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'] .'" />
		<input type="hidden" name="keep_tags[]" value="content" />
		<input type="hidden" name="keep_tags[]" value="form_content" />
		<input type="hidden" name="keep_tags[]" value="form_success_content" />
	</form>';

	//Field template
	echo '<table class="hidden"><tbody>'.$leadin_template.'</tbody></table>';

}

?>