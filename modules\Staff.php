<?php

define('STAFF_ID', $SiteBuilder->get_pagebit_id('staff'));

//All staff
if(PAGE_ID == $_sitepages['staff']['page_id'] || (PARENT_ID == $_sitepages['staff']['page_id'] && is_null(PAGE_ID))){
	
	//Define vars
	$panel_id = 15;
	
	//Get staff categories
	$db->query("SELECT `staff_categories`.*, (SELECT COUNT(`staff_id`) FROM `staff` WHERE `category_id` = `staff_categories`.`category_id` && `showhide` = 0) AS `total` 
	FROM `staff_categories` WHERE `showhide` = 0 
	ORDER BY `ordering` ASC");
	$staff_categories = $db->fetch_assoc('category_id');
	
	//Get staff
	$db->query("SELECT * FROM `staff` WHERE `showhide` = 0 ORDER BY `ordering` ASC");
	$staff_arr = $db->fetch_assoc('staff_id');
	foreach($staff_arr as $id=>$row){
		
		//Set page url
		$staff_arr[$row['staff_id']]['page_url'] = $_sitepages['staff']['page_url'].$row['page'].'-'.$row['staff_id'].'/';
		
		//Add to category
		if(array_key_exists($row['category_id'], $staff_categories)){
			$staff_arr[$row['staff_id']]['category_name'] = $staff_categories[$row['category_id']]['name'];
		}
	}	
	
	//Selected staff
	if(PARENT_ID == $_sitepages['staff']['page_id'] && PAGE_ID == '' && $_sitepages['staff']['showhide'] < 2){
		if($staff = $staff_arr[STAFF_ID]){
			$error404 = false;

			//Set page vars
			$parent = $SiteBuilder->get_page_content(PARENT_ID);
			$page['page_title'] = $parent['page_title'];
			$page['description'] = '';
			$page['content'] = '';
			$page['page_panels'] = [];
			$page['meta_canonical'] = $staff['page_url'];
			$page['meta_title'] = ($staff['meta_title'] ?: $staff['name'].' | '.$parent['seo_title']);
			$page['meta_description'] = $staff['meta_description'] ?: $parent['meta_description'];
		
			//Set social sharing image
			if(check_file($staff['image'], 'images/staff/featured/')){
				$page['og_image']         = 'images/staff/featured/'.$staff['image'];
				
				[$width, $height] = getimagesize($page['og_image']);
				$page['og_image_type']    = mime_content_type($page['og_image']);
				$page['og_image_width']   = $width;
				$page['og_image_height']  = $height;
			} 

			//Ensure url is correct
			if($staff['page_url'] != $page['page_url']){
				header("HTTP/1.1 301 Moved Permanently");
				header('Location: ' .$staff['page_url']);
				exit();
			}

			//Add to breadcrumbs
			array_pop($breadcrumbs);
			array_push($breadcrumbs, array('name' => $staff['name'], 'url' => $staff['page_url']));
		
			//Get staff social links
			$db->query("SELECT * FROM `staff_social` WHERE `staff_id` = ? && `url` IS NOT NULL && `url` != ?", array(STAFF_ID, ''));
			$staff['social'] = $db->fetch_assoc('service');
		}
	}
}
	
?>