<?php

//List all Careers
if(!isset($career_id) || $career_id == ''){ 
	// print_r($career_id);
	// exit;
	//Load Panel Content
	
	$html = '';
	// if(!empty($page['page_panels'][$panel_id]['content'])){
	// 	$html .= (trim($page['page_panels'][$panel_id]['content']) != '' ? $page['page_panels'][$panel_id]['content'].'<hr />' : '');
	// }

	$html .= '<div  id="job-browse">';
	
	$html .= '<div class="container">';

	$html .= '<div id="job-filters-wrapper">';

		//Create Filtering form
				$html .= '<form id="filter-form">';
				
					
				
				$html .=	'
					<fieldset class="search-job">
						<input type="text" placeholder="Search" name="filter_search" value="'.(isset($_GET['filter_search']) ? $_GET['filter_search'] : '').'" class="input side nomargin" id="filter_search">
						<button type="submit" name="submit" id="submit" class="button solid side submit nomargin"><i class="fa fa-search"></i></button>
					</fieldset>

					<fieldset>';
				
					//Filter by Member Classification
					if(!empty($classes)){
						$html .= '<div class="dropdown-checkbox-wrapper">
							<label class="title side" data-default="Showing all Member Classes">Showing all Member Classes</label>
							<a class="button solid side submit"><i class="fa fa-angle-down"></i></a>

							<ul id="class-dropdown" class="checklist clearfix">';

							//Create a checkbox for each class in the database
							foreach ($classes as $key => $class){
								$html .= '<li title="'.$class['class_name'].'"><input name="class_ids[]" id="class'.$key.'" class="checkbox" type="checkbox" value="'.$class['class_id'].'"'. (isset($_GET['class_ids']) && in_array($class['class_id'], $_GET['class_ids']) ? 'checked' : '') .'><label for="class'.$key.'">'.$class['class_name'].'</label></li>';
							}

						$html .= '</ul>
						</div>';
					}

					//Filter by Career Category
					if(!empty($categories)){
						$html .= '<div class="dropdown-checkbox-wrapper">
							<label class="title side" data-default="Showing all Categories">Showing all Categories</label>
							<a class="button solid side submit"><i class="fa fa-angle-down"></i></a>

							<ul id="category-dropdown" class="checklist clearfix">';

							//Create a checkbox for each category in the database
							foreach ($categories as $key => $category){
								$html .= '<li title="'.$category['category_name'].'"><input name="category_ids[]" id="category'.$key.'" class="checkbox" type="checkbox" value="'.$category['category_id'].'"'. (isset($_GET['category_ids']) && in_array($category['category_id'], $_GET['category_ids']) ? 'checked' : '') .'><label for="category'.$key.'">'.$category['category_name'].'</label></li>';
							}

						$html .= '</ul>
						</div>';
					}

					//Filter by Facility
					if(!empty($facilities)){
						$html .= '<div class="dropdown-checkbox-wrapper">
							<label class="title side" data-default="Showing all Facilities">Showing all Facilities</label>
							<a class="button solid side submit"><i class="fa fa-angle-down"></i></a>

							<ul id="facility-dropdown" class="checklist clearfix">';

							//Create a checkbox for each facility in the database
							foreach ($facilities as $key => $facility){
								$html .= '<li title="'.$facility['facility_name'].'"><input name="facility_ids[]" id="facility'.$key.'" class="checkbox" type="checkbox" value="'.$facility['facility_id'].'"'. (isset($_GET['facility_ids']) && in_array($facility['facility_id'], $_GET['facility_ids']) ? 'checked' : '') .'><label for="facility'.$key.'">'.$facility['facility_name'].'</label></li>';
							}

						$html .= '</ul>
						</div>';
					}

					//Filter by Province
					$html .= '<div class="dropdown-checkbox-wrapper">
						<label class="title side" data-default="Showing all Provinces">Showing all Provinces</label>
						<a class="button solid side submit"><i class="fa fa-angle-down"></i></a>

						<ul id="province-dropdown" class="checklist clearfix">
							<li title="Alberta" ><input name="province[]" id="province1" class="checkbox" type="checkbox" value="AB"'. (isset($_GET['province']) && in_array('AB', $_GET['province']) ? 'checked' : '') .'><label for="province1">Alberta</label></li>
							<li title="Outside" ><input name="province[]" id="province2" class="checkbox" type="checkbox" value="Outside"'. (isset($_GET['province']) && in_array('Outside', $_GET['province']) ? 'checked' : '') .'><label for="province2">Outside Province</label></li>
						</ul>
						
					</div>';
				
					//Filter by status	
					if(MEMBER_ACCESS){
						$html .= '<p class="radio-buttons">
							<input type="radio" name="status" class="radio" value="current" id="status-1"' .(!isset($_GET['status']) || $_GET['status'] == 'current' ? ' checked' : '').' />
							<label for="status-1"><small>Current Postings</small></label>
							<input type="radio" name="status" class="radio" value="archived" id="status-2"' .(isset($_GET['status']) && $_GET['status'] == 'archived' ? ' checked' : '').' />
							<label for="status-2"><small>Archived Postings</small></label>
						</p>';
					}
				
						$html .= '<p class="nopadding"><button type="submit" name="submit" id="submit" class="button primary back-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Filter</button>
						<a href="'.$page['page_url'].'" class="button secondary">Clear</a></p>
					</fieldset>
					
				</form></br>';
//Login prompt
					if(!USER_LOGGED_IN){
						$html .= '<p><small>Members <a href="' .$_sitepages['login']['page_url']. '?redirect=' .urlencode($_SERVER['REQUEST_URI']). '">login here</a> for full access.</small></p>';
					}
				
					$html .= '<p>
						<a href="' .$_sitepages['post-job']['page_url']. '">Submit a Job Posting</a> 
						<span class="divider color-light">|</span> 
						<a href="' .$_sitepages['my-job-postings']['page_url']. '">Edit My Postings</a>
					</p>';
	$html .= '</div>'; //close #job-filters-wrapper

	$html .= '<div id="job-listings-wrapper">';
		//Create a listing for each career in the database
	if(!empty($careers)){
		foreach($careers as $career){ 
	
			$isnew = (date('Y-m-d', strtotime($career['posted_date'].' + 1 week')) > date('Y-m-d'));
			
			$html .= '<section class="listing" id="career-'.$career['career_id'].'">
				<div class="job_details">
				<p><a class="job-title" href="'.$page['page_url'].$career['page'].'-'.$career['career_id'].'/'.'">'.$career['title'].'</a>'
				.($isnew ? ' <small class="color-red">NEW!</small>' : '')
				.(!empty($career['category_name']) ? ' <small class="color-red">- '.$career['category_name'].' </small>' : '').'</p>
				<p>'.$career['facility_name'].' in '.$career['city'].', '.$career['province'].'</p>
				<p><small>Posted: '.date('M j, Y', strtotime($career['posted_date'])).' <span class="divider">/</span> Closing: '. date('M j, Y', strtotime($career['closing_date'])).'</small></p>
				</div>
				<a href="'.$page['page_url'].$career['page'].'-'.$career['career_id'].'/'.'" class="button solid side submit"><i class="fa fa-angle-right"></i></a>
			</section>';
		}
		
		//Pager
		$html .= '<p class="pager clear"><small>';
			$html .= 'Displaying '.($pg == 'all' ? '1 - '.$total_careers : (1+($limit*($pg-1))).' - '.(count($careers)+($limit*($pg-1)))).' (of '.$total_careers.' Total)<br />';
			if($total_careers > $limit && $pg != 'all'){
				$tagend = round($total_careers % $limit, 0);
				$splits = round(($total_careers - $tagend)/$limit, 0);
				$num_pages = ($tagend == 0 ? $splits : $splits+1);	
				$pos = $pg;
				$startpos = ($pos*$limit)-$limit;
				$filtering = '';
				foreach($_GET as $key=>$data){
					if(is_array($data)){
						foreach($data as $value){
							$filtering .= '&'.$key.'[]='.$value;	
						}
					}else{
						if($key != 'pg'){
							$filtering .= '&'.$key.'='.$data;	
						}
					}
				}

				$html .= ($pos > 1 ? '<a href="'.$page['page_url'].'?pg='.($pos-1).$filtering.'">&lsaquo; Prev</a> ' : '');
				for($i=1; $i<=$num_pages; $i++){
					$html .= ($i != $pos ? '<a href="'.$page['page_url'].'?pg='.$i.$filtering.'">'.$i.'</a> ' : '<span><u>'.$i.'</u></span> ');
				}
				$html .= ($pos < $num_pages ? '<a href="'.$page['page_url'].'?pg='.($pos+1).$filtering.'">Next &rsaquo;</a> ' : '');
			}
		$html .= '</small></p>';

	 //No careers found
	}else{
		$html .= '<p>No job postings found.' .($search ? ' Please try a new search.' : ''). '</p>';
	}
	$html .= '</div>';
	$html .= '</div>';
	
	$html .= '</div>';
	
	//Set panel content
	// $page['page_panels'][$panel_id]['show_title_first'] = true;
	// $page['page_panels'][$panel_id]['content'] .= $html;
	// $page['page_panels'][$panel_id]['sidebar'] .= $form;
	// $page['page_panels'][$panel_id]['sidebar_first'] = true;
// if (isset($page['page_panels'][$panel_id])) {
			$page['page_panels'][$panel_id]['append_content'] = $html;
		// }
	
//Display single career	
}else if (USER_LOGGED_IN || $career['public'] == 1) {
	// print_r($career);
	// exit;
	$html = '<h2 class="underline">Job Details</h2>
	<section id="careers">';

		$html .= '<aside class="right-column">
			<h6>Facility</h6>
			<p><a href="'.$parent['page_url'].'?facility_ids%5B%5D='.$career['facility_id'].'">'.$career['facility_name'].'</a></p>
			<hr />

			<h6>Location</h6>
			<p><a href="'.$parent['page_url'].'?filter_search='.$career['city'].'">'.$career['city'].'</a>, <a href="'.$parent['page_url'].'?province%5B%5D='.$career['province'].'">'.$career['province'].'</a></p>
			<hr />';
	
			if(!empty($career['category_name'])){
				$html .= '<h6>Category</h6>
				<p><a href="'.$parent['page_url'].'?category_ids%5B%5D='.$career['category_id'].'">'.$career['category_name'].'</a></p>
				<hr />';
			}

			$html .= '<h6>Posted</h6>
			<p>'.date('F j, Y', strtotime($career['posted_date'])).'</p>
			<hr />

			<h6>Closing</h6>
			<p>'.date('F j, Y', strtotime($career['closing_date'])).'</p>
			<hr />

			<h6>Monthly Salary</h6>
			<p>'.$career['salary'].'</p>
			<hr />
			
			<h6>Duration</h6>
			<p>'.$career['duration'].'</p>
			<hr />
			
			<h6>Contact Information</h6>
			<p>
				'.$career['first_name'].' '.$career['last_name'].'<br>
				<a href="mailto:'.$career['email'].'">'.$career['email'].'</a><br>
				<a href="tel:'.$career['phone'].'">'.formatPhoneNumber($career['phone']).'</a>
			</p>
			<hr />
		</aside>

		<section class="left-column">
			<h3>' .$career['title']. '</h3>';
			$html .= $career['content'];
			if($career['file_name'] != '' && file_exists('uploads/files/'.$career['file_name'])){
				$html .= '<p><a href="' .$path. 'uploads/files/' .$career['file_name']. '" target="_blank"><i class="fa fa-file-pdf-o"></i>&nbsp; ' .$career['title']. '</a></p>';
			}	

		$html .= '<div class="form-buttons clearfix">';
	
			//Check if application is open
			if(date("U") > date("U", strtotime($career['posted_date'])) && date("U") < date("U", strtotime($career['closing_date']))){
				$apply = true;
				$html .= '<a href="'.$_sitepages['application']['page_url'].'?id='.$career['career_id'].'" class="button primary red"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Apply Now</a>';
			}else{
				$apply = false;
				$html .= '<h6>Applications are now closed.</h6>';
			}
	
			//Check if current user is author
			if($career['account_id'] && USER_LOGGED_IN == $career['account_id']){				
				$html .= '<a href="'.$_sitepages['my-job-postings']['page_url'].'?edit='.$career['career_id'].'" class="button primary black back-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span>Edit</a>';
			}

			//Back button
			$html .= '<a class="' .($apply || ($career['account_id'] && USER_LOGGED_IN == $career['account_id']) ? 'previous' : ''). ' button secondary" href="'.$parent['page_url'].'"> Back to All</a>';

		$html .= '</div>
		</section>
	</section>';
	
	//Set page content
	$page['content'] = $html;
}

//Page panels
include('includes/pagepanels.php');

?>
