<?php

/*-----------------------------------/
* Deals with all functionality associated with pages and content
* <AUTHOR> Army
* @date		13-09-06
* @file		SiteBuilder.class.php
*/

class SiteBuilder{

	/*-----------------------------------/
	* @var path
	* Relative path to top of the site
	*/
	public $path;

	/*-----------------------------------/
	* @var pathbits
	* Array of url segments
	*/
	public $pathbits;

	/*-----------------------------------/
	* @var db
	* Mysqli database object for this class
	*/
	private $db;

	/*-----------------------------------/
	* @var sitemap
	* Non-recursive array of all pages in site map
	*/
	private $sitemap;

	/*-----------------------------------/
	* @var navigation
	* Recursive array of navigation links
	*/
	private $navigation;

	/*-----------------------------------/
	* @var pageurl
	* Url of current page
	*/
	private $pageurl;

	/*-----------------------------------/
	* @var settings
	* Array of global settings data
	*/
	private $settings;

	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @param	$path		Relative path to top of the site
	* @return	SiteBuilder	New SiteBuilder object
	* @throws	Exception
	*/
	public function __construct($path='/'){

		//Set database instance
		$this->db = Database::get_instance();

		//Get path variables
		$pageurl = $_SERVER['REQUEST_URI'];
		if(!empty($_SERVER['QUERY_STRING'])){
            $pageurl = str_replace('?'.$_SERVER['QUERY_STRING'], '', $pageurl);
        }
		if($pageurl == $path || $pageurl == $path.'index.php'){
			$pageurl = $path.'home/';
		}
		if(empty($pageurl)){
			$pathbits = array('');
		}else{
			$pathbits = explode("/",  $pageurl);
		}
		$shifts = explode("/", $path);
		for($i=0; $i<count($shifts)-2; $i++){
			array_shift($pathbits);
		}
		foreach($pathbits as $key => $bit){
			$pathbits[$key] = strip_data($bit);
			if($pathbits[$key] == "?".$_SERVER['QUERY_STRING']){
				$pageurl = str_replace($pathbits[$key], '', $pageurl);
				$pathbits[$key] = "";
			}
		}

		//Set path variables
		$this->path = $path;
		$this->pathbits = $pathbits;
		$this->pageurl = $pageurl;

		//Load global settings
		$this->settings = array();
		try{
			$this->fetch_settings();
		}catch(Exception $e){
			throw new Exception($e->getMessage());
		}

		//Load sitemap and navigation
		$this->sitemap = array();
		$this->navigation = array();
		try{
			$this->fetch_pages();
		}catch(Exception $e){
			throw new Exception($e->getMessage());
		}
    }

	/*-----------------------------------/
	* Loads the sitemap and navigation data into this object
	*
	* <AUTHOR> Army
	* @throws	Exception
	*/
	private function fetch_pages(){
		$this->db->query("SELECT * FROM `pages` ORDER BY `ordering`");
		if(!$this->db->error()){
			$result = $this->db->fetch_array();

			$this->sitemap = $this->build_sitemap($result);
			$this->navigation = $this->build_navigation($this->sitemap);

		}else{
			throw new Exception('Error retrieving site map: '.$this->db->error());
		}
	}

	/*-----------------------------------/
	* Reorders pages based on hierarchy and builds flat array of pages
	*
	* <AUTHOR> Army
	* @param	$pages_arr	Array of pages with page ID as key
	*/
	private function build_sitemap($pages_arr=array(), $parent_id=0){
		$row = array();
		foreach($pages_arr as $page){
			if($page['parent_id'] == $parent_id){
				$row[$page['page_id']] = $page;
				$row[$page['page_id']]['original_url'] = $this->path.$page['page'].'/';
				$row[$page['page_id']]['page_url'] = $this->path.($page['slug'] != NULL ? $page['slug'] : $page['page']).'/';
				if($page['meta_canonical'] != ''){
					$row[$page['page_id']]['meta_canonical'] = $this->path.$page['meta_canonical'];
				}
				$row[$page['page_id']]['seo_title'] = $page['meta_title'] ?? '' ?: $page['page_title'];
				$row[$page['page_id']]['banner_image'] = $page['image'];
				$row[$page['page_id']]['banner_image_alt'] = $page['image_alt'];
				$row[$page['page_id']]['theme'] = $page['theme'] ?: $this->settings['theme'];

				$children = $this->build_sitemap($pages_arr, $page['page_id']);
				if($children){
					foreach($children as $child){
						$row[$child['page_id']] = $child;
						$row[$child['page_id']]['original_url'] = $row[$child['parent_id']]['page_url'].$child['page'].'/';
						$row[$child['page_id']]['page_url'] = $row[$child['parent_id']]['page_url'].($child['slug'] != NULL ? $child['slug'] : $child['page']).'/';
						if($child['meta_canonical'] != ''){
							$row[$child['page_id']]['meta_canonical'] = $this->path.$child['meta_canonical'];
						}
						$row[$child['page_id']]['seo_title'] = $child['meta_title'] ?? '' ?: $child['page_title'];
						$row[$child['page_id']]['banner_image'] = $child['image'] ?? '' ?: $row[$child['parent_id']]['banner_image'];
						$row[$child['page_id']]['banner_image_alt'] = $child['image_alt'] ?? '' ?: $row[$child['parent_id']]['banner_image_alt'];
						$row[$child['page_id']]['theme'] = $child['theme'] ?? '' ?: $row[$child['parent_id']]['theme'];
					}
				}
			}
		}
		return $row;
	}

	/*-----------------------------------/
	* Builds nested array of pages excluding hidden pages
	*
	* <AUTHOR> Army
	* @param	$pages_arr	Array of pages with page ID as key
	*/
	private function build_navigation($pages_arr=array(), $parent_id=0){
		$row = array();
		foreach($pages_arr as $page){
		if($page['parent_id'] == $parent_id && $page['showhide'] == 0 && $page['page_id'] > 2){
				$children = $this->build_navigation($pages_arr, $page['page_id']);
				if($children){
					$page['sub_pages'] = $children;
				}else{
					$page['sub_pages'] = array();
				}
				$row[$page['page_id']] = $page;
			}
		}
		return $row ;
	}

	/*-----------------------------------/
	* Loads global website settings into this object
	*
	* <AUTHOR> Army
	* @throws	Exception
	*/
	private function fetch_settings(){
		$this->db->query("SELECT * FROM `global_settings`");
		if(!$this->db->error()){
			$result = $this->db->fetch_array();

			//Set defaults
			$result[0]['locations'] = array();
			$result[0]['social'] = array();
			$result[0]['contact_address'] = NULL;
			$result[0]['contact_address2'] = NULL;
			$result[0]['contact_city'] = NULL;
			$result[0]['contact_province'] = NULL;
			$result[0]['contact_postal_code'] = NULL;
			$result[0]['contact_country'] = NULL;
			$result[0]['contact_phone'] = NULL;
			$result[0]['contact_fax'] = NULL;
			$result[0]['contact_toll_free'] = NULL;
			$result[0]['contact_hours'] = array();
			$result[0]['contact_email'] = NULL;
			$result[0]['gpslat'] = NULL;
			$result[0]['gpslong'] = NULL;
			$result[0]['google_place_id'] = NULL;
			$result[0]['keyword_inserts'] = array();

			//Get locations
			$this->db->query("SELECT * FROM `locations` ORDER BY `head_office` DESC, `ordering` ASC");
			if(!$this->db->error()){
				$result2 = $this->db->fetch_array();
				foreach($result2 as $row2){
					$row2['location_numbers'] = array();
					$row2['location_hours'] = array();

					//Get phone numbers
					$this->db->query("SELECT * FROM `location_numbers` WHERE `phone` IS NOT NULL && `phone` != ? && `location_id` = ?", array('', $row2['location_id']));
					$row2['location_numbers'] = $this->db->fetch_array();

					//Get additional phones and emails
					$this->db->query("SELECT * FROM `location_contacts` WHERE `location_id` = ?", array($row2['location_id']));
					$row2['location_contacts'] = $this->db->fetch_array();

					//Get business hours
					$this->db->query("SELECT * FROM `location_hours` WHERE `location_id` = ".$row2['location_id']);
					$row2['location_hours'] = $this->db->fetch_array();

					//Format address
					$row2['full_address'] = prettify_address($row2);

					//Push to locations if visible
					if($row2['showhide'] == 0){
						$result[0]['locations'][] = $row2;
					}

					//Set defaults to head office
					if($row2['head_office']){
						$result[0]['full_address'] = $row2['full_address'];
						$result[0]['contact_address'] = $row2['address'];
						$result[0]['contact_address2'] = $row2['address2'];
						$result[0]['contact_city'] = $row2['city'];
						$result[0]['contact_province'] = $row2['province'];
						$result[0]['contact_postal_code'] = $row2['postal_code'];
						$result[0]['contact_country'] = $row2['country'];
						$result[0]['contact_phone'] = $row2['phone'];
						$result[0]['contact_fax'] = $row2['fax'];
						$result[0]['contact_toll_free'] = $row2['toll_free'];
						$result[0]['contact_email'] = $row2['email'];
						$result[0]['contact_hours'] = ($row2['show_hours'] ? $row2['location_hours'] : array());
						$result[0]['gpslat'] = $row2['gpslat'];
						$result[0]['gpslong'] = $row2['gpslong'];
						$result[0]['zoom'] = $row2['zoom'];
						$result[0]['google_place_id'] = $row2['google_place_id'];
					}
				}
			}

			//Get social links
			$this->db->query("SELECT * FROM `global_social`");
			$result5 = $this->db->fetch_array();
			foreach($result5 as $social){
				$result[0]['social'][$social['service']] = $social['url'];
			}

			//Get aggregate rating
			$this->db->query("SELECT COUNT(*) AS `total`, ROUND(AVG(`rating`), 1) AS `average` FROM `reviews` WHERE `rating` IS NOT NULL");
			$result6 = $this->db->fetch_array();
			$result[0]['reviews_total'] = $result6[0]['total'];
			$result[0]['reviews_average'] = $result6[0]['average'];

			//Get dynamic keyword inserts
			$this->db->query("SELECT * FROM `keyword_inserts`");
			$result7 = $this->db->fetch_array();

			$result[0]['keyword_inserts']['shortcodes'] = array();
			$result[0]['keyword_inserts']['values'] = array();

			foreach($result7 as $keyword_insert) {
				$value = $keyword_insert['default_value']; // The value that will replace the shortcode
				$accepted_values = explode('|', $keyword_insert['accepted_values']); // Array of accepted values
				$accepted_lowercase_values = explode('|', strtolower($keyword_insert['accepted_values'])); // Array of accepted values in lower case format (used to validate the passed value)

				//If the keyword insert query string label is set
				if(isset($_GET[$keyword_insert['query_string_label']]) && !empty($accepted_values)) {
					$passed_value = strtolower(urldecode($_GET[$keyword_insert['query_string_label']])); // Passed value in lower case format
					$is_valid = array_search($passed_value, $accepted_lowercase_values); // Check the validity of the passed value (case insensitive)

					//If the passed value is valid, use it as the new value to replace the shortcode
					if($is_valid !== false) {
						$value = $accepted_values[$is_valid];
					}
				}

				$result[0]['keyword_inserts']['shortcodes'][] = '[' . $keyword_insert['query_string_label'] . ']';
				$result[0]['keyword_inserts']['values'][] = $value;
			}

			//Save settings to object
			$this->settings = $result[0];

			//Set timezone if available
			if($this->settings['timezone'] != NULL){
				date_default_timezone_set($this->settings['timezone']);
			}

		}else{
			throw new Exception('Error retrieving global settings: '.$this->db->error());
		}
	}

	/*-----------------------------------/
	* Gets full site map for website
	*
	* <AUTHOR> Army
	* @return	Array of pages
	*/
	public function get_sitemap(){
		return $this->sitemap;
	}

	/*-----------------------------------/
	* Gets navigation structure for website
	*
	* <AUTHOR> Army
	* @return	Array of navigation links
	*/
	public function get_navigation(){
		return $this->navigation;
	}

	/*-----------------------------------/
	* Gets global website settings
	*
	* <AUTHOR> Army
	* @return	Array of global data
	*/
	public function global_settings(){
		return $this->settings;
	}

	/*-----------------------------------/
	* Gets all data for requested page
	*
	* <AUTHOR> Army
	* @param	$page	Unique page id or relative page url (ie. /about-us/our-team/)
	* @return	Array of page data. If page is not found returns 404 data
	*/
	public function get_page_content($page){

		//Get page id
		if(!is_numeric($page)){
			$page_id = $this->get_page_id($page);
		}else{
			$page_id = $page;
		}

		//Get global settings
		$global = $this->settings;

		//Retrieve page data
		$this->db->query("SELECT * FROM `pages` WHERE `page_id` = ? && `showhide` < 2", array($page_id));
		$results = $this->db->fetch_array();
		if($results){
			$row = $results[0];

			//Get page panels
			$row['page_panels'] = $this->get_page_panels($row['page_id']);

			//Get page panels
			$row['page_form'] = $this->get_page_form($row['page_id']);

			//Set target string
			$target = $row['urltarget'];
			if($target == 0){ $target = "_self"; }else{ $target = "_blank"; }

			//Get default meta data
			$page_title = str_replace(['{','}'], '', $row['page_title']);
			$row['seo_title'] = $row['meta_title'] ?? '' ?: $page_title;
			$row['meta_title'] = $row['meta_title'] ?: $page_title. ' | ' .$global['meta_title'];
			$row['meta_description'] = $row['meta_description'] ?: $page_title. ' - ' .$global['meta_description'];

			// Replace the page keyword insert shortcodes with their corresponding values
			$row['meta_title'] = $this->replace_keyword_inserts($row['meta_title']);
			$row['meta_description'] = $this->replace_keyword_inserts($row['meta_description']);
			$row['page_title'] = $this->replace_keyword_inserts($row['page_title']);
			$row['description'] = $this->replace_keyword_inserts($row['description']);
			$row['button_text'] = $this->replace_keyword_inserts($row['button_text']);
			$row['form_title'] = $this->replace_keyword_inserts($row['form_title']);
			$row['form_description'] = $this->replace_keyword_inserts($row['form_description']);
			$row['form_button_text'] = $this->replace_keyword_inserts($row['form_button_text']);

			//Should we redirect to slug?
			$row['redirect_to_slug'] = false;

			if($row['slug'] != NULL){
				//Only redirect if we are not already on this page
				if($this->pageurl != $this->sitemap[$page_id]['page_url']){
					$row['redirect_to_slug'] = true;
				}
				$row['meta_canonical'] = $this->sitemap[$page_id]['meta_canonical'];
			}
			$row['meta_canonical'] = $row['meta_canonical'] ?: ($row['page_id'] == 3 && trim($row['slug']) == '' ? '' : $this->sitemap[$page_id]['page_url']);

			//Set page url
			$row['page_url'] = $this->sitemap[$page_id]['page_url'];

			//Set banner image
			$row['banner_image'] = $this->sitemap[$page_id]['banner_image'];
			$row['banner_image_alt'] = $this->sitemap[$page_id]['banner_image_alt'];
			if(empty($row['banner_image'])){
				$row['banner_image'] = $global['banner_image'];
				$row['banner_image_alt'] = $global['banner_image_alt'];
			}

			//Set theme
			$row['theme'] = $this->sitemap[$page_id]['theme'];

			//Set leadin
			$row['leadin'] = $this->get_attached_leadin($row['leadin_id']);

			//Not found
			$row['error404'] = false;

			return $row;

		}

		//404 error
		$this->db->query("SELECT * FROM `pages` WHERE page_id = 1");
		if(!$this->db->error()){
			$results = $this->db->fetch_array();
			$row = $results[0];

			//Set variables
			$row['error404'] = true;
			$row['page_id'] = NULL;
			$row['parent_id'] = $this->get_parent_id($this->pageurl);
			$row['banner_image'] = $global['banner_image'];
			$row['banner_image_alt'] = $global['banner_image_alt'];
			$row['page_url'] = $this->pageurl;
			$row['meta_canonical'] = $this->path.'404/';

			return $row;

		}else{
			trigger_error('Error retrieving 404 data: '.$this->db->error());
		}

	}

	/*-----------------------------------/
	* Gets all data for current page
	*
	* <AUTHOR> Army
	* @return	Array of page data. If page is not found returns 404 data
	*/
	public function curr_page_content(){
		return $this->get_page_content($this->pageurl);
	}

	/*-----------------------------------/
	* Gets all form fields for a given page
	*
	* <AUTHOR> Army
	* @param	$page_id
	* @return	Array of form fields
	*/
	public function get_page_form($page_id){
		$this->db->query("SELECT * FROM `pages_form` WHERE `page_id` = ? ANd `showhide` = 0 ORDER BY `ordering` ASC", [$page_id]);
		$results = $this->db->fetch_assoc('name');

		// Format data
		foreach ($results as $name => $row) {
			$row['label'] = $this->replace_keyword_inserts($row['label']);

			// Split options into array
			$row['options'] = explode('|', $row['options']);
			foreach ($row['options'] as $key => $option)
				$row['options'][$key] = $this->replace_keyword_inserts($option);

			$results[$name] = $row;
		}

		return $results;
	}

	/*-----------------------------------/
	* Gets all panels for a given page
	*
	* <AUTHOR> Army
	* @param	$page_id
	* @return	Array of page panels
	*/
	public function get_page_panels($page_id){
		$response = array();
		
		$this->db->query("SELECT * FROM `pages_panels` WHERE `showhide` = 0 && `page_id` = ? ORDER BY `ordering` ASC, `panel_id` ASC", array($page_id));
		$result = $this->db->fetch_array();
		foreach($result as $row){
			$showpanel = true;

			if($row['panel_type'] == 'cta') {
				$row['cta'] = $this->get_attached_cta($row['cta_id']);
				$showpanel = $row['cta'] ? $showpanel : false;

			} else if($row['panel_type'] == 'form') {
				$row['form'] = $this->get_attached_form($row['form_id']);
				$showpanel = $row['form'] ? $showpanel : false;

			} else if($row['panel_type'] == 'gallery') {
				$row['gallery'] = $this->get_attached_gallery($row['gallery_id'], $row['gallery_limit']);

			} else if ($row['panel_type'] == 'promo') {
				$row['panel_promos'] = $this->get_attached_promos($row['panel_id']);

			} else if($row['panel_type'] == 'staff') {
				$row['panel_staff'] = $this->get_attached_staff($row['panel_id']);

			} else if($row['panel_type'] == 'faqs') {
				$row['panel_faqs'] = $this->get_attached_faqs($row['panel_id']);
				
			} else if($row['panel_type'] == 'reviews') {
				$row['panel_reviews'] = $this->get_attached_reviews($row['panel_id']);
			//Get Partners
			}else if($row['panel_type'] == 'partners') {
				$row['panel_partners'] = $this->get_attached_partners($row['panel_id']);
			}


			if($showpanel){
				
				//Get panel tabs
				$this->db->query("SELECT * FROM `pages_panels_tabs` WHERE `panel_id` = ? && `showhide` = 0 ORDER BY `ordering`, `panel_id`", array($row['panel_id']));
				$row['panel_tabs'] = $this->db->fetch_array();


				//Replace the keyword insert shortcodes
				$row['title'] = $this->replace_keyword_inserts($row['title']);
				$row['subtitle'] = $this->replace_keyword_inserts($row['subtitle']);
				$row['content'] = $this->replace_keyword_inserts($row['content']);
				$row['url_text'] = $this->replace_keyword_inserts($row['url_text']);

				if(!empty($row['cta'])) {
					$row['cta']['title'] = $this->replace_keyword_inserts($row['cta']['title']);
					$row['cta']['subtitle'] = $this->replace_keyword_inserts($row['cta']['subtitle']);
					$row['cta']['url_text'] = $this->replace_keyword_inserts($row['cta']['url_text']);
				}

				if(!empty($row['gallery'])) {
					$row['gallery']['name'] = $this->replace_keyword_inserts($row['gallery']['name']);
					$row['gallery']['content'] = $this->replace_keyword_inserts($row['gallery']['content']);
				}

				foreach($row['panel_promos'] ?? [] as $index => $promo) {
					$row['panel_promos'][$index]['title'] = $this->replace_keyword_inserts($row['panel_promos'][$index]['title']);
					$row['panel_promos'][$index]['description'] = $this->replace_keyword_inserts($row['panel_promos'][$index]['description']);
					$row['panel_promos'][$index]['url_text'] = $this->replace_keyword_inserts($row['panel_promos'][$index]['url_text']);
				}

				foreach($row['panel_tabs'] as $index => $tab) {
					$row['panel_tabs'][$index]['title'] = $this->replace_keyword_inserts($row['panel_tabs'][$index]['title']);
					$row['panel_tabs'][$index]['content'] = $this->replace_keyword_inserts($row['panel_tabs'][$index]['content']);
				}

				$response[$row['panel_id']] = $row;
			}
		}

		return $response;
	}

	/*-----------------------------------/
	* Replaces the keyword insert shortcodes found in the provided string with its corresponding value
	*
	* <AUTHOR> Army
	* @param	$string
	* @return	String containing the shortcodes that were replaced
	*/
	public function replace_keyword_inserts($subject){
		//Get keyword inserts
		$keyword_inserts = $this->settings['keyword_inserts'];

		$subject = trim(str_replace($keyword_inserts['shortcodes'], $keyword_inserts['values'], $subject));

		return $subject;
	}

	/*-----------------------------------/
	* Gets all attached staff members with category and social media data
	*
	* <AUTHOR> Army
	* @param	$parent_id
	* @return	Array of staff
	*/
	public function get_attached_staff($parent_id){
		$this->db->query("SELECT 
			`staff`.*, 
			`staff_categories`.`name` as category, 
			`staff_categories`.`page` as category_page 

		FROM `pages_panels_staff` 
			LEFT JOIN `staff` ON `staff`.`staff_id` = `pages_panels_staff`.`staff_id` 
			LEFT JOIN `staff_categories` ON `staff_categories`.`category_id` = `staff`.`category_id` 

		WHERE 
			`pages_panels_staff`.`panel_id` = ? AND 
			`staff`.`showhide` = 0 AND 
			`staff_categories`.`showhide` = 0 

		GROUP BY 
			`staff`.`staff_id` 

		ORDER BY 
			`pages_panels_staff`.`ordering`, 
			`staff`.`staff_id`", [$parent_id]);
		$response = $this->db->fetch_assoc('staff_id');

		// Get social media
		foreach ($response as $staff_id => $member) {
			$this->db->query("SELECT * FROM `staff_social` WHERE `staff_id` = ?", [$staff_id]);
			$response[$staff_id]['social'] = array_column($this->db->fetch_assoc('service'), 'url', 'service');
		}

		return $response;
	}

	/*-----------------------------------/
	* Gets all attached promo boxes
	*
	* <AUTHOR> Army
	* @param	$parent_id
	* @return	Array of promo boxes
	*/
	public function get_attached_promos($parent_id){
		$this->db->query("SELECT `promo_boxes`.* FROM `pages_panels_promo` LEFT JOIN `promo_boxes` ON `promo_boxes`.`promo_id` = `pages_panels_promo`.`promo_id` WHERE `pages_panels_promo`.`panel_id` = ? && `promo_boxes`.`showhide` = 0 GROUP BY `promo_boxes`.`promo_id` ORDER BY `pages_panels_promo`.`ordering`, `promo_boxes`.`promo_id`", array($parent_id));
		return $this->db->fetch_assoc('promo_id');
	}

	/*-----------------------------------/
	* Gets data for a given cta_id
	*
	* <AUTHOR> Army
	* @param	$cta_id
	* @return	Array of cta data
	*/
	public function get_attached_cta($cta_id){
		$this->db->query("SELECT * FROM `pages_cta` WHERE `showhide` = 0 && `cta_id` = ?", array($cta_id));
		return $this->db->fetch_array()[0] ?? [];
	}

	/*-----------------------------------/
	* Gets all attached FAQs
	*
	* <AUTHOR> Army
	* @param	$parent_id
	* @return	Array of FAQs
	*/
	public function get_attached_faqs($parent_id){
		$this->db->query("SELECT
			`faqs`.*,
			`faq_categories`.`name` AS `category`,
			`faq_categories`.`page` AS `category_page`
		FROM `pages_panels_faqs`
		LEFT JOIN `faq_categories` ON `faq_categories`.`category_id` = `pages_panels_faqs`.`faq_category_id`
		LEFT JOIN `faqs` ON `faqs`.`category_id` = `faq_categories`.`category_id`
		WHERE
			`pages_panels_faqs`.`panel_id` = ? &&
			`faqs`.`showhide` = 0 &&
			`faq_categories`.`showhide` = 0
		GROUP BY `faqs`.`faq_id`
		ORDER BY `pages_panels_faqs`.`ordering`, `faqs`.`faq_id`", array($parent_id));
		return $this->db->fetch_assoc('faq_id');
	}

	/*-----------------------------------/
	* Gets all attached reviews
	*
	* <AUTHOR> Army
	* @param	$parent_id
	* @return	Array of reviews
	*/
	public function get_attached_reviews($parent_id){
		$this->db->query("SELECT `reviews`.* FROM `pages_panels_reviews`
			LEFT JOIN `reviews` ON `reviews`.`review_id` = `pages_panels_reviews`.`review_id`
			WHERE
				`pages_panels_reviews`.`panel_id` = ?
				AND (`reviews`.`client` != ? || `reviews`.`company` != ? || `reviews`.`content` != ?)
				AND (`reviews`.`client` IS NOT NULL || `reviews`.`company` IS NOT NULL || `reviews`.`content` IS NOT NULL)
				AND `reviews`.`showhide` = 0
			GROUP BY `reviews`.`review_id`
			ORDER BY `pages_panels_reviews`.`ordering`, `reviews`.`review_id`", array($parent_id, '', '', ''));
		$response = $this->db->fetch_assoc('review_id') ?: [];
		shuffle($response);
		return $response;
	}

	/*-----------------------------------/
	* Gets data for a given gallery_id
	*
	* <AUTHOR> Army
	* @param	$gallery_id
	* @return	Array of gallery with photos
	*/
	public function get_attached_gallery($gallery_id, $limit = false){
		$this->db->query("SELECT * FROM `galleries` WHERE `showhide` < 2 && `gallery_id` = ?", [$gallery_id]);
		if ($response = $this->db->fetch_array()[0] ?? []) {
			$limit = $limit ? "LIMIT $limit" : "";
			$this->db->query("SELECT * FROM `galleries_photos` WHERE `gallery_id` = ? AND `showhide` = 0 ORDER BY `ordering` $limit", [$gallery_id]);
			$response['photos'] = $this->db->fetch_assoc('photo_id');
		}

		return $response;
	}

	/*-----------------------------------/
	* Gets data for a given form_id
	*
	* <AUTHOR> Army
	* @param	$form_id
	* @return	Array of form details
	*/
	public function get_attached_form($form_id){
		$this->db->query("SELECT * FROM `forms` WHERE `showhide` = 0 && `form_id` = ?", [$form_id]);
		if ($response = $this->db->fetch_array()[0] ?? []) {
			$response['form_fields'][0] = []; //default group

			//Get form fieldsets
			$this->db->query("SELECT *, `label` AS `legend` FROM `form_fields` WHERE `type` = ? && `form_id` = ? ORDER BY `ordering` ASC, `field_id` ASC", ['fieldset', $form_id]);
			$fieldsets = $this->db->fetch_assoc('field_id');
			foreach($fieldsets as $fieldset){
				$fieldset['form_fields'] = [];
				$response['form_fields'][$fieldset['field_id']] = $fieldset;
			}

			//Get form fields
			$this->db->query("SELECT *, IFNULL(`parent_id`, 0) AS `parent_id` FROM `form_fields` WHERE `type` != ? && `form_id` = ? ORDER BY `ordering` ASC, `field_id` ASC", ['fieldset', $form_id]);
			$fields = $this->db->fetch_array() ?? [];
			if(empty($fields)){
				return false; //No form fields
			}else{
				foreach($fields as $field){

					//Get field options
					$field['field_options'] = array();
					if($field['type'] == 'dropdown' || $field['type'] == 'checkbox' || $field['type'] == 'radio'){
						$options = $this->db->query("SELECT * FROM `form_field_options` WHERE `field_id` = ? ORDER BY `ordering`", [$field['field_id']]);
						$field['field_options'] = $this->db->fetch_array();
					}

					$response['form_fields'][$field['parent_id']]['form_fields'][$field['field_id']] = $field;
				}
			}
		}

		return $response;
	}

	/*-----------------------------------/
	* Gets data for a given leadin_id
	*
	* <AUTHOR> Army
	* @param	$leadin_id
	* @return	Array of lead-in details
	*/
	public function get_attached_leadin($leadin_id){
		$this->db->query("SELECT * FROM `pages_leadins` WHERE `leadin_id` = ? AND `showhide` = 0", [$leadin_id]);
		if ($response = $this->db->fetch_array()[0] ?? []) {
			
			// Replace the page keyword insert shortcodes with their corresponding values
			$response['title']   = $this->replace_keyword_inserts($response['title']);
			$response['content'] = $this->replace_keyword_inserts($response['content']);

			//Get form fields
			$this->db->query("SELECT * FROM `pages_leadins_fields` WHERE `leadin_id` = ? ORDER BY `ordering` ASC, `field_id` ASC", [$leadin_id]);
			$response['form_fields'] = $this->db->fetch_assoc('field_id');
			foreach($response['form_fields'] as &$field){
				$field['name']    = 'field-'.$leadin_id.'-'.$field['field_id'];
				$field['options'] = explode('|', $field['options']);
				unset($field);
			}
		}

		return $response;
	}

	/*-----------------------------------/
	* Gets page id based on url
	*
	* <AUTHOR> Army
	* @param	$page_url	Full page url
	* @param	$pages_arr	Array of pages to search
	* @return	Integer		Page id
	*/
	public function get_page_id($page_url, $pages_arr=NULL){
		$page_id = NULL;
		if(is_null($pages_arr)){
			$pages_arr = $this->sitemap;
		}
		foreach($pages_arr as $page){
			if($page['page_url'] === $page_url || $page['original_url'] === $page_url){
				$page_id = $page['page_id'];
				break;
			}else if(isset($page['sub_pages']) && is_array($page['sub_pages']) && count($page['sub_pages']) > 0){
				$page_id = $this->get_page_id($page_url, $page['sub_pages']);
				if(!is_null($page_id)){
					break;
				}
			}
		}
		return $page_id;
	}

	/*-----------------------------------/
	* Gets closest parent id based on url
	*
	* <AUTHOR> Army
	* @param	$page_url	Full page url
	* @return	Integer		Parent id
	*/
	public function get_parent_id($page_url){
		$parent_id = NULL;
		$pathbits = explode('/', $page_url);

		for($i=2; $i<count($pathbits); $i++){
			$pagebits = array_slice($pathbits, 0, count($pathbits)-$i);
			$parent_id = $this->get_page_id(implode('/', $pagebits).'/');
			if(!empty($parent_id)) break;
		}

		return $parent_id;
	}

	/*-----------------------------------/
	* Gets breadcrumb info for current page user is on
	*
	* <AUTHOR> Army
	* @return	Array of breadcrumb pages
	*/
	public function get_breadcrumb(){
		$breadcrumb = array();
		$url = $this->path;

		for($i=1; $i<count($this->pathbits); $i++){
			if($this->pathbits[$i] != ''){
				$notfound = true;
				$url .= $this->pathbits[$i]. '/';
				foreach($this->sitemap as $id=>$page){
					if($page['showhide'] < 2 && $page['page_url'] == $url){
						$notfound = false;
						array_push($breadcrumb, array('url' => $url, 'name' => $page['name']));
						break;
					}
				}
				if($notfound){
					array_push($breadcrumb, array('url' => $url, 'name' => '404 Error'));
					break;
				}
			}
		}

		return $breadcrumb;
	}

	/*-----------------------------------/
	* Gets array of url segments relative to given page
	*
	* <AUTHOR> Army
	* @param	$page	Url segment of page
	* @return	Array
	*/
	public function get_pagebits($page, $pagebits=array()){
		if(empty($pagebits)){
			$pagebits = $this->pathbits;
		}
		foreach($pagebits as $key=>$data){
			if($data == $page){
				array_unshift($pagebits, '');
				break;
			}
			array_shift($pagebits);
		}
		return $pagebits;
	}

	/*-----------------------------------/
	* Fetches record ID from a display page's slug
	*
	* <AUTHOR> Army
	* @param	$sitepage	Key of _sitepages of the listing page
	* @param	$lvl		How many levels deep to grab the pathbit.  Negative numbers count backward from the last entry.
	*                  		Defaults to -1, meaning the last entry.
	*                  		EG: "/blog/category-1/entry-1/", -1 = entry, 0 = blog, 1 = category, 2 = entry
	*
	* @return	record ID of a display page, or NULL
	*/
	public function get_pagebit_id($sitepage, $lvl = -1) {
		global $_sitepages;

		$item_id    = NULL;
		$on_subpage = PARENT_ID == $_sitepages[$sitepage]['page_id'] && PAGE_ID == '';
		$pagebits   = array_filter($this->get_pagebits($_sitepages[$sitepage]['slug'] ?: $_sitepages[$sitepage]['page']));

		$listings_pagebit = array_slice($pagebits, 0, 1)[0] ?? false;
		$display_pagebit  = array_slice($pagebits, $lvl, 1)[0] ?? false;

		if($on_subpage && $listings_pagebit && $display_pagebit) {
			$bits    = explode('-', $display_pagebit);
			$item_id = array_pop($bits);
			$item_id = is_numeric($item_id) ? +$item_id : NULL;
		}

		return $item_id;
	}

	/*-----------------------------------/
	* Gets all attached partners
	*
	* <AUTHOR> Army
	* @param	$parent_id
	* @return	Array of partners
	*/
	public function get_attached_partners($parent_id){
		$this->db->query("SELECT 
			`partners`.*, 
			`partner_categories`.`name` as `category` 

		FROM `pages_panels_partners` 
			LEFT JOIN `partners` ON `partners`.`partner_id` = `pages_panels_partners`.`partner_id` 
			LEFT JOIN `partner_categories` ON `partner_categories`.`category_id` = `partners`.`category_id` 

		WHERE 
			`pages_panels_partners`.`panel_id` = ?
			AND `partners`.`showhide` = 0 
			AND `partner_categories`.`showhide` = 0 

		GROUP BY 
			`partners`.`partner_id` 

		ORDER BY 
			`pages_panels_partners`.`ordering`, 
			`partners`.`partner_id`", [$parent_id]);

		return $this->db->fetch_assoc('partner_id');
	}
}

?>