<?php

echo '<ul class="page-contact">';

	//Contact Address
	$address = $loc['full_address'] ?? $global['full_address'];
	if($address){
		echo '<li class="address">
			<a href="http://maps.google.com/?q='.urlencode($address).'" target="_blank" class="value" rel="noopener noreferrer">'
				.prettify_address($loc ?? $global, true).
			'</a>
		</li>';
	}

	//Contact Numbers
	$phone = $loc['phone'] ?? $global['contact_phone'];
	if($phone){
		echo '<li class="phone test">
			<span class="label">Phone</span>
			<a href="tel://'.format_intl_number($phone).'" class="value">'.$phone.'</a>';
		// </li>';
	}
	$toll_free = $loc['toll_free'] ?? $global['contact_toll_free'];
	if($toll_free){
		// echo '<li class="tollfree">
		echo '<span class="label"> / Toll Free</span>
			<a href="tel://'.format_intl_number($toll_free).'" class="value">'.$toll_free.'</a>
		</li>';
	}
	// $fax = $loc['fax'] ?? $global['contact_fax'];
	// if($fax){
	// 	echo '<li class="fax">
	// 		<span class="label">Fax</span>
	// 		<span class="value">'.$fax.'</span>
	// 	</li>';
	// }

	//Contact Email
	$email = $loc['email'] ?? $global['contact_email'];
	if($email){
		echo '<li class="mailto">
			<a  class="value" href="mailto:'.$email.'">'.$email.'</a>
		</li>';
	}

	foreach($loc['location_numbers'] ?? [] as $number){
		echo '<li class="phone alt">
			<span class="label">'.ucwords($number['type']).'</span>
			<a class="value" href="tel://'.format_intl_number($number['phone']).'">'.$number['phone'].'</a>
		</li>';
	}

	foreach($loc['location_contacts'] ?? [] as $contact){
		echo '<li class="'.($contact['type'] == 'email' ? 'mailto' : $contact['type']).' alt">
			<span class="label">'.($contact['label'] ?: 'Alt '.ucfirst($contact['type'])).'</span>'
			.create_button($contact['value'], 0, $contact['value'], 'value').
		'</li>';
	}


echo '</ul>';
	
?>