@charset "utf-8";
/*
	landing-page.less

*/

@import "../../../core/less/landing-page.less";
@import (reference) "critical.less";

body.landing-page #page-hero{
	--logo-width: 96.42px; // Same width as the logo's width when the navbar is theme-transparent

	.page-hero-wrapper{
		&:extend(.container);
		&:extend(.container.container-lg);
	}

	.container{
		display: block;
		max-width: none;
		padding: 0;
	}

	#page-logo{
		.position(0, 0, auto, 0);
		&:extend(.container);
		&:extend(.container.container-xl);
		padding: var(--container-padding) var(--container-padding) 0;

		svg{
			fill: @color-light;
		}
	}

	.page-title-wrapper{
		width: 100%;

		.page-subtitle{
			.regular();
		}
	}

	.page-buttons{
		display: block;
		max-width: none;
		.fluid-property(margin-top, 20px, 40px);
	}

	#landing-form{
		--field-border: fade(@color-gray-light, 50%);
		--field-border-hover: @color-gray-light;
	}
}

@media @notebook{
	body.landing-page #page-hero{
		--logo-width: 153.2px;
	}
}