<?php

//Sanitize data
sanitize_form_data();

//Instatiate Account
require_once($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Account.class.php");
$Account = new Account('Admin');

//Instatiate CMSBuilder
require_once($_SERVER['DOCUMENT_ROOT'].$path."modules/classes/CMSBuilder.class.php");
$CMSBuilder = new CMSBuilder($path);
$pathbits = $CMSBuilder->pathbits;
$sitemap = $CMSBuilder->get_sitemap();
$navigation = $CMSBuilder->get_navigation();
$section = $CMSBuilder->curr_section();
$global = $CMSBuilder->global_settings();
$cms_settings = $CMSBuilder->cms_settings();
$error404 = $section['error404'];

//Sendgrid
if(isset($global['sendgrid_key']) && !empty($global['sendgrid_key'])){
	include($_SERVER['DOCUMENT_ROOT'].$root."includes/plugins/sendgrid/config.php");
}

//Instantiate Registration
include($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Registration.class.php");
$Registration = new Registration();
$reg_settings = $Registration->get_reg_settings();
// $reg_widgets = array();

//Instantiate SEOAnalyzer
if($cms_settings['enhanced_seo']){
	require_once($_SERVER['DOCUMENT_ROOT'].$path."modules/classes/SEOAnalyzer.class.php");
	$Analyzer = new SEOAnalyzer();
}

//Instantiate reSmush
require_once($_SERVER['DOCUMENT_ROOT'].$path."includes/plugins/resmush/reSmush.class.php");
$reSmush = new reSmush(); 

//XSS prevention cookie
if(!isset($_COOKIE['xssid']) || $_COOKIE['xssid'] == ""){
	$randomstr = gen_random_string();
	$_COOKIE['xssid'] = $randomstr;
	$domain = (strpos($_SERVER['HTTP_HOST'], ':8888') === false) ? $_SERVER['HTTP_HOST'] : false;
	setcookie("xssid", $randomstr, 0, "/", $domain, $ssl, true);
}
$_COOKIE['xssid'] = strip_data($_COOKIE['xssid']);
if(!empty($_POST) && $_POST['xssid'] != $_COOKIE['xssid']){
	$Account->logout();
	$CMSBuilder->set_system_alert('Invalid session. Please ensure cookies are enabled and try again.', false);
	header("Location:" .$path. "login/");
	exit();
}

//Definitions
define('USER_LOGGED_IN', $Account->login_status());
define('MASTER_USER', $Account->account_has_role('Master'));
define('SEO_USER', MASTER_USER || $Account->account_has_role('SEO'));
define('SECTION_ID', $section['section_id']);
define('PARENT_ID', $section['parent_id']);
define('PAGE_URL', $section['page_url']);
define('ITEM_ID', ($_GET['item_id'] ?? ''));
define('PAGE_ID', ($_GET['page_id'] ?? ''));
define('ACTION', ($_GET['action'] ?? ''));
define('THEME', $cms_settings['theme']);

//
define('EVENT_CODE', (SECTION_ID == 66 ? 'Tournament' : 'Event'));
define('OCCURRENCE_CODE', EVENT_CODE.' Date');
define('STEP', (isset($_GET['step']) ? $_GET['step'] : ''));
define('OCCURRENCE_ID', (isset($_GET['occurrence_id']) ? $_GET['occurrence_id'] : ''));
define('IMAGE_ID', (isset($_GET['image_id']) ? $_GET['image_id'] : ''));
//

//Filemanager access key
define('FM_ACCESS_KEY', md5($_COOKIE['xssid'].$fm_access_token));

// Set crop sizes
require_once($_SERVER['DOCUMENT_ROOT'].$path."modules/classes/CMSUploader.class.php");
include($_SERVER['DOCUMENT_ROOT'].$path."includes/cropsizes.php");

//Theme names
$_themes = array(
	'theme1' => $cms_settings['theme_color1'],
	'theme2' => $cms_settings['theme_color2'],
	'gradient' => 'Gradient',
	'black' => 'Black'
);

?>