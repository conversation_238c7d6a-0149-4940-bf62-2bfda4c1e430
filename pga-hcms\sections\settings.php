<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(!isset($_POST['save'])){
	$row = $global;
}

//Display form
echo '<form action="" method="post" enctype="multipart/form-data">';

	//General settings
	echo '<div class="panel">
		<div class="panel-header">General Settings
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">
				<div class="flex-column">
					<div class="form-field">
						<label>Company Name' .(in_array('company_name', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<input type="text" name="company_name" value="' .($row['company_name'] ?? ''). '" class="input' .(in_array('company_name', $required) ? ' required' : ''). '" />
					</div>

					<div class="form-field">
						<label>Default Colour Overlay' .(in_array('theme', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Colour Overlay', 'This will be used as the default overlay colour for slideshow and page banner images.'). '</label>
						<select name="theme" class="select' .(in_array('theme', $required) ? ' required' : ''). '">';
						foreach($_themes as $theme_key => $theme_name){
							echo '<option value="' .$theme_key. '"' .(($row['theme'] ?? false) == $theme_key ? ' selected' : ''). '>' .$theme_name. '</option>';
						}
						echo '</select>
					</div>

					<div class="form-field hidden">
						<label>Show/Hide Google Map' .(in_array('google_map', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Show/Hide Google Map', 'Set to &quot;Show&quot; to display a google map of locations on the page.'). '</label>
						<select name="google_map" class="select' .(in_array('google_map', $required) ? ' required' : ''). '">
							<option value="1"' .(($row['google_map'] ?? 0) ? ' selected' : ''). '>Show</option>
							<option value="0"' .(!($row['google_map'] ?? 0) ? ' selected' : ''). '>Hide</option>
						</select>
					</div>
				</div>';

				//Reviews
				if($CMSBuilder->get_section_status($_cmssections['reviews']) == 'Enabled'){
					echo '<div class="flex-column">
						<div class="form-field">
							<label>Minimum Stars to Show' .(in_array('min_rating_display', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Minimum Stars to Show', 'When automatically retrieving reviews from your Google My Business, hide reviews by default that are below the star rating set here.<br/><br/><small>Note: Reviews with no content will be automatically hidden by default.</small>'). '</label>
							<select name="min_rating_display" class="select' .(in_array('min_rating_display', $required) ? ' required' : ''). '">';
								for($i=1; $i<=5; $i++){
									echo '<option value="' .$i. '"' .(($row['min_rating_display'] ?? 4) == $i ? ' selected' : ''). '>' .$i. ' Star Reviews</option>';
								}
								echo '<option value="-1"' .(($row['min_rating_display'] ?? 4) == -1 ? ' selected' : ''). '>Hide All</option>
							</select>
						</div>
						<div class="form-field">
							<label>Show/Hide Rating Badge' .(in_array('rating_badge', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Show/Hide Rating Badge', 'Set to &quot;Show&quot; to display the rating badge in the header section. (Badge will always be displayed in the footer section)'). '</label>
							<select name="rating_badge" class="select' .(in_array('rating_badge', $required) ? ' required' : ''). '">
								<option value="1"' .(($row['rating_badge'] ?? 0) ? ' selected' : ''). '>Show</option>
								<option value="0"' .(!($row['rating_badge'] ?? 0) ? ' selected' : ''). '>Hide</option>
							</select>
						</div>
					</div>';
				}

				echo '<div class="flex-column">
					<div class="form-field">
						<label>Disclaimer' .(in_array('disclaimer', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Website Disclaimer', 'This text will display in the footer of your website. This is suitable for legal disclaimers.'). '</label>
						<textarea name="disclaimer" class="textarea input_lg' .(in_array('disclaimer', $required) ? ' required' : ''). '">' .($row['disclaimer'] ?? ''). '</textarea>
					</div>
				</div>

				<div class="flex-column">
					<div class="form-field">
						<label>Timezone' .(in_array('timezone', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<select name="timezone" class="select' .(in_array('timezone', $required) ? ' required' : ''). '">
							<option value="">Default to Server</option>';
							$timezones = timezone_list();
							foreach($timezones as $zone => $zone_name){
								echo '<option value="' .$zone. '"' .(($row['timezone'] ?? '') == $zone ? ' selected' : ''). '>' .$zone_name. '</option>';
							}
						echo '</select>
					</div>
				</div>';

			echo '</div>
		</div>
	</div>'; //General settings

	//Notification settings
	echo '<div class="panel">
		<div class="panel-header">Notification Settings
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<p><strong>Note:</strong> Comma separate emails if sending to more than one email (e.g. <EMAIL>, <EMAIL>).  If left blank, submissions will send to the head office email address (<i>'.$global['contact_email'].'</i>).</p>
				<div class="flex-container">';

		foreach ($global_emails as $fieldname => [$label, $section_id]) {
			if(!$section_id || $CMSBuilder->get_section_status($section_id) == 'Enabled') {
				echo '<div class="form-field">
					<label>'.$label.' Email Address' .(in_array($fieldname, $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip($label.' Email', '<p>This is the address that all '.strtolower($label).' submissions will send to. </p>'). '</label>
					<input type="text" name="'.$fieldname.'" value="' .($row[$fieldname] ?? ''). '" class="input' .(in_array($fieldname, $required) ? ' required' : ''). '" />
				</div>';
			}
		}

			echo '</div>
		</div>
	</div>'; //Notification settings

	//Login settings
	echo '<div class="panel">
		<div class="panel-header">Login Settings
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">

				<div class="form-field">
					<label>Failed Logins Before Lockout' .(in_array('failed_login_attempts', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Failed Logins Before Lockout', 'The number of failed login attempts allowed before an account is locked.'). '</label>
					<select name="failed_login_attempts" class="select' .(in_array('failed_login_attempts', $required) ? ' required' : ''). '">
						<option value="0">Unlimited</option>';
						for($i = 1; $i <= 10; $i++) {
							echo '<option value="' .$i. '"' .(($row['failed_login_attempts'] ?? 0) == $i ? ' selected' : ''). '>' .$i. '</option>';
						}
					echo '</select>
				</div>

				<div class="form-field">
					<label>Reset Timeframe <small>(Minutes)</small>' .(in_array('failed_login_timeframe', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Reset Timeframe', 'Reset the count of login attempts every X number of minutes. This only applies to accounts that are not locked.<br/><br/><small>For example: if this was set to 5 minutes, the server will only count the number of failed logins in the past 5 minutes. If there are 3 allowed login attempts and a user has attempted to login twice and failed both times, the number of attempts that user made will reset back to 0 after 5 minutes.</small>'). '</label>
					<input type="text" name="failed_login_timeframe" value="' .($row['failed_login_timeframe'] ?? ''). '" class="input number' .(in_array('failed_login_timeframe', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Lockout Duration <small>(Minutes)</small>' .(in_array('lockout_duration', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Lockout Duration', 'The number of minutes an account will be locked due to failed login attempts.'). '</label>
					<input type="text" name="lockout_duration" value="' .($row['lockout_duration'] ?? ''). '" class="input number' .(in_array('lockout_duration', $required) ? ' required' : ''). '" />
				</div>

			</div>
		</div>
	</div>'; //Login settings

	//Social networking
	echo '<div class="panel">
		<div class="panel-header">Social Networking
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<p>Please Enter the Full URL:</p>
			<div class="flex-container">';

				foreach($global['global_social'] as $social){
					echo '<div class="form-field social">
						<i class="fab fa-' .$social['service']. '"></i>
						<input type="text" name="social_' .$social['id']. '" class="input social" value="' .($_POST['social_'.$social['id']] ?? $social['url']). '" />
					</div>';
				}

			echo '</div>
		</div>
	</div>'; //Social networking

	//Default SEO
	$maxchars = 160;
	echo '<div class="panel">
		<div class="panel-header">Search Engine Optimization
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">

				<div class="form-field">
					<label class="flex-container">
						<span class="flex-column left">
							Default Title'.
							(in_array('meta_title', $required_fields) ? ' <span class="required">*</span>' : '')
							.$CMSBuilder->tooltip('Default Title', 'The title of the website. Usually just the name of the company, however can also include a keyword-rich slogan. This will aid in search engine optimization. (e.g. Pixel Army - Edmonton Web Design)').'
						</span>

						<small class="flex-column right">
							<span id="count-seo-title"' .(strlen($row['meta_title'] ?? '') > 70 ? ' class="error"' : ''). '>' .strlen($row['meta_title'] ?? ''). '</span>/70
						</small>
					</label>
					<input type="text" name="meta_title" id="seo-title" class="input char-count-70' .(in_array('meta_title', $required) ? ' required' : ''). '" value="' .($row['meta_title'] ?? ''). '" />

					<label class="flex-container">
						<div class="flex-column left">
							Default Description' .(in_array('meta_description', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Default Description', 'A keyword-rich description of the website that will aid in search engine optimization.'). '
						</div>
						<div class="flex-column right">
							<small><span id="count-seo-description"' .(strlen($row['meta_description'] ?? '') > $maxchars ? ' class="error"' : ''). '>' .strlen($row['meta_description'] ?? ''). '</span>/' .$maxchars. '</small>
						</div>
					</label>
					<textarea name="meta_description" id="seo-description" class="textarea char-count-' .$maxchars. (in_array('meta_description', $required) ? ' required' : ''). '">' .($row['meta_description'] ?? ''). '</textarea>

					<input type="hidden" id="default-meta-title" value="' .($row['company_name'] ?? ''). '" />
					<input type="hidden" id="default-url" value="' .$siteurl. '" />
					<input type="hidden" id="seo-slug" value="' .$siteurl. '" />
				</div>

				<div class="google-preview seo-preview">
					<p>This Page in Google Search Results:</p>
					<div>
						<h2 class="seo-title">' .($row['meta_title'] ?? ''). '</h2>
						<h6 class="seo-slug">' .$siteurl. '</h6>
						<p class="seo-description">' .str_limit_characters($row['meta_description'] ?? '', $maxchars). '</p>
					</div>
				</div>

			</div>
		</div>
	</div>'; //Default SEO

	// Advanced settings
	echo $CMSBuilder->important('Changing advanced settings can be harmful to site features like performance, lead generation, and security. <b>Proceed with caution.</b>');

	echo '<div class="panel">
		<div class="panel-header">Advanced Settings
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content">
			<div class="flex-container">
				<div class="form-field">
					<label>Google API Key' .(in_array('google_api_key', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Google API Key', 'Required for the usage of Google services like maps, geocoding, or address autocomplete.'). '</label>
					<input type="text" name="google_api_key" value="' .($row['google_api_key'] ?? ''). '" class="input nomargin' .(in_array('google_api_key', $required) ? ' required' : ''). '" />
					<p><small class="block"><a href="https://console.cloud.google.com/apis/credentials" target="_blank">Find API Key</a></small></p>
				</div>

				<div class="form-field">
					<label>reCAPTCHA Site Key' .(in_array('recaptcha_key', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('reCAPTCHA Site Key', 'Required for the usage of the Google reCAPTCHA plugin which helps prevent spam and misuse of forms on your website.'). '</label>
					<input type="text" name="recaptcha_key" value="' .($row['recaptcha_key'] ?? ''). '" class="input nomargin' .(in_array('recaptcha_key', $required) ? ' required' : ''). '" />
					<p><small class="block"><a href="https://www.google.com/recaptcha/admin/create" target="_blank">Find API Key</a></small></p>
				</div>

				<div class="form-field">
					<label>reCAPTCHA Secret Key' .(in_array('recaptcha_secret', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('reCAPTCHA Secret', 'Required for the validation of the Google reCAPTCHA plugin which helps prevent spam and misuse of forms on your website.'). '</label>
					<input type="password" name="recaptcha_secret" value="' .($row['recaptcha_secret'] ?? ''). '" class="input nomargin' .(in_array('recaptcha_secret', $required) ? ' required' : ''). '" />
					<p><small class="block"><a href="https://console.developers.google.com" target="_blank">Find API Key</a></small></p>
				</div>
			</div>

			<hr>

			<div class="flex-container">
				<div class="form-field">
					<label>SMTP Server' .(in_array('smtp_server', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('SMTP Email Server', 'Enter the hostname for processing and sending SMTP email.  You can generally find your SMTP email server address in the account or settings section of your mail client.'). '</label>
					<input id="smtp-server" type="text" name="smtp_server" value="' .($row['smtp_server'] ?? ''). '" class="input' .(in_array('smtp_server', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>SMTP Email Address' .(in_array('smtp_email', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('SMTP Email Address', 'Email from which all website notifications and mail will be sent from.  Must be a valid address accepted by the <b>SMTP server</b>.'). '</label>
					<input id="smtp-email" type="text" name="smtp_email" value="' .($row['smtp_email'] ?? ''). '" class="input' .(in_array('smtp_email', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>SMTP Email Password' .(in_array('smtp_pass', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('SMTP Email Password', 'Password for the <b>SMTP Email Address</b> to authenticate sending of mail and site notifications.'). '</label>
					<input id="smtp-pass" type="password" name="smtp_pass" value="' .($row['smtp_pass'] ?? ''). '" class="input' .(in_array('smtp_pass', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label></label>
					<button id="smtp-test" type="button" class="button-sm" style="vertical-align: top;">Validate SMTP</button>
				</div>
			</div>
		</div>
	</div>'; //API Keys

	//Sticky footer
	echo '<footer id="cms-footer">
		<div class="flex-container">
			<div class="flex-column right">
				<button type="submit" class="button" name="save" value="save"><i class="fas fa-check"></i>Save Changes</button>
			</div>
		</div>
	</footer>';

	echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '"/>';
echo '</form>';

?>