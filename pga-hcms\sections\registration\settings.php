<?php
	
//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

echo "<form action='' method='post' enctype='multipart/form-data'>";

	//General settings
	echo "<div class='panel'>";
		echo "<div class='panel-header'>General Settings
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			echo "<p><strong>Registration Package: Custom</strong></p>";

			echo "<div class='form-field hidden'>
				<label>Social Sharing ".$CMSBuilder->tooltip('Social Sharing', 'Enable the ability to share '.EVENT_CODE.' information via social networks. Displayed on the individual '.EVENT_CODE.' page.')."</label>
				<select name='social_sharing' class='select'>
					<option value='1'".(isset($reg_settings['social_sharing']) && $reg_settings['social_sharing'] == 1 ? " selected" : "").">Enabled</option>
					<option value='0'".(isset($reg_settings['social_sharing']) && $reg_settings['social_sharing'] == 0 ? " selected" : "").">Disabled</option>
				</select>
			</div>";
			echo "<div class='form-field'>
				<label>Attendee Information Sharing ".$CMSBuilder->tooltip('Attendee Information Sharing', 'Enable the ability to display attendee lists when viewing tournaments.')."</label>
				<select name='attendee_sharing' class='select'>
					<option value='1'".(isset($reg_settings['attendee_sharing']) && $reg_settings['attendee_sharing'] == 1 ? " selected" : "").">Enabled</option>
					<option value='0'".(isset($reg_settings['attendee_sharing']) && $reg_settings['attendee_sharing'] == 0 ? " selected" : "").">Disabled</option>
				</select>
			</div>";
			echo "<div class='form-field hidden'>
				<label>Waiting Lists ".$CMSBuilder->tooltip('Waiting Lists', 'Enable the ability for attendees to be placed on a waiting list if the '.EVENT_CODE.' is full.')."</label>
				<select name='waiting_lists' class='select'>
					<option value='1'".(isset($reg_settings['waiting_lists']) && $reg_settings['waiting_lists'] == 1 ? " selected" : "").">Enabled</option>
					<option value='0'".(isset($reg_settings['waiting_lists']) && $reg_settings['waiting_lists'] == 0 ? " selected" : "").">Disabled</option>
				</select>
			</div>";
			echo "<div class='form-field auto-width'>
				<label>Withdrawal Fee ".$CMSBuilder->tooltip('Withdrawal Fee', 'Attendees who withdraw from a tournament past the payment deadline but before the withdrawal deadline will be charged this dollar amount as a penalty.')."</label>
				<input type='text' name='cancellation_fee' value='".(isset($reg_settings['cancellation_fee']) ? number_format($reg_settings['cancellation_fee'], 2) : '')."' class='input input_sm number' />
			</div>";
			echo "<div class='form-field auto-width'>
				<label>Late Withdrawal Fee ".$CMSBuilder->tooltip('Late Withdrawal Fee', 'Attendees who withdraw from a tournament past the withdrawal deadline but before the registration deadline will be charged this percentage of the total fees as a penalty.')."</label>
				<input type='text' name='cancellation_fee_late' value='".(isset($reg_settings['cancellation_fee_late']) ? number_format($reg_settings['cancellation_fee_late'], 2) : '')."' class='input input_sm number' />%
			</div>";
		echo "</div>";
	echo "</div>";

	//Notification settings
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Notification Settings
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			echo "<div class='form-field'>
				<label>Tournaments " .$CMSBuilder->tooltip('Tournaments', 'This is the email address that all tournament notifications will send to. If left blank, submissions will be sent to the head office email address.</p><small><strong>Note:</strong> Comma separate emails if sending to more than one email. (e.g. <EMAIL>, <EMAIL>)</small>'). "</label>
				<input type='text' name='email_tournaments' value='" .(isset($reg_settings['email_tournaments']) ? $reg_settings['email_tournaments'] : ''). "' class='input' />	
			</div>";
			echo "<div class='form-field'>
				<label>Events " .$CMSBuilder->tooltip('Events', 'This is the email address that all event notifications will send to. If left blank, submissions will be sent to the head office email address.</p><small><strong>Note:</strong> Comma separate emails if sending to more than one email. (e.g. <EMAIL>, <EMAIL>)</small>'). "</label>
				<input type='text' name='email_events' value='" .(isset($reg_settings['email_events']) ? $reg_settings['email_events'] : ''). "' class='input' />	
			</div>";
			echo "<div class='form-field'>
				<label>Conferences " .$CMSBuilder->tooltip('Conferences', 'This is the email address that all conference notifications will send to. If left blank, submissions will be sent to the head office email address.</p><small><strong>Note:</strong> Comma separate emails if sending to more than one email. (e.g. <EMAIL>, <EMAIL>)</small>'). "</label>
				<input type='text' name='email_conferences' value='" .(isset($reg_settings['email_conferences']) ? $reg_settings['email_conferences'] : ''). "' class='input' />	
			</div>";
			echo "<div class='form-field'>
				<label>Hole In One " .$CMSBuilder->tooltip('Hole In One', 'This is the email address that all hole in one submissions will send to. If left blank, submissions will be sent to the head office email address.</p><small><strong>Note:</strong> Comma separate emails if sending to more than one email. (e.g. <EMAIL>, <EMAIL>)</small>'). "</label>
				<input type='text' name='email_hio' value='" .(isset($reg_settings['email_hio']) ? $reg_settings['email_hio'] : ''). "' class='input' />	
			</div>";
		echo "</div>";
	echo "</div>";

	//Eligibility Reset
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Tournament Settings
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			echo "<p>Click the reset button below to clear tournament eligibility for all members. This action is <strong>NOT</strong> undoable and should only be performed before tournament registration opens for the season.</p>";
			echo "<p><button type='button' name='reset' id='reset-eligibility' value='reset' class='button-sm'><i class='fa fa-undo'></i>Reset Eligibility</button></p>";
		echo "</div>";
	echo "</div>";
	
	//Sticky footer
	echo "<footer id='cms-footer' class='resize'>";
	echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
	echo "</footer>";

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid']. "'/>";	
echo "</form>";

?>