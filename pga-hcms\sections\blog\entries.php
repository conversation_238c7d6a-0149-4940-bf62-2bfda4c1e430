<?php

//Table listing
if (ACTION == '') {
	
	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Advanced Search
	echo '<form action="" method="get" enctype="multipart/form-data">
		<div class="panel">
			<div class="panel-header">Filters
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="form-field">
					<label>Entry Title</label>
					<input type="text" name="search" value="'.$searchterm.'" class="input" />
				</div>

				<div class="form-field">
					<label>Start Date</label>
					<input type="text" name="start_date" value="'.($_GET['start_date'] ?? '').'" class="input datepicker" autocomplete="off"/>
				</div>

				<div class="form-field">
					<label>End Date</label>
					<input type="text" name="end_date" value="'.($_GET['end_date'] ?? '').'" class="input datepicker" autocomplete="off"/>
				</div>';

			if($CMSBuilder->get_section_status($_cmssections['blog_categories']) == 'Enabled'){
				echo '<div class="form-field">
					<label>Category</label>
					<select name="category_id" class="select">
						<option value="">- All -</option>';
					
					foreach($blog_cats as $cat_id => $cat){
						echo '<option value="'.$cat_id.'"'.(($_GET['category_id'] ?? '') == $cat_id ? ' selected' : '').'>'.$cat['name'].'</option>';
					}
					
					echo '</select>
				</div>';
			}

			if($CMSBuilder->get_section_status($_cmssections['blog_authors']) == 'Enabled'){
				echo '<div class="form-field">
					<label>Author</label>
					<select name="author_id" class="select">
						<option value="">- All -</option>';
					
					foreach($blog_authors as $author_id => $author){
						echo '<option value="'.$author_id.'"'.(($_GET['author_id'] ?? '') == $author_id ? ' selected' : '').'>'.$author['name'].'</option>';
					}
					
					echo '</select>
				</div>';
			}

			echo '</div>

			<div class="pager">
				<div class="flex-container">
					<div class="flex-column left"><button type="button" class="button delete" onclick="document.getElementById(\'clear-search-form\').submit();"><i class="fa fa-times"></i> Clear</a></div>
					<div class="flex-column right"><button type="submit" class="button"><i class="fa fa-search"></i> Search</button></div>
				</div>
			</div>
		</div>
	</form>

	<form id="clear-search-form" name="clear-search-form" class="hidden" action="'.PAGE_URL.'" method="post">
		<input type="hidden" name="clear-search" value="Clear" />
		<input type="hidden" name="search" value="" />
		<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'].'" />
	</form>';
	
	
	//Disply listings
	echo '<div class="panel">
		<div class="panel-header">'.$record_names.
			'<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
			
				<thead>
					<th width="1px" data-sorter="false"></th>
					<th width="400px">Title</th>	
					<th data-sorter="date">Posted&nbsp;Date</th>';

				if($CMSBuilder->get_section_status($_cmssections['blog_categories']) == 'Enabled'){
					echo '<th>Category</th>';
				}

				if($CMSBuilder->get_section_status($_cmssections['blog_authors']) == 'Enabled'){
					echo '<th>Author</th>';
				}

					echo '<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
					<th width="1px" class="nopadding-l" data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				
				foreach($records_arr as $row){
					$seo_class = $seo_tooltip = '';
					if($cms_settings['enhanced_seo'] && (MASTER_USER || SEO_USER)){
						$seo_indicator = $Analyzer->score_tooltip($row['seo_score']);
						$seo_class     = $seo_indicator['class'];
						$seo_tooltip   = ($row['is_future'] ? '<span class="seo-tool tooltip" title="<h4>Cannot determine SEO score: </h4>Entry is scheduled to be posted on '.date('F jS, Y', strtotime($row['post_date'])).'."></span>' : $seo_indicator['tooltip']);
					}
					
					echo '<tr class="'.$seo_class.'">
						<td class="nopadding-r">'.$seo_tooltip.($row['image'] ? '<a href="'.$path.$row['full_image'].'" class="light-gallery" rel="prettyPhoto" title="'.$row['title'].'">'.render_gravatar($row['full_image']).'</a>' : '').'</td>
						<td>'.$row['title'].'</td>
						<td>'.date('M j, Y', strtotime($row['post_date'])).'</td>';

						if ($CMSBuilder->get_section_status($_cmssections['blog_categories']) == 'Enabled') {
							echo '<td>'.$CMSBuilder->item_link($row['category_name'], $_cmssections['blog_categories'], $row['category_id']).'</td>';
						}
						if ($CMSBuilder->get_section_status($_cmssections['blog_authors']) == 'Enabled') {
							echo '<td>'.$CMSBuilder->item_link($row['author_name'], $_cmssections['blog_authors'], $row['author_id']).'</td>';
						}

						echo '<td>'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td class="right"><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
						<td class="nopadding-l">'.(!$row['is_future'] ? '<a href="'.$row['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a>' : '').'</td>
					</tr>';	
				}

				echo '</tbody>
			</table>';
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo '</div>	
	</div>';
	

//Image cropping
}else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');

//Display form	
} else {
	
	$image = '';
	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = $data['image'];	
		if(!isset($_POST['save'])){
			$row = $data;
		}
	
		echo '<div class="actions-nav flex-container">
			<div class="flex-column right">
				<small><b>Link to '.$record_name.':</b> '.$data['page_url'].'&nbsp;&nbsp;<a href="'.$data['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td></small>
			</div>
		</div>';
		
	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';
	
		//Entry details
		echo '<div class="panel">
			<div class="panel-header">Entry Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show Entry</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"'.(($row['showhide'] ?? 0) ? '' : ' checked').' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>

			<div class="panel-content">
				<div class="flex-container">
				
					<div class="form-field">
						<label>Title'.(in_array('title', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input id="button-text" type="text" name="title" value="'.($row['title'] ?? '').'" class="input'.(in_array('title', $required) ? ' required' : '').'" />
					</div>

					<div class="form-field">
						<label>Post Date'.(in_array('post_date', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="post_date" value="'.($row['post_date'] ?? '').'" class="input datepicker'.(in_array('post_date', $required) ? ' required' : '').'" autocomplete="off" placeholder="Current Date" />
					</div>';

					if($CMSBuilder->get_section_status($_cmssections['blog_categories']) == 'Enabled'){
						echo '<div class="form-field">
							<label>Category'.(in_array('category_id', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<select name="category_id" class="select'.(in_array('category_id', $required) ? ' required' : '').'">
								<option value="">- Select -</option>';

							foreach($blog_cats as $cat){
								echo '<option value="'.$cat['category_id'].'"'.($row['category_id'] == $cat['category_id'] ? ' selected' : '').'>'.$cat['name'].($cat['showhide'] > 0 ? ' (Hidden)' : '').'</option>';
							}

							echo '</select>
						</div>';
					}

					if($CMSBuilder->get_section_status($_cmssections['blog_authors']) == 'Enabled'){
						$row['author_id'] = ($row['author_id'] ?? false ?: (count($blog_authors) === 1 ? $blog_authors[array_keys($blog_authors)[0]]['author_id'] : NULL)); //default to only author

						echo '<div class="form-field">
							<label>Author'.(in_array('author_id', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
							<select name="author_id" class="select'.(in_array('author_id', $required) ? ' required' : '').'">
								<option value="">- Select -</option>';

							foreach($blog_authors as $author){
								echo '<option value="'.$author['author_id'].'"'.($row['author_id'] == $author['author_id'] ? ' selected' : '').'>'.$author['name'].($author['showhide'] > 0 ? ' (Hidden)' : '').'</option>';
							}

							echo '</select>
						</div>';
					}
				
				echo '</div>
				
				<div class="form-field">
					<label>Short Description'.(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Short Description', 'This is the description that will display in the list view of entries.').'</label>
					<textarea name="description" class="textarea input_lg'.(in_array('description', $required) ? ' required' : '').'">'.($row['description'] ?? '').'</textarea>
				</div>

			</div>
		</div>'; //Entry details
		
		//Entry image
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Image
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">';

				if($image){
					echo '<div class="img-holder">
						<button type="button" name="recrop" value="image" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
						<a href="'.$path.$imagedir.$image.'" class="light-gallery" target="_blank" title="">
							<img src="'.$path.$imagedir.'thumbs/'.$image.'" alt="" />
						</a>
						<input type="checkbox" class="checkbox" name="deleteimage" id="deleteimage" value="1">
						<label for="deleteimage">Delete Current Image</label>
					</div>';
				}

					[$max_W, $max_H] = CMSUploader::max_size('blog_entry', 'image');
					echo '<div class="form-field">
						<label>Upload Image '.(in_array('image', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Upload Image', 'Image must be at least '.$max_W.' x '.$max_H.' and smaller than '.$_max_filesize['megabytes'].'.').'</label>
						<input type="file" class="input'.(in_array('image', $required) ? ' required' : '').'" name="image" value="" />
					</div>

					<div class="form-field">
						<label>Alt Text <small>(SEO)</small> '.(in_array('image_alt', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.').'</label>
						<input type="text" name="image_alt" value="'.($row['image_alt'] ?? '').'" class="input" />
					</div>
				</div>
			</div>
		</div>'; //Entry image
		
		//Entry content
		echo '<div class="panel">
			<div class="panel-header'.(in_array('content', $required) ? ' required' : '').'">
				Entry Content'.(in_array('content', $required_fields) ? ' <span class="required">*</span>' : '').'
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content nopadding">
				<textarea name="content" class="tinymceEditor'.(in_array('content', $required) ? ' required' : '').'">'.($row['content'] ?? '').'</textarea>
			</div>
		</div>'; //Entry content
		
		//SEO Content/Analysis
		include('includes/widgets/seotabs.php');

		//Sticky footer
		include('includes/widgets/formbuttons.php');
		
		echo '<input type="hidden" name="keep_tags[]" value="content" />
		<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'] .'" />
	</form>';
	
}

?>