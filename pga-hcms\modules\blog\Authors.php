<?php

//Dashboard widget
if(SECTION_ID == 4){
	$total_records = $db->get_record_count('blog_authors');
	$CMSBuilder->set_widget($_cmssections['blog_authors'], 'Total '.$sitemap[$_cmssections['blog']]['name'].' Authors', $total_records);
}

if(SECTION_ID == $_cmssections['blog_authors']){
	
	//Instatiate Blog
	include_once($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Blog.class.php");
	$Blog = new Blog();
	$blog_settings = $Blog->blog_settings();

	//Define vars
	$record_db = 'blog_authors';
	$record_id = 'author_id';
	$record_name = 'Author';
	$record_names = $record_name.'s';
	
	//Validation
	$errors   = false;
	$required = [];
	$required_fields = ['name'];
	
	//Image Uploader
	$imagedir = '../images/blog/authors/';
	$CMSUploader = new CMSUploader('blog_author', $imagedir);
	
	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.name",
		"$record_db.title",
		"$record_db.email"
	];

	//Redirects
	$blog_pageurl = get_page_url($_sitepages['blog']);
	$entries_section = $CMSBuilder->get_section($_cmssections['blog_entries']);

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;

	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	$db->query("SELECT $record_db.*, COUNT(entry_id) as num_entries FROM $record_db LEFT JOIN blog_entries ON blog_entries.$record_id = $record_db.$record_id $where GROUP BY $record_id ORDER BY ordering", $params);
	$records_arr = $db->fetch_assoc($record_id);
	foreach ($records_arr as $item_id => &$record) {
		$record['image']       = check_file($record['image'], $imagedir);
		$record['page_url']    = $siteurl.$root.$blog_pageurl.$record['page'].'-'.$item_id.'/';
		$record['entries_url'] = $entries_section['page_url']."?$record_id=$item_id";
		unset($record);
	}

	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}
	
	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];
		}
	}
	
	//Delete item
	if(isset($_POST['delete'])){
		
		if(ITEM_ID == 1){
			$CMSBuilder->set_system_alert('You cannot delete the default author '.$_POST['name'].'.',false);
		}else{

			$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
			if(!$db->error()) {

				//Delete image
				$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
				
				//Save sitemap
				sitemap_XML();

				$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

			}else{
				$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
			}

			header("Location: " .PAGE_URL);
			exit();
		}
			
	//Save item
	}else if(isset($_POST['save'])){
		
		//Format data
		$_POST['content'] = trim(str_replace("<p>&nbsp;</p>", "", $_POST['content']));
		
		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);
		
		//Required fields validation
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}
		
		//Image validation
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large.';
			$required[] = 'image';
		}

		//Email validation
		if($_POST['email'] && !checkmail($_POST['email'])){
			$errors[] = 'Please enter a valid email.';
			$required[] = 'email';
		}
	
		if(!$errors){
			
			//Format validated data
			$pagename  = clean_url($_POST['name']);
			$content   = trim(str_replace(['&nbsp;'], '', strip_tags($_POST['content']))) ? $_POST['content'] : NULL;
		
			//Delete image
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//Upload image
			try{
				$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}
	
			//Insert to db
			$params = array(
				$record_id => ITEM_ID,
				'name' => $_POST['name'],
				'page' => $pagename,
				'title' => $_POST['title'],
				'email' => $_POST['email'],
				'phone' => $_POST['phone'],
				'image' => $images['image'] ?? NULL,
				'image_alt' => $_POST['image_alt'],
				'content' => $content,
				'showhide' => $_POST['showhide'],
				'last_updated' => date('Y-m-d H:i:s')
			);
			$db->insert($record_db, $params, $params);
			if(!$db->error()){
					
				//Save sitemap
				sitemap_XML();

				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}
				
			}else{
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}
			
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}

		foreach($_POST AS $key=>$data){
			$row[$key] = $data;
		}
	
	//Handle images
	}else{
		include('modules/CropImages.php');
	}
}

?>