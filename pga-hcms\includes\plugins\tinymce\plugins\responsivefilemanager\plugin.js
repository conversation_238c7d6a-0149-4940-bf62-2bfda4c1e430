//Copied structure of other plugins for responsivefilemanager to be compatible with new version of tinymce

(function () {

	var defs = {}; // id -> {dependencies, definition, instance (possibly undefined)}

	// Used when there is no 'main' module.
	// The name is probably (hopefully) unique so minification removes for releases.
	var register_3795 = function (id) {
	  var module = dem(id);
	  var fragments = id.split('.');
	  var target = Function('return this;')();
	  for (var i = 0; i < fragments.length - 1; ++i) {
	    if (target[fragments[i]] === undefined)
	      target[fragments[i]] = {};
	    target = target[fragments[i]];
	  }
	  target[fragments[fragments.length - 1]] = module;
	};

	var instantiate = function (id) {
	  var actual = defs[id];
	  var dependencies = actual.deps;
	  var definition = actual.defn;
	  var len = dependencies.length;
	  var instances = new Array(len);
	  for (var i = 0; i < len; ++i)
	    instances[i] = dem(dependencies[i]);
	  var defResult = definition.apply(null, instances);
	  if (defResult === undefined)
	     throw 'module [' + id + '] returned undefined';
	  actual.instance = defResult;
	};

	var def = function (id, dependencies, definition) {
	  if (typeof id !== 'string')
	    throw 'module id must be a string';
	  else if (dependencies === undefined)
	    throw 'no dependencies for ' + id;
	  else if (definition === undefined)
	    throw 'no definition function for ' + id;
	  defs[id] = {
	    deps: dependencies,
	    defn: definition,
	    instance: undefined
	  };
	};

	var dem = function (id) {
	  var actual = defs[id];
	  if (actual === undefined)
	    throw 'module [' + id + '] was undefined';
	  else if (actual.instance === undefined)
	    instantiate(id);
	  return actual.instance;
	};

	var req = function (ids, callback) {
	  var len = ids.length;
	  var instances = new Array(len);
	  for (var i = 0; i < len; ++i)
	    instances.push(dem(ids[i]));
	  callback.apply(null, callback);
	};

	var ephox = {};

	ephox.bolt = {
	  module: {
	    api: {
	      define: def,
	      require: req,
	      demand: dem
	    }
	  }
	};

	var define = def;
	var require = req;
	var demand = dem;
	// this helps with minificiation when using a lot of global references
	var defineGlobal = function (id, ref) {
	  define(id, [], function () { return ref; });
	};
	/*jsc
	["tinymce.plugins.responsivefilemanager.Plugin","tinymce.core.PluginManager","tinymce.core.util.Tools","global!tinymce.util.Tools.resolve"]
	jsc*/
	defineGlobal("global!tinymce.util.Tools.resolve", tinymce.util.Tools.resolve);
	/**
	 * ResolveGlobal.js
	 *
	 * Released under LGPL License.
	 * Copyright (c) 1999-2017 Ephox Corp. All rights reserved
	 *
	 * License: http://www.tinymce.com/license
	 * Contributing: http://www.tinymce.com/contributing
	 */

	define(
	  'tinymce.core.PluginManager',
	  [
	    'global!tinymce.util.Tools.resolve'
	  ],
	  function (resolve) {
	    return resolve('tinymce.PluginManager');
	  }
	);

	/**
	 * ResolveGlobal.js
	 *
	 * Released under LGPL License.
	 * Copyright (c) 1999-2017 Ephox Corp. All rights reserved
	 *
	 * License: http://www.tinymce.com/license
	 * Contributing: http://www.tinymce.com/contributing
	 */

	define(
	  'tinymce.core.util.Tools',
	  [
	    'global!tinymce.util.Tools.resolve'
	  ],
	  function (resolve) {
	    return resolve('tinymce.util.Tools');
	  }
	);

	/**
	 * Plugin.js
	 *
	 * Released under LGPL License.
	 * Copyright (c) 1999-2017 Ephox Corp. All rights reserved
	 *
	 * License: http://www.tinymce.com/license
	 * Contributing: http://www.tinymce.com/contributing
	 */

	/**
	 * This class contains all core logic for the responsivefilemanager plugin.
	 *
	 * @class tinymce.responsivefilemanager.Plugin
	 * @private
	 */
	define(
		'tinymce.plugins.responsivefilemanager.Plugin',
		[
			'tinymce.core.PluginManager',
			'tinymce.core.util.Tools'
		],
		function (PluginManager, Tools) {
			PluginManager.add('responsivefilemanager', function (editor) {
	       		function responsivefilemanager_onMessage(event){
	       		    if(editor.settings.external_filemanager_path.toLowerCase().indexOf(event.origin.toLowerCase()) === 0){
	       		        if(event.data.sender === 'responsivefilemanager'){
	       		            // tinymce.activeEditor.insertContent(event.data.html);
	       		            editor.insertContent(event.data.html);
	       		            editor.windowManager.close();

	       		            // Remove event listener for a message from ResponsiveFilemanager
	       		            if(window.removeEventListener){
	       		                window.removeEventListener('message', responsivefilemanager_onMessage, false);
	       		            } else {
	       		                window.detachEvent('onmessage', responsivefilemanager_onMessage);
	       		            }
	       		        }
	       		    }
	       		}

	       		function openmanager() {
	       		    var width = window.innerWidth-30;
	       		    var height = window.innerHeight-60;
	       		    if(width > 1800) width=1800;
	       		    if(height > 1200) height=1200;
	       		    var width_reduce = (width - 20) % 138;
	       		    width = width - width_reduce + 10;
	       		    if(width>600){
	       		        var width_reduce = (width - 20) % 138;
	       		        width = width - width_reduce + 10;
	       		    }

	       		    editor.focus(true);
	       		    var title="RESPONSIVE FileManager";
	       		    if (typeof editor.settings.filemanager_title !== "undefined" && editor.settings.filemanager_title) {
	       		        title=editor.settings.filemanager_title;
	       		    }
	       		    var akey="key";
	       		    if (typeof editor.settings.filemanager_access_key !== "undefined" && editor.settings.filemanager_access_key) {
	       		        akey=editor.settings.filemanager_access_key;
	       		    }
	       		    var sort_by="";
	       		    if (typeof editor.settings.filemanager_sort_by !== "undefined" && editor.settings.filemanager_sort_by) {
	       		        sort_by="&sort_by="+editor.settings.filemanager_sort_by;
	       		    }
	       		    var descending="false";
	       		    if (typeof editor.settings.filemanager_descending !== "undefined" && editor.settings.filemanager_descending) {
	       		        descending=editor.settings.filemanager_descending;
	       		    }
	       		    var fldr="";
	       		    if (typeof editor.settings.filemanager_subfolder !== "undefined" && editor.settings.filemanager_subfolder) {
	       		        fldr="&fldr="+editor.settings.filemanager_subfolder;
	       		    }
	       		    var crossdomain="";
	       		    if (typeof editor.settings.filemanager_crossdomain !== "undefined" && editor.settings.filemanager_crossdomain) {
	       		        crossdomain="&crossdomain=1";

	       		        // Add handler for a message from ResponsiveFilemanager
	       		        if(window.addEventListener){
	       		            window.addEventListener('message', responsivefilemanager_onMessage, false);
	       		        } else {
	       		            window.attachEvent('onmessage', responsivefilemanager_onMessage);
	       		        }
	       		    }

	       		    win = editor.windowManager.open({
	       		        title: title,
	       		        file: editor.settings.external_filemanager_path+'dialog.php?type=4&descending='+descending+sort_by+fldr+crossdomain+'&lang='+editor.settings.language+'&akey='+akey,
	       		        width: width,
	       		        height: height,
	       		        inline: 1,
	       		        resizable: true,
	       		        maximizable: true
	       		    });
	       		}

	       		editor.addButton('responsivefilemanager', {
	       		    icon: 'browse',
	       		    tooltip: 'Insert file',
	       		    shortcut: 'Ctrl+E',
	       		    onclick:openmanager
	       		});

	       		editor.addShortcut('Ctrl+E', '', openmanager);

	       		editor.addMenuItem('responsivefilemanager', {
	       		    icon: 'browse',
	       		    text: 'Insert file',
	       		    shortcut: 'Ctrl+E',
	       		    onclick: openmanager,
	       		    context: 'insert'
	       		});
	       		
			});
			return function () { };
	   }//END function
	);//END define
	dem('tinymce.plugins.responsivefilemanager.Plugin')();
})();