<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	
	echo "<form id='search-form' action='' method='get' class='multiple-search f_left'>";
		echo "<select name='award' class='select f_left'>";
			echo "<option value=''>- Select Award -</option>";
			if(!empty($awards)) {
				foreach($awards as $cat){
					echo "<option value='".$cat."' ".(!empty($award) && $award == $cat ? "selected" : "").">".$cat."</option>";
				}
			}
		echo "</select>";
		echo "<div class='relative f_left'>
			<input type='text' name='search' class='input f_left' value='" .$searchterm. "' placeholder='Search' />";
			if($searchterm != ''){ 
				echo "<a id='clear-search'><i class='fa fa-times-circle'></i></a>";
			}
			echo "<button type='button' class='button' onclick='this.form.submit();'><i class='fa fa-search'></i></button>
		</div>
	</form>
	<form id='clear-search-form' name='clear-search-form' class='hidden' action='" .PAGE_URL. "' method='post'>
		<input type='hidden' name='clear-search' value='Clear' />
		<input type='hidden' name='search' value='' />
		<input type='hidden' name='xssid' value='" .$_COOKIE['xssid']. "' />
	</form>";
	
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>" .$record_name."s  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter stickyheader sortable'>";
		
			echo "<thead>";
			echo "<th width='40px' class='{sorter:false}'></th>";	
			echo "<th width='250px'>Name</th>";
			echo "<th width='150px'>Year</th>";
			echo "<th width='200px'>Award</th>";
			echo "<th width='70px'>Visible</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .($row['image'] != "" ? "<a href='" .$path.$imagedir.$row['image']. "' class='light-gallery' title='" .$row['first_name']. " " .$row['last_name']. "'>" .renderGravatar($imagedir.$row['image']). "</a>" : ""). "</td>";
					echo "<td>" .$row['first_name']. " " .$row['last_name']. "</td>";
					echo "<td>" .$row['year']. "</td>";
					echo "<td>" .$row['award']. "</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo "</div>";	
	echo "</div>";

//Image cropping
} else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');

} else {

	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		$image = $data['image'];
		$photo = $data['photo'];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	} else if(ACTION == 'add' && !isset($_POST['save'])){
		$image = '';	
		$photo = '';
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		//Award details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Award Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";			
				echo "<div class='form-field'>
					<label>Award <span class='required'>*</span></label>
					<select name='award' class='select" .(in_array('award', $required) ? ' required' : ''). "'>
					<option value=''>- Select -</option>";
						if(!empty($awards)) {
							foreach($awards as $cat){
								echo "<option value='".$cat."' ".(isset($row['award']) && $row['award'] == $cat ? "selected" : "").">".$cat."</option>";
							}
						}
					echo "</select>
				</div>";
				echo "<div class='form-field'>
					<label>Year <span class='required'>*</span></label>
					<select name='year' class='select" .(in_array('year', $required) ? ' required' : ''). "'>
					<option value=''>- Select -</option>";
						for($y=date('Y'); $y>=1950; $y--){
							echo "<option value='" .$y. "'" .(isset($row['year']) && $row['year'] == $y ? ' selected' : ''). ">" .$y. "</option>";
						}
					echo "</select>
				</div>";
				echo "<div class='form-field'>
					<label>Type</label>
					<select name='type' class='select" .(in_array('type', $required) ? ' required' : ''). "'>
					<option value=''>- None -</option>";
						if(!empty($types)) {
							foreach($types as $type){
								echo "<option value='".$type."' ".(isset($row['type']) && $row['type'] == $type ? "selected" : "").">".$type."</option>";
							}
						}
					echo "</select>
				</div>";
			echo "</div>";
		echo "</div>";
	
		//Winner details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				<div class='panel-switch'>
					<label>Show $record_name</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " />
						<label for='showhide'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field ui-front'>
					<label>Member Search</label>
					<input type='text' name='term' value='' class='member_suggest input' />
				</div>";
				echo "<hr class='clear' />";
				echo "<div class='form-field'>
					<label>First Name <span class='required'>*</span></label>
					<input type='text' name='first_name' value='" .(isset($row['first_name']) ? $row['first_name'] : ''). "' class='input first_name" .(in_array('first_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Last Name <span class='required'>*</span></label>
					<input type='text' name='last_name' value='" .(isset($row['last_name']) ? $row['last_name'] : ''). "' class='input last_name" .(in_array('last_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Description</label>
					<input type='text' name='description' value='" .(isset($row['description']) ? $row['description'] : ''). "' class='input facility_name" .(in_array('description', $required) ? ' required' : ''). "' />
				</div>";
				
				echo "<input type='hidden' name='profile_first_name' class='first_name' value='" .(isset($row['profile_first_name']) ? $row['profile_first_name'] : '') ."' />";
				echo "<input type='hidden' name='profile_last_name' class='last_name' value='" .(isset($row['profile_last_name']) ? $row['profile_last_name'] : '') ."' />";
				echo "<input type='hidden' name='account_id' class='account_id' value='" .(isset($row['account_id']) ? $row['account_id'] : '') ."' />";
	
			echo "</div>";
		echo "</div>"; // END Details

		//Image
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Photo
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='img-holder' id='" .(!isset($image) || $image == $photo ? "profile_photo" : ""). "'>";
				if(isset($image) && $image != '' && file_exists($imagedir.$image)){
						echo "<a href='" .$path.$imagedir.$row['image']. "' class='light-gallery' title='" .$row['first_name']. " " .$row['last_name']. "'>
							<img src='" .$path.$imagedir.$image. "' alt='' />
						</a>";
						if($image != $photo){
							echo "<input type='checkbox' class='checkbox' name='deleteimage' id='deleteimage' value='1'>
							<label for='deleteimage'>Delete Current Image</label>";
						}
				}
				echo "</div>";
				[$max_W, $max_H] = CMSUploader::max_size('award_winners_img', 'image');
				echo "<div class='form-field'>
					<label>Upload Image " .$CMSBuilder->tooltip('Upload Image', 'Image dimensions must be at least '.$max_W.' x '.$max_H.' and file size must be smaller than 20MB.'). "</label>
					<input type='file' class='input" .(in_array('image', $required) ? ' required' : ''). "' name='image' value='' />
					<input type='hidden' name='old_image' value='" .(isset($image) && $image != '' && $image != $photo && file_exists($imagedir.$image) ? $image : ''). "' />
				</div>";
			echo "</div>";
		echo "</div>"; //END Image

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";

}

?>