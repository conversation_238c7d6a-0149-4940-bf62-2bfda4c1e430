<?php

$html = '<div class="center">';	

	//Current winners
	if(!empty($current_winners)){
		$html .= '<h3>' .$award_year.' '.$award_type.' Winners</h3>';
		foreach($current_winners as $winner){
			
			if(!empty($winner['type'])){
				$winner['description'] = '<h5 class="nomargin">' .$winner['type']. '</h5>' .$winner['description'];
			}
			$html .= format_personnel($winner['first_name'], $winner['last_name'], $winner['description'], $winner['profile_url'], $winner['image']);
		}
	}

	//Past winners
	if(!empty($past_winners)){
		$html .= '<br class="clear" /><br />
		<h3>Past '.$award_type.' Winners</h3>';
		foreach($past_winners as $winner){
			
			if(PAGE_ID == 45){
				$winner['description'] .= '<br />'.$winner['year'].' Winner';
			}else{
				$winner['description'] = '<h5 class="nomargin">' .(!empty($winner['type']) ? $winner['type'].', '.$winner['year'] : $winner['year'].' Winner'). '</h5>' .$winner['description'];
			}
			
			$html .= format_personnel($winner['first_name'], $winner['last_name'], $winner['description'], $winner['profile_url'], $winner['image']);
		}
	}

$html .= '</div>';

//Set panel content
$page['page_panels'][$panel_id]['content'] .= $html;
include("includes/pagepanels.php");

?>