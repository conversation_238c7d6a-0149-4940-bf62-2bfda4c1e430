<?php
// pages/account/profile.php - Displays the Edit Profile form

// --- Environment Setup ---
// Ensure session is started BEFORE any output
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// --- Data Fetching & Login Check ---
// This block runs on EVERY load of this page (GET or after POST redirect)

$Account = null; // Initialize $Account to null
$current_account_id = null;
$login_check_ok = false;

// Determine logged-in user ID (Adjust based on your auth mechanism)
if (defined('USER_LOGGED_IN') && USER_LOGGED_IN) {
    $current_account_id = USER_LOGGED_IN;
    $login_check_ok = true;
} elseif (isset($_SESSION['auth']['account_id'])) { // Example fallback check
     $current_account_id = $_SESSION['auth']['account_id'];
     $login_check_ok = true;
} // Add other checks if necessary

if (!$login_check_ok) {
    // Not logged in - Redirect or display error message
    echo "<p style='color:red; font-weight:bold;'>Access Denied. Please log in.</p>";
    exit;
}

// --- Instantiate or Reload the Account Object ---
try {
    $Account = new Account(null, $current_account_id); // Assumes constructor loads profile
} catch (Exception $e) {
    error_log("Error loading Account object for profile page: " . $e->getMessage());
    echo "<p style='color:red;'>Error loading account data. Please try again later.</p>";
    exit;
}
// --- End Account Object Load ---

// --- Retrieve session data for feedback/repopulation ---
$success_message = $_SESSION['profile_success_message'] ?? null;
$error_message = $_SESSION['profile_error_message'] ?? null;
$submitted_data = $_SESSION['profile_form_data'] ?? null; // Data submitted on last attempt (if error)

unset($_SESSION['profile_success_message']);
unset($_SESSION['profile_error_message']);
unset($_SESSION['profile_form_data']);

// --- Helper function definitions ---
// (Ideally move to a shared include file)
if (!function_exists('echo_selected_with_post')) {
    function echo_selected_with_post($submitted_value, $current_value, $option_value) {
        $value_to_check = $current_value;
        if ($submitted_value !== null) {
            $value_to_check = $submitted_value;
        }
        if ((string)$value_to_check == (string)$option_value) {
            echo ' selected';
        }
    }
}

if (!function_exists('get_form_value')) {
    function get_form_value($field_name, $submitted_data_array, $account_object, $is_textarea = false) {
        $value = null;
        if (isset($submitted_data_array[$field_name])) {
            $value = $submitted_data_array[$field_name];
        } elseif (isset($account_object->$field_name)) {
             $value = $account_object->$field_name;
        }
        return $is_textarea ? ($value ?? '') : htmlspecialchars($value ?? '', ENT_QUOTES, 'UTF-8');
    }
}
// Helper function specifically for array inputs like q_answer
if (!function_exists('get_form_array_value')) {
    function get_form_array_value($array_name, $key, $submitted_data_array, $current_data_array) {
         $value = null;
         // Check submitted data first (e.g., $_POST['q_answer'][1])
         if (isset($submitted_data_array[$array_name][$key])) {
              $value = $submitted_data_array[$array_name][$key];
         }
         // Fallback to current data (e.g., $profile_answers[1])
         elseif (isset($current_data_array[$key])) {
              $value = $current_data_array[$key];
         }
         return htmlspecialchars($value ?? '', ENT_QUOTES, 'UTF-8');
    }
}


// --- Prepare specific values for the form ---
// Format dates
$member_since_formatted = '';
$member_since_source = $submitted_data['pga_member_since'] ?? $Account->pga_member_since ?? '';
if (!empty($member_since_source) && $member_since_source != '0000-00-00') {
    $member_since_timestamp = strtotime($member_since_source);
    if ($member_since_timestamp) {
        $member_since_formatted = date('Y-m-d', $member_since_timestamp);
    }
}

// Image paths
$imagedir = 'images/users/thumbs/'; // Display thumbnail
$imagepath = (isset($path) ? $path : '/') . $imagedir; // $path should be global

// Assume $provinces and $states arrays are available globally or included
global $provinces, $states, $db; // Ensure $db is available
// Define $countries if not global
$countries = ['CA' => 'Canada', 'US' => 'United States']; // Add more if needed

// --- Define URLs ---
// Processing script URL (This form posts to itself, processing handled by included module)
$processing_script_url = ''; // Action is empty, handled by index.php including modules/account/profile.php
// URL for the main account page
$account_page_url = (isset($path) ? $path : '/') . 'account/'; // Adjust if needed


// --- Fetch Profile Questions ---
$profile_questions = [];
if (isset($db) && is_object($db)) {
    $query = $db->query("SELECT `question_id`, `question` FROM `account_profile_questions` WHERE `showhide` = 0 ORDER BY `ordering` ASC"); // Ensure ASC order
    if ($query && !$db->error()) {
        // Use fetch_array assuming it gets all rows as numerically indexed array of assoc arrays
        $question_rows = $db->fetch_array($query);
        if (is_array($question_rows)) {
            foreach ($question_rows as $row) {
                $profile_questions[$row['question_id']] = $row['question'];
            }
        }
    } else {
        error_log("Error fetching profile questions: " . ($db->error() ?? 'Query failed'));
    }
} else {
    error_log("Database object not available to fetch profile questions.");
}
// --- End Fetch Profile Questions ---


// --- Fetch Existing Answers ---
$profile_answers = [];
if (isset($db) && is_object($db) && isset($Account->account_id)) {
     $query_answers = $db->query("SELECT `question_id`, `answer` FROM `account_profile_answers` WHERE `account_id` = ?", [$Account->account_id]);
     if ($query_answers && !$db->error()) {
          // Use fetch_array and loop through results
          $answer_rows = $db->fetch_array($query_answers);
          if (is_array($answer_rows)) {
              foreach ($answer_rows as $row) {
                  $profile_answers[$row['question_id']] = $row['answer'];
              }
          }
     } else {
          error_log("Error fetching profile answers: " . ($db->error() ?? 'Query failed'));
     }
}
// --- End Fetch Existing Answers ---


// --- Display Messages ---
if (!empty($success_message)) {
    echo '<div class="alert alert-success" style="background-color: #dff0d8; border: 1px solid #d6e9c6; color: #3c763d; padding: 15px; margin-bottom: 20px;">' . htmlspecialchars($success_message, ENT_QUOTES, 'UTF-8') . '</div>';
}
if (!empty($error_message)) {
    echo '<div class="alert alert-danger" style="background-color: #f2dede; border: 1px solid #ebccd1; color: #a94442; padding: 15px; margin-bottom: 20px;">' . $error_message . '</div>'; // Error message might contain HTML (<br>), don't escape fully
}

// --- Display Form ---
?>

<h3>Edit Profile</h3>

<form name="profile-form" id="profile-form" method="post" action="" enctype="multipart/form-data"> <?php // Action is empty to post to self ?>

    <?php /* ----- Current Golf Facility (Static Section) ----- */ ?>
    <h4 style="margin-top: 30px;">Current Golf Facility</h4>
    <div class="static-info-box form-grid">
        <?php // Using dummy data as requested ?>
        <div><label>Facility Name:</label> <span>ABC Company Facility</span><br><br><label>Membership Type:</label> <span>None</span></div>
        <div><label>Facility Phone Number:</label> <span>************</span><br><br><label>Member Classification:</label> <span>Head Professional</span></div>
    </div>
    <p><small>Note: If you would like to modify your facility information please contact our offices.</small></p>

    <?php /* ----- Current Address Section ----- */ ?>
    <h4 style="margin-top: 30px;">Current Address</h4>
    <div class="form-grid">
        <div class="form-field"><label for="address1">Street Address</label><input type="text" name="address1" id="address1" class="input" value="<?php echo get_form_value('address1', $submitted_data, $Account); ?>" /></div>
        <div class="form-field"><label for="province">Province/State</label><select name="province" id="province" class="select"><option value="">- Select -</option><?php if(isset($provinces)&&is_array($provinces)):?><optgroup label="Canada"><?php foreach($provinces as $code=>$name){ echo "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['province']??null,$Account->province??'',$code);echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";}?></optgroup><?php endif;?><?php if(isset($states)&&is_array($states)):?><optgroup label="United States"><?php foreach($states as $code=>$name){ echo "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['province']??null,$Account->province??'',$code);echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";}?></optgroup><?php endif;?></select></div>
        <div class="form-field"><label for="address2">Unit No.</label><input type="text" name="address2" id="address2" class="input" value="<?php echo get_form_value('address2', $submitted_data, $Account); ?>" /></div>
        <div class="form-field"><label for="postalcode">Postal/Zip Code</label><input type="text" name="postalcode" id="postalcode" class="input" value="<?php echo get_form_value('postalcode', $submitted_data, $Account); ?>" /></div>
        <div class="form-field"><label for="city">City/Town</label><input type="text" name="city" id="city" class="input" value="<?php echo get_form_value('city', $submitted_data, $Account); ?>" /></div>
        <div class="form-field"><label for="country">Country</label><select name="country" id="country" class="select"><option value="">- Select -</option><?php foreach($countries as $code=>$name){ echo "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['country']??null,$Account->country??'',$code);echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";}?></select></div>
    </div>

    <?php /* ----- Photo & Social Media Section ----- */ ?>
    <div class="form-grid" style="margin-top: 30px; align-items: flex-start;">
        <div> <?php // Left Column: Photo ?>
            <h4>Photo</h4>
            <div class="form-field photo-upload-area">
                <?php $current_photo = $Account->photo ?? null; $photo_src = null; if (!empty($current_photo) && file_exists($_SERVER['DOCUMENT_ROOT'] . $imagepath . $current_photo)) { $photo_src = htmlspecialchars($imagepath . $current_photo, ENT_QUOTES, 'UTF-8').'?t='.time(); /* Add timestamp to bypass cache */ } ?>
                <label for="photo">Profile Photo</label>
                <?php if ($photo_src): ?>
                    <div class="current-photo"> <img src="<?php echo $photo_src; ?>" alt="Current Profile Photo" class="profile-thumbnail" style="max-width: 150px; max-height: 150px; display: block; margin-bottom: 10px;" /> <div class="photo-actions"> <button type="button" id="delete-profile-photo-btn" class="btn btn-danger" data-filename="<?php echo htmlspecialchars($current_photo, ENT_QUOTES, 'UTF-8'); ?>" style="/* Add styles */"> <i class="fas fa-trash-alt"></i> Delete Photo </button> <input type="hidden" name="old_photo" value="<?php echo htmlspecialchars($current_photo, ENT_QUOTES, 'UTF-8'); ?>" /> </div> </div>
                <?php else: ?>
                    <p class="no-photo-message" style="min-height: 170px;">No current photo.</p> <?php // Add min-height to prevent layout jump ?> <input type="hidden" name="old_photo" value="" />
                <?php endif; ?>
                <div class="upload-field" style="margin-top: 15px;"> <input type="file" name="photo" id="photo" class="input-file" accept="image/jpeg, image/png" /> <p class="field-help"><small>(JPG/PNG, 500*500px, Up to 2MB)</small></p> </div>
            </div>
        </div>
        <div> <?php // Right Column: Social Media ?>
            <h4>Social Media</h4> <p><small>Enter the full URLs (e.g., https://...)</small></p>
            <div class="form-field"><label for="facebook">Facebook</label><input type="url" name="facebook" id="facebook" class="input" placeholder="https://" value="<?php echo get_form_value('facebook', $submitted_data, $Account); ?>" /></div>
            <div class="form-field"><label for="linkedin">LinkedIn</label><input type="url" name="linkedin" id="linkedin" class="input" placeholder="https://" value="<?php echo get_form_value('linkedin', $submitted_data, $Account); ?>" /></div>
            <div class="form-field"><label for="instagram">Instagram</label><input type="url" name="instagram" id="instagram" class="input" placeholder="https://" value="<?php echo get_form_value('instagram', $submitted_data, $Account); ?>" /></div>
            <div class="form-field"><label for="twitter">Twitter</label><input type="url" name="twitter" id="twitter" class="input" placeholder="https://" value="<?php echo get_form_value('twitter', $submitted_data, $Account); ?>" /></div>
        </div>
    </div>

    <?php /* ----- Biography Section ----- */ ?>
    <h4 style="margin-top: 30px;">Biography</h4>
    <div class="form-grid">
        <div class="form-field"><label for="pga_member_since">PGA Member Since</label><input type="date" name="pga_member_since" id="pga_member_since" class="input" value="<?php echo htmlspecialchars($member_since_formatted, ENT_QUOTES, 'UTF-8'); ?>" /></div>
        <div class="form-field"><label for="website">Website</label><input type="url" name="website" id="website" class="input" placeholder="https://" value="<?php echo get_form_value('website', $submitted_data, $Account); ?>" /></div>
        <div class="form-field"><label for="profile">Profile</label><textarea name="profile" id="profile" class="textarea" rows="8"><?php echo get_form_value('profile', $submitted_data, $Account, true); ?></textarea><small>Approx 325 words.</small></div>
        <div class="form-field"><label for="education">Education Background</label><textarea name="education" id="education" class="textarea" rows="8"><?php echo get_form_value('education', $submitted_data, $Account, true); ?></textarea></div>
    </div>

    <?php /* ----- Q&A Section ----- */ ?>
    <?php if (!empty($profile_questions)): ?>
        <h4 style="margin-top: 30px;">Q&A</h4>
        <div class="form-grid">
            <?php
            foreach ($profile_questions as $qid => $qtext):
                $input_name = "q_answer[" . $qid . "]"; // Use array notation
                $answer_value = get_form_array_value('q_answer', $qid, $submitted_data, $profile_answers);
            ?>
                <div class="form-field">
                    <label for="q_answer_<?php echo $qid; ?>"><?php echo htmlspecialchars($qtext, ENT_QUOTES, 'UTF-8'); ?></label>
                    <input type="text" name="<?php echo $input_name; ?>" id="q_answer_<?php echo $qid; ?>" class="input" value="<?php echo $answer_value; // Already escaped by helper ?>" />
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <?php /* ----- Action Buttons ----- */ ?>
    <div class="form-actions" style="margin-top: 30px; display: flex; justify-content: space-between; align-items: center;">
        <a href="<?php echo htmlspecialchars($account_page_url, ENT_QUOTES, 'UTF-8'); ?>" class="button primary black back-button"> BACK TO MY ACCOUNT <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
        <button type="submit" id="save-profile-btn" name="submitform" class="button primary red save-button" value="Save Changes"> SAVE CHANGES <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> </button>
	</div>

    <?php /* ----- Hidden Fields ----- */ ?>
	<input type="hidden" name="update_profile" value="1" />
    <?php if(isset($_COOKIE['xid'])): // Add XID if cookie exists ?>
    <input type="hidden" name="xid" value="<?php echo htmlspecialchars($_COOKIE['xid'], ENT_QUOTES, 'UTF-8'); ?>" />
    <?php endif; ?>
</form>