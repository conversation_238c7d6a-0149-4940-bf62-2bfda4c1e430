<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('pages_leadins');
	$CMSBuilder->set_widget($_cmssections['leadins'], 'Total Attention Boxes', $total_records);
}

if(SECTION_ID == $_cmssections['leadins']){

	//Define vars
	$record_db 	  = 'pages_leadins';
	$record_id 	  = 'leadin_id';
	$record_name  = 'Attention Box';
	$records_name = 'Attention Boxes';
	
	//Validation
	$errors 	= false;
	$required 	= [];
	$required_fields = [
		'title', 
		'type', 
		'theme', 
		'show_again_days', 
		'button_type', 
		'content'
	];

	//Image Uploader
	$imagedir    = '../images/leadins/';
	$CMSUploader = new CMSUploader();

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.title",
		"$record_db.content",
		"$record_db.url",
		"$record_db.url_text"
	];

	//Stats section
	$statspage    = $CMSBuilder->get_section($_cmssections['leadin_stats']);


	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}


	//Get Records
	$db->query("SELECT * FROM $record_db $where", $params);
	$records_arr = $db->fetch_assoc($record_id);
	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}


	//Supplumentary data
	$leadin_themes           = $db->get_enum_vals($record_db, 'theme');
	$leadin_themes           = array_combine($leadin_themes, array_map('ucwords', $leadin_themes));
	$leadin_themes['theme1'] = $cms_settings['theme_color1'];
	$leadin_themes['theme2'] = $cms_settings['theme_color2'];

	$leadin_types          = $db->get_enum_vals($record_db, 'type');
	$leadin_types          = array_combine($leadin_types, array_map('ucwords', $leadin_types));
	$leadin_types['popup'] = 'Intrusive Pop-up';

	$field_types = $db->get_enum_vals('pages_leadins_fields', 'type');

	$button_animations = [
		'bounce'      => 'Bounce', 
		'rubber-band' => 'Stretch', 
		'swing'       => 'Swing', 
		'tada'        => 'Tada!'
	];

	$default_form_fields = [
		['field_id' => NULL, 'type' => 'text',  'label' => 'First Name',   'required' => 1],
		['field_id' => NULL, 'type' => 'text',  'label' => 'Last Name',    'required' => 1],
		['field_id' => NULL, 'type' => 'email', 'label' => 'Email',        'required' => 1],
		['field_id' => NULL, 'type' => 'text',  'label' => 'Phone Number', 'required' => 0]
	];

	//Selected item
	if(ACTION == 'edit'){
		if($row = $records_arr[ITEM_ID] ?? false){

			//Reset cropsize according to type
			if($row['type'] == 'popup' || $row['type'] == 'corner'){
				$CMSUploader->set_crop_type('leadin-'.$row['type'], $imagedir);
			}

			$row['button_type'] = $row['url_target'] == 3 ? 'form' : 'link';

			//Get form fields
			$db->query("SELECT * FROM `pages_leadins_fields` WHERE `$record_id` = ? ORDER BY `ordering` ASC, `field_id` ASC", [ITEM_ID]);
			$row['form_fields'] = $db->fetch_assoc('field_id');

			$records_arr[ITEM_ID] = $row;
			
		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()) {
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}

		header("Location: " .PAGE_URL);
		exit();
		

	//Save item
	}else if(isset($_POST['save'])){

		$_POST['showhide'] = +!isset($_POST['showhide']);

		// Format form data
		if($_POST['button_type'] == 'form'){
			$_POST['url'] = NULL;
			$_POST['url_target'] = 3;
			$_POST['form_fields'] = $_POST['form_fields'] ?? [];

		// Reset form data
		}else{
			$_POST['form_title'] = NULL;
			$_POST['form_content'] = NULL;
			$_POST['form_button_text'] = NULL;
			$_POST['form_success_content'] = NULL;
			$_POST['form_fields'] = [];
		}

		// Format bar type data
		if($_POST['type'] != 'bar'){
			$_POST['button_style'] = NULL;
		}
		if($_POST['type'] == 'bar' && ($_POST['position'] ?? '') == 'top'){
			$_POST['delay'] = 0;
			$_POST['delay_type'] = 'scroll';

		// Delay is needed if not a top bar
		}else{
			$required_fields[] = 'delay_type';
		}

		// Format popup type data
		if($_POST['type'] == 'popup'){
			$_POST['position'] = NULL;

			if($_POST['button_type'] == 'form'){
				$_POST['url_text'] = NULL;
				$_POST['button_animation'] = NULL;
			}

		// Corner and bar require position
		}else{
			$required_fields[] = 'position';
		}

		//Required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === '') {
				$required[] = $field;
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';

				if($field == 'content'){
					$errors[] = 'Content is required.';
				}
			}
		}

		// Required form field data
		if($_POST['button_type'] == 'form'){
			foreach($_POST['form_fields'] as $i => $field){
				if(($field['label'] ?? '') === ''){
					$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
					$required[] = "form_fields[$i][label]";
				}
				if(($field['type'] ?? '') == 'dropdown' && ($field['options'] ?? '') === ''){
					$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
					$required[] = "form_fields[$i][options]";
				}
			}

			if(empty($_POST['form_fields'])){
				$errors[] = 'At least one form field is required.';
			}
		}


		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large. Cannot exceed '.$_max_filesize['megabytes'].'.';
		}

		//Display Again Days must be > 0
		if(empty($_POST['show_again_days'])){
			$errors[] = 'Display Again After __ Days&rsquo; value must be greater than 0.';
		}

		if(!$errors){
			
			//Clean page name
			$pagename = clean_url($_POST['title']);
			
			//Reset cropsize according to type
			if($_POST['type'] == 'popup' || $_POST['type'] == 'corner'){
				$CMSUploader->set_crop_type('leadin-'.$_POST['type'], $imagedir);
		
				//Delete images
				if(isset($_POST['deleteimage'])){
					$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
				}
				
				//Upload images
				try{
					$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []); 
				}catch(Exception $e){
					$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
				}
			}

			//Save in database
			$db->new_transaction();
			$db_errors = false;

			$params = [
				
				//Insert
				(ITEM_ID != '' ? ITEM_ID : NULL), 
				$_POST['title'], 
				$_POST['content'], 
				$_POST['type'], 
				$_POST['position'], 
				$_POST['delay'], 
				$_POST['delay_type'], 
				$_POST['show_again_days'], 
				$_POST['show_mobile'], 
				$_POST['theme'], 
				$images['image'] ?? NULL, 
				$_POST['image_alt'], 
				$_POST['url'], 
				$_POST['url_target'], 
				$_POST['url_text'], 
				$_POST['button_animation'], 
				($_POST['button_style'] ?? NULL),
				$_POST['form_title'],
				$_POST['form_content'], 
				$_POST['form_button_text'], 
				$_POST['form_success_content'], 
				($_POST['form_recipient'] ?: NULL),
				$_POST['showhide'], 
				date('Y-m-d H:i:s'),
				
				//Update
				$_POST['title'], 
				$_POST['content'], 
				$_POST['type'], 
				$_POST['position'], 
				$_POST['delay'], 
				$_POST['delay_type'], 
				$_POST['show_again_days'], 
				$_POST['show_mobile'], 
				$_POST['theme'], 
				$images['image'] ?? NULL, 
				$_POST['image_alt'], 
				$_POST['url'], 
				$_POST['url_target'], 
				$_POST['url_text'], 
				$_POST['button_animation'], 
				($_POST['button_style'] ?? NULL),
				$_POST['form_title'],
				$_POST['form_content'], 
				$_POST['form_button_text'], 
				$_POST['form_success_content'], 
				($_POST['form_recipient'] ?: NULL),
				$_POST['showhide'], 
				date('Y-m-d H:i:s')
			];
			$db->query("INSERT INTO `".$record_db."` (`".$record_id."`, `title`, `content`, `type`, `position`, `delay`, `delay_type`, `show_again_days`, `show_mobile`, `theme`, `image`, `image_alt`, `url`, `url_target`, `url_text`, `button_animation`, `button_style`, `form_title`, `form_content`, `form_button_text`, `form_success_content`, `form_recipient`, `showhide`, `date_added`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `title` = ?, `content` = ?, `type` = ?, `position` = ?, `delay` = ?, `delay_type` = ?, `show_again_days` = ?, `show_mobile` = ?, `theme` = ?, `image` = ?, `image_alt` = ?, `url` = ?, `url_target` = ?, `url_text` = ?, `button_animation` = ?, `button_style` = ?, `form_title` = ?, `form_content` = ?, `form_button_text` = ?, `form_success_content` = ?, `form_recipient` = ?, `showhide` = ?, `date_added` = ?", $params);
			if(!$db->error()) {
				$item_id = (ITEM_ID != '' ? ITEM_ID : $db->insert_id());

				//Grab fields
				$form_fields = $_POST['form_fields'] ?? [];

				//Delete non-selected fields
				$field_ids   = array_filter(array_column($form_fields, 'field_id'));
				$params      = array_merge([$item_id], $field_ids);
				$filter      = implode(",", array_fill_keys($field_ids, "?"));
				$db->query("DELETE FROM `pages_leadins_fields` WHERE `$record_id` = ?".($filter ? " AND field_id NOT IN ($filter)" : ""), $params);

				//Insert/Update new fields
				foreach($form_fields as $ordering => $field){
					$params = [
						$field['field_id'] ?? NULL ?: NULL,
						$item_id,
						$field['type'],
						$field['label'],
						$field['options'],
						isset($field['required']),
						$ordering+1,
						date('Y-m-d H:i:s'),

						$field['type'],
						$field['label'],
						$field['options'],
						isset($field['required']),
						$ordering+1,
						date('Y-m-d H:i:s')
					];
					$db->query("INSERT INTO `pages_leadins_fields` (`field_id`, `$record_id`, `type`, `label`, `options`, `required`, `ordering`, `date_added`) VALUES (?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `type` = ?, `label` = ?, `options` = ?, `required` = ?, `ordering` = ?, `date_added` = ?", $params);
				}
			}

			//Commit changes
			if(!$db->error()){
				$db->commit(); 
				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}

			//Transaction error
			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
				foreach($_POST AS $key=>$data){
					$row[$key] = $data;
				}
			}

		//Error reporting
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}
		}

		
	//Determine crop type before handling images
	}else{
		$item_id = $_POST['item_id'] ?? false ?: ITEM_ID;
		$type = $records_arr[$item_id]['type'] ?? false;

		//If leadin type exists, set crop type according to leadin type
		if($type){
			if($type == 'popup' || $type == 'corner'){
				$CMSUploader->set_crop_type('leadin-'.$type, $imagedir);
			}
			
			//Handle images
			include('modules/CropImages.php');
		}
	}

}

?>