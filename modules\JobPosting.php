<?php 
if(PAGE_ID == $_sitepages['post-job']['page_id']){

	// ini_set('display_errors', 1);
	// ini_set('display_startup_errors', 1);
	// error_reporting(E_ALL);
	
	//Define vars
	$panel_id = 52;
	$errors = array();
	$required = array();
	$required_fields = array('title', 'salary', 'duration', 'posted_date', 'closing_date', 'facility_id', 'category_id', 'first_name', 'last_name', 'email', 'phone', 'texteditor');

	$categories = array();
	$facilities = array();
      
	$filedir = "docs/temp/";
	$filetypes = array('pdf');

	$attachments = [];

	$file_fields = [
		'file'
	];

	//Load categories
	$query = $db->query("SELECT * FROM `career_categories` ORDER BY `category_name`");
	if($query && !$db->error()){
		$results = $db->fetch_array();
		foreach ($results as $row) {
			$categories[$row['category_id']] = $row;
		}
		
	}

	//Load facilities
	$query = $db->query("SELECT * FROM `facilities` ORDER BY `facility_name`");
	if($query && !$db->error()){
		$results = $db->fetch_array();
		foreach ($results as $row) {
			$facilities[$row['facility_id']] = $row;
		}
	}
	
	//Load classes
	$classes = array();
	$classes_query = $db->query("SELECT * FROM `membership_classes` WHERE `job_filter` = 1");
	if($classes_query && !$db->error() && $db->num_rows() > 0) {
		$classes = $db->fetch_array();
	}

	//Post job
	if(isset($_POST['post'])){
		// echo '<pre>';
		// print_r($_POST);
		// exit;
		//XSS cookie validation
		if(empty($_POST['xid']) || $_POST['xid'] != $_COOKIE['xid']){
			$errors[] = 'Invalid session. Please ensure cookies are enabled in your browser.';
			unset($_POST);		
			
		}else{
			
			//Validation
			foreach($required_fields as $field){
				if(!isset($_POST[$field]) || !trim(strip_tags($_POST[$field]))){
					$errors[0] = 'Please fill out all the required fields.';
					$required[] = $field;
				}
				if($field == 'phone' && !detectPhone($_POST[$field])){
					$errors[] = 'Please enter a valid phone number.';
					$required[] = $field;
				}
				if($field == 'email' && !checkMail($_POST[$field])){
					$errors[] = 'Please enter a valid email.';
					$required[] = $field;
				}
				if($field == 'texteditor' && !trim(strip_tags(str_replace('&nbsp;', ' ', $_POST['texteditor'])))){
					$errors[] = 'Please enter a job description.';
				}
			}
			if(strtotime($_POST['posted_date']) > strtotime($_POST['closing_date'])){
				$errors[] = 'Invalid closing date. Please enter a closing date after the posted date.';
				$required[] = 'closing_date';
				$required[] = 'posted_date';
			}
			if($_FILES['file']['name'] != ''){
				$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
				
				if($_FILES['file']['error'] == 1){
					$errors[] = 'Unable to upload file. Please try again.';
					$required[] = 'file';
				}else if($_FILES['file']['size'] > 2097152){
					$errors[] = 'Attached file is too large. File size cannot exceed 2MB.';
					$required[] = 'file';
				}else if($ext != 'pdf'){
					$errors[] = 'Invalid file type. Only PDF Documents are accepted.';
					$required[] = 'file';
				}
			}

			//Public User Recaptcha
			if(!USER_LOGGED_IN){
				// require_once("includes/plugins/recaptcha/recaptchalib.php");
				// $recaptcha_response = NULL;
				// $reCaptcha = new ReCaptcha($global['recaptcha_secret']);
				// $recaptcha_response = $reCaptcha->verifyResponse(
				// 	$_SERVER["REMOTE_ADDR"],
				// 	$_POST["g-recaptcha-response"]
				// );
				// if($recaptcha_response != NULL && $recaptcha_response->success){
				// 	//recaptcha validated
				// }else{
				// 	$errors[] = 'Please verify you are not a robot.';
				// 	$required[] = 'recaptcha';
				// }
				if(validate_recaptcha()) {
					$errors['errors'][] = 'Please verify you are not a robot.';
				}
			}

			//Valid submission
			if(empty($errors)){
				
				//Upload file
				// $filedir = 'docs/temp/';
				$filename = ($_FILES['file']['name'] != '' ? clean_url($_POST['title']).'-'.date("ymdhis").'.'.$ext : NULL);
				
				foreach ($file_fields as $field) {
						$$field = NULL;
					
						if (!empty($_FILES[$field]['name'])) {
							// Check for errors during upload
							if ($_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
								$errors[] = "Error uploading file: " . $_FILES[$field]['name'] . " - " . $_FILES[$field]['error'];
								continue; // Skip this file and continue with the next
							}
					
							// Attempt to copy the file
							if (!@copy($_FILES[$field]['tmp_name'], $filedir . $filename)) {
								$errors[] = "Failed to upload the file: " . $_FILES[$field]['name'];
							} else {
								// Validate file exists
								$$field = check_file($filename, $filedir) ?: NULL;
					
								// Add to array for email attachment
								if ($$field) {
									$attachments[] = $filedir . $filename;
								}
							}
						}
						
				}
				// echo $filename ;
				// exit;
				if(is_null($filename) || file_exists($filedir.$filename)){
					
					//Insert data
					$params = array(
						$_POST['title'], 
						clean_url($_POST['title']),
						$_POST['category_id'], 
						$_POST['facility_id'], 
						$_POST['texteditor'], 
						$_POST['salary'],
						$_POST['duration'],
						$filename,
						USER_LOGGED_IN ?: NULL,
						$_POST['first_name'],
						$_POST['last_name'],
						$_POST['email'],
						formatPhoneNumber($_POST['phone']),
						date('Y-m-d', strtotime($_POST['posted_date'])),
						date('Y-m-d', strtotime($_POST['closing_date'])),
						(isset($_POST['public']) ? $_POST['public'] : 0),
						0,
						0,
						date('Y-m-d H:i:s'),
						date('Y-m-d H:i:s')
					);
					$query = $db->query("INSERT INTO `careers`(`title`, `page`, `category_id`, `facility_id`, `content`, `salary`, `duration`, `file_name`, `account_id`, `first_name`, `last_name`, `email`, `phone`, `posted_date`, `closing_date`, `public`, `showhide`, `approved`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
					$item_id = $db->insert_id();

					//Save class
					$delete = $db->query("DELETE FROM `career_classes` WHERE `career_id` = ?", array($item_id));
					if(isset($_POST['class_id']) && !empty($_POST['class_id'])){
						foreach($_POST['class_id'] as $class_id){
							$query = $db->query("INSERT INTO `career_classes`(`career_id`, `class_id`) VALUES(?,?) ON DUPLICATE KEY UPDATE `class_id` = ?", array($item_id, $class_id, $class_id));
						}
					}

					if(!$db->error()){
						
						//Format email
						$message = '<h3>New Job Submission</h3>
						<p>The following job posting requires admin approval.</p><hr>
						<h6>' .$_POST['title']. '</h6><br>
						<p><b>Posted by:</b> ' .$_POST['first_name'].' '.$_POST['last_name']. '<br>
						<b>Email:</b> <a href="mailto:'.$_POST['email'].'">' .$_POST['email']. '</a><br>
						<b>Phone:</b> <a href="tel://'.formatPhoneNumber($_POST['phone']).'">' .formatPhoneNumber($_POST['phone']). '</a></p>
						<p><b>Posting Date:</b> ' .date('F j, Y', strtotime($_POST['posted_date'])). '<br>
						<b>Closing Date:</b> ' .date('F j, Y', strtotime($_POST['closing_date'])). '<br>
						<b>Monthly Salary:</b> '.$_POST['salary'].'<br>
						<b>Duration:</b> '.$_POST['duration'].'</p>
						<hr>
						'.$_POST['texteditor'];
												
						//Send to admin
						send_email(($global['email_jobsubmissions'] ?: $global['contact_email']), 'Job Submissions', $message);
						
						$success = $Account->alert('Your job posting has been submitted for approval.', true);

						unset($_POST);

					}else{
						$alert = $Account->alert('Unable to post job. '.$db->error(), false);
					}
					
				//Upload error
				}else{
					$errors[] = 'Unable to upload file. Please try again.';
					$required[] = 'file';
				}

			}else{
				$alert = $Account->alert(implode('<br/>', $errors), false);
			}
		}

	}
}

?>