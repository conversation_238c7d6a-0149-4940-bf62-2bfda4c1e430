<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Set row data
if(isset($_SESSION['cms']['reg'])){
	$row = $_SESSION['cms']['reg'];
}

//Alerts
if(!empty($important)){
	echo $CMSBuilder->important(implode('<br />', $important));
}

//Registration form
if(!isset($_POST['confirm']) || $errors){
			
	echo "<form id='register-form' action='" .PAGE_URL. "?action=add&event=" .$occurrence_id. "' method='post' enctype='multipart/form-data'>";
	
		//Event
		echo "<div class='panel'>";
			echo "<div class='panel-header'>".EVENT_NAME." Information 
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
					echo "<tr>
						<td width='150px'>" .EVENT_NAME. " Name:</td>
						<td>" .$event['event_name']. "</td>
					</tr>
					<tr>
						<td>" .EVENT_NAME. " Date:</td>
						<td>" .format_date_range($event['start_date'], $event['end_date']). "</td>
					</tr>
					<tr>
						<td>Location:</td>
						<td>" .(!empty($event['location_name']) ? $event['location_name'] : $event['facility_name']). "</td>
					</tr>
					<tr>
						<td>Payment Deadline:</td>
						<td>" .date("F j, Y @g:iA", strtotime($event['payment_deadline'].' '.$reg_settings['reg_close_time'])). "</td>
					</tr>
					<tr>
						<td>Registration Deadline:</td>
						<td>" .date("F j, Y @g:iA", strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time'])). "</td>
					</tr>";
				echo "</table>";
			echo "</div>";
		echo "</div>";
	
		//Attendee
		echo "<div class='panel'>";
			echo "<div class='panel-header'>" .(EVENT_TYPE == 2 ? "Attendee" : "Contact"). " Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field ui-front'>
					<label>Account Search</label>
					<input type='text' name='term' value='' class='account_suggest input' />
				</div>";
				echo "<hr class='clear' />";
				echo "<div class='form-field'>
					<label>First Name <span class='required'>*</span></label>
					<input type='text' name='first_name' value='" .(isset($row['first_name']) ? $row['first_name'] : ''). "' class='input first_name" .(in_array('first_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Last Name <span class='required'>*</span></label>
					<input type='text' name='last_name' value='" .(isset($row['last_name']) ? $row['last_name'] : ''). "' class='input last_name" .(in_array('last_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Email Address <span class='required'>*</span>" .$CMSBuilder->tooltip('Email Adress', 'Confirmation email will be sent to this email address on registration.'). "</label>
					<input type='text' name='email' value='" .(isset($row['email']) ? $row['email'] : ''). "' class='input email" .(in_array('email', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Phone Number <span class='required'>*</span></label>
					<input type='text' name='phone' value='" .(isset($row['phone']) ? $row['phone'] : ''). "' class='input phone" .(in_array('phone', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Account No.</label>
					<input type='text' name='account_id' class='input number account_id' value='" .(isset($row['account_id']) ? $row['account_id'] : '') ."' />
				</div>";	
				
				//Mailing Address
				if(EVENT_TYPE != 2){
					echo "<div class='clear'>";
					
						echo "<div class='form-field'>
							<label>Company Name</label>
							<input type='text' name='company' value='" .(isset($row['company']) ? $row['company'] : ''). "' class='input company" .(in_array('company', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>Facility Name</label>
							<input type='text' name='facility' value='" .(isset($row['facility']) ? $row['facility'] : ''). "' class='input facility" .(in_array('facility', $required) ? ' required' : ''). "' />
						</div>";
					
						echo "<hr class='clear' />";
					
						echo "<div class='form-field'>
							<label>Mailing Address</label>
							<input type='text' name='address1' value='" .(isset($row['address1']) ? $row['address1'] : ''). "' class='input address1" .(in_array('address1', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>Unit No.</label>
							<input type='text' name='address2' value='" .(isset($row['address2']) ? $row['address2'] : ''). "' class='input address2" .(in_array('address2', $required) ? ' required' : ''). "' />
						</div>";

						echo "<br class='clear' />";

						echo "<div class='form-field'>
							<label>City/Town</label>
							<input type='text' name='city' value='" .(isset($row['city']) ? $row['city'] : ''). "' class='input city" .(in_array('city', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>Province</label>
							<select name='province' class='select province" .(in_array('province', $required) ? ' required' : ''). "'>";
								echo "<option value=''>- Select -</option>";
								for($p=1; $p<=count($provinces); $p++){
									echo "<option value='" .$provinces[$p][1]. "'" .((isset($row['province']) && $row['province'] == $provinces[$p][1]) ? " selected" : ""). ">" .$provinces[$p][0]. "</option>";	
								}
							echo "</select>
						</div>";
						echo "<div class='form-field'>
							<label>Postal Code</label>
							<input type='text' name='postal_code' value='" .(isset($row['postal_code']) ? $row['postal_code'] : ''). "' class='input postal_code" .(in_array('postal_code', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
						<label>Country</label>
							<select name='country' class='select country'>
								<option value='CA'" .(isset($row['country']) && $row['country'] == 'CA' ? " selected" : ""). ">Canada</option>
							</select>
						</div>";
					
					echo "</div>";
					
				}
	
			echo "</div>";
		echo "</div>";
		
		//Partner
		if(EVENT_TYPE == 2 && $event['team_event']){
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Partner Information
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";

				echo "<div class='panel-content clearfix'>";

					$eligible_attendees = $Registration->get_eligible_attendees($occurrence_id);
					echo "<div class='form-field'>
						<label>Select Partner (Eligible Member)</label>
						<select name='partner_id' class='select" .(in_array('partner_id', $required) ? " required" : ""). "'>
							<option value=''>- Select -</option>";
							foreach($eligible_attendees as $eligible){
								if(empty($eligible['facility_name'])){
									$eligible['facility_name'] = "No Facility";
								}
								echo "<option value='" .$eligible['account_id']."'" .(isset($row['partner']['account_id']) && $row['partner']['account_id'] == $eligible['account_id'] ? " selected" : ""). ">" .$eligible['last_name'].", ".$eligible['first_name']. " - ".$eligible['facility_name']. "</option>";
							}
						echo "</select>
					</div>
					<div class='form-field'>
						<label><strong>OR</strong> Enter Partner Name (Non-Member)</label>
						<input type='text' name='partner_first_name' class='input' value='" .(isset($row['partner']['first_name']) && empty($row['partner']['account_id']) ? $row['partner']['first_name'] : ""). "' placeholder='First Name' />
					</div>
					<div class='form-field'>
						<label>&nbsp;</label>
						<input type='text' name='partner_last_name' class='input' value='" .(isset($row['partner']['last_name']) && empty($row['partner']['account_id']) ? $row['partner']['last_name'] : ""). "' placeholder='Last Name' />
					</div>
					<div class='form-field'>
						<label>Partner Handicap</label>
						<input type='text' name='partner_handicap' class='input' value='" .(isset($row['partner']['handicap']) ? $row['partner']['handicap'] : ""). "' />
					</div>";

				echo "</div>";
			echo "</div>";
		}
	
		//Event attendees
		if(EVENT_TYPE != 2){
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Attendee Information <small>(" .(!is_null($event['spots_available']) ? $event['spots_available'] : "Unlimited"). " Spots Available)</small>
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";

				echo "<div class='panel-content nopadding'>";
					echo "<div id='reg-attendees' class='rows-wrapper'>";	
						echo "<div class='row'><br /><p>Fill out the information below for each individual attending this event.</p></div>";		
			
						//Attendee
						foreach($row['attendees'] as $attendee){
							echo "<div class='row reg-attendee clearfix'>";

								//Select ticket
								echo "<div class='form-field'>
									<label>Ticket Type <span class='required'>*</span></label>
									<select name='ticket_id[]' class='select" .(in_array('ticket_id_'.$key, $required) ? ' required' : ''). "'>
										<option value=''>- Select -</option>";
										foreach($event['pricing'] as $price){
											echo "<option value='" .$price['pricing_id']. "'" .((!empty($attendee['ticket_type']) && $attendee['ticket_type'] == $price['price_type']) && (!empty($attendee['ticket_price']) && $attendee['ticket_price'] == $price['price']) ? ' selected' : ''). ">" .$price['price_type']." - $".number_format($price['price'], 2). "</option>";
										}
									echo "</select>
								</div>";

								//Form fields
								foreach($event['attendee_information'] as $field=>$optional){
									$label = ucwords(str_replace('_', ' ', $field));
									echo "<div class='form-field'>
										<label>" .$label. ($optional == 'Required' ? ' <span class="required">*</span>' : '')."</label>
										<input type='text' name='attendee_" .$field. "[]' class='input" .(in_array($field.'_'.$key, $required) ? ' required' : ''). "' value='" .(isset($attendee['attendee_fields'][$field]) ? $attendee['attendee_fields'][$field] : ''). "' />
									</div>";
								}

								//Addons
								if(!empty($event['addons'])){
									foreach($event['addons'] as $addon){
										echo "<div class='form-field'>
											<label>" .$addon['name']. ($addon['required'] == 'Required' ? ' <span class="required">*</span>' : '')."</label>
											<select name='addon_" .$addon['addon_id']. "[]' class='select" .(in_array('addon_'.$addon['addon_id'].'_'.$key, $required) ? ' required' : ''). "'>
												<option value=''>- Select -</option>";
												foreach($addon['options'] as $option){
													echo "<option value='" .$option['option_id']. "'" .(isset($attendee['addons'][$addon['addon_id']]['option_id']) && $attendee['addons'][$addon['addon_id']]['option_id'] == $option['option_id'] ? ' selected' : ''). ">" .$option['name'].($option['price_adjustment'] > 0 ? ' - $'.number_format($option['price_adjustment'], 2) : ''). "</option>";	
												}
											echo "</select>
										</div>";
									}
								}
								echo "<input type='hidden' name='attendee_account_id[]' value='" .$attendee['account_id']. "' />";
							echo "</div>";
						}
			
						echo "<a id='add-attendee-btn' class='add-row-btn button-sm' href='#' onclick='addNewAttendee(); return false;'>+ Add Attendee</a>";
			
				echo "</div>";
			echo "</div>";
		}
	
		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			echo "<button type='submit' class='button f_right' name='confirm' value='1'><i class='fa fa-arrow-right'></i>Continue</button>";
			echo "<a href='".$sitemap[EVENT_SECTION]['page_url']."?action=edit&item_id=" .EVENT_ID. "&step=1' class='cancel'>Cancel</a>";
		echo "</footer>";
	
	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "</form>";
	

//Confirm	
}else{
	
	echo "<form id='register-form' action='' method='post' enctype='multipart/form-data'>";
		
		//Contact
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Contact Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";

			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table width='100%' cellpadding='0' cellspacing='0'>";
					echo "<tr>
						<td width='150px'>Name:</td>
						<td>" .$row['first_name']." ".$row['last_name']. "</td>
					</tr>
					<tr>
						<td>Email Address:</td>
						<td>" .$row['email']. "</td>
					</tr>
					<tr>
						<td>Phone No:</td>
						<td>" .$row['phone']. "</td>
					</tr>";
					if(!empty($row['account_id'])){
						echo "<tr>
							<td>Account No:</td>
							<td>" .$row['account_id']. "</td>
						</tr>";
					}
					if(!empty($row['company'])){
						echo "<tr>
							<td>Company:</td>
							<td>" .$row['company']. "</td>
						</tr>";
					}
					if(!empty($row['facility'])){
						echo "<tr>
							<td>Facility:</td>
							<td>" .$row['facility']. "</td>
						</tr>";
					}
					if(trim($row['address1']) != ''){
						echo "<tr>
							<td valign='top'>Mailing Address:</td>
							<td>".(trim($row['address2']) != '' ? $row['address2'].' - ' : '').$row['address1']."<br/>".$row['city'].", ".$row['province'].", ".$row['country']."<br/>".$row['postal_code']."</td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>";

		//Events
		echo "<div class='panel'>";
			echo "<div class='panel-header'>".$event['event_name']." (".format_date_range($event['start_date'], $event['end_date']).")  
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";

			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table width='100%' cellpadding='0' cellspacing='0'>";
					echo "<thead>";
						echo "<tr>";
							echo "<th width='350px' class='{sorter:false}'>Attendee</th>";
							echo "<th class='{sorter:false}'>Extras</th>";
							echo "<th width='150px' class='{sorter:false}'></th>";
							echo "<th width='150px' class='{sorter:false} right'>Total</th>";
						echo "</tr>";
					echo "</thead>";

					echo "<tbody>";
						foreach($row['attendees'] as $key=>$attendee){
							echo "<tr>";
								echo "<td class='v_top'>";
									if($event['event_type'] == 2){
										echo $attendee['first_name']." ".$attendee['last_name'];
										echo (trim($attendee['email']) != '' ? "<small class='clear'>Email: ".$attendee['email']."</small>" : "");
										echo (trim($attendee['phone']) != '' ? "<small class='clear'>Phone: ".$attendee['phone']."</small>" : "");
									}else{
										echo $attendee['attendee_fields']['first_name']." ".$attendee['attendee_fields']['last_name'];
										foreach($attendee['attendee_fields'] as $field=>$value){
											if($field != 'first_name' && $field != 'last_name'){
												echo (trim($value) != '' ? "<small class='clear'>" .ucwords(str_replace('_', ' ', $field)). ": ".$value."</small>" : "");
											}
										}
									}
								echo "</td>";
								echo "<td class='v_top'>";
									$addons_sum = 0;
									if(!empty($attendee['addons'])){
										foreach($attendee['addons'] as $att_addon){
											if($att_addon['value'] != ""){
												echo $att_addon['name']."<br /><small>".$att_addon['value'];
												if($att_addon['price_adjustment'] != ""){
													echo " - $".number_format($att_addon['price_adjustment'], 2);
													$addons_sum += $att_addon['price_adjustment'];
												}
												echo "</small><br/>";
											}
										}
									}else{
										echo "None";
									} 
									echo "<input type='hidden' name='addons_".$key."' class='addons_sum' value='" .$addons_sum. "' />";
								echo "</td>";
								echo "<td class='right'><strong>".$attendee['ticket_type'].":</strong></td>";
								echo "<td class='right'>$<input type='text' name='ticket_price[]' class='input input_sm nomargin number sum" .(in_array('ticket_price_'.$key, $required) ? ' required' : ''). "' value='".number_format($attendee['ticket_price'], 2, '.', '')."' /></td>";
							echo "</tr>";
						}
						if(!empty($row['partner'])){
							echo "<tr>";
								echo "<td class='v_top'>";
									echo $row['partner']['first_name']." ".$row['partner']['last_name'];
									echo (trim($row['partner']['email']) != '' ? "<small class='clear'>Email: ".$row['partner']['email']."</small>" : "");
									echo (trim($row['partner']['phone']) != '' ? "<small class='clear'>Phone: ".$row['partner']['phone']."</small>" : "");
								echo "</td>";
								echo "<td class='v_top'>None</td>";
								echo "<td class='v_top right'><strong>".$row['partner']['ticket_type'].":</strong></td>";
								echo "<td class='right v_top'>$".number_format($row['partner']['ticket_price'], 2)."</td>";
							echo "</tr>";
						}
					echo "</tbody>";
				echo "</table>";
			echo "</div>";
		echo "</div>";

		//Totals
		echo "<div id='registration-totals' class='panel'>";
			echo "<div class='panel-header'>Totals 
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table width='100%' cellpadding='0' cellspacing='0'>";
					echo "<tbody>";
						echo "<tr>
							<td class='right'><strong>Subtotal:</strong></td>
							<td class='right' id='subtotal'>$".number_format($row['registration_total']-$row['taxes']-$row['fees'],2)."</td>
						</tr>";
						echo "<tr>
							<td class='right'><strong>Taxes:</strong></td>
							<td class='right' id='taxes'>$".number_format($row['taxes'],2)."</td>
						</tr>";
						if(EVENT_TYPE == 2){
							echo "<tr>
								<td class='right'><strong>Skins:</strong></td>
								<td class='right'>$<input type='text' name='fees' id='fees' class='input input_sm number nomargin' value='" .number_format($row['fees'],2)."' /></td>
							</tr>";	
						}
						echo "<tr id='ordertotal'>
							<td class='right'><strong>Total:</strong></td>
							<td class='right' width='150px'><strong id='total'>$".number_format($row['registration_total'],2)."</strong></td>
						</tr>";
					echo "</tbody>";
				echo "</table>";
			echo "</div>";
		echo "</div>"; 
	
		//Notes
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Notes ".$CMSBuilder->tooltip('Notes', 'Notes are for internal use only. They will not appear on the front-end of the website.')."
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<textarea name='notes' class='textarea'>".(isset($_SESSION['cms']['reg']['notes']) ? $_SESSION['cms']['reg']['notes'] : "")."</textarea>";
			echo "</div>";
		echo "</div>"; //Notes
	
		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Submit Registration</button>";
			echo "<a href='' class='cancel'>Go Back</a>";
		echo "</footer>";
	
	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "</form>";

?>
<script type="text/javascript">
	function numberWithCommas(n){
		var parts = n.toString().split(".");
		return parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",") + (parts[1] ? "." + parts[1] : "");
	}
	
	$('input.sum, input#fees').on('input', function(){
		var tax_rate_percent = <?php echo (($taxrates['gst_rate']+$taxrates['pst_rate'])/100); ?>;
		var fees =  parseFloat($('input#fees').length && $('input#fees').val() != '' ? $('input#fees').val() : 0);
		
		var subtotal = 0;
		$('input.sum').each(function(){
			var this_sum = parseFloat($(this).val() != '' ? $(this).val() : 0);
			subtotal += this_sum;
		});
		$('input.addons_sum').each(function(){
			var this_sum = parseFloat($(this).val() != '' ? $(this).val() : 0);
			subtotal += this_sum;
		});
		
		var taxes = subtotal*tax_rate_percent;
		var total = subtotal+taxes+fees;
						 
		subtotal = subtotal.toFixed(2);
		taxes = taxes.toFixed(2);
		total = total.toFixed(2);

		$('#subtotal').html('$'+numberWithCommas(subtotal));		
		$('#taxes').html('$'+numberWithCommas(taxes));		
		$('#total').html('$'+numberWithCommas(total));			 
						 
	});
</script>
<?php } ?>