<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']) {
	$total_records = 0;
	$get_total = $db->query("SELECT COUNT(*) AS `total` FROM `reg_promo_codes` WHERE `account_id` IS NULL");
	if($get_total  && !$db->error() && $db->num_rows() > 0) {
		$total_records = $db->fetch_array();
		$total_records = $total_records[0]['total'];
	}	
	$CMSBuilder->set_widget(93, 'Total Promo Codes', $total_records);
}

if(SECTION_ID == $_cmssections['registration-discounts']) {
	//Define vars
	$record_db = 'reg_promo_codes';
	$record_id = 'promo_id';
	$record_name = 'Promo Code';

	$errors = false;
	$required = array();
	
	//Get events
	$events = array();
	$params = array('Trashed', 1, date("Y")."-01-01");
	$query = $db->query("SELECT `reg_events`.`name`, `reg_events`.`event_id`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date` FROM `reg_events` ".
	"LEFT JOIN `reg_occurrences` ON `reg_events`.`event_id` = `reg_occurrences`.`event_id` ". 
	"WHERE `reg_events`.`status` != ? AND `reg_events`.`event_type` = ? && `reg_occurrences`.`end_date` >= ? ".
	"ORDER BY `reg_events`.`name` ASC", $params);
	if($query && !$db->error()){
		$events = $db->fetch_array();
	}
	
	//Get Records
	$records_arr = array();
	$params = array();

	if($searchterm != ""){
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
	}
	$query = $db->query("SELECT `$record_db`.*, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `reg_events`.`name` AS `event_name` FROM `$record_db` ".
	"LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` ".
	"LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `$record_db`.`event_id` ".
	($searchterm != "" ? "WHERE `code` LIKE ? || `reg_events`.`name` LIKE ? || CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ? " : ""). 
	"ORDER BY `code`, `$record_id`", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$row['term'] = (!empty($row['account_id']) ? $row['first_name']." ".$row['last_name']." (No. " .$row['account_id']. ")" : "");
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		$delete = $db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	
	//Save item
	} else if(isset($_POST['save'])){
		
		//Set defaults
		if(ITEM_ID != ""){
			$_POST['code'] = $records_arr[ITEM_ID]['code'];
		}
		if(isset($_POST['unlimited_count']) || trim($_POST['promo_max_count']) == '') {
			$_POST['promo_max_count'] = -1;
		}
		if(isset($_POST['no_expiry'])) {
			$_POST['expiry'] = '2999-01-01 00:00:00';
		}

		//Validate
		if(trim($_POST['code']) == '') {
			$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
			array_push($required, 'code');
		}
		if(trim($_POST['begin_date']) == '') {
			$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
			array_push($required, 'begin_date');
		}
		if(trim($_POST['expiry']) == '') {
			$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
			array_push($required, 'expiry');
		}
		if(strtotime($_POST['begin_date']) > strtotime($_POST['expiry'])) {
			$errors[] = 'Start date cannot be greater than expiry date.';
		}
		if((trim($_POST['discount']) == '' || !is_numeric($_POST['discount']) || $_POST['discount'] <= 0)) {
			$errors[] = 'You must enter a discount percentage or dollar amount.';
			array_push($required, 'discount');
		}
		if(!empty($_POST['account_id']) && !strstr($_POST['term'], 'No. '.$_POST['account_id'])){
			$_POST['account_id'] = NULL;
		}

		if(!$errors){
			
			//Insert to db
			$params = array(
				ITEM_ID, 
				$_POST['code'], 
				$_POST['discount'], 
				$_POST['discount_type'], 
				$_POST['promo_max_count'], 
				$_POST['begin_date'], 
				$_POST['expiry'], 
				(!empty($_POST['account_id']) ? $_POST['account_id'] : NULL),
				(!empty($_POST['event_id']) ? $_POST['event_id'] : NULL),
				date('Y-m-d H:i:s'),
				date('Y-m-d H:i:s'),
				USER_LOGGED_IN,
				
				$_POST['discount'], 
				$_POST['discount_type'], 
				$_POST['promo_max_count'], 
				$_POST['begin_date'], 
				$_POST['expiry'], 
				(!empty($_POST['account_id']) ? $_POST['account_id'] : NULL),
				(!empty($_POST['event_id']) ? $_POST['event_id'] : NULL),
				date('Y-m-d H:i:s'),
				USER_LOGGED_IN
			);
			$insert = $db->query("INSERT INTO `$record_db` (`$record_id`, `code`, `discount`, `discount_type`, `promo_max_count`, `begin_date`, `expiry`, `account_id`, `event_id`, `date_added`, `last_updated`, `updated_by`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `discount` = ?, `discount_type` = ?, `promo_max_count` = ?, `begin_date` = ?, `expiry` = ?, `account_id` = ?, `event_id` = ?, `last_updated` = ?, `updated_by` = ?", $params);
			if($insert && !$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
				foreach($_POST AS $key=>$data){
					$row[$key] = $data;
				}	
			}

		} else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}
	}
}

?>