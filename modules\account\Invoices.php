<?php
//Display user invoices
if(PAGE_ID == $_sitepages['invoices']['page_id']){


	//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
		exit();
	}
	
	//Define vars
	$panel_id = 70;
	$invoices = array();
	$invoice = array();
	$params = array();

	//Get selected invoice
	if(ACTION == 'edit' && ITEM_ID != ''){

		$query = $db->query("SELECT `invoices`.*, `hio`.`event_name`, `hio`.`hio_id`, `hio`.`field`, `hio`.`comments` AS `hio_comments` FROM `invoices` ".
		"LEFT JOIN `hio` ON `hio`.`invoice_id` = `invoices`.`invoice_id` ".
		"WHERE `invoices`.`status` = ? && `invoices`.`account_id` = ? && `invoices`.`invoice_id` = ?",
		array('Active', USER_LOGGED_IN, ITEM_ID));
		if(!$query || $db->error() || !$db->num_rows()){
			$notfound = true;
			$errors[] = 'Invoice not found. Please select from the list below.';
		}else{
			$result = $db->fetch_array();
			$invoice = $result[0];
			$invoice['comments'] .= $invoice['hio_comments'];

			//Get payments
			$invoice['total_paid'] = 0;
			$invoice['payments'] = array();
			$get_payments = $db->query("SELECT * FROM `payments` WHERE `invoice_id` = ? ORDER BY `payment_date` DESC", array(ITEM_ID));
			if($get_payments && !$db->error() && $db->num_rows() > 0){
				$payment_result = $db->fetch_array();
				foreach($payment_result as $payment){
					if($payment['status'] == 1){
						$invoice['total_paid'] += $payment['amount'];
					}
					$invoice['payments'][] = $payment;
				}
			}

			//Get refunds
			$invoice['total_refunded'] = 0;
			$invoice['refunds'] = array();
			$get_refunds = $db->query("SELECT `refunds`.*, `payments`.`payment_number`, `payments`.`cctype`, `payments`.`ccnumber`, `payments`.`ccexpiry` FROM `refunds` LEFT JOIN `payments` ON `refunds`.`payment_id` = `payments`.`payment_id` WHERE `refunds`.`invoice_id` = ? ORDER BY `refund_date` DESC", array(ITEM_ID));
			if($get_refunds && !$db->error() && $db->num_rows() > 0){
				$refund_result = $db->fetch_array();
				foreach($refund_result as $refund){
					if($refund['status'] == 1){
						$invoice['total_refunded'] += $refund['amount'];
					}

					$invoice['refunds'][] = $refund;
				}
			}

			//Get hio
			$invoice['hio'] = array();
			if(!empty($invoice['hio_id'])){
				$invoice['hio']['event_name'] = $invoice['event_name'];
				$invoice['hio']['field'] = $invoice['field'];

				//Get dates
				$invoice['hio']['event_dates'] = array();
				$query = $db->query("SELECT * FROM `hio_dates` WHERE `hio_id` = ? ORDER BY `event_date` ASC", array($invoice['hio_id']));
				if($query && !$db->error()){
					$result = $db->fetch_array();
					foreach($result as $date){
						$invoice['hio']['event_dates'][] = date('F j, Y', strtotime($date['event_date']));
					}
				}

				//Get courses
				$invoice['hio']['courses'] = array();
				$query = $db->query("SELECT * FROM `hio_courses` WHERE `hio_id` = ?", array($invoice['hio_id']));
				if($query && !$db->error()){
					$result = $db->fetch_array();
					foreach($result as $course){

						//Get holes
						$course['holes'] = array();
						$query = $db->query("SELECT * FROM `hio_holes` WHERE `course_id` = ?", array($course['course_id']));
						if($query && !$db->error()){
							$course['holes'] = $db->fetch_array();
						}

						$invoice['hio']['courses'][] = $course;
					}
				}
			}

			//Download invoice
			if(isset($_GET['download'])){

				//Generate document
				$pdf = $Account->generate_invoice($invoice);
				require_once("includes/plugins/mpdf60/mpdf.php");
				if(class_exists('mPDF')){
					$filename = 'Invoice-'.$invoice['invoice_number'].'.pdf';
					$mpdf = new mPDF('utf-8',array(216,279.4),8,'Arial',20,20,16,16,5,7,'P');
					$mpdf->SetDisplayMode('fullpage');
					$mpdf->list_indent_first_level = 0;
					$mpdf->WriteHTML($pdf, 2);
					$mpdf->Output($filename,'D');
				}

			}

		}
	}

	//Get all invoices
	if(ACTION == '' || (ACTION == 'edit' && empty($invoice))){

		//Pagination
		$pg = (isset($_GET['pg']) ? $_GET['pg'] : 1);
		$limit = 20;
		$totalresults = 0;

		//Invoices
		$params = array('Active', USER_LOGGED_IN);
		if(isset($_GET['search']) && trim($_GET['search']) != ''){
			$params[] = '%'.$_GET['search'].'%';
			$params[] = '%'.str_replace('$', '', $_GET['search']).'%';
		}
		$query = $db->query("SELECT * FROM `invoices` WHERE `status` = ? && `account_id` = ? ".(isset($_GET['search']) && trim($_GET['search']) != '' ? "&& (`invoice_number` LIKE ? || `invoice_total` LIKE ?) " : "")."ORDER BY `invoice_date` DESC", $params);
		if($query && !$db->error()){
			$invoices = $db->fetch_array();

			//Pagination
			$totalresults = count($invoices);
			if($pg != 'all'){
				$start = (($pg-1)*$limit);
				$end = $limit;
			}else{
				$start = 0;
				$end = $totalresults;
			}
			$invoices = array_slice($invoices, $start, $end);
		}

	}

}

?>