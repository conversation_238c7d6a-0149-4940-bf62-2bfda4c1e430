<?php  

if(SECTION_ID == $_cmssections['inquiries'] && USER_LOGGED_IN){

	//Export columns
	$export_columns = [
		'inquiry' =>'Inquiry Type',
		'timestamp' => 'Request Date',
		'name' => 'Name',
		'email' => 'Email Address',
		'phone' => 'Phone Number',
		'company' => 'Company Name',
		'subject' => 'Subject',
		'message' => 'Message'
	];

	//Export records
	$csv_rows = [];
	foreach($records_arr as $row){
		$csv_row = [];
		foreach(array_keys($export_columns) as $column){
			if($column == 'status'){
				$row[$column] = ($row[$column] ? 'TRUE' : 'FALSE');
			}

			$field_data = html_entity_decode($row[$column]);
			$csv_row[$column] = $field_data;
		}

		$csv_rows[] = $csv_row;
	}


	/* OUTPUT CSV */
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=inquiries-".date("Ymd").".csv");
	header("Content-Transfer-Encoding: binary ");

	$fp = fopen('php://output', 'w');
	fputcsv($fp, array_values($export_columns));
	foreach($csv_rows as $row) {
		fputcsv($fp, $row);
	}
	fclose($fp);

	exit();

}

?>