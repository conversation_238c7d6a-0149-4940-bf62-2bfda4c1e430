<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['form_fieldsets']){

	//Define vars
	$record_db = 'form_fields';
	$record_id = 'field_id';
	$record_name = 'Fieldset';
	
	$formpage = $CMSBuilder->get_section($_cmssections['forms']);
	define('FORM_ID', (isset($_GET['form_id']) ? $_GET['form_id'] : NULL));
	
	//Validation
	$errors = false;
	$required = array();
	$required_fields = array('label');
	
	//Form not found
	$db->query("SELECT `form_id` FROM `forms` WHERE `form_id` = ?", array(FORM_ID));
	if($db->error() || !$db->num_rows()){
		$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
		header('Location:' .$formpage['page_url']);
		exit();	
	}
	
	//Get record
	if(ACTION == 'edit'){
		
		$params = array(FORM_ID, ITEM_ID, 'fieldset');
		$db->query("SELECT * FROM `$record_db` WHERE `form_id` = ? && `field_id` = ? && `type` = ?", $params);
		if(!$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$row = $result[0];
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .$formpage['page_url']. '?action=edit&item_id=' .FORM_ID);
			exit();
		}
		
	}
	
	//Delete item
	if(isset($_POST['delete'])){
		
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(), false);	
		}
			
		//Send back to form page
		header("Location: " .$formpage['page_url']. "?action=edit&item_id=" .FORM_ID);
		exit();	
	
	//Save item
	}else if(isset($_POST['save'])){
		
		//Validate
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}
	
		if(!$errors){
			$params = [
				'label' => $_POST['label'],
				'description' => $_POST['description'],
				'ordering' => $_POST['ordering'],
				'last_updated' => date('Y-m-d H:i:s')

			];

			$db->insert($record_db, array_merge([
				$record_id => ITEM_ID,
				'form_id' => FORM_ID,
				'type' => 'fieldset',
				'date_added' => date('Y-m-d H:i:s')
			], $params), $params);
			
			if(!$db->error()) {
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .$formpage['page_url']. "?action=edit&item_id=" .FORM_ID);
				exit();

			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);	
		}
	}

}

?>