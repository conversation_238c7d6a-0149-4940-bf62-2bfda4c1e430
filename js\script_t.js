$(function() {
	$('form.dynamic-form1').on('submit', function() {
		var form_id = $(this).attr('id');
		submitForm1(form_id, 'js/ajax/submit-dynamic-form.php', function() {});
		return false;
	});
});

//Form submission
function submitForm1(form_id, ajax_file, callback) {
	
	var this_form = $("#" + form_id);
	var button_text = this_form.find('*[name="submitform"]').html();
	var recaptcha_field = (this_form.data('recaptcha') != undefined && this_form.data('recaptcha') != '' ? this_form.data('recaptcha') : '');
	var validated = true;
	var post_to_ajax = false;
	var errormsg = '';
	var alert_success = (!this_form.hasClass('leadin-form') ? true : false);
	var form_data = (ajax_file ? new FormData(this_form.get(0)) : this_form.serialize());
	
	//Hack for Safari bug where empty values were breaking upload
	if(form_data instanceof FormData && navigator.userAgent.match(/version\/11((\.[0-9]*)*)? .*safari/i)) {
		try {
			eval('for (var pair of form_data.entries()) {\
				if (pair[1] instanceof File && pair[1].name === \'\' && pair[1].size === 0) {\
					form_data.delete(pair[0]);\
				}\
			}');
		} catch(e) {}
	}
	
	//Validate fields
	this_form.find('.jsvalidate').each(function(index, el) {
		if($(el).find('input.radio, input.checkbox').length !== 0){
			var ischecked = false;
			$(el).find('input.radio, input.checkbox').each(function(){
				if($(this).is(':checked')){
					ischecked = true;
				}
			});
			if(!ischecked){
				validated = false;
				$(el).addClass('error');
				errormsg = 'Please fill out all the required fields.<br />';
			}
		}else{
			if($.trim($(el).val()) == '') {
				validated = false;
				$(el).addClass('error');
				$(el).parents('.input-file-container').addClass('error');
				errormsg = 'Please fill out all the required fields.<br />';
			}
		}
	});

	//Validate email fields
	this_form.find('input[type="email"]').each(function(index, el) {
		if($.trim($(el).val()) != '' && !checkmail($(el).val())) {
			validated = false;
			$(el).addClass('error');
			errormsg += 'Please enter a valid email address.<br />';
		}
	});

	//Form validated
	if(validated) {

		//Form requires recaptcha validation
		if(recaptcha_field != '' && $(recaptcha_field).length > 0) {

			//Recaptcha validated
			if(grecaptcha.getResponse($(recaptcha_field).data('id')) != '') {
				post_to_ajax = true;

			//Recaptcha not yet validated - open dialog box that has recaptcha
			} else {
				$('#recaptcha-modal').dialog('open');
			}
			
		//No recaptcha required
		} else {
			post_to_ajax = true;
		}

		if(post_to_ajax) {
			
			if(ajax_file != '') {
			
				//Disable submit button temporarily to prevent double submissions
				this_form.find('*[name="submitform"]').attr({'disabled':true}).find('span').prepend('<i class="fas fa-spinner fa-spin"></i>');

				//Close recaptcha dialog box if it was open
				if(recaptcha_field != '' && $(recaptcha_field).length > 0) {
					$('#recaptcha-modal').dialog('close');
				}

				$.ajax({
					url: path + ajax_file,
					data: form_data,
					dataType: 'json',
					method: 'POST',
					contentType: false, // IMPORTANT: you must set this to false for the file to be uploaded through AJAX
					processData: false, // IMPORTANT: you must set this to false for the file to be uploaded through AJAX
					success: function(result) {

						//Error fields
						if(result.error_fields.length > 0) {
							$.each(result.error_fields, function(key, errfield) {
								var errorfield = this_form.find(':input[name="'+errfield+'"]');
								if(errorfield.hasClass('radio, checkbox')){ //Add checkbox and radio
									errorfield.parents('.jsvalidate').addClass('error');
								}else{
									errorfield.addClass('error');
								}
							});
						}

						//No errors - display success alert
						if(result.errors < 1) {
							this_form[0].reset();
							this_form.find(':input').removeClass('error');
							this_form.find('.input-file-trigger').html('<i class="fas fa-file"></i>Select File... &nbsp; <small>(.pdf .doc .docx)</small>');

							if(alert_success){
								dialogAlert('Success!', result['msg_validation'], 'success');
							}

						//Has errors - display error alert
						} else {
							dialogAlert('Error!', result['msg_validation'], 'error');
						}

						//Re-enable submit button
						this_form.find('*[name="submitform"]').removeAttr('disabled').find('.fas').remove();

						//Reset recaptcha
						if(recaptcha_field != '' && $(recaptcha_field).length > 0) {
							grecaptcha.reset();
						}

						//Execute callback function
						callback(result);
					},
					error: function(result) {
						dialogAlert('Error!', 'Unable to submit form. Please try again.', 'error'); 

						//Re-enable submit button
						this_form.find('*[name="submitform"]').removeAttr('disabled').find('.fas').remove();

						//Reset recaptcha
						if(recaptcha_field != '' && $(recaptcha_field).length > 0) {
							grecaptcha.reset();
						}
					}
				});

			}else{

				//Execute callback function
				callback();
			}
		}
		
	}else{
		dialogAlert('Error!', errormsg, 'error');
	}
}

//Form submission
// function submitForm1(form, ajaxFile, callback) {
// 	let $form           = $(form),
// 		$submit         = $form.find('[name="submitform"]'),
// 		$recaptchaField = $($form.data('recaptcha')),
// 		$recaptchaModal = $recaptchaField.closest('.recaptcha-modal'),
// 		validated       = true,
// 		postAjax        = false,
// 		errormsg        = '';
// 		var this_form = $("#" + form);
// 		var alert_success = (!this_form.hasClass('leadin-form') ? true : false);
// 		var recaptcha_field = (this_form.data('recaptcha') != undefined && this_form.data('recaptcha') != '' ? this_form.data('recaptcha') : '');
// 		var form_data = (ajaxFile ? new FormData($form.get(0)) : $form.serialize());

// 		//Hack for Safari bug where empty values were breaking upload
// 		if(form_data instanceof FormData && navigator.userAgent.match(/version\/11((\.[0-9]*)*)? .*safari/i)) {
// 			try {
// 				eval('for (var pair of form_data.entries()) {\
// 					if (pair[1] instanceof File && pair[1].name === \'\' && pair[1].size === 0) {\
// 						form_data.delete(pair[0]);\
// 					}\
// 				}');
// 			} catch(e) {}
// 		}

// 		console.log('form data ===- '+ $form.serialize());

// 	//Validate fields
// 	$errors = $form.find('.jsvalidate').filter((i, el) => $(el).val().trim() == '');
// 	if ($errors.length) {
// 		$errors.addClass('error');
// 		validated = false;
// 		errormsg += 'Please fill out all the required fields.<br />';
// 	}

// 	$errors = $form.find('.jsvalidate').filter((i, el) => {
// 		if($(el).is(':checkbox') || $(el).is(':radio')){
// 			return !$(el).closest('form').find(`input[name="${$(el)[0].name}"]:checked`).length;
// 		}else{
// 			return ($(el).val().trim() == '');
// 		}
// 	});

// 	//Validate email fields
// 	$errors = $form.find('input[type="email"]').filter((i, el) => $(el).val().trim() != '' && !checkmail($(el).val()));
// 	if ($errors.length) {
// 		$errors.addClass('error');
// 		validated = false;
// 		errormsg += 'Please enter a valid email address.<br />';
// 	}

// 	//Form validated
// 	if(validated) {

// 		//Form requires recaptcha validation
// 		if($recaptchaField.length > 0) {
// 			if(grecaptcha.getResponse($recaptchaField.data('id')) != '') {
// 				postAjax = true;

// 			//Recaptcha not yet validated - open dialog box that has recaptcha
// 			} else {
// 				$recaptchaModal.trigger('modal-open');
// 			}

// 		//No recaptcha required
// 		} else {
// 			postAjax = true;
// 		}

// 		if(postAjax) {
// 			if(ajaxFile != '') {
// 				//Disable submit button temporarily to prevent double submissions
// 				$submit.attr('disabled', true).addClass('loading');
                
// 				//Close recaptcha dialog box if it was open
// 				$recaptchaField.length > 0 && $recaptchaModal.dialog('close');

// 				$.ajax({
// 					url: path + ajaxFile,
// 					data: form_data,
// 					dataType: 'json',
// 					method: 'POST',
// 					contentType: false, // IMPORTANT: you must set this to false for the file to be uploaded through AJAX
// 					processData: false, // IMPORTANT: you must set this to false for the file to be uploaded through AJAX
// 					success: function(result) {

// 						//Error fields
// 						if(result.error_fields.length > 0) {
// 							$.each(result.error_fields, function(key, errfield) {
// 								var errorfield = this_form.find(':input[name="'+errfield+'"]');
// 								if(errorfield.hasClass('radio, checkbox')){ //Add checkbox and radio
// 									errorfield.parents('.jsvalidate').addClass('error');
// 								}else{
// 									errorfield.addClass('error');
// 								}
// 							});
// 						}

// 						//No errors - display success alert
// 						if(result.errors < 1) {
// 							this_form[0].reset();
// 							this_form.find(':input').removeClass('error');
// 							this_form.find('.input-file-trigger').html('<i class="fas fa-file"></i>Select File... &nbsp; <small>(.pdf .doc .docx)</small>');

// 							if(alert_success){
// 								dialogAlert('Success!', result['msg_validation'], 'success');
// 							}

// 						//Has errors - display error alert
// 						} else {
// 							dialogAlert('Error!', result['msg_validation'], 'error');
// 						}

// 						//Re-enable submit button
// 						this_form.find('*[name="submitform"]').removeAttr('disabled').find('.fas').remove();

// 						//Reset recaptcha
// 						if(recaptcha_field != '' && $(recaptcha_field).length > 0) {
// 							grecaptcha.reset();
// 						}

// 						//Execute callback function
// 						callback(result);
// 					},
// 					error: function(result) {
// 						dialogAlert('Error!', 'Unable to submit form. Please try again.', 'error'); 

// 						//Re-enable submit button
// 						$form.find('*[name="submitform"]').removeAttr('disabled').find('.fas').remove();

// 						//Reset recaptcha
// 						if(recaptcha_field != '' && $(recaptcha_field).length > 0) {
// 							grecaptcha.reset();
// 						}
// 					}
// 				});

// 				// $.post(path + ajaxFile, $form.serialize(), function(result) {

// 				// 	//No errors - display success alert
// 				// 	if(result.errors < 1) {
// 				// 		$form[0].reset();
// 				// 		$form.find(':input.error').removeClass('error');

// 				// 		if(!$form.hasClass('leadin-form')){
// 				// 			dialogAlert('Success!', result['msg_validation'], 'success '+$form.attr('id')+'-success');
// 				// 		}

// 				// 	//Has errors - display error alert
// 				// 	} else {
// 				// 		$(result.error_fields.map(fieldname => ':input[name="'+fieldname+'"]').join(',')).addClass('error');
// 				// 		dialogAlert('Error!', result['msg_validation'], 'error');

// 				// 		if(result.error_fields.length > 0) {
// 				// 			$.each(result.error_fields, function(key, errfield) {
// 				// 				var errorfield = this_form.find(':input[name="'+errfield+'"]');
// 				// 				if(errorfield.hasClass('radio, checkbox')){ //Add checkbox and radio
// 				// 					errorfield.parents('.jsvalidate').addClass('error');
// 				// 				}else{
// 				// 					errorfield.addClass('error');
// 				// 				}
// 				// 			});
// 				// 		}
// 				// 	}
                    
// 				// 	//Re-enable submit button
// 				// 	$submit.removeAttr('disabled').removeClass('loading');

// 				// 	//Execute callback function
// 				// 	callback(result);

// 				// 	//Reset recaptcha
// 				// 	$recaptchaField.length > 0 && grecaptcha.reset($recaptchaField.data('id'));
// 				// }, 'json');

// 			//Execute callback function
// 			}else{
// 				callback();
// 			}
// 		}
// 	}else{
// 		dialogAlert('Error!', errormsg, 'error');
// 	}
// }

//Clear/Set error class
$(function(){
	$(document).on('blur', '.jsvalidate:input', function() {
		let $this = $(this);
		$this.toggleClass('error', !$this.val());
		$this.is(':not(.error)[type="email"]') && $this.toggleClass('error', !checkmail($this.val()));
		//clear fields for form builder
		if($this.is(':checkbox') || $this.is(':radio')){
			let $these = $this.closest('form').find(`input[name="${$this[0].name}"]`);
			$these.toggleClass('error', !$these.filter(':checked').length);
		}else{
			$this.toggleClass('error', !$this.val());
			$this.is(':not(.error)[type="email"]') && $this.toggleClass('error', !checkmail($this.val()));
		}
	});
});

//Message centre subscribe
$(function(){
	$('#mc_subscribe').bind('click', function(){
		var mc_subscribe = ($(this).is(':checked') ? 1 : 0);
		$.ajax({
			url: path+'js/ajax/mcsubscribe.php',
			data: 'mc_subscribe='+mc_subscribe,
			method: 'post',
		}).done(function(data){
			if(data == 'login'){
				window.location = window.location.href;	
			}
		});
	});
});

//handle search bar
$(function(){
	$('.search_input').hide();
	$('.search-btn').on('click',function(){
		$('.search_input').show("slide", { direction: "left" }, 1000);
		$('.search-btn').on('click',function(){
			$('#site-search').submit();
		});
	});
});
