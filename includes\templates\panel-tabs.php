<?php

//Panel tabs
if(!empty($panel['panel_tabs'])){
	echo '<div class="panel-tabs">
		<div class="container">
			<div'.(isset($panel['panel_tabs_id']) ? ' id="'.$panel['panel_tabs_id'].'"' : '').' class="content-tabs">
				<div class="tabs-nav-wrapper">
					<ul class="tabs-nav">';

					$index = 0;
					foreach($panel['panel_tabs'] as $tab){
						echo '<li><a href="#'.$tab['page'].'-'.$tab['tab_id'].'" data-index="'.$index.'"'.(!empty($tab['attrs']) ? ' '.$tab['attrs'] : '').'>'.$tab['title'].'</a></li>';
						$index++;
					}
					
					echo '</ul>
				</div>';
				foreach($panel['panel_tabs'] as $tab){
					echo '<div id="'.$tab['page'].'-'.$tab['tab_id'].'" class="tabs-panel panel-text'.(!empty($tab['class']) ? ' '.$tab['class'] : '').'">'.$tab['content'].'</div>';
				}

			echo '</div>'.
			($panel['append_tabs'] ?? '').
		'</div>
	</div>';
}

?>