<?php

$html = $page['page_panels'][81]['content'];

//Display search form
$html .= '<form name="pd-search" action="' .$path.'reports/report-pd.php" method="get" target="_blank" id="pd-search">
	<div class="form-field">';

		//Select year
		$html .= '<select name="year" class="select ">';
		for($y=$pd_settings['current_year']; $y>=2018; $y--){
			$html .= '<option value="' .$y. '">' .$y. '</option>';
		}
		$html .= '</select>';

		//Select type
		if(isset($membership_types) && !empty($membership_types)){
			$html .= '<select name="mid" class="select">
			<option value="">All Memberships</option>';
			foreach($membership_types as $type){
				$html .= '<option value="' .$type['membership_id']. '">' .$type['membership_name']. '</option>';
			}
			$html .= '</select>';
		}	

	$html .= '</div>
	<div class="form-column f_right">
		<button type="submit" name="search" value="1" class="button solid inline">View Report</button>
	</div>
</form>';

//Set panel content
$page['page_panels'][81]['content'] = $html;

//Page panels
include("includes/pagepanels.php");

?>