<?php

//Get awards
if(PAGE_ID == $_sitepages['award-winners']['page_id'] || (PARENT_ID == $_sitepages['award-winners']['page_id'] && PAGE_ID == '')){
	
	//Define vars
	$panel_id = 64;
	$award_year = date('Y');
	$awards = array();
	$award_winners = array();
	$current_winners = array();
	
	//Get most recent award year
	$query = $db->query("SELECT `year` FROM `award_winners` WHERE `showhide` = 0 ORDER BY `year` DESC LIMIT 1");
	if($query && !$db->error() && $db->num_rows()){
		$result = $db->fetch_array();
		$award_year = $result[0]['year'];
	}
	
	//Get awards
	$query = $db->query("SELECT * FROM `awards` WHERE `showhide` = 0 ORDER BY `parent_id` ASC, `ordering` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			if(empty($row['parent_id'])){
				$awards[$row['award_id']] = $row;
			}else{
				if(array_key_exists($row['parent_id'], $awards)){
					$awards[$row['parent_id']]['sub_categories'][$row['award_id']] = $row;
				}
				
			}
		}
	}
		
	//Get winners
	$query = $db->query("SELECT `award_winners`.*, ".
	"`accounts`.`status` AS `account_status`, ".
	"`accounts`.`showhide` AS `profile_showhide`, ".
	"`account_profiles`.`profile_id`, ".
	"IFNULL(`award_winners`.`first_name`, `account_profiles`.`first_name`) AS `first_name`, ".
	"IFNULL(`award_winners`.`last_name`, `account_profiles`.`last_name`) AS `last_name`, ".
	"IFNULL(`award_winners`.`image`, `account_profiles`.`photo`) AS `image` ".
	"FROM `award_winners` ".
	"LEFT JOIN `accounts` ON `award_winners`.`account_id` = `accounts`.`account_id` ".
	"LEFT JOIN `account_profiles` ON `award_winners`.`account_id` = `account_profiles`.`account_id` ".
	"WHERE `award_winners`.`showhide` = 0 ORDER BY `year` DESC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			
			//Set profile url
			$row['profile_url'] = NULL;
			if(!empty($row['profile_id']) && $row['account_status'] == 'Active' && $row['profile_showhide'] == 0){
				$row['profile_url'] = $_sitepages['directory']['page_url'].clean_url($row['first_name'].' '.$row['last_name'].'-'.$row['profile_id']).'/';	
			}
			
			//Current winner
			if($row['year'] == $award_year){
				if(!array_key_exists($row['award_id'], $current_winners)){
					$current_winners[$row['award_id']] = array();
				}		
				array_push($current_winners[$row['award_id']], $row);
				
			//Past winner
			}else{
				if(!array_key_exists($row['award_id'], $award_winners)){
					$award_winners[$row['award_id']] = array();
				}		
				array_push($award_winners[$row['award_id']], $row);
			}
		}
	}
		
}

//Get selected award
if(PARENT_ID == $_sitepages['award-winners']['page_id'] && PAGE_ID == ''){

	$pagebits = $SiteBuilder->get_pagebits((trim($_sitepages['award-winners']['slug']) != '' ? $_sitepages['award-winners']['slug'] : $_sitepages['award-winners']['page']));
	$panel_id = 64;
	
	if(!isset($pagebits[3]) || $pagebits[3] == ''){
		$award_bits = explode("-", $pagebits[2]);
		$award_id = $award_bits[count($award_bits)-1];
		
		$award = array();
		if(array_key_exists($award_id, $awards)){
			$error404 = false;
			$award = $awards[$award_id];
			$award['page_url'] = $_sitepages['award-winners']['page_url'].$award['page'].'-'.$award_id.'/';
			
			//Correct path
			if(clean_url($award['page'].'-'.$award_id) != $pagebits[2]){
				header("HTTP/1.1 301 Moved Permanently");
				header('Location: ' .$award['page_url']);
				exit();
			}
			
			//Set page vars
			$parent = $SiteBuilder->get_page_content(PARENT_ID);
		
			$page['page_title'] = $parent['page_title'];
			$page['description'] = $award['name'];
			$page['content'] = '';
			$page['meta_canonical'] = $award['page_url'];
			$page['meta_title'] = ($award['meta_title'] != '' ? $award['meta_title'] : $award['name'].' | '.$parent['meta_title']);
			$page['meta_description'] = ($award['meta_description'] != '' ? $award['meta_description'] : $parent['meta_description']);
			if($award['image'] != '' && file_exists('images/heroes/1920/'.$award['image'])){
				$page['banner_image'] = $award['image'];
				$page['banner_image_alt'] = (trim($award['image_alt']) != '' ? $award['image_alt'] : $award['name']);
			}else{
				$page['banner_image'] = $parent['banner_image'];
				$page['banner_image_alt'] = $parent['banner_image'];
			}			
			$page['page_panels'] = array();
			$page['page_panels'][$panel_id] = $parent['page_panels'][$panel_id]; //Utilize parent panel
			$page['page_panels'][$panel_id]['content'] = $award['description'];
			$page['page_panels'][$panel_id]['show_title'] = false;
			$page['page_panels'][$panel_id]['title'] = $award['name'];
			$award['name'] = str_replace('{', '', str_replace('}', '', $award['name']));
			
			//Breadcrumb
			array_pop($breadcrumbs);
			array_push($breadcrumbs, array('name'=>$award['name'], 'url'=>$award['page_url']));
		
		}else{
			unset($award_id);
		}
	}

}

?>