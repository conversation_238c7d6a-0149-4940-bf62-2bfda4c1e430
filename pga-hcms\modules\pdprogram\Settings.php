<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['top-100-settings']){
	
	//Define vars
	$errors = false;
	$required = array();
	$imagedir = "../images/logos/";
	$CMSUploader = new CMSUploader('top_settings', $imagedir);

	//Get settings
	$pd_settings = array();
	$query = $db->query("SELECT * FROM `pd_settings` WHERE `id` = 1");
	if($query && !$db->error() && $db->num_rows()) {
		$pd_settings = $db->fetch_array()[0];
	}

	//Save changes
	if(isset($_POST['save'])) {
		
		//Validate
		if(trim($_POST['current_year']) == ''){
			$errors[0] = 'Please fill out all the required fields.';
			$required[] = 'current_year';
		}

		//Image validation
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large.';
			$required[] = 'image';
		}

		// if((!empty($_FILES['image']['name']) || !empty($pd_settings['badge'])) && trim($_POST['badge_year']) == ''){
		// 	$errors[0] = 'Please fill out all the required fields.';
		// 	$required[] = 'badge_year';
		// }
		// if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > 20480000){
		// 	$errors[] = 'Image filesize is too large.';
		// }
			
		if(!$errors) {

			$pagename = 'top100-badge-'.$_POST['badge_year'];

			//Delete image
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//Upload new image
			try{
				$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			$image = $images['image'] ?? ($records_arr[ITEM_ID]['image'] ?? '');
			
			//Update settings
			$params = array(
				$_POST['current_year'],
				$image,
				$_POST['badge_year'],
				date("Y-m-d H:i:s")
			);
			$query = $db->query("UPDATE `pd_settings` SET `current_year`=?, `badge`=?, `badge_year`=?, `last_updated`=? WHERE `id`=1", $params);
			if($query && !$db->error()){

				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert('Settings have been updated.', true);
					header("Location: " .PAGE_URL);
					exit();
				}
				
				// $CMSBuilder->set_system_alert('Settings have been updated.', true);
				// header('Location: '.PAGE_URL);
				// exit();

			} else {
				$CMSBuilder->set_system_alert('Unable to save settings: '.$db->error(), false);
			}

		} else if($errors && is_array($errors)) {
			$CMSBuilder->set_system_alert(implode('<br/>', $errors), false);
		}
	}else{
		include('modules/CropImages.php');
	}
}

?>