<?php

//Members on the move
if(PAGE_ID == $_sitepages['motm']['page_id'] || (PARENT_ID == $_sitepages['motm']['page_id'] && PAGE_ID == '')){

	//Define vars
	$start_date = NULL;
	$end_date = NULL;
	$panel_id = 51;
	$archive_year = NULL; // Initialize this variable

	// Get page bits similar to Blog.php
	$pagebits = $SiteBuilder->get_pagebits($_sitepages['motm']['slug'] ?: $_sitepages['motm']['page']);

    // echo "<pre>motm - ";
    // print_r($pagebits);
    // print_r($_GET);
    // echo "</pre>";
    // exit;

	//Get archive years
	$archives = array();
	$query = $db->query("SELECT `updated_on` FROM `account_change_log` ORDER BY `updated_on` ASC LIMIT 1");
	if($query && !$db->error() && $db->num_rows()){
		$result = $db->fetch_array();
		$oldest = date("Y", strtotime($result[0]['updated_on'])); 
		for($y=date("Y")-1; $y>=$oldest; $y--){
			$archives[] = $y;
		}
	}

	//Check if we have an archive URL
	if(!empty($pagebits[2]) && strpos($pagebits[2], 'archive-') === 0) {

		// Extract year from archive URL (e.g., "archive-2024" -> "2024")
		$archive_year = str_replace('archive-', '', $pagebits[2]);

		// Validate that the year exists in our archives
		if(in_array($archive_year, $archives)){
			$error404 = false;

			//Set page data for archive view
			$page = $SiteBuilder->get_page_content($_sitepages['motm']['page_id']);
			$page['meta_canonical'] = $_sitepages['motm']['page_url'] . $pagebits[2] . '/';
			$page['page_url'] = $_sitepages['motm']['page_url']; // Keep base URL for "Back to Recent" link
			$page['meta_title'] = $archive_year.' '.$page['meta_title'];
			$page['meta_description'] = $archive_year.' '.$page['meta_description'];
			$page['redirect_to_slug'] = false;

			// Update panel content
			$page['page_panels'][$panel_id]['title'] = $archive_year.' Archive';
			$page['page_panels'][$panel_id]['show_title'] = true;
			$page['page_panels'][$panel_id]['content'] = '';

			//Update breadcrumbs
			array_pop($breadcrumbs);
			array_push($breadcrumbs, array('name'=>$page['page_panels'][$panel_id]['title'], 'url'=>$page['meta_canonical']));

			//Set date range for the selected year
			$start_date = $archive_year.'-01-01';
			$end_date = $archive_year.'-12-31';

		} else {
			// Invalid archive year - set 404
			$error404 = true;
		}

	} else {
		// Default view - last 12 months
		$start_date = date("Y-m", strtotime("-11 months")).'-01';
		$end_date = date("Y-m-d");
	}

	// Handle 404 for invalid archive years
	if(isset($error404) && $error404) {
		$page = $SiteBuilder->get_page_content('error');
		$changelog = array(); // Initialize empty changelog for 404
	} else {
		//Get entries for the specified date range
		$changelog = array();
		if($start_date && $end_date) {
			$query = $db->query("SELECT `comments`, `updated_on` FROM `account_change_log` WHERE `showhide` = 0 AND (`updated_on` >= ? AND `updated_on` <= ?) ORDER BY `updated_on` DESC, `id` DESC", array($start_date, $end_date));
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $row){
					$date = date("F Y", strtotime($row['updated_on']));
					$changelog[$date][] = $row;
				}
			}
		}
	}

}

?>