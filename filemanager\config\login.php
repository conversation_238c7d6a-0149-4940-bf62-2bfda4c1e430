<?php

$root = "/";
include($_SERVER['DOCUMENT_ROOT'].$root."config/config.php");
include($_SERVER['DOCUMENT_ROOT'].$root."config/database.php");

//Login status
require_once($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Account.class.php");
$Account = new Account('Admin');
define('USER_LOGGED_IN', $Account->login_status());

//Instantiate reSmush
require_once($_SERVER['DOCUMENT_ROOT'].$cmspath."includes/plugins/resmush/reSmush.class.php");
$reSmush = new reSmush(); 

?>