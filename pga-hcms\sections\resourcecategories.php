<?php

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">'.$records_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">

				<thead>
				<th width="1px" data-sorter="false"></th>
				<th>Folder Name</th>
				<th>Permissions</th>
				<th width="80px">Visible</th>
				<th width="1px" data-sorter="false"></th>
				</thead>

				<tbody>';

				foreach($records_arr as $row){

					// Build permissions display
					$folder_permissions = "";
					if(!empty($row['committees'])){
						$folder_committees = explode(",",$row['committees']);
						foreach($folder_committees as $comm){
							if(isset($committees[$comm])){
								$folder_permissions .= $committees[$comm]['name']."<br/>";
							}
						}
					}
					if(!empty($row['boards'])){
						$folder_boards = explode(",",$row['boards']);
						foreach($folder_boards as $board){
							if(isset($boards[$board])){
								$folder_permissions .= $boards[$board]."<br/>";
							}
						}
					}

					if($folder_permissions == ""){
						$folder_permissions = "<small class='text-muted'>Unrestricted</small>";
					}

					echo '<tr data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-id="'.$row[$record_id].'">
					<td class="handle"><i class="fas fa-arrows-alt"></i></td>
					<td><i class="fas fa-folder text-warning"></i> '.$row['name'].'</td>
					<td>'.$folder_permissions.'</td>
					<td>'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
					<td><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
					</tr>';
				}

				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager(50);

		echo '</div>
	</div>';


//Display form
}else{
	$data = $records_arr[ITEM_ID] ?? [];
	$row  = !isset($_POST['save']) ? $data : $_POST;

	echo '<form action="" method="post" enctype="multipart/form-data">';

		// Folder details
		echo '<div class="panel">
			<div class="panel-header">Folder Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show Folder</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="1"' .(($row['showhide'] ?? 1) == 0 ? ' checked' : ''). ' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>

			<div class="panel-content flex-container">
				<div class="form-field" style="flex: 2;">
				<label>Folder Name <span class="required">*</span></label>
				<input type="text" name="name" value="'.($row['name'] ?? '').'" class="input'.(in_array('name', $required) ? ' required' : '').'" />
				</div>

				<div class="form-field">
				<label>Numerical Order'.$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.').'</label>
				<select name="ordering" class="select">
				<option value="101">Default</option>';

				for($i = 1; $i < 101; $i++){
				echo '<option value="'.$i.'"'.(($row['ordering'] ?? false) == $i ? ' selected' : '').'>'.$i.'</option>';
				}

				echo '</select>
				</div>
			</div>
		</div>';

		// Permissions panel
		echo '<div class="panel">
			<div class="panel-header">Folder Permissions
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="alert alert-info">
					<i class="fas fa-info-circle"></i> If no board or committee is selected, access to this folder is unrestricted to all members.
				</div>

				<div class="flex-container">
					<div class="form-field" style="flex: 1; margin-right: 20px;">
						<label><strong>Committee(s)</strong></label>
						<div class="checkbox-group">';

						foreach($committees as $committee){
							$checked = in_array($committee['committee_id'], $row['committees'] ?? []) ? ' checked' : '';
							echo '<div class="checkbox-item">
								<input type="checkbox" value="'.$committee['committee_id'].'" id="committee_'.$committee['committee_id'].'" name="committees[]" class="checkbox"'.$checked.'>
								<label for="committee_'.$committee['committee_id'].'">'.$committee['name'].'</label>
							</div>';
						}

						echo '</div>
					</div>

					<div class="form-field" style="flex: 1;">
						<label><strong>Board(s)</strong></label>
						<div class="checkbox-group">';

						foreach($boards as $board_id => $board){
							$checked = in_array($board_id, $row['boards'] ?? []) ? ' checked' : '';
							echo '<div class="checkbox-item">
								<input type="checkbox" value="'.$board_id.'" id="board_'.$board_id.'" name="boards[]" class="checkbox"'.$checked.'>
								<label for="board_'.$board_id.'">'.$board.'</label>
							</div>';
						}

						echo '</div>
					</div>
				</div>
			</div>
		</div>';

		//Sticky footer
		include('includes/widgets/formbuttons.php');

		echo '<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'].'" />
	</form>';

}

?>