!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("6",tinymce.util.Tools.resolve),g("1",["6"],function(a){return a("tinymce.dom.DOMUtils")}),g("2",["6"],function(a){return a("tinymce.PluginManager")}),g("3",["6"],function(a){return a("tinymce.util.JSON")}),g("4",["6"],function(a){return a("tinymce.util.Tools")}),g("5",["6"],function(a){return a("tinymce.util.XHR")}),g("0",["1","2","3","4","5"],function(a,b,c,d,e){return b.add("template",function(b){function f(a){return function(){var d=b.settings.templates;return"function"==typeof d?void d(a):void("string"==typeof d?e.send({url:d,success:function(b){a(c.parse(b))}}):a(d))}}function g(c){function f(a){function c(a){if(a.indexOf("<html>")==-1){var c="";d.each(b.contentCSS,function(a){c+='<link type="text/css" rel="stylesheet" href="'+b.documentBaseURI.toAbsolute(a)+'">'});var e=b.settings.body_class||"";e.indexOf("=")!=-1&&(e=b.getParam("body_class","","hash"),e=e[b.id]||""),a="<!DOCTYPE html><html><head>"+c+'</head><body class="'+e+'">'+a+"</body></html>"}a=j(a,"template_preview_replace_values");var f=g.find("iframe")[0].getEl().contentWindow.document;f.open(),f.write(a),f.close()}var f=a.control.value();f.url?e.send({url:f.url,success:function(a){h=a,c(h)}}):(h=f.content,c(h)),g.find("#description")[0].text(a.control.value().description)}var g,h,i=[];if(!c||0===c.length){var l=b.translate("No templates defined.");return void b.notificationManager.open({text:l,type:"info"})}d.each(c,function(a){i.push({selected:!i.length,text:a.title,value:{url:a.url,content:a.content,description:a.description}})}),g=b.windowManager.open({title:"Insert template",layout:"flex",direction:"column",align:"stretch",padding:15,spacing:10,items:[{type:"form",flex:0,padding:0,items:[{type:"container",label:"Templates",items:{type:"listbox",label:"Templates",name:"template",values:i,onselect:f}}]},{type:"label",name:"description",label:"Description",text:"\xa0"},{type:"iframe",flex:1,border:1}],onsubmit:function(){k(!1,h)},minWidth:Math.min(a.DOM.getViewPort().w,b.getParam("template_popup_width",600)),minHeight:Math.min(a.DOM.getViewPort().h,b.getParam("template_popup_height",500))}),g.find("listbox")[0].fire("select")}function h(a,c){function d(a,b){if(a=""+a,a.length<b)for(var c=0;c<b-a.length;c++)a="0"+a;return a}var e="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),f="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),g="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),h="January February March April May June July August September October November December".split(" ");return c=c||new Date,a=a.replace("%D","%m/%d/%Y"),a=a.replace("%r","%I:%M:%S %p"),a=a.replace("%Y",""+c.getFullYear()),a=a.replace("%y",""+c.getYear()),a=a.replace("%m",d(c.getMonth()+1,2)),a=a.replace("%d",d(c.getDate(),2)),a=a.replace("%H",""+d(c.getHours(),2)),a=a.replace("%M",""+d(c.getMinutes(),2)),a=a.replace("%S",""+d(c.getSeconds(),2)),a=a.replace("%I",""+((c.getHours()+11)%12+1)),a=a.replace("%p",""+(c.getHours()<12?"AM":"PM")),a=a.replace("%B",""+b.translate(h[c.getMonth()])),a=a.replace("%b",""+b.translate(g[c.getMonth()])),a=a.replace("%A",""+b.translate(f[c.getDay()])),a=a.replace("%a",""+b.translate(e[c.getDay()])),a=a.replace("%%","%")}function i(a){var c=b.dom,e=b.getParam("template_replace_values");d.each(c.select("*",a),function(a){d.each(e,function(b,d){c.hasClass(a,d)&&"function"==typeof e[d]&&e[d](a)})})}function j(a,c){return d.each(b.getParam(c),function(b,c){"function"==typeof b&&(b=b(c)),a=a.replace(new RegExp("\\{\\$"+c+"\\}","g"),b)}),a}function k(a,c){function e(a,b){return new RegExp("\\b"+b+"\\b","g").test(a.className)}var f,g,k=b.dom,l=b.selection.getContent();c=j(c,"template_replace_values"),f=k.create("div",null,c),g=k.select(".mceTmpl",f),g&&g.length>0&&(f=k.create("div",null),f.appendChild(g[0].cloneNode(!0))),d.each(k.select("*",f),function(a){e(a,b.getParam("template_cdate_classes","cdate").replace(/\s+/g,"|"))&&(a.innerHTML=h(b.getParam("template_cdate_format",b.getLang("template.cdate_format")))),e(a,b.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(a.innerHTML=h(b.getParam("template_mdate_format",b.getLang("template.mdate_format")))),e(a,b.getParam("template_selected_content_classes","selcontent").replace(/\s+/g,"|"))&&(a.innerHTML=l)}),i(f),b.execCommand("mceInsertContent",!1,f.innerHTML),b.addVisual()}b.addCommand("mceInsertTemplate",k),b.addButton("template",{title:"Insert template",onclick:f(g)}),b.addMenuItem("template",{text:"Template",onclick:f(g),context:"insert"}),b.on("PreProcess",function(a){var c=b.dom;d.each(c.select("div",a.node),function(a){c.hasClass(a,"mceTmpl")&&(d.each(c.select("*",a),function(a){c.hasClass(a,b.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(a.innerHTML=h(b.getParam("template_mdate_format",b.getLang("template.mdate_format"))))}),i(a))})})}),function(){}}),d("0")()}();