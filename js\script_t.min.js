function submitForm1(form_id,ajax_file,callback){var this_form=$("#"+form_id),button_text=this_form.find('*[name="submitform"]').html(),recaptcha_field=null!=this_form.data("recaptcha")&&""!=this_form.data("recaptcha")?this_form.data("recaptcha"):"",validated=!0,post_to_ajax=!1,errormsg="",alert_success=!this_form.hasClass("leadin-form"),form_data=ajax_file?new FormData(this_form.get(0)):this_form.serialize();if(form_data instanceof FormData&&navigator.userAgent.match(/version\/11((\.[0-9]*)*)? .*safari/i))try{eval("for (var pair of form_data.entries()) {\t\t\t\tif (pair[1] instanceof File && pair[1].name === '' && pair[1].size === 0) {\t\t\t\t\tform_data.delete(pair[0]);\t\t\t\t}\t\t\t}")}catch(a){}this_form.find(".jsvalidate").each((function(a,e){if(0!==$(e).find("input.radio, input.checkbox").length){var r=!1;$(e).find("input.radio, input.checkbox").each((function(){$(this).is(":checked")&&(r=!0)})),r||(validated=!1,$(e).addClass("error"),errormsg="Please fill out all the required fields.<br />")}else""==$.trim($(e).val())&&(validated=!1,$(e).addClass("error"),$(e).parents(".input-file-container").addClass("error"),errormsg="Please fill out all the required fields.<br />")})),this_form.find('input[type="email"]').each((function(a,e){""==$.trim($(e).val())||checkmail($(e).val())||(validated=!1,$(e).addClass("error"),errormsg+="Please enter a valid email address.<br />")})),validated?(""!=recaptcha_field&&$(recaptcha_field).length>0?""!=grecaptcha.getResponse($(recaptcha_field).data("id"))?post_to_ajax=!0:$("#recaptcha-modal").dialog("open"):post_to_ajax=!0,post_to_ajax&&(""!=ajax_file?(this_form.find('*[name="submitform"]').attr({disabled:!0}).find("span").prepend('<i class="fas fa-spinner fa-spin"></i>'),""!=recaptcha_field&&$(recaptcha_field).length>0&&$("#recaptcha-modal").dialog("close"),$.ajax({url:path+ajax_file,data:form_data,dataType:"json",method:"POST",contentType:!1,processData:!1,success:function(a){a.error_fields.length>0&&$.each(a.error_fields,(function(a,e){var r=this_form.find(':input[name="'+e+'"]');r.hasClass("radio, checkbox")?r.parents(".jsvalidate").addClass("error"):r.addClass("error")})),a.errors<1?(this_form[0].reset(),this_form.find(":input").removeClass("error"),this_form.find(".input-file-trigger").html('<i class="fas fa-file"></i>Select File... &nbsp; <small>(.pdf .doc .docx)</small>'),alert_success&&dialogAlert("Success!",a.msg_validation,"success")):dialogAlert("Error!",a.msg_validation,"error"),this_form.find('*[name="submitform"]').removeAttr("disabled").find(".fas").remove(),""!=recaptcha_field&&$(recaptcha_field).length>0&&grecaptcha.reset(),callback(a)},error:function(a){dialogAlert("Error!","Unable to submit form. Please try again.","error"),this_form.find('*[name="submitform"]').removeAttr("disabled").find(".fas").remove(),""!=recaptcha_field&&$(recaptcha_field).length>0&&grecaptcha.reset()}})):callback())):dialogAlert("Error!",errormsg,"error")}$((function(){$("form.dynamic-form1").on("submit",(function(){return submitForm1($(this).attr("id"),"js/ajax/submit-dynamic-form.php",(function(){})),!1}))})),$((function(){$(document).on("blur",".jsvalidate:input",(function(){let a=$(this);if(a.toggleClass("error",!a.val()),a.is(':not(.error)[type="email"]')&&a.toggleClass("error",!checkmail(a.val())),a.is(":checkbox")||a.is(":radio")){let e=a.closest("form").find(`input[name="${a[0].name}"]`);e.toggleClass("error",!e.filter(":checked").length)}else a.toggleClass("error",!a.val()),a.is(':not(.error)[type="email"]')&&a.toggleClass("error",!checkmail(a.val()))}))})),$((function(){$("#mc_subscribe").bind("click",(function(){var a=$(this).is(":checked")?1:0;$.ajax({url:path+"js/ajax/mcsubscribe.php",data:"mc_subscribe="+a,method:"post"}).done((function(a){"login"==a&&(window.location=window.location.href)}))}))})),$((function(){$(".search_input").hide(),$(".search-btn").on("click",(function(){$(".search_input").show("slide",{direction:"left"},1e3),$(".search-btn").on("click",(function(){$("#site-search").submit()}))}))}));