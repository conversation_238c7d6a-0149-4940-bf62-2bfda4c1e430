<?php  

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../includes/utils.php");

if(isset($_GET) && USER_LOGGED_IN){

	//Define vars
	$record_db = 'hio';
	$record_id = 'hio_id';
	$record_name = 'Hole In One';
	$records_arr = array();

	$db_columns = array(); //for SELECT in query
	$table_columns = array(); //for listing label

	$params = array();
	$wheretxt = " WHERE `$record_db`.`approved` = 1";
	$querytxt = "";

	//Set columns to get
	$db_columns = array(
		'first_name',
		'last_name',
		'account_id',
		'facility_name',
		'event_name',
		'event_date',
		'field',
		'hole',
		'yardage',
		'prize_total',
		'premium_total',
		'invoice_number',
		'approved',
		'date_approved',
		'date_added'
	);
	$table_columns = array(
		'First Name',
		'Last Name',
		'Account No.',
		'Facility Name',
		'Event Name',
		'Event Date',
		'No. of Golfers',
		'Hole',
		'Yardage',
		'Prize Amount',
		'Premium Amount',
		'Invoice No.',
		'Approved',
		'Date Approved',
		'Date Submitted'
	);
	
	//Search term
	if(isset($_GET['search']) && $_GET['search'] != ""){
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."(CONCAT(`account_profiles`.`first_name`, ? ,`account_profiles`.`last_name`) LIKE ? || `facilities`.`facility_name` LIKE ? || `$record_db`.`event_name` LIKE ?)";
		$params[] = ' ';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
	}

	//Filter by dates
	$date_range = '';
	if(isset($_GET['start_date']) && $_GET['start_date'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`date_added` >= ?";
		$params[] = date('Y-m-d 00:00:00', strtotime($_GET['start_date']));
		$date_range .= date('M j, Y', strtotime($_GET['start_date']));
	}
	if(isset($_GET['end_date']) && $_GET['end_date'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`date_added` <= ?";
		$params[] = date('Y-m-d 23:59:59', strtotime($_GET['end_date']));
		$date_range .= (!empty($date_range) ? ' - ' : '').date('M j, Y', strtotime($_GET['end_date']));
	}

	//Create query
	$querytxt = "SELECT `$record_db`.*, `accounts`.`email`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `accounts`.`email`, `account_profiles`.`phone`, `facilities`.`facility_name`, `facilities`.`email` AS `facility_email`, `facilities`.`phone` AS `facility_phone`, `invoices`.`invoice_number`, ".
	"(SELECT `event_date` FROM `hio_dates` WHERE `hio_dates`.`$record_id` = `$record_db`.`$record_id` ORDER BY `hio_dates`.`event_date` ASC LIMIT 1) AS `start_date`, ".
	"(SELECT `event_date` FROM `hio_dates` WHERE `hio_dates`.`$record_id` = `$record_db`.`$record_id` ORDER BY `hio_dates`.`event_date` DESC LIMIT 1) AS `end_date`".
	"FROM `$record_db` ".
	"LEFT JOIN `accounts` ON `accounts`.`account_id` = `$record_db`.`account_id` ".
	"LEFT JOIN `account_profiles` ON `account_profiles`.`account_id` = `$record_db`.`account_id` ".
	"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `$record_db`.`facility_id` ".
	"LEFT JOIN `invoices` ON `invoices`.`invoice_id` = `$record_db`.`invoice_id`".
	$wheretxt." ORDER BY `$record_db`.`date_added` DESC";
	$query = $db->query($querytxt, $params);
	if($query && !$db->error() && $db->num_rows() > 0){
		$result = $db->fetch_array();
		foreach($result as $row){
			//$row['event_date'] = format_date_range($row['start_date'], $row['end_date']);
			
			//Get event dates
			$datesquery = $db->query("SELECT `event_date` FROM `hio_dates` WHERE `$record_id` = ? ORDER BY `event_date` DESC", array($row[$record_id]));
			if($datesquery && !$db->error()){
				$eventdates = $db->fetch_array();
				foreach($eventdates as $eventdate){
					$row['event_date'] = $eventdate['event_date'];
					$row['premium_total'] = number_format($premium_total/count($eventdates), 2);
					
					//Get individual holes
					$holesquery = $db->query("SELECT `hio_courses`.`course_name`, `hio_holes`.* FROM `hio_holes` LEFT JOIN `hio_courses` ON `hio_holes`.`course_id` = `hio_courses`.`course_id` WHERE `hio_courses`.`$record_id` = ? GROUP BY `hio_holes`.`hole_id` ORDER BY `hio_holes`.`hole` ASC", array($row[$record_id]));
					if($holesquery && !$db->error()){
						$eventholes = $db->fetch_array();
						foreach($eventholes as $eventhole){
							
							$row['facility_name'] = $eventhole['course_name'];
							$row['hole'] = $eventhole['hole'];
							$row['yardage'] = (!empty($eventhole['yards_men']) ? 'Men: '.$eventhole['yards_men'].' ' : '').(!empty($eventhole['yards_women']) ? 'Women: '.$eventhole['yards_women'].' ' : '');
							$row['prize_total'] = $eventhole['prize'];
							$row['premium_total'] = $eventhole['premium'];
							
							//Push each line to array
							$records_arr[] = $row;
						}
					}
					
					
				}
			}
			
		}
	}

	//Compile records
	$csv_rows = array();
	foreach($records_arr as $row) {
		$data = array();
		foreach($db_columns as $column) {
			if($column == 'date_approved' || $column == 'date_added' || $column == 'event_date') {
				$data[] = ($row[$column] != '' && $row[$column] != '0000-00-00' && $row[$column] != '0000-00-00 00:00:00' ? date('M j, Y', strtotime($row[$column])) : "");
			} else if($column == 'approved') {
				$data[] = ($row[$column] == '1' ? 'Yes' : 'No');
			} else if($column == 'prize_total' || $column == 'premium_total') {
				$data[] = '$'.number_format($row[$column], 2);
			} else {
				$data[] = $row[$column];
			}
		}
		$csv_rows[] = $data;
	}

	//Output CSV
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=holeinone-".date("Ymd").".csv");
	header("Content-Transfer-Encoding: binary ");
	
	$fp = fopen('php://output', 'w');
	
	//Data
	fputcsv($fp, str_replace("&rsquo;", "'", $table_columns));
	foreach($csv_rows as $row) {
		fputcsv($fp, str_replace("&rsquo;", "'", $row));
	}
	fputcsv($fp, array(''));
	
	//Footer
	$footer = array('Date Exported: ', date('M j, Y'));
	fputcsv($fp, $footer);
	$footer = array('Status Filter: ', 'Approved');
	fputcsv($fp, $footer);
	if(!empty($date_range)){
		$footer = array('Date Filter: ', $date_range);
		fputcsv($fp, $footer);
	}
	if(isset($_GET['search']) && $_GET['search'] != ""){
		$footer = array('Search Filter: ', '`'.str_replace("&rsquo;", "'", $_GET['search']).'`');
		fputcsv($fp, $footer);
	}
	
	fclose($fp);

} 

?>