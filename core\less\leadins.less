/*------ leadin ------*/

// Themes
.leadin-popup{
	--leadin-bg: var(--theme-bg, @color-light);
	
	&.theme-theme1{
		--leadin-color: @color-light;
		--leadin-title-color: @color-light;
		--leadin-border-color: @color-light;
	}
	&.theme-theme2{
		--leadin-color: @color-light;
		--leadin-title-color: @color-light;
		--leadin-border-color: @color-light;
	}
	&.theme-gradient{
		--leadin-color: @color-light;
		--leadin-title-color: @color-light;
		--leadin-border-color: @color-light;
	}
	&.theme-black{
		--leadin-color: @color-light;
		--leadin-title-color: @color-light;
		--leadin-border-color: @color-light;
	}
	&.theme-white{
		--leadin-color: @font-color;
		--leadin-title-color: @color-theme1;
		--leadin-border-color: @color-theme1;
	}
}

// Leadin
.leadin-popup{
	display: block;
	max-width: 100%;
	color: var(--leadin-color);
	background: var(--leadin-bg);
	visibility: hidden;
	z-index: 100;

	&.open{
		visibility: visible;
	}

	.container{
		position: relative;
	}

	.leadin-success{
		display: none;
	}

	.leadin-title{
		color: var(--leadin-title-color);
	}

	.control-buttons{
		position: absolute;
		top: 0;
		right: 0;
		text-align: right;

		.control-button{
			width: 20px;
			line-height: 20px;
			text-align: center;
		}
	}

	.close-button{
		display: block;
		color: var(--leadin-title-color);
		opacity: 0.7;
		font-style: normal; 
		text-decoration: none;
		cursor: pointer;
		font-size: 15px;
		.trans(opacity);

		&:hover{
			opacity: 1;
		}

		&::before{
			.font-awesome(f00d);
		}
		
		&.theme-white &{
			color: var(--leadin-title-color);
		}
	}

	&.position-top{
		top: 0;
	}
	&.position-bottom{
		bottom: 0;
	}
	&.position-left{
		left: 0;
	}
	&.position-right{
		right: 0;
	}

	&.type-corner .leadin-form-wrapper,
	&.type-bar .leadin-form-wrapper{
		display: none;
	}

	&.type-corner,
	&.type-bar.position-bottom{
		position: fixed;
	}

	&.type-corner{
		top: auto;
		bottom: 0;
		width: 420px;
		max-width: calc(100% - 20px);
		padding: 20px;
		font-size: 16px;
		.box-shadow(0; 0; 10px; 0;);
		
		.leadin-img-wrapper{
			display: none;
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			margin: -20px 0 -20px -20px;
			width: 140px;
			height: auto;
			overflow: visible;
			border-radius: 0;

			img{
				width: 100%; 
				height: 100%; 
				max-width: none; 
			}

			~ .leadin-content,
			~ .leadin-success{
				float: none;
				width: 100%;
			}
		}

		.leadin-title{
			padding-right: 20px;
			font-size: 18px;
		}

		.container{
			padding: 0;
		}
		.control-buttons{
			top: -3px;
		}
	}

	&.type-bar{
		width: 100%;
		padding: 10px 0px;
		font-size: 16px;

		.control-buttons{
			right: 20px;
			height: 100%;
			.flexbox(@cross: flex-start);
		}

		.leadin-title{
			padding: 5px 0;
			margin: 0;
			font-size: inherit;
		}

		&.position-top{
			border-bottom: 1px solid var(--leadin-border-color);
		}
	}

	&.type-popup{
		--leadin-popup-padding: 20px;
		display: none;
		padding: var(--leadin-popup-padding);

		.container{padding: 0; }

		.leadin-title{
			&:extend(.ui-dialog .ui-dialog-titlebar .ui-dialog-title);
		}

		.leadin-img-wrapper img{
			display: block;
			width: calc(100% + var(--leadin-popup-padding) * 2);
			max-width: none;
			margin: calc(-1 * var(--leadin-popup-padding)) calc(-1 * var(--leadin-popup-padding)) 0;
		}

		.leadin-form,
		.leadin-success{
			padding: 20px 0 0 0;
		}

		&:not(.noimage){
			.control-buttons{
				top: var(--leadin-popup-padding);
				.text-shadow();
			}

			.leadin-content{
				padding-top: var(--leadin-popup-padding);
			}
		}
	}
}
@media @tablet-l{
	.leadin-popup{
		&.type-bar{
			.container{
				padding-right: 50px;
			}

			.control-buttons{
				align-items: center;
			}

			.leadin-success,
			.content-wrapper{
				.flexbox(row wrap; center; baseline); 
				gap: 10px;
				text-align: center;

				> *{
					margin: auto 0;
				}

				p{
					padding: 0;
				}
			}
			.leadin-title{
				padding-right: 10px;
			}
		}
		&.type-corner{
			.leadin-img-wrapper{
				display: block; 

				~ .leadin-content,
				~ .leadin-success{
					padding-left: 135px;
				}
			}
		}
	}
}
@media @notebook{
	.leadin-popup.type-bar.position-top{
		position: relative;
		z-index: 1000;
	}
}

// Dialog
.leadin-popup-widget{
	border: 0;
}
.leadin-popup-widget .ui-dialog-titlebar{
	display: none;
}
.leadin-popup.ui-dialog-content{
	&:extend(.leadin-popup all);
} // Extend styles to dialog, ensure specificity

// Form
.leadin-form{
	margin: 0;
}