<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<a href="' .PAGE_URL. '?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	echo '<div class="panel">
		<div class="panel-header">' .$records_name. '
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
				
				<thead>
					<th width="1px" class="nopadding-r" data-sorter="false"></th>
					<th width="1px" class="nopadding-r" data-sorter="false"></th>
					<th>Title</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				foreach($records_arr as $row){
					echo '<tr>
						<td class="nopadding-r">'.render_gravatar($imagedir.'thumbs/'.$row['image'], $imagedir.'thumbs/'.$row['image'], $row['title']).'</td>
						<td class="nopadding-r"><i class="' .$row['icon']. ' color-theme3"></i></td>
						<td>' .$row['title']. '</td>
						<td>' .$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']). '</td>
						<td class="right"><a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
					</tr>';	
				}
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';


//Image cropping
}else if($CMSUploader->crop_queue()){
	include("includes/jcropimages.php");
	

//Display form	
}else{
	$image = '';
	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = $data['image'];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';

	//Promo details
	echo '<div class="panel">
		<div class="panel-header">' .$record_name. ' Details
			<span class="panel-toggle fas fa-chevron-up"></span>
			<div class="panel-switch">
				<label>Show ' .$record_name. '</label>
				<div class="onoffswitch">
					<input type="checkbox" name="showhide" id="showhide" value="0"' .(($row['showhide'] ?? 0) ? '' : ' checked'). ' />
					<label for="showhide">
						<span class="inner"></span>
						<span class="switch"></span>
					</label>
				</div>
			</div>
		</div>
		<div class="panel-content">
			<div class="flex-container">
		
				<div class="form-field">
					<label>' .$record_name. ' Title' .(in_array('title', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="title" value="' .($row['title'] ?? ''). '" class="input' .(in_array('title', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Icon' .(in_array('icon', $required_fields) ? ' <span class="required">*</span>' : '').
						$CMSBuilder->tooltip('Icon', 'Select an icon for this promo box. Icons will only be visible when the panel\'s &quot;Promo Box Type&quot; is set to &quot;Mini&quot;.'.(!empty($default_icon) ? '<br/><br/><small>Defaults to <i class=\''.$default_icon.'\'></i> ('.$default_icon.') if left blank.</small>' : '').'').
					'</label>
					<div class="iconpicker-container">
						<input type="text" name="icon" value="'.($row['icon'] ?? '').'" class="input iconpicker'.(in_array('icon', $required) ? ' required' : '').'" autocomplete="nope" placeholder="Type to search icon">
						<span class="iconpicker-component"></span>
					</div>
				</div>

				<div class="form-field">
					<label>Short Description' .(in_array('description', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="description" value="' .($row['description'] ?? ''). '" class="input' .(in_array('description', $required) ? ' required' : ''). '" />
				</div>

			</div>

			<div class="flex-container">

				<div class="form-field">
					<label>' .$record_name. ' Link <small>(URL, Phone, or Email)</small>' .(in_array('url', $required_fields) ? ' <span class="required">*</span>' : '').
						$CMSBuilder->tooltip($record_name.' Link', 'If a link is provided, a clickable button will be displayed.<br /><br /><strong>URL</strong> - User will be taken to this link.<br /><strong>Phone</strong> - User will be prompted to call the phone number provided.<br />Example: (*************.<br /><strong>Email</strong> - User will be prompted to emaill the address provided.').
						$CMSBuilder->tooltip('Sitemap Reference', 'Click on the icon to view the sitemap. You can use the sitemap pop up as your reference when entering links.', '<span class="fas fa-sitemap"></span>', array('sitemap-reference')). '
					</label>
					<input type="text" name="url" value="' .($row['url'] ?? ''). '" class="input' .(in_array('url', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Link Text' .(in_array('url_text', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Link Text', $record_name.' link will be displayed with this text. Defaults to &quot;Learn More&quot;.'). '</label>
					<input type="text" name="url_text" value="' .($row['url_text'] ?? ''). '" class="input' .(in_array('url_text', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Open Link in' .(in_array('url_target', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<select name="url_target" class="select' .(in_array('url_target', $required) ? ' required' : ''). '">
						<option value="0"' .(!($row['url_target'] ?? 0) ? ' selected' : ''). '>Same Window</option>
						<option value="1"' .(($row['url_target'] ?? 0) ? ' selected' : ''). '>New Window</option>
					</select>
				</div>

			</div>
		</div>
	</div>'; //Promo details

	//Promo image
	echo '<div class="panel">
		<div class="panel-header">' .$record_name. ' Image
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">'.
				$CMSBuilder->img_holder($image, $imagedir.'thumbs/', NULL, false).

				'<div class="form-field">
					<label>Upload Image <span class="required">*</span>' .$CMSBuilder->tooltip('Upload Image', 'Image must be at least '.$CMSUploader::size_label('promo', 'image').' and smaller than '.$_max_filesize['megabytes']. '.'). '</label>
					<input type="file" class="input' .(in_array('image', $required) ? ' required' : ''). '" name="image" value="" />
				</div>
				
				<div class="form-field">
					<label>Alt Text <small>(SEO)</small>' .(in_array('image_alt', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.'). '</label>
					<input type="text" name="image_alt" value="' .($row['image_alt'] ?? ''). '" class="input' .(in_array('image_alt', $required) ? ' required' : ''). '" />
				</div>
				
			</div>
		</div>
	</div>'; //Promo image

	//
	// Mini Image Section
	echo '<div class="panel">
	<div class="panel-header">Mini Promo Image
		<span class="panel-toggle fas fa-chevron-up"></span>
	</div>
	<div class="panel-content">
		<div class="flex-container">'.
			$CMSBuilder->img_holder($row['mini_image'] ?? '', $imagedir.'thumbs/', NULL, false).

			'<div class="form-field">
				<label>Upload Image' .$CMSBuilder->tooltip('Upload Image', 'Optional mini promo image. Must be smaller than '.$_max_filesize['megabytes']. '.'). '</label>
				<input type="file" class="input" name="mini_image" value="" />
			</div>
			
			<div class="form-field">
				<label>Alt Text <small>(SEO)</small>' .$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this mini promo image.'). '</label>
				<input type="text" name="mini_image_alt" value="' .($row['mini_image_alt'] ?? ''). '" class="input" />
			</div>
			
		</div>
	</div>
	</div>';
	//

	//Sticky footer
	include("includes/widgets/formbuttons.php");

	echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

}

?>