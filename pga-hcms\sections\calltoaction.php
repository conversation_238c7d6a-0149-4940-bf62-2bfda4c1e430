<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){
	
	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<a href="' .PAGE_URL. '?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">' .$record_name. 's
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
			
				<thead>'.
					(CTA_IMAGE ? '<th width="1px" class="nopadding-r" data-sorter="false"></th>' : '').
					'<th width="auto">Title</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				foreach($records_arr as $row){
					echo '<tr>'.
						(CTA_IMAGE ? '<td class="nopadding-r">'.render_gravatar($imagedir.'480/'.$row['image'], $imagedir.'1920/'.$row['image'], $row['title']).'</td>' : '').
						'<td>' .$row['title'].($row['subtitle'] ? '<br /><small>' .truncate($row['subtitle'], 125). '</small>' : ''). '</td>
						<td>' .$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']). '</td>
						<td class="right"><a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td
					</tr>';
				}
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';
	

//Image cropping
}else if($CMSUploader->crop_queue()){
	include("includes/jcropimages.php");
	

//Position images
}else if($CMSUploader->position_queue()){
	include("includes/positionimages.php");
	

//Display form	
}else{
	
	$image = '';		
	$image_mobile = '';	

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = check_file($data['image'], $imagedir);
		$image_mobile = check_file($data['image_mobile'], $imagedir);
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';

	//CTA details
	echo '<div class="panel">
		<div class="panel-header">' .$record_name. ' Details
			<span class="panel-toggle fas fa-chevron-up"></span>
			<div class="panel-switch">
				<label>Show ' .$record_name. '</label>
				<div class="onoffswitch">
					<input type="checkbox" name="showhide" id="showhide" value="0"' .(($row['showhide'] ?? 0) ? '' : ' checked'). ' />
					<label for="showhide">
						<span class="inner"></span>
						<span class="switch"></span>
					</label>
				</div>
			</div>
		</div>
		<div class="panel-content">
			<div class="flex-container">
			
				<div class="form-field">
					<label>Title' .(in_array('title', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Title', 'Put special emphasis on a word or phrase by enclosing it in {curly brackets}.'). '</label>
					<input type="text" name="title" value="' .($row['title'] ?? ''). '" class="input' .(in_array('title', $required) ? ' required' : ''). '" />
				</div>
				
				<div class="form-field">
					<label>Description' .(in_array('subtitle', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="subtitle" value="' .($row['subtitle'] ?? ''). '" class="input' .(in_array('subtitle', $required) ? ' required' : ''). '" />
				</div>
	
			</div>
		</div>
	</div>'; //CTA details

	//Links
	echo '<div class="page-content">
		<div class="tabs tab-ui">
			<ul>
				<li><a href="#link1">Button 1</a></li><li><a href="#link2">Button 2</a></li>
			</ul>
		
			<div id="link1">
				<div class="flex-container">
		
					<div class="form-field">
						<label>Button Link <small>(URL, Phone, or Email)</small>' .(in_array('url', $required_fields) ? ' <span class="required">*</span>' : '').
							$CMSBuilder->tooltip('Button Link', 'If a link is provided, a clickable button will be displayed.<br /><br /><strong>URL</strong> - User will be taken to this link.<br /><strong>Phone</strong> - User will be prompted to call the phone number provided.<br />Example: (*************.<br /><strong>Email</strong> - User will be prompted to emaill the address provided.').$CMSBuilder->tooltip('Sitemap Reference', 'Click on the icon to view the sitemap. You can use the sitemap pop up as your reference when entering links.', '<span class="fas fa-sitemap"></span>', array('sitemap-reference')). '
						</label>
						<input type="text" name="url" value="' .($row['url'] ?? ''). '" class="input' .(in_array('url', $required) ? ' required' : ''). '" />
					</div>
		
					<div class="form-field">
						<label>Button Text' .(in_array('url_text', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Button Text', 'Button will be displayed with this text. Defaults to &quot;Learn More&quot;.'). '</label>
						<input type="text" name="url_text" value="' .($row['url_text'] ?? ''). '" class="input' .(in_array('url_text', $required) ? ' required' : ''). '" />
					</div>
		
					<div class="form-field">
						<label>Open Link in' .(in_array('url_target', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<select name="url_target" class="select' .(in_array('url_target', $required) ? ' required' : ''). '">
							<option value="0"' .(!($row['url_target'] ?? 0) ? ' selected' : ''). '>Same Window</option>
							<option value="1"' .(($row['url_target'] ?? 0) ? ' selected' : ''). '>New Window</option>
						</select>
					</div>
		
				</div>
			</div>
		
			<div id="link2">
				<div class="flex-container">
					
					<div class="form-field">
						<label>Button Link <small>(URL, Phone, or Email)</small>' .(in_array('url2', $required_fields) ? ' <span class="required">*</span>' : '').
							$CMSBuilder->tooltip('Button Link', 'If a link is provided, a clickable button will be displayed.<br /><br /><strong>URL</strong> - User will be taken to this link.<br /><strong>Phone</strong> - User will be prompted to call the phone number provided.<br />Example: (*************.<br /><strong>Email</strong> - User will be prompted to emaill the address provided.').$CMSBuilder->tooltip('Sitemap Reference', 'Click on the icon to view the sitemap. You can use the sitemap pop up as your reference when entering links.', '<span class="fas fa-sitemap"></span>', array('sitemap-reference')). '</label>
						<input type="text" name="url2" value="' .($row['url2'] ?? ''). '" class="input' .(in_array('url2', $required) ? ' required' : ''). '" />
					</div>
		
					<div class="form-field">
						<label>Button Text' .(in_array('url_text2', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Button Text', 'Button will be displayed with this text. Defaults to &quot;Learn More&quot;.'). '</label>
						<input type="text" name="url_text2" value="' .($row['url_text2'] ?? ''). '" class="input' .(in_array('url_text2', $required) ? ' required' : ''). '" />
					</div>
		
					<div class="form-field">
						<label>Open Link in' .(in_array('url_target2', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<select name="url_target2" class="select' .(in_array('url_target2', $required) ? ' required' : ''). '">
							<option value="0"' .(!($row['url_target2'] ?? 0) ? ' selected' : ''). '>Same Window</option>
							<option value="1"' .(($row['url_target2'] ?? 0) ? ' selected' : ''). '>New Window</option>
						</select>
					</div>
		
				</div>
			</div>
		
		</div>
	</div>'; //Links

	//CTA image
	echo '<div class="panel '.(CTA_IMAGE ? '' : ' hidden').'">
		<div class="panel-header">Background Image
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">';
	
			//Upload Image (Desktop)
			echo '<div class="flex-container">';
				echo $CMSBuilder->img_holder($image, $imagedir.'1920/', $imagedir.'1024/');

				echo '<div class="form-field">
					<label>Upload Image (Desktop)' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploader::size_label('cta', 'image'). ' and file size must be smaller than '.$_max_filesize['megabytes'].'.'). '</label>
					<input type="file" class="input' .(in_array('image', $required) ? ' required' : ''). '" name="image" value="" />
				</div>
			</div>';

			echo '<hr />';
	
			//Upload Image (Mobile)
			echo '<div class="flex-container">';
				echo $CMSBuilder->img_holder($image_mobile, $imagedir.'768/', $imagedir.'480/', true, 'image_mobile');

				echo '<div class="form-field">
					<label>Upload Image (Mobile)' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploader::size_label('cta', 'image_mobile'). ' and file size must be smaller than '.$_max_filesize['megabytes'].'.'). '</label>
					<input type="file" class="input' .(in_array('image_mobile', $required) ? ' required' : ''). '" name="image_mobile" value="" />
				</div>
			</div>';
	
		echo '</div>
	</div>'; //CTA image

	//Sticky footer
	include("includes/widgets/formbuttons.php");

	echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

}

?>