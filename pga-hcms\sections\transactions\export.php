<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Get GL Accounts
$glaccounts = array();
$gldefaults = array();
$query = $db->query("SELECT * FROM `gl_accounts` ORDER BY `gl_number`");
if($query && !$db->error()){
	$result = $db->fetch_array();
	foreach($result as $row){
		$glaccounts[$row['gl_id']] = $row;
		if(!empty($row['item_no'])){
			$gldefaults[$row['item_no']] = $row;
		}
	}
}
	
//Search
echo "<div class='panel'>";
	echo "<div class='panel-header'>Export Transactions
		<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
	</div>";

	echo "<div class='panel-content clearfix'>";
		echo "<form id='advanced-search-form' class='clearfix' action='".$path."exports/export-transactions.php' method='get' enctype='multipart/form-data' target='_blank'>";
			echo "<div id='search-fields' class='column clearfix'>";
				echo "<div class='form-field'>
					<label>Start Date </label>
					<input type='text' name='start_date' value='".date('Y-m-d', strtotime('-1 year'))."' class='input nomargin datepicker' autocomplete='off' />
				</div>";
				echo "<div class='form-field'>
					<label>End Date </label>
					<input type='text' name='end_date' value='".date('Y-m-d')."' class='input nomargin datepicker' autocomplete='off' />
				</div>";
				echo "<div class='form-field'>
					<label>Status </label>
					<select name='status' class='select'>
						<option value=''>All</option>
						<option value='1'>Processed</option>
						<option value='0'>Failed</option>
					</select>
				</div>";
				echo "<div class='form-field'>
					<label>GL Account </label>
					<select name='gl_account' class='select'>
						<option value=''>All</option>";
						foreach($glaccounts as $gl){
							echo "<option value='" .$gl['gl_number']. "'>" .$gl['gl_name']. "</option>";
						}
					echo "</select>
				</div>";
			echo "</div>";
			echo "<div class='buttons-wrapper'>";
				echo "<div class='f_left'>";
					echo "<button type='submit' class='button'><i class='fa fa-download'></i>Export</button> &nbsp;";
				echo "</div>";
			echo "</div>";

		echo "</form>";
	echo "</div>";
echo "</div>";


	echo "</div>";	
echo "</div>";

?>