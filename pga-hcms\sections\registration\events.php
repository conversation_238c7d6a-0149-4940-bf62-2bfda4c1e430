<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	echo "<form id='search-form' class='multiple-search clearfix' action='' method='get'>";
		if(!empty($all_categories)) {
			echo "<select class='select f_left' name='category_id'>
				<option value=''>All Categories</option>";
				foreach($all_categories as $cat) {
					echo "<option value='".$cat['category_id']."'".(isset($_SESSION['search_category_id'][SECTION_ID]) && $_SESSION['search_category_id'][SECTION_ID] == $cat['category_id'] ? " selected" : "").">" .$cat['name']. "</option>";
				}
			echo "</select>";
		}
		echo "<div class='f_left relative'>
			<input type='text' name='search' class='input f_left' value='$searchterm' placeholder='Search' />
			".($searchterm != "" ? "<a id='clear-search'><i class='fa fa-times-circle'></i></a>" : "")."
		</div>";
		echo "<button type='button' class='button' onclick='this.form.submit();'><i class='fa fa-search'></i></button>
	</form>
	<form id='clear-search-form' name='clear-search-form' class='hidden' action='".PAGE_URL."' method='post'>
		<input type='hidden' name='clear-search' value='Clear' />
		<input type='hidden' name='search' value='' />
		<input type='hidden' name='xssid' value='".$_COOKIE['xssid']."' />
	</form>";

	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add&step=1' class='button'><i class='fa fa-plus'></i>Add New</a></p>";

	//Upcoming
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Current ".EVENT_CODE."s
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

			echo "<thead>";
			echo "<th width='40px' class='{sorter:false}'></th>";
			echo "<th width='90px'>Name</th>";
			echo "<th width='90px'>Category</th>";
			echo "<th width='90px'>Location</th>";
			echo "<th width='120px' class='{sorter:\"monthDayYear\"}'>Start Date</th>";
			echo "<th width='120px' class='{sorter:\"monthDayYear\"}'>End Date</th>";
			echo "<th width='120px' class='center'>" .(EVENT_TYPE == 2 ? 'Field' : 'Capacity'). "</th>";
			echo "<th width='70px'>Visible</th>";
			echo "<th width='150px' class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";

			echo "<tbody>";
			foreach($current_records as $row){
				echo "<tr>";
					echo "<td>" .($row['banner_image'] != "" ? "<a href='" .$path.$imagedir.$imagesizes[0]['dir'].$row['banner_image']. "' class='light-gallery' title='" .$row['name']. "'>" .renderGravatar($imagedir.$imagesizes[4]['dir'].$row['banner_image']). "</a>" : ""). "</td>";
					echo "<td>" .$row['name']. "</td>";
					echo "<td>" .(isset($all_categories[$row['category_id']]) ? $all_categories[$row['category_id']]['name'] : "")."</td>";
					if(EVENT_TYPE == 2){
						echo "<td>" .$facilities[$row['facility_id']]['facility_name']. "</td>";
					}else{
						echo "<td>" .$row['location_name']. "</td>";
					}
					echo "<td>" .date('M j, Y', strtotime($row['start_date'])). "</td>";
					echo "<td>" .date('M j, Y', strtotime($row['end_date'])). "</td>";
					echo "<td class='center'>" .$row['attendance'].'/'.($row['max_capacity'] != "" ? $row['max_capacity'] : "&infin;"). "</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'>
						<a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "&step=1' class='button-sm'><i class='fa fa-pencil'></i>Edit</a>
						<a href='" .$sitemap[$_cmssections['registration-attendees']]['page_url']. "?action=add&event=" .$row['occurrence_id']. "' class='button-sm'><i class='fa fa-user-plus nomargin'></i></a>
					</td>";
				echo "</tr>";
			}
			echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager(25);

		echo "</div>";
	echo "</div><br />";

	//Archived
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Archived ".EVENT_CODE."s
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

			echo "<thead>";
			echo "<th width='40px' class='{sorter:false}'></th>";
			echo "<th width='auto'>Name</th>";
			echo "<th width='auto'>Category</th>";
			echo "<th width='300px'>Location</th>";
			echo "<th width='120px' class='{sorter:\"monthDayYear\"}'>Start Date</th>";
			echo "<th width='120px' class='{sorter:\"monthDayYear\"}'>End Date</th>";
			echo "<th width='120px' class='center'>" .(EVENT_TYPE == 2 ? 'Field' : 'Capacity'). "</th>";
			echo "<th width='70px'>Visible</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";

			echo "<tbody>";
			foreach($archived_records as $row){
				echo "<tr>";
					echo "<td>" .($row['banner_image'] != "" ? "<a href='" .$path.$imagedir.$imagesizes[0]['dir'].$row['banner_image']. "' class='light-gallery' title='" .$row['name']. "'>" .renderGravatar($imagedir.$imagesizes[4]['dir'].$row['banner_image']). "</a>" : ""). "</td>";
					echo "<td>" .$row['name']. "</td>";
					echo "<td>" .(isset($all_categories[$row['category_id']]) ? $all_categories[$row['category_id']]['name'] : "")."</td>";
					if(EVENT_TYPE == 2){
						echo "<td>" .$facilities[$row['facility_id']]['facility_name']. "</td>";
					}else{
						echo "<td>" .$row['location_text']. "</td>";
					}
					echo "<td>" .date('M j, Y', strtotime($row['start_date'])). "</td>";
					echo "<td>" .date('M j, Y', strtotime($row['end_date'])). "</td>";
					echo "<td class='center'>" .$row['attendance'].'/'.($row['max_capacity'] != "" ? $row['max_capacity'] : "&infin;"). "</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "&step=1' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";
			}
			echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager(25);

		echo "</div>";
	echo "</div>";


//Image cropping
// }else if(count($cropimages) > 0){
}else if($CMSUploader->crop_queue()){
	include("includes/jcropimages.php");

//Event form
}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = $data['banner_image'];
		if(IMAGE_ID != "") {
			$current_image = $data['images'][IMAGE_ID];
		}

		if(!isset($_POST['save'])){
			$row = $data;

			if(IMAGE_ID != "") {
				$row = $current_image;
			}
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		$image = '';
		unset($row);
	}

	if(ITEM_ID != '' && IMAGE_ID == ''){
		echo "<div class='clearfix'>";
			//Show/Hide Toggle
			echo "<div class='panel-switch panel-switch-lg f_left'>
				<label>Show ".EVENT_CODE."</label>
				<div class='onoffswitch'>
					<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " class='ajax-showhide' data-table='".$record_db."' data-tableid='".$record_id."' data-itemid='".ITEM_ID."' data-itemcol='showhide' />
					<label for='showhide'>
						<span class='inner'></span>
						<span class='switch'></span>
					</label>
				</div>
			</div>";

			//Duplicate
			if(EVENT_TYPE === 1){
				echo "<form class='f_right' action='' method='post' enctype='multipart/form-data'>
					<div class='column left'>
						<button type='submit' name='duplicate' class='button copy'><i class='fa fa-copy'></i>Duplicate Event</button>
					</div>
					<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />
				</form>";
			}
		echo "</div>";
	}

	if(ITEM_ID != ''){
		echo "<p class='f_right'>
			<a href='" .$sitemap[$_cmssections['registration-attendees']]['page_url']. "?filter[reg_attendees][reg_status]=Registered&filter[reg_events][name]=".urlencode($row['name'])."&filter[reg_occurrences][start_date]=".$row['start_date']."&filter[reg_occurrences][end_date]=".$row['end_date']."&column[reg_events][name]=Event&column[reg_occurrences][occurrence_id]=Event+Date&column[reg_attendees][email]=Email&column[reg_attendees][phone]=Phone&advanced_search=advanced_search' class='button'><i class='fa fa-users'></i> Attendees</a>
			<a href='" .$sitemap[$_cmssections['registration-waiting-list']]['page_url']. "?event_id=".ITEM_ID."&occurrence_id=".$row['occurrence_id']."' class='button'><i class='fa fa-hourglass-start'></i> Waitlist</a>
			<a href='" .$sitemap[$_cmssections['registration-attendees']]['page_url']. "?action=add&event=".$row['occurrence_id']."' class='button'><i class='fa fa-user-plus'></i> Register</a>
		</p>";
	}

	//Steps Nav
	include("includes/widgets/stepsnav.php");

	//Event Info
	if(STEP == 1){

		echo "<form id='event1_form' action='' method='post' enctype='multipart/form-data'>";

			//Details
			echo "<div class='panel'>";
				echo "<div class='panel-header'>$record_name Details
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content clearfix'>";
					echo "<div class='form-field'>
						<label>$record_name Name <span class='required'>*</span></label>
						<input type='text' name='name' value='" .(isset($row['name']) ? $row['name'] : ''). "' class='input" .(in_array('name', $required) ? ' required' : ''). "' />
					</div>";
					echo "<div class='form-field'>
						<label>".EVENT_CODE." Category <span class='required'>*</span></label>
						<select name='category_id' class='select" .(in_array('category_id', $required) ? ' required' : ''). "'>
						<option value=''>- Select -</option>";
						foreach($all_categories as $cat) {
							echo "<option value='".$cat['category_id']."'".(isset($row['category_id']) && $row['category_id'] == $cat['category_id'] ? " selected" : "").">" .$cat['name']. "</option>";
						}
						echo "</select>
					</div>";
					if(EVENT_TYPE == 2){
						echo "<div class='form-field'>
							<label>Tournament Type ".$CMSBuilder->tooltip('Tournament Type', 'If team is selected, members will be prompted to select their partner on registration.')."</label>
							<select name='team_event' class='select'>
								<option value='0'".(isset($row['team_event']) && $row['team_event'] == '0' ? " selected" : "").">Individual</option>
								<option value='1'".(isset($row['team_event']) && $row['team_event'] == '1' ? " selected" : "").">Team</option>
							</select>
						</div>";
					}
					echo "<div class='form-field'>
						<label>Attendee Information Sharing ".$CMSBuilder->tooltip('Attendee Information Sharing', 'Enable the ability to display attendee lists on the tournament page. If set to &ldquo;Default&rdquo; will default to the information sharing setting in Registration Settings.')."</label>
						<select name='attendee_sharing' class='select'>
							<option value='-1'".(isset($row['attendee_sharing']) && $row['attendee_sharing'] == '-1' ? " selected" : "").">Default</option>
							<option value='1'".(isset($row['attendee_sharing']) && $row['attendee_sharing'] == '1' ? " selected" : "").">Enabled</option>
							<option value='0'".(isset($row['attendee_sharing']) && $row['attendee_sharing'] == '0' ? " selected" : "").">Disabled</option>
						</select>
					</div>";
					echo "<div class='form-field'>
						<label>Waiting List ".$CMSBuilder->tooltip('Waiting List', 'Enable the ability for attendees to be placed on a waiting list if the ' .EVENT_CODE. ' is full.')."</label>
						<select name='waiting_list' class='select'>
							<option value='1'".(isset($row['waiting_list']) && $row['waiting_list'] == '1' ? " selected" : "").">Enabled</option>
							<option value='0'".(isset($row['waiting_list']) && $row['waiting_list'] == '0' ? " selected" : "").">Disabled</option>
						</select>
					</div>";
					echo "<div class='form-field'>
						<label>Attach Gallery</label>
						<select name='gallery_id' class='select" .(in_array('gallery_id', $required) ? ' required' : ''). "'>
							<option value=''>- None -</option>";
							foreach($galleries as $gallery){
								echo "<option value='" .$gallery['gallery_id']. "'" .((isset($row['gallery_id']) && $row['gallery_id'] == $gallery['gallery_id'] ? ' selected' : '')). ">" .$gallery['name']. "</option>";
							}
						echo "</select>
					</div>";
					echo "<div class='form-field'>
						<label>Call to Action</label>
						<select name='cta_id' class='select" .(in_array('cta_id', $required) ? ' required' : ''). "'>
							<option value=''>- None -</option>";
							foreach($ctas as $cta){
								echo "<option value='" .$cta['cta_id']. "'" .((isset($row['cta_id']) && $row['cta_id'] == $cta['cta_id'] ? ' selected' : '')). ">" .$cta['title']. "</option>";
							}
						echo "</select>
					</div>";

					//Event fields
					if(EVENT_TYPE != 2){

						echo "<hr class='clear' />";

						echo "<div class='form-field'>
							<label>Maximum Capacity " .$CMSBuilder->tooltip('Maximum Capacity', 'Enter the maximum number of attendees for the entire event. Leave blank for no limit.'). "</label>
							<input type='text' name='max_capacity' value='" .(isset($row['max_capacity']) ? $row['max_capacity'] : ''). "' class='input number" .(in_array('max_capacity', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>Maximum Attendees " .$CMSBuilder->tooltip('Maximum Attendees', 'Enter the maximum number of attendees allowed per registration. Leave blank for no limit.'). "</label>
							<input type='text' name='max_attendees' value='" .(isset($row['max_attendees']) ? $row['max_attendees'] : ''). "' class='input number" .(in_array('max_attendees', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>Eligibility</label>
							<select name='role_id' class='select" .(in_array('role_id', $required) ? ' required' : ''). "'>
								<option value=''>Members &amp; Non-Members</option>
								<option value='2'" .((isset($row['role_id']) && $row['role_id'] == 2 ? ' selected' : '')). ">Members Only</option>
								<option value='7'" .((isset($row['role_id']) && $row['role_id'] == 7 ? ' selected' : '')). ">Non-Members Only</option>
							</select>
						</div>";

					//Tournament fields
					}else if(EVENT_TYPE == 2){

						echo "<hr class='clear' />";

						echo "<div class='form-field'>
							<label>Facility <span class='required'>*</span></label>
							<select name='facility_id' class='select" .(in_array('facility_id', $required) ? ' required' : ''). "'>
							<option value=''>- Select -</option>";
							foreach($facilities as $facility){
								echo "<option value='" .$facility['facility_id']. "'" .(isset($row['facility_id']) && $row['facility_id'] == $facility['facility_id'] ? ' selected' : ''). ">" .$facility['facility_name']. "</option>";
							}
							echo "</select>
						</div>";
						echo "<div class='form-field'>
							<label>Field <span class='required'>*</span></label>
							<input type='text' name='max_capacity' value='" .(isset($row['max_capacity']) ? $row['max_capacity'] : ''). "' class='input number" .(in_array('max_capacity', $required) ? ' required' : ''). "' />
						</div>";

						$draw_types = $db->get_enum_vals('reg_occurrences', 'draw_type');
						echo "<div class='form-field'>
							<label>Draw Type</label>
							<select name='draw_type' class='select" .(in_array('draw_type', $required) ? ' required' : ''). "'>
								<option value=''>- Select -</option>";
								foreach($draw_types as $draw){
									echo "<option value='" .$draw. "'" .(isset($row['draw_type']) && $row['draw_type'] == $draw ? " selected" : ""). ">" .$draw. "</option>";
								}
							echo "</select>
						</div>";

						$scoring_types = $db->get_enum_vals('reg_occurrences', 'scoring_type');
						echo "<div class='form-field'>
							<label>Scoring Type</label>
							<select name='scoring_type' class='select" .(in_array('scoring_type', $required) ? ' required' : ''). "'>
								<option value=''>- Select -</option>";
								foreach($scoring_types as $score){
									echo "<option value='" .$score. "'" .(isset($row['scoring_type']) && $row['scoring_type'] == $score ? " selected" : ""). ">" .$score. "</option>";
								}
							echo "</select>
						</div>";

						echo "<div class='form-field'>
							<label>Course Par</label>
							<input type='text' name='par' value='" .(isset($row['par']) ? $row['par'] : ''). "' class='input number" .(in_array('par', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>Rounds</label>
							<input type='text' name='rounds' value='" .(isset($row['rounds']) ? $row['rounds'] : ''). "' class='input number" .(in_array('rounds', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>Purse ($)</label>
							<input type='text' name='purse' value='" .(isset($row['purse']) ? $row['purse'] : ''). "' class='input number" .(in_array('purse', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>3 year - Average of Purse ($)</label>
							<input type='text' name='purse_3_year_avg' value='" .(isset($row['purse_3_year_avg']) ? $row['purse_3_year_avg'] : ''). "' class='input number" .(in_array('purse_3_year_avg', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>R1 Skins Pot ($)</label>
							<input type='text' name='skins_pot1' value='" .(isset($row['skins_pot1']) ? $row['skins_pot1'] : ''). "' class='input number" .(in_array('skins_pot1', $required) ? ' required' : ''). "' />
						</div>";
						echo "<div class='form-field'>
							<label>R2 Skins Pot ($)</label>
							<input type='text' name='skins_pot2' value='" .(isset($row['skins_pot2']) ? $row['skins_pot2'] : ''). "' class='input number" .(in_array('skins_pot2', $required) ? ' required' : ''). "' />
						</div>";
					}

				echo "</div>";
			echo "</div>"; //Details

			//Dates
			echo "<div class='panel'>";
				echo "<div class='panel-header'>" .EVENT_CODE. " Dates
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content clearfix'>";
					echo "<div class='form-field'>
						<label>Start Date <span class='required'>*</span></label>
						<input type='text' name='start_date' value='" .(isset($row['start_date']) ? $row['start_date'] : ''). "' class='input datepicker" .(in_array('start_date', $required) ? ' required' : ''). "' autocomplete='off' />
					</div>";
					echo "<div class='form-field'>
						<label>End Date <span class='required'>*</span></label>
						<input type='text' name='end_date' value='" .(isset($row['end_date']) ? $row['end_date'] : ''). "' class='input datepicker" .(in_array('end_date', $required) ? ' required' : ''). "' autocomplete='off' />
					</div>";
					echo "<hr class='clear' />";
					echo "<div class='form-field'>
						<label>Registration Opens <small>" .date('g:iA', strtotime($reg_settings['reg_open_time'])). "</small> <span class='required'>*</span></label>
						<input type='text' name='reg_open' value='" .(isset($row['reg_open']) ? $row['reg_open'] : ''). "' class='input datepicker" .(in_array('reg_open', $required) ? ' required' : ''). "'" .(EVENT_TYPE == 2 ? " style='margin-bottom:5px;'" : "")." autocomplete='off' />";
						if(EVENT_TYPE == 2){
							echo "<input type='checkbox' name='email_notifications' id='email_notifications' class='checkbox' value='1'".(isset($row['email_notifications']) && $row['email_notifications'] == '1' ? " checked" : "")." />
							<label for='email_notifications'><small>Send email notifications</small></label>".$CMSBuilder->tooltip('Email Notifications', 'Check this box to notify eligible members when registration will become available.');
						}
					echo "</div>";
					echo "<div class='form-field'>
						<label>Payment Deadline <small>" .date('g:iA', strtotime($reg_settings['reg_close_time'])). "</small> <span class='required'>*</span>" .$CMSBuilder->tooltip('Payment Deadline', 'Outstanding fees are due on this date. Withdrawals made on or after this date will be subject to the withdrawal fee.'). "</label>
						<input type='text' name='payment_deadline' value='" .(isset($row['payment_deadline']) ? $row['payment_deadline'] : ''). "' class='input datepicker" .(in_array('payment_deadline', $required) ? ' required' : ''). "'" .(EVENT_TYPE == 2 ? " style='margin-bottom:5px;'" : "")." autocomplete='off' />";
						if(EVENT_TYPE == 2){
							echo "<input type='checkbox' name='payment_notifications' id='payment_notifications' class='checkbox' value='1'".(isset($row['payment_notifications']) && $row['payment_notifications'] == '1' ? " checked" : "")." />
							<label for='payment_notifications'><small>Send payment notifications</small></label>".$CMSBuilder->tooltip('Payment Notifications', 'Check this box to notify attendees the day before their tournament fees will be automatically processed.');
						}
					echo "</div>";
					echo "<div class='form-field" .(EVENT_TYPE != 2 ? ' hidden' : ''). "'>
						<label>Withdrawal Deadline <small>" .date('g:iA', strtotime($reg_settings['reg_close_time'])). "</small> <span class='required'>*</span>" .$CMSBuilder->tooltip('Withdrawal Deadline', 'Withdrawals made on or after this date will be subject to the late withdrawal fee (percentage of total fees).'). "</label>
						<input type='text' name='withdrawal_deadline' value='" .(isset($row['withdrawal_deadline']) ? $row['withdrawal_deadline'] : ''). "' class='input datepicker" .(in_array('withdrawal_deadline', $required) ? ' required' : ''). "' autocomplete='off' />
					</div>";
					echo "<div class='form-field'>
						<label>Registration Closes <small>" .date('g:iA', strtotime($reg_settings['reg_close_time'])). "</small> <span class='required'>*</span></label>
						<input type='text' name='reg_deadline' value='" .(isset($row['reg_deadline']) ? $row['reg_deadline'] : ''). "' class='input datepicker" .(in_array('reg_deadline', $required) ? ' required' : ''). "' autocomplete='off' />
					</div>";
				echo "</div>";
			echo "</div>";

			//Event Location
			if(EVENT_TYPE != 2){
				echo "<div class='panel'>";
					echo "<div class='panel-header'>" .EVENT_CODE. " Location
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
						<div class='panel-switch'>
							<label>Show Google Map " .$CMSBuilder->tooltip('Show Google Map', 'Display this location on an interactive map (google map must be enabled in settings). You can set your location by using the locate function or by clicking and dragging the map pin.'). "</label>
							<div class='onoffswitch'>
								<input type='checkbox' name='google_map' id='google_map' value='1'" .(isset($row['google_map']) && $row['google_map'] ? " checked" : ""). " />
								<label for='google_map'>
									<span class='inner'></span>
									<span class='switch'></span>
								</label>
							</div>
						</div>
					</div>";
					echo "<div class='panel-content clearfix'>";

						echo "<div class='clear'>";
							echo "<div class='form-field'>
								<label>Location Name<span class='required'>*</span></label>
								<input type='text' name='location_name' value='" .(isset($row['location_name']) ? $row['location_name'] : ''). "' class='input' />
							</div>";
						echo "</div>";

						echo "<hr class='clear' />";

						echo "<div class='clear'>";

						echo "<fieldset id='gllpLatlonPicker' class='gllpLatlonPicker'>
								<div class='gllpMap'>Google Maps</div>
								<div class='gllpSearch'>
									<button type='button' class='gllpSearchButton button-sm f_right nomargin center' id='gllpSearchButton'><i class='fa fa-location-arrow nomargin'></i></button>
									<input type='text' name='' class='gllpSearchField input f_right nomargin' value='' />
								</div>
								<input type='hidden' name='gpslat' class='gllpLatitude' value='".(isset($row['gpslat']) && $row['gpslat'] != ""  ? $row['gpslat'] : "53.563967")."'/>
								<input type='hidden' name='gpslong' class='gllpLongitude' value='".(isset($row['gpslong']) && $row['gpslong'] != "" ? $row['gpslong'] : "-113.490357")."'/>
								<input type='hidden' name='zoom' class='gllpZoom' value='".(isset($row['zoom']) && $row['zoom'] != 0 ? $row['zoom'] : "12")."'/>
							</fieldset>";

							echo "<div class='f_left'>";
								echo "<div class='form-field'>
									<label>Street Address</label>
									<input type='text' name='address' value='" .(isset($row['address']) ? $row['address'] : ''). "' class='input' />
									<label>Unit No.</label>
									<input type='text' name='address2' value='" .(isset($row['address2']) ? $row['address2'] : ''). "' class='input' />
									<label>City/Town</label>
									<input type='text' name='city' value='" .(isset($row['city']) ? $row['city'] : ''). "' class='input' />
								</div>";
								echo "<div class='form-field'>
									<label>Province/State</label>
									<select name='province' class='select'>";
										echo "<option value=''>- Select -</option>";
										echo "<optgroup label='Canada'>";
										for($p=1; $p<=count($provinces); $p++){
											echo "<option value='" .$provinces[$p][1]. "'" .((isset($row['province']) && $row['province'] == $provinces[$p][1]) ? " selected" : ""). ">" .$provinces[$p][0]. "</option>";
										}
										echo "</optgroup>";
										echo "<optgroup label='United States'>";
										for($p=1; $p<=count($states); $p++){
											echo "<option value='" .$states[$p][1]. "'" .((isset($row['province']) && $row['province'] == $states[$p][1]) ? " selected" : ""). ">" .$states[$p][0]. "</option>";
										}
										echo "</optgroup>";
									echo "</select>
									<label>Country</label>
									<select name='country' class='select'>
										<option value=''>- Select -</option>
										<option value='Canada'" .(isset($row['country']) && $row['country'] == 'Canada' ? " selected" : ""). ">Canada</option>
										<option value='United States'" .(isset($row['country']) && $row['country'] == 'United States' ? " selected" : ""). ">United States</option>
									</select>
									<label>Postal/Zip Code</label>
									<input type='text' name='postal_code' value='" .(isset($row['postal_code']) ? $row['postal_code'] : ''). "' class='input' />
								</div>";
							echo "</div>";

						echo "</div>";
					echo "</div>";
				echo "</div>"; //Location
			}

			//Tournament restrictions
			if(EVENT_TYPE == 2){

				echo "<div class='panel'>";
					echo "<div class='panel-header'>Restrictions
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";
					echo "<div class='panel-content clearfix'>";

						$gender_types = $db->get_enum_vals('reg_events', 'gender');
						echo "<div class='form-field'>
							<label>Gender Restriction</label>
							<select name='gender' class='select" .(in_array('gender', $required) ? ' required' : ''). "'>
								<option value=''>- None -</option>";
								foreach($gender_types as $gender){
									echo "<option value='" .$gender. "'" .(isset($row['gender']) && $row['gender'] == $gender ? ' selected' : ''). ">" .$gender. "</option>";
								}
							echo "</select>
						</div>";

						$age_types = $db->get_enum_vals('reg_events', 'age');
						echo "<div class='form-field'>
							<label>Age Restriction</label>
							<select name='age' class='select" .(in_array('age', $required) ? ' required' : ''). "'>
								<option value=''>- None -</option>";
								foreach($age_types as $age){
									echo "<option value='" .$age. "'" .(isset($row['age']) && $row['age'] == $age ? ' selected' : ''). ">" .$age. "</option>";
								}
							echo "</select>
						</div>";

						echo "<div class='form-field'>
							<label>Member Eligibility</label>
							<select name='eligibility' class='select" .(in_array('eligibility', $required) ? ' required' : ''). "'>
								<option value=''>- None -</option>";
								$category_ids = array();
								$category_names = array();
								foreach($member_categories as $cat){
									$category_ids[] = $cat['category_id'];
									$category_names[] = $cat['name'];
									echo "<option value='" .implode(', ', $category_ids). "'" .(isset($row['eligibility']) && $row['eligibility'] == implode(', ', $category_ids) ? ' selected' : ''). ">" .implode(', ', $category_names). "</option>";
								}
								echo "<option value='Non-Members'" .($row['role_id'] == 8 ? ' selected' : ''). ">Non-Members</option>
							</select>
						</div>";

						//Membership classes
						echo "<div class='clear'>";
							echo "<div class='multiselects-wrapper clearfix'>";
								echo "<div class='multiselect-field inlineblock v_middle'>";
									echo "<label>Membership Classes</label>";
									echo "<select name='class_id' class='multiselect' size='5' data-action='add' data-move='1' data-level='1'>";
										foreach($member_classes as $class) {
											echo "<option value='".$class['class_id']."'>".$class['class_name']."</option>";
										}
									echo "</select>";
								echo "</div>";

								echo "<div class='multiselect-arrows inlineblock v_middle'>";
									echo "<a href='#' class='multiselect-arrow button disabled' data-action='add'>&raquo;</a>";
									echo "<a href='#' class='multiselect-arrow button disabled' data-action='remove'>&laquo;</a>";
								echo "</div>";

								echo "<div class='multiselect-field inlineblock v_middle'>";
									echo "<label>Assigned Classes</label>";
									echo "<select name='membership_classes[]' class='multiselect' multiple='multiple' data-action='remove' data-move='1'>";
										if(isset($row['membership_classes']) && !empty($row['membership_classes'])) {
											foreach($row['membership_classes'] as $class_id) {
												$class_name = $member_classes[$class_id]['class_name'];
												echo "<option value='".$class_id."'>".$class_name."</option>";
											}
										}
									echo "</select>";
								echo "</div>";
							echo "</div>";
						echo "</div>";

					echo "</div>";
				echo "</div>";

			}

			//Sponsors
			echo "<div class='panel'>";
				echo "<div class='panel-header'>" .EVENT_CODE. " Sponsors
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content clearfix'>";

				foreach($sponsor_types as $stype){
					echo "<div class='multiselects-wrapper clearfix'>";
						echo "<div class='multiselect-field inlineblock v_middle'>";
							echo "<label>".$stype." Sponsors</label>";
							echo "<select name='sponsor_id' class='multiselect' size='5' data-action='add' data-move='1' data-level='1'>";
								foreach($sponsors as $sponsor) {
									echo "<option value='".$sponsor['sponsor_id']."'>".$sponsor['name']."</option>";
								}
							echo "</select>";
						echo "</div>";

						echo "<div class='multiselect-arrows inlineblock v_middle'>";
							echo "<a href='#' class='multiselect-arrow button disabled' data-action='add'>&raquo;</a>";
							echo "<a href='#' class='multiselect-arrow button disabled' data-action='remove'>&laquo;</a>";
						echo "</div>";

						echo "<div class='multiselect-field inlineblock v_middle'>";
							echo "<label>&nbsp;</label>";
							echo "<select name='assigned_sponsors[".$stype."][]' class='multiselect' multiple='multiple' data-action='remove' data-move='1'>";
								if(isset($row['assigned_sponsors'][$stype]) && !empty($row['assigned_sponsors'][$stype])) {
									foreach($row['assigned_sponsors'][$stype] as $sponsor_id) {
										$sponsor_name = $sponsors[$sponsor_id]['name'];
										echo "<option value='".$sponsor_id."'>".$sponsor_name."</option>";
									}
								}
							echo "</select>";
						echo "</div>";
					echo "</div>";
				}

				echo "</div>";
			echo "</div>";

			//Banner Image
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Banner Image
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content clearfix'>";
					if(isset($image) && $image != '' && file_exists($imagedir.$imagesizes[0]['dir'].$image)){
						echo "<div class='img-holder'>
							<a href='" .$path.$imagedir.$imagesizes[0]['dir'].$image. "' class='light-gallery' target='_blank' title=''>
								<img src='" .$path.$imagedir.$imagesizes[0]['dir'].$image. "' alt='' />
							</a>
							<input type='checkbox' class='checkbox' name='deleteimage' id='deleteimage' value='1'>
							<label for='deleteimage'>Delete Current Image</label>
						</div>";
					}
					echo "<div class='form-field'>
						<label>Upload Image " .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least '.$imagesizes[0]['width'].' x '.$imagesizes[0]['height'].' and file size must be smaller than 20MB.'). "</label>
						<input type='file' class='input" .(in_array('image', $required) ? ' required' : ''). "' name='image' value='' />
						<input type='hidden' name='old_image' value='" .(isset($image) && $image != '' && file_exists($imagedir.$imagesizes[0]['dir'].$image) ? $image : ''). "' />
					</div>";
					echo "<div class='form-field'>
						<label>Image Description</label>
						<input type='text' name='image_alt' value='" .(isset($row['banner_image_alt']) ? $row['banner_image_alt'] : ''). "' class='input' />
					</div>";
				echo "</div>";
			echo "</div>";

			//Description
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Description <span class='required'>*</span>
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content clearfix'>";
					echo "<textarea name='description' class='tinymceEditor'>".(isset($row['description']) ? $row['description'] : "")."</textarea>";
				echo "</div>";
			echo "</div>";

			//Sticky footer
			echo "<footer id='cms-footer' class='resize'>";
				echo "<button type='submit' name='save' value='save' class='button f_right'><i class='fa fa-check'></i>Save &amp; Continue</button>";
				if(ITEM_ID != "" && (!isset($row['deletable']) || $row['deletable'] == 1)){
					echo "<button type='button' name='delete' value='delete' class='button delete'><i class='fa fa-trash'></i>Delete ".EVENT_CODE."</button>";
				}
				echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
			echo "</footer>";

			echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
			echo "<input type='hidden' name='keep_tags[]' value='description' />";

		echo "</form>";

	//Pricing
	}else if(STEP == 2) {

		echo "<form action='' method='post' enctype='multipart/form-data'>";

			echo "<div class='panel'>";
				echo "<div class='panel-header'>" .EVENT_CODE. " Pricing Settings
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content'>";
					echo "<div class='form-field'>
						<label>GL Account</label>
						<select name='gl_id' class='select'>";
						$default = EVENT_TYPE;
						foreach($glaccounts as $gl){
							echo "<option value='" .($gl['item_no'] == $default ? '' : $gl['gl_id']). "'" .((isset($row['gl_id']) && $row['gl_id'] == $gl['gl_id']) || (!isset($row['gl_id']) && $gl['item_no'] == $default) ? ' selected' : ''). ">" .$gl['gl_name']. "</option>";
						}
						echo "</select>
					</div>";

					if(EVENT_TYPE != 2){

						echo "<div class='form-field auto-width'>
							<label>Service Fee ".$CMSBuilder->tooltip('Service Fee', 'Administrative fee to be added to online credit card purchases. May be a flat rate, or a percentage (example: 5.00 <b>or</b> 2.3). Please be sure to select the correct type below. Leave blank to use global service fee settings.')."</label>
							<input type='text' name='admin_fee' value='".(isset($row['admin_fee']) ? number_format($row['admin_fee'], 2, '.', '') : '')."' class='input input_sm decimal nomargin' />
						</div>";
						echo "<div class='form-field'>
							<label>Service Fee Type ".$CMSBuilder->tooltip('Service Fee Type', 'Select if the Service Fee (if applicable) will be calculated as a percentage, or a flat fee added to online credit card purchases.')."</label></td>
							<select name='admin_fee_type' class='select nomargin'>
								<option value='Dollar'".(isset($row['admin_fee_type']) && $row['admin_fee_type'] == 'Dollar' ? " selected" : "").">Dollar Amount</option>
								<option value='Percent'".(isset($row['admin_fee_type']) && $row['admin_fee_type'] == 'Percent' ? " selected" : "").">Percentage</option>
							</select>
						</div>";

					}else{

						echo "<div class='form-field auto-width'>
							<label>Skins Fee ".$CMSBuilder->tooltip('Skins Fee', 'Non-taxable fee to be added to tournament entry fee.')."</label>
							<input type='text' name='tournament_fee' value='".(isset($row['tournament_fee']) ? number_format($row['tournament_fee'], 2, '.', '') : '')."' class='input input_sm decimal nomargin' />
						</div>";
						echo "<div class='form-field'>
							<label>Skins GL Account</label>
							<select name='gl_id_fees' class='select'>";
							$default = 11; //Skins account
							foreach($glaccounts as $gl){
								echo "<option value='" .$gl['gl_id']. "'" .((isset($row['gl_id_fees']) && $row['gl_id_fees'] == $gl['gl_id']) || (!isset($row['gl_id_fees']) && $gl['gl_id'] == $default) ? ' selected' : ''). ">" .$gl['gl_name']. "</option>";
							}
							echo "</select>
						</div>";
					}

				echo "</div>";
			echo "</div>";


			echo "<div class='panel'>";
				echo "<div class='panel-header'>" .EVENT_CODE. " Pricing
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";

				$pricing_types = array();
				if(EVENT_TYPE == 2){
					$pricing_types = array('Entry Fee'); //Tournaments have one type
				}else{
					$pricing_types = array('General');
				}

				echo "<div class='panel-content nopadding'>";
					echo "<div id='tickets-wraper' class='rows-wrapper'>";
						$pcount=0;
						if(isset($row['pricing']) && !empty($row['pricing'])){
							foreach($row['pricing'] as $ticket) {
								echo "<div class='ticket_template row clearfix'>";
									echo "<div class='form-field'>";
										echo "<label>Type</label>";

										if(EVENT_TYPE == 2){
											echo "<select name='price_types[$pcount]' class='select'>
												<option value='Entry Fee' selected>Entry Fee</option>";
											echo "</select>";
										}else{
											echo "<input type='text' name='price_types[$pcount]' class='input' value='" .$ticket['price_type']. "' />";
										}

									echo "</div>";

									echo "<div class='form-field'>";
										echo "<label>Price ($)</label>";
										echo "<input type='text' name='prices[$pcount]' value='".$ticket['price']."' class='input decimal' />";
									echo "</div>";

									echo "<input type='hidden' name='price_id[$pcount]' value='".$ticket['pricing_id']."' />";
								echo "</div>";
								$pcount++;
							}

						}else{
							echo "<div class='ticket_template row clearfix'>";
								echo "<div class='form-field'>";
									echo "<label>Type</label>";

									if(EVENT_TYPE == 2){
										echo "<select name='price_types[$pcount]' class='select'>
											<option value='Entry Fee' selected>Entry Fee</option>";
										echo "</select>";
									}else{
										echo "<input type='text' name='price_types[$pcount]' class='input' value='General' />";
									}

								echo "</div>";

								echo "<div class='form-field'>";
									echo "<label>Price ($)</label>";
									echo "<input type='text' name='prices[$pcount]' value='' class='input decimal' />";
								echo "</div>";

								echo "<input type='hidden' name='price_id[$pcount]' value='' />";
							echo "</div>";
						}

						echo (EVENT_TYPE != 2 ? "<a id='add-ticket-btn' class='add-row-btn button-sm' href='#' onclick='addNewTicket(); return false;'>+ Add Type</a>" : "");
					echo "</div>";
				echo "</div>";
			echo "</div>"; // END Pricing

			//Sticky footer
			echo "<footer id='cms-footer' class='resize'>";
				echo "<button type='submit' name='save' value='save' class='button f_right'><i class='fa fa-check'></i>Save &amp; Continue</button>";
				if(ITEM_ID != "" && (!isset($row['deletable']) || $row['deletable'] == 1)){
					echo "<button type='button' name='delete' value='delete' class='button delete'><i class='fa fa-trash'></i>Delete ".EVENT_CODE."</button>";
				}
				echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
			echo "</footer>";

			echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "</form>";


	//Waivers
	}else if(STEP == 3){

		echo "<form id='event1_form' action='' method='post' enctype='multipart/form-data'>";

			//Waivers
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Attach Waivers
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content'>";
					echo "<p>To assign one or more waivers specific to this ".$record_name.", select from the list of waivers below and use the arrow keys to populate the list on the right.</p>";

					echo "<div class='multiselects-wrapper clearfix'>";
						echo "<div class='multiselect-field inlineblock v_middle'>";
							echo "<label>All Waivers</label>";
							echo "<select name='waiver_id' class='multiselect' size='5' data-action='add' data-move='1' data-level='1'>";
								foreach($all_waivers as $waiver){
									echo "<option value='".$waiver['waiver_id']."'>".$waiver['title']."</option>";
								}
							echo "</select>";
						echo "</div>";

						echo "<div class='multiselect-arrows inlineblock v_middle'>";
							echo "<a href='#' class='multiselect-arrow button disabled' data-action='add'>&raquo;</a>";
							echo "<a href='#' class='multiselect-arrow button disabled' data-action='remove'>&laquo;</a>";
						echo "</div>";

						echo "<div class='multiselect-field inlineblock v_middle'>";
							echo "<label>Assigned Waivers</label>";
							echo "<select name='assigned_waivers[]' class='multiselect' multiple='multiple' data-action='remove' data-move='1'>";
								if(isset($row['assigned_waivers']) && !empty($row['assigned_waivers'])){
									foreach($row['assigned_waivers'] as $waiver_id){
										echo "<option value='".$waiver_id."'>".$all_waivers[$waiver_id]['title']."</option>";
									}
								}
							echo "</select>";
						echo "</div>";

					echo "</div>";
				echo "</div>";
			echo "</div>";

			//Forms
			if(EVENT_TYPE != 2){

				echo $CMSBuilder->important('Any forms selected will be <strong>required</strong>, and users must complete them before they can register. It is <strong>crucial</strong> that all attached forms are visible, have been added to a page, and the page cannot be disabled. Otherwise, users will not be able to complete the forms and will not be able to register.');

				echo "<div class='panel'>";
					echo "<div class='panel-header'>Attach Forms
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";
					echo "<div class='panel-content'>";
						echo "<p>To assign one or more forms specific to this ".$record_name.", select from the list of forms below and use the arrow keys to populate the list on the right.</p>";

						echo "<div class='multiselects-wrapper clearfix'>";
							echo "<div class='multiselect-field inlineblock v_middle'>";
								echo "<label>All Forms <small>(must require login)</small></label>";
								echo "<select name='form_id' class='multiselect' size='5' data-action='add' data-move='1' data-level='1'>";
									foreach($all_forms as $form){
										echo "<option value='".$form['form_id']."'>".$form['form_name']."</option>";
									}
								echo "</select>";
							echo "</div>";

							echo "<div class='multiselect-arrows inlineblock v_middle'>";
								echo "<a href='#' class='multiselect-arrow button disabled' data-action='add'>&raquo;</a>";
								echo "<a href='#' class='multiselect-arrow button disabled' data-action='remove'>&laquo;</a>";
							echo "</div>";

							echo "<div class='multiselect-field inlineblock v_middle'>";
								echo "<label>Assigned Forms</label>";
								echo "<select name='assigned_forms[]' class='multiselect' multiple='multiple' data-action='remove' data-move='1'>";
									if(isset($row['assigned_forms']) && !empty($row['assigned_forms'])){
										foreach($row['assigned_forms'] as $form_id){
											echo "<option value='".$form_id."'>".$all_forms[$form_id]['form_name']."</option>";
										}
									}
								echo "</select>";
							echo "</div>";

						echo "</div>";
					echo "</div>";
				echo "</div>";
			}

			//Sticky footer
			echo "<footer id='cms-footer' class='resize'>";
				echo "<button type='submit' name='save' value='save' class='button f_right'><i class='fa fa-check'></i>Save &amp; " .(STEP == count($form_steps) ? "Finish" : "Continue"). "</button>";
				if(ITEM_ID != "" && (!isset($row['deletable']) || $row['deletable'] == 1)){
					echo "<button type='button' name='delete' value='delete' class='button delete'><i class='fa fa-trash'></i>Delete ".EVENT_CODE."</button>";
				}
				echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
			echo "</footer>";

			echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

		echo "</form>";

	//Attendee Info
	}else if(STEP == 4){

		//Event attendee fields
		if(EVENT_TYPE != 2){

			echo "<form action='' method='post' enctype='multipart/form-data'>";

				//Attendee Info
				echo "<div class='panel'>";
					echo "<div class='panel-header'>Attendee Fields
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";
					echo "<div class='panel-content attendee-fields-wrapper clearfix nopadding'>";


						echo "<div class='rows-wrapper'>";
							echo "<div class='row'><br /><p>Only fields set to \"Show\" will be displayed in the Attendee Information section during registration. First name and last name are always required.<br/><b>NOTE:</b> It is highly recommended that you <u>require the email field</u> for attendee notification purposes.</p></div>";

							echo "<div class='row'>";
								echo "<label>First Name</label>";
								echo "<select name='f_name_show_hide' class='select' disabled>";
									echo "<option value='1'>Show</option>";
									echo "<option value='-1'".(isset($row['attendee_fields']['first_name']) && $row['attendee_fields']['first_name'] < 0 ? " selected" : "").">Hide</option>";
								echo "</select>";
								echo "<input type='radio' id='f_name_req_1' name='f_name_req' value='1' class='radio'".(!isset($row['attendee_fields']['first_name']) || (isset($row['attendee_fields']['first_name']) && abs($row['attendee_fields']['first_name']) == 1) ? " checked" : "")." disabled /><label for='f_name_req_1'>Required</label>";
								echo "<input type='radio' id='f_name_req_2' name='f_name_req' value='2' class='radio'".(isset($row['attendee_fields']['first_name']) && abs($row['attendee_fields']['first_name']) == 2 ? " checked" : "")." disabled /><label for='f_name_req_2'>Optional</label>";
							echo "</div>";

							echo "<div class='row'>";
								echo "<label>Last Name</label>";
								echo "<select name='l_name_show_hide' class='select' disabled>";
									echo "<option value='1'>Show</option>";
									echo "<option value='-1'".(isset($row['attendee_fields']['last_name']) && $row['attendee_fields']['last_name'] < 0 ? " selected" : "").">Hide</option>";
								echo "</select>";
								echo "<input type='radio' id='l_name_req_1' name='l_name_req' value='1' class='radio'".(!isset($row['attendee_fields']['last_name']) || (isset($row['attendee_fields']['last_name']) && abs($row['attendee_fields']['last_name']) == 1) ? " checked" : "")." disabled /><label for='l_name_req_1'>Required</label>";
								echo "<input type='radio' id='l_name_req_2' name='l_name_req' value='2' class='radio'".(isset($row['attendee_fields']['last_name']) && abs($row['attendee_fields']['last_name']) == 2 ? " checked" : "")." disabled /><label for='l_name_req_2'>Optional</label>";
							echo "</div>";

							echo "<div class='row'>";
								echo "<label>Email</label>";
								echo "<select name='email_show_hide' class='select'>";
									echo "<option value='1'>Show</option>";
									echo "<option value='-1'".(isset($row['attendee_fields']['email']) && $row['attendee_fields']['email'] < 0 ? " selected" : "").">Hide</option>";
								echo "</select>";
								echo "<input type='radio' id='email_req_1' name='email_req' value='1' class='radio'".(!isset($row['attendee_fields']['email']) || (isset($row['attendee_fields']['email']) && abs($row['attendee_fields']['email']) == 1) ? " checked" : "")." /><label for='email_req_1'>Required</label>";
								echo "<input type='radio' id='email_req_2' name='email_req' value='2' class='radio'".(isset($row['attendee_fields']['email']) && abs($row['attendee_fields']['email']) == 2 ? " checked" : "")." /><label for='email_req_2'>Optional</label>";
							echo "</div>";

							echo "<div class='row'>";
								echo "<label>Phone</label>";
								echo "<select name='phone_show_hide' class='select'>";
									echo "<option value='1'>Show</option>";
									echo "<option value='-1'".(isset($row['attendee_fields']['phone']) && $row['attendee_fields']['phone'] < 0 ? " selected" : "").">Hide</option>";
								echo "</select>";
								echo "<input type='radio' id='phone_req_1' name='phone_req' value='1' class='radio'".(isset($row['attendee_fields']['phone']) && abs($row['attendee_fields']['phone']) == 1 ? " checked" : "")." /><label for='phone_req_1'>Required</label>";
								echo "<input type='radio' id='phone_req_2' name='phone_req' value='2' class='radio'".(!isset($row['attendee_fields']['phone']) || (isset($row['attendee_fields']['phone']) && abs($row['attendee_fields']['phone']) == 2) ? " checked" : "")." /><label for='phone_req_2'>Optional</label>";
							echo "</div>";

							echo "<div class='row'>";
								echo "<label>Company</label>";
								echo "<select name='company_show_hide' class='select'>";
									echo "<option value='1'>Show</option>";
									echo "<option value='-1'".(isset($row['attendee_fields']['company']) && $row['attendee_fields']['company'] < 0 ? " selected" : "").">Hide</option>";
								echo "</select>";
								echo "<input type='radio' id='company_req_1' name='company_req' value='1' class='radio'".(isset($row['attendee_fields']['company']) && abs($row['attendee_fields']['company']) == 1 ? " checked" : "")." /><label for='company_req_1'>Required</label>";
								echo "<input type='radio' id='company_req_2' name='company_req' value='2' class='radio'".(!isset($row['attendee_fields']['company']) || (isset($row['attendee_fields']['company']) && abs($row['attendee_fields']['company']) == 2) ? " checked" : "")." /><label for='company_req_2'>Optional</label>";
							echo "</div>";

							echo "<div class='row'>";
								echo "<label>Facility</label>";
								echo "<select name='facility_show_hide' class='select'>";
									echo "<option value='1'>Show</option>";
									echo "<option value='-1'".(isset($row['attendee_fields']['facility']) && $row['attendee_fields']['facility'] < 0 ? " selected" : "").">Hide</option>";
								echo "</select>";
								echo "<input type='radio' id='facility_req_1' name='facility_req' value='1' class='radio'".(isset($row['attendee_fields']['facility']) && abs($row['attendee_fields']['facility']) == 1 ? " checked" : "")." /><label for='facility_req_1'>Required</label>";
								echo "<input type='radio' id='facility_req_2' name='facility_req' value='2' class='radio'".(!isset($row['attendee_fields']['facility']) || (isset($row['attendee_fields']['facility']) && abs($row['attendee_fields']['facility']) == 2) ? " checked" : "")." /><label for='facility_req_2'>Optional</label>";
							echo "</div>";

							echo "<div class='row'>";
								echo "<label>Position</label>";
								echo "<select name='position_show_hide' class='select'>";
									echo "<option value='1'>Show</option>";
									echo "<option value='-1'".(isset($row['attendee_fields']['position']) && $row['attendee_fields']['position'] < 0 ? " selected" : "").">Hide</option>";
								echo "</select>";
								echo "<input type='radio' id='position_req_1' name='position_req' value='1' class='radio'".(isset($row['attendee_fields']['position']) && abs($row['attendee_fields']['position']) == 1 ? " checked" : "")." /><label for='position_req_1'>Required</label>";
								echo "<input type='radio' id='position_req_2' name='position_req' value='2' class='radio'".(!isset($row['attendee_fields']['position']) || (isset($row['attendee_fields']['position']) && abs($row['attendee_fields']['position']) == 2) ? " checked" : "")." /><label for='position_req_2'>Optional</label>";
							echo "</div>";

							echo "<div class='row'>";
								echo "<label>Comments</label>";
								echo "<select name='comments_show_hide' class='select'>";
									echo "<option value='1'>Show</option>";
									echo "<option value='-1'".(isset($row['attendee_fields']['comments']) && $row['attendee_fields']['comments'] < 0 ? " selected" : "").">Hide</option>";
								echo "</select>";
								echo "<input type='radio' id='comments_req_1' name='comments_req' value='1' class='radio'".(isset($row['attendee_fields']['comments']) && abs($row['attendee_fields']['comments']) == 1 ? " checked" : "")." /><label for='comments_req_1'>Required</label>";
								echo "<input type='radio' id='comments_req_2' name='comments_req' value='2' class='radio'".(!isset($row['attendee_fields']['comments']) || (isset($row['attendee_fields']['comments']) && abs($row['attendee_fields']['comments']) == 2) ? " checked" : "")." /><label for='comments_req_2'>Optional</label>";
							echo "</div>";

						echo "</div>";
					echo "</div>";
				echo "</div>"; // Attendee Info

				//Sticky footer
				echo "<footer id='cms-footer' class='resize'>";
					echo "<button type='submit' name='save' value='save' class='button f_right'><i class='fa fa-check'></i>Save &amp; Continue</button>";
					if(ITEM_ID != "" && (!isset($row['deletable']) || $row['deletable'] == 1)){
						echo "<button type='button' name='delete' value='delete' class='button delete'><i class='fa fa-trash'></i>Delete ".EVENT_CODE."</button>";
					}
					echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
				echo "</footer>";

				echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
			echo "</form>";


		//Tournament field
		}else if(EVENT_TYPE == 2){

			echo "<form action='' method='post' enctype='multipart/form-data'>";

				echo "<div class='panel'>";
					echo "<div class='panel-header'>Import Data
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";
					echo "<div class='panel-content clearfix'>";
						echo "<div class='form-field'>
							<label>Upload File " .$CMSBuilder->tooltip('Upload File', 'Alpha Listing or Results XML file exported from Impact tournament software.'). "</label>
							<input type='file' name='import_file' class='input' value='' />
						</div>
						<div class='form-field'>
							<label>Round</label>
							<select name='import_round' class='select'>
								<option value='1'" .(isset($_POST['import_round']) && $_POST['import_round'] == '1' ? ' selected' : ''). ">Round 1</option>
								<option value='2'" .(isset($_POST['import_round']) && $_POST['import_round'] == '2' ? ' selected' : ''). ">Round 2</option>
							</select>
						</div>
						<div class='form-field'>
							<label>&nbsp;</label>
							<button type='submit' name='import' class='button-sm'><i class='fa fa-upload'></i>Import</button>
						</div>";
					echo "</div>";
				echo "</div>";

			echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
			echo "</form>";

			//Import notice
			if(isset($_POST['import'])){
				if(!empty($field_data) || !empty($results_data)){
					echo $CMSBuilder->important('<strong>Round '.$_POST['import_round']. ' ' .(!empty($field_data) ? 'Field Data' : 'Results Data'). '</strong> was successfully loaded. Please review the entries below to ensure all data is correct, then click the save button to complete the import.'.(!empty($import_errors) ? '<br /><br />'.implode('<br />', $import_errors) : ''));
				}
			}

			echo "<form action='' method='post' enctype='multipart/form-data'>";

				echo "<div class='panel'>";
					echo "<div class='panel-header'>Tournament Field &nbsp;<small>(" .count($row['attendees']). "/" .($row['max_capacity'] != "" ? $row['max_capacity'] : "&infin;"). ")</small>
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";
					echo "<div class='panel-content nopadding center'>";
						echo "<table cellpadding='0' cellspacing='0' border='0' width='100%' class='tablesorter nopager'>";

							if(!empty($row['attendees'])){
								echo "<thead>";
									echo "<th>Player</th>";
									echo ($row['team_event'] ? "<th>Partner</th>" : "");
									echo "<th class='center nopadding-h' width='100px'>Starting Hole</th>";
									echo "<th class='center nopadding-h' width='100px'>Tee Time " .$CMSBuilder->tooltip('Tee Time', '24 Hour Format (ie. 0630, 1700)'). "</th>";
									echo (!$row['team_event'] ? "<th class='center nopadding-h' width='100px'>Avg Par Over/Under</th>" : "");
									echo "<th class='center nopadding-h' width='100px'>Individual Score</th>";
									echo ($row['team_event'] ? "<th class='center nopadding-h' width='100px'>Team Gross</th>" : "");
									echo ($row['team_event'] ? "<th class='center nopadding-h' width='100px'>Team Net</th>" : "");
									echo "<th class='center nopadding-h' width='100px'>Final<br />Result</th>";
									echo "<th class='center nopadding-h' width='100px'>Prize<br />Amount</th>";
									echo "<th class='center nopadding-h' width='100px'>Gift Cert Amount</th>";
									echo "<th class='center nopadding-h' width='100px'>Individual Points</th>";
									echo "<th class='center nopadding-h' width='100px'>Skins " .$CMSBuilder->tooltip('Skins', 'Separate multiple holes with a comma (ie. 1,10,18)'). "</th>";
								echo "</thead>
								<tbody>";

								$count = 0;
								foreach($row['attendees'] as $aid=>$attendee){
									$missing = false;
									$pmissing = false;

									//Fill from import data
									if(isset($_POST['import'])){
										$round = $_POST['import_round'];

										//Field
										if(!empty($field_data)){

											//Player
											$key = $attendee['last_name'].', '.$attendee['first_name']; //last, first
											if(array_key_exists($key, $field_data)){
												if(isset($field_data[$key]['Hole'])){
													$attendee['r'.$round.'_hole'] = trim($field_data[$key]['Hole']);
												}
												if(isset($field_data[$key]['Time'])){
													$attendee['r'.$round.'_time'] = date('Hi', strtotime($field_data[$key]['Time']));
												}
												if(isset($field_data[$key]['Handicap'])){
													$attendee['handicap'] = trim($field_data[$key]['Handicap']);
												}
											}else{
												$missing = true;
											}

											//Partner
											if($row['team_event'] && !empty($attendee['partner'])){
												$pkey = $attendee['partner']['last_name'].', '.$attendee['partner']['first_name']; //last, first
												if(array_key_exists($pkey, $field_data)){
													if(isset($field_data[$pkey]['Handicap'])){
														$attendee['partner']['handicap'] = trim($field_data[$pkey]['Handicap']);
													}
												}else{
													$pmissing = true;
												}
											}

										//Results
										}else if(!empty($results_data)){

											//Player
											$key = $attendee['first_name'].' '.$attendee['last_name']; //first last
											if(array_key_exists($key, $results_data)){

												//Individual score
												if(isset($results_data[$key]['Individual Gross'])){
													$attendee['r'.$round.'_score'] = $results_data[$key]['Individual Gross'];
													$attendee['final_score'] = (($attendee['r1_score'] != "" ? $attendee['r1_score'] : 0)+($attendee['r2_score'] != "" ? $attendee['r2_score'] : 0));
												}

												//Team gross
												if(isset($results_data[$key]['Team Gross'])){
													$attendee['r'.$round.'_team_gross'] = $results_data[$key]['Team Gross'];
													$attendee['team_gross'] = (($attendee['r1_team_gross'] != "" ? $attendee['r1_team_gross'] : 0)+($attendee['r2_team_gross'] != "" ? $attendee['r2_team_gross'] : 0));
												}

												//Team net
												if(isset($results_data[$key]['Team Net'])){
													$attendee['r'.$round.'_team_net'] = $results_data[$key]['Team Net'];
													$attendee['team_net'] = (($attendee['r1_team_net'] != "" ? $attendee['r1_team_net'] : 0)+($attendee['r2_team_net'] != "" ? $attendee['r2_team_net'] : 0));
												}

												$attendee['team_final_score'] = $attendee['team_gross'].'/'.$attendee['team_net'];

											}else{
												$missing = true;
											}

											//Partner
											if($row['team_event'] && !empty($attendee['partner'])){
												$pkey = $attendee['partner']['first_name'].' '.$attendee['partner']['last_name']; //first last
												if(array_key_exists($pkey, $results_data)){
													if(isset($results_data[$pkey]['Handicap'])){
														$attendee['partner']['handicap'] = trim($results_data[$pkey]['Handicap']);
													}
												}else{
													$pmissing = true;
												}
											}

										}

									}

									$count++;
									echo "<tr>
										<td class='left'>";

											echo "<a href='" .$sitemap[$_cmssections['registration-attendees']]['page_url']. "?action=edit&item_id=".$aid."'>" .$attendee['last_name'].", ".$attendee['first_name']. "</a><br />";
											echo $attendee['facility_name']. "<br /><small>Handicap " .$attendee['handicap']. "</small>";
											echo "<input type='hidden' name='handicap_".$aid."' value='" .$attendee['handicap']. "' />";
											echo ($missing ? "<br /><strong class='required'><i class='fa fa-exclamation-triangle'></i> No data found</strong>" : "");

											//Junior Masters
											if($row['category_id'] == 15){
												echo "<br /><select name='entries[".$aid."][flight]' class='select select_sm nomargin'>
													<option value=''>- Flight -</option>
													<option value='14 & Under'" .(isset($attendee['flight']) && $attendee['flight'] == '14 & Under' ? ' selected' : ''). ">14 & Under</option>
													<option value='16 & Under'" .(isset($attendee['flight']) && $attendee['flight'] == '16 & Under' ? ' selected' : ''). ">16 & Under</option>
													<option value='18 & Under'" .(isset($attendee['flight']) && $attendee['flight'] == '18 & Under' ? ' selected' : ''). ">18 & Under</option>
												</select> &nbsp; ";
												echo "<input type='checkbox' id='champion-".$aid."' name='entries[".$aid."][champion]' class='checkbox' value='1'" .(isset($attendee['champion']) && $attendee['champion'] == '1' ? " checked" : ""). " /><label for='champion-".$aid."'><small>Overall Champion</small></label>";

												//Sort by gender
												echo "<input type='hidden' name='entries[".$aid."][gender]' value='" .$attendee['gender']. "' />";

											//PATs
											}else if($row['category_id'] == 16){
												echo "<br /><input type='checkbox' id='target-".$aid."' name='entries[".$aid."][target_score]' class='checkbox' value='Class A'" .(isset($attendee['target_score']) && $attendee['target_score'] == 'Class A' ? " checked" : ""). " /><label for='target-".$aid."'><small>Target Score Achieved - Class A</small></label>
												<br /><input type='checkbox' id='targetcfm-".$aid."' name='entries[".$aid."][target_score_cfm]' class='checkbox' value='CFM'" .(isset($attendee['target_score']) && $attendee['target_score'] == 'CFM' ? " checked" : ""). " /><label for='targetcfm-".$aid."'><small>Target Score Achieved - CFM</small></label>";

											}else{
												echo "<br /><input type='checkbox' id='playoff-".$aid."' name='entries[".$aid."][playoff]' class='checkbox' value='1'" .(isset($attendee['playoff']) && $attendee['playoff'] == '1' ? " checked" : ""). " /><label for='playoff-".$aid."'><small>Playoff Winner</small></label>";
											}

										echo "</td>";

										//Team Events
										if($row['team_event']){
											echo "<td class='left'>";
												if(!empty($attendee['partner'])){
													echo "<a href='" .$sitemap[$_cmssections['registration-attendees']]['page_url']. "?action=edit&item_id=" .$attendee['partner']['attendee_id']. "'>" .$attendee['partner']['last_name'].", ".$attendee['partner']['first_name']. "</a><br />";
													echo $attendee['partner']['facility_name']. "<br /><small>Handicap " .$attendee['partner']['handicap']. "</small>";
													echo "<input type='hidden' name='handicap_".$attendee['partner']['attendee_id']."' value='" .$attendee['partner']['handicap']. "' />";
													echo ($pmissing ? "<br /><strong class='required'><i class='fa fa-exclamation-triangle'></i> No data found</strong>" : "");
												}else{
													echo "<a href='" .$sitemap[$_cmssections['registration-attendees']]['page_url']. "?action=edit&item_id=".$aid."' class='button-sm'><i class='fa fa-user-plus nomargin'></i></a>";
												}
											echo "</td>";
										}

										echo "<td class='nopadding-h'>
											<span class='hidden'>".
												(isset($attendee['r1_hole']) ? $attendee['r1_hole'] : "").
												(isset($attendee['r2_hole']) ? "-".$attendee['r2_hole'] : ""). "
											</span>
											<small>Round 1</small><br /><input type='text' name='entries[".$aid."][r1_hole]' class='input input_xs' value='" .(isset($attendee['r1_hole']) ? $attendee['r1_hole'] : ""). "' /><br />
											<small>Round 2</small><br /><input type='text' name='entries[".$aid."][r2_hole]' class='input input_xs' value='" .(isset($attendee['r2_hole']) ? $attendee['r2_hole'] : ""). "' />
										</td>
										<td class='nopadding-h'>
											<span class='hidden'>".
												(isset($attendee['r1_time']) ? $attendee['r1_time'] : "").
												(isset($attendee['r2_time']) ? "-".$attendee['r2_time'] : ""). "
											</span>
											<small>Round 1</small><br /><input type='text' name='entries[".$aid."][r1_time]' class='input input_xs number' maxlength='4' value='" .(isset($attendee['r1_time']) ? $attendee['r1_time'] : ""). "' /><br />
											<small>Round 2</small><br /><input type='text' name='entries[".$aid."][r2_time]' class='input input_xs number' maxlength='4' value='" .(isset($attendee['r2_time']) ? $attendee['r2_time'] : ""). "' />
										</td>";
										if(!$row['team_event']){
											echo "<td class='nopadding-h'>
												<span class='hidden'>".
													(isset($attendee['r1_overunder']) ? $attendee['r1_overunder'] : "").
													(isset($attendee['r2_overunder']) ? "-".$attendee['r2_overunder'] : ""). "
												</span>
												<small>Round 1</small><br /><input type='text' name='entries[".$aid."][r1_overunder]' class='input input_xs' value='" .(isset($attendee['r1_overunder']) ? $attendee['r1_overunder'] : ""). "' /><br />
												<small>Round 2</small><br /><input type='text' name='entries[".$aid."][r2_overunder]' class='input input_xs' value='" .(isset($attendee['r2_overunder']) ? $attendee['r2_overunder'] : ""). "' />
											</td>";
										}
										echo "<td class='nopadding-h'>
											<span class='hidden'>".
												(isset($attendee['r1_score']) ? $attendee['r1_score'] : "").
												(isset($attendee['r2_score']) ? "-".$attendee['r2_score'] : ""). "
											</span>
											<small>Round 1</small><br /><input type='text' name='entries[".$aid."][r1_score]' class='input input_xs number' value='" .(isset($attendee['r1_score']) ? $attendee['r1_score'] : ""). "' /><br />
											<small>Round 2</small><br /><input type='text' name='entries[".$aid."][r2_score]' class='input input_xs number' value='" .(isset($attendee['r2_score']) ? $attendee['r2_score'] : ""). "' />
										</td>";

										//Team Events
										if($row['team_event']){
											echo "<td class='nopadding-h'>
												<span class='hidden'>".
													(isset($attendee['r1_team_gross']) ? $attendee['r1_team_gross'] : "").
													(isset($attendee['r2_team_gross']) ? "-".$attendee['r2_team_gross'] : ""). "
												</span>
												<small>Round 1</small><br /><input type='text' name='entries[".$aid."][r1_team_gross]' class='input input_xs number' value='" .(isset($attendee['r1_team_gross']) ? $attendee['r1_team_gross'] : ""). "' /><br />
												<small>Round 2</small><br /><input type='text' name='entries[".$aid."][r2_team_gross]' class='input input_xs number' value='" .(isset($attendee['r2_team_gross']) ? $attendee['r2_team_gross'] : ""). "' />
											</td>
											<td class='nopadding-h'>
												<span class='hidden'>".
													(isset($attendee['r1_team_net']) ? $attendee['r1_team_net'] : "").
													(isset($attendee['r2_team_net']) ? "-".$attendee['r2_team_net'] : ""). "
												</span>
												<small>Round 1</small><br /><input type='text' name='entries[".$aid."][r1_team_net]' class='input input_xs number' value='" .(isset($attendee['r1_team_net']) ? $attendee['r1_team_net'] : ""). "' /><br />
												<small>Round 2</small><br /><input type='text' name='entries[".$aid."][r2_team_net]' class='input input_xs number' value='" .(isset($attendee['r2_team_net']) ? $attendee['r2_team_net'] : ""). "' />
											</td>
											<td class='nopadding-h'>
												<span class='hidden'>".
													(isset($attendee['final_score']) ? $attendee['final_score'] : "").
													(isset($attendee['team_final_score']) ? "-".$attendee['team_final_score'] : ""). "
												</span>
												<small>Individual</small><br /><input type='text' name='entries[".$aid."][final_score]' class='input input_xs number' value='" .(isset($attendee['final_score']) ? $attendee['final_score'] : "0"). "' disabled /><br />
												<small>Team (G/N)</small><br /><input type='text' name='entries[".$aid."][team_final_score]' class='input input_xs number' value='" .(isset($attendee['team_final_score']) ? $attendee['team_final_score'] : "0"). "' disabled /><br />
											</td>
											<td class='nopadding-h'>
												<span class='hidden'>".
													(isset($attendee['prize']) ? $attendee['prize'] : "").
													(isset($attendee['team_prize']) ? "-".$attendee['team_prize'] : ""). "
												</span>
												<small>Individual</small><br /><input type='text' name='entries[".$aid."][prize]' class='input input_xs number' value='" .(isset($attendee['prize']) ? $attendee['prize'] : ""). "' /><br />
												<small>Team</small><br /><input type='text' name='entries[".$aid."][team_prize]' class='input input_xs number' value='" .(isset($attendee['team_prize']) ? $attendee['team_prize'] : ""). "' />
											</td>
											<td class='nopadding-h'>
												<span class='hidden'>".
													(isset($attendee['gc_prize']) ? $attendee['gc_prize'] : "").
													(isset($attendee['team_gc_prize']) ? "-".$attendee['team_gc_prize'] : ""). "
												</span>
												<small>Individual</small><br /><input type='text' name='entries[".$aid."][gc_prize]' class='input input_xs number' value='" .(isset($attendee['gc_prize']) ? $attendee['gc_prize'] : ""). "' /><br />
												<small>Team</small><br /><input type='text' name='entries[".$aid."][team_gc_prize]' class='input input_xs number' value='" .(isset($attendee['team_gc_prize']) ? $attendee['team_gc_prize'] : ""). "' />
											</td>";

										//Individual Events
										}else{
											echo "<td class='nopadding-h'>
												<span class='hidden'>" .(isset($attendee['final_score']) ? $attendee['final_score'] : ""). "</span>
												<input type='text' name='entries[".$aid."][final_score]' class='input input_xs number' value='" .(isset($attendee['final_score']) ? $attendee['final_score'] : "0"). "' disabled />
											</td>
											<td class='nopadding-h'>
												<span class='hidden'>" .(isset($attendee['prize']) ? $attendee['prize'] : ""). "</span>
												<input type='text' name='entries[".$aid."][prize]' class='input input_xs number' value='" .(isset($attendee['prize']) ? $attendee['prize'] : ""). "' />
											</td>
											<td class='nopadding-h'>
												<span class='hidden'>" .(isset($attendee['gc_prize']) ? $attendee['gc_prize'] : ""). "</span>
												<input type='text' name='entries[".$aid."][gc_prize]' class='input input_xs number' value='" .(isset($attendee['gc_prize']) ? $attendee['gc_prize'] : ""). "' />
											</td>";
										}

										echo "<td class='nopadding-h'>
											<span class='hidden'>" .(isset($attendee['points']) ? $attendee['points'] : ""). "</span>
											<input type='text' name='entries[".$aid."][points]' class='input input_xs number' value='" .(isset($attendee['points']) ? $attendee['points'] : ""). "' />
										</td>";
										echo "<td class='nopadding-h'>
											<span class='hidden'>".
												$attendee['r1_skins']."-".$attendee['r2_skins']. "
											</span>
											<small>Round 1</small><br /><input type='text' name='entries[".$aid."][r1_skins]' class='input input_xs' value='" .(isset($attendee['r1_skins']) ? $attendee['r1_skins'] : ""). "' /><br />
											<small>Round 2</small><br /><input type='text' name='entries[".$aid."][r2_skins]' class='input input_xs' value='" .(isset($attendee['r2_skins']) ? $attendee['r2_skins'] : ""). "' />
										</td>";

									echo "</tr>";
								}
								echo "</tbody>";

							}else{
								echo "<tbody><tr><td class='left'>No entries to display for this tournament.</td></tr></tbody>";
							}
						echo "</table>";

					echo "</div>";
				echo "</div>";

				//Sticky footer
				echo "<footer id='cms-footer' class='resize'>";
					echo "<button type='submit' name='save' value='save' class='button f_right'><i class='fa fa-check'></i>Save &amp; Continue</button>";
					if(ITEM_ID != "" && (!isset($row['deletable']) || $row['deletable'] == 1)){
						echo "<button type='button' name='delete' value='delete' class='button delete'><i class='fa fa-trash'></i>Delete ".EVENT_CODE."</button>";
					}
					echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
				echo "</footer>";

				echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
			echo "</form>";


		}

	//Final step
	}else if(STEP == 5){

		echo "<form action='' method='post' enctype='multipart/form-data'>";

		//Event extras
		if(EVENT_TYPE != 2){

			echo "<div class='panel'>";
				echo "<div class='panel-header'>" .EVENT_CODE. " Extras
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";

				echo "<div class='panel-content nopadding'>";
					echo "<div class='rows-wrapper'>";
						echo "<div class='row'><br /><p>Enter the name of the addon and select +Add Option to set options and their relative prices.<br /><small>Extras are attached to each ticket purchased. Ex. meals, accommodations, required materials, etc.</small></p></div>";
					echo "</div><hr class='nomargin' />";

					echo "<div id='addons-wrapper' class='rows-wrapper'>";
						$acount = 0;
						$ocount = 0;
						if(isset($row['addons']) && !empty($row['addons'])){
							foreach($row['addons'] as $addon) {
								echo "<div class='addon-template row clearfix'>";
									echo "<div class='form-field'>";
										echo "<label>Addon Name </label>";
										echo "<input type='text' name='addon_name[$acount]' value='".(isset($addon['name']) ? $addon['name'] : "")."' class='input' />";
									echo "</div>";

									echo "<div class='form-field'>";
										echo "<label>Required ".$CMSBuilder->tooltip('Addon Required', 'If an addon is set to <strong>Required</strong> users must select an option in order to continue registration.')."</label>";
										echo "<select name='addon_required[$acount]' class='select select_sm'>
											<option value='Optional'>No</option>
											<option value='Required'".(isset($addon['required']) && $addon['required'] == 'Required' ? "selected" : "").">Yes</option>
										</select>";
									echo "</div>";

									echo "<input type='hidden' name='addon_id[$acount]' value='".(isset($addon['addon_id']) ? $addon['addon_id'] : "")."' />";

								echo "</div>";

								echo "<div class='addon-options row clear' data-count='".$acount."'>";
									if(isset($addon['options']) && !empty($addon['options'])) {
										foreach($addon['options'] as $option) {
											echo "<div class='option-template clearfix'>";
												echo "<div class='form-field'>";
													echo "<label>Option Name </label>";
													echo "<input type='text' name='option_name[$acount][$ocount]' value='".(isset($option['name']) ? $option['name'] : "")."' class='input' />";
												echo "</div>";

												echo "<div class='form-field'>";
													echo "<label>Option Price ($)</label>";
													echo "<input type='text' name='option_price[$acount][$ocount]' value='".(isset($option['price_adjustment']) ? $option['price_adjustment'] : "")."' class='input number' />";
												echo "</div>";

												echo "<input type='hidden' name='option_id[$acount][$ocount]' value='".(isset($option['option_id']) ? $option['option_id'] : "")."' />";
											echo "</div>";

											$ocount++;
										}
									}

									echo "<a class='add-option-btn add-row-btn button-sm' href='#' onclick='addNewOption(this); return false;'>+ Add Option</a>";
								echo "</div>";

								$acount++;
							}

						}else{
							echo "<div class='addon-template row clearfix'>";
								echo "<div class='form-field'>";
									echo "<label>Addon Name </label>";
									echo "<input type='text' name='addon_name[$acount]' value='' class='input' />";
								echo "</div>";

								echo "<div class='form-field'>";
									echo "<label>Required ".$CMSBuilder->tooltip('Addon Required', 'If an addon is set to <strong>Required</strong> users must select an option in order to continue registration.')."</label>";
									echo "<select name='addon_required[$acount]' class='select select_sm'>
										<option value='Optional'>No</option>
										<option value='Required'>Yes</option>
									</select>";
								echo "</div>";

								echo "<input type='hidden' name='addon_id[$acount]' value='' />";

							echo "</div>";

							echo "<div class='addon-options row clear' data-count='".$acount."'>";
								echo "<div class='option-template clearfix'>";
									echo "<div class='form-field'>";
										echo "<label>Option Name </label>";
										echo "<input type='text' name='option_name[$acount][$ocount]' value='' class='input' />";
									echo "</div>";

									echo "<div class='form-field'>";
										echo "<label>Option Price ($)</label>";
										echo "<input type='text' name='option_price[$acount][$ocount]' value='' class='input number' />";
									echo "</div>";

									echo "<input type='hidden' name='option_id[$acount][$ocount]' value='' />";
								echo "</div>";

								echo "<a class='add-option-btn add-row-btn button-sm' href='#' onclick='addNewOption(this); return false;'>+ Add Option</a>";
							echo "</div>";
						}

						echo "<a id='add-addon-btn' class='add-row-btn button-sm' href='#' onclick='addNewAddon(); return false;'>+ Add Addon</a>";
					echo "</div>";
				echo "</div>";

			echo "</div>";


		//Tournament reports
		}else if(EVENT_TYPE == 2){

			echo "<div class='panel'>";
				echo "<div class='panel-header'>" .EVENT_CODE. " Reports
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content nopadding'>";
					echo "<table cellpadding='0' cellspacing='0' border='0' width='0'>
						<tr>
							<td width='185px'>
								<input type='checkbox' class='checkbox' name='report_drawalpha' id='report_drawalpha' value='1'" .(isset($row['report_drawalpha']) && $row['report_drawalpha'] == '1' ? ' checked' : ''). " />
								<label for='report_drawalpha'>Draw (Alphabetical)</label>";
							echo "</td>
							<td>";
								if(isset($row['occurrence_id'])){
									echo "<a href='" .$root. "reports/report-draw.php?id=" .$row['occurrence_id']. "&sort=alpha' target='_blank' />
										<i class='fa fa-file-pdf-o'></i> PDF
									</a> &nbsp;&nbsp;";
									echo "<a href='" .$root. "reports/report-draw.php?id=" .$row['occurrence_id']. "&sort=alpha&format=csv' target='_blank' />
										<i class='fa fa-file-excel-o'></i> CSV
									</a>";
								}
							echo "</td>
						</tr>
						<tr>
							<td>
								<input type='checkbox' class='checkbox' name='report_draw' id='report_draw' value='1'" .(isset($row['report_draw']) && $row['report_draw'] == '1' ? ' checked' : ''). " />
								<label for='report_draw'>Draw (Tee Time/Hole)</label>";
							echo "</td>
							<td>";
								if(isset($row['occurrence_id'])){
									echo "<a href='" .$root. "reports/report-draw.php?id=" .$row['occurrence_id']. "' target='_blank' />
										<i class='fa fa-file-pdf-o'></i> PDF
									</a> &nbsp;&nbsp;";
									echo "<a href='" .$root. "reports/report-draw.php?id=" .$row['occurrence_id']. "&format=csv' target='_blank' />
										<i class='fa fa-file-excel-o'></i> CSV
									</a>";
								}
							echo "</td>
						</tr>
						<tr>
							<td>
								<input type='checkbox' class='checkbox' name='report_drawscore' id='report_drawscore' value='1'" .(isset($row['report_drawscore']) && $row['report_drawscore'] == '1' ? ' checked' : ''). " />
								<label for='report_drawscore'>R1 Results &amp; R2 Draw</label>";
							echo "</td>
							<td>";
								if(isset($row['occurrence_id'])){
									echo "<a href='" .$root. "reports/report-draw.php?id=" .$row['occurrence_id']. "&sort=score' target='_blank' />
										<i class='fa fa-file-pdf-o'></i> PDF
									</a> &nbsp;&nbsp;";
									echo "<a href='" .$root. "reports/report-draw.php?id=" .$row['occurrence_id']. "&sort=score&format=csv' target='_blank' />
										<i class='fa fa-file-excel-o'></i> CSV
									</a>";
								}
							echo "</td>
						</tr>
						<tr>
							<td>
								<input type='checkbox' class='checkbox' name='report_results' id='report_results' value='1'" .(isset($row['report_results']) && $row['report_results'] == '1' ? ' checked' : ''). " />
								<label for='report_results'>Final Results</label>";
							echo "</td>
							<td>";
								if(isset($row['occurrence_id'])){
									echo "<a href='" .$root. "reports/report-results.php?id=" .$row['occurrence_id']. "' target='_blank' />
										<i class='fa fa-file-pdf-o'></i> PDF
									</a> &nbsp;&nbsp;";
									echo "<a href='" .$root. "reports/report-results.php?id=" .$row['occurrence_id']. "&format=csv' target='_blank' />
										<i class='fa fa-file-excel-o'></i> CSV
									</a>";
								}
							echo "</td>
						</tr>
						<tr>
							<td>
								<input type='checkbox' class='checkbox' name='report_money' id='report_money' value='1'" .(isset($row['report_money']) && $row['report_money'] == '1' ? ' checked' : ''). " />
								<label for='report_money'>Overall Money</label>";
							echo "</td>
							<td>";
								if(isset($row['occurrence_id'])){
									echo "<a href='" .$root. "reports/report-money.php?id=" .$row['occurrence_id']. "' target='_blank' />
										<i class='fa fa-file-pdf-o'></i> PDF
									</a> &nbsp;&nbsp;";
									echo "<a href='" .$root. "reports/report-money.php?id=" .$row['occurrence_id']. "&format=csv' target='_blank' />
										<i class='fa fa-file-excel-o'></i> CSV
									</a>";
								}
							echo "</td>
						</tr>
					</table>";
				echo "</div>";
			echo "</div>";

		}

			//Sticky footer
			echo "<footer id='cms-footer' class='resize'>";
				echo "<button type='submit' name='save' value='save' class='button f_right'><i class='fa fa-check'></i>Save &amp; Finish</button>";
				if(ITEM_ID != "" && (!isset($row['deletable']) || $row['deletable'] == 1)){
					echo "<button type='button' name='delete' value='delete' class='button delete'><i class='fa fa-trash'></i>Delete ".EVENT_CODE."</button>";
				}
				echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
			echo "</footer>";

			echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "</form>";

	}
}

?>