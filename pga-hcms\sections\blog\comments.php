<?php

//Table listing
if(ACTION == ''){
	
	include('includes/widgets/searchform.php');
	
	echo '<div class="panel">
		<div class="panel-header">Pending Comments
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
		
				<thead>
					<th>Commenter</th>
					<th>Date Posted</th>
					<th>Entry</th>
					<th>Comment</th>
					<th width="200px" align="right" class="right {sorter:false}">Approve' .$CMSBuilder->tooltip('Approve Comment','The comment will display publicly on the entry page once approved.'). ' or Delete' .$CMSBuilder->tooltip('Delete Comment','The comment will be permanently removed from the system.'). '</th>
				</thead>
				
				<tbody id="comments-pending">';

				foreach($blogcomments_pending as $row){
					echo '<tr>
						<td>' .($row['name'] ?: 'Anonymous').($row['email'] != '' ? '<br/><small>'.$row['email'].'</small>' : ''). '</td>
						<td>' .date('F d, Y',strtotime($row['date_added'])). '</td>
						<td>' .$row['title']. ' &nbsp; <a href="'.$siteurl.$root.$blog_page.date('mY', strtotime($row['post_date'])).'/'.$row['page'].'-'.$row['entry_id'].'/" target="_blank"><i class="fas fa-external-link-alt"></i></a></td>
						<td>' .$row['message']. '</td>
						<td class="approval-buttons right">
							<a href="" data-id="'.$row[$record_id].'" data-approve="1" class="approve-comment button-sm"><i class="fas fa-check nomargin"></i></a>
							<a href="" data-id="'.$row[$record_id].'" data-approve="-1" class="approve-comment button-sm delete"><i class="fas fa-trash-alt nomargin"></i></a>
						</td>
					</tr>';	
				}

				echo '</tbody>
			</table>';
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo '</div>	
	</div>
	
	<div class="panel">
		<div class="panel-header">Posted Comments
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
				<thead>
					<th>Commenter</th>
					<th>Date Posted</th>
					<th>Entry</th>
					<th>Comment</th>
					<th width="200px" align="right" class="right {sorter:false}">Disapprove' .$CMSBuilder->tooltip('Disapprove Comment','The comment will be removed from public display on the entry page but will be available for later use in the CMS.'). ' or Delete' .$CMSBuilder->tooltip('Delete Comment','The comment will be permanently removed from the system.'). '</th>
				</thead>
				
				<tbody id="comments-approved">';

				foreach($blogcomments_approved as $row){
					echo '<tr>
						<td>' .($row['name'] ?: 'Anonymous').($row['email'] != '' ? '<br/><small>'.$row['email'].'</small>' : ''). '</td>
						<td>' .date('F d, Y',strtotime($row['date_added'])). '</td>
						<td>' .$row['title']. ' &nbsp; <a href="'.$siteurl.$root.$blog_page.date('mY', strtotime($row['post_date'])).'/'.$row['page'].'-'.$row['entry_id'].'/" target="_blank"><i class="fas fa-external-link-alt"></i></a></td>
						<td>' .$row['message']. '</td>
						<td class="approval-buttons right">
							<a href="" data-id="'.$row[$record_id].'" data-approve="0" class="approve-comment button-sm"><i class="fas fa-ban nomargin"></i></a>
							<a href="" data-id="'.$row[$record_id].'" data-approve="-1" class="approve-comment button-sm delete"><i class="fas fa-trash-alt nomargin"></i></a>
						</td>
					</tr>';	
				}

				echo '</tbody>
			</table>';
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo '</div>	
	</div>
	
	<div id="approve-button-template" class="hidden">
		<a href="" data-id="{id}" data-approve="1" class="approve-comment button-sm"><i class="fas fa-check nomargin"></i></a>
		<a href="" data-id="{id}" data-approve="-1" class="approve-comment button-sm delete"><i class="fas fa-trash-alt nomargin"></i></a>
	</div>
	
	<div id="disapprove-button-template" class="hidden">
		<a href="" data-id="{id}" data-approve="0" class="approve-comment button-sm"><i class="fas fa-ban nomargin"></i></a>
		<a href="" data-id="{id}" data-approve="-1" class="approve-comment button-sm delete"><i class="fas fa-trash-alt nomargin"></i></a>
	</div>';

}
?>