<?php  
if(PAGE_ID == $_sitepages['partners']['page_id']) {
	
	//Define vars
	$panel_id = 40;// REPLACE_ME;
	$imagedir = 'images/partners/';

	$db->query("SELECT partner_categories.* FROM partner_categories 
		INNER JOIN partners ON partners.category_id = partner_categories.category_id
		WHERE partner_categories.showhide = 0 AND partners.showhide = 0 
		GROUP BY partner_categories.category_id
		ORDER BY partner_categories.ordering");
	$categories = $db->fetch_assoc('category_id');
	$tabbed = count($categories) != 1;

	//Get all partners
	$db->query("SELECT partners.*, partner_categories.name as category, partner_categories.page FROM partners INNER JOIN partner_categories ON partner_categories.category_id = partners.category_id WHERE partners.showhide = 0 AND partner_categories.showhide = 0 ORDER BY partners.ordering");
	$partners = $db->fetch_assoc('partner_id');

	$index = 0;
	foreach($partners as $partner_id => &$partner) {
		$partner['image'] = check_file($partner['image'], $imagedir);
		$partner['tab_id'] = $partner['page'].'-'.$partner['category_id'];
		unset($partner);
	}

	// if ($tabbed) {
	// 	foreach ($categories as $cat_id => $cat) {
	// 		$page['page_panels'][$panel_id]['panel_tabs'][$cat['page'].'-'.$cat_id] = [
	// 			'tab_id'  => $cat['page'].'-'.$cat_id,
	// 			'page'    => 'cat',
	// 			'title'   => $cat['name'],
	// 			'content' => ''
	// 		];
	// 	}
	// }
}
?>