<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN) {
	exit();
}

//Dashboard widget
if(SECTION_ID ==  $_cmssections['dashboard']) {
	$total_records = $db->get_record_count('resource_categories');
	$CMSBuilder->set_widget($_cmssections['resource_categories'], 'Total Resource Folders', $total_records);
}

if(SECTION_ID == $_cmssections['resource_categories']) {

	// Define vars
	$record_db    = 'resource_categories';
	$record_id    = 'category_id';
	$record_name  = 'Folder';
	$records_name = 'Resource Folders';

	// Validation
	$errors   = false;
	$required = [];
	$required_fields = ['name'];

	//Get committees
	$committees = [];
	$db->query("SELECT * FROM committees ORDER BY name ASC");
	$committees = $db->fetch_assoc('committee_id');

	//Get boards (static list)
	$boards = [
		1 => "Board of Directors",
		2 => "Advisory Board", 
		3 => "Assistants' Board of Directors",
		4 => "National Director"
	];

	// Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.name"
	];

	// Build search query
	if ($searchterm) {
		foreach ($searchable_fields as $key => $field) {
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' OR ', $searchable_fields).')';
	}

	// Get Records with permissions
	$db->query("SELECT $record_db.*, 
		GROUP_CONCAT(DISTINCT rcc.committee_id) as committees, 
		GROUP_CONCAT(DISTINCT rcb.board_id) as boards 
		FROM $record_db 
		LEFT JOIN resource_category_committees rcc ON rcc.$record_id = $record_db.$record_id 
		LEFT JOIN resource_category_boards rcb ON rcb.$record_id = $record_db.$record_id 
		$where 
		GROUP BY $record_db.$record_id 
		ORDER BY ordering, $record_id", $params);
	$records_arr = $db->fetch_assoc($record_id);

	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}


	// Not found
	if(ACTION == 'edit' && empty($records_arr[ITEM_ID])) {
		$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
		header('Location:' .PAGE_URL);
		exit();
	}

	// Get detailed permissions for edit
	if(ACTION == 'edit') {
		// Get committee permissions
		$db->query("SELECT committee_id FROM resource_category_committees WHERE category_id = ?", [ITEM_ID]);
		$committee_perms = $db->fetch_array();
		$records_arr[ITEM_ID]['committees'] = array_column($committee_perms, 'committee_id');

		// Get board permissions  
		$db->query("SELECT board_id FROM resource_category_boards WHERE category_id = ?", [ITEM_ID]);
		$board_perms = $db->fetch_array();
		$records_arr[ITEM_ID]['boards'] = array_column($board_perms, 'board_id');
	}


	// Delete item
	if(isset($_POST['delete'])) {
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()) {
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		} else {
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}

		header("Location: " .PAGE_URL);
		exit();


	// Save item
	} else if(isset($_POST['save'])) {

		// Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		// Generate page slug
		$pagename = clean_url($_POST['name']);

		if(!$errors) {

            $_POST['showhide'] = isset($_POST['showhide']) ? 0 : 1;
			// Insert to db
			$params = [
				'name'         => $_POST['name'],
				'page'         => $pagename,
				'ordering'     => $_POST['ordering'],
				'showhide'     => isset($_POST['showhide']) ? $_POST['showhide'] : 0,
				'date_added'   => date("Y-m-d H:i:s"),
				'last_updated' => date("Y-m-d H:i:s")
			];

			// For update, don't change date_added
			if(ACTION == 'edit'){
				unset($params['date_added']);
			}

			$db->insert($record_db, [$record_id => ITEM_ID] + $params, $params);
			$item_id = ACTION == 'edit' ? ITEM_ID : $db->insert_id();

			if(!$db->error()) {

				// Save board permissions
				if(ACTION == 'edit'){
					$db->query("DELETE FROM resource_category_boards WHERE category_id = ?".(!empty($_POST['boards']) ? " AND board_id NOT IN (".implode(",",$_POST['boards']).")" : ""), [$item_id]);
				}
				if(!empty($_POST['boards'])){
					foreach($_POST['boards'] as $board_id){
						$db->query("INSERT IGNORE INTO resource_category_boards (category_id, board_id) VALUES(?,?)", [$item_id, $board_id]);
					}
				}

				// Save committee permissions
				if(ACTION == 'edit'){
					$db->query("DELETE FROM resource_category_committees WHERE category_id = ?".(!empty($_POST['committees']) ? " AND committee_id NOT IN (".implode(",",$_POST['committees']).")" : ""), [$item_id]);
				}
				if(!empty($_POST['committees'])){
					foreach($_POST['committees'] as $committee_id){
						$db->query("INSERT IGNORE INTO resource_category_committees (category_id, committee_id) VALUES(?,?)", [$item_id, $committee_id]);
					}
				}

				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			} else {
				$CMSBuilder->set_system_alert('Unable to save record.', false);
			}

		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);	
		}

	}
}

?>