[{"name": "sendgrid/php-http-client", "version": "3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sendgrid/php-http-client.git", "reference": "3c4c35eafd364ebcfdbb0a37f655417beed8ee0f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sendgrid/php-http-client/zipball/3c4c35eafd364ebcfdbb0a37f655417beed8ee0f", "reference": "3c4c35eafd364ebcfdbb0a37f655417beed8ee0f", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "~4.4", "squizlabs/php_codesniffer": "~2.0"}, "time": "2016-11-17 22:45:31", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"SendGrid\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "el<PERSON>@thinkingserious.com"}], "description": "HTTP REST client, simplified for PHP", "homepage": "http://github.com/sendgrid/php-http-client", "keywords": ["api", "fluent", "http", "rest", "sendgrid"]}]