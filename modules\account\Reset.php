<?php  

if(PAGE_ID == $_sitepages['reset']['page_id']){
	$displayform = false;
	
	//Reset request
	if(isset($_GET['reset']) && !empty($_GET['reset'])){
		$public_key = $_GET['reset'];
		$account_id = $Account->validate_public_key($public_key);
		if($account_id){
			$displayform = true;
			if(isset($_POST['reset'])){
				if($_POST['xid'] == $_COOKIE['xid']){
					$new_password = $_POST['new_password'];
					$confirm_password = $_POST['confirm_password'];

					try{
						$Account->reset_password($new_password, $confirm_password, $account_id);
						$Account->clear_public_key($public_key);
						$displayform = false;
						$alert = $Account->alert('Password has been updated. Go to the <a href="'.$path.'">home page</a> or <a href="'.$_sitepages['login']['page_url'].'">login</a>.', true);
						
					}catch(Exception $e){
						$alert = $Account->alert($e->getMessage(), false);	
					}
				}else{
					$alert = $Account->alert('Invalid session. Please ensure cookies are enabled in your browser.', false);	
				}
			}					
		}else{
			$alert = $Account->alert('Password reset request has expired. You must <a href="#" data-open-hidden-modal="#forgot-password-modal">submit a new request.</a>', false);
		}
   	
   //Cancel request
   }else if(isset($_GET['cancel']) && !empty($_GET['cancel'])){
		$public_key = $_GET['cancel'];
		
		if($Account->validate_public_key($public_key)){
			if($Account->clear_public_key($public_key)){
				$alert = $Account->alert('Password reset request has been cancelled. Go to the <a href="'.$path.'">home page</a>', true);
			}else{
				$alert = $Account->alert('Unable to cancel password reset request.', false);	
			}
		}else{
			$alert = $Account->alert('Password reset request has expired. You must <a href="#" data-open-hidden-modal="#forgot-password-modal">submit a new request.</a>', false);	
		}

	//Missing info
	}else{
		$alert = $Account->alert('Unable to retrieve password reset information. You must <a href="#" data-open-hidden-modal="#forgot-password-modal">submit a new request.</a>', false);
	}
}

?>