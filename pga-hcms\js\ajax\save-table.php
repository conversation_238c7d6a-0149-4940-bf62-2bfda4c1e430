<?php

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

//Recursive Algorithm to update table
function update_table($parent_id, $pages, $ordering, $current_level) {
	global $db;
	$recursed = false;
	for($i = $ordering; $i < count($pages); $i++){
		if(isset($pages[$i]['level']) && $pages[$i]['level'] == $current_level + 1 && !$recursed){
			update_table($pages[$i - 1]['page_id'], $pages, $i, $pages[$i]['level']);
			$recursed = true;
		} else if(isset($pages[$i]['level']) && $pages[$i]['level'] < $current_level){
			return;	
		} else if (!isset($pages[$i]['level']) || $pages[$i]['level'] == $current_level) {
			$recursed = false;
			$params = array();
			array_push($params, ($i+1)); //Ordering
			if(isset($pages[$i]['level'])){
				array_push($params, $parent_id); //Parent Id
			}
			array_push($params, $pages[$i]['page_id']);  //page id			
			$db->query("UPDATE ".$pages[$i]['table']." SET ordering = ?".(isset($pages[$i]['level']) ? ", parent_id = ?" : "")." WHERE ".$pages[$i]['column']." = ?", $params);	
		}		
	}
}

$response = array();
$response['errors'] = false;
$response['content'] = '';

if(isset($_POST) && USER_LOGGED_IN){
			
	update_table(NULL, $_POST['pages'], 0, 1);
	$response['success'] = true;		
	
} else {
	$response['errors'] = true;
	$response['content'] = 'Unable to sort item. No data received or user is not logged in.';
}

echo json_encode($response);
	
?>