<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == 4) {
	$total_records = $db->get_record_count('forms');
	$CMSBuilder->set_widget($_cmssections['forms'], 'Total Forms', $total_records);
}
//var_dump(SECTION_ID);exit;
if(SECTION_ID == $_cmssections['forms']){

	//Define vars
	$record_db = 'forms';
	$record_id = 'form_id';
	$record_name = 'Form';

	$fieldsetpage = $CMSBuilder->get_section($_cmssections['form_fieldsets']);
	$fieldpage = $CMSBuilder->get_section($_cmssections['form_fields']);
	$submissionspage = $CMSBuilder->get_section($_cmssections['form_submissions']);
		
	//Validation
	$errors = false;
	$required = array();
	$required_fields = array('form_name'); 
	

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.form_name",
	];

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get records
	$db->query("SELECT `$record_db`.* FROM `$record_db` $where GROUP BY `$record_db`.`$record_id` ORDER BY `$record_db`.`form_name`", $params);
	$records_arr = $db->fetch_assoc($record_id);
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}

	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){	
			
			//Get fieldsets
			$db->query("SELECT * FROM `form_fields` WHERE `type` = ? && `form_id` = ? ORDER BY `ordering` ASC, `field_id` ASC", array('fieldset', ITEM_ID));
			$records_arr[ITEM_ID]['form_fields'] = [0 => []]; //default group
			$records_arr[ITEM_ID]['form_fields'] += $db->fetch_assoc('field_id');

			foreach($records_arr[ITEM_ID]['form_fields'] as &$fieldset){
				$fieldset['form_fields'] = [];
				unset($fieldset);
			}

			//Get fields
			$db->query("SELECT *, IFNULL(`parent_id`, 0) AS `parent_id` FROM `form_fields` WHERE `type` != ? && `form_id` = ? ORDER BY `ordering` ASC, `field_id` ASC", array('fieldset', ITEM_ID));
			if(!$db->error()){
				$fields = $db->fetch_array();
				foreach($fields as $field){
					$records_arr[ITEM_ID]['form_fields'][$field['parent_id']]['form_fields'][$field['field_id']] = $field;
				}
			}

			$row = $records_arr[ITEM_ID];

		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();	
		}
	}

	
	//Delete item
	if(isset($_POST['delete'])){
		
		//Foreign keys will cascade
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	
	//Save item
	} else if(isset($_POST['save'])){

		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);

		//Validate
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		if(!$errors) {
			$params = [
				'form_name' => $_POST['form_name'],
				'login' => $_POST['login'],
				'send_copy' => (isset($_POST['send_copy']) ? 1 : 0),
				'send_to' => $_POST['send_to'],
				'showhide' => $_POST['showhide'],
				'last_updated' => date('Y-m-d H:i:s')
			];

			$db->insert($record_db, array_merge([
				$record_id => ITEM_ID,
				'date_added' => date('Y-m-d H:i:s')
			], $params), $params);
		
			if(!$db->error()) {
				
				if($_POST['save'] == 'fieldset'){
					header("Location: " .$fieldsetpage['page_url']."?form_id=" .$db->insert_id(). "&action=add");
					exit();
				}else if($_POST['save'] == 'field'){
					header("Location: " .$fieldpage['page_url']."?form_id=" .$db->insert_id(). "&action=add");
					exit();
				}else{
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}

			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}

	}

}

?>