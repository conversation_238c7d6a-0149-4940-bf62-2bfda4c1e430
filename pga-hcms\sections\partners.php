<?php

//Table listing
if(ACTION == ''){

	include("includes/widgets/searchform.php");
	echo '<p class="f_right"><a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a></p>

	<div class="panel">
		<div class="panel-header">'.$records_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">
				<thead>
					<th width="1px" data-sorter="false"></th>
					<th width="1px" class="nopadding" data-sorter="false"></th>
					<th>Name</th>'.
					($categories_enabled ? '<th>Category</th>' : '').
					'<th width="1px" class="center">Visible</th>
					<th width="1px"></th>
				</thead>

				<tbody>';

				foreach($records_arr as $row){
					echo '<tr data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-id="'.$row[$record_id].'">
						<td class="handle"><i class="fas fa-arrows-alt"></i></td>
						<td class="nopadding">'.($row['image'] ? '<a href="'.$path.$imagedir.$row['image'].'" class="light-gallery" rel="prettyPhoto" title="'.$row['name'].'">'.render_gravatar($imagedir.$row['image']).'</a>' : '').'</td>
						<td>'.$row['name'].($row['url'] ? '<br><small>'.$row['url'].' &nbsp;&nbsp;<a href="'.$row['url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></small>' : '').'</td>'.
						($categories_enabled ? '<td>'.$CMSBuilder->item_link($row['category_name'] ?: '<small>N/A</small>', 'partner_categories', $row['category_id']).'</td>' : '').
						'<td class="center">'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
					</tr>';
				}

				echo '</tbody>
			</table>';

			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';
	

//Image cropping
}else if($CMSUploader->crop_queue()){
	include("includes/jcropimages.php");
	
//Display form
}else{
	$data  = $records_arr[ITEM_ID] ?? [];
	$row   = !isset($_POST['save']) ? $data : $_POST;
	$image = $data['image'] ?? NULL;

	echo '<form action="" method="post" enctype="multipart/form-data">';

		// Details
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Details
				<span class="panel-toggle fas fa-chevron-up"></span>

				<div class="panel-switch">
					<label>Show '.$record_name.'</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"'.(empty($row['showhide']) ? " checked" : "").' />
						
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>

			<div class="panel-content flex-container">
				<div class="form-field">
					<label>Name'.(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="name" class="input'.(in_array('name', $required) ? ' required' : '').'" value="'.($row['name'] ?? '').'" />
				</div>

				<div class="form-field">
					<label>Website URL'.(in_array('url', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Website URL', 'Item will be linked to the given URL.  If the URL is to an external website, ensure it starts with <b>https://</b>').'</label>
					<input type="text" name="url" value="'.($row['url'] ?? '').'" class="input'.(in_array('url', $required) ? ' required' : '').'" placeholder="https:// " />
				</div>';

		if($categories_enabled){
			echo '<div class="form-field">
				<label>Category'.(in_array('category_id', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
				<select name="category_id" class="select'.(in_array('category_id', $required) ? " required" : "").'">
					<option value=""> - Select -</option>';
				foreach($categories as $cat_id => $cat){
					echo '<option value="'.$cat_id.'"'.(($row['category_id'] ?? '') == $cat_id ? ' selected' : '').'>'.$cat['name'].($cat['showhide'] > 0 ? ' (Hidden)' : '').'</option>';
				}
				echo '</select>
			</div>';
		}

				echo '<div class="form-field">
					<label>Numerical Order'.(in_array('ordering', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.').'</label>
					<select name="ordering" class="select">
						<option value="101">Default</option>';

						for($i = 1; $i < 101; $i++){
							echo '<option value="'.$i.'"'.(($row['ordering'] ?? false) == $i ? 'selected' : '').'>'.$i.'</option>';	
						}

					echo '</select>
				</div>
			</div>
		</div>';

		// Image
		echo '<div class="panel">
			<div class="panel-header">Company Logo
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content flex-container">';

			if($image){
				echo '<div class="img-holder">
					<a href="' .$path.$imagedir.$image. '" class="light-gallery" target="_blank" title=""><img src="' .$path.$imagedir.$image. '" alt="" /></a>
					<input type="checkbox" class="checkbox" name="deleteimage" id="deleteimage" value="1">
				</div>';
			}

				echo '<div class="form-field">
					<label>Upload Image <span class="required">*</span>' .$CMSBuilder->tooltip('Upload Image', 'Image must be at least '.$CMSUploader::size_label('partners', 'image').' and smaller than '.$_max_filesize['megabytes'].'.'). '</label>
					<input type="file" class="input' .(in_array('image', $required) ? ' required' : ''). '" name="image" value="" />
				</div>
				
				<div class="form-field">
					<label>Alt Text <small>(SEO)</small>' .$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.'). '</label>
					<input type="text" name="image_alt" value="' .($row['image_alt'] ?? ''). '" class="input" />
				</div>
			</div>
		</div>';

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "</form>";

}

?>