<?php
//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

error_reporting(0);
ini_set('display_errors', 'off');

$response = [
	'errors'         => false,
	'error_fields'   => [],
	'msg_validation' => '',
	'new_comment'    => '',
];

$required_fields = [
	'email',
	'comment'
];

//Comments allowed
if($blog_settings['comments']) {

	//Cookie validation
	if($_POST['xid'] != $_COOKIE['xid']) {
		$response['errors'][] = 'Please make sure cookies are enabled on your browser then try again.';
	}

	//Recaptcha
	if (!validate_recaptcha()) {
		$response['errors'][] = 'Please verify you are not a robot.';
	}

	//Required fields validation
	foreach($required_fields as $field) {
		if(($_POST[$field] ?? '') === '') {
			$response['error_fields'][] = $field;
			$response['errors'][0] = '<p>Please fill in all required fields.</p>';
		}
	}

	//Email validation
	if(!checkmail($_POST['email'])) {
		$response['error_fields'][] = 'email';
		$response['errors'][] = '<p>Email address is invalid.</p>';
	}

	//Validated
	if(!$response['errors']) {
		if($Blog->post_comment($_POST['entry_id'], $_POST['comment'], $_POST['name'], $_POST['email'])) {
			if($blog_settings['comment_approval']) {
				$response['msg_validation'] = 'Thank you! Your comment has been submitted for approval.';
			
			} else {
				$response['msg_validation'] = 'Your comment was successfully posted!';

				/* Example: For more complex HTML, utilize a template file:
				ob_start();
				include('../../includes/templates/blog-comment.php');
				$response['new_comment'] = ob_get_clean(); 
				*/
				
				$response['new_comment'] = '<div class="blog-comment">
					<h5>' .($_POST['name'] ?: 'Anonymous').' &nbsp; <small class="tmlight">Just Now</small></h5>
					<p>'.nl2br($_POST['comment']).'</p>
				</div>';
			}

		} else {
			$response['errors'][] = 'An error occurred while submitting your comment. Please try again.';
		}
	}

// Commenting disabled
} else {
	$response['errors'][] = 'Commenting is currently disabled.';
}

//Has Errors
if($response['errors']) {
	$response['msg_validation'] = implode('', $response['errors']);
}

echo json_encode($response);

?>