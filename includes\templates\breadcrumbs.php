<?php if(!LANDING && PAGE_ID != $_sitepages['home']['page_id']){ ?>
	<nav id="breadcrumbs">
		<ol class="container" itemscope itemtype="http://schema.org/BreadcrumbList">
			<li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
				<a itemprop="item" href="<?php echo $_sitepages['home']['page_url']; ?>" target="_self"><span itemprop="name"><?php echo $_sitepages['home']['name']; ?></span></a>
				<meta itemprop="position" content="1" /><span class="arrow">&rsaquo;</span>
			</li>
			<?php
			foreach($breadcrumbs as $key=>$crumb){
				echo '<li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
					<a itemprop="item" href="'.$siteurl.$crumb['url'].'" target="_self"><span itemprop="name">'.$crumb['name'].'</span></a>
					<meta itemprop="position" content="'.($key+2).'" />'.(count($breadcrumbs) - 1 == $key ? '' : '<span class="arrow">&rsaquo;</span>').'
				</li>
				';
			}
			?>
		</ol>
	</nav>
<?php } ?>