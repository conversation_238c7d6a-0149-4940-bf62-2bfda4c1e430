@charset "utf-8";
/*
	core.less

*/


/*------ responsive ------*/

@path: "../../";
@tablet-p: ~"all and (min-width: 481px)";
@tablet-l: ~"all and (min-width: 769px)";
@notebook: ~"all and (min-width: 1025px)";
@desktop: ~"all and (min-width: 1367px)";
@widescreen: ~"all and (min-width: 1921px)";
@max-tablet-p: ~"all and (max-width: 480px)";
@max-tablet-l: ~"all and (max-width: 768px)";
@max-notebook: ~"all and (max-width: 1024px)";
@max-desktop: ~"all and (max-width: 1366px)";
@max-widescreen: ~"all and (max-width: 1920px)";


/*------ imports ------*/

@import "mixins.less";


/*------ reset ------*/

*{
	margin: 0;
	box-sizing: border-box;
}

body, html{
	width: 100%;
	height: 100%;
	text-align: left; // safari
}

main, article, aside, details, figcaption, figure, picture,
footer, header, hgroup, menu, nav, section, label{display: block; }

input, select, textarea, button{
	color: inherit;
	outline: none;
	.no-appearance();
}

input, select, textarea, button, th, td{
	font-size: inherit;
	font-family: inherit;
	line-height: normal;
	letter-spacing: inherit;
}

th, td{
	text-align: inherit;
	line-height: inherit;
}

button{
	background: none;
	border: 0;
	padding: 0;
	cursor: pointer;
	.trans(background-color);
	.trans(border-color);
	.trans(color);
	.trans(opacity);
}

select{
	background: none;
	text-overflow: ellipsis;
}

fieldset{
	padding: 0;
	border: 0;
}


/*------ typography ------*/

body, input, select, textarea, button, th, td{
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

p, ul, ol{
	padding: 0 0 20px;
	margin: 0;
}

ul, ol{
	margin: 0 0 0 40px; 

	& &{
		padding-bottom: 0; 
	}
}

ol ol{list-style-type: lower-alpha; }
ol ol ol{list-style-type: lower-roman; }

a{
	text-decoration: none;
	cursor: pointer;
	.trans(background-color);
	.trans(border-color);
	.trans(color);
	.trans(opacity);
}

h1, h2, h3, h4, h5, h6{padding: 0; }
h1{margin: 0; }
h2{margin: 0 0 30px; }
h3{margin: 0 0 25px; }
h4{margin: 0 0 20px; }
h5{margin: 0 0 15px; }
h6{margin: 0 0 10px; }

p, ul, ol, table, blockquote{
	+ h2{margin-top: 20px; }
	+ h3{margin-top: 15px; }
	+ h4{margin-top: 10px; }
	+ h5{margin-top: 5px; }
}

small{display: inline-block; }

blockquote p{padding: 0;}


/*------ interface ------*/

img{
	display: inline-block;
	border: 0;
	max-width: 100%;
	object-fit: cover;
	&:where([width][height]){height: auto; } // 0 specificity with :where
}

iframe{max-width: 100%; }

hr{
	height: 0;
	margin: 0 0 20px 0;
	padding: 0;
	border: 1px solid;
	border-width: 1px 0 0;
}

table{
	border-collapse: collapse;
	border-style: solid;
	margin: 10px 0 30px;

	tbody, thead, tr, th, td{
		text-align: inherit; 
		border-color: inherit; 
		border-style: inherit;
		border-collapse: inherit;
		border-width: inherit;
	}

	&:where(:not([border])){
		border-width: 1px;
	}

	&.responsive{
		.table-header{
			display: none; 
			margin: 0; 
		}
	}
}

// No styles, display content as columns
table.column{
	width: calc(100% + 20px) !important;
	margin: 0;
	padding: 0;
	table-layout: fixed;

	&, td, th{
		background-color: transparent !important;
		height: auto !important;
	}

	&:not(.mce-item-table) td,
	&:not(.mce-item-table) th,
	&{border: none !important; }

	td{
		padding: 0;
		vertical-align: top;
	}
}

@media @tablet-l{
	table.column{
		&:not(.mce-item-table){margin: 0 -10px; }

		td{padding: 0 10px; }
	}
}

@media @max-tablet-l {
	table.column{
		ul:only-child,
		ol:only-child{padding-bottom: 0px; }
		td:last-child ul:only-child,
		td:last-child ol:only-child{padding-bottom: 20px; }
	}

	// Responsive tables (below 768px)
	table{
		width: 100% !important;
		border: 1px solid;

		&.responsive{
			tr.header-row, th{display: none; }

			tr:not(:last-child) td:last-child,
			tr:not(:last-child) th:last-child{
				border-bottom-width: 0px;
			}

			td{
				display: block;
				width: auto !important;
				text-align: left;
			}

			.table-header{
				display: inline-block;
			}
		}
	}
}


/*---- embed media ----*/

a.embed-media{
	position: relative;
	display: inline-block;
	max-width: 100%;
	&:extend(img);

	img{display: block; }

	.play{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		left: 0;
		width: 100%;
		height: 100%;
		font-size: 36px;
		font-weight: normal;
		font-style: normal;
		text-align: center;
		line-height: 1;
		z-index: 1;
		color: #fff;
		background-color: rgba(0,0,0,0);
		.trans(background-color, 0.3s);
		.flexbox(row nowrap; center; center;);

		&::before{
			.font-awesome(f04b);
			text-shadow: 0 0 8px rgba(0,0,0,0.16);
		}
	}

	&:hover .play{background-color: rgba(0,0,0,0.5); }
}

iframe.embed-media{
	display: inline-block;
	background-color: rgb(15,15,15);
}


/*---- dialog ----*/

.ui-dialog{
	position: absolute;
	visibility: hidden;
	overflow: hidden;
	top: 0;
	left: 0;
}


/*---- light gallery ----*/

.lg-outer img{object-fit: cover; }


/*---- page structure ----*/

#page-wrapper{
	position: relative;
	width: 100%;
	height: auto;
}
#seo-wrapper{
	position: relative;
	z-index: 1;
}
#sitemap .menu-header{
	display: none; 
}

.panel-text{
	.clearfix();

	> :where(:last-child){
		margin-bottom: 0;
		padding-bottom: 0;
	}
}