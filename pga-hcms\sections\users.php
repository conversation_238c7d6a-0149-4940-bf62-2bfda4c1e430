<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<a href="' .PAGE_URL. '?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//All records
	$record_statuses = ['Requested', 'Users'];
	foreach($record_statuses as $status){
		echo '<div class="panel">
			<div class="panel-header">'.$status.'
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content nopadding">
				<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">

					<thead>
						<th width="1px" class="nopadding-r" data-sorter="false"></th>
						'.($avatar ? '<th width="1px" class="nopadding-r" data-sorter="false"></th>' : '').'
						<th width="250px">Name</th>
						<th>Username</th>
						<th>Type</th>
						<th>Status</th>
						<th>Email</th>
						<th width="1px" data-sorter="false">&nbsp;</th>
					</thead>

					<tbody>';
					foreach($records_arr as $row){
						if($status == 'Requested' && $row['status'] != 'Requested'){ continue; }
						if($status == 'Users' && $row['status'] == 'Requested'){ continue; }

						$lockout = ($row['locked'] ? $CMSBuilder->tooltip('Account Lockout', 'Account has been locked due to multiple failed login attempts. Lockout is set to expire on<br />'.date('F j, Y g:i:s A', strtotime($row['lockout_expiry'])).'.', '<i class="fas fa-lg required fa-lock"></i>') : '');

						echo '<tr class="' .($row['master'] ? 'bold' : ''). '">
							<td class="nopadding-r center">' .($row['master'] ? $CMSBuilder->tooltip('Master Administrator', '', '<i class="fas fa-sm fa-crown color-theme3"></i>', ['nopadding']) : ''). '</td>';

							if($avatar){
								echo '<td class="nopadding-r">' .($row['photo'] ? '<a href="' .$path.$imagedir.$row['photo']. '" class="light-gallery" title="' .$row['first_name'].' ' .$row['last_name']. '">' .render_gravatar($imagedir.'thumbs/'.$row['photo']). '</a>' : render_gravatar($imagedir.'thumbs/default.jpg')). '</td>';
							}

							echo '<td>' .$row['fullname']. '</td>
							<td>' .$row['username']. '</td>
							<td>' .(in_array(1, $row['roles']) ? $account_roles[1]['role_name'] : $account_roles[2]['role_name']). '</td>
							<td>' .$row['status'].' '.$lockout. '</td>
							<td>' .$row['email']. '</td>
							<td class="right"><a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
						</tr>';

					}
					echo '</tbody>
				</table>';

				//Pager
				$CMSBuilder->tablesorter_pager();

			echo '</div>
		</div>';
	}

//Image cropping
}else if($avatar && $CMSUploader->crop_queue()){
	include("includes/jcropimages.php");


//Display form
}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = $data['photo'];
		$master = $data['master'];
		if(!isset($_POST['save'])){
			$row = $data;
			$roles = $data['roles'];
			$permissions = $data['permissions'];
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		$image = '';
		$master = false;
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';

	//Account lockout
	if($row['locked'] ?? false){
		echo $CMSBuilder->important('<p>Account has been locked due to multiple failed login attempts. Lockout is set to expire on '.date('F j, Y g:i:s A', strtotime($row['lockout_expiry'])). '.</p><button type="submit" name="unlock" class="button-sm"><i class="fas fa-unlock"></i>Unlock</button>');
	}

	//Account details
	echo '<div class="panel">
		<div class="panel-header">Account Details
			<span class="panel-toggle fas fa-chevron-up"></span>
			<div class="panel-switch">
				<label>Show in Directory</label>
				<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="1"' .(($row['showhide'] ?? 1) == 0 ? ' checked' : ''). ' />
					<label for="showhide">
						<span class="inner"></span>
						<span class="switch"></span>
					</label>
				</div>
			</div>
		</div>
		<div class="panel-content">
			<div class="flex-container">';

				if($fullaccess){
					echo '<div class="form-field">
						<label>Account Status <span class="required">*</span>' .$CMSBuilder->tooltip('Account Status', 'Only accounts set to &quot;Active&quot; will be able to login. When creating a new account, set to &quot;Pending&quot; if you wish to have an activation email sent to the new user. Otherwise, a welcome email will be sent.'). '</label>
						<select name="status" class="select' .(in_array('status', $required) ? ' required' : ''). '"' .($master ? ' disabled' : ''). '>';
							$statuses = ['Active', 'Inactive', 'Suspended'];
							if(!isset($row['status']) || $row['status'] == 'Requested' || $row['status'] == 'Pending'){
								array_unshift($statuses, 'Pending');
							}
							if(($row['status'] ?? '') == 'Requested'){
								array_unshift($statuses, 'Requested');
							}
							foreach($statuses as $status){
								echo '<option value="' .$status. '"' .(($row['status'] ?? '') == $status ? ' selected' : ''). '>' .$status. '</option>';
							}
						echo '</select>
					</div>';

					echo "<div class='form-field'>
						<label>Account Type <span class='required'>*</span>" .$CMSBuilder->tooltip('Account Type', 'Admin accounts have the ability to login to the CMS as well as the website. User accounts can only login to the website.'). "</label>
						<select name='roles[]' class='select animation-control'>
							<option value='2'" .(in_array(2, $roles) ? " selected" : ""). ">User</option>
							<option value='1'" .(in_array(1, $roles) && !in_array(2, $roles) ? " selected" : ""). " data-toggle='.admin-field'>Admin</option>
						</select>
					</div>";

				// NEW: Account Group Dropdown
				echo '<div class="form-field">
					<label>Account Group ' . $CMSBuilder->tooltip('Account Group', 'Categorization used for searching and filtering of accounts.'). '</label>
					<select name="group_id" class="select' . (in_array('group_id', $required ?? []) ? ' required' : '') . '">'; // Check $required array
						echo '<option value="">- None -</option>'; // Default empty option
						if (!empty($account_groups) && is_array($account_groups)) {
							foreach ($account_groups as $group_id_key => $group_data) {
								// Use $group_id_key as value, $group_data['group_name'] as text
								// Check against $row['group_id'] for selected state
								$selected_group = (isset($row['group_id']) && $row['group_id'] == $group_id_key) ? ' selected' : '';
								echo '<option value="' . htmlspecialchars($group_id_key, ENT_QUOTES, 'UTF-8') . '"' . $selected_group . '>' . htmlspecialchars($group_data['group_name'], ENT_QUOTES, 'UTF-8') . '</option>';
							}
						}
					echo '</select>
				</div>';
				// END: Account Group Dropdown
				}

				echo '<div class="form-field">
					<label>First Name' .(in_array('first_name', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="first_name" value="' .($row['first_name'] ?? ''). '" class="input' .(in_array('first_name', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Last Name' .(in_array('last_name', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="last_name" value="' .($row['last_name'] ?? ''). '" class="input' .(in_array('last_name', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Email Address' .(in_array('email', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="email" value="' .($row['email'] ?? ''). '" class="input' .(in_array('email', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Alternate Email Address' .(in_array('email_alt', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="email_alt" value="' .($row['email_alt'] ?? ''). '" class="input' .(in_array('email_alt', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Username' .(in_array('username', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="username" value="' .($row['username'] ?? ''). '" class="input' .(in_array('username', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Date of Birth' .(in_array('dob', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="dob" value="' .($row['dob'] ?? ''). '" class="input datepicker' .(in_array('dob', $required) ? ' required' : ''). '  autocomplete="off" />
				</div>

				<div class="form-field">
					<label>Title' .(in_array('title', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="title" value="' .($row['title'] ?? ''). '" class="input' .(in_array('title', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Company' .(in_array('company', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="company" value="' .($row['company'] ?? ''). '" class="input' .(in_array('company', $required) ? ' required' : ''). '" />
				</div>

				<div class="form-field">
					<label>Gender'.(in_array('gender', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>';
				echo '<select name="gender" class="select">
					<option value=""' .(isset($row['gender']) && $row['gender'] == '' ? ' selected' : ''). '>- Select -</option>"';
					foreach($gender_types as $gender){
						echo "<option value=" .$gender. "" .(isset($row["gender"]) && $row["gender"] == $gender ? " selected" : ""). ">" .$gender. "</option>";
					}
				echo '</select>';
				echo '</div>';

			echo '</div>
		</div>
	</div>'; //Account details

	// Facility
	echo '<div class="panel">
		<div class="panel-header">Facility Details
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">';
			echo "<div class='form-field'>
				<label>Current Facility</label>
				<select name='facility_id' class='select" .(in_array('facility_id', $required) ? ' required' : ''). "'>
				<option value=''>- None -</option>";
				foreach($facilities as $facility){
					echo "<option value='" .$facility['facility_id']. "'" .(isset($row['facility_id']) && $row['facility_id'] == $facility['facility_id'] ? ' selected' : ''). ">" .$facility['facility_name']. "</option>";
				}
				echo '</select>
			</div>';



			if(isset($row['facility_id']) && array_key_exists($row['facility_id'], $facilities)){
				$facility = $facilities[$row['facility_id']];
				// echo "<pre>";
				// print($facility);
				// echo "</pre>";

				$facility_url = $siteurl.$root.get_page_url($_sitepages['facilities']).$facility['page'].'-'.$facility['facility_id'].'/';
				echo "<p class='clear'>
					<strong>" .$facility['facility_name']. "</strong>&nbsp; <a href='" .$facility_url. "' target='_blank'><i class='fa fa-external-link'></i></a><br />".
					(!empty($facility['phone']) ? "P. ".$facility['phone']."<br />" : "").
					(!empty($facility['email']) ? "E. <a href='mailto:" .$facility['email']. "'>".$facility['email']."</a><br />" : "").
					(!empty($facility['address2']) ? $facility['address2']." - " : "").
					(!empty($facility['address1']) ? $facility['address1']."<br />" : "").
					(!empty($facility['city']) ? $facility['city'].", " : "").
					(!empty($facility['province']) ? $facility['province']." " : "").
					(!empty($facility['postal_code']) ? $facility['postal_code'] : "").
				"</p>";
			}
			echo '</div>
		</div>
	</div>'; //Facility

	//Account password
	echo '<div class="panel">
		<div class="panel-header">' .(ITEM_ID != '' ? 'Change' : 'Account'). ' Password
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">
				<input type="password" name="" class="hidden" />

				<div class="form-field">
					<label>' .(ITEM_ID != '' ? 'New ' : ''). 'Password' .(in_array('password', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="password" name="password" value="" class="input' .(in_array('password', $required) ? ' required' : ''). '" autocomplete="new-password" />
				</div>

				<div class="form-field">
					<label>Re-enter ' .(ITEM_ID != '' ? 'New ' : ''). 'Password' .(in_array('password2', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="password" name="password2" value="" class="input' .(in_array('password2', $required) ? ' required' : ''). '" autocomplete="new-password" />
				</div>

			</div>
		</div>
	</div>'; //Account password

	// --- NEW: Member Details Panel ---
	echo '<div class="panel">
		<div class="panel-header">Member Details
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="flex-container">'; // Opening flex-container for Member Details

				// PGA Membership Number
				echo '<div class="form-field">
					<label>PGA Membership Number' . (in_array('pga_number', $required_fields ?? []) ? ' <span class="required">*</span>' : '') . '</label>
					<input type="text" name="pga_number" value="' . htmlspecialchars($row['pga_number'] ?? '', ENT_QUOTES, 'UTF-8') . '" class="input' . (in_array('pga_number', $required ?? []) ? ' required' : '') . '" />
				</div>';

				// Membership Type (Dropdown)
				echo '<div class="form-field">
					<label>Membership Type' . (in_array('membership_id', $required_fields ?? []) ? ' <span class="required">*</span>' : '') . '</label>
					<select name="membership_id" class="select' . (in_array('membership_id', $required ?? []) ? ' required' : '') . '">';
						echo '<option value="">- Select Type -</option>';
						if (!empty($member_types) && is_array($member_types)) {
							foreach ($member_types as $type_id => $type_data) {
								$selected = (isset($row['membership_id']) && $row['membership_id'] == $type_id) ? ' selected' : '';
								echo '<option value="' . htmlspecialchars($type_id, ENT_QUOTES, 'UTF-8') . '"' . $selected . '>' . htmlspecialchars($type_data['membership_name'], ENT_QUOTES, 'UTF-8') . '</option>';
							}
						}
					echo '</select>
				</div>';

				// Member Classification (Dropdown) - Assuming this maps to class_id
				echo '<div class="form-field">
					<label>Member Classification' . (in_array('class_id', $required_fields ?? []) ? ' <span class="required">*</span>' : '') . '</label>
					<select name="class_id" class="select' . (in_array('class_id', $required ?? []) ? ' required' : '') . '">';
						echo '<option value="">- Select Classification -</option>';
						if (!empty($member_classes) && is_array($member_classes)) {
							foreach ($member_classes as $class_id_key => $class_data) {
								$selected = (isset($row['class_id']) && $row['class_id'] == $class_id_key) ? ' selected' : '';
								echo '<option value="' . htmlspecialchars($class_id_key, ENT_QUOTES, 'UTF-8') . '"' . $selected . '>' . htmlspecialchars($class_data['class_name'], ENT_QUOTES, 'UTF-8') . '</option>';
							}
						}
					echo '</select>
				</div>';

				// Member Since (Date Picker)
				$pga_member_since_value = '';
				if (!empty($row['pga_member_since']) && $row['pga_member_since'] != '0000-00-00') {
					// Assuming it's stored as YYYY-MM-DD, which datepicker input type="date" prefers
					// If it's a different format, you might need to reformat it here:
					// $pga_member_since_value = date('Y-m-d', strtotime($row['pga_member_since']));
					$pga_member_since_value = $row['pga_member_since'];
				}
				echo '<div class="form-field">
					<label>Member Since' . (in_array('pga_member_since', $required_fields ?? []) ? ' <span class="required">*</span>' : '') . '</label>
					<input type="date" name="pga_member_since" value="' . htmlspecialchars($pga_member_since_value, ENT_QUOTES, 'UTF-8') . '" class="input datepicker' . (in_array('pga_member_since', $required ?? []) ? ' required' : '') . '" autocomplete="off" />
				</div>';

				//
				  // Tournament Eligibility (category_id)
            echo '<div class="form-field">
                <label>Tournament Eligibility</label>
                <select name="category_id" class="select' . (in_array('category_id', $required ?? []) ? ' required' : '') . '">';
                    echo '<option value="">- None -</option>';
                    if (!empty($member_categories) && is_array($member_categories)) {
                        foreach ($member_categories as $cat_id => $cat_data) {
                            $selected = (isset($row['category_id']) && $row['category_id'] == $cat_id) ? ' selected' : '';
                            echo '<option value="' . htmlspecialchars($cat_id, ENT_QUOTES, 'UTF-8') . '"' . $selected . '>' . htmlspecialchars($cat_data['name'], ENT_QUOTES, 'UTF-8') . '</option>';
                        }
                    }
                echo '</select>
            </div>';



        echo '</div>'; // Closing flex-container for top row

        // Second row for Board Member Role and Reward Card No.
        echo '<div class="flex-container member-details-flex-bottom-row">';
			// Board Member
            $board_member_options = [
                '0' => 'No',
                '1' => "Yes - Board of Directors",
                '2' => "Yes - Advisory Board",
                '3' => "Yes - Assistants' Board of Directors",
                '4' => "Yes - National Director"
            ];
            echo '<div class="form-field">
                <label>Board Member?</label>
                <select name="board_member" class="select board-member-select">'; // Added class for JS
                    foreach ($board_member_options as $bm_val => $bm_label) {
                        $selected = (isset($row['board_member']) && (string)$row['board_member'] === (string)$bm_val) ? ' selected' : '';
                        echo '<option value="' . htmlspecialchars($bm_val, ENT_QUOTES, 'UTF-8') . '"' . $selected . '>' . htmlspecialchars($bm_label, ENT_QUOTES, 'UTF-8') . '</option>';
                    }
                echo '</select>
            </div>';

            // Board Member Role (Initially hidden by CSS, shown by JS)
            $board_role_display_style = (isset($row['board_member']) && $row['board_member'] != '0') ? '' : 'display:none;';
            echo '<div class="form-field board-role-field" style="' . $board_role_display_style . '">
                <label>Board Member Role</label>
                <select name="board_member_role" class="select">';
                    echo '<option value="">- Select Role -</option>';
                    // $board_roles is fetched in the module
                    if (!empty($board_roles) && is_array($board_roles)) {
                        foreach ($board_roles as $role_val) {
                            $selected = (isset($row['board_member_role']) && $row['board_member_role'] == $role_val) ? ' selected' : '';
                            echo '<option value="' . htmlspecialchars($role_val, ENT_QUOTES, 'UTF-8') . '"' . $selected . '>' . htmlspecialchars($role_val, ENT_QUOTES, 'UTF-8') . '</option>';
                        }
                    }
                echo '</select>
            </div>';

            // Reward Card No.
            echo '<div class="form-field">
                <label>Reward Card No.</label>
                <input type="text" name="rewards_number" value="' . htmlspecialchars($row['rewards_number'] ?? '', ENT_QUOTES, 'UTF-8') . '" class="input" />
            </div>';
        echo '</div>'; // Closing flex-container for bottom row

        // Member of Committee(s) - Checkboxes
        	echo '<div class="form-field full-width-field committee-checkbox-group">
            <label>Member of Committee(s):</label>
            <div class="checkbox-list">'; // Wrapper for checkbox columns
                if (!empty($committees) && is_array($committees)) {
                    foreach ($committees as $committee_id => $committee_data) {
                        $checked = (isset($row['committees']) && is_array($row['committees']) && in_array($committee_id, $row['committees'])) ? ' checked' : '';

                        echo '<div>
                                <input type="checkbox" value="' . htmlspecialchars($committee_id, ENT_QUOTES, 'UTF-8') . '" id="committee_' . htmlspecialchars($committee_id, ENT_QUOTES, 'UTF-8') . '" name="committees[]" class="checkbox"' . $checked . ' />
                                <label for="committee_' . htmlspecialchars($committee_id, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($committee_data['name'], ENT_QUOTES, 'UTF-8') . '</label>
                              </div>';
                    }
                } else {
                    echo '<p><small>No committees available.</small></p>';
                }
            echo '</div>
        </div>';
				//

			// echo '</div>'; // Closing flex-container for Member Details
		echo '</div>
	</div>'; // END: Member Details Panel

	//Account permissions
	if($fullaccess){
		$admin_account = (in_array(1, $roles) && !in_array(2, $roles));

		echo '<div class="panel admin-field"' .(!$admin_account ? ' style="display:none;"' : ''). '>
			<div class="panel-header">Account Permissions' .$CMSBuilder->tooltip('Account Permissions', 'User will have access to manage entries in the enabled sections.'). '
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>';

			if($master){
				echo '<div class="panel-content">
					<p><em>Master accounts have full access.</em></p>
				</div>';

			}else{

				echo '<div class="panel-content nopadding">
					<table cellpadding="0" cellspacing="0" border="0" width="100%">
						<tr>
							<th colspan="4">System</th>
						</tr>
						<tr>';

						//Roles and ungrouped sections
						$count=0;
						foreach($Account->get_account_roles() as $role){
							if($role['role_id'] > 3){
								if($count && $count%2==0) echo '</tr><tr>';
								$count++;

								echo '<td width="1px" class="nopadding-r">
									<div class="onoffswitch">
										<input type="checkbox" value="' .$role['role_id']. '" id="role_' .$role['role_id']. '" name="roles[]"' .(in_array($role['role_id'], $roles) ? ' checked' : ''). '>
										<label for="role_' .$role['role_id']. '">
											<span class="inner"></span>
											<span class="switch"></span>
										</label>
									</div>
								</td>
								<td width="'.($count%2 ? '200px' : 'auto').'">' .$role['role_name']. '</td>';
							}
						}

						foreach($sitemap as $nav){
							if(!$nav['parent_id'] && !$nav['group_id'] && $nav['section_id'] > 4){
								if($count && $count%2==0) echo '</tr><tr>';
								$count++;

								echo '<td width="1px" class="nopadding-r">
									<div class="onoffswitch">
										<input type="checkbox" value="' .$nav['section_id']. '" id="section_' .$nav['section_id']. '" name="permissions[]"' .(in_array($nav['section_id'], $permissions) ? ' checked' : ''). '>
										<label for="section_' .$nav['section_id']. '">
											<span class="inner"></span>
											<span class="switch"></span>
										</label>
									</div>
								</td>
								<td width="'.($count%2 ? '200px' : 'auto').'">' .$nav['name']. '</td>';
							}
						}

						//Add dummy cells for odd number
						if($count%2) echo '<td></td><td></td>';

						echo '</tr>';

						//Grouped sections
						$section_groups = $CMSBuilder->fetch_section_groups();
						foreach($section_groups as $group_id=>$group){

							$count=0;
							$html = '';
							foreach($sitemap as $nav){
								if(!$nav['parent_id'] && $nav['group_id'] == $group_id && $nav['section_id'] > 4){
									if($count && $count%2==0) $html .= '</tr><tr>';
									$count++;

									$html .= '<td width="1px" class="nopadding-r">
										<div class="onoffswitch">
											<input type="checkbox" value="' .$nav['section_id']. '" id="section_' .$nav['section_id']. '" name="permissions[]"' .(in_array($nav['section_id'], $permissions) ? ' checked' : ''). '>
											<label for="section_' .$nav['section_id']. '">
												<span class="inner"></span>
												<span class="switch"></span>
											</label>
										</div>
									</td>
									<td width="'.($count%2 ? '200px' : 'auto').'">' .$nav['name']. '</td>';
								}
							}

							//Only display group if it has sections
							if($count){
								echo '<tr>
									<th colspan="4">' .$group['group_name']. '</th>
								</tr>
								<tr>';
									echo $html;

									//Add dummy cells for odd number
									if($count%2) echo '<td></td><td></td>';

								echo '</tr>';
							}
						}

					echo '</table>
				</div>';

			}
		echo '</div>';

	} //Account permissions

	//Contact details
	echo '<div class="panel">
		<div class="panel-header">Contact Details
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">

			<div class="flex-container">
				<div class="form-field">
					<label>Phone Number' .(in_array('phone', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="phone" value="' .($row['phone'] ?? ''). '" class="input' .(in_array('phone', $required) ? ' required' : ''). '" />
				</div>
			</div>

			<div class="flex-container">
				<div class="form-field">
					<label>Street Address' .(in_array('address1', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="address1" value="' .($row['address1'] ?? ''). '" class="input' .(in_array('address1', $required) ? ' required' : ''). '" />
				</div>
				<div class="form-field">
					<label>Unit No.' .(in_array('address2', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="address2" value="' .($row['address2'] ?? ''). '" class="input' .(in_array('address2', $required) ? ' required' : ''). '" />
				</div>
				<div class="form-field">
					<label>City/Town' .(in_array('city', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="city" value="' .($row['city'] ?? ''). '" class="input' .(in_array('city', $required) ? ' required' : ''). '" />
				</div>
			</div>

			<div class="flex-container">
				<div class="form-field">
					<label>Province/State' .(in_array('province', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<select name="province" class="select' .(in_array('province', $required) ? ' required' : ''). '">
						<option value="">- Select -</option>
						<optgroup label="Canada">';
							foreach($provinces as $code=>$name){
								echo '<option value="' .$code. '"' .(($row['province'] ?? '') == $code ? ' selected' : ''). '>' .$name. '</option>';
							}
						echo '</optgroup>
						<optgroup label="United States">';
							foreach($states as $code=>$name){
								echo '<option value="' .$code. '"' .(($row['province'] ?? '') == $code ? ' selected' : ''). '>' .$name. '</option>';
							}
						echo '</optgroup>
					</select>
				</div>
				<div class="form-field">
					<label>Postal/Zip Code' .(in_array('postalcode', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<input type="text" name="postalcode" value="' .($row['postalcode'] ?? ''). '" class="input' .(in_array('postalcode', $required) ? ' required' : ''). '" />
				</div>
				<div class="form-field">
					<label>Country' .(in_array('country', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<select name="country" class="select' .(in_array('country', $required) ? ' required' : ''). '">
						<option value="">- Select -</option>
						<option value="CA"' .(($row['country'] ?? '') == 'CA' ? ' selected' : ''). '>Canada</option>
						<option value="US"' .(($row['country'] ?? '') == 'US' ? ' selected' : ''). '>United States</option>
					</select>
				</div>
			</div>

		</div>
	</div>'; //Contact details

	// --- NEW: Social Networking Panel ---
	echo '<div class="panel">
		<div class="panel-header">Social Networking
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<p style="padding-bottom: 10px;"><small>Please Enter the Full URL (e.g., https://x.com/yourprofile):</small></p>
			<div class="flex-container social-networking-flex-container">';
				// Twitter
				echo '<div class="form-field input-with-icon">
					<span class="input-icon"><i class="fab fa-twitter"></i></span>
					<input type="url" name="twitter" value="' . htmlspecialchars($row['twitter'] ?? '', ENT_QUOTES, 'UTF-8') . '" class="input" placeholder="https://x.com/username" />
				</div>';

				// Facebook
				echo '<div class="form-field input-with-icon">
					<span class="input-icon"><i class="fab fa-facebook-f"></i></span>
					<input type="url" name="facebook" value="' . htmlspecialchars($row['facebook'] ?? '', ENT_QUOTES, 'UTF-8') . '" class="input" placeholder="https://facebook.com/profile" />
				</div>';

				// LinkedIn
				echo '<div class="form-field input-with-icon">
					<span class="input-icon"><i class="fab fa-linkedin-in"></i></span>
					<input type="url" name="linkedin" value="' . htmlspecialchars($row['linkedin'] ?? '', ENT_QUOTES, 'UTF-8') . '" class="input" placeholder="https://linkedin.com/in/profile" />
				</div>';

				// Instagram
				echo '<div class="form-field input-with-icon">
					<span class="input-icon"><i class="fab fa-instagram"></i></span>
					<input type="url" name="instagram" value="' . htmlspecialchars($row['instagram'] ?? '', ENT_QUOTES, 'UTF-8') . '" class="input" placeholder="https://instagram.com/username" />
				</div>';

			echo '</div>'; // Closing flex-container
		echo '</div>
	</div>'; // END: Social Networking Panel

	// tab panels for profile, qa
	//Account profile
		echo "<div class='panel-tabs'>";
			echo "<div class='tabs tab-ui'>";
				echo "<ul>";
					echo "<li><a href='#photo'>Photo</a></li>";
					echo "<li><a href='#profile'>Profile</a></li>";
					echo "<li><a href='#qa'>Q&amp;A</a></li>";
				echo "</ul>";

				//Photo
				echo "<div id='photo' class='clearfix'>";
				echo '<div class="panel-content">
				<div class="flex-container">';
					// 			echo "path : ".$path."<br>";
	// 			echo "imagedir : ".$imagedir."<br>";
	// 			echo "image : ".$image."<br>";
					if(!empty($image)){
						echo '<div class="img-holder">
							<button type="button" name="recrop" value="photo" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
							<a href="' .$path.$imagedir.$image. '" class="light-gallery" target="_blank" title="">
								<img src="' .$path.$imagedir.'thumbs/'.$image. '" alt="" />
							</a>
							<input type="checkbox" class="checkbox" name="deleteimage" id="deleteimage" value="1" />
							<label for="deleteimage">Delete Current Image</label>
						</div>';
					}

					echo '<div class="form-field">
						<label>Upload Image ' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploader::size_label('avatar', 'photo'). ' and file size must be smaller than '.$_max_filesize['megabytes'].'.'). '</label>
						<input type="file" class="input' .(in_array('photo', $required) ? ' required' : ''). '" name="photo" value="" />
					</div>';

				echo '</div>
			</div>';
				echo "</div>";

				//Profile
				echo "<div id='profile'>";
					echo "<div class='form-field'>
						<label>Education Background</label>
						<input type='text' name='education' value='" .(isset($row['education']) ? $row['education'] : ''). "' class='input" .(in_array('education', $required) ? ' required' : ''). "' />
					</div>";
					echo "<div class='clear'>
						<label>Profile</label>
						<textarea name='profile' class='tinymceMini' style='width:800px;'>" .(isset($row['profile']) ? $row['profile'] : ""). "</textarea>
					</div>";
				echo "</div>";

				//Questions
				if(!empty($profile_questions)){
					echo "<div id='qa' class='nopadding'>
						<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
							foreach($profile_questions as $q){
								$answer = (isset($_POST['question-'.$q['question_id']]) ? $_POST['question-'.$q['question_id']] : (isset($row['answers'][$q['question_id']]) ? $row['answers'][$q['question_id']] : ''));
								echo "<tr>
									<td>
										<br /><label>" .$q['question']. "</label>
										<input type='text' name='question-" .$q['question_id']. "' class='input' style='width:800px;' value='" .$answer. "' />
									</td>
								</tr>";
							}
						echo "</table>
					</div>";
				}

			echo "</div>";
		echo "</div>"; //Account profile
	//

	// --- NEW: Privacy Settings Panel ---
	echo '<div class="panel">
		<div class="panel-header">Privacy Settings
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="privacy-settings-container">'; // A container for all privacy rows
			$privacy_options = [
				'1'  => 'Public',
				'0'  => 'Private',
				'-1' => 'Members Only'
			];

			// Email Address Privacy
			echo '<div class="privacy-setting-row">
				<span class="privacy-label">Email Address</span>
				<div class="privacy-radio-group">';
					foreach ($privacy_options as $value => $label_text) { // Renamed $label to $label_text to avoid conflict
						// Determine if this radio button should be checked
						$is_checked = (isset($row['show_email']) && (string)$row['show_email'] === (string)$value);
						$checked_attribute = $is_checked ? ' checked' : ''; // Add leading space for valid HTML attribute

						// Use unique IDs for labels and inputs
						echo '<input type="radio" id="show_email_' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '" name="show_email" value="' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '"' . $checked_attribute . ' />
							<label for="show_email_' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($label_text, ENT_QUOTES, 'UTF-8') . '</label>';
					}
				echo '</div>
			</div>';

			// Repeat for show_phone, show_profile, show_qa, changing 'show_email' in the id, name, and $row[] accordingly:

			// Phone Number Privacy
			echo '<div class="privacy-setting-row">
				<span class="privacy-label">Phone Number</span>
				<div class="privacy-radio-group">';
					foreach ($privacy_options as $value => $label_text) {
						$is_checked = (isset($row['show_phone']) && (string)$row['show_phone'] === (string)$value);
						$checked_attribute = $is_checked ? ' checked' : '';
						echo '<input type="radio" id="show_phone_' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '" name="show_phone" value="' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '"' . $checked_attribute . ' />
							<label for="show_phone_' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($label_text, ENT_QUOTES, 'UTF-8') . '</label>';
					}
				echo '</div>
			</div>';

			// Profile Privacy
			echo '<div class="privacy-setting-row">
				<span class="privacy-label">Profile</span>
				<div class="privacy-radio-group">';
					foreach ($privacy_options as $value => $label_text) {
						$is_checked = (isset($row['show_profile']) && (string)$row['show_profile'] === (string)$value);
						$checked_attribute = $is_checked ? ' checked' : '';
						echo '<input type="radio" id="show_profile_' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '" name="show_profile" value="' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '"' . $checked_attribute . ' />
							<label for="show_profile_' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($label_text, ENT_QUOTES, 'UTF-8') . '</label>';
					}
				echo '</div>
			</div>';

			// Q&A Privacy
			echo '<div class="privacy-setting-row">
				<span class="privacy-label">Q&A</span>
				<div class="privacy-radio-group">';
					foreach ($privacy_options as $value => $label_text) {
						$is_checked = (isset($row['show_qa']) && (string)$row['show_qa'] === (string)$value);
						$checked_attribute = $is_checked ? ' checked' : '';
						echo '<input type="radio" id="show_qa_' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '" name="show_qa" value="' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '"' . $checked_attribute . ' />
							<label for="show_qa_' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($label_text, ENT_QUOTES, 'UTF-8') . '</label>';
					}
				echo '</div>
			</div>';
			echo '</div>'; // Closing .privacy-settings-container
		echo '</div>
	</div>'; // END: Privacy Settings Panel

	// --- NEW: Member on the Move (Changelog) Panel ---
	// This section is typically for display only within the user edit form.
	// The "Edit" button for individual log entries would link to a separate management page.
	if ($fullaccess && !empty($row['changelog']) && is_array($row['changelog'])) {
		echo '<div class="panel">
			<div class="panel-header">Member on the Move
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">'; // nopadding to make table flush
				echo '<table cellpadding="0" cellspacing="0" border="0" class="data-table fixed-layout-table">'; // Added class for styling
					// No <thead> needed if it's just a list of logs
					echo '<tbody>';
					foreach ($row['changelog'] as $log_entry) {
						$updater_name = 'Admin'; // Default
						if (isset($log_entry['updated_by']) && isset($allusers[$log_entry['updated_by']])) {
							$updater_name = htmlspecialchars($allusers[$log_entry['updated_by']]['first_name'] . ' ' . $allusers[$log_entry['updated_by']]['last_name'], ENT_QUOTES, 'UTF-8');
						} elseif (isset($log_entry['updated_by_name'])) { // If name is directly available
							$updater_name = htmlspecialchars($log_entry['updated_by_name'], ENT_QUOTES, 'UTF-8');
						}

						$updated_on_formatted = !empty($log_entry['updated_on']) ? date("M j, Y", strtotime($log_entry['updated_on'])) : 'N/A';

						echo '<tr>
								<td class="changelog-comment-cell">
									' . nl2br(htmlspecialchars($log_entry['comments'] ?? 'No comment.', ENT_QUOTES, 'UTF-8')) . '<br/>
									<small class="changelog-meta">Updated by ' . $updater_name . ' on ' . $updated_on_formatted . '</small>
								</td>
								<td class="changelog-action-cell right">'; // 'right' for text-align right
									// // if (isset($sitemap[43]['page_url'], $log_entry['id'])) {
									
									// working code commented 
									// 	echo '<a href="' .$siteurl.$path. htmlspecialchars('users/motm/' . '?action=edit&item_id=' . $log_entry['id'], ENT_QUOTES, 'UTF-8') . '" class="button button-sm"><i class="fas fa-edit"></i>Edit</a>';

									// // }
								echo '</td>
							</tr>';
					}
					echo '</tbody>
				</table>';
			echo '</div>
		</div>'; // END: Member on the Move Panel
	}


	// --- NEW: Account History Panel ---
	// Display if editing an existing user (ITEM_ID is set)
	if (ACTION == 'edit' && ITEM_ID != '') {
		echo '<div class="panel">
			<div class="panel-header">Account History
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">'; // nopadding to make table flush
				echo '<table cellpadding="0" cellspacing="0" border="0" class="data-table key-value-table">'; // Added class for styling
					echo '<tbody>';
						echo '<tr>
								<td class="history-label-cell">Registration Date:</td>
								<td class="history-value-cell">' . (!empty($row['register_time']) ? date("M j, Y g:iA", strtotime($row['register_time'])) : 'N/A') . '</td>
							</tr>';
						echo '<tr>
								<td class="history-label-cell">Activation Date:</td>
								<td class="history-value-cell">' . (!empty($row['activate_time']) ? date("M j, Y g:iA", strtotime($row['activate_time'])) : 'N/A') . '</td>
							</tr>';
						echo '<tr>
								<td class="history-label-cell">Last Updated:</td>
								<td class="history-value-cell">' . (!empty($row['last_updated']) ? date("M j, Y g:iA", strtotime($row['last_updated'])) : 'N/A') . '</td>
							</tr>';
						echo '<tr>
								<td class="history-label-cell">Last Login:</td>
								<td class="history-value-cell">' . (!empty($row['last_login']) ? date("M j, Y g:iA", strtotime($row['last_login'])) : 'N/A') . '</td>
							</tr>';
					echo '</tbody>
				</table>';
			echo '</div>
		</div>'; // END: Account History Panel
	}

	// //Profile image
	// if($avatar){
	// 	echo '<div class="panel">
	// 		<div class="panel-header">Profile Image
	// 			<span class="panel-toggle fas fa-chevron-up"></span>
	// 		</div>
	// 		<div class="panel-content">
	// 			<div class="flex-container">';

	// 			echo "path : ".$path."<br>";
	// 			echo "imagedir : ".$imagedir."<br>";
	// 			echo "image : ".$image."<br>";

	// 				if(!empty($image)){
	// 					echo '<div class="img-holder">
	// 						<button type="button" name="recrop" value="photo" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
	// 						<a href="' .$path.$imagedir.$image. '" class="light-gallery" target="_blank" title="">
	// 							<img src="' .$path.$imagedir.'thumbs/'.$image. '" alt="" />
	// 						</a>
	// 						<input type="checkbox" class="checkbox" name="deleteimage" id="deleteimage" value="1" />
	// 						<label for="deleteimage">Delete Current Image</label>
	// 					</div>';
	// 				}

	// 				echo '<div class="form-field">
	// 					<label>Upload Image ' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploader::size_label('avatar', 'photo'). ' and file size must be smaller than '.$_max_filesize['megabytes'].'.'). '</label>
	// 					<input type="file" class="input' .(in_array('photo', $required) ? ' required' : ''). '" name="photo" value="" />
	// 				</div>';

	// 			echo '</div>
	// 		</div>
	// 	</div>'; //Profile image
	// }

	// --- NEW: Notes Panel ---
	echo '<div class="panel">
		<div class="panel-header">Notes ' . $CMSBuilder->tooltip('Notes', 'Notes are for internal use only. They will not appear on the front-end of the website.') . '
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content">
			<div class="form-field full-width-field">'; // Using a single full-width field for the textarea
				// No explicit label needed above textarea if panel header serves as the title
				// If you want a <label> tag, you can add: <label for="notes_textarea">Internal Notes:</label>
				echo '<textarea name="notes" id="notes_textarea" class="textarea" rows="6" placeholder="Enter internal notes here...">' . htmlspecialchars($row['notes'] ?? '', ENT_QUOTES, 'UTF-8') . '</textarea>
			</div>
		</div>
	</div>'; // END: Notes Panel

	//Sticky footer
	echo '<footer id="cms-footer">
		<div class="flex-container">
			<div class="flex-column right">
				<button type="submit" class="button" name="save" value="save"><i class="fas fa-check"></i>Save Changes</button>
			</div>';
			if(SECTION_ID != $_cmssections['account']){
				echo '<div class="flex-column left">';
					if(ITEM_ID != "" && USER_LOGGED_IN != ITEM_ID){
						echo ($master ? $CMSBuilder->tooltip('Delete User', 'Master accounts cannot be deleted.').'&nbsp;' : '');
						echo '<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn" data-confirm="Are you sure you want to permanently delete this account? This action is NOT undoable. You can also deactivate this account instead."' .($master ? ' disabled' : ''). '><i class="fas fa-trash-alt"></i>Delete</button>';
					}
					echo '<a href="' .PAGE_URL. '" class="cancel">Cancel</a>';
				echo '</div>';
			}
		echo '</div>
	</footer>';

	echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid'] .'" />
	</form>';

}

?>