<?php

// Check for valid login
if (!defined('USER_LOGGED_IN') || !USER_LOGGED_IN) {
	exit();
}

// Dashboard widget
if (SECTION_ID == $_cmssections['dashboard']) {
	$total_records = $db->get_record_count('career_locations');
	$CMSBuilder->set_widget($_cmssections['career_locations'], 'Total Career Locations', $total_records);
}

if (SECTION_ID == $_cmssections['career_locations']) {

	// Define vars
	$record_db    = 'career_locations';
	$record_id    = 'location_id';
	$record_name  = 'Career Location';
	$record_names = $record_name.'s';
	
	$records_arr = [];

	// Validation
	$errors   = false;
	$required = [];
	$required_fields = ['name'];

	// Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.name",
		"$record_db.email",
		"$record_db.address",
		"$record_db.address2",
		"$record_db.city",
		"$record_db.province",
		"$record_db.postal_code",
		"$record_db.country"
	];

	// Selected item
	if (ACTION == 'edit' && ITEM_ID != '') {
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	// Build search query
	} else if ($searchterm) {
		foreach ($searchable_fields as $key => $field) {
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	// Get records
	$db->query("SELECT $record_db.* FROM $record_db $where ORDER BY ordering, $record_id", $params);
	$records_arr = $db->fetch_assoc($record_id);

	// Format data
	foreach ($records_arr as $loc_id => $row) {

		// Concatenate Address
		$row['line1'] = trim($row['address2'].' '.$row['address']);
		$row['line2'] = implode(', ', array_filter([$row['city'], $row['province']]));
		$row['line2'] .= rtrim(' '.$row['postal_code']);

		// If line 1 is empty and postal code exists then use city as line 1 and province & PC as line 2 
		if (!$row['line1'] && $row['postal_code']) {
			$row['line2'] = trim($row['province'].' '.$row['postal_code']);
			$row['line1'] = $row['city'];
		}

		$records_arr[$loc_id] = $row;
	}

	// Display errors
	if ($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}

	// Selected item
	if (ACTION == 'edit') {
		if ($row = ($records_arr[ITEM_ID] ?? false)) {
			$records_arr[ITEM_ID] = $row;

		// Not found
		} else {
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}

	// Delete item
	if (isset($_POST['delete'])) {
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", array(ITEM_ID));
		if (!$db->error()) {
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		} else {
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}
		header("Location: " .PAGE_URL);
		exit();

	// Save item
	} else if (isset($_POST['save'])) {
		
		// Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);

		// Validate required fields
		foreach ($required_fields as $field) {
			if (!($_POST[$field] ?? false)) {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}


		// Insert to db
		if (!$errors) {
			$params = [
				'name' => $_POST['name'],
				'address'       => $_POST['address'],
				'address2'      => $_POST['address2'],
				'city'          => $_POST['city'],
				'province'      => $_POST['province'],
				'postal_code'   => $_POST['postal_code'],
				'country'       => $_POST['country'],
				'email'         => $_POST['email'],
				'ordering'      => $_POST['ordering'],
				'showhide'      => $_POST['showhide']
			];

			$db->insert($record_db, [$record_id => ITEM_ID] + $params, $params);

			if (!$db->error()) {
				$db->commit(); 
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			} else {
				$db->rollback(); 
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
		}
	}
}

?>