<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

// Get current crops - determine which uploader has crops to process
$logo_crops = $CMSUploaderLogo->crop_queue();
$banner_crops = $CMSUploaderBanner->crop_queue();

// Determine which crops to show (logo first, then banner)
if (!empty($logo_crops)) {
    $cropimages = $logo_crops;
    $current_crop_type = 'logo';
} else if (!empty($banner_crops)) {
    $cropimages = $banner_crops;
    $current_crop_type = 'banner';
} else {
    $cropimages = [];
    $current_crop_type = 'none';
}

// Crop form
if(isset($_POST['save']) || isset($_POST['recrop'])){

	echo '<form action="" method="post">';

	// Show progress indicator
	if (!empty($logo_crops) || !empty($banner_crops)) {
		echo '<div class="panel">
			<div class="panel-header">Crop Progress
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">
				<div class="crop-progress">';

		if (!empty($logo_crops)) {
			echo '<span class="crop-step ' . ($current_crop_type == 'logo' ? 'active' : 'completed') . '">1. Logo Images</span>';
		}
		if (!empty($banner_crops)) {
			echo '<span class="crop-step ' . ($current_crop_type == 'banner' ? 'active' : 'pending') . '">2. Banner Images</span>';
		}

		echo '</div>
			</div>
		</div>';
	}

	foreach ($cropimages as $i => $image) {
		$image['root_dir'] = $image['root_dir'] ?? $image['dir'];
		$size_label = CMSUploader::size_label($image['width'], $image['height']);
		[$root_w, $root_h] = getimagesize($image['root_dir'].$image['src']);

		echo '<div class="panel">
			<div class="panel-header">'.$image['label'].' <small>('.$size_label.')</small>
				<span class="f_right"><a class="panel-toggle fas fa-chevron-up"></a></span>
			</div>

			<div class="panel-content nopadding">
				<div class="cropper-wrapper">
				<div class="cropper-alert">
				<i class="fa fa-warning"></i>
				<span>';

				if (($image['width'] && $root_w < $image['width']) || ($image['height'] && $root_h < $image['height'])) {
				echo 'Your image is too small and may be noticeably stretched on save.<br>Images should be at least '.$size_label.' to avoid loss in quality.';
				} else {
				echo 'Your image may be noticeably stretched on save.<br>Expand the crop to avoid loss in quality.';
				}

				echo '</span>
				</div>

				<div class="cropper-image">
				<img src="'.$path.$image['root_dir'].$image['src'].'">
				</div>

				<div class="cropper-controls">
				<button type="button" class="button-sm control drag-crop" style="display: none;"><i class="fas fa-crop"></i> Crop Image</button>
				<button type="button" class="button-sm control drag-move"><i class="fas fa-arrows-alt"></i> Move Image</button>
				<button type="button" class="button-sm control flip-horz"><i class="fas fa-left-right"></i> Flip Horizontal</button>
				<button type="button" class="button-sm control flip-vert"><i class="fas fa-up-down"></i> Flip Veritcal</button>

				<div class="cropper-zoom">
				<span>Zoom</span>
				<input type="range" min="0.1" max="2" step="0.01" value="'.(600/$root_h).'" class="zoom-scale">
				</div>

				<button type="button" class="button-sm control reset"><i class="fas fa-refresh"></i>Reset</button>
				</div>
				</div>
			</div>
		</div>

		<input type="hidden" id="x'.$i.'" name="x[]" value="0" />
		<input type="hidden" id="y'.$i.'" name="y[]" value="0" />
		<input type="hidden" id="w'.$i.'" name="w[]" value="'.$image['width'].'" />
		<input type="hidden" id="h'.$i.'" name="h[]" value="'.$image['height'].'" />
		<input type="hidden" id="flip-vert'.$i.'" name="flip_vert[]" value="0" />
		<input type="hidden" id="flip-horz'.$i.'" name="flip_horz[]" value="0" />
		<input type="hidden" name="imgrootdir[]" value="'.$image['root_dir'].'">
		<input type="hidden" name="imgdir[]" value="'.$image['dir'].'">
		<input type="hidden" name="imgsrc[]" value="'.$image['src'].'">
		<input type="hidden" name="imgwidth[]" value="'.$image['width'].'">
		<input type="hidden" name="imgheight[]" value="'.$image['height'].'">
		<input type="hidden" name="imglabel[]" value="'.$image['label'].'">';
	}

	// Dynamic button text
	$button_text = 'Crop Images';
	if ($current_crop_type == 'logo' && !empty($banner_crops)) {
		$button_text = 'Crop Logo & Continue to Banner';
	} else if ($current_crop_type == 'banner') {
		$button_text = 'Crop Banner Images & Finish';
	}

		echo '<footer id="cms-footer" class="resize sticky">
			<a href="' .(isset($redirect) ? $redirect : PAGE_URL) .'" class="cancel">'.($cropimages[0]['root_dir'] != $cropimages[0]['dir'] ? 'Skip' : 'Cancel').'</a>
			<button type="submit" class="button f_right" name="crop"><i class="fas fa-crop"></i>'.$button_text.'</button>
		</footer>

		<input type="hidden" name="xssid" value="' .$_COOKIE['xssid'] .'" />
		<input type="hidden" name="redirect" value="' .(isset($redirect) ? $redirect : '') .'" />
		<input type="hidden" name="item_id" value="' .($item_id ?? false ?: ITEM_ID).'" />
		<input type="hidden" name="current_crop_type" value="' .$current_crop_type .'" />
	</form>';


//Do cropping
}else if(isset($_POST['crop'])){
	$error = false;

	//Loop through images to crop
	for($i=0; $i<count($_POST['imgsrc']); $i++){

		$imgsrc = $_POST['imgsrc'][$i];
		if($imgsrc != ''){

			//Remove negative numbers added by js
			if ($_POST['x'][$i] < 0) {
				$_POST['w'][$i] += $_POST['x'][$i];
				$_POST['x'][$i] = 0;
			}

			if ($_POST['y'][$i] < 0) {
				$_POST['h'][$i] += $_POST['y'][$i];
				$_POST['y'][$i] = 0;
			}

			//Dont expand past original image dimensions and remove decimals
			$_POST['w'][$i] = floor($_POST['w'][$i]);
			$_POST['h'][$i] = floor($_POST['h'][$i]);

			if($_POST['imgwidth'][$i] != '' && $_POST['imgheight'][$i] != ''){
				$targ_w = $_POST['imgwidth'][$i];
				$targ_h = $_POST['imgheight'][$i];

			}else if($_POST['imgwidth'][$i] != '' && $_POST['imgheight'][$i] == ''){
				$targ_w = $_POST['imgwidth'][$i];
				$targ_h = round(($_POST['imgwidth'][$i]*$_POST['h'][$i])/$_POST['w'][$i]);

			}else if($_POST['imgwidth'][$i] == '' && $_POST['imgheight'][$i] != ''){
				$targ_w = round(($_POST['imgheight'][$i]*$_POST['w'][$i])/$_POST['h'][$i]);
				$targ_h = $_POST['imgheight'][$i];

			}else{
				$targ_w = $_POST['w'][$i];
				$targ_h = $_POST['h'][$i];
			}

			$img_quality = 90;
			$src    = $_POST['imgrootdir'][$i].$imgsrc;
			$to    = $_POST['imgdir'][$i].$imgsrc;
			$imgtype    = exif_imagetype($src);
			$flip    = false;

			// Set flip constant for imageflip()
			if ($_POST['flip_horz'][$i] && $_POST['flip_vert'][$i]) {
				$flip = IMG_FLIP_BOTH;
			} else if ($_POST['flip_horz'][$i]) {
				$flip = IMG_FLIP_HORIZONTAL;
			} else if ($_POST['flip_vert'][$i]) {
				$flip = IMG_FLIP_VERTICAL;
			}

			if($imgtype == IMAGETYPE_JPEG){
				$img_r = imagecreatefromjpeg($src);
				if ($flip) imageflip($img_r, $flip);

				$dst_r = imagecreatetruecolor($targ_w, $targ_h);
				imagecopyresampled($dst_r, $img_r, 0, 0, $_POST['x'][$i], $_POST['y'][$i], $targ_w, $targ_h, $_POST['w'][$i], $_POST['h'][$i]);
				imagejpeg($dst_r,$to,$img_quality);

			}else if($imgtype == IMAGETYPE_GIF){
				$img_r = imagecreatefromgif($src);
				if ($flip) imageflip($img_r, $flip);

				$dst_r = imagecreatetruecolor($targ_w, $targ_h);
				imagecopyresampled($dst_r, $img_r, 0, 0, $_POST['x'][$i], $_POST['y'][$i], $targ_w, $targ_h, $_POST['w'][$i], $_POST['h'][$i]);
				imagegif($dst_r,$to,$img_quality);

			}else if($imgtype == IMAGETYPE_PNG){
				$img_r = imagecreatefrompng($src);
				imagealphablending($img_r, true);
				if ($flip) imageflip($img_r, $flip);

				$dst_r = imagecreatetruecolor($targ_w, $targ_h);
				imagesavealpha($dst_r, true);
				imagealphablending($dst_r, false);
				$transparent = imagecolorallocatealpha($dst_r, 0, 0, 0, 127);
				imagefill($dst_r, 0, 0, $transparent);

				imagecopyresampled($dst_r, $img_r, 0, 0, $_POST['x'][$i], $_POST['y'][$i], $targ_w, $targ_h, $_POST['w'][$i], $_POST['h'][$i]);
				imagepng($dst_r, $to);
			}

			imagedestroy($img_r);
			imagedestroy($dst_r);

			//Image optimization
			if(class_exists('reSmush')){
				try{
				$optimized = $reSmush->optimize_image($imgsrc, str_replace('../', '', $_POST['imgdir'][$i]));
				}catch(Exception $e){
				$error = $e->getMessage();
				}
			}
		}
	}

	if ($error) {
		$CMSBuilder->set_system_alert('Image optimization failed. '.$error, false);
	}

}

?>