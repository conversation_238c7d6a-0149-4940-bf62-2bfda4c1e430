/* Tablesorter Custom LESS Theme by <PERSON>

 To create your own theme, modify the code below and run it through
 a LESS compiler, like this one: http://leafo.net/lessphp/editor.html
 or download less.js from http://lesscss.org/

Test out these custom less files live
 Basic Theme : http://codepen.io/Mottie/pen/eqBbn
 Bootstrap   : http://codepen.io/Mottie/pen/Ltzpi
 Metro Style : http://codepen.io/Mottie/pen/gCslk

 */
/*** theme ***/
/*** fonts ***/
/*** color definitions ***/
/* for best results, only change the hue (120),
   leave the saturation (60%) and luminosity (75%) alone
   pick the color from here: http://hslpicker.com/#99E699 */
/* darken(@headerBackground, 10%); */
/* desaturate(@headerAsc, 5%); */
/* it might be best to match the document body background color here */
/* ajax error message (added to thead) */
/* becomes height using padding (so it's divided by 2) */
/* 20px should be slightly wider than the icon width to avoid overlap */
/* url(icons/loading.gif); */
/* zebra striping */
.allRows {
  background-color: #fff;
  color: #000;
}
.evenRows {
  background-color: #ffffff;
  color: #000;
}
.oddRows {
  background-color: #ebfaeb;
}
/* hovered rows */
.oddHovered {
  background-color: #bfbfbf;
  color: #000;
}
.evenHovered {
  background-color: #d9d9d9;
  color: #000;
}
/* Columns widget */
/* saturate( darken( desaturate(@headerBackground, 10%), 10% ), 30%); */
/* Filter widget transition */
.filterWidgetTransition {
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
/*** Arrows ***/
/* black */
/* white */
/* automatically choose the correct arrow/text color */
/* variable theme name - requires less.js 1.3+;
   or just replace (!".@{theme}") with the contents of @theme
 */
.tablesorter-custom {
  font: 11px 'trebuchet ms', verdana, arial;
  background-color: #cdcdcd;
  margin: 10px 0 15px;
  width: 100%;
  text-align: left;
  border-spacing: 0;
  border: #cdcdcd 1px solid;
  border-width: 1px 0 0 1px;
  /* style th's outside of the thead */
  /* style header */
  /* tfoot */
  /* optional disabled input styling */
  /* body */
  /* hovered row colors
	   you'll need to add additional lines for
	   rows with more than 2 child rows
	*/
  /* table processing indicator - indeterminate spinner */
  /* Column Widget - column sort colors */
  /* caption (non-theme matching) */
  /* filter widget */
  /* hidden filter row */
  /* rows hidden by filtering (needed for child rows) */
  /* ajax error row */
}
.tablesorter-custom th,
.tablesorter-custom td {
  border: #cdcdcd 1px solid;
  border-width: 0 1px 1px 0;
}
.tablesorter-custom th,
.tablesorter-custom thead td {
  font: 11px 'trebuchet ms', verdana, arial;
  font-weight: bold;
  background-color: #99e699;
  color: #000;
  border-collapse: collapse;
  padding: 4px;
}
.tablesorter-custom tbody td,
.tablesorter-custom tfoot th,
.tablesorter-custom tfoot td {
  padding: 4px;
  vertical-align: top;
}
.tablesorter-custom .tablesorter-header {
  background-image: url(data:image/gif;base64,R0lGODlhFQAJAIAAACMtMP///yH5BAEAAAEALAAAAAAVAAkAAAIXjI+AywnaYnhUMoqt3gZXPmVg94yJVQAAOw==);
  background-repeat: no-repeat;
  background-position: right 5px center;
  padding: 4px 20px 4px 4px;
  cursor: pointer;
}
.tablesorter-custom .tablesorter-header.sorter-false {
  background-image: none;
  cursor: default;
  padding: 4px;
}
.tablesorter-custom .tablesorter-headerAsc {
  background-color: #70db79;
  background-image: url(data:image/gif;base64,R0lGODlhFQAEAIAAACMtMP///yH5BAEAAAEALAAAAAAVAAQAAAINjI8Bya2wnINUMopZAQA7);
}
.tablesorter-custom .tablesorter-headerDesc {
  background-color: #c6f0c2;
  background-image: url(data:image/gif;base64,R0lGODlhFQAEAIAAACMtMP///yH5BAEAAAEALAAAAAAVAAQAAAINjB+gC+jP2ptn0WskLQA7);
}
.tablesorter-custom tfoot .tablesorter-headerAsc,
.tablesorter-custom tfoot .tablesorter-headerDesc {
  /* remove sort arrows from footer */
  background-image: none;
}
.tablesorter-custom .disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: not-allowed;
}
.tablesorter-custom tbody {
  /* Zebra Widget - row alternating colors */
}
.tablesorter-custom tbody td {
  background-color: #fff;
  color: #000;
  padding: 4px;
  vertical-align: top;
}
.tablesorter-custom tbody tr.odd > td {
  background-color: #ebfaeb;
}
.tablesorter-custom tbody tr.even > td {
  background-color: #ffffff;
  color: #000;
}
.tablesorter-custom tbody > tr.hover td,
.tablesorter-custom tbody > tr:hover td,
.tablesorter-custom tbody > tr:hover + tr.tablesorter-childRow > td,
.tablesorter-custom tbody > tr:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td,
.tablesorter-custom tbody > tr.even.hover > td,
.tablesorter-custom tbody > tr.even:hover > td,
.tablesorter-custom tbody > tr.even:hover + tr.tablesorter-childRow > td,
.tablesorter-custom tbody > tr.even:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td {
  background-color: #d9d9d9;
  color: #000;
}
.tablesorter-custom tbody > tr.odd.hover > td,
.tablesorter-custom tbody > tr.odd:hover > td,
.tablesorter-custom tbody > tr.odd:hover + tr.tablesorter-childRow > td,
.tablesorter-custom tbody > tr.odd:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td {
  background-color: #bfbfbf;
  color: #000;
}
.tablesorter-custom .tablesorter-processing {
  background-image: url('data:image/gif;base64,R0lGODlhFAAUAKEAAO7u7lpaWgAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQBCgACACwAAAAAFAAUAAACQZRvoIDtu1wLQUAlqKTVxqwhXIiBnDg6Y4eyx4lKW5XK7wrLeK3vbq8J2W4T4e1nMhpWrZCTt3xKZ8kgsggdJmUFACH5BAEKAAIALAcAAAALAAcAAAIUVB6ii7jajgCAuUmtovxtXnmdUAAAIfkEAQoAAgAsDQACAAcACwAAAhRUIpmHy/3gUVQAQO9NetuugCFWAAAh+QQBCgACACwNAAcABwALAAACE5QVcZjKbVo6ck2AF95m5/6BSwEAIfkEAQoAAgAsBwANAAsABwAAAhOUH3kr6QaAcSrGWe1VQl+mMUIBACH5BAEKAAIALAIADQALAAcAAAIUlICmh7ncTAgqijkruDiv7n2YUAAAIfkEAQoAAgAsAAAHAAcACwAAAhQUIGmHyedehIoqFXLKfPOAaZdWAAAh+QQFCgACACwAAAIABwALAAACFJQFcJiXb15zLYRl7cla8OtlGGgUADs=');
  background-position: center center;
  background-repeat: no-repeat;
}
.tablesorter-custom tr.odd td.primary {
  background-color: #99e6a6;
}
.tablesorter-custom td.primary,
.tablesorter-custom tr.even td.primary {
  background-color: #c2f0c9;
}
.tablesorter-custom tr.odd td.secondary {
  background-color: #c2f0c9;
}
.tablesorter-custom td.secondary,
.tablesorter-custom tr.even td.secondary {
  background-color: #d6f5db;
}
.tablesorter-custom tr.odd td.tertiary {
  background-color: #d6f5db;
}
.tablesorter-custom td.tertiary,
.tablesorter-custom tr.even td.tertiary {
  background-color: #ebfaed;
}
.tablesorter-custom caption {
  background-color: #fff;
}
.tablesorter-custom .tablesorter-filter-row input,
.tablesorter-custom .tablesorter-filter-row select {
  width: 98%;
  height: auto;
  margin: 0;
  padding: 4px;
  color: #333;
  background-color: #fff;
  border: 1px solid #bbb;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
.tablesorter-custom .tablesorter-filter-row {
  background-color: #eee;
}
.tablesorter-custom .tablesorter-filter-row td {
  background-color: #eee;
  line-height: normal;
  text-align: center;
  /* center the input */
  -webkit-transition: line-height 0.1s ease;
  -moz-transition: line-height 0.1s ease;
  -o-transition: line-height 0.1s ease;
  transition: line-height 0.1s ease;
}
.tablesorter-custom .tablesorter-filter-row.hideme td {
  padding: 2px;
  margin: 0;
  line-height: 0;
  cursor: pointer;
}
.tablesorter-custom .tablesorter-filter-row.hideme * {
  height: 1px;
  min-height: 0;
  border: 0;
  padding: 0;
  margin: 0;
  /* don't use visibility: hidden because it disables tabbing */
  opacity: 0;
  filter: alpha(opacity=0);
}
.tablesorter-custom .filtered {
  display: none;
}
.tablesorter-custom .tablesorter-errorRow td {
  text-align: center;
  cursor: pointer;
  background-color: #e6bf99;
}
