/*************
  Ice Theme (by the<PERSON><PERSON>)
 *************/
/* overall */
.tablesorter-ice {
	width: 100%;
	background-color: #fff;
	margin: 10px 0 15px;
	text-align: left;
	border-spacing: 0;
	border: #ccc 1px solid;
	border-width: 1px 0 0 1px;
}
.tablesorter-ice th,
.tablesorter-ice td {
	border: #ccc 1px solid;
	border-width: 0 1px 1px 0;
}

/* header */
.tablesorter-ice th,
.tablesorter-ice thead td {
	font: 12px/18px Arial, Sans-serif;
	color: #555;
	background-color: #f6f8f9;
	border-collapse: collapse;
	padding: 4px;
	text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
}
.tablesorter-ice tbody td,
.tablesorter-ice tfoot th,
.tablesorter-ice tfoot td {
	padding: 4px;
	vertical-align: top;
}
.tablesorter-ice .header,
.tablesorter-ice .tablesorter-header {
	background-color: #f6f8f9;
	background-position: center right;
	background-repeat: no-repeat;
	background-image: url(data:image/gif;base64,R0lGODlhDAAMAMQAAAJEjAJCiwJBigJAiANFjgNGjgNEjQRIkQRHkANIkAVMlAVQmAZWnQZUnAdYoAhdpAhZoAlhqQlepQliqQppsApmrQxutgtutQtutAxwtwxwtg1yug1zugxtsw1yuP8A/yH5BAEAAB8ALAAAAAAMAAwAAAUx4Cd+3GiOW4ado2d9VMVm1xg9ptadTsP+QNZEcjoQTBDGCAFgLRSfQgCYMAiCn8EvBAA7);
	/* background-image: url(images/ice-unsorted.gif) */
	padding: 4px 20px 4px 4px;
	white-space: normal;
	cursor: pointer;
}
.tablesorter-ice .headerSortUp,
.tablesorter-ice .tablesorter-headerSortUp,
.tablesorter-ice .tablesorter-headerAsc {
	color: #333;
	background-color: #ebedee;
	background-position: center right;
	background-repeat: no-repeat;
	background-image: url(data:image/gif;base64,R0lGODlhDAAMANUAAAJCiwNHkANFjgNEjQRIkQNJkQRMlARKkwRKkgVPlwZSmgdaogdYnwhfpghcowlhqgliqglgqAlgpwljqwporwpmrQplrAtsswtqsgtrsgtqsQxttAtvtQtttAxyuQxwtwxxtwxvtg10uw1zuQ1xuP8A/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAACUALAAAAAAMAAwAAAY6wJKwJBoahyNQ6Dj0fDoZCpPEuWgqk4jxs8FQLI+Gg8Esm5kQydFQMC7IwkOAqUiUCAIzIjA4lwBlQQA7);
	/* background-image: url(images/ice-desc.gif) */
}
.tablesorter-ice .headerSortDown,
.tablesorter-ice .tablesorter-headerSortDown,
.tablesorter-ice .tablesorter-headerDesc {
	color: #333;
	background-color: #ebedee;
	background-position: center right;
	background-repeat: no-repeat;
	background-image: url(data:image/gif;base64,R0lGODlhDAAMANUAAAE/iAJBigNFjgNEjQNFjQNDiwRHkQRHjwNHjwROlgRMlQRMlARJkgRKkgZQmAVPlgZWnQZSmgZRmAdXoAdXnwdUnAdbogdZoQhbowlhqAlepglkrAliqQtstAtqsQxyugxyuQxwuAxxuAxxtwxwtgxvtQ10vA12vA10u/8A/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAACkALAAAAAAMAAwAAAY6wJQwdRoah6bP6DhEiVIdDxNEGm4yxlDpiJkwv2AmR2OhVCSJBsJ4gUQeCwOB6VAwBAXwYRAIpwBfQQA7);
	/* background-image: url(images/ice-asc.gif); */
}
.tablesorter-ice thead .sorter-false {
	background-image: none;
	cursor: default;
	padding: 4px;
}

/* tfoot */
.tablesorter-ice tfoot .tablesorter-headerSortUp,
.tablesorter-ice tfoot .tablesorter-headerSortDown,
.tablesorter-ice tfoot .tablesorter-headerAsc,
.tablesorter-ice tfoot .tablesorter-headerDesc {
	background-color: #ebedee;
}

/* tbody */
.tablesorter-ice td {
	color: #333;
}

/* hovered row colors */
.tablesorter-ice tbody > tr.hover > td,
.tablesorter-ice tbody > tr:hover > td,
.tablesorter-ice tbody > tr.even:hover > td,
.tablesorter-ice tbody > tr.odd:hover > td {
	background-color: #ebf2fa;
}

/* table processing indicator */
.tablesorter-ice .tablesorter-processing {
	background-position: center center !important;
	background-repeat: no-repeat !important;
	/* background-image: url(images/loading.gif) !important; */
	background-image: url('data:image/gif;base64,R0lGODlhFAAUAKEAAO7u7lpaWgAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQBCgACACwAAAAAFAAUAAACQZRvoIDtu1wLQUAlqKTVxqwhXIiBnDg6Y4eyx4lKW5XK7wrLeK3vbq8J2W4T4e1nMhpWrZCTt3xKZ8kgsggdJmUFACH5BAEKAAIALAcAAAALAAcAAAIUVB6ii7jajgCAuUmtovxtXnmdUAAAIfkEAQoAAgAsDQACAAcACwAAAhRUIpmHy/3gUVQAQO9NetuugCFWAAAh+QQBCgACACwNAAcABwALAAACE5QVcZjKbVo6ck2AF95m5/6BSwEAIfkEAQoAAgAsBwANAAsABwAAAhOUH3kr6QaAcSrGWe1VQl+mMUIBACH5BAEKAAIALAIADQALAAcAAAIUlICmh7ncTAgqijkruDiv7n2YUAAAIfkEAQoAAgAsAAAHAAcACwAAAhQUIGmHyedehIoqFXLKfPOAaZdWAAAh+QQFCgACACwAAAIABwALAAACFJQFcJiXb15zLYRl7cla8OtlGGgUADs=') !important;
}

/* Zebra Widget - row alternating colors */
.tablesorter-ice tr.odd > td {
	background-color: #dfdfdf;
}
.tablesorter-ice tr.even > td {
	background-color: #efefef;
}

/* Column Widget - column sort colors */
.tablesorter-ice td.primary,
.tablesorter-ice tr.odd td.primary {
	background-color: #9ae5e5;
}
.tablesorter-ice tr.even td.primary {
	background-color: #c2f0f0;
}
.tablesorter-ice td.secondary,
.tablesorter-ice tr.odd td.secondary {
	background-color: #c2f0f0;
}
.tablesorter-ice tr.even td.secondary {
	background-color: #d5f5f5;
}
.tablesorter-ice td.tertiary,
.tablesorter-ice tr.odd td.tertiary {
	background-color: #d5f5f5;
}
.tablesorter-ice tr.even td.tertiary {
	background-color: #ebfafa;
}

/* sticky headers */
.tablesorter-ice.containsStickyHeaders thead tr:nth-child(1) th,
.tablesorter-ice.containsStickyHeaders thead tr:nth-child(1) td {
	border-top: #ccc 1px solid;
}

/* caption */
caption {
	background-color: #fff;
}

/* filter widget */
.tablesorter-ice .tablesorter-filter-row {
	background-color: #eee;
}
.tablesorter-ice .tablesorter-filter-row td {
	background-color: #eee;
	line-height: normal;
	text-align: center; /* center the input */
	-webkit-transition: line-height 0.1s ease;
	-moz-transition: line-height 0.1s ease;
	-o-transition: line-height 0.1s ease;
	transition: line-height 0.1s ease;
}
/* optional disabled input styling */
.tablesorter-ice .tablesorter-filter-row .disabled {
	opacity: 0.5;
	filter: alpha(opacity=50);
	cursor: not-allowed;
}
/* hidden filter row */
.tablesorter-ice .tablesorter-filter-row.hideme td {
	/*** *********************************************** ***/
	/*** change this padding to modify the thickness     ***/
	/*** of the closed filter row (height = padding x 2) ***/
	padding: 2px;
	/*** *********************************************** ***/
	margin: 0;
	line-height: 0;
	cursor: pointer;
}
.tablesorter-ice .tablesorter-filter-row.hideme * {
	height: 1px;
	min-height: 0;
	border: 0;
	padding: 0;
	margin: 0;
	/* don't use visibility: hidden because it disables tabbing */
	opacity: 0;
	filter: alpha(opacity=0);
}
/* filters */
.tablesorter-ice input.tablesorter-filter,
.tablesorter-ice select.tablesorter-filter {
	width: 98%;
	height: auto;
	margin: 0;
	padding: 4px;
	background-color: #fff;
	border: 1px solid #bbb;
	color: #333;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-transition: height 0.1s ease;
	-moz-transition: height 0.1s ease;
	-o-transition: height 0.1s ease;
	transition: height 0.1s ease;
}
/* rows hidden by filtering (needed for child rows) */
.tablesorter .filtered {
	display: none;
}

/* ajax error row */
.tablesorter .tablesorter-errorRow td {
	text-align: center;
	cursor: pointer;
	background-color: #e6bf99;
}
