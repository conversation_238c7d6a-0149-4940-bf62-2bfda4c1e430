const basePath = root;
const cmsRoot = path;

//Default settings
const default_css = [
	basePath+'theme/css/global.css?i='+(+new Date()),
	basePath+'core/plugins/font-awesome/css/all.min.css'
];
const default_textcolor_map = [
	'FFFFFF', 'Light',
	'EEEEEE', 'Lightest Gray',
	'DDDDDD', 'Lighter Gray',
	'CCCCCC', 'Light Gray',
	'999999', 'Gray',
	'666666', 'Dark Gray',
	'484848', 'Darker Gray',
	'333333', 'Darkest Gray',
	'111111', 'Dark',
	'C70000', 'Error',
	'00C700', 'Success',
];
const default_header_items = [
	{title: "Header 2", format: "h2"},
	{title: "Header 3", format: "h3"},
	{title: "Header 4", format: "h4"},
	{title: "Header 5", format: "h5"},
	{title: "Header 6", format: "h6"}
];
const default_inline_items = [
	{title: "Gradient", inline: 'span', selector: 'p,h2,h3,h4,h5,h6,ul,ol,th,td,table', classes: "gradient-text"},
	{title: "Bold", icon: "bold", format: "bold"},
	{title: "Italic", icon: "italic", format: "italic"},
	{title: "Underline", icon: "underline", format: "underline"},
	{title: "Strikethrough", icon: "strikethrough", format: "strikethrough"},
	{title: "Superscript", icon: "superscript", format: "superscript"},
	{title: "Subscript", icon: "subscript", format: "subscript"},
	{title: "Code", icon: 'code', format: 'code'}
];
const default_block_items = [
	{title: "Paragraph", format: "p"},
	{title: "Blockquote", format: "blockquote"},
	{title: "Preformatted", format: "pre"}
];
const default_alignment = [
	{title: "Left", icon: "alignleft", format: "alignleft"},
	{title: "Center", icon: "aligncenter", format: "aligncenter"},
	{title: "Right", icon: "alignright", format: "alignright"},
	{title: "Justify", icon: "alignjustify", format: "alignjustify"},
	{title: 'Image Left', selector: 'img', styles: {'float': 'left', 'margin': '0 15px 10px 0'} },
	{title: 'Image Right', selector: 'img', styles: {'float': 'right', 'margin': '0 0 10px 15px'} }
];
const default_font_sizes = [
	{title: 'XXX-Large', inline: 'span', classes: 'font-h3' },
	{title: 'XX-Large', inline: 'span', classes: 'font-h4' },
	{title: 'X-Large', inline: 'span', classes: 'font-h5' },
	{title: 'Large', inline: 'span', classes: 'font-h6' },
	{title: 'Paragraph', inline: 'span', classes: 'font-paragraph' },
	{title: 'Small', inline: 'small' },
];
const default_contextmenu = "link image inserttable | cell row column deletetable";
const default_link_list = cmsRoot + "js/ajax/tinymce-pages-list.php";
const default_link_class_list = [
	{title: 'None', value: ''},
	{title: 'Button', value: 'button'},
	{title: 'Alt Button', value: 'button simple'},
	{title: 'Embed Media', value: 'embed-media'}
];
const default_init_instance_callback = function (editor) {
	editor.on('NodeChange', function (e) {
		$(e.element).filter('a.button')
			.filter((i, el) => el.innerHTML.match(/^<span>|<\/span>$/g)?.length != 2)
			.each((i, el) => {
				el.innerHTML = `<span>${el.innerHTML}</span>`;
			});
	});
};
const default_external_plugins = { "filemanager" : basePath + "filemanager/plugin.min.js", "nanospell" : cmsRoot + "includes/plugins/tinymce/plugins/nanospell/plugin.js"};


//Default
tinymceInitDefault("textarea.tinymceEditor");
function tinymceInitDefault(tinymceSelector){
	tinymce.init({
		selector: tinymceSelector,
		theme: "modern",
		height: 600,
		content_css: default_css,
		textcolor_map: default_textcolor_map,
		style_formats:[
			{title: "Headers", items: default_header_items},
			{title: "Inline", items: default_inline_items},
			{title: "Blocks", items: default_block_items},
			{title: "Font Size", items: default_font_sizes},
			{title: "Alignment", items: default_alignment}
		],

		table_class_list: [
			{title: 'None', value: ''},
			{title: 'Column', value: 'column'}
		],

		plugins: "paste textcolor image media link hr autolink code lists preview anchor searchreplace wordcount visualblocks insertdatetime table contextmenu colorpicker fullscreen responsivefilemanager fontawesome noneditable",
		toolbar1: "styleselect | fontawesome | forecolor | undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent",
		toolbar2: "responsivefilemanager | hr | image | media | youtube | link unlink anchor | preview code | pastetext",

		image_advtab: true,
		extended_valid_elements: 'span[class|style]',
		contextmenu: default_contextmenu,
		contextmenu_never_use_native: true,
		paste_as_text: true,
		end_container_on_empty_block: true,
		link_list: default_link_list,
		link_class_list: default_link_class_list,
		relative_urls: false,

		external_filemanager_path: basePath + "filemanager/",
		filemanager_title:"Responsive Filemanager",
		filemanager_access_key:fmaccesskey,
		nanospell_server: "php",
		external_plugins: default_external_plugins,

		init_instance_callback: default_init_instance_callback
	});
}

//Basic
tinymce.init({
    selector: "textarea.tinymceBasic",
    theme: "modern",
	height: 600,
	content_css: default_css,
	textcolor_map: default_textcolor_map,
	style_formats:[
		{title: "Inline", items: default_inline_items},
		{title: "Font Size", items: default_font_sizes},
		{title: "Alignment", items: default_alignment}
	],

    plugins: "paste textcolor link autolink code lists preview anchor searchreplace wordcount visualblocks insertdatetime table contextmenu fullscreen responsivefilemanager",
    toolbar1: "styleselect | forecolor | undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent",
   	toolbar2: "responsivefilemanager | link unlink anchor | preview code | pastetext",

    image_advtab: true,
    extended_valid_elements: 'span[class|style]',
	contextmenu: default_contextmenu,
	contextmenu_never_use_native: true,
	paste_as_text: true,
	end_container_on_empty_block: true,
    link_list: default_link_list,
	link_class_list: default_link_class_list,
	relative_urls: false,

    external_filemanager_path: basePath + "filemanager/",
   	filemanager_title:"Responsive Filemanager",
   	filemanager_access_key:fmaccesskey,
	nanospell_server: "php",
   	external_plugins: default_external_plugins,

	init_instance_callback: default_init_instance_callback
});

//Mini
tinymce.init({
    selector: "textarea.tinymceMini",
    theme: "modern",
    height: 400,
	content_css: default_css,
	textcolor_map: default_textcolor_map,
	style_formats:[
		{title: "Inline", items: default_inline_items},
		{title: "Font Size", items: default_font_sizes}
	],

    plugins: "paste textcolor link autolink code lists preview anchor searchreplace wordcount visualblocks insertdatetime contextmenu fullscreen",
    toolbar1: "styleselect | forecolor | undo redo | bold italic underline",
   	toolbar2: "link unlink anchor | preview code | pastetext",

    extended_valid_elements: 'span[class|style]',
	contextmenu: "link",
	contextmenu_never_use_native: true,
	paste_as_text: true,
	end_container_on_empty_block: true,
    link_list: default_link_list,
	link_class_list: default_link_class_list,
	relative_urls: false,

    external_filemanager_path: basePath + "filemanager/",
   	filemanager_title:"Responsive Filemanager",
   	filemanager_access_key:fmaccesskey,
	nanospell_server: "php",
   	external_plugins: default_external_plugins,

	init_instance_callback: default_init_instance_callback
});

//Email
tinymce.init({
	selector: "textarea.tinymceEmail",
	theme: "modern",
	height: 400,
	content_css: default_css,
	textcolor_map: default_textcolor_map,
	style_formats:[
		{title: "Headers", items: default_header_items},
		{title: "Inline", items: default_inline_items},
		{title: "Blocks", items: default_block_items},
		{title: "Alignment", items: default_alignment},
		{title: "Font Size", items: default_font_sizes}
	],

	plugins: "paste textcolor image media link hr autolink code lists preview anchor searchreplace wordcount visualblocks insertdatetime table contextmenu colorpicker fullscreen responsivefilemanager fontawesome noneditable",
	toolbar1: "styleselect | fontawesome | forecolor | undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent",
	toolbar2: "responsivefilemanager | hr | image | media | youtube | link unlink anchor | preview code | pastetext",

	image_advtab: true,
	extended_valid_elements: 'span[class|style]',
	contextmenu: default_contextmenu,
	contextmenu_never_use_native: true,
	paste_as_text: true,
	end_container_on_empty_block: true,
	link_list: default_link_list,
	link_class_list: default_link_class_list,
	relative_urls: false,
	remove_script_host: false, //Set to false so links and images will contain full url

	external_filemanager_path: basePath + "filemanager/",
	filemanager_title:"Responsive Filemanager",
	filemanager_access_key:fmaccesskey,
	nanospell_server: "php",
	external_plugins: default_external_plugins,

	init_instance_callback: default_init_instance_callback
});