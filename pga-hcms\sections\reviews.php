<?php

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">Reviews
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter stickyheader sortable">

				<thead>
					<th width="1px" class="nopadding-r" data-sorter="false"></th>
					<th width="250px">Client</th>
					<th width="auto">Rating</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
				</thead>

				<tbody>';

				foreach($records_arr as $row){
					echo '<tr data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-name="'.$row['client'].'" data-id="'.$row[$record_id].'">
						<td class="handle nopadding-r"><span class="fas fa-arrows-alt"></span></td>
						<td>'.
							($row['client'] ?: 'Anonymous').
							($row['type'] == 'Google Review' ? '<br><small>Imported from Google</small>'.$CMSBuilder->tooltip('Google Review', 'This review has been automatically imported from Google My Business.  Poor reviews are automatically hidden from the front-end.') : '').
						'</td>
						<td class="nowrap">'.str_repeat('<i class="fas fa-star"></i>', $row['rating'] ?: 0).'</td>
						<td>'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td class="right"><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
					</tr>';
				}
				
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';

}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	$editable = ($data['type'] ?? false) != 'Google Review';
	$disabled = $editable ? '' : ' disabled';
	if(!$editable){
		echo $CMSBuilder->important('Ratings pulled from Google cannot be edited.');
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';

		// Details
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show '.$record_name.'</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"'.(isset($row['showhide']) && $row['showhide'] ? '' : ' checked').' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>
			<div class="panel-content">
				<div class="flex-container">
					<div class="form-field">
						<label>Client Name'.(in_array('client', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input'.$disabled.' type="text" name="client" value="'.($row['client'] ?? '').'" class="input" />
						<label>Company Name'.(in_array('company', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input'.$disabled.' type="text" name="company" value="'.($row['company'] ?? '').'" class="input" />
					</div>

					<div class="form-field">
						<label>Rating'.(in_array('rating', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<div class="rating-wrapper">
							<div class="rating-select clearfix">
								<input type="radio" name="rating" id="star-5" value="5"'.(($row['rating'] ?? false) == 5 ? ' checked' : '').$disabled.'/>
								<label for="star-5"></label>

								<input type="radio" name="rating" id="star-4" value="4"'.(($row['rating'] ?? false) == 4 ? ' checked' : '').$disabled.'/>
								<label for="star-4"></label>

								<input type="radio" name="rating" id="star-3" value="3"'.(($row['rating'] ?? false) == 3 ? ' checked' : '').$disabled.'/>
								<label for="star-3"></label>

								<input type="radio" name="rating" id="star-2" value="2"'.(($row['rating'] ?? false) == 2 ? ' checked' : '').$disabled.'/>
								<label for="star-2"></label>

								<input type="radio" name="rating" id="star-1" value="1"'.(($row['rating'] ?? false) == 1 ? ' checked' : '').$disabled.'/>
								<label for="star-1"></label>
							</div>
						</div>

						<label>Numerical Order'.$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.').'</label>
						<select name="ordering" class="select">
							<option value="101">Default</option>';

						for($i = 1; $i < 101; $i++){
							echo '<option value="'.$i.'" '.(($row['ordering'] ?? '') == $i ? 'selected' : '').'>'.$i.'</option>';
						}

						echo '</select>
					</div>

					<div class="form-field auto-width clear">
						<label>Content'.(in_array('content', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<textarea'.$disabled.' name="content" class="textarea input_lg">'.($row['content'] ?? '').'</textarea>
					</div>
				</div>
			</div>
		</div>'; // END Details

		//Sticky footer
		include('includes/widgets/formbuttons.php');

		echo '<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'].'" />

	</form>';

}

?>