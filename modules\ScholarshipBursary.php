<?php

//Winners
if(PAGE_ID ==  $_sitepages['past-winners']['page_id'] || PAGE_ID == 76){
	
	//Define vars
	$panel_id = (PAGE_ID == $_sitepages['past-winners']['page_id'] ? 104 : 103);
	$award_year = NULL;
	$award_type = (PAGE_ID == $_sitepages['past-winners']['page_id'] ? 'Bursary' : 'Scholarship');
	$current_winners = array();
	$past_winners = array();
	
	//Get winners
	$query = $db->query("SELECT `scholarship_bursary`.*, ".
	"`accounts`.`status` AS `account_status`, ".
	"`accounts`.`showhide` AS `profile_showhide`, ".
	"`account_profiles`.`profile_id`, ".
	"IFNULL(`scholarship_bursary`.`first_name`, `account_profiles`.`first_name`) AS `first_name`, ".
	"IFNULL(`scholarship_bursary`.`last_name`, `account_profiles`.`last_name`) AS `last_name`, ".
	"IFNULL(`scholarship_bursary`.`image`, `account_profiles`.`photo`) AS `image` ".
	"FROM `scholarship_bursary` ".
	"LEFT JOIN `accounts` ON `scholarship_bursary`.`account_id` = `accounts`.`account_id` ".
	"LEFT JOIN `account_profiles` ON `scholarship_bursary`.`account_id` = `account_profiles`.`account_id` ".
	"WHERE `scholarship_bursary`.`showhide` = 0 && `award` = ? ORDER BY `year` DESC, `type`", array($award_type));
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $key=>$row){
			
			//Set profile url
			$row['profile_url'] = NULL;
			if(!empty($row['profile_id']) && $row['account_status'] == 'Active' && $row['profile_showhide'] == 0){
				$row['profile_url'] = $_sitepages['directory']['page_url'].clean_url($row['first_name'].' '.$row['last_name'].'-'.$row['profile_id']).'/';	
			}
			
			//Set award year
			if(is_null($award_year)){
				$award_year = $row['year'];
			}
			
			//Current winner
			if($row['year'] == $award_year){
				$current_winners[] = $row;
				
			//Past winner
			}else{
				$past_winners[] = $row;
			}
		}
	}
		
}

?>