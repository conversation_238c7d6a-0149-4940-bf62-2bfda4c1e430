<?php

// Build rich snippet for Local Business
// If possible, replace @type with a LocalBusiness type to be more specific (See: https://schema.org/LocalBusiness for a list)
$schema = [
	'@context' => "http://schema.org",
	'@type' => "Organization",
	'@id' => $siteurl,
	'name' => $global['company_name'],
	'url' => $siteurl,
	'image' => $siteurl.$path.'images/logo.jpg',
	'telephone' => format_intl_number($global['contact_phone']),

	'address' => [
		'@type' => "PostalAddress",
		'streetAddress' => ($global['contact_address2'] != '' ? $global['contact_address2'].', ' : '').$global['contact_address'],
		'addressLocality' => $global['contact_city'],
		'addressRegion' => $global['contact_province'],
		'postalCode' => $global['contact_postal_code'],
		'addressCountry' => ($global['contact_country'] == 'Canada' ? 'CA' : ($global['contact_country'] == 'United States' ? 'US' : $global['contact_country']))
	],

	'geo' => [
		'@type' => 'GeoCoordinates',
		'latitude' => $global['gpslat'],
		'longitude' => $global['gpslong'],
	]
];

//Contact numbers
foreach($global['locations'] as $loc){
	$schema['contactPoint'] = [];
	if($loc['phone']){
		$schema['contactPoint'][] = [
			'@type' => "ContactPoint",
			'telephone' => format_intl_number($loc['phone']),
			'contactType' => "customer support"
		];
	}

	if($loc['toll_free']){
		$schema['contactPoint'][] = [
			'@type' => "ContactPoint",
			'telephone' => format_intl_number($loc['toll_free']),
			'contactType' => "customer support",
			'contactOption' => "TollFree"
		];
	}

	$contact_option = [];
	foreach($loc['location_numbers'] as $number){
		if($number['tollfree']){
			$contact_option[] = "TollFree";
		}

		if($number['hearingimpaired']){
			$contact_option[] = "HearingImpairedSupported";
		}

		$schema['contactPoint'][] = [
			'@type' => "ContactPoint",
			'telephone' => format_intl_number($number['phone']),
			'contactType' => $number['type'],
			'contactOption' => $contact_option
		];
	}
}

//Business hours
if ($global['contact_hours']) {
	$schema['openingHoursSpecification'] = [];
	
	foreach($global['contact_hours'] as $hours){
		if($hours['closed'] == 0){
			$schema['openingHoursSpecification'][] = [
				'@type' => "OpeningHoursSpecification",
				'dayOfWeek' => array($hours['day']),
				'opens' => $hours['start_time'],
				'closes' => $hours['end_time'],
			];
		}
	}
}

if(isset($global['reviews_total']) && isset($global['reviews_average'])){
	$schema['aggregateRating'] = [
		'@type' => 'AggregateRating',
		'ratingValue' => $global['reviews_average'],
		'ratingCount' => $global['reviews_total']
	];
}

?>
<script type="application/ld+json"><?php echo json_encode($schema); ?></script>