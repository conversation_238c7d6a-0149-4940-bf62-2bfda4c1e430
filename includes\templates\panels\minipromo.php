<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?>">
	<div class="curve-background">
	<?php
	if($panel['title'] || $panel['include_h1'] || $panel['content']) {
		echo '<div class="panel-wrapper">';

		if($panel['title'] || $panel['include_h1']){
			echo '<header class="panel-header">
				<div class="container">
					'.($panel['include_h1'] ? '<h1>'.fancy_text($page['page_title']).'</h1>' : '').'
					'.($panel['title'] ? '<div class="panel-title"><h2>'.fancy_text($panel['title']).'</h2></div>' : '').'
				</div>
			</header>';
		}

		if($panel['content']){
			echo '<div class="panel-content">
				<div class="container">
					<div class="panel-text">'.$panel['content'].'</div>
				</div>
			</div>';
		}

		echo '</div>';
	}

	if(!empty($panel['panel_promos'])){
		echo '<div class="panel-promos">
			<div class="container">
				<div class="mini-promo-boxes animate" data-animate=".mini-promo-box">';

				foreach($panel['panel_promos'] as $promo){
					echo '<div class="mini-promo-box">
						<div class="promo-content">
							<div class="blob-container"> <!-- Wrap icon in blob container -->
								<i class="'.$promo['icon'].' promo-fa-icon"></i>
								'.(!empty($promo['mini_image']) ? '<img class="promo-icon" src="/images/promos/'.$promo['mini_image'].'" alt="'.$promo['mini_image_alt'].'">' : '').'
							</div>
							<h3 class="promo-title">'.fancy_text($promo['title']).'</h3>
							<p>'.$promo['description'].'</p>
						';
					
					if(!empty($promo['url'])) {
						echo '<a href="'.$promo['url'].'" class="button ternary" '.($promo['url_target'] ? 'target="'.$promo['url_target'].'"' : '').'><span class="top-border"></span> <span class="bottom-border"></span> <span class="left-border"></span>
						<span class="right-border"></span>
						'.$promo["url_text"].'</a></div>';
					}
					
					echo '</div>';
				}
				echo '</div>
			</div>
		</div>';
	}
	?>
	</div>
</section>