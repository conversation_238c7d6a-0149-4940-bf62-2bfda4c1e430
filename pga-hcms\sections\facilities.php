<?php
//Table listing
if (ACTION == '') {
   	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

    echo '<form id="clear-search-form" name="clear-search-form" class="hidden" action="'.PAGE_URL.'" method="post">
        <input type="hidden" name="clear-search" value="Clear" />
        <input type="hidden" name="search" value="" />
        <input type="hidden" name="xssid" value="'.$_COOKIE['xssid'].'" />
    </form>';

    //Display listings
    if(!empty($records_arr)){
        echo '<div class="panel">
            <div class="panel-header">'.$record_names.'
                <span class="panel-toggle fas fa-chevron-up"></span>
            </div>

            <div class="panel-content nopadding">
                <table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
                    <thead>
                        <tr>
                            <th width="1px" data-sorter="false"></th>
                            <th>Name</th>
                            <th>Region</th>
                            <th width="100px">Visible</th>
                            <th width="100px" data-sorter="false">Actions</th>
                            <th width="100px" data-sorter="false"></th>
                        </tr>
                    </thead>
                    <tbody>';

        foreach($records_arr as $id => $facility){

            $seo_class = $seo_tooltip = '';
            if($cms_settings['enhanced_seo'] && (MASTER_USER || SEO_USER)){
                $seo_indicator = $Analyzer->score_tooltip($facility['seo_score']);
                $seo_class     = $seo_indicator['class'];
                $seo_tooltip   = ($facility['is_future'] ? '<span class="seo-tool tooltip" title="<h4>Cannot determine SEO score: </h4>Entry is scheduled to be posted on '.date('F jS, Y', strtotime($facility['post_date'])).'."></span>' : $seo_indicator['tooltip']);
            }

            // $status = $facility['status'] ? '<span class="status active">Active</span>' : '<span class="status inactive">Inactive</span>';

            echo '<tr class="">';
                echo '<td><input type="checkbox" name="selected[]" value="'.$id.'" class="checkbox" /></td>';
                echo '<td>'.htmlspecialchars($facility['facility_name']).'</td>
                <td>'.nl2br(htmlspecialchars(substr($facility['region'], 0, 100).(strlen($facility['region']) > 100 ? '...' : ''))).'</td>';
                echo '<td>'.$CMSBuilder->showhide_toggle($record_db, $record_id, $facility[$record_id], $facility['showhide']).'</td>
				<td class="right"><a href="'.PAGE_URL.'?action=edit&item_id='.$facility[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
				<td class="nopadding-l">'.'<a href="'.$facility['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td>
            </tr>';
        }

        echo '</tbody>
              </table>';

              //Pager
		$CMSBuilder->tablesorter_pager();

        echo  '</div>
        </div>';

    } else {
        echo '<div class="notice notice-info">No facilities found. <a href="'.PAGE_URL.'?action=add">Add a new facility</a> to get started.</div>';
    }

// }else if($CMSUploader->crop_queue()){
    // include('includes/jcropimages.php');
// } else if (isset($_SESSION['facility_crop_target'], $_SESSION['facility_crop_item_id']) &&
// ($CMSUploaderLogo->crop_queue() || $CMSUploaderBanner->crop_queue()) && // Check if *any* relevant queue has items
// (ITEM_ID == $_SESSION['facility_crop_item_id']) // Ensure we're on the correct facility's edit page
// ) {
// One of the facility images needs cropping.
// The specific target (logo/banner) is in $_SESSION['facility_crop_target']
// modules/CropImagesFacility.php will use this session var to init the correct CMSUploader.
// error_log("Pages/Facilities: Detected active crop queue for facility " . $_SESSION['facility_crop_item_id'] . ", target: " . $_SESSION['facility_crop_target']);
// include("includes/jcropimagesFacility.php"); // Include the NEW facility-specific crop UI

} else if ($CMSUploaderLogo->crop_queue() || $CMSUploaderBanner->crop_queue()) {
    include('includes/jcropimagesFacility.php');
} else {
    //Display form
    $facility = [];
    $page_title = 'Add New Facility';

    if(ACTION == 'edit' && !empty($records_arr[ITEM_ID])){
        $facility = $records_arr[ITEM_ID];
        $page_title = 'Edit Facility: '.$facility['facility_name'];
		$image = $facility['logo'];
		$banner_image = $facility['image'];

		// echo '<div class="actions-nav flex-container">
		// 	<div class="flex-column right">
		// 		<small><b>Link to '.$record_name.':</b> '.$data['page_url'].'&nbsp;&nbsp;<a href="'.$data['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td></small>
		// 	</div>
		// </div>';

    }else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

    echo '<form action="" method="post" enctype="multipart/form-data">
        <!-- Basic Information Block -->
        <div class="panel">
            <div class="panel-header">Facility Details
                <span class="panel-toggle fas fa-chevron-up"></span>

                <div class="panel-switch">
				<label>Show in Directory</label>
				<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="1"' .(($facility['showhide'] ?? 1) == 0 ? ' checked' : ''). ' />
					<label for="showhide">
						<span class="inner"></span>
						<span class="switch"></span>
					</label>
				</div>
				</div>

            </div>

            <div class="panel-content">
                <div class="form-row">
                    <div class="form-field col-6 '.($required['facility_name'] ?? '').'">
                        <label>Facility Name <span class="required">*</span></label>
                        <input type="text" name="facility_name" value="'.htmlspecialchars($facility['facility_name'] ?? '').'" class="input" required />
                    </div>

                    <div class="form-field col-6">
                        <label>Facility Type</label>
                        <select name="type" class="select">
                            <option value="">-- Select Type --</option>
                            <option value="Public" '.(isset($facility['type']) && $facility['type'] == 'Public' ? 'selected' : '').'>Public</option>
                            <option value="Range/Teaching Facility" '.(isset($facility['type']) && $facility['type'] == 'Range/Teaching Facility' ? 'selected' : '').'>Range/Teaching Facility</option>
                            <option value="Semi-Private" '.(isset($facility['type']) && $facility['type'] == 'Semi-Private' ? 'selected' : '').'>Semi-Private</option>
                            <option value="Resort" '.(isset($facility['type']) && $facility['type'] == 'Resort' ? 'selected' : '').'>Resort</option>
                            <option value="Private" '.(isset($facility['type']) && $facility['type'] == 'Private' ? 'selected' : '').'>Private</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-field col-6">
                        <label>Affiliation</label>
                        <select name="affiliation" class="select">
                            <option value="">-- Select Affiliation --</option>
                            <option value="All" '.(isset($facility['affiliation']) && $facility['affiliation'] == 'All' ? 'selected' : '').'>All</option>
                            <option value="CPGA" '.(isset($facility['affiliation']) && $facility['affiliation'] == 'CPGA' ? 'selected' : '').'>CPGA</option>
                            <option value="Non-CPGA" '.(isset($facility['affiliation']) && $facility['affiliation'] == 'Non-CPGA' ? 'selected' : '').'>Non-CPGA</option>
                        </select>
                    </div>

                    <div class="form-field col-6">
                        <label>Region</label>
                        <select name="region" class="select">
                            <option value="">-- Select Region --</option>
                            <option value="Northern Alberta" '.(isset($facility['region']) && $facility['region'] == 'Northern Alberta' ? 'selected' : '').'>Northern Alberta</option>
                            <option value="Edmonton & Area" '.(isset($facility['region']) && $facility['region'] == 'Edmonton & Area' ? 'selected' : '').'>Edmonton & Area</option>
                            <option value="Central Alberta" '.(isset($facility['region']) && $facility['region'] == 'Central Alberta' ? 'selected' : '').'>Central Alberta</option>
                            <option value="Calgary & Area" '.(isset($facility['region']) && $facility['region'] == 'Calgary & Area' ? 'selected' : '').'>Calgary & Area</option>
                            <option value="Southern Alberta" '.(isset($facility['region']) && $facility['region'] == 'Southern Alberta' ? 'selected' : '').'>Southern Alberta</option>
                            <option value="Other" '.(isset($facility['region']) && $facility['region'] == 'Other' ? 'selected' : '').'>Other</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact & Location Block -->
        <div class="panel">
            <div class="panel-header">Contact & Location
                <span class="panel-toggle fas fa-chevron-up"></span>
            </div>

            <div class="panel-content">
                <div class="form-row">
                    <div class="form-field col-6">
                        <label>Email</label>
                        <input type="email" name="email" value="'.htmlspecialchars($facility['email'] ?? '').'" class="input" />
                    </div>

                    <div class="form-field col-6">
                        <label>Phone</label>
                        <input type="text" name="phone" value="'.htmlspecialchars($facility['phone'] ?? '').'" class="input" />
                    </div>
                </div>

                <div class="form-field">
                    <label>Website</label>
                    <input type="url" name="website" value="'.htmlspecialchars($facility['website'] ?? '').'" class="input" placeholder="https://" />
                </div>
                </div>


        </div>';

        // Facility Location and Map
        echo '<div class="panel">
        <div class="panel-header">Address & Map
            <span class="panel-toggle fas fa-chevron-up"></span>
            <div class="panel-switch">
                <label>Show Google Map' .$CMSBuilder->tooltip('Show Google Map', 'Display this location on an interactive map (google map must be enabled in settings). You can set your location by using the locate function or by clicking and dragging the map pin.'). '</label>
                <div class="onoffswitch">
						<input type="checkbox" name="google_map" id="google_map" value="1"' .(($facility['google_map'] ?? 1) == 0 ? ' checked' : ''). ' />
					<label for="google_map">
						<span class="inner"></span>
						<span class="switch"></span>
					</label>
				</div>
            </div>
        </div>';

        echo'<div class="panel-content">
				<div class="flex-container">
					<div id="gllpLatlonPicker" class="gllpLatlonPicker flex-column">
						<div class="gllpMap">Google Maps</div>
						<div class="gllpSearch flex-container input-button">
							<input type="text" class="gllpSearchField input places-autocomplete" placeholder="Find an address..." data-address="#address" data-clear="#clear-location"/>
							<button type="button" class="gllpSearchButton button-sm" id="gllpSearchButton"><i class="fas fa-search nomargin"></i></button>
						</div>
						<input type="hidden" name="gpslat" class="gllpLatitude" value="'.(($facility['gpslat'] ?? '') != '' ? $facility['gpslat'] : '53.563967'). '"/>
						<input type="hidden" name="gpslong" class="gllpLongitude" value="' .(($facility['gpslong'] ?? '') != '' ? $facility['gpslong'] : '-113.490357'). '"/>
						<input type="hidden" name="zoom" class="gllpZoom" value="' .(($facility['zoom'] ?? 0) != 0 ? $facility['zoom'] : '12'). '"/>
					</div>

					<div class="flex-container flex-column auto-width">

						<div class="form-field">
							<label>
								Street Address'.(in_array('address', $required_fields) ? ' <span class="required">*</span>' : '').'
								<small class="f_right" id="clear-location"><a>Clear Fields</a></small>
							</label>
							<input type="text"  name="address1" value="' .($facility['address1'] ?? ''). '" class="input" />
							<label>Unit No.' .(in_array('address2', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<input type="text" name="address2" value="' .($facility['address2'] ?? ''). '" class="input" />
							<label>City/Town' .(in_array('city', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<input type="text" name="city" value="' .($facility['city'] ?? ''). '" class="input" />
						</div>

						<div class="form-field">
							<label>Province/State' .(in_array('province', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<select name="province" class="select">
								<option value="">- Select -</option>
								<optgroup label="Canada">';
								foreach($provinces as $code=>$name){
									echo '<option value="' .$code. '"' .(($facility['province'] ?? '') == $code ? ' selected' : ''). '>' .$name. '</option>';
								}
								echo '</optgroup>
								<optgroup label="United States">';
								foreach($states as $code=>$name){
									echo '<option value="' .$code. '"' .(($facility['province'] ?? '') == $code ? ' selected' : ''). '>' .$name. '</option>';
								}
								echo '</optgroup>
							</select>

							<label>Country' .(in_array('country', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<select name="country" class="select">
								<option value="">- Select -</option>
								<option value="Canada"' .(($facility['country'] ?? '') == 'Canada' ? ' selected' : ''). '>Canada</option>
								<option value="United States"' .(($facility['country'] ?? '') == 'United States' ? ' selected' : ''). '>United States</option>
							</select>
							<label>Postal/Zip Code' .(in_array('postal_code', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<input type="text" name="postal_code" value="' .($facility['postal_code'] ?? ''). '" class="input" />
						</div>
					</div>
				</div>
		</div>
		</div>'; //Location
        // end facility location and map

        //Profile image
	// if($avatar){
		// echo '<div class="panel">
		// 	<div class="panel-header">Profile Image
		// 		<span class="panel-toggle fas fa-chevron-up"></span>
		// 	</div>
		// 	<div class="panel-content">
		// 		<div class="flex-container">';

		// 		echo "path : ".$path."<br>";
		// 		echo "imagedir : ".$imagedir."<br>";
		// 		echo "image : ".$image."<br>";

		// 			if(!empty($image)){
		// 				echo '<div class="img-holder">
		// 					<button type="button" name="recrop" value="photo" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
		// 					<a href="' .$path.$imagedir.$image. '" class="light-gallery" target="_blank" title="">
		// 						<img src="' .$path.$imagedir.'thumbs/'.$image. '" alt="" />
		// 					</a>
		// 					<input type="checkbox" class="checkbox" name="deleteimage" id="deleteimage" value="1" />
		// 					<label for="deleteimage">Delete Current Image</label>
		// 				</div>';
		// 			}

		// 			echo '<div class="form-field">
		// 				<label>Upload Image ' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploader::size_label('logos', 'photo'). ' and file size must be smaller than '.$_max_filesize['megabytes'].'.'). '</label>
		// 				<input type="file" class="input' .(in_array('photo', $required) ? ' required' : ''). '" name="photo" value="" />
		// 			</div>';

		// 		echo '</div>
		// 	</div>
		// </div>'; //Profile image
	// }

    echo "<div class='panel'>
            <div class='panel-header'>Logo Image
				<span class='panel-toggle fas fa-chevron-up'></span>
			</div>";
            // echo "path : ".$path."<br>";
            // echo "logo_imagedir : ".$logo_imagedir."<br>";
            // echo "image : ".$image."<br>";
            // echo 'image : '.htmlspecialchars($image).'';

			echo '<div class="panel-content">
				<div class="flex-container">';
					if(!empty($image)){
                        echo '<div class="img-holder">
                        <button type="button" name="recrop" value="photo" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
                        <a href="' .$path.$logo_imagedir.$image. '" class="light-gallery" target="_blank" title="">
                            <img src="' .$path.$logo_imagedir.$image. '" alt="" />
                        </a>
                        <input type="checkbox" class="checkbox" name="delete_logo" id="delete_logo" value="1" />
                        <label for="delete_logo">Delete Current Logo</label>
						</div>';
					}

					echo '<div class="form-field">
						<label>Upload Image ' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploaderLogo::size_label('logo', 'photo'). ' and file size must be smaller than '.$_max_filesize['megabytes'].'.'). '</label>
						<input type="file" class="input' .(in_array('photo', $required) ? ' required' : ''). '" name="logo" value="" />
					</div>';

				echo '</div>
			        </div>';
		echo "</div>";


        // Banner Image start
        echo "<div class='panel'>
            <div class='panel-header'>Banner Image
				<span class='panel-toggle fas fa-chevron-up'></span>
			</div>";
            // echo "path : ".$path."<br>";
            // echo "banner_imagedir : ".$banner_imagedir."<br>";
            // echo "image : ".$banner_image."<br>";
            // echo 'image : '.htmlspecialchars($banner_image).'';

			echo '<div class="panel-content">
				<div class="flex-container">';
					if(!empty($banner_image)){
                        echo '<div class="img-holder">
                        <button type="button" name="recrop" value="photo" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
                        <a href="' .$path.$banner_imagedir.$banner_image. '" class="light-gallery" target="_blank" title="">
                            <img src="' .$path.$banner_imagedir.$banner_image. '" alt="" />
                        </a>
                        <input type="checkbox" class="checkbox" name="delete_banner" id="delete_banner" value="1" />
                        <label for="delete_banner">Delete Current Banner</label>
						</div>';
					}

					echo '<div class="form-field">
						<label>Upload Image ' .$CMSBuilder->tooltip('Upload Image', 'Image dimensions should be at least ' .$CMSUploaderBanner::size_label('banner', 'photo'). ' and file size must be smaller than '.$_max_filesize['megabytes'].'.'). '</label>
						<input type="file" class="input' .(in_array('photo', $required) ? ' required' : ''). '" name="image" value="" />
					</div>';

				echo '</div>
			        </div>';
		echo "</div>";
        // Banner Image upload end

        echo '<div class="panel">
            <div class="panel-header">Content
                <span class="panel-toggle fas fa-chevron-up"></span>
            </div>

            <div class="panel-content">';

            echo "<div id='content'>";
					echo "<div class='clear'>
						<textarea name='content' class='tinymceMini' style='width:800px;'>" .(isset($facility['content']) ? $facility['content'] : ""). "</textarea>
					</div>";
				echo "</div>";

            echo '</div>
        </div>';

        //SEO Content/Analysis
		include('includes/widgets/seotabs.php');

		//Sticky footer
		include('includes/widgets/formbuttons.php');

        // echo '<div class="form-actions">
        //     <a href="'.PAGE_URL.'" class="button">Cancel</a>
        //     <button type="submit" name="save" value="1" class="button primary">Save Facility</button>
        // </div>';

        echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid'] .'" />

    </form>';
}

?>
