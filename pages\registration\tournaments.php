<?php

$html = '';
$sidebar = '';

//Tournament listings
if(!isset($event)){
	
	//Login prompt
	if(!USER_LOGGED_IN){
		$html .= '<p><small>You must <a href="' .$_sitepages['login']['page_url']. '?redirect=' .base64_encode($_SERVER['REQUEST_URI']). '">login</a> to access tournament registration.</small></p>';
	
	//Get user waitlists
	}else{
		$account_waitlists = $Registration->get_user_wait_lists();
	}
		
	//Search form
	$html .= '<form name="search-form" id="directory-search-bar" action="" method="get" class="clearfix multi-search tournament-search-container">
			<select name="category" class="select  f_left" onchange="this.form.submit();">
				<option value="">All Tournaments</option>';
				foreach($categories as $category){
					$html .= '<option value="' .$category['category_id']. '"' .(isset($_GET['category']) && $_GET['category'] == $category['category_id'] ? ' selected' : ''). '>' .$category['name']. '</option>';	
				}
			$html .= '</select>
			<select name="year" class="select  f_right" onchange="this.form.submit();">
				<option value="' .date('Y'). '">Current Season</option>';
				for($y=(date('Y')-1); $y>=2014; $y--){
					$html .= '<option value="' .$y. '"' .(isset($_GET['year']) && $_GET['year'] == $y ? ' selected' : ''). '>' .$y. '</option>';
				}
			$html .= '</select>
		<div class="form-column">
			<input type="text" name="search" class="input" value="' .(isset($_GET['search']) ? $_GET['search'] : ''). '" placeholder="Search" />
			<button type="submit" class="button solid"><i class="fa fa-search"></i></button>
			' .(isset($_GET['search']) && trim($_GET['search']) != '' ? '<a href="' .$page['page_url']. '" class="fa fa-times-circle search-icon"></a>' : ''). '
		</div>
	</form>';
	
	$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="tournament-list">';
	if(!empty($tournaments)){
		$html .= '<tr class="header-row">
			<th align="left" width="150px">Tournament</th>
			<th align="left" width="170px">Location</th>
			<th align="left" width="120px">Start Date</th>
			<th align="center" width="100px">Field</th>
			<th width="100px">&nbsp;</th>
		</tr>';
		
		foreach($tournaments as $event){
			
			//Check availability
			$open = ($event['occurrence_status'] == 'Open' && (strtotime($event['reg_open'].' '.$reg_settings['reg_open_time']) <= strtotime('now')) && (strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time']) > strtotime('now')) ? true : false);
			$full = (!is_null($event['max_capacity']) && $event['attendance'] >= $event['max_capacity'] ? true : false);
			$started = ($event['start_date'] <= date("Y-m-d") ? true : false);
			$isgender = (is_null($event['gender']) || (!is_null($event['gender']) && $Account->gender == $event['gender']) ? true : false);
			$isregistered = (!is_null($event['registered']) ? true : false);
						
			//Check eligibility
			$eligible = false;
			$isclass = false;
			if(empty($event['role_id']) || (!empty($event['role_id']) && $Account->account_has_role($event['role_id']))){
				
				//Member eligibility check
				if($event['role_id'] == 2){
					$isclass = ((!is_null($event['class_eligibility']) || $event['membership_classes'] == 0) ? true : false);
					$eligible = ($isclass && !empty($Account->category_id) && (!is_null($event['eligibility']) || $event['eligible_categories'] == 0) ? true : false);
				}else{
					$eligible = true;
					$isclass = true;
				}
			}
							
			$html .= '<tr class="tr-row">
				<td>
					<a href="' .$page['page_url'].$event['page'].'-'.$event['occurrence_id']. '/">' .$event['event_name']. '</a>
					<small class="dblock">' .$event['category_name']. '</small>
				</td>
				<td>' .$event['facility_name']. '</td>
				<td>' .date('M j, Y', strtotime($event['start_date'])). '</td>
				<td align="center">' .$event['attendance'].'/'.$event['max_capacity']. '</td>';

                // TODO For debugging only
                // echo "<pre>Event:";
                // print_r($event);
                // echo "</pre>";

                // echo "eligible :".$eligible."<br>";
                // echo "isgender :".$isgender."<br>";
                // echo "isregistered :".$isregistered."<br>";
                // echo "open :".$open."<br>";
                // echo "started :".$started."<br>";
                // echo "full :".$full."<br>";

						
				//Register
				if($eligible && $isgender && !$isregistered && $open && !$started && !$full){
					$html .= '<td align="center"><a onclick="addCartItem(this);" class="red border" data-id="' .$event['occurrence_id']. '" data-name="' .$event['event_name']. '"><i class="fa"></i><strong>Register</strong></a></td>';
					
				//Waitlist
				}else if($eligible && $isgender && !$isregistered && $open && !$started && $full && $event['waiting_list'] == '1'){
					if(array_key_exists($event['occurrence_id'], $account_waitlists)){
						$html .= '<td align="center"><strong>Waiting</strong></td>';
					}else{
						$html .= '<td align="center"><a onclick="waitList(this);" class="red border" data-id="' .$event['occurrence_id']. '" data-name="' .$event['event_name']. '"><i class="fa"></i><strong>Waitlist</strong></a></td>';
					}
				
				//Closed
				}else{
					$html .= '<td align="center">'; 
						if($isregistered){
							$html .= '<strong>Registered</strong>';
						}else if(strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time']) <= strtotime('now')){
							$html .= '<strong>Closed</strong>';
						}else if(!$eligible || !$isgender){
							$html .= '<strong>Ineligible</strong>';
						}else{
							$html .= '<strong>Unavailable</strong>';
						}
					$html .= '</td>';
				}
			
			$html .= '</tr>';
		}
		
	}else{
		$html .= '<tr><td class="nobg">No tournaments found' .(isset($_GET['search']) && $_GET['search'] != '' ? ' matching `' .$_GET['search']. '`' : ''). '.</td></tr>';
	}
	$html .= '</table>';
	
	if(count($tournaments) > 0){
		$html .= '<p><small>Displaying 1-' .count($tournaments). ' (' .count($tournaments). ' Total)</small></p>';
	}
		
	//Set panel content
	$page['page_panels'][$panel_id]['content'] .= $html;
	
	
//Selected tournament	
}else{

    // echo "<pre>Event:";
    // // print_r($event);
    // echo "</pre>";
    // exit;
	
	//Sent back from register page
	if(isset($_SESSION['reg_error'])){
		$html .= $Account->alert($_SESSION['reg_error'], false);
		unset($_SESSION['reg_error']);
	}
	
	if(USER_LOGGED_IN){
	
		//Register
		if($event['reg_available']){
			$html .= '<form name="register-form" action="' .$_sitepages['registration']['page_url']. '" method="post">
				<p><button type="submit" class="button solid">Register Now</button></p>
				<input type="hidden" name="tournament_id" value="' .$event['event_id']. '" />
				<input type="hidden" name="occurrence_id" value="' .$event['occurrence_id']. '" />
				<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
			</form>';

		//Waitlist
		}else if($event['reg_waitlist']){
			
			//Get user waitlists
			$account_waitlists = $Registration->get_user_wait_lists();
			if(array_key_exists($event['occurrence_id'], $account_waitlists)){
				$html .= $Account->important('This event is currently at capacity and you are subscribed to the waiting list.');	
			}else{
				$html .= $Account->important('This event is currently at capacity.');	
				$html .= '<p><button type="button" class="button solid waitlist" onclick="waitList(this);" data-id="' .$event['occurrence_id']. '" data-name="' .$event['event_name']. '"><i class="fa"></i>Waiting List</button></p>';
			}

		//Closed
		}else{
			if($event['isregistered']){
				if($event['end_date'] > date("Y-m-d")){
					$html .= $Account->important('You are registered for this tournament.');
				}else{
					$html .= $Account->important('Registration is now closed.');
				}
			}else if(strtotime($event['reg_open'].' '.$reg_settings['reg_open_time']) > strtotime('now')){
				$html .= $Account->important('Registration opens on ' .date("F j, Y @g:iA", strtotime($event['reg_open'].' '.$reg_settings['reg_open_time'])). '.');
			}else if($event['occurrence_status'] != 'Open' || $event['started'] || (strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time']) <= strtotime('now'))){
				$html .= $Account->important('Registration is now closed.');
			}else if(!$event['eligible'] || !$event['isgender']){
				$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> You do not meet the eligibility requirements to register for this event.');
			}else if($event['full']){
				$html .= $Account->important('This event is currently at capacity.');
			}else{
				$html .= $Account->important('Registration is currently unavailable.');
			}
		}
		
	//Login prompt
	}else{
		$html .= $Account->important('<i class="fa fa-lock"></i> You must <a href="' .$_sitepages['login']['page_url']. '?redirect=' .base64_encode($_SERVER['REQUEST_URI']). '">login</a> to access tournament registration.');
	}
	
	//Event details
	$html .= '<section id="tournament-information">';
		
		//Reports
		if($event['report_drawalpha'] || $event['report_draw'] || $event['report_drawscore'] || $event['report_results'] || $event['report_money']){
			$html .= '<p class="report">';
				if($event['report_drawalpha']){
					$html .= '<a href="' .$path. 'reports/report-draw.php?id=' .$event['occurrence_id']. '&sort=alpha" target="_blank">
						<i class="fa fa-file-pdf-o"></i>&nbsp; Tournament Draw (Alphabetical)
					</a><br />';
				}
				if($event['report_draw']){
					$html .= '<a href="' .$path. 'reports/report-draw.php?id=' .$event['occurrence_id']. '" target="_blank">
						<i class="fa fa-file-pdf-o"></i>&nbsp; Tournament Draw (Tee Time/Hole)
					</a><br />';
				}
				if($event['report_drawscore']){
					$html .= '<a href="' .$path. 'reports/report-draw.php?id=' .$event['occurrence_id']. '&sort=score" target="_blank">
						<i class="fa fa-file-pdf-o"></i>&nbsp; Round 1 Results &amp; Round 2 Draw
					</a><br />';
				}
				if($event['report_results']){
					$html .= '<a href="' .$path. 'reports/report-results.php?id=' .$event['occurrence_id']. '"" target="_blank">
						<i class="fa fa-file-pdf-o"></i>&nbsp; Final Results
					</a><br />';
				}
				if($event['report_money']){
					$html .= '<a href="' .$path. 'reports/report-money.php?id=' .$event['occurrence_id']. '"" target="_blank">
						<i class="fa fa-file-pdf-o"></i>&nbsp; Overall Money
					</a><br />';
				}
			$html .= '</p>';
		}

		$html .= '<div class="event-container">';
		$html .= '<div class="event-table-container">';
	
		//Information
		$html .= '<header><h4>Event Information</h4></header>';
		$html .= '<table cellpadding="0" cellspacing="0" border="0" width="100%" class="noborder nobgs event-table">
			<tr>
				<td width="250px"><strong>Event Date:</strong></td>
				<td>' .format_date_range($event['start_date'], $event['end_date']). '</td>
			</tr>
			<tr>
				<td><strong>Host Venue:</strong></td>
				<td>' .(!empty($facility) ? '<a href="' .$_sitepages['facilities']['page_url'].$facility['page'].'-'.$facility['facility_id']. '/">' .$facility['facility_name']. '</a>' : 'Unknown'). '</td>
			</tr>
			<tr>
				<td><strong>Current Field:</strong></td>
				<td>';
					$field_term = ($event['team_event'] ? 'team' : 'player');
					if($event['attendee_sharing'] == '1' || ($event['attendee_sharing'] == '-1' && $reg_settings['attendee_sharing'] == '1')){
						$html .= '<a onclick="dialogModal(\'#attendee-list\');">' .$event['attendance']. ' '.$field_term.($event['attendance'] == 1 ? '' : 's'). '</a>';

						$attendee_list = $Registration->get_current_attendance($event['occurrence_id']);
						
						//Sort teams
						if($event['team_event']){
							$attendees = array();
							$partners = array();
							foreach($attendee_list as $attendee){
								if(empty($attendee['partner_id'])){
									$attendee['partner'] = array();
									$attendees[$attendee['attendee_id']] = $attendee;
								}
							}
							foreach($attendee_list as $partner){
								if(!empty($partner['partner_id'])){
									if(array_key_exists($partner['partner_id'], $attendees)){
										$attendees[$partner['partner_id']]['partner'] = $partner;
									}else{
										$attendees[$partner['partner_id']] = $partner;
									}
								}
							}
							$attendee_list = $attendees;
						}
						$html .= '<div class="hidden">
							<div id="attendee-list" class="hidden-modal" title="Current Field" data-modal-width="500" data-modal-class="dialog-scroll">';
							$noattendees = true;	
							if(!empty($attendee_list)){
								foreach($attendee_list as $attendee){
									if($attendee['attendee_sharing']){
										$html .= '<strong>' .$attendee['last_name']. ', ' .$attendee['first_name']. '</strong>';
										$html .= (!empty($attendee['facility_name']) ? ' - '.$attendee['facility_name'] : '');
										if(!empty($attendee['partner'])){
											$html .= '<br /><strong>' .$attendee['partner']['last_name']. ', ' .$attendee['partner']['first_name']. '</strong>';
											$html .= (!empty($attendee['partner']['facility_name']) ? ' - '.$attendee['partner']['facility_name'] : '');
										}
										$html .= '<hr class="nomargin" />';
										$noattendees = false;
									}
								}
							}
							if($noattendees){
								$html .= 'No registered '.$field_term.'s found.';
							}
							$html .= '</div>
						</div>';

					}else{
						$html .= $event['attendance']. ' '.$field_term.($event['attendance'] == 1 ? '' : 's');
					}
				$html .= '</td>
			</tr>
			<tr>
				<td><strong>Maximum Field:</strong></td>
				<td>' .(!is_null($event['max_capacity']) ? $event['max_capacity'] : 'Unlimited'). ' '.$field_term.'s</td>
			</tr>';
			if($event['waiting_list'] != '0'){
				$waiting_list = $Registration->get_current_wait_list($event['occurrence_id']);
				$html .= '<tr>
					<td><strong>Waiting List:</strong></td>
					<td>';
						if($event['attendee_sharing'] == '1' || ($event['attendee_sharing'] == '-1' && $reg_settings['attendee_sharing'] == '1')){
							$html .= '<a onclick="dialogModal(\'#wait-list\');">' .count($waiting_list). ' player' .(count($waiting_list) == 1 ? '' : 's'). '</a>';
							$html .= '<div class="hidden">
								<div id="wait-list" class="hidden-modal" title="Current Waiting List" data-modal-width="500" data-modal-class="dialog-scroll">';
								if(!empty($waiting_list)){
									foreach($waiting_list as $wait){
										$html .= '<strong>' .$wait['last_name']. ', ' .$wait['first_name']. '</strong>';
										$html .= (!empty($wait['facility_name']) ? ' - '.$wait['facility_name'] : ''). '<hr class="nomargin" />';
									}
								}else{
									$html .= 'Waiting list is empty.';
								}
								$html .= '</div>
							</div>';

						}else{
							$html .= count($waiting_list). ' player' .(count($waiting_list) == 1 ? '' : 's');
						}
					$html .= '</td>
				</tr>';
			}
			if(!empty($event['pricing'])){
				$html .= '<tr>
					<td><strong>' .$event['pricing'][0]['price_type']. ':</strong></td>
					<td>$' .$event['pricing'][0]['price']. '</td>
				</tr>';
			}
			if($event['tournament_fee'] > 0){
				$html .= '<tr>
					<td><strong>Skins:</strong></td>
					<td>$' .$event['tournament_fee']. '</td>
				</tr>';
			}
			if(!empty($event['draw_type'])){
				$html .= '<tr>
					<td><strong>Draw Type:</strong></td>
					<td>' .$event['draw_type']. '</td>
				</tr>';
			}
			if(!empty($event['scoring_type'])){
				$html .= '<tr>
					<td><strong>Scoring Type:</strong></td>
					<td>' .$event['scoring_type']. '</td>
				</tr>';
			}
			if(!empty($event['purse'])){
				$html .= '<tr>
					<td><strong>Purse:</strong></td>
					<td>$' .number_format($event['purse']). '</td>
				</tr>';
			}
			if(!empty($event['purse_3_year_avg'])){
				$html .= '<tr>
					<td><strong>3 year - Average of Purse:</strong></td>
					<td>$' .number_format($event['purse_3_year_avg']). '</td>
				</tr>';
			}
			if(!empty($event['par'])){
				$html .= '<tr>
					<td><strong>Course Par:</strong></td>
					<td>' .$event['par']. '</td>
				</tr>';
			}
			if(!empty($event['rounds'])){
				$html .= '<tr>
					<td><strong>Rounds:</strong></td>
					<td>' .$event['rounds']. '</td>
				</tr>';
			}

		$html .= '</table>';
	
		//Restrictions
		$html .= '<h4>Event Eligibility</h4>
		<table cellpadding="0" cellspacing="0" border="0" width="100%" class="noborder nobgs event-table">';
			if(!empty($event['eligibility_categories'])){
				$ecats = array();
				foreach($event['eligibility_categories'] as $ecat){
					$ecats[] = $ecat['name'];
				}
				$html .= '<tr>
					<td valign="top"><strong>Members:</strong></td>
					<td>' .implode(', ', $ecats). '</td>
				</tr>';
			}else if($event['role_id'] == 8){
				$html .= '<tr>
					<td valign="top"><strong>Membership:</strong></td>
					<td>Non-Members</td>
				</tr>';
			}
			if(!empty($event['event_membership_classes'])){
				$mclasses = array();
				foreach($event['event_membership_classes'] as $mclass){
					$mclasses[] = $mclass['class_name'];
				}
				$html .= '<tr>
					<td valign="top"><strong>Membership Class:</strong></td>
					<td>' .implode(', ', $mclasses). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td width="250px"><strong>Gender:</strong></td>
				<td>' .(is_null($event['gender']) ? 'Both' : $event['gender']). '</td>
			</tr>
			<tr>
				<td><strong>Age:</strong></td>
				<td>' .(is_null($event['age']) ? 'All' : $event['age']). '</td>
			</tr>
		</table>';

		//Deadlines
		$html .= '<h4>Event Deadlines</h4>
		<table cellpadding="0" cellspacing="0" border="0" width="100%" class="noborder nobgs event-table">';
	
		//Only show payment deadline if it is later than registration open date
		/*if($event['payment_deadline'] != $event['reg_open']){
			$html .= '<tr>
				<td valign="top" width="250px"><strong>Payment Deadline:</strong></td>
				<td valign="top">' .date("F j, Y @g:iA", strtotime($event['payment_deadline'].' '.$reg_settings['reg_close_time'])). '</td>
			</tr>';
		}*/
			$html .= '<tr>
				<td valign="top"><strong>Payment Deadline:</strong></td>
				<td valign="top">' .date("F j, Y @g:iA", strtotime($event['payment_deadline'].' '.$reg_settings['reg_close_time'])). ' &nbsp; <small>Outstanding tournament fees are due</small></td>
			</tr>
			<tr>
				<td valign="top"><strong>Withdrawal Deadline:</strong></td>
				<td valign="top">' .date("F j, Y @g:iA", strtotime($event['withdrawal_deadline'].' '.$reg_settings['reg_close_time'])). '</td>
			</tr>
			<tr>
				<td valign="top"><strong>Registration Deadline:</strong></td>
				<td valign="top">' .date("F j, Y @g:iA", strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time'])). '</td>
			</tr>
		</table>';

	//Event content
	$html .= $event['description'];

	$html .= '</div><div class="event-info-container">';

	//Sponsors
	if(!empty($event['sponsors'])){
		krsort($event['sponsors']);
		
		foreach($event['sponsors'] as $type=>$type_sponsors){
			$html .= '<h3 class="center">Thank You to Our<br />'.$type.' Sponsors</h3>';
			$html .= '<ul class="sponsors">';
			foreach($type_sponsors as $sponsor){
				$sponsor['website'] = str_replace('http://', '', str_replace('https://', '', $sponsor['website']));
				$html .= '<li class="sponsor">
					<a' .(trim($sponsor['website']) != '' ? ' href="http://' .$sponsor['website']. '"' : ''). ' class="img-holder">';
						if($sponsor['image'] != '' && file_exists('images/partners/'.$sponsor['image'])){
							$html .= '<div><img src="' .$path.'images/partners/' .$sponsor['image']. '" alt="' .(trim($sponsor['image_alt']) != '' ? $sponsor['image_alt'] : $sponsor['name']). '" /></div>';
						}
					$html .= '</a>
					<h4>' .$sponsor['name']. '</h4>';
					if(trim($sponsor['website']) != ''){
						$html .= '<p><a href="http://' .$sponsor['website']. '">' .$sponsor['website']. '</a></p>';
					} 
				$html .= '</li>';
			}
			$html .= '</ul>';
		}
	}

	$html .= '</div>';
	$html .= '</div>';
	
	$html .= '</section>';
	
	// $html .= '<hr />';
	
	//Event content
	// $html .= $event['description'];

	// echo "<pre> sponsors - ";
	// print_r($event['sponsors']);
	// echo "</pre>";
	// exit;

	// //Sponsors
	// if(!empty($event['sponsors'])){
	// 	krsort($event['sponsors']);
		
	// 	foreach($event['sponsors'] as $type=>$type_sponsors){
	// 		$sidebar .= '<h3 class="center">Thank You to Our<br />'.$type.' Sponsors</h3>';
	// 		$sidebar .= '<ul class="sponsors">';
	// 		foreach($type_sponsors as $sponsor){
	// 			$sponsor['website'] = str_replace('http://', '', str_replace('https://', '', $sponsor['website']));
	// 			$sidebar .= '<li class="sponsor">
	// 				<a' .(trim($sponsor['website']) != '' ? ' href="http://' .$sponsor['website']. '"' : ''). ' class="img-holder">';
	// 					if($sponsor['image'] != '' && file_exists('images/logos/'.$sponsor['image'])){
	// 						$sidebar .= '<div><img src="' .$path.'images/logos/' .$sponsor['image']. '" alt="' .(trim($sponsor['image_alt']) != '' ? $sponsor['image_alt'] : $sponsor['name']). '" /></div>';
	// 					}
	// 				$sidebar .= '</a>
	// 				<h4>' .$sponsor['name']. '</h4>';
	// 				if(trim($sponsor['website']) != ''){
	// 					$sidebar .= '<p><a href="http://' .$sponsor['website']. '">' .$sponsor['website']. '</a></p>';
	// 				} 
	// 			$sidebar .= '</li>';
	// 		}
	// 		$sidebar .= '</ul>';
	// 	}
	// }

	//Set panel content
	$page['page_panels'][$panel_id]['title'] = $event['event_name'];
	$page['page_panels'][$panel_id]['show_title'] = true;
	$page['page_panels'][$panel_id]['show_title_first'] = true;
	$page['page_panels'][$panel_id]['content'] = $html;
	$page['page_panels'][$panel_id]['sidebar'] = $sidebar;
	$page['page_panels'][$panel_id]['sidebar_border'] = false;
	
}

//Upcoming events
$page['page_panels']['events'] = array(
	'panel_id' => 'events',
	'panel_type' => 'events',
	'title' => 'More Upcoming Events',
	'show_title' => 1,
	'content' => ''
);

//Page panels
include("includes/pagepanels.php");

?>