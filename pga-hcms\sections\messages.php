<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";

	echo "<div class='panel'>";
		echo "<div class='panel-header'>" .$record_name. "s
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

			echo "<thead>";
			echo "<th width='auto'>Subject</th>";
			echo "<th class='{sorter:\"monthDayYear\"}' width='150px'>Post Date</th>";
			echo "<th class='center' width='70px'>Sent</th>";
			echo "<th class='{sorter:false}' width='250px'>&nbsp;</th>";
			echo "</thead>";

			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .$row['subject']. "</td>";
					echo "<td>" .date('M d, Y', strtotime($row['date_added'])). "</td>";
					echo "<td class='center'>" .(!is_null($row['date_sent']) ? "<span class='show'>Sent</span>" : "<span class='hide'>Not Sent</span>"). "</td>";
					echo "<td class='right'>
						".(!is_null($row['date_sent']) ? "<a href='" .PAGE_URL. "?action=stats&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-bar-chart'></i>Stats</a>" : "")."
						<a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a>
					</td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo "</div>";	
	echo "</div>";

//Mass mail stats
}else if(ACTION == 'stats'){

	$records_arr[ITEM_ID]['item_id'] = 'mc'.$records_arr[ITEM_ID]['message_id'];
	$statistics = $sendgrid->get_newsletter_stats($records_arr[ITEM_ID]['item_id'], '', true);
	
	echo "<h2>".$records_arr[ITEM_ID]['subject']."</h2>";
	echo "<p>
		<strong>Sent on:</strong> ".date("F d, Y @ g:iA",strtotime($records_arr[ITEM_ID]['date_sent']))."<br />
	</p>";
	
	include("includes/widgets/newsletterchart.php");
	include("includes/widgets/newsletterstats.php");

//Display form	
}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$file = $data['filename'];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		$file = '';		
		unset($row);
	}

	echo "<form id='email-content' action='' method='post' enctype='multipart/form-data'>";

		//Content
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Subject <span class='required'>*</span></label>
					<input type='text' name='subject' class='input" .(in_array('subject', $required) ? ' required' : ''). "' value='" .(isset($row['subject']) ? $row['subject'] : ''). "' />
				</div>";
				echo "<div class='clear' style='width:840px'>
					<label>Content <span class='required'>*</span></label>
					<textarea name='content' class='tinymceEmail'>" .(isset($row['content']) && trim($row['content']) != '' ? $row['content'] : ''). "</textarea>
				</div>";
			echo "</div>";
		echo "</div>";

		//Upload file
		echo "<div class='panel'>";
			echo "<div class='panel-header'>PDF Attachment
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Upload File" .$CMSBuilder->tooltip('Upload File', 'File size must be smaller than 20MB.'). "</label>
					<input type='file' class='input" .(in_array('file', $required) ? ' required' : ''). "' name='file' value='' />
					<input type='hidden' name='old_file' value='" .(isset($file) && $file != '' && file_exists($filedir.$file) ? $file : ''). "' />
				</div>";
				if(isset($file) && $file != '' && file_exists($filedir.$file)){
					echo "<p class='clear'>
						<a href='".$root."download.php?file=".$file."&dir=attachments' target='_blank'><i class='fa fa-download'></i> Download Current File</a> &nbsp; 
						<input type='checkbox' class='checkbox' name='deletefile' id='deletefile' value='1'>
						<label for='deletefile'>Delete Current File</label>";
					echo "</p>";
				}
			echo "</div>";
		echo "</div>";
	
		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "<input type='hidden' name='keep_tags[]' value='content' />";
	echo "</form>";

}

?>