<?php
	
if($permission && ITEM_ID){
	try{
		//Set page/item details
		$Analyzer->set_page(
			$row['focus_keyword'] ?? '', 
			strip_tags($page_url), 
			$row['slug'] ?? false ?: $row['page'], 
			$title ?? $row['page_title'], 
			ITEM_ID, 
			$record_db,
			$record_id
		);

		//Analyze page
		$Analyzer->analyze_page();

	}catch(Exception $e){
		$seo_error = '<p class="error"><i class="fas fa-exclamation-triangle"></i> &nbsp;<strong>Warning:</strong> ' .$e->getMessage(). '</p><hr/>';
	}

	//Get page score
	$overall_score = $Analyzer->get_score();
	if($overall_score > 80){
		$class = 'seo-pass';
	}else if($overall_score >= 50 && $overall_score <= 80){
		$class = 'seo-average';
	}else{
		$class = 'seo-fail';
	}

	//Get site average for all items with seo score
	$tables_to_avg = array('pages', 'staff', 'careers');
	$site_avg = $Analyzer->get_site_average($tables_to_avg);
	if($site_avg > 80){
		$avg_class = 'seo-pass';
	}else if($site_avg >= 50 && $site_avg <= 80){
		$avg_class = 'seo-average';
	}else{
		$avg_class = 'seo-fail';
	}


	// SEO summary
	$summary = $Analyzer->get_summary();

	// Group by grade
	$failed  = array_search_assoc($summary, 'grade', 1);
	$warning = array_search_assoc($summary, 'grade', 2);
	$passed  = array_search_assoc($summary, 'grade', 3);
}

if(ITEM_ID && ($on_pages || ($seo_page_id ?? false))){
	if ($seo_error ?? false) {
		echo $seo_error;

	} else {
		echo '<div id="seo-summary">';

		if ($permission) {
			echo '<div class="dashboard-box progress-bar">
				<div class="percent-progress '.$class.'" style="--percent:'.$overall_score.'%"></div>
				<div class="percentage">
					<div class="numbers">
						<span>'.$overall_score.'</span>
					</div>
					<p class="uppercase">
						Search Engine Optimization
						<small>Content Analysis (This Page)</small>
					</p>
				</div>
			</div>';
		}
		
			$slug   = basename($page_url);
			$domain = preg_replace('/'.preg_quote($slug, '/').'$/', '', rtrim($page_url, '/'));
			$slug   = !$on_pages && ITEM_ID ? preg_replace('/-'.ITEM_ID.'$/', '', $slug) : $slug;

			echo '<div class="google-preview">
				<p class="color-grey">This Page in Google Search Results:</p>

				<div>
					<h2 class="seo-title">' .($row['meta_title'] ?? false ?: $default_meta_title). '</h2>
					<h6 class="seo-slug">' .$domain.'<span>'.$slug.'</span>'.(!$on_pages && ITEM_ID ? '-'.ITEM_ID : '').'/</h6>

					<p class="seo-description">' .truncate($row['meta_description'] ?? '', 160). '</p>
					<input id="default-url" type="hidden" name="default-url" value="' .strip_tags($page_url). '" />
					<input id="default-meta-title" type="hidden" name="default-meta-title" value="' .$default_meta_title. '" />
				</div>
			</div>';


		if ($permission) {
			echo '<hr>

			<div class="seo-summaries">
				<div class="summary-header">
					<h3 class="color-darkest bolder text-caps nomargin">SEO Analysis & Recommendations</h3>
					<p class="color-grey">Analyzer requires all changes to be saved to update.</p>
				</div>';

			if ($failed) {
				echo '<div class="seo-summary failed">
					<div class="summary-title">'.count($failed).' Items Require Attention</div>
					<ul class="summary-list">
						<li>'.implode('</li><li>', array_column($failed, 'message')).'</li>
					</ul>
				</div>';
			}

			if ($warning) {
				echo '<div class="seo-summary warning">
					<div class="summary-title">'.count($warning).' Items Require Adjustments</div>
					<ul class="summary-list">
						<li>'.implode('</li><li>', array_column($warning, 'message')).'</li>
					</ul>
				</div>';
			}

			if ($passed) {
				echo '<div class="seo-summary passed">
					<div class="summary-title">'.count($passed).' Items Passed</div>
					<ul class="summary-list">
						<li>'.implode('</li><li>', array_column($passed, 'message')).'</li>
					</ul>
				</div>';
			}

			echo '</div>';
		}

		echo '</div>';
	}
}
	
?>