/*---- dialogs ----*/

// Themes
.ui-dialog{
	--dialog-bg: @color-light;
	--dialog-color: @font-color;
	--title-bg: @color-gray-darker;
	--title-color: @color-light;
	--title-padding: 20px;
	--content-padding: 20px;

	&.dialog-alert{
		--title-padding: 10px 20px;
		--content-padding: 20px;
	}
	&.dialog-success{
		--title-bg: @color-success;
	}
	&.dialog-error{
		--title-bg: @color-error;
	}
}

.ui-dialog{
	max-width: calc(100% - 40px);
	max-height: calc(100vh - 40px);
	background: var(--dialog-bg);
	color: var(--dialog-color);
	outline: none;
	overflow: hidden auto;
	visibility: visible;
	z-index: 9999;

	button:not(.button){
		background: none;
		border: 0;
		padding: 0;
		margin: 0;
		cursor: pointer;
		.trans(color);
	}

	.ui-icon{
		top:10%;
	}

	.ui-dialog-titlebar{
		.flexbox(row nowrap; space-between; center;);
		padding: var(--title-padding);
		color: var(--title-color);
		background: var(--title-bg);
		// background: @color-theme2;

		.ui-dialog-title{
			margin: 0;
			.flex(1 1 auto);
		}

		.ui-dialog-titlebar-close{
			outline: none;
			margin-left: 5px;
			.flex(0 0 auto);

			&::before{
				width: 1em;
				height: 1em;
				line-height: 1em;
				text-align: center;
				.font-awesome(f00d);
			}
		}
	}

	.ui-dialog-content,
	.ui-dialog-buttonset{
		padding: var(--content-padding);
	}
	.ui-dialog-buttonset{
		padding-top: 0;
	}

	.ui-dialog-content{
		overflow: hidden auto; 
	}

	.ui-dialog-buttonset{
		.flexbox(row nowrap; flex-end; center;); 
		gap: 10px;
	}
}

.ui-widget-overlay{
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9998;
	background: rgba(0,0,0,0.25);
}

#dialog-box,
.recaptcha-modal,
.hidden-modal{
	&:not(.ui-dialog-content){
		display: none;
	}
}

.recaptcha-modal{
	overflow: hidden;
}