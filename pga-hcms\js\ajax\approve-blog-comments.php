<?php
//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

$return = array("success"=>"false","message"=>"");

if(isset($_POST) && USER_LOGGED_IN){
	$comment_id = $_POST['comment_id'];
	$approved = $_POST['approve'];
	if($comment_id != ''){
		if($approved == -1){
			$params = array($comment_id);
			$query = $db->query("DELETE FROM blog_comments WHERE comment_id = ?",$params);
		} else {
			$params = array($approved,$comment_id);
			$query = $db->query("UPDATE blog_comments SET approved = ? WHERE comment_id = ?",$params);
		}
		if($query && !$db->error()){
			$return['success'] = true;
			$return['message'] = $CMSBuilder->mini_alert("<p>Comment status successfully saved!</p>",true);
		}else{
			$return['message'] = $CMSBuilder->mini_alert("<p>There was an error updating this comment status: ".$db->error()."</p>",false);
		}
	} else {
		$return['message'] = $CMSBuilder->mini_alert("<p>There was an error updating this comment status.</p>",false);
	}
}

echo json_encode($return);

?>