<?php

//Table listing
if(ACTION == ''){
	
	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';
	
	//Display listings
	echo '<div class="panel">
		<div class="panel-header">'.$record_names.
			'<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
				<thead>
					<th width="1px" class="nopadding-r" data-sorter="false"></th>	
					<th>Name</th>
					<th class="center">Entries</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>'.
					($blog_settings['show_author'] ? '<th width="1px" class="nopadding-l" data-sorter="false">&nbsp;</th>' : '').
				'</thead>
			
				<tbody>';

				foreach($records_arr as $row){
					echo '<tr>
						<td class="nopadding-r">'.($row['image'] ? '<a href="'.$path.$imagedir.'thumbs/'.$row['image'].'" class="light-gallery" title="'.$row['name'].'">'.render_gravatar($imagedir.'thumbs/'.$row['image']).'</a>' : render_gravatar($imagedir.'thumbs/default.jpg')).'</td>
						<td>'.$row['name'].'</td>
						<td class="center">'.($row['num_entries'] ? '<a class="bold" href='.$row['entries_url'].'>'.$row['num_entries'].'</a>' : $row['num_entries']).'</td>
						<td class="center">'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td class="right"><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>'.
						($blog_settings['show_author'] ? '<td class="nopadding-l"><a href="'.$row['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td>' : '').
					'</tr>';
				}

				echo '</tbody>
			</table>';
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo '</div>	
	</div>';
	

//Image cropping
}else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');

//Display form	
}else{
	
	$image = '';
	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$image = $data['image'];	
		if(!isset($_POST['save'])){
			$row = $data;
		}
	
		echo '<div class="actions-nav flex-container">
			<div class="flex-column right">
				<small><b>Link to '.$record_name.':</b> '.$data['page_url'].'&nbsp;&nbsp;<a href="'.$data['page_url'].'" target="_blank"><i class="fas fa-external-link-alt"></i></a></td></small>
			</div>
		</div>';
		
		echo $CMSBuilder->important('Deleting an author will delete all related entries. Ensure all entries are set to the correct author before deleting an author.');
		
	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';
	
		//Author details
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show '.$record_name.' Entries '.$CMSBuilder->tooltip('Show '.$record_name, 'Disabling this option will hide all entries posted by this author.').'</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"'.(($row['showhide'] ?? 0) ? '' : ' checked').' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>
			<div class="panel-content">
				<div class="flex-container">
					<div class="form-field">
						<label>Name'.(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="name" value="'.($row['name'] ?? '').'" class="input'.(in_array('name', $required) ? ' required' : '').'" />
					</div>
					<div class="form-field hidden">
						<label>Title'.(in_array('title', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="title" value="'.($row['title'] ?? '').'" class="input'.(in_array('title', $required) ? ' required' : '').'" />
					</div>
					<div class="form-field hidden">
						<label>Email'.(in_array('email', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="email" value="'.($row['email'] ?? '').'" class="input'.(in_array('email', $required) ? ' required' : '').'" />
					</div>
					<div class="form-field hidden">
						<label>Phone'.(in_array('phone', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="phone" value="'.($row['phone'] ?? '').'" class="input'.(in_array('phone', $required) ? ' required' : '').'" />
					</div>
				</div>
			</div>
		</div>'; //Author details
		
		//Author image
		echo '<div class="panel hidden">
			<div class="panel-header">'.$record_name.' Image
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">';

				//Upload Image
				if($image){
					echo '<div class="img-holder">
						<button type="button" name="recrop" value="image" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
						<a href="'.$path.$imagedir.'thumbs/'.$image.'" class="light-gallery" target="_blank" title="">
							<img src="'.$path.$imagedir.'thumbs/'.$image.'" alt="" />
						</a>
						<input type="checkbox" class="checkbox" name="deleteimage" id="deleteimage" value="1">
						<label for="deleteimage">Delete Current Image</label>
					</div>';
				}

					[$max_W, $max_H] = CMSUploader::max_size('blog_author', 'image');
					echo '<div class="form-field">
						<label>Upload Image'.(in_array('image', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Upload Image', 'Image must be smaller than '.$_max_filesize['megabytes'].' and larger than '.$max_W.' x '.$max_H.'.').'</label>
						<input type="file" class="input'.(in_array('image', $required) ? ' required' : '').'" name="image" value="" />
					</div>
					
					<div class="form-field">
						<label>Alt Text <small>(SEO)</small>' .$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.'). '</label>
						<input type="text" name="image_alt" value="' .($row['image_alt'] ?? ''). '" class="input" />
					</div>
					
				</div>
			</div>
		</div>'; //Author image
		
		//Author content
		echo '<div class="panel hidden">
			<div class="panel-header">'.$record_name.' Bio
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">
				<textarea name="content" class="tinymceEditor">'.($row['content'] ?? '').'</textarea>
			</div>
		</div>'; //Author content

		//Sticky footer
		include('includes/widgets/formbuttons.php');
	
		echo '<input type="hidden" name="keep_tags[]" value="content" />
		<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'] .'" />
	</form>';
	
}

?>