<?php

//Config
include("config.php");

//Get vars
$id = (isset($_GET['id']) ? $_GET['id'] : NULL);
$format = (isset($_GET['format']) ? $_GET['format'] : 'html');
$reports = array();
$columns = array();
$genders = array();
$csv_rows = array(
	0 => array(), 
	1 => array(), 
	2 => array()
);

//Get event
$event = $Registration->get_occurrence($id);
if(empty($event)){
	header('Location: '.$_sitepages['tournaments']['page_url']);
	exit();
}else{
		
	//Check report permissions
	if((USER_LOGGED_IN && $Account->account_has_role(1)) || (isset($event['report_results']) && $event['report_results'])){
		
		//Get tournament field size
		$query = $db->query("SELECT `attendee_id` FROM `reg_attendees` WHERE `occurrence_id` = ? && `reg_status` = ? && `partner_id` IS NULL", array($id, 'Registered'));
		$field_size = $db->num_rows();
		
		//Get tournament field
		$records_arr = array(
			'individual' => array(),
			'team_gross' => array(),
			'team_net' => array(),
			'skins' => array(1=>array(), 2=>array())
		);
		$query = $db->query("SELECT `reg_tournament_field`.*, `reg_tournament_field`.`attendee_id` AS `draw_id`, `reg_tournament_field`.`gender` AS `sort_gender`, (IFNULL(`reg_tournament_field`.`r1_score`, 0)+IFNULL(`reg_tournament_field`.`r2_score`, 0)) AS `final_score`, (IFNULL(`reg_tournament_field`.`r1_team_gross`, 0)+IFNULL(`reg_tournament_field`.`r2_team_gross`, 0)) AS `team_gross`, (IFNULL(`reg_tournament_field`.`r1_team_net`, 0)+IFNULL(`reg_tournament_field`.`r2_team_net`, 0)) AS `team_net`, `reg_attendees`.*, `reg_attendees`.`attendee_id` AS `attendee_id`, `facilities`.`facility_name` FROM `reg_attendees` ".
		"LEFT JOIN `reg_tournament_field` ON `reg_attendees`.`attendee_id` = `reg_tournament_field`.`attendee_id` ".
		"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"WHERE `reg_attendees`.`occurrence_id` = ? && `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NULL ".
		"ORDER BY `final_score` ASC, `reg_tournament_field`.`prize` DESC, `reg_attendees`.`last_name` ASC, `reg_attendees`.`first_name` ASC", 
		array($id, 'Registered'));
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				$row['handicap'] = (!empty($row['handicap']) ? $row['handicap'] : 0);
				$row['partner'] = array();
				
				if(!is_null($row['r1_score'])){
					$reports[] = 'individual';
					if($row['final_score'] > 0){
						$records_arr['individual'][$row['attendee_id']] = $row;
					}
				}
				if(!empty($row['r1_score']) && !empty($row['r2_score'])){
					$columns[] = 'r1_score';
					$columns[] = 'r2_score';
				}
				if(!is_null($row['r1_team_gross'])){
					$reports[] = 'team_gross';
					if($row['team_gross'] > 0){
						$records_arr['team_gross'][$row['attendee_id']] = $row;
					}
				}
				if(!is_null($row['r1_team_net'])){
					$reports[] = 'team_net';
					if($row['team_net'] > 0){
						$records_arr['team_net'][$row['attendee_id']] = $row;
					}
				}
				if(!empty($row['target_score'])){
					if(!empty($row['r1_score'])){
						$columns[] = 'r1_score';
					}
					if(!empty($row['r2_score'])){
						$columns[] = 'r2_score';
					}
					$columns[] = 'target_score';
				}
				if(!empty($row['prize'])){
					$columns[] = 'prize';
				}
				if(!empty($row['team_prize'])){
					$columns[] = 'team_prize';
				}
				if(!empty($row['gc_prize']) && $event['category_id'] != 8){
					$columns[] = 'gc_prize';
				}
				if(!empty($row['team_gc_prize'])){
					$columns[] = 'team_gc_prize';
				}
				if(!empty($row['champion'])){
					$columns[] = 'champion';
				}
				if(!empty($row['playoff'])){
					$columns[] = 'playoff';
				}
				
				//Junior Masters
				if(!empty($row['flight'])){
					$columns[] = 'flight';
				}
				if(!empty($row['sort_gender'])){
					$genders[$row['sort_gender']] = ($row['sort_gender'] == 'Male' ? 'Boys' : 'Girls');
				}
				
				//Skins
				if(!empty($row['r1_skins']) || !empty($row['r2_skins'])){
					$reports[] = 'skins';
					
					//Round 1
					if(!empty($row['r1_skins'])){
						$r1_skins = explode(',', $row['r1_skins']);
						foreach($r1_skins as $hole){
							$hole = str_replace('#', '', trim($hole));
							if($hole != ""){
								$records_arr['skins'][1][$hole] = '#'.$hole.' '.$row['last_name'].', '.$row['first_name'];
							}
						}
					}
					
					//Round 2
					if(!empty($row['r2_skins'])){
						$r2_skins = explode(',', $row['r2_skins']);
						foreach($r2_skins as $hole){
							$hole = str_replace('#', '', trim($hole));
							if($hole != ""){
								$records_arr['skins'][2][$hole] = '#'.$hole.' '.$row['last_name'].', '.$row['first_name'];
							}
						}
					}
				}
				
			}
		}
		
		//Partners
		if($event['team_event']){
			$query = $db->query("SELECT `reg_attendees`.*, `facilities`.`facility_name` FROM `reg_attendees` ".
			"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
			"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
			"WHERE `reg_attendees`.`occurrence_id` = ? && `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NOT NULL ".
			"ORDER BY `reg_attendees`.`last_name` ASC, `reg_attendees`.`first_name` ASC", 
			array($id, 'Registered'));
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $row){
					$row['handicap'] = (!empty($row['handicap']) ? $row['handicap'] : 0);
					if(array_key_exists($row['partner_id'], $records_arr['team_gross'])){
						$records_arr['team_gross'][$row['partner_id']]['partner'] = $row;
					}
					if(array_key_exists($row['partner_id'], $records_arr['team_net'])){
						$records_arr['team_net'][$row['partner_id']]['partner'] = $row;
					}
				}
			}
		}
		
		//Get tournament sponsors
		$sponsors = $Registration->get_occurrence_sponsors($id);
		
		//Set html
		$header = '<table cellpadding="0" cellspacing="0" width="100%" border="0">
			<tr>
				<td width="120px"><img src="' .$logo. '" width="120px" /></td>
				<td align="center" style="font-size:12px;">
					<strong>' .$event['event_name']. '</strong><br />'.
					(trim($event['facility_name']) != '' ? $event['facility_name'] : 'TBD'). '<br />'.
					format_date_range($event['start_date'], $event['end_date']). '<br />
					Field Size: ' .$field_size. '<br />'.
					(!empty($event['purse']) && $event['purse'] > 0 ? 'Total Purse: $'.number_format($event['purse'], 2) : '').'
				</td>
				<td width="120px" align="right">';
				if(!empty($sponsors)){
					foreach($sponsors as $sponsor){
						if($sponsor['image'] != '' && file_exists('../images/logos/'.$sponsor['image'])){
							$header .= '<img src="' .$imagepath.'logos/'.$sponsor['image']. '" width="80px" style="margin-bottom:10px;" />';	
						}
					}
				}
				$header .= '</td>
			</tr>
		</table><br />';
		
		//Skins
		$subheader = '';
		if(in_array('skins', $reports)){
			$subheader .= '<hr />';
			
			//R1 skins
			if(!empty($records_arr['skins'][1])){
				ksort($records_arr['skins'][1]);
				$subheader .= '<p style="text-align:center;"><strong>' .(!empty($records_arr['skins'][2]) ? 'R1 ' : ''). 'Skins Winners: $' .number_format($event['skins_pot1']/count($records_arr['skins'][1]), 2). '/person</strong><br />(' .implode(', ', $records_arr['skins'][1]). ')</p>';		
			}
			//R2 skins
			if(!empty($records_arr['skins'][2])){
				ksort($records_arr['skins'][2]);
				$subheader .= '<p style="text-align:center;"><strong>' .(!empty($records_arr['skins'][1]) ? 'R2 ' : ''). 'Skins Winners: $' .number_format($event['skins_pot2']/count($records_arr['skins'][2]), 2). '/person</strong><br />(' .implode(', ', $records_arr['skins'][2]). ')</p>';		
			}
			
			$subheader .= '<br />';
		}
		
		//Team Net Score 
		if(in_array('team_net', $reports)){
			
			//Sort by net score
			foreach($records_arr['team_net'] as $key=>$row){
				$net_score[$key] = $row['team_net'];
				$net_money[$key] = $row['team_prize'];
				$net_giftcert[$key] = $row['team_gc_prize'];
				$net_last_name[$key] = $row['last_name'];
				$net_first_name[$key] = $row['first_name'];
			}
			array_multisort($net_score, SORT_ASC, $net_money, SORT_DESC, $net_giftcert, SORT_DESC, $net_last_name, SORT_ASC, $net_first_name, SORT_ASC, $records_arr['team_net']);
			
			//Html
			$html = $header;
			if($subheader != ''){
				$html .= $subheader;
				$subheader = '';
			}
			$html .= '<h3 style="' .$css['heading']. '">Team Net Results</h3>
			<table cellpadding="5" cellspacing="1" width="100%" border="0">
				<tr>
					<th style="' .$css['th']. '">Player</th>
					<th style="' .$css['th']. '">Facility</th>
					<th style="' .$css['th']. '">Partner</th>
					<th style="' .$css['th']. '">Facility</th>
					<th style="' .$css['th']. '" width="80px">Score</th>
					' .(in_array('team_prize', $columns) ? '<th style="' .$css['th']. '" width="80px">Money</th>' : ''). '
					' .(in_array('team_gc_prize', $columns) ? '<th style="' .$css['th']. '" width="80px">Gift Cert</th>' : ''). '
				</tr>';
			
				//CSV
				$csv_rows[1]['title'] = 'Team Net Results';
				$csv_rows[1]['headers'] = array('Player', 'Facility', 'Partner', 'Facility', 'Score');
				if(in_array('team_prize', $columns)){
					$csv_rows[1]['headers'][] = 'Money';
				}
				if(in_array('team_gc_prize', $columns)){
					$csv_rows[1]['headers'][] = 'Gift Cert';
				}
				if(in_array('playoff', $columns)){
					$csv_rows[1]['headers'][] = '';
				}
			
				//Html
				$count = 0;
				foreach($records_arr['team_net'] as $record){
					if((in_array('team_prize', $columns) && !empty($record['team_prize'])) || (!in_array('team_prize', $columns) && !empty($record['team_net']))){
						$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
						$html .= '<tr>
							<td style="' .$css_td. '">' .
								$record['last_name']. ', '.$record['first_name']. 
								(in_array('playoff', $columns) && $record['playoff'] == '1' ? '<br /><small>(Playoff Winner)</small>' : ''). 
							'</td>
							<td style="' .$css_td. '">' .$record['facility_name']. '</td>
							<td style="' .$css_td. '">' .
								(!empty($record['partner']) ? $record['partner']['last_name'].', '.$record['partner']['first_name'] : '').
								(in_array('playoff', $columns) && $record['playoff'] == '1' ? '<br /><small>(Playoff Winner)</small>' : '').
							'</td>
							<td style="' .$css_td. '">' .(!empty($record['partner']) ? $record['partner']['facility_name'] : ''). '</td>
							<td style="' .$css_td. '" align="center">' .$record['team_net']. '</td>
							' .(in_array('team_prize', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['team_prize'], 2). '</td>' : ''). '
							' .(in_array('team_gc_prize', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['team_gc_prize'], 2). '</td>' : ''). '
						</tr>';
						$count++;
						
						//CSV
						$csv_data = array(
							$record['last_name']. ', '.$record['first_name'],
							$record['facility_name'],
							(!empty($record['partner']) ? $record['partner']['last_name'].', '.$record['partner']['first_name'] : ''),
							(!empty($record['partner']) ? $record['partner']['facility_name'] : ''),
							$record['team_net']
						);
						if(in_array('team_prize', $columns)){
							$csv_data[] = '$'.number_format($record['team_prize'], 2);
						}
						if(in_array('team_gc_prize', $columns)){
							$csv_data[] = '$'.number_format($record['team_gc_prize'], 2);
						}
						if(in_array('playoff', $columns)){
							$csv_data[] = ($record['playoff'] == '1' ? '(Playoff Winner)' : '');
						}
						$csv_rows[1]['rows'][] = $csv_data;
					}
				}
			$html .= '</table>';
			
			//$mpdf->AddPage();
			$mpdf->WriteHTML($html, 2);
		}
		
		//Team Gross Score 
		if(in_array('team_gross', $reports)){
			
			//Sort by gross score
			foreach($records_arr['team_gross'] as $key=>$row){
				$gross_score[$key] = $row['team_gross'];
				$gross_money[$key] = $row['team_prize'];
				$gross_giftcert[$key] = $row['team_gc_prize'];
				$gross_last_name[$key] = $row['last_name'];
				$gross_first_name[$key] = $row['first_name'];
			}
			array_multisort($gross_score, SORT_ASC, $gross_money, SORT_DESC, $gross_giftcert, SORT_DESC, $gross_last_name, SORT_ASC, $gross_first_name, SORT_ASC, $records_arr['team_gross']);
			
			//Html
			if(!isset($html)){
				$html = $header;
				if($subheader != ''){
					$html .= $subheader;
					$subheader = '';
				}
			}else{
				$html = '<br /><br />';
			}
			
			$html .= '<h3 style="' .$css['heading']. '">Team Gross Results</h3>
			<table cellpadding="5" cellspacing="1" width="100%" border="0">
				<tr>
					<th style="' .$css['th']. '">Player</th>
					<th style="' .$css['th']. '">Facility</th>
					<th style="' .$css['th']. '">Partner</th>
					<th style="' .$css['th']. '">Facility</th>
					<th style="' .$css['th']. '" width="80px">Score</th>
					' .(in_array('team_prize', $columns) ? '<th style="' .$css['th']. '" width="80px">Money</th>' : ''). '
					' .(in_array('team_gc_prize', $columns) ? '<th style="' .$css['th']. '" width="80px">Gift Cert</th>' : ''). '
				</tr>';
			
				//CSV
				$csv_rows[0]['title'] = 'Team Gross Results';
				$csv_rows[0]['headers'] = array('Player', 'Facility', 'Partner', 'Facility', 'Score');
				if(in_array('team_prize', $columns)){
					$csv_rows[0]['headers'][] = 'Money';
				}
				if(in_array('team_gc_prize', $columns)){
					$csv_rows[0]['headers'][] = 'Gift Cert';
				}
				if(in_array('playoff', $columns)){
					$csv_rows[0]['headers'][] = '';
				}
			
				//Html
				$count = 0;
				foreach($records_arr['team_gross'] as $record){
					if((in_array('team_prize', $columns) && !empty($record['team_prize'])) || (!in_array('team_prize', $columns) && !empty($record['team_gross']))){
						$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
						$html .= '<tr>
							<td style="' .$css_td. '">' .
								$record['last_name']. ', '.$record['first_name']. 
								(in_array('playoff', $columns) && $record['playoff'] == '1' ? '<br /><small>(Playoff Winner)</small>' : ''). 
							'</td>
							<td style="' .$css_td. '">' .$record['facility_name']. '</td>
							<td style="' .$css_td. '">' .
								(!empty($record['partner']) ? $record['partner']['last_name'].', '.$record['partner']['first_name'] : '').
								(in_array('playoff', $columns) && $record['playoff'] == '1' ? '<br /><small>(Playoff Winner)</small>' : '').
							'</td>
							<td style="' .$css_td. '">' .(!empty($record['partner']) ? $record['partner']['facility_name'] : ''). '</td>
							<td style="' .$css_td. '" align="center">' .$record['team_gross']. '</td>
							' .(in_array('team_prize', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['team_prize'], 2). '</td>' : ''). '
							' .(in_array('team_gc_prize', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['team_gc_prize'], 2). '</td>' : ''). '
						</tr>';
						$count++;
						
						//CSV
						$csv_data = array(
							$record['last_name']. ', '.$record['first_name'],
							$record['facility_name'],
							(!empty($record['partner']) ? $record['partner']['last_name'].', '.$record['partner']['first_name'] : ''),
							(!empty($record['partner']) ? $record['partner']['facility_name'] : ''),
							$record['team_gross']
						);
						if(in_array('team_prize', $columns)){
							$csv_data[] = '$'.number_format($record['team_prize'], 2);
						}
						if(in_array('team_gc_prize', $columns)){
							$csv_data[] = '$'.number_format($record['team_gc_prize'], 2);
						}
						if(in_array('playoff', $columns)){
							$csv_data[] = ($record['playoff'] == '1' ? '(Playoff Winner)' : '');
						}
						$csv_rows[0]['rows'][] = $csv_data;
					}
				}
			$html .= '</table>';
			
			//$mpdf->AddPage();
			$mpdf->WriteHTML($html, 2);
			
		}
		
		//Individual Score
		if(in_array('individual', $reports)){
			
			//Sort by gender (if applicable)
			if(empty($genders)){
				$genders['All'] = '';
			}else{
				ksort($genders);
			}
			foreach($genders as $gender=>$genderlabel){
			
				//Html
				if(!isset($html)){
					$html = $header;
					if($subheader != ''){
						$html .= $subheader;
						$subheader = '';
					}
				}else{
					$html = '<br /><br />';
				}
				
				$html .= '<h3 style="' .$css['heading']. '">'.($genderlabel != '' ? $genderlabel : 'Individual').' Results</h3>
				<table cellpadding="5" cellspacing="1" width="100%" border="0">
					<tr>
						<th style="' .$css['th']. '">Player</th>
						<th style="' .$css['th']. '">Facility</th>
						' .(in_array('flight', $columns) ? '<th style="' .$css['th']. '">Flight</th>' : ''). '
						' .(in_array('r1_score', $columns) ? '<th style="' .$css['th']. '" width="80px">R1 Score</th>' : ''). '
						' .(in_array('r2_score', $columns) ? '<th style="' .$css['th']. '" width="80px">R2 Score</th>' : ''). '
						<th style="' .$css['th']. '" width="80px">' .(in_array('r1_score', $columns) || in_array('r2_score', $columns) ? 'Total Score' : 'Score'). '</th>
						' .(in_array('target_score', $columns) ? '<th style="' .$css['th']. '">Target Score Achieved</th>' : ''). '
						' .(in_array('prize', $columns) ? '<th style="' .$css['th']. '" width="80px">Money</th>' : ''). '
						' .(in_array('gc_prize', $columns) ? '<th style="' .$css['th']. '" width="80px">Gift Cert</th>' : ''). '
					</tr>';

					//CSV
					$csv_rows[2]['title'] = ($genderlabel != '' ? $genderlabel : 'Individual').' Results';
					$csv_rows[2]['headers'] = array('Player', 'Facility');
					if(in_array('flight', $columns)){
						$csv_rows[2]['headers'][] = 'Flight';
					}
					if($gender != 'All'){
						$csv_rows[2]['headers'][] = 'Gender';
					}
					if(in_array('r1_score', $columns)){
						$csv_rows[2]['headers'][] = 'R1 Score';
					}
					if(in_array('r2_score', $columns)){
						$csv_rows[2]['headers'][] = 'R2 Score';
					}
					$csv_rows[2]['headers'][] = (in_array('r1_score', $columns) || in_array('r2_score', $columns) ? 'Total Score' : 'Score');
					if(in_array('target_score', $columns)){
						$csv_rows[2]['headers'][] = 'Target Score Achieved';
					}
					if(in_array('prize', $columns)){
						$csv_rows[2]['headers'][] = 'Money';
					}
					if(in_array('gc_prize', $columns)){
						$csv_rows[2]['headers'][] = 'Gift Cert';
					}
					if(in_array('champion', $columns)){
						$csv_rows[2]['headers'][] = '';
					}
					if(in_array('playoff', $columns)){
						$csv_rows[2]['headers'][] = '';
					}

					//Html
					$count = 0;
					foreach($records_arr['individual'] as $record){
						
						//Sort by gender
						if($gender == 'All' ||  ($gender != 'All' && $record['sort_gender'] == $gender)){
						
							if((in_array('prize', $columns) && !empty($record['prize'])) || (!in_array('prize', $columns) && !empty($record['final_score']))){
								$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
								$html .= '<tr>
									<td style="' .$css_td. '">' .
										$record['last_name']. ', '.$record['first_name']. 
										(in_array('champion', $columns) && $record['champion'] == '1' ? '<br /><small>(Overall Champion)</small>' : ''). 
										(in_array('playoff', $columns) && $record['playoff'] == '1' ? '<br /><small>(Playoff Winner)</small>' : ''). 
									'</td>
									<td style="' .$css_td. '">' .$record['facility_name']. '</td>
									' .(in_array('flight', $columns) ? '<td style="' .$css_td. '" align="center">' .$record['flight']. '</td>' : ''). '
									' .(in_array('r1_score', $columns) ? '<td style="' .$css_td. '" align="center">' .$record['r1_score']. '</td>' : ''). '
									' .(in_array('r2_score', $columns) ? '<td style="' .$css_td. '" align="center">' .$record['r2_score']. '</td>' : ''). '
									<td style="' .$css_td. '" align="center">' .$record['final_score']. '</td>
									' .(in_array('target_score', $columns) ? '<td style="' .$css_td. '" align="center">' .$record['target_score']. '</td>' : ''). '
									' .(in_array('prize', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['prize'], 2). '</td>' : ''). '
									' .(in_array('gc_prize', $columns) ? '<td style="' .$css_td. '" align="right">$' .number_format($record['gc_prize'], 2). '</td>' : ''). '
								</tr>';
								$count++;

								//CSV
								$csv_data = array(
									$record['last_name']. ', '.$record['first_name'],
									$record['facility_name']
								);
								if(in_array('flight', $columns)){
									$csv_data[] = $record['flight'];
								}
								if($gender != 'All'){
									$csv_data[] = $record['sort_gender'];
								}
								if(in_array('r1_score', $columns)){
									$csv_data[] = $record['r1_score'];
								}
								if(in_array('r2_score', $columns)){
									$csv_data[] = $record['r2_score'];
								}
								$csv_data[] = $record['final_score'];
								if(in_array('target_score', $columns)){
									$csv_data[] = $record['target_score'];
								}
								if(in_array('prize', $columns)){
									$csv_data[] = '$'.number_format($record['prize'], 2);
								}
								if(in_array('gc_prize', $columns)){
									$csv_data[] = '$'.number_format($record['gc_prize'], 2);
								}
								if(in_array('champion', $columns)){
									$csv_data[] = ($record['champion'] == '1' ? '(Overall Champion)' : '');
								}
								if(in_array('playoff', $columns)){
									$csv_data[] = ($record['playoff'] == '1' ? '(Playoff Winner)' : '');
								}
								$csv_rows[2]['rows'][] = $csv_data;
							}
							
						}
					}
				$html .= '</table>';
				
				//$mpdf->AddPage();
				$mpdf->WriteHTML($html, 2);
				
			}
			
		}
		
		//No results
		if(empty($reports)){
			$html = $header.'<h3 style="' .$css['heading']. '">Results</h3>
			<table cellpadding="5" cellspacing="1" width="100%" border="0">
				<tr>
					<th style="' .$css['th']. '">Player</th>
					<th style="' .$css['th']. '">Facility</th>
					<th style="' .$css['th']. '" width="80px">Score</th>
					<th style="' .$css['th']. '" width="80px">Money</th>
					<th style="' .$css['th']. '" width="80px">Gift Cert</th>
				</tr>
				<tr>
					<td colspan="5">No results found.</td>
				</tr>
			</table>';
			
			$mpdf->WriteHTML($html, 2);
		}
		
		//Generate csv document to download
		if($format == 'csv'){
			
			//Output csv
			header("Pragma: public");
			header("Expires: 0");
			header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
			header("Content-Type: application/force-download");
			header("Content-Type: text/csv");
			header("Content-Type: application/octet-stream");
			header("Content-Type: application/download");;
			header("Content-Disposition: attachment;filename=".$event['page']."-results.csv");
			header("Content-Transfer-Encoding: binary ");

			$fp = fopen('php://output', 'w');
			
			if(empty($reports)){
				fputcsv($fp, array('No results found.'));
				
			}else{
				
				fputcsv($fp, array($event['event_name']));
				fputcsv($fp, array((trim($event['facility_name']) != '' ? $event['facility_name'] : 'TBD')));
				fputcsv($fp, array(format_date_range($event['start_date'], $event['end_date'])));
				fputcsv($fp, array('Field Size: '.$field_size));
				fputcsv($fp, array((!empty($event['purse']) && $event['purse'] > 0 ? 'Total Purse: $'.number_format($event['purse'], 2) : '')));
				fputcsv($fp, array(''));
				
				if(!empty($csv_rows[0])){
					fputcsv($fp, array($csv_rows[0]['title']));
					fputcsv($fp, str_replace("&rsquo;", "'", $csv_rows[0]['headers']));
					fputcsv($fp, array(''));
					foreach($csv_rows[0]['rows'] as $row){
						fputcsv($fp, str_replace("&rsquo;", "'", $row));
					}
					fputcsv($fp, array(''));
				}
				if(!empty($csv_rows[1])){
					fputcsv($fp, array($csv_rows[1]['title']));
					fputcsv($fp, str_replace("&rsquo;", "'", $csv_rows[1]['headers']));
					fputcsv($fp, array(''));
					foreach($csv_rows[1]['rows'] as $row){
						fputcsv($fp, str_replace("&rsquo;", "'", $row));
					}
					fputcsv($fp, array(''));
				}
				if(!empty($csv_rows[2])){
					fputcsv($fp, array($csv_rows[2]['title']));
					fputcsv($fp, str_replace("&rsquo;", "'", $csv_rows[2]['headers']));
					fputcsv($fp, array(''));
					foreach($csv_rows[2]['rows'] as $row){
						fputcsv($fp, str_replace("&rsquo;", "'", $row));
					}
					fputcsv($fp, array(''));
				}
			}
			
			fclose($fp);
			
			
		//Generate pdf and send to browser	
		}else{
			$mpdf->Output($event['event_name'].' Results.pdf','I');
		}
	
		
	//Access denied
	}else{
		header('Location: '.$_sitepages['tournaments']['page_url'].$id.'/');
		exit();
	}
	
}

?>