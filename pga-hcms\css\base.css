@charset "utf-8";
/*
	base.css
	Project: Honeycomb CMS v4.0
*/
/*------ imports ------*/
/* 
	definitions.less
	Project: Honeycomb CMS v4.0
*/
/* 
	mixins.less

*/
/*------ utilities ------*/
/*------ typography ------*/
.light {
  font-weight: 300;
}
.regular {
  font-weight: 400;
}
.medium {
  font-weight: 500;
}
.semibold {
  font-weight: 600;
}
.bold {
  font-weight: 700;
}
.extrabold {
  font-weight: 800;
}
.black {
  font-weight: 900;
}
.uppercase {
  text-transform: uppercase;
}
.capitalize {
  text-transform: capitalize;
}
.strikethrough {
  text-decoration: line-through;
}
.underline {
  text-decoration: underline;
}
/*------ forms ------*/
/*------ display ------*/
.full {
  width: 100%;
}
.half {
  width: 50%;
}
.auto {
  width: auto;
}
.auto-width {
  width: auto !important;
}
.auto-height {
  height: auto !important;
}
.sr-only {
  position: absolute;
  margin: 0;
  padding: 0;
  border: 0;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  background: none;
  overflow: hidden;
}
.f_right {
  float: right;
  display: block;
}
.f_left {
  float: left;
  display: block;
}
.clear {
  display: block;
  clear: both;
}
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}
.right {
  text-align: right;
}
.left {
  text-align: left;
}
.center {
  text-align: center;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.block {
  display: block !important;
}
.inline-block {
  display: inline-block !important;
}
.inline {
  display: inline !important;
}
.hidden {
  display: none !important;
}
@media all and (max-width: 480px) {
  .show-tablet-p {
    display: none !important;
  }
}
@media all and (max-width: 768px) {
  .show-tablet-l {
    display: none !important;
  }
}
@media all and (max-width: 1024px) {
  .show-notebook {
    display: none !important;
  }
}
@media all and (max-width: 1366px) {
  .show-desktop {
    display: none !important;
  }
}
@media all and (max-width: 1920px) {
  .show-widescreen {
    display: none !important;
  }
}
@media all and (min-width: 481px) {
  .hide-tablet-p {
    display: none !important;
  }
}
@media all and (min-width: 769px) {
  .hide-tablet-l {
    display: none !important;
  }
}
@media all and (min-width: 1025px) {
  .hide-notebook {
    display: none !important;
  }
}
@media all and (min-width: 1367px) {
  .hide-desktop {
    display: none !important;
  }
}
@media all and (min-width: 1921px) {
  .hide-widescreen {
    display: none !important;
  }
}
.noborder {
  border: 0 !important;
}
.nobg {
  background: none !important;
}
.nomargin {
  margin: 0 !important;
}
.nomargin-v,
.nomargin-t {
  margin-top: 0 !important;
}
.nomargin-h,
.nomargin-r {
  margin-right: 0 !important;
}
.nomargin-v,
.nomargin-b {
  margin-bottom: 0 !important;
}
.nomargin-h,
.nomargin-l {
  margin-left: 0 !important;
}
.nopadding {
  padding: 0 !important;
}
.nopadding-v,
.nopadding-t {
  padding-top: 0 !important;
}
.nopadding-h,
.nopadding-r {
  padding-right: 0 !important;
}
.nopadding-v,
.nopadding-b {
  padding-bottom: 0 !important;
}
.nopadding-h,
.nopadding-l {
  padding-left: 0 !important;
}
/*------ gradients ------*/
/*------ flexbox ------*/
/*------ columns ------*/
/* For elements within a .multi-column element. Prevents elements from breaking into multiple columns */
/*------ filters ------*/
/*------ transformations ------*/
/*------ animations ------*/
/* 
	forms.less
	Project: Honeycomb CMS v4.0
	
*/
/*------ standard elements ------*/
label {
  display: block;
  height: 20px;
  margin-bottom: 5px;
}
.form-field {
  float: left;
  display: block;
  max-width: 100%;
  margin: 0 10px 0 0;
}
.flex-container .form-field,
.flex-container .img-holder {
  float: none;
}
.input,
.select,
.textarea {
  font-size: 16px;
  font-family: 'Open Sans', sans-serif;
  color: #657788;
  font-weight: 400;
  border-radius: 0;
  border: 1px solid #CCC;
  background-color: #fff;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 4px 8px;
  width: 260px;
  max-width: 100%;
  height: 40px;
  margin-bottom: 20px;
  outline: none;
  -webkit-transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s;
  transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.input:focus,
.select:focus,
.textarea:focus {
  background-color: #53C5EB;
  color: #fff;
  border-color: #003A5E;
}
.input:disabled,
.select:disabled,
.textarea:disabled {
  background-color: #EEE !important;
  color: #999999 !important;
}
.textarea {
  height: 125px;
  resize: none;
}
.select {
  padding-right: 22px;
  background-image: url("../images/angle-down.svg");
  background-position: calc(100% - 6px) calc(50% + 1px);
  background-repeat: no-repeat;
  background-size: 10px auto;
  text-overflow: "";
  line-height: 1.5;
}
.input_sm,
.select_sm {
  width: 128px;
}
.input_lg {
  width: 540px;
}
.select_lg {
  height: 50px;
}
#seo-description.textarea {
  height: 200px;
}
input[type=file] {
  padding: 4px;
  line-height: 30px;
  font-size: 80%;
  color: #999999;
}
.checkbox,
.radio {
  display: none;
}
.checkbox + label,
.radio + label {
  position: relative;
  display: inline-block;
  padding: 1px 10px 1px 26px;
  margin: 0 0 2px;
  cursor: pointer;
  line-height: 20px;
  color: #657788;
}
.checkbox + label.no-text,
.radio + label.no-text {
  padding-left: 10px;
}
.checkbox + label:before,
.radio + label:before,
.radio:checked + label:after {
  position: absolute;
  content: "";
  display: inline-block;
  line-height: 19px;
  left: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #CCC;
  text-align: center;
  color: #53C5EB;
  background: #fff;
}
.checkbox:disabled + label:before,
.radio:disabled + label:before {
  background-color: #EEE;
}
.radio + label:before,
.radio:checked + label:after {
  border-radius: 50%;
}
.radio:checked + label:after {
  transform: scale(0.6);
  -webkit-transform: scale(0.6) rotate(0.02deg);
  -moz-transform: scale(0.6) rotate(0.02deg);
  border-color: transparent;
  background: #53C5EB;
}
.checkbox:checked + label:before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f00c";
  font-size: 14px;
}
.img-holder {
  float: left;
  display: block;
  max-width: 260px;
  min-height: 64px;
  margin: 0 10px 20px 0;
}
.img-holder img {
  display: block;
  width: 100%;
  min-height: 64px;
  border: 1px solid #CCC;
  margin-bottom: 10px;
}
.cropbox {
  border: 1px solid #CCC !important;
}
.mce-tinymce {
  margin-bottom: 10px !important;
}
.mce-tinymce .mce-edit-area {
  padding: 10px;
}
.mce-tinymce .mce-ico.mce-i-flag::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f024";
}
.panel-content.nopadding .mce-tinymce {
  margin: 0 !important;
  border-width: 1px 0 !important;
  overflow: hidden;
}
/*------ tag editor ------*/
.tag-editor-hidden-src {
  position: absolute;
  margin: 0;
  padding: 0;
  border: 0;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  background: none;
  overflow: hidden;
}
ul.tag-editor {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-line-pack: start;
      align-content: flex-start;
  gap: 5px;
  padding: 8px;
  min-height: 40px;
  margin-left: 0;
  margin-right: 0;
  resize: vertical;
  list-style-type: none;
  overflow: auto;
  cursor: text;
}
ul.tag-editor li {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  margin: 0;
  overflow: hidden;
  line-height: 22px;
}
ul.tag-editor input {
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: inherit;
  outline: none;
  cursor: text;
  -webkit-box-shadow: none;
          box-shadow: none;
  background: none;
  font: inherit;
  letter-spacing: inherit;
}
ul.tag-editor .ui-sortable-placeholder {
  visibility: visible !important;
  border: 2px dashed #CCC;
  max-height: 22px;
  padding-right: 12px;
}
ul.tag-editor .tag-editor-tag {
  padding: 0 5px;
  background: #EEE;
  overflow: hidden;
  cursor: pointer;
  white-space: nowrap;
}
ul.tag-editor .tag-editor-tag.active {
  background: none !important;
}
ul.tag-editor .tag-editor-delete {
  width: 1em;
  padding: 0 4px;
  font-size: 16px;
  background: #EEE;
  cursor: pointer;
  text-align: center;
}
ul.tag-editor .tag-editor-delete i:before {
  content: "\00d7";
  display: inline-block;
  line-height: 20px;
  font-style: normal;
}
ul.tag-editor .tag-editor-delete:hover i:before {
  color: #999999;
}
ul.tag-editor .tag-editor-tag.active + .tag-editor-delete,
ul.tag-editor .tag-editor-tag.active + .tag-editor-delete i {
  visibility: hidden;
  cursor: text;
}
ul.tag-editor .tag-editor-spacer,
ul.tag-editor li[style^="width"] {
  display: none;
}
ul.tag-editor.full {
  width: 100%;
}
td .tag-editor {
  min-height: 40px;
  height: auto;
  margin: 0;
}
.ui-dialog ul.tag-editor {
  padding: 4px 8px;
  margin: 0;
}
/*------ buttons ------*/
.button,
a.button,
.button-sm,
a.button-sm {
  position: relative;
  display: inline-block;
  background: #53C5EB;
  border: none;
  color: #fff;
  font-size: 16px;
  padding: 0 15px 0 65px;
  text-decoration: none;
  height: 50px;
  line-height: 50px;
  white-space: nowrap;
  cursor: pointer;
  outline: none;
  font-family: 'Exo', sans-serif;
  text-transform: uppercase;
  font-weight: 800;
  -webkit-transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s, opacity 0.3s ease 0s;
  transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s, opacity 0.3s ease 0s;
}
.button i,
a.button i,
.button-sm i,
a.button-sm i {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  margin: 0;
  background: #25b6e6;
  -webkit-transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s, opacity 0.3s ease 0s;
  transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s, opacity 0.3s ease 0s;
}
.button:hover,
a.button:hover,
.button-sm:hover,
a.button-sm:hover,
.button:focus,
a.button:focus,
.button-sm:focus,
a.button-sm:focus {
  background: #003A5E;
  color: #fff;
}
.button:hover i,
a.button:hover i,
.button-sm:hover i,
a.button-sm:hover i,
.button:focus i,
a.button:focus i,
.button-sm:focus i,
a.button-sm:focus i {
  background: #001E2F;
}
.button:disabled,
a.button:disabled,
.button-sm:disabled,
a.button-sm:disabled,
.button.disabled,
a.button.disabled,
.button-sm.disabled,
a.button-sm.disabled,
.button.loading,
a.button.loading,
.button-sm.loading,
a.button-sm.loading {
  background-color: #CCC !important;
  color: #EEE !important;
  cursor: default !important;
  text-decoration: line-through;
}
.button:disabled i,
a.button:disabled i,
.button-sm:disabled i,
a.button-sm:disabled i,
.button.disabled i,
a.button.disabled i,
.button-sm.disabled i,
a.button-sm.disabled i,
.button.loading i,
a.button.loading i,
.button-sm.loading i,
a.button-sm.loading i {
  background-color: #bfbfbf !important;
  color: #fff !important;
  cursor: default !important;
}
.button.loading,
a.button.loading,
.button-sm.loading,
a.button-sm.loading {
  font-size: 0;
  text-decoration: none;
  pointer-events: none;
}
.button.loading i::before,
a.button.loading i::before,
.button-sm.loading i::before,
a.button-sm.loading i::before {
  display: inline-block;
  font-size: 16px;
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f110";
  -webkit-animation: fa-spin 2s linear infinite;
  animation: fa-spin 2s linear infinite;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-play-state: var(--animation-play-state, running);
          animation-play-state: var(--animation-play-state, running);
}
.button.loading::after,
a.button.loading::after,
.button-sm.loading::after,
a.button-sm.loading::after {
  content: 'Loading...';
  font-size: 16px;
}
.button + .button,
a.button + .button,
.button-sm + .button,
a.button-sm + .button,
.button + .button-sm,
a.button + .button-sm,
.button-sm + .button-sm,
a.button-sm + .button-sm {
  margin-left: 10px;
}
.button-sm,
a.button-sm {
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  background: #53C5EB;
}
.button-sm i,
a.button-sm i {
  position: relative;
  display: inline;
  line-height: inherit;
  margin-right: 15px;
  background: none;
}
.button-sm:hover,
a.button-sm:hover,
.button-sm:focus,
a.button-sm:focus {
  background: #25b6e6;
}
.button-sm:hover i,
a.button-sm:hover i,
.button-sm:focus i,
a.button-sm:focus i {
  background: none;
}
.button-sm:disabled i,
a.button-sm:disabled i,
.button-sm.disabled i,
a.button-sm.disabled i {
  color: #EEE !important;
  cursor: default !important;
  background: none !important;
}
.button-sm.delete i,
a.button-sm.delete i {
  background: #999999;
}
.button-sm.delete:hover i,
a.button-sm.delete:hover i {
  background: #001E2F;
}
.actions-nav .button-sm,
.actions-nav a.button-sm {
  border: 1px solid #53C5EB;
  color: #53C5EB;
  background: transparent;
}
.actions-nav .button-sm:hover,
.actions-nav a.button-sm:hover,
.actions-nav .button-sm:focus,
.actions-nav a.button-sm:focus {
  color: #fff;
  background: #53C5EB;
}
.cancel {
  display: inline-block;
  line-height: 50px;
  padding: 0;
  font-size: 16px;
}
.delete,
a.delete {
  background: #999999;
}
.delete i,
a.delete i {
  background: #CCC;
  color: #666666;
}
.delete:hover,
a.delete:hover,
.delete:focus,
a.delete:focus {
  background: #001E2F;
}
.delete:hover i,
a.delete:hover i,
.delete:focus i,
a.delete:focus i {
  color: #fff;
  background: #003f62;
}
.img-holder {
  position: relative;
}
.img-holder .recrop-button {
  position: absolute;
  top: 1px;
  left: 1px;
  background: rgba(44, 44, 44, 0.7);
  cursor: pointer;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  border: 0;
  padding: 8px;
  font-size: 16px;
  color: #fff;
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.img-holder .recrop-button:hover {
  color: #53C5EB;
  background: #001E2F;
}
/*------ datepicker ------*/
.ui-datepicker {
  width: 260px;
  padding: 0.2em 4px 0 4px;
  display: none;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background: #003A5E;
  -webkit-box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.16);
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.16);
  /* with multiple calendars */
}
.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: 0.2em 0;
  background: #003A5E;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
}
.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em;
}
.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
  position: absolute;
  display: block;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px;
  font-size: 0px;
}
.ui-datepicker .ui-datepicker-prev span::before,
.ui-datepicker .ui-datepicker-next span::before {
  font-size: 16px;
  line-height: 1;
}
.ui-datepicker .ui-datepicker-prev span::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f137";
}
.ui-datepicker .ui-datepicker-next span::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f138";
}
.ui-datepicker .ui-datepicker-prev-hover,
.ui-datepicker .ui-datepicker-next-hover {
  color: #fff;
}
.ui-datepicker .ui-datepicker-prev {
  left: 2px;
}
.ui-datepicker .ui-datepicker-next {
  right: 2px;
}
.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center;
}
.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0;
}
.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
  width: 45%;
}
.ui-datepicker table {
  width: 100%;
  font-size: 0.9em;
  border-collapse: collapse;
  margin: 0 0 0.4em;
}
.ui-datepicker table th {
  padding: 0.7em 0.3em;
  height: auto;
  text-align: center;
  font-weight: bold;
  border: 0;
  background: #EEE;
}
.ui-datepicker table td {
  border: 0;
  padding: 2px;
  background: #fff !important;
}
.ui-datepicker table td span,
.ui-datepicker table td a {
  display: block;
  padding: 0.3em;
  text-align: right;
  text-decoration: none;
  border: 1px solid #EEE;
}
.ui-datepicker table td a.ui-state-active {
  border-color: #53C5EB;
}
.ui-datepicker table td a.ui-state-hover {
  background: #53C5EB;
  color: #fff;
  font-weight: 700;
}
.ui-datepicker table td.ui-datepicker-today a {
  background: #003A5E;
  color: #fff;
  font-weight: 700;
}
.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: 0.7em 0 0 0;
  padding: 0 0.2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}
.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: 0.5em 0.2em 0.4em;
  cursor: pointer;
  padding: 0.2em 0.6em 0.3em 0.6em;
  width: auto;
  overflow: visible;
}
.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
  float: left;
}
.ui-datepicker.ui-datepicker-multi {
  width: auto;
}
.ui-datepicker.ui-datepicker-multi .ui-datepicker-group {
  float: left;
}
.ui-datepicker.ui-datepicker-multi .ui-datepicker-group table {
  width: 98%;
  margin: 0 auto 0.4em;
}
.ui-datepicker.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0;
}
.ui-datepicker.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left;
}
.ui-datepicker .ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%;
}
.ui-datepicker .ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%;
}
.ui-datepicker .ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%;
}
.ui-datepicker .ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0;
}
/* with month and year select */
.ui-datepicker select {
  font-size: 16px;
  font-family: 'Open Sans', sans-serif;
  color: #657788;
  font-weight: 400;
  border-radius: 0;
  border: 1px solid #CCC;
  background-color: #fff;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 4px 8px;
  width: 260px;
  max-width: 100%;
  height: 40px;
  margin-bottom: 20px;
  outline: none;
  -webkit-transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s;
  transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding-right: 22px;
  background-image: url("../images/angle-down.svg");
  background-position: calc(100% - 6px) calc(50% + 1px);
  background-repeat: no-repeat;
  background-size: 10px auto;
  text-overflow: "";
  line-height: 1.5;
  font-size: 14px !important;
  padding-top: 0;
  padding-bottom: 0;
  height: 30px;
  background-position: right -10px;
}
.ui-datepicker select:focus {
  background-color: #53C5EB;
  color: #fff;
  border-color: #003A5E;
}
.ui-datepicker select:disabled {
  background-color: #EEE !important;
  color: #999999 !important;
}
/*------ google map picker ------*/
.gllpLatlonPicker {
  position: relative;
  border: 0;
  padding: 0;
  margin: 0 15px 10px 0;
  max-width: 100%;
  -ms-flex-item-align: start;
      align-self: flex-start;
}
.gllpMap {
  position: relative;
  z-index: 1;
  width: 590px;
  height: 235px;
  max-width: 100%;
  overflow: hidden;
  resize: both;
}
.gllpSearch {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
  border-radius: 2px;
  background: #FFF;
  -webkit-box-shadow: 0px 1px 4px -1px #000000;
          box-shadow: 0px 1px 4px -1px #000000;
  overflow: hidden;
  color: #666;
}
.gllpSearch .input {
  border: 0;
}
.gllpSearch .input::-webkit-input-placeholder {
  color: #999;
}
.gllpSearch .input:-ms-input-placeholder {
  color: #999;
}
.gllpSearch .input::-moz-placeholder {
  color: #999;
}
.gllpSearch .input::-ms-input-placeholder {
  color: #999;
}
.gllpSearch .input::placeholder {
  color: #999;
}
.gllpSearch .input,
.gllpSearch .input:focus {
  background: none;
  color: inherit;
}
.gllpSearch .input:focus::-webkit-input-placeholder {
  color: #666;
}
.gllpSearch .input:focus:-ms-input-placeholder {
  color: #666;
}
.gllpSearch .input:focus::-moz-placeholder {
  color: #666;
}
.gllpSearch .input:focus::-ms-input-placeholder {
  color: #666;
}
.gllpSearch .input:focus::placeholder {
  color: #666;
}
.gllpSearch .button-sm {
  position: relative;
  padding: 0;
  font-size: 18px;
  color: inherit;
  background: #FFF;
}
.gllpSearch .button-sm:hover,
.gllpSearch .button-sm:focus {
  color: #111;
}
.gllpSearch .button-sm::after {
  position: absolute;
  top: 0;
  right: auto;
  bottom: 0;
  left: 0;
  width: 1px;
  height: 30px;
  margin: auto;
  background-color: #e6e6e6;
  content: '';
}
fieldset.gllpLatlonPicker {
  float: left;
  display: block;
}
/*------ alerts ------*/
.success {
  color: #65B561;
}
.error {
  color: #CC0000;
}
.alert {
  color: #e0b400;
}
.required {
  border-color: #CC0000;
  color: #CC0000;
}
.system-alert {
  background: #fff;
  margin: 0 0 20px;
  opacity: 0;
  overflow: hidden;
  -webkit-box-shadow: 0px 0px 4px 0px rgba(102, 102, 102, 0.16);
  box-shadow: 0px 0px 4px 0px rgba(102, 102, 102, 0.16);
}
.system-alert .title {
  padding: 20px 20px 20px 15px;
  text-transform: uppercase;
  color: #fff;
  font-size: 16px;
  font-family: 'Exo', sans-serif;
  font-weight: 800;
}
.system-alert .title i {
  padding-right: 10px;
}
.system-alert .message {
  padding: 20px 15px;
  color: #001E2F;
}
.system-alert.important .panel-toggle {
  color: #fff;
  font-size: 16px;
}
.system-alert.important .title {
  background: #53C5EB;
  color: #fff;
}
.system-alert.important.error .title {
  background: #CC0000;
}
.system-alert.error .title {
  background: #CC0000;
}
.system-alert.success .title {
  background: #65B561;
}
.system-alert.mini {
  position: relative;
  width: 100%;
  color: #fff;
  background: #001E2F;
  opacity: 0.9;
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.system-alert.mini p {
  padding-bottom: 10px;
}
.system-alert.mini .message {
  padding: 10px 15px;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
}
.system-alert.mini.error .title,
.system-alert.mini.success .title {
  padding-bottom: 0;
  background: none;
}
.system-alert.mini.error .title i {
  color: #CC0000;
}
.system-alert.mini.success .title i {
  color: #65B561;
}
#system-mini-alerts {
  position: fixed;
  z-index: 20;
  top: 110px;
  right: 20px;
  width: 260px;
  pointer-events: none;
}
/*------ login table ------*/
#login-wrapper {
  background: url("../images/hexagons.jpg") center no-repeat #000 fixed;
  background-size: cover;
  width: 100%;
  height: 100%;
  min-height: 400px;
}
#login-header {
  padding: 0 0 20px;
  margin: 0 0 25px;
  border-bottom: 1px solid #EEE;
}
#login-header img {
  width: 100%;
}
#login-form {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 360px;
  max-width: 100%;
  margin: 0;
  padding: 25px 25px 40px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background: #fff;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
#login-form .form-field {
  display: block;
  float: none;
  margin: 0 0 20px;
}
#login-form .form-field label {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  font-family: 'Exo', sans-serif;
  font-weight: 800;
  text-transform: uppercase;
}
#login-form .form-field label a {
  display: inline-block;
  margin-left: auto;
  font-family: 'Open Sans', sans-serif;
  text-transform: none;
  font-weight: 400;
}
#login-form .form-field .input {
  width: 100%;
  height: 50px;
}
#login-form .checkbox + label {
  padding-top: 15px;
}
#login-form #login-buttons {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
}
#login-form #login-buttons .button {
  margin-left: auto;
}
#login-form #login-alert {
  background: #ffe6e6;
  color: #CC0000;
  font-weight: 600;
  padding: 5px 10px;
  margin-bottom: 20px;
}
#login-form #login-alert.success {
  background: #ecf6ec;
  color: #65B561;
}
/*------ search form ------*/
#search-form {
  position: relative;
  float: left;
  display: block;
}
#search-form .input {
  float: left;
  display: block;
  height: 50px;
  padding-right: 20px;
}
#search-form .select {
  height: 50px;
}
#search-form .button {
  padding: 0;
  width: 50px;
}
#search-form .button i {
  background: #657788;
}
#search-form .button:hover i,
#search-form .button:focus i {
  background: #001E2F;
}
#search-form #clear-search {
  position: absolute;
  z-index: 1;
  left: 240px;
  line-height: 50px;
}
.multiple-search .input,
.multiple-search .select {
  margin-right: 10px;
}
/*------ social inputs ------*/
.social {
  position: relative;
}
.social .input {
  position: relative;
  z-index: 1;
  padding-left: 50px;
}
.social .fab {
  position: absolute;
  display: block;
  z-index: 2;
  width: 38px;
  height: 38px;
  top: 1px;
  left: 1px;
  background-color: #81d4f0;
  color: #fff;
  font-size: 20px;
  text-align: center;
  line-height: 38px;
}
/*------ img position picker ------*/
.img-position-picker {
  position: relative;
  float: left;
  clear: both;
  margin-bottom: 20px;
}
.img-position-picker img {
  display: block;
  width: 600px;
  max-width: 100%;
}
.img-position-picker.img-picker-small img {
  width: 300px;
}
.img-position-picker .radios-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.img-position-picker .radios-wrapper input[type="radio"] {
  display: none;
}
.img-position-picker .radios-wrapper input[type="radio"] + label {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 33.33333333%;
  height: 33.33333333%;
  margin: 0;
  padding: 0;
  border: 1px solid #fff;
  z-index: 1;
  cursor: pointer;
}
.img-position-picker .radios-wrapper input[type="radio"].left-0 + label {
  left: 0;
}
.img-position-picker .radios-wrapper input[type="radio"].left-3 + label {
  left: 33.33333333%;
}
.img-position-picker .radios-wrapper input[type="radio"].left-6 + label {
  left: 66.66666667%;
}
.img-position-picker .radios-wrapper input[type="radio"].top-0 + label {
  top: 0;
}
.img-position-picker .radios-wrapper input[type="radio"].top-3 + label {
  top: 33.33333333%;
}
.img-position-picker .radios-wrapper input[type="radio"].top-6 + label {
  top: 66.66666667%;
}
.img-position-picker .radios-wrapper input[type="radio"]:checked + label {
  background: rgba(83, 197, 235, 0.75);
}
/*------ 5 star rating input ------*/
.rating-wrapper {
  margin: 0 0 20px;
}
.rating-select {
  display: inline-block !important;
  font-size: 25px;
  padding: 6px 0 9px;
  line-height: 1em;
  vertical-align: top;
  white-space: nowrap;
}
.rating-select input {
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  visibility: hidden;
  opacity: 0;
}
.rating-select label {
  position: relative;
  display: block;
  color: #53C5EB;
  cursor: pointer;
  float: right;
  -webkit-transition: color 0.3s ease 0s;
  transition: color 0.3s ease 0s;
}
.rating-select label:not(:first-of-type) {
  padding: 0 5px 0 0;
}
.rating-select label::before,
.rating-select label::after {
  display: block;
  font-family: "Font Awesome 6 Free";
  font-weight: 400;
  content: "\f005";
}
.rating-select label::before {
  -webkit-transition: opacity 0.1s ease 0s;
  transition: opacity 0.1s ease 0s;
}
.rating-select label::after {
  opacity: 0;
  font-weight: 900;
  -webkit-transition: opacity 0.15s ease 0s;
  transition: opacity 0.15s ease 0s;
  position: absolute;
  top: 0;
  left: 0;
}
.rating-select label:hover,
.rating-select label:hover ~ label {
  color: #003A5E;
}
.rating-select > input:checked ~ label::after {
  opacity: 1;
}
.rating-select > input:checked ~ label::before {
  opacity: 0;
  -webkit-transition: opacity 0.1s ease 0.15s;
  transition: opacity 0.1s ease 0.15s;
}
.rating-select input:disabled + label {
  color: #999999;
}
/*------ actions nav ------*/
.actions-nav {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #CCC;
}
.actions-nav .column,
.actions-nav .flex-column {
  margin-bottom: 20px;
}
.actions-nav .column.right,
.actions-nav .flex-column.right {
  margin-left: auto;
}
.actions-nav .nav-item {
  display: inline-block;
}
.actions-nav .nav-item ~ .nav-item {
  margin-left: 10px;
}
.actions-nav #search-form .input {
  margin-bottom: 0;
}
/*------ multiselects ------*/
.multiselect {
  width: 235px;
  height: 115px;
  margin: 0 0 20px;
  padding: 10px;
  border: 1px solid #CCC;
  background: #fff;
  outline: none;
  border-radius: 0;
  font-size: 16px;
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  line-height: 30px;
  color: #666666;
  text-overflow: "";
  list-style: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-transition: background-color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s;
}
.multiselect .checkbox + label,
.multiselect .radio + label {
  padding-right: 0;
}
.multiselect.multiselect-lg {
  width: 352.5px;
  height: 230px;
}
.multiselect-field {
  display: inline-block;
  margin-right: 10px;
  vertical-align: middle;
}
.multiselect option {
  padding: 4px 8px 4px 4px;
}
.multiselect-arrows {
  display: inline-block;
  margin: 5px 20px 0 10px;
  vertical-align: middle;
}
.multiselect-arrow,
a.multiselect-arrow {
  display: block;
  margin: 0 0 1px 0 !important;
  padding: 0 20px;
}
/*------ dropzone ------*/
.dropzone {
  margin-bottom: 20px;
  border-style: dashed;
  border-color: #53C5EB;
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.dropzone input[type="file"] {
  display: none;
}
.dropzone .dz-message.dz-default {
  margin: 45px 0;
  color: #666666;
  font-size: 16px;
}
.dropzone .dz-message.dz-default small {
  margin: 4px 0 5px;
}
.dropzone.dz-drag-hover,
.dropzone:hover {
  border-color: #003A5E;
  background: #ffffff;
}
.dropzone.dz-drag-hover .button-sm,
.dropzone:hover .button-sm {
  background-color: #53C5EB;
}
.dropzone.dz-drag-hover {
  border-style: solid;
}
.dz-gallery {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  list-style: none;
  margin: -6px;
  padding: 0;
}
.dz-gallery li {
  position: relative;
  margin: 0 6px 12px;
  overflow: hidden;
}
.dz-gallery li .container-checkbox {
  position: absolute;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 0;
  height: 0;
  opacity: 0;
}
.dz-gallery li .container-checkbox + label {
  position: relative;
  display: block;
  width: 150px;
  height: auto;
  min-height: 150px;
  padding: 6px;
  margin-bottom: 0;
  background-color: #EEE;
  cursor: pointer;
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.dz-gallery li .container-checkbox:checked + label {
  background-color: #53C5EB;
}
.dz-gallery li .container-checkbox:not(:checked) + label img {
  -webkit-filter: grayscale(1);
  -moz-filter: grayscale(1);
  -ms-filter: grayscale(1);
  filter: grayscale(1);
}
.dz-gallery li img {
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  max-width: calc(100% - 12px);
  max-height: calc(100% - 12px);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.dz-gallery li .sort-handler {
  position: absolute;
  top: 6px;
  left: 6px;
  padding: 9px;
  background: rgba(0, 30, 47, 0.7);
  opacity: 0;
  cursor: pointer;
  color: #fff;
  text-align: center;
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.dz-gallery li .sort-handler:hover {
  background: #001E2F;
}
.dz-gallery li .sort-handler:active {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}
.dz-gallery li .sort-handler i {
  color: inherit;
}
.dz-gallery li .buttons {
  position: absolute;
  left: 6px;
  right: 6px;
  bottom: 6px;
  background: rgba(0, 30, 47, 0.7);
  opacity: 0;
  text-align: center;
  -webkit-transform: translateY(20px);
  transform: translateY(20px);
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.dz-gallery li .buttons a {
  display: inline-block;
  margin: 5px 0;
  padding: 3px 5px;
  color: #fff;
}
.dz-gallery li .buttons a i {
  font-size: 18px;
}
.dz-gallery li .buttons a:hover,
.dz-gallery li .buttons a:active {
  color: #53C5EB;
}
.dz-gallery li .buttons:hover,
.dz-gallery li .buttons:active {
  background: #333;
}
.dz-gallery li:hover .sort-handler,
.dz-gallery li:active .sort-handler {
  opacity: 1;
}
.dz-gallery li:hover .buttons,
.dz-gallery li:active .buttons {
  opacity: 1;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}
.dz-gallery li.placeholder-item {
  display: block;
  width: 192px;
  border: 1px dashed #ddd;
  background: #EEE;
}
/*------ image/icon picker ------*/
.item-picker {
  --width: 250px;
  --spacing: 6px;
  display: -ms-grid;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--width), 1fr));
  gap: 20px;
  padding-bottom: 10px;
}
.item-picker input[type="checkbox"] {
  position: absolute;
  margin: 0;
  padding: 0;
  border: 0;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  background: none;
  overflow: hidden;
}
.item-picker label {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column;
          flex-flow: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  position: relative;
  height: auto;
  margin: 0;
  padding: var(--spacing);
  border: 1px solid #CCC;
  cursor: pointer;
}
.item-picker label img {
  display: block;
  padding-bottom: 10px;
  width: 100%;
  height: auto;
}
.item-picker label i:not(.handle) {
  display: block;
  font-size: 30px;
  line-height: 90px;
  text-align: center;
  color: #53C5EB;
}
.item-picker label .handle {
  color: #FFF;
  background-color: rgba(0, 30, 47, 0.7);
  opacity: 0;
  font-size: 16px;
  line-height: 30px;
  text-align: center;
  position: absolute;
  top: var(--spacing);
  right: auto;
  bottom: auto;
  left: var(--spacing);
  width: 30px;
  height: 30px;
  margin: auto;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row;
          flex-flow: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.item-picker label .title {
  font-weight: bold;
}
.item-picker label:hover {
  border-color: #53C5EB;
}
.item-picker label:hover .handle {
  opacity: 1;
}
.item-picker :checked + label {
  background-color: #53C5EB;
  border-color: #53C5EB;
}
.item-picker :checked + label,
.item-picker :checked + label small,
.item-picker :checked + label i:not(.handle) {
  color: #FFF;
}
.item-picker .placeholder-item {
  border: 2px dashed #CCC;
}
.item-picker.icons {
  --width: 150px;
  --spacing: 10px;
}
.item-picker.icons label {
  padding: 10px;
}
.item-picker.icons label i:not(.handle) {
  font-size: inherit;
  line-height: inherit;
  padding-bottom: 10px;
}
.item-picker.icons label .handle {
  width: auto;
  height: auto;
  line-height: inherit;
  background: none;
  font-size: inherit;
  opacity: 1;
  color: #CCC;
}
.item-picker.icons :checked + label .handle {
  color: #FFF;
}
/*------ autocomplete ------*/
.ui-autocomplete.ui-menu {
  position: absolute;
  top: 0;
  right: 0;
  bottom: auto;
  left: auto;
  display: block;
  max-height: 200px;
  margin: 0;
  padding: 0;
  border: 1px solid #CCC;
  border-width: 0 1px 1px;
  overflow-y: auto;
  background-color: #FFF;
  list-style: none;
  z-index: 2;
}
.ui-autocomplete.ui-menu li {
  display: block;
}
.ui-autocomplete.ui-menu .ui-menu-item {
  padding: 4px 8px;
  cursor: pointer;
  color: #53C5EB;
  background-color: inherit;
  -webkit-transition: background-color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s;
}
.ui-autocomplete.ui-menu .ui-menu-item:not(:last-child) {
  border-bottom: 1px solid #CCC;
}
.ui-autocomplete.ui-menu .ui-menu-item:hover {
  background-color: #EEE;
}
/*------ iconpicker ------*/
.iconpicker-container {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  width: 260px;
}
.iconpicker-container .input {
  width: auto;
  -webkit-box-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}
.iconpicker-container .iconpicker-component {
  display: block;
  width: 40px;
  height: 40px;
  background-color: #657788;
  color: #FFF;
  line-height: 40px;
  text-align: center;
  -webkit-transition: background-color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s;
}
.iconpicker-container .iconpicker-component i {
  font-style: normal;
}
.iconpicker-container .iconpicker-component i[class=""]:before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f002";
}
.iconpicker-container .iconpicker-component:hover {
  background-color: #003A5E;
}
.iconpicker-popover.popover {
  width: 266px;
}
.iconpicker-popover.popover.bottom > .arrow {
  top: -9px;
  border-bottom-color: #CCC;
  z-index: 2;
}
.iconpicker-popover .popover-content {
  position: relative;
  border: 1px solid #CCC;
  z-index: 1;
}
.iconpicker-popover .iconpicker-search {
  font-size: 16px;
  font-family: 'Open Sans', sans-serif;
  color: #657788;
  font-weight: 400;
  border-radius: 0;
  border: 1px solid #CCC;
  background-color: #fff;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 4px 8px;
  width: 260px;
  max-width: 100%;
  height: 40px;
  margin-bottom: 20px;
  outline: none;
  -webkit-transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s;
  transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
}
.iconpicker-popover .iconpicker-search:focus {
  background-color: #53C5EB;
  color: #fff;
  border-color: #003A5E;
}
.iconpicker-popover .iconpicker-search:disabled {
  background-color: #EEE !important;
  color: #999999 !important;
}
/*------ cropper ------*/
.cropper-wrapper {
  --overlay: rgba(0, 0, 0, 0.6);
  position: relative;
  background: repeating-conic-gradient(#808080 0% 25%, #666 0% 50%) 0% 16px;
  overflow: hidden;
}
.cropper-wrapper img {
  display: block;
  margin: auto;
  max-width: 100%;
}
.cropper-wrapper .cropper-image {
  position: relative;
  height: 50vw;
  max-height: 600px;
}
.cropper-wrapper .cropper-image > img {
  max-height: 100%;
  background-color: #999999;
}
.cropper-wrapper .cropper-image::before {
  font-size: 48px;
  line-height: 1em;
  text-align: center;
  color: #FFF;
  opacity: 0;
  z-index: 1;
  pointer-events: none;
  -webkit-transition: opacity 0.3s ease 0.3s;
  transition: opacity 0.3s ease 0.3s;
  -webkit-animation: fa-spin 2s linear infinite;
  animation: fa-spin 2s linear infinite;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-play-state: var(--animation-play-state, running);
          animation-play-state: var(--animation-play-state, running);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 1em;
  height: 1em;
  margin: auto;
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f110";
}
.cropper-wrapper .cropper-alert {
  position: absolute;
  top: 0;
  right: auto;
  bottom: auto;
  left: auto;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -ms-flex-flow: nowrap;
      flex-flow: nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  gap: 10px;
  padding: 8px 12px;
  color: #FFF;
  background-color: var(--overlay);
  font-weight: 700;
  opacity: 0;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity 0.3s ease 0s;
  transition: opacity 0.3s ease 0s;
}
.cropper-wrapper .cropper-alert.active {
  opacity: 1;
}
.cropper-wrapper .cropper-alert a {
  pointer-events: auto;
}
.cropper-wrapper .cropper-alert a:hover {
  color: #c5ecf8;
}
.cropper-wrapper .cropper-controls {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  position: relative;
  background-color: #FFF;
  gap: 10px;
  padding: 10px;
}
.cropper-wrapper .cropper-controls .control {
  margin: 0;
}
.cropper-wrapper .cropper-controls .cropper-zoom {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -ms-flex-flow: nowrap;
      flex-flow: nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 20px;
  padding: 0px 15px;
  font-family: 'Exo', sans-serif;
  font-weight: 800;
  text-transform: uppercase;
  background-color: #53C5EB;
  color: #FFF;
  pointer-events: auto;
  font-size: 16px;
}
.cropper-wrapper .cropper-controls .cropper-zoom span {
  margin-top: 2px;
}
.cropper-wrapper .cropper-controls input[type="range"] {
  width: 100%;
  background: none;
  color: inherit;
  --height: 14px;
  --track-height: 2px;
  height: var(--height);
}
.cropper-wrapper .cropper-controls input[type="range"],
.cropper-wrapper .cropper-controls input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.cropper-wrapper .cropper-controls input[type="range"]::-webkit-slider-runnable-track {
  height: var(--height);
}
.cropper-wrapper .cropper-controls input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: var(--height);
  width: var(--height);
  border-radius: 50%;
  border: 0;
  background-color: currentColor;
  cursor: pointer;
  margin-top: calc(var(--height)/-2 + var(--track-height)/2);
}
.cropper-wrapper .cropper-controls input[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: var(--track-height);
  border: 0;
  background-color: currentColor;
}
.cropper-wrapper .cropper-controls input[type="range"]::-moz-range-thumb {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: var(--height);
  width: var(--height);
  border-radius: 50%;
  border: 0;
  background-color: currentColor;
  cursor: pointer;
  margin-top: calc(var(--height)/-2 + var(--track-height)/2);
}
.cropper-wrapper .cropper-controls input[type="range"]::-moz-range-track {
  width: 100%;
  height: var(--track-height);
  border: 0;
  background-color: currentColor;
}
.cropper-wrapper .cropper-canvas,
.cropper-wrapper .cropper-wrap-box {
  overflow: visible;
}
.cropper-wrapper .cropper-point,
.cropper-wrapper .cropper-line {
  opacity: 1;
}
.cropper-wrapper .cropper-point {
  background-color: #FFF;
}
.cropper-wrapper .cropper-line {
  background-color: #98dcf3;
}
.cropper-wrapper .line-n,
.cropper-wrapper .line-s {
  height: 1px;
}
.cropper-wrapper .line-e,
.cropper-wrapper .line-w {
  width: 1px;
}
.cropper-wrapper .line-n {
  top: -1px;
}
.cropper-wrapper .line-e {
  right: -1px;
}
.cropper-wrapper .line-s {
  bottom: -1px;
}
.cropper-wrapper .line-w {
  left: -1px;
}
.cropper-wrapper:not(.cropper-init) .cropper-image::before {
  opacity: 1;
}
.cropper-wrapper:not(.cropper-init) .cropper-image::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  content: '';
}
#partner-picker + .thumbnails {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  text-align: center;
}
#partner-picker + .thumbnails li {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-flex: 1 0 auto;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  min-width: 200px;
}
#partner-picker + .thumbnails li .thumbnail {
  -webkit-box-flex: 1 0 auto;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
}
#partner-picker + .thumbnails img {
  display: block;
  width: auto;
  margin: auto;
  max-width: 200px;
  max-height: 100px;
}
/* 
	regstyles.css
	Project: PACMS v3.0
*/
/*------ regsystem styles ------*/
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.block {
  display: block;
}
.inlineblock {
  display: inline-block;
}
.v_top {
  vertical-align: top;
}
.v_middle {
  vertical-align: middle;
}
.v_bottom {
  vertical-align: bottom;
}
.text-underlined {
  text-decoration: underline;
}
.text-caps {
  text-transform: uppercase;
}
label.inlineblock {
  display: inline-block;
}
/*------ listings ------*/
#listings-extra-buttons .button {
  margin-left: 10px;
  margin-bottom: 20px;
}
/*------ search ------*/
#search-form .select {
  height: 50px;
  margin-bottom: 20px;
  background-position-y: 0;
}
#search-form.multiple-search .input,
#search-form.multiple-search .select {
  margin-right: 10px;
}
#advanced-search-form #search-fields {
  max-width: 1090px;
}
#advanced-search-form .buttons-wrapper {
  margin-top: 20px;
  width: 100%;
  float: left;
  display: block;
  clear: both;
}
#advanced-search-form .column {
  float: left;
}
#advanced-search-form h3 {
  font-weight: 600;
}
.panel-content.autoscroll {
  overflow: auto;
}
.reset,
a.reset {
  background: #999999;
}
.reset i,
a.reset i {
  background: #CCC;
  color: #666666;
}
.reset:hover,
a.reset:hover,
.reset:focus,
a.reset:focus {
  background: #001E2F;
}
.reset:hover i,
a.reset:hover i,
.reset:focus i,
a.reset:focus i {
  color: #fff;
  background: #003f62;
}
.reset i,
a.reset i {
  background: #CCC;
  color: #666666;
}
.reset:hover,
a.reset:hover {
  background: #001E2F;
}
.reset:hover i,
a.reset:hover i {
  color: #fff;
  background: #003f62;
}
.scrollbox {
  display: block;
  max-height: 220px;
  width: 260px;
  overflow-y: scroll;
  padding: 10px;
  border: 1px solid #CCC;
}
/*------ showhide toggle ------*/
.panel-switch-lg {
  margin-bottom: 20px;
}
.panel-switch-lg label:first-child {
  display: block;
  float: left;
  height: auto;
  padding: 0 10px 0 0;
  line-height: 40px;
  text-transform: uppercase;
  font-weight: 800;
}
.panel-switch-lg .onoffswitch {
  width: 100px;
}
.panel-switch-lg .onoffswitch .inner:before,
.panel-switch-lg .onoffswitch .inner:after {
  height: 40px;
  line-height: 40px;
}
.panel-switch-lg .onoffswitch .inner:before {
  padding-left: 19px;
}
.panel-switch-lg .onoffswitch .inner:after {
  padding-right: 19px;
}
.panel-switch-lg .onoffswitch .switch {
  width: 50px;
  right: 50px;
}
/*------ footer ------*/
#cms-footer .button,
.tablesorter-save-order .button {
  margin-left: 10px;
}
/*------ rows ------*/
.instructions-text {
  padding: 20px 20px 0;
}
.rows-wrapper .row {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #EEE;
}
.rows-wrapper .row:nth-child(2n) {
  background: #EEE;
}
.remove-row-btn {
  margin-bottom: 10px;
}
.add-row-btn {
  display: inline-block;
  margin: 20px;
}
/*------ notifications ------*/
.pre-notification-panel label {
  min-width: 200px;
}
.post-notification-panel label {
  min-width: 250px;
}
.notification-days-wrapper label {
  margin-top: 10px;
}
.notification-days-wrapper a {
  display: inline-block;
  margin-bottom: 20px;
}
/*------ hierachry ------*/
.hierarchy-table tr.push td.show-lvl:before {
  content: "\f3bf";
  display: inline-block;
  margin-right: 10px;
  font-family: "Font Awesome 5 Free";
  font-size: 12px;
  color: #53C5EB;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
.hierarchy-table tr.push.lvl2 td.show-lvl {
  padding-left: 25px;
}
.hierarchy-table tr.push.lvl3 td.show-lvl {
  padding-left: 50px;
}
.hierarchy-table tr.push.lvl4 td.show-lvl {
  padding-left: 75px;
}
.hierarchy-table tr.push.lvl5 td.show-lvl {
  padding-left: 100px;
}
/*------ steps nav ------*/
.steps-nav {
  margin-bottom: 19px;
}
.steps-nav ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.steps-nav ul li {
  display: inline-block;
  padding-right: 1px;
  padding-bottom: 1px;
}
.steps-nav ul li:last-child {
  padding-right: 0;
}
.steps-nav ul li a {
  display: inline-block;
  position: relative;
  padding: 15px;
  background: #666666;
  text-align: center;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  text-decoration: none;
  white-space: nowrap;
}
.steps-nav ul li a:hover,
.steps-nav ul li a.active {
  background: #53C5EB;
}
.steps-nav ul li a:hover small,
.steps-nav ul li a.active small {
  color: #003A5E;
}
.steps-nav ul li a.disabled {
  background: #CCC;
  color: #EEE;
  cursor: not-allowed;
}
.steps-nav ul li a.disabled small {
  color: #EEE;
}
/*------ qr code ------*/
.generate-qr-code {
  margin-bottom: 20px;
}
/*------ multiselect ------*/
.multiselect-field {
  margin-right: 10px;
}
.multiselect {
  width: 260px;
  height: 115px;
  margin-bottom: 20px;
  padding: 0;
  border: 1px solid #CCC;
  border-radius: 0;
  background: #fff;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  text-overflow: "";
  font-size: 16px;
  line-height: 30px;
  font-family: 'Open Sans', sans-serif;
  color: #666666;
  font-weight: 400;
}
.multiselect option {
  padding: 4px 8px 4px 4px;
}
.multiselect-arrows {
  margin: 5px 20px 0 10px;
}
.multiselect-arrow,
a.multiselect-arrow {
  display: block;
  margin: 0 0 1px 0;
  padding: 0 20px;
}
/*------ tag editor ------*/
.tag-editor {
  font-size: 16px;
  font-family: 'Open Sans', sans-serif;
  color: #657788;
  font-weight: 400;
  border-radius: 0;
  border: 1px solid #CCC;
  background-color: #fff;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 4px 8px;
  width: 260px;
  max-width: 100%;
  height: 40px;
  margin-bottom: 20px;
  outline: none;
  -webkit-transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s;
  transition: color 0.3s ease 0s, background-color 0.3s ease 0s, border-color 0.3s ease 0s;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: 125px;
  resize: none;
  list-style-type: none;
  margin: 0;
  overflow: auto;
  cursor: text;
}
.tag-editor:focus {
  background-color: #53C5EB;
  color: #fff;
  border-color: #003A5E;
}
.tag-editor:disabled {
  background-color: #EEE !important;
  color: #999999 !important;
}
.tag-editor li {
  position: relative;
  display: inline-block;
  margin: 3px 7px 0 0;
  overflow: hidden;
  line-height: 1.5;
}
.tag-editor li[style="width:1px"] {
  position: absolute;
  margin-right: 0;
}
.tag-editor div {
  float: left;
  padding: 0 4px;
}
.tag-editor input {
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: inherit;
  outline: none;
  cursor: text;
  -webkit-box-shadow: none;
          box-shadow: none;
  background: none;
  font-family: inherit;
  font-weight: inherit;
  font-size: inherit;
  font-style: inherit;
}
.tag-editor .placeholder {
  padding: 0 8px;
  color: #001E2F;
}
.tag-editor .tag-editor-spacer {
  display: none;
}
.tag-editor .tag-editor-tag {
  padding-left: 5px;
  padding-right: 25px;
  background: #EEE;
  overflow: hidden;
  cursor: pointer;
  white-space: nowrap;
}
.tag-editor .tag-editor-tag.active {
  background: none !important;
}
.tag-editor .tag-editor-tag.active + .tag-editor-delete,
.tag-editor .tag-editor-tag.active + .tag-editor-delete i {
  visibility: hidden;
  cursor: text;
}
.tag-editor .tag-editor-delete {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 17px;
  background: #EEE;
  cursor: pointer;
  text-align: center;
}
.tag-editor .tag-editor-delete i:before {
  content: "\00d7";
  display: inline-block;
  line-height: 20px;
  font-size: 16px;
  font-style: normal;
}
.tag-editor .tag-editor-delete:hover i:before {
  color: #999999;
}
.tag-editor-hidden-src {
  position: absolute !important;
  left: -99999px;
}
.tag-editor ::-ms-clear {
  display: none;
}
/*------ gallery ------*/
.gallery-img {
  display: inline-block;
  width: 160px;
  height: 160px;
  margin: 0 10px 10px 0;
  border: 1px solid #CCC;
  overflow: hidden;
}
/*------ attendee info fields ------*/
.attendee-fields-wrapper .row .select + .radio + label {
  margin-left: 20px;
}
#occ_extra_buttons {
  margin-bottom: 15px;
}
#occ_extra_buttons .button-sm {
  margin-right: 10px;
  background: #53C5EB;
}
#occ_extra_buttons .button-sm:hover {
  background: #003A5E;
}
/*------ addons and options ------*/
#addons-wrapper .addon-options {
  padding-top: 20px;
  padding-left: 50px;
  border-bottom: 1px solid #CCC;
  background: url('../images/gradient_10_down.png') repeat-x #EEE;
}
.add-option-btn {
  margin: 0 0 10px 0;
}
/*------ restrictions ------*/
.restriction-template {
  display: block;
  clear: both;
  width: 100%;
}
#add-restriction-btn {
  clear: both;
  float: left;
  display: block;
}
/*------ autocomplete ------*/
.ui-front {
  position: relative;
  z-index: 100;
}
.ui-helper-hidden-accessible {
  display: none;
}
.ui-autocomplete.ui-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  max-width: 256px;
  max-height: 160px;
  border: 1px solid #CCC;
  overflow-y: auto;
  position: absolute !important;
}
.ui-autocomplete.ui-menu li {
  display: block;
}
.ui-autocomplete.ui-menu li .ui-menu-item-wrapper {
  background: #fff;
  display: block;
  padding: 4px 8px;
  cursor: pointer;
}
.ui-autocomplete.ui-menu li .ui-menu-item-wrapper:hover {
  background: #CCC;
}
/*------ add attendees ------*/
#add-basic-attendee {
  margin-top: 25px;
}
#attendee_list td {
  vertical-align: top;
}
#attendee_list .push td {
  border-bottom: 1px solid #CCC;
}
#attendee_list .push td:first-child {
  padding-left: 90px;
}
#attendee_list .extra-field {
  display: inline-block;
  padding-right: 20px;
}
#attendee_list .toggle-extra-fields {
  margin-top: 13px;
  color: #53C5EB;
  cursor: pointer;
}
#attendee_list .toggle-extra-fields:hover {
  color: #003A5E;
}
/*------ registration ------*/
#registration-totals #ordertotal td {
  text-transform: uppercase;
  font-weight: 800;
  font-size: 18px;
}
/*------ attendee stats ------*/
#attendee-charts .span_3 {
  width: 33.33%;
}
.donut-chart {
  width: 100%;
  height: 200px;
}
#chart-attendee-locations {
  max-width: 100% !important;
  height: 200px;
  padding-bottom: 20px;
  overflow-y: hidden;
  overflow-x: auto;
}
/*------ reset ------*/
* {
  margin: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
body,
html {
  width: 100%;
  height: 100%;
  background: #EEE;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
img {
  border: 0;
  -ms-interpolation-mode: bicubic;
}
p {
  padding: 0 0 20px 0;
  margin: 0;
}
ul,
ol {
  padding: 0 0 20px 0;
  margin: 0 0 0 40px;
}
hr {
  border: 0;
  border-top: 1px solid #CCC;
  height: 0px;
  background: #FFF;
  padding: 0;
  margin: 0 0 20px 0;
}
/*------ typography ------*/
body,
tr,
td {
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: #001E2F;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.02em;
}
a {
  text-decoration: none;
  cursor: pointer;
  outline: none;
  color: #53C5EB;
  -webkit-transition: color 0.3s ease 0s, background-color 0.3s ease 0s;
  transition: color 0.3s ease 0s, background-color 0.3s ease 0s;
}
a:hover,
a:focus {
  color: #003A5E;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Exo', sans-serif;
  padding: 0;
  margin: 0;
  font-weight: 500;
}
h1 {
  font-size: 24px;
  line-height: 1.2;
}
h2 {
  font-size: 18px;
  margin-bottom: 20px;
  font-weight: 600;
}
h3 {
  font-size: 16px;
  margin-bottom: 15px;
}
h4 {
  font-size: 14px;
  margin-bottom: 10px;
  letter-spacing: 0;
}
h5 {
  font-size: 13px;
  margin-bottom: 5px;
  letter-spacing: 0;
}
h6 {
  font-size: 12px;
  margin-bottom: 0px;
  letter-spacing: 0;
}
small {
  font-size: 80%;
  color: #657788;
}
.color-theme1 {
  color: #53C5EB;
}
.color-theme2 {
  color: #003A5E;
}
.color-theme3 {
  color: #657788;
}
.color-grey {
  color: #999999;
}
.color-darkest {
  color: #001E2F;
}
.color-error {
  color: #CC0000;
}
.fas {
  font-weight: 900 !important;
}
.far {
  font-weight: 400 !important;
}
.fab {
  font-weight: 400 !important;
}
.medium {
  font-weight: 500;
}
.extrabold {
  font-weight: 800;
}
.bolder {
  font-weight: 900;
}
.text-underlined {
  text-decoration: underline;
}
.text-caps {
  text-transform: uppercase;
}
.truncate {
  display: block;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}
.truncate.rtl {
  direction: rtl;
}
/*------ cms mixins ------*/
.flex-container {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  gap: 10px;
  width: 100%;
}
.flex-container.no-wrap {
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}
.flex-container.align-start {
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
.flex-container.align-center {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.flex-container.align-end {
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
.flex-container.justify-start {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}
.flex-container.justify-center {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.flex-container.justify-space {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.flex-container.justify-end {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.flex-container .flex-column {
  max-width: 100%;
}
.flex-container .flex-column.no-shrink {
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.flex-container .flex-column.grow {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
.flex-container .flex-column.align-start {
  -ms-flex-item-align: start;
      align-self: flex-start;
}
.flex-container .flex-column.align-center {
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
}
.flex-container .flex-column.align-end {
  -ms-flex-item-align: end;
      align-self: flex-end;
}
.flex-container .flex-column.center {
  margin-right: auto;
}
.flex-container .flex-column.center,
.flex-container .flex-column.right {
  margin-left: auto;
}
.input-button {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  width: 260px;
  max-width: 100%;
}
.input-button .input {
  display: block;
  margin: 0;
  height: auto;
  -webkit-box-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}
.input-button .button-sm,
.input-button .button {
  margin: 0;
  padding: 0;
  text-align: center;
  -webkit-box-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
.input-button .button {
  width: 50px;
}
.input-button .button-sm {
  width: 40px;
}
.input-button .button::empty::before,
.input-button .button-sm::empty::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f002";
}
.input-button.large {
  width: 320px;
}
.gravatar-link {
  position: relative;
  display: block;
}
.gravatar-link .tooltip {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0 !important;
  right: 0;
  width: 40px !important;
  height: 40px !important;
  margin: auto;
  padding: 0;
  background: rgba(179, 0, 0, 0.75);
  color: #FFF;
  text-align: center;
  line-height: 40px;
}
.gravatar {
  display: block;
  width: 40px;
  height: 40px;
  border: 1px solid #CCC;
  overflow: hidden;
}
/*------ interface ------*/
#cms-wrapper {
  position: relative;
  width: 100%;
  min-height: 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding-left: 20px;
}
#section-title {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  -webkit-transition: left 0.3s ease-out 0s;
  transition: left 0.3s ease-out 0s;
  z-index: 100;
  top: 0;
  left: 0;
  right: 0;
  background: #FFF;
  padding: 0 20px;
  margin: 0 0 30px -20px;
  height: 85px;
  width: calc(100% + 40px);
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
#section-title h1 {
  -webkit-box-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding-left: 10px;
}
#section-title h1 i {
  padding-right: 15px;
}
#section-title h1 small {
  font-size: 60%;
}
#section-title .back-to-site {
  -webkit-box-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  display: block;
  white-space: nowrap;
  padding-left: 15px;
}
#section-title .back-to-site i {
  padding-left: 5px;
}
#cms-content {
  position: relative;
  z-index: 1;
  padding: 0 20px;
}
#dashboard-overview {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  position: relative;
  width: calc(100% + 20px);
  left: -10px;
}
#dashboard-overview .flex-column {
  margin-left: 10px;
  margin-right: 10px;
}
.cms-overview {
  width: 100%;
  max-width: 400px;
}
.cms-overview table a {
  color: #001E2F;
}
.cms-overview table a:hover {
  color: #53C5EB;
}
.cms-overview table a i {
  width: 18px;
  text-align: center;
}
.dashboard-box {
  width: 200px;
  height: 170px;
  margin: 0 20px 20px 0;
  padding: 20px 0 0 0;
  background: #FFF;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
}
.cms-overview {
  width: 100%;
  max-width: 400px;
}
.cms-overview table a {
  color: #001E2F;
}
.cms-overview table a:hover {
  color: #53C5EB;
}
.cms-overview table a i {
  width: 18px;
  text-align: center;
}
.panel {
  display: block;
  clear: both;
  margin-bottom: 20px;
  background: #FFF;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
}
.panel.f_left,
.panel.f_right {
  clear: none;
}
.panel.f_left {
  margin-right: 20px;
}
.panel.f_right {
  margin-left: 20px;
}
.panel-header {
  position: relative;
  padding: 15px 50px 15px 20px;
  line-height: 30px;
  background: #001E2F;
  color: #FFF;
  font-family: 'Exo', sans-serif;
  font-size: 16px;
  font-weight: 800;
  text-transform: uppercase;
}
.panel-header.required {
  color: #CC0000;
}
.panel-header form {
  line-height: 40px;
}
.panel-header form .input,
.panel-header form .select {
  text-transform: none;
}
.panel-header .panel-toggle {
  position: absolute;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  top: 50%;
  right: 20px;
  color: #FFF;
  cursor: pointer;
  -webkit-transition: color 0.3s ease 0s;
  transition: color 0.3s ease 0s;
}
.panel-header .panel-toggle:hover {
  color: #53C5EB;
}
.panel-header .panel-switch {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  margin: 0;
  float: none;
  clear: both;
  line-height: 10px;
}
.panel-header .panel-switch label:first-child {
  padding: 0 10px 0 0;
  line-height: 30px;
  height: auto;
  margin: 0;
}
.panel-content {
  padding: 15px 15px 5px;
  overflow: hidden;
  overflow-x: auto;
}
.panel-content.collapsed {
  display: none;
}
.panel-content p:only-child {
  padding-bottom: 10px;
}
.panel-content .privacy-settings-container .privacy-setting-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 12px 15px;
}
.panel-content .privacy-settings-container .privacy-setting-row:nth-child(odd) {
  background-color: #EEE;
}
.panel-content .privacy-settings-container .privacy-setting-row:last-child {
  border-bottom: none;
}
.panel-content .privacy-settings-container .privacy-setting-row .privacy-label {
  width: 125px;
  color: #666666;
  font-weight: normal;
}
.panel-content .privacy-settings-container .privacy-setting-row .privacy-radio-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
}
.panel-content .privacy-settings-container .privacy-setting-row .privacy-radio-group input[type="radio"] {
  margin-right: 5px;
}
.panel-content .privacy-settings-container .privacy-setting-row .privacy-radio-group label {
  color: #666666;
  font-weight: normal;
  cursor: pointer;
  margin-right: 10px;
}
.panel-content .privacy-settings-container .privacy-setting-row .privacy-radio-group label:last-child {
  margin-right: 0;
}
.panel-content .history-label-cell {
  width: 200px;
}
#cms-footer,
.tablesorter-save-order {
  position: absolute;
  z-index: 900;
  background: #FFF;
  left: 0;
  right: 0;
  padding: 10px 20px;
  text-align: left;
}
#cms-footer .flex-container,
.tablesorter-save-order .flex-container {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
}
#cms-footer .flex-column,
.tablesorter-save-order .flex-column {
  width: 100%;
}
#cms-footer .flex-column.left,
.tablesorter-save-order .flex-column.left {
  -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
          order: 2;
  min-width: 175px;
}
#cms-footer .flex-column.right,
.tablesorter-save-order .flex-column.right {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}
#cms-footer .delete,
.tablesorter-save-order .delete {
  margin-right: 15px;
}
#cms-footer .button,
#cms-footer .cancel,
.tablesorter-save-order .button,
.tablesorter-save-order .cancel {
  margin-top: 5px;
  margin-bottom: 5px;
}
@media all and (min-width: 481px) {
  #cms-footer .flex-container,
  .tablesorter-save-order .flex-container {
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
            flex-flow: row nowrap;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: stretch;
        -ms-flex-align: stretch;
            align-items: stretch;
  }
  #cms-footer .flex-column,
  .tablesorter-save-order .flex-column {
    width: auto;
  }
  #cms-footer .flex-column.left,
  .tablesorter-save-order .flex-column.left {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
  #cms-footer .flex-column.right,
  .tablesorter-save-order .flex-column.right {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
}
@media all and (min-width: 769px) {
  #cms-wrapper {
    padding-left: 85px;
  }
  #section-title {
    position: fixed;
    z-index: 1000;
    padding: 0 30px;
    width: auto;
    left: 85px;
    right: 0;
    margin: 0;
  }
  #section-title h1 {
    padding: 0;
  }
  #cms-content {
    padding: 115px 30px 95px;
    overflow: hidden;
  }
  .panel-header {
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
            flex-flow: row wrap;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: stretch;
        -ms-flex-align: stretch;
            align-items: stretch;
    gap: 5px;
  }
  .panel-header .panel-switch {
    margin-left: auto;
  }
  .panel-content {
    padding: 20px 20px 10px;
  }
  #cms-footer,
  .tablesorter-save-order {
    position: fixed;
    left: 85px;
    right: 0;
    bottom: 0;
    width: auto;
    padding: 15px 30px;
  }
}
@media all and (min-width: 1025px) {
  #cms-content {
    padding: 125px 30px 105px;
  }
}
/*------ navigation ------*/
#cms-menu {
  display: block;
  position: fixed;
  z-index: 900;
  top: 0;
  bottom: 0;
  left: -280px;
  width: 300px;
  background: #FFF;
  font-family: 'Exo', sans-serif;
  font-weight: 500;
  -webkit-transition: left 0.3s ease-out 0s;
  transition: left 0.3s ease-out 0s;
}
#cms-menu #menu-header {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  right: 0;
  height: 85px;
  padding-left: 75px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background: #001E2F;
}
#cms-menu #menu-header a {
  position: relative;
  z-index: 1;
  display: inline-block;
  margin: 0 20px;
  padding: 10px 0 10px 30px;
  color: #FFF;
}
#cms-menu #menu-header a i {
  position: absolute;
  -webkit-transition: color 0.3s ease 0s;
  transition: color 0.3s ease 0s;
  top: 12px;
  left: 0;
  width: 18px;
  color: #657788;
}
#cms-menu #menu-header a:hover,
#cms-menu #menu-header a.active {
  color: #53C5EB;
}
#cms-menu #menu-header a:hover i,
#cms-menu #menu-header a.active i {
  color: inherit;
}
#cms-menu #menu-header #cms-title {
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  width: 75px;
  height: 85px;
  padding: 15px 10px 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background: #FFF;
  text-align: center;
}
#cms-menu #menu-header #cms-title a {
  display: block;
  margin: 0;
  padding: 0;
  height: 70px;
  border-bottom: 1px solid #EEE;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
#cms-menu #menu-header #cms-title a small {
  display: block;
  color: #999999;
  font-size: 9px;
  line-height: 26px;
  white-space: nowrap;
}
#cms-menu nav#menu-icons {
  position: absolute;
  z-index: 2;
  top: 85px;
  bottom: 0;
  left: 0;
  width: 75px;
  overflow: hidden;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background: #FFF;
}
#cms-menu nav#menu-icons > ul {
  position: absolute;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0;
  padding: 24px 0 0;
  list-style: none;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-align: center;
}
#cms-menu nav#menu-icons > ul > li {
  display: block;
  padding: 0 10px 24px;
}
#cms-menu nav#menu-icons > ul > li a {
  display: block;
  overflow: hidden;
  padding: 0;
  color: #657788;
  font-size: 9px;
  line-height: 1.4;
  text-overflow: ellipsis;
  text-transform: uppercase;
}
#cms-menu nav#menu-icons > ul > li a i {
  display: block;
  margin-bottom: 8px;
  font-size: 20px;
}
#cms-menu nav#menu-icons > ul > li a .initials {
  position: relative;
  display: block;
  width: 24px;
  height: 24px;
  margin: 0 auto 10px;
}
#cms-menu nav#menu-icons > ul > li a .initials span {
  position: relative;
  display: block;
  z-index: 1;
  line-height: 26px;
  color: #FFF;
  text-indent: 1px;
  letter-spacing: 0em;
  font-weight: 900;
}
#cms-menu nav#menu-icons > ul > li a .hexagon {
  position: absolute;
  z-index: 0;
  display: block;
  top: 0;
  left: 0;
  width: 24px;
  height: 14px;
  background: #657788;
  margin: 6px auto 12px;
  -webkit-transition: background 0.3s ease 0s;
  transition: background 0.3s ease 0s;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
#cms-menu nav#menu-icons > ul > li a .hexagon:before,
#cms-menu nav#menu-icons > ul > li a .hexagon:after {
  position: absolute;
  -webkit-transition: border 0.3s ease 0s;
  transition: border 0.3s ease 0s;
  content: '';
  width: 0;
  height: 0;
}
#cms-menu nav#menu-icons > ul > li a .hexagon:before {
  top: -6px;
  left: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 6px solid #657788;
}
#cms-menu nav#menu-icons > ul > li a .hexagon:after {
  bottom: -6px;
  left: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-top: 6px solid #657788;
}
#cms-menu nav#menu-icons > ul > li a:hover,
#cms-menu nav#menu-icons > ul > li a.active {
  color: #53C5EB;
}
#cms-menu nav#menu-icons > ul > li a:hover .hexagon,
#cms-menu nav#menu-icons > ul > li a.active .hexagon {
  background: #53C5EB;
}
#cms-menu nav#menu-icons > ul > li a:hover .hexagon:before,
#cms-menu nav#menu-icons > ul > li a.active .hexagon:before {
  border-bottom: 6px solid #53C5EB;
}
#cms-menu nav#menu-icons > ul > li a:hover .hexagon:after,
#cms-menu nav#menu-icons > ul > li a.active .hexagon:after {
  border-top: 6px solid #53C5EB;
}
#cms-menu nav#menu-icons > ul > li ul {
  display: block;
  list-style: none;
  margin: 0;
  padding: 0;
}
#cms-menu nav#menu-icons > ul > li ul li {
  padding-top: 24px;
}
#cms-menu nav#menu-icons > ul > li:last-child {
  margin-top: auto;
}
#cms-menu nav#menu-icons > ul > li:last-child a {
  padding-top: 24px;
  border-top: 1px solid #EEE;
}
#cms-menu nav#menu-icons > ul > li:last-child a i {
  margin: 6px 0 0;
}
#cms-menu nav#menu-list {
  position: absolute;
  z-index: 1;
  top: 85px;
  bottom: 0;
  left: 75px;
  right: 0;
  overflow: hidden;
  background: #001E2F;
}
#cms-menu nav#menu-list > ul {
  list-style: none;
  margin: 0;
  padding: 10px 0 40px;
}
#cms-menu nav#menu-list > ul li {
  display: block;
  padding: 0 20px;
}
#cms-menu nav#menu-list > ul li a {
  position: relative;
  display: inline-block;
  padding: 10px 0 10px 30px;
  color: #FFF;
  font-size: 14px;
  line-height: 1.2;
}
#cms-menu nav#menu-list > ul li a i {
  position: absolute;
  -webkit-transition: color 0.3s ease 0s;
  transition: color 0.3s ease 0s;
  top: 10px;
  left: 0;
  width: 18px;
  text-align: center;
  color: #657788;
}
#cms-menu nav#menu-list > ul li a:hover,
#cms-menu nav#menu-list > ul li a.active,
#cms-menu nav#menu-list > ul li a.ui-state-active {
  color: #53C5EB;
}
#cms-menu nav#menu-list > ul li a:hover i,
#cms-menu nav#menu-list > ul li a.active i,
#cms-menu nav#menu-list > ul li a.ui-state-active i {
  color: inherit;
}
#cms-menu nav#menu-list > ul li ul {
  list-style: none;
  margin: 0 0 2px;
  padding: 0;
}
#cms-menu nav#menu-list > ul li ul li {
  padding: 0 0 0 8px;
}
#cms-menu nav#menu-list > ul li ul li a {
  padding: 5px 0 5px 20px;
  border-left: 2px solid #657788;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-size: 12px;
  color: #CCC;
}
#cms-menu nav#menu-list > ul li ul li a:hover,
#cms-menu nav#menu-list > ul li ul li a.active {
  color: #FFF;
}
#cms-menu nav#menu-list > ul li.accordion:not(.expanded) ul {
  display: none;
}
#cms-menu nav#menu-list > ul li.menu-header h6 {
  color: #657788;
  margin-bottom: 10px;
  letter-spacing: 0.2em;
  font-weight: 800;
  text-transform: uppercase;
}
#cms-menu nav#menu-list > ul li.menu-header:not(:first-child) {
  padding-top: 30px;
}
#cms-menu nav#menu-list > ul li.expanded > a {
  color: #53C5EB;
}
#cms-menu nav#menu-list > ul li.expanded > a i {
  color: inherit;
}
#menu-toggle {
  position: absolute;
  -webkit-transform: translate(50%, -50%);
  transform: translate(50%, -50%);
  -webkit-transition: background-color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s;
  display: block;
  z-index: 100;
  top: 50%;
  right: 0;
  width: 30px;
  height: 30px;
  margin: 0;
  padding: 8px;
  color: #FFF;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  background-color: #657788;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  line-height: 1;
  cursor: pointer;
}
#menu-toggle:hover {
  background-color: #53C5EB;
}
.menu-simple #cms-menu {
  width: 95px;
  left: -75px;
}
.menu-simple #cms-menu #menu-header a:last-child {
  display: none;
}
.menu-simple #menu-toggle {
  right: 0;
}
.menu-open #cms-menu {
  left: 0;
}
.animated #cms-content {
  -webkit-transition: margin 0.3s ease-out 0s;
  transition: margin 0.3s ease-out 0s;
}
.animated #menu-toggle {
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.animated #cms-footer,
.animated .tablesorter-save-order {
  -webkit-transition: left 0.3s ease-out 0s;
  transition: left 0.3s ease-out 0s;
}
.steps-nav {
  display: block;
  margin-bottom: 20px;
}
.steps-nav ul {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}
.steps-nav ul li {
  padding: 0 1px 0 0;
  text-align: center;
  font-size: 16px;
  min-width: 120px;
}
.steps-nav ul li:last-child {
  padding-right: 0;
}
.steps-nav ul li a {
  position: relative;
  display: block;
  padding: 15px 30px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background: #657788;
  color: #FFF;
  border-top: 2px solid #EEE;
  border-bottom: 2px solid #EEE;
  font-weight: 800;
  text-transform: uppercase;
}
.steps-nav ul li a small {
  display: block;
  color: #CCC;
}
.steps-nav ul li a:hover,
.steps-nav ul li a.active {
  background: #001E2F;
}
.steps-nav ul li a:hover small,
.steps-nav ul li a.active small {
  color: #53C5EB;
}
.steps-nav ul li a.active {
  border-color: #001E2F;
}
.steps-nav ul li a.disabled {
  background: #c9d0d7;
  border-color: #EEE;
  color: #EEE;
  cursor: not-allowed;
}
.steps-nav ul li a.disabled small {
  color: #EEE;
}
@media all and (min-width: 769px) {
  #cms-wrapper {
    padding-left: 85px;
  }
  #menu-toggle {
    right: 5px;
  }
  #cms-menu {
    left: -215px;
  }
  #cms-menu #menu-header #cms-title {
    position: fixed;
  }
  #cms-menu nav#menu-icons {
    position: fixed;
  }
  .menu-open #menu-toggle {
    right: 0;
  }
  .menu-simple #cms-menu {
    width: 85px !important;
    left: 0;
  }
  .menu-simple #menu-toggle {
    display: none;
  }
}
@media all and (min-width: 1025px) {
  .menu-open:not(.menu-simple) #cms-content {
    margin-left: 215px;
  }
  .menu-open:not(.menu-simple) #section-title,
  .menu-open:not(.menu-simple) #cms-footer,
  .menu-open:not(.menu-simple) .tablesorter-save-order {
    left: 300px;
  }
}
/*------ tables ------*/
table {
  position: relative;
  width: 100%;
}
table tr {
  background: #FFF;
}
table tr:nth-child(2n) {
  background: #EEE;
}
table tr.first-child {
  -webkit-box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5) inset;
  box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5) inset;
}
table tr.first-child:nth-child(2n) {
  -webkit-box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.35) inset;
  box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.35) inset;
}
table tr.last-child {
  -webkit-box-shadow: 0 -10px 10px -10px rgba(0, 0, 0, 0.5) inset;
  box-shadow: 0 -10px 10px -10px rgba(0, 0, 0, 0.5) inset;
}
table tr.last-child:nth-child(2n) {
  -webkit-box-shadow: 0 -10px 10px -10px rgba(0, 0, 0, 0.35) inset;
  box-shadow: 0 -10px 10px -10px rgba(0, 0, 0, 0.35) inset;
}
table tr.first-child.last-child {
  -webkit-box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5) inset, 0 -10px 10px -10px rgba(0, 0, 0, 0.5) inset;
  box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5) inset, 0 -10px 10px -10px rgba(0, 0, 0, 0.5) inset;
}
table tr.first-child.last-child:nth-child(2n) {
  -webkit-box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.35) inset, 0 -10px 10px -10px rgba(0, 0, 0, 0.35) inset;
  box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.35) inset, 0 -10px 10px -10px rgba(0, 0, 0, 0.35) inset;
}
table tr.bolder td,
table tr.bold td {
  font-weight: inherit;
}
table th {
  background: #c9d0d7;
  font-family: 'Exo', sans-serif;
  padding: 5px 20px;
  text-align: left;
  height: 60px;
  text-transform: uppercase;
  font-weight: 800;
}
table td {
  padding: 10px 20px;
  -webkit-transition: background-color 0.5s ease 0s;
  transition: background-color 0.5s ease 0s;
}
table tr[data-level] td {
  padding: 15px 20px;
}
table tr[data-level='1']:after {
  left: 0px;
}
table tr[data-level='2']:after {
  left: 30px;
}
table tr[data-level='3']:after {
  left: 60px;
}
table tr[data-level='4']:after {
  left: 90px;
}
table tr[data-level='5']:after {
  left: 120px;
}
table.sortable:not(.tablesorter) tr[data-level] td.show-lvl:before,
table.helper-sortable tr[data-level] td.show-lvl:before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f3bf";
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  display: inline-block;
  margin-right: 10px;
  font-size: 12px;
  color: #53C5EB;
}
table.sortable:not(.tablesorter) tr[data-level='1'] td.show-lvl:before,
table.helper-sortable tr[data-level='1'] td.show-lvl:before {
  content: "";
  margin-right: 0;
}
table.sortable:not(.tablesorter) tr[data-level='2'] td.show-lvl,
table.helper-sortable tr[data-level='2'] td.show-lvl {
  padding-left: 25px;
}
table.sortable:not(.tablesorter) tr[data-level='3'] td.show-lvl,
table.helper-sortable tr[data-level='3'] td.show-lvl {
  padding-left: 50px;
}
table.sortable:not(.tablesorter) tr[data-level='4'] td.show-lvl,
table.helper-sortable tr[data-level='4'] td.show-lvl {
  padding-left: 75px;
}
table.sortable:not(.tablesorter) tr[data-level='5'] td.show-lvl,
table.helper-sortable tr[data-level='5'] td.show-lvl {
  padding-left: 100px;
}
table td.handle {
  color: #999999;
}
table.sortable.saveable tr.higher td.handle,
table.sortable.saveable tr.lower td.handle {
  color: #53C5EB;
}
table .highlight {
  height: 70px;
  position: relative !important;
}
table .highlight:after {
  position: absolute;
  content: "";
  display: inline-block;
  height: inherit;
  left: 0;
  right: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border: 1px dashed #53C5EB;
  z-index: 9;
}
table .highlight:nth-child(n) {
  background-color: #FFF;
}
table .highlight ~ tr:nth-child(2n + 1) {
  background: #eee;
}
table .highlight ~ tr:nth-child(2n) {
  background-color: #FFF;
}
table .highlight td {
  display: none;
}
table .handle {
  position: relative;
}
table .handle:hover {
  cursor: move;
  cursor: -webkit-grab;
  cursor: grab;
}
table .handle:active,
table .handle .ui-sortable-helper {
  cursor: move;
  cursor: -webkit-grabbing;
  cursor: grabbing;
}
.ui-sortable-helper {
  border: 1px solid #c9d0d7;
  -webkit-transform-origin: left;
          transform-origin: left;
  -webkit-transition: -webkit-transform 0.3s;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
  opacity: 0.9;
  background: #FFF !important;
}
.ui-sortable-helper tr[data-level] td {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
table.nowrap,
td.nowrap,
th.nowrap {
  white-space: nowrap;
}
.leadin-field-options-table {
  padding: 10px 0;
}
.leadin-field-options-table tr:nth-child(n) {
  background: transparent;
}
/*------ tablesorter ------*/
.tablesorter .tablesorter-header {
  position: relative;
  cursor: pointer;
  outline: none;
}
.tablesorter .sorter-false {
  cursor: default;
}
.tablesorter .tablesorter-headerAsc,
.tablesorter .tablesorter-headerDesc {
  background: #bac3cc;
}
.tablesorter .tablesorter-headerAsc:after,
.tablesorter .tablesorter-headerDesc:after {
  position: absolute;
  font-size: 16px;
  top: 50%;
  right: 8px;
  height: 20px;
}
.tablesorter .tablesorter-headerAsc:after {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f0d8";
  margin-top: -10px;
}
.tablesorter .tablesorter-headerDesc:after {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f0d7";
  margin-top: -8px;
}
.tablesorter .show,
.tablesorter .hide {
  display: block;
  text-indent: -9999px;
  height: 18px;
}
.tablesorter .show:after,
.tablesorter .hide:after {
  display: block;
  text-align: center;
  font-size: 16px;
  text-indent: 0;
  margin-top: -16px;
}
.tablesorter .show:after {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f00c";
  color: #65B561;
}
.tablesorter .hide:after {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f00d";
  color: #001E2F;
}
.pager {
  display: block;
  position: relative !important;
  top: auto !important;
  padding: 10px 20px;
  background: #FFF;
  border-top: 1px solid #EEE;
  text-align: center;
}
.pager .pagebuttons .button-sm {
  width: 40px;
  text-align: center;
  margin: 0 1px;
  border: 0;
  padding: 0;
}
.pager .pagebuttons .button-sm.hoverable {
  outline-offset: -3px;
  outline: 3px dashed #53C5EB;
  -webkit-transition: background-color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s;
}
.pager .pagebuttons .button-sm.dragged-over {
  background-color: #53C5EB;
  outline-color: #003A5E;
}
.pager .pagebuttons .button-sm.disabled {
  background: #EEE !important;
  color: #FFF !important;
  cursor: default;
  outline: 0;
  text-decoration: none;
}
.pager .pagedisplay {
  display: block;
  padding: 0 0 5px;
}
.pager .pagebuttons {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 240px;
  margin: 0 auto;
}
.pager .gotoPage {
  width: 70px;
  height: 40px;
  margin: 0 1px;
}
.tablesorter-sticky-visible {
  visibility: hidden !important;
}
.tablesorter-save-order {
  display: none;
}
.tablesorter-save-order .flex-column:not(.right) {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.tablesorter-save-order .flex-column.right {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
@media all and (min-width: 769px) {
  .tablesorter-sticky-visible {
    visibility: visible !important;
  }
  .tablesorter-save-order .flex-column.right {
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
            flex-flow: row nowrap;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .tablesorter-save-order .flex-column.right .button {
    margin-left: 10px;
  }
}
/*------ charts ------*/
.panel-content.chart {
  display: block !important;
  min-height: 68px !important;
  padding: 0;
  overflow: hidden;
}
.panel-content.chart table.chart-stats {
  width: 100%;
  height: 68px;
  table-layout: fixed;
}
.panel-content.chart table.chart-stats th:not(:first-child) {
  border-left: 1px solid #657788;
}
.panel-content.chart table.chart-stats b {
  display: block;
  color: #FFF;
  font-size: 18px;
}
.panel-content.chart.closed :not(.chart-stats) {
  display: none;
}
.chart-container {
  position: relative;
  width: 100%;
  margin-bottom: 30px;
  padding: 20px 20px 0 20px;
}
.chart-container canvas {
  display: block;
  width: 100%;
  height: 200px;
}
/*------ sitemap ------*/
#site-pages .page-name i {
  margin-right: 5px;
  color: #999999;
  vertical-align: middle;
}
#sitemap-reference {
  padding: 0;
}
#sitemap-reference li {
  margin-top: 5px;
}
#sitemap-reference .sitemap-button {
  font-size: 14px;
  margin: 0;
}
#sitemap-reference .sitemap-button i {
  margin-right: 0;
}
.sitemap-pages tr:not(.level-1) .page-name:before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f3bf";
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  display: inline-block;
  margin-right: 10px;
  font-size: 12px;
  color: #53C5EB;
}
.sitemap-pages tr.level-2 .page-name {
  padding-left: 30px;
}
.sitemap-pages tr.level-3 .page-name {
  padding-left: 50px;
}
.sitemap-pages tr.level-4 .page-name {
  padding-left: 70px;
}
.sitemap-pages tr.level-5 .page-name {
  padding-left: 90px;
}
.sitemap-pages tr td:last-child {
  padding-right: 10px;
  padding-left: 0;
  width: 100px;
}
/*------ toggle switch ------*/
.onoffswitch {
  display: inline-block;
  width: 80px;
  background-color: #FFF;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.onoffswitch input {
  position: absolute;
  margin: 0;
  padding: 0;
  border: 0;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  background: none;
  overflow: hidden;
}
.onoffswitch label {
  display: block;
  width: 150%;
  height: auto;
  margin: 0;
  cursor: pointer;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-transition: background-color 0.2s ease-in 0s, transform 0.2s ease-in 0s;
  -webkit-transition: background-color 0.2s ease-in 0s, -webkit-transform 0.2s ease-in 0s;
  transition: background-color 0.2s ease-in 0s, -webkit-transform 0.2s ease-in 0s;
  transition: background-color 0.2s ease-in 0s, transform 0.2s ease-in 0s;
  transition: background-color 0.2s ease-in 0s, transform 0.2s ease-in 0s, -webkit-transform 0.2s ease-in 0s;
}
.onoffswitch label .inner,
.onoffswitch label::before,
.onoffswitch label::after {
  text-align: center;
  width: 40px;
  height: 30px;
  padding: 0;
  font-size: 14px;
  line-height: 30px;
  color: #FFF;
  -webkit-transition: transform 0.3s ease 0s;
  -webkit-transition: -webkit-transform 0.3s ease 0s;
  transition: -webkit-transform 0.3s ease 0s;
  transition: transform 0.3s ease 0s;
  transition: transform 0.3s ease 0s, -webkit-transform 0.3s ease 0s;
}
.onoffswitch label::before {
  background-color: #53C5EB;
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f00c";
}
.onoffswitch label::after {
  background-color: #CC0000;
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f00d";
}
.onoffswitch label .inner {
  background-color: #657788;
  opacity: 0;
  -webkit-transition: opacity 0.15s ease 0s;
  transition: opacity 0.15s ease 0s;
}
.onoffswitch label .inner::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f068";
}
.onoffswitch :not(:checked) + label {
  -webkit-transform: translateX(-33.33%);
  transform: translateX(-33.33%);
}
.onoffswitch :indeterminate + label,
.onoffswitch [indeterminate] + label {
  -webkit-transform: translateX(-16.665%);
  transform: translateX(-16.665%);
}
.onoffswitch :indeterminate + label::before,
.onoffswitch [indeterminate] + label::before {
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}
.onoffswitch :indeterminate + label::after,
.onoffswitch [indeterminate] + label::after {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}
.onoffswitch :indeterminate + label .inner,
.onoffswitch [indeterminate] + label .inner {
  opacity: 1;
}
.switch-sorter {
  display: none;
}
table tr:nth-child(odd) td .onoffswitch {
  background-color: #EEE;
}
table td .onoffswitch,
table th .onoffswitch,
table td .item-status,
table th .item-status {
  display: block;
}
table td.center .onoffswitch,
table th.center .onoffswitch,
table td.center .item-status,
table th.center .item-status {
  margin: auto;
}
table td.right .onoffswitch,
table th.right .onoffswitch,
table td.right .item-status,
table th.right .item-status {
  margin-left: auto;
}
/*------ page status buttons ------*/
.item-status {
  position: relative;
  display: inline-block;
  width: 125px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
.item-status button {
  width: 40px;
  margin: 0 1px 0 0 !important;
  height: 30px;
  padding: 0;
  font-size: 16px;
  font-weight: normal;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background-clip: padding-box;
}
.item-status button i {
  width: 40px;
  height: 30px;
  line-height: 30px;
  color: #666666;
  background: #EEE;
}
.item-status button:hover i {
  color: #FFF;
  background: #25b6e6;
}
.item-status button.active i {
  color: #FFF;
  background: #53C5EB;
}
table tr:nth-child(2n) .item-status button i,
.panel-header .item-status button i {
  background: #FFF;
}
table tr:nth-child(2n) .item-status button:hover i,
.panel-header .item-status button:hover i {
  color: #FFF;
  background: #25b6e6;
}
table tr:nth-child(2n) .item-status button.active i,
.panel-header .item-status button.active i {
  color: #FFF;
  background: #53C5EB;
}
/*------ jquery ui helpers ------*/
.ui-helper-hidden {
  display: none;
}
.ui-helper-hidden-accessible {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  border: 0;
  overflow: hidden;
  clip: rect(0 0 0 0);
}
.ui-helper-clearfix:before,
.ui-helper-clearfix:after {
  content: "";
  display: table;
}
.ui-helper-clearfix:after {
  clear: both;
}
.ui-helper-clearfix {
  zoom: 1;
}
.ui-widget-overlay,
.ui-widget-overlay.ui-front {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.5;
}
/*------ tabs ------*/
.tabs.page-content {
  display: none;
}
.tabs {
  display: block !important;
  margin-bottom: 20px;
}
.tabs > ul {
  position: relative;
  list-style: none;
  margin: 0;
  padding: 0 0 0 1px;
  width: 100%;
  z-index: 1;
}
.tabs > ul > li {
  position: relative;
  display: inline-block;
  margin-right: 2px;
}
.tabs > ul > li a {
  display: block;
  z-index: 1;
  padding: 15px 20px;
  background: #CCC;
  color: #666666;
  font-weight: 800;
  line-height: 30px;
  margin: 0 0 0 -1px;
  outline: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 -5px 10px -5px rgba(102, 102, 102, 0.16) inset;
  box-shadow: 0 -5px 10px -5px rgba(102, 102, 102, 0.16) inset;
}
.tabs > ul > li a:hover {
  background: #ffffff;
}
.tabs > ul li:not(.ui-tabs-tab):first-child a,
.tabs > ul li.ui-state-active a:hover,
.tabs > ul li.ui-state-active a {
  background: #FFF;
  color: #001E2F;
  z-index: 2;
  -webkit-box-shadow: none;
  box-shadow: none;
  text-shadow: none;
}
.tabs > ul li:not(.ui-tabs-tab):first-child::before,
.tabs > ul li.ui-state-active::before {
  position: absolute;
  content: "";
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  clip-path: inset(-10px -10px 0 -10px);
}
.tabs > ul ~ .ui-tabs-panel.nopadding > .mce-tinymce.mce-container {
  margin-left: -1px;
}
.tabs > ul ~ .ui-tabs-panel ~ .ui-tabs-panel > .mce-tinymce.mce-container {
  margin-left: 0px;
}
.tabs > :not(ul:first-child) {
  position: relative;
  display: block;
  clear: both;
  padding: 20px 20px 10px;
  background: #FFF;
}
.tabs > :not(ul:first-child):before {
  position: absolute;
  content: "";
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
}
.tabs > :not(ul:first-child) ~ * {
  display: none;
}
/*------ dialog box ------*/
.ui-dialog {
  position: absolute;
  max-height: calc(100% - 40px);
  z-index: 10000;
  top: 0;
  left: 0;
  padding: 0;
  overflow: hidden;
  outline: none !important;
  background: #FFF;
  color: #001E2F;
  border: 0;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(102, 102, 102, 0.16);
  box-shadow: 0px 0px 10px 0px rgba(102, 102, 102, 0.16);
}
.ui-dialog .ui-dialog-titlebar {
  position: relative;
  padding: 20px;
  background: #53C5EB;
  font-family: 'Exo', sans-serif;
  color: #FFF;
  font-size: 18px;
  font-weight: 800;
}
.ui-dialog.dialog-error .ui-dialog-titlebar {
  background: #CC0000;
}
.ui-dialog .ui-dialog-title {
  float: left;
  margin: 0 25px 0 0;
  text-transform: uppercase;
}
.ui-dialog .ui-dialog-titlebar-close {
  position: absolute;
  right: 10px;
  top: 50%;
  width: 20px;
  margin: -8px 4px 0 0;
  padding: 0;
  height: 20px;
  background: none;
  color: #FFF;
  text-align: center;
  text-decoration: none;
  border: 0;
  font-size: 0;
  cursor: pointer;
  font-weight: 700;
}
.ui-dialog .ui-dialog-titlebar-close:hover,
.ui-dialog .ui-dialog-titlebar-close:focus {
  background: none;
  color: #FFF;
}
.ui-dialog .ui-dialog-titlebar-close span {
  display: block;
}
.ui-dialog .ui-dialog-titlebar-close .ui-icon-closethick {
  display: block;
  background: none;
  outline: none;
}
.ui-dialog .ui-dialog-titlebar-close .ui-icon-closethick:before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f00d";
  font-size: 20px;
  font-style: normal;
  text-decoration: inherit;
}
.ui-dialog ul,
.ui-dialog ol {
  margin: 0 0 0 20px;
}
.ui-dialog ul:last-child,
.ui-dialog ol:last-child {
  padding: 0;
}
.ui-dialog ul ul,
.ui-dialog ol ul,
.ui-dialog ul ol,
.ui-dialog ol ol {
  padding: 5px 0;
}
.ui-dialog .ui-dialog-content {
  position: relative;
  max-height: 300px !important;
  border: 0;
  padding: 20px;
  background: none;
  overflow: auto;
  zoom: 1;
}
@media all and (min-width: 769px) {
  .ui-dialog .ui-dialog .ui-dialog-content {
    max-height: 400px !important;
  }
}
@media all and (min-width: 1025px) {
  .ui-dialog .ui-dialog .ui-dialog-content {
    max-height: 500px !important;
  }
}
.ui-dialog .ui-dialog-buttonpane {
  text-align: left;
  border-width: 1px 0 0 0;
  background-image: none;
  margin: 0;
  padding: 0 10px 10px;
}
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
  clear: both;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
}
.ui-dialog .ui-dialog-buttonpane .ui-button {
  margin: 0 10px 5px 10px;
  cursor: pointer;
  background: #666666;
  color: #FFF;
  outline: none !important;
  border: 0;
  font-size: 12px;
  text-transform: none;
  padding: 5px 10px;
  font-weight: 600;
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.ui-dialog .ui-dialog-buttonpane .ui-button i {
  margin-right: 5px;
}
.ui-dialog .ui-dialog-buttonpane .ui-button:hover {
  background: #001E2F;
  color: #FFF;
}
.ui-dialog .ui-dialog-buttonpane .ui-button .ui-resizable-se {
  width: 14px;
  height: 14px;
  right: 3px;
  bottom: 3px;
}
/*------ tooltip ------*/
.tooltip {
  font-weight: 800;
  color: #53C5EB;
  cursor: pointer;
  padding-left: 5px;
  font-family: 'Open Sans', sans-serif;
}
.handle .tooltip {
  position: absolute;
  top: 0;
  left: -5px;
  bottom: 0;
}
.ui-tooltip {
  position: absolute;
  display: none;
  background: #001E2F;
  font-size: 12px;
  width: 210px;
  padding: 20px;
  color: #FFF;
  z-index: 99;
  line-height: 18px;
  opacity: 0.9;
  word-break: break-word;
  -webkit-box-shadow: 0 0 20px 0px rgba(102, 102, 102, 0.16);
  box-shadow: 0 0 20px 0px rgba(102, 102, 102, 0.16);
}
.ui-tooltip h4 {
  color: #53C5EB;
}
.ui-tooltip h4:only-child {
  margin: 0;
}
.ui-tooltip p {
  padding-bottom: 15px;
}
.ui-tooltip p:only-child {
  padding: 0;
}
.ui-tooltip small {
  color: #EEE;
  font-size: 85%;
  line-height: 1.3;
  display: inline-block;
}
/*------ seo styles ------*/
.google-preview {
  overflow: hidden;
  min-width: 240px;
}
.google-preview div {
  font-family: Arial, Helvetica, sans-serif;
  color: #545454;
  font-size: 13px;
  line-height: 18px;
}
.google-preview div h2 {
  font-family: inherit;
  font-size: 18px;
  font-weight: normal;
  color: #1a0dab;
  margin: 0 0 3px;
  padding: 0;
}
.google-preview div h6 {
  font-family: inherit;
  margin-bottom: 6px;
  font-size: 14px;
  color: #006621;
}
.seo-pass {
  color: #65B561 !important;
}
.seo-fail {
  color: #CC0000 !important;
}
.seo-average {
  color: #FFCC00 !important;
}
tr[class^="seo-"] > td:first-child {
  position: relative;
  border-left: 5px solid #ff6767;
}
tr[class^="seo-"] > td:first-child .tooltip {
  position: absolute;
  display: block;
  top: 0;
  left: -5px;
  width: 5px;
  height: 100%;
}
tr.seo-1 > td:first-child {
  border-left: 5px solid #CC0000;
}
tr.seo-2 > td:first-child {
  border-left: 5px solid #FFCC00;
}
tr.seo-3 > td:first-child {
  border-left: 5px solid #65B561;
}
.graph,
.progress-bar {
  width: 150px;
  margin-right: 20px;
}
.graph p,
.progress-bar p {
  color: #001E2F;
  font-weight: 800;
  text-transform: uppercase;
}
.graph p small,
.progress-bar p small {
  display: block;
  font-size: 14px;
  font-weight: 500;
}
.progress-bar {
  width: 400px;
  height: auto;
  max-width: 100%;
  min-height: 130px;
  font-size: 16px;
  padding: 20px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-family: 'Exo', sans-serif;
}
.progress-bar .percentage {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.progress-bar .percentage .numbers {
  margin-right: 10px;
  font-size: 60px;
  font-family: 'Exo', sans-serif;
  line-height: 1;
  font-weight: 900;
}
.progress-bar .percentage p {
  padding: 0;
}
.progress-bar .percent-progress {
  position: relative;
  display: block;
  width: 100%;
  height: 10px;
  overflow: hidden;
  margin-bottom: 20px;
  background: #EEE;
}
.progress-bar .percent-progress:before {
  position: absolute;
  content: '';
  width: var(--percent);
  height: 10px;
  -webkit-animation: progress-bar 0.8s;
  animation: progress-bar 0.8s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-play-state: var(--animation-play-state, running);
          animation-play-state: var(--animation-play-state, running);
}
.progress-bar .percent-progress.seo-pass:before {
  background-color: #65B561;
}
.progress-bar .percent-progress.seo-average:before {
  background-color: #e0b400;
}
.progress-bar .percent-progress.seo-fail:before {
  background-color: #990000;
}
#seo-summary {
  max-width: 750px;
  padding-left: 30px;
}
#seo-summary .progress-bar {
  width: 450px;
}
.seo-summary {
  --theme: #CC0000;
  overflow: hidden;
  width: 100%;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #CCC;
}
.seo-summary.warning {
  --theme: #FFCC00;
}
.seo-summary.passed {
  --theme: #65B561;
}
.seo-summary:last-child {
  border: 0;
}
.seo-summary .summary-title {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row nowrap;
          flex-flow: row nowrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  cursor: pointer;
}
.seo-summary .summary-title::before {
  width: 1em;
  height: 1em;
  background-color: var(--theme);
  border-radius: 50%;
  content: '';
  -webkit-box-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
.seo-summary .summary-title::after {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  content: "\f077";
  color: #003A5E;
  margin-left: auto;
}
.seo-summary .summary-title::after:hover {
  color: #53C5EB;
}
.seo-summary .summary-title.ui-accordion-header-collapsed::after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
.seo-summary ul {
  list-style-type: none;
  margin: 10px 0 0 6px;
  border-left: 2px solid var(--theme);
  padding: 0 0 0 16px;
}
.seo-summary li {
  font-size: 12px;
  padding: 5px 0;
  border-top: 1px solid #EEE;
}
.seo-summary li:first-child {
  border: 0;
}
.seo-summary .ui-accordion-header-icon {
  display: none;
}
/*------ animations ------*/
@-webkit-keyframes progress-bar {
  0% {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes progress-bar {
  0% {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@-webkit-keyframes come-in-top {
  0% {
    opacity: 0;
    top: 10px;
  }
  100% {
    opacity: 0.9;
    top: 0;
  }
}
@keyframes come-in-top {
  0% {
    opacity: 0;
    top: 10px;
  }
  100% {
    opacity: 0.9;
    top: 0;
  }
}
.come-in-top {
  -webkit-animation: come-in-top 0.8s ease;
  animation: come-in-top 0.8s ease;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-play-state: var(--animation-play-state, running);
          animation-play-state: var(--animation-play-state, running);
}
@-webkit-keyframes bounce-out-top {
  0% {
    opacity: 0.9;
    top: 0;
  }
  40% {
    opacity: 0.5;
    top: 20px;
  }
  100% {
    opacity: 0;
    top: -50px;
  }
}
@keyframes bounce-out-top {
  0% {
    opacity: 0.9;
    top: 0;
  }
  40% {
    opacity: 0.5;
    top: 20px;
  }
  100% {
    opacity: 0;
    top: -50px;
  }
}
.bounce-out-top {
  -webkit-animation: bounce-out-top 0.8s ease;
  animation: bounce-out-top 0.8s ease;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-play-state: var(--animation-play-state, running);
          animation-play-state: var(--animation-play-state, running);
}
.input-with-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
}
.input-with-icon .input-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0 12px;
  background-color: #657788;
  color: #CCC;
  border: 1px solid #4f5d6b;
  border-right: none;
  border-radius: 4px 0 0 4px;
}
.input-with-icon .input-icon i {
  font-size: 1.1em;
}
.input-with-icon .input {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  border-left: none;
  border-radius: 0 4px 4px 0;
  padding-left: 12px;
}
.input-with-icon input {
  margin-bottom: 0;
}
.input.readonly {
  border-color: #FFF;
  padding-left: 0px;
}

#field-options-table .delete-template-btn{ margin-bottom: 20px; }
