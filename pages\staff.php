<?php

$html = '';

//Display all
if(!STAFF_ID){

	if(!empty($staff_categories)){
		$html .= '<section id="staff-section" class="fluid-max container">';

		foreach($staff_categories as $staffcat){
			if($staffcat['total'] > 0){
				$html .= '<h2>' .fancy_text($staffcat['name']). '</h2>';
				$html .= $staffcat['content'];
				$html .= '<ul class="staff-listing">';

				foreach($staff_arr as $staff){
					if($staff['category_id'] == $staffcat['category_id']){
						$staffdir = 'images/staff/thumbs/';
						$staffimg = check_file($staff['image'], $staffdir) ? $staff['image'] : 'default.jpg';

						$html .= '<li>
							<a href="' .$staff['page_url']. '">
								<img class="lazy-load" src="' .$path.$staffdir. 'default.jpg" data-src="' .$path.$staffdir.$staffimg. '" alt="'.($staff['image_alt'] ?: $staff['name']).'" />
							</a>
							<div class="staff-info-box">
							<div class="staff-info">
							<h5>' .fancy_text($staff['name']).'</h5>
							</div>
							<div class="location-scroll-arrow staff-link location-scroll-right"><a href="' .$staff['page_url']. '"></a></div>
							</div>
						</li>';
					}
				}
				// <i class="fas fa-arrow-right"></i>
				$html .= '</ul>';
			}
		}

		$html .= '</section>';
	}

	//Set panel content
	if(isset($page['page_panels'][$panel_id])){
		$page['page_panels'][$panel_id]['content'] .= $html;
	}


//Display selected
}else{

	$html .= '<article id="staff-bio">';

		//Staff photo
		if(check_file($staff['image'], 'images/staff/featured/')){
			$html .= '<div class="image-box"><img src="' .$path. 'images/staff/featured/' .$staff['image']. '" alt="'.($staff['image_alt'] ?: $staff['name']).'" /></div>';
		}

		$html .= '<div class="content-box">
			<header>
				<h2>' .fancy_text($staff['name']). '</h2>
			</header>';

			//Contact info
			if(!empty($staff['phone']) || !empty($staff['email'])){
				$html .= '<div class="contact-info">';
				$html .= (!empty($staff['phone']) ? '<i class="fas fa-phone"></i>&nbsp;&nbsp;&nbsp;' .$staff['phone']. '<hr class="contact-separator" />' : '');
				$html .= (!empty($staff['email']) ? '<i class="fas fa-envelope"></i>&nbsp;&nbsp;&nbsp;<a class="staff-email" href="mailto:' .$staff['email']. '">' .$staff['email']. '</a><br />' : '');
				$html .= '</div>';
			}

			//Social icons
			if(!empty($staff['social'])){
				$html .= '<ul class="social-icons staff-social">';
				foreach($staff['social'] as $social){
					$html .= (!empty($social['url']) ? '<li><a href="'.$social['url'].'" target="_blank"><span class="fab fa-'.$social['service'].'"></span><span class="fab fa-'.$social['service'].'"></span>'.$social['service'].'</a></li>' : '');
				}
				$html .= '</ul>';
			}

			//Biography
			$html .= $staff['content'];

			//Back to all
			// $html .= '<p><a href="' .$_sitepages['staff']['page_url']. '">&lsaquo; All Staff Members</a></p>';
			$html .= '<div class="page-buttons"><a class="button primary red" href="/about/staff/" target=""><span class="left-border"></span><span class="right-border"></span>BACK TO ALL</a></div>

		</div>
	</article>';

	//
	//Create a standard panel for staff detail
	$panel = [
		'panel_type' => 'standard',
		'content' => $html,
		'class' => 'staff-detail-panel',
		'show_title' => 0 // Hide title as it's already included in the HTML
	];

	//Create "Other Staff" panel content
	$other_staff_html = '<div class="other-staff-section">';
	$other_staff_html .= '<h2 class="staff-section-title"><span style="color:#e73c3e;">Other</span> Staff</h2>';
	$other_staff_html .= '<div class="staff-grid">';

	// Get 4 random staff members (excluding current one)
	$other_staff_ids = array_keys($staff_arr);
	$current_key = array_search(STAFF_ID, $other_staff_ids);
	if ($current_key !== false) {
		unset($other_staff_ids[$current_key]); // Remove current staff from the pool
	}

	// If we have more than 4 staff members, randomly select 4
	if (count($other_staff_ids) > 4) {
		shuffle($other_staff_ids);
		$other_staff_ids = array_slice($other_staff_ids, 0, 4);
	}

	// Generate HTML for each selected staff member
	foreach ($other_staff_ids as $other_id) {
		$other = $staff_arr[$other_id];
		$staff_img = check_file($other['image'], 'images/staff/thumbs/') ? $other['image'] : 'default.jpg';

		$other_staff_html .= '
		<div class="staff-card staff-listing">
			<a href="' . $other['page_url'] . '">
				<img src="' . $path . 'images/staff/thumbs/' . $staff_img . '" alt="' . ($other['image_alt'] ?: $other['name']) . '" />
			</a>
			<div class="staff-info-box other-staff-info-box">
				<div class="staff-info">
					<h5>' .fancy_text($other['name']).'</h5>
				</div>
				<div class="location-scroll-arrow staff-link location-scroll-right"><a href="' .$other['page_url']. '"></a></div>
			</div>
		</div>';
	}

	$other_staff_html .= '</div></div>';

	//Create a standard panel for other staff members
	$panel2 = [
		'panel_type' => 'standard',
		'prepend_content' => $other_staff_html,
		'class' => 'staff-detail-panel',
		'show_title' => 0
	];

	//Add the panel to the page panels array
	$page['page_panels'][] = $panel;
	$page['page_panels'][] = $panel2;

	//Set panel content
	// $page['content'] = $html;
}


//Panels
include("includes/pagepanels.php");

?>