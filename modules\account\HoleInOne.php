<?php


//Display user invoices
if(PAGE_ID == $_sitepages['hole-in-one']['page_id']){

    //Check for active login
    if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
        header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
        exit();
    }


	//Hole-in-One accounts must pay immediately
	// define('HIO_ACCESS', in_array(9, $Account->roles));

	//Restrict hole-in-one access
	if((!defined('MEMBER_ACCESS') || !MEMBER_ACCESS) && (!defined('HIO_ACCESS') || !HIO_ACCESS)){
		exit();
	}

	//Define vars
	$panel_id =81;//67;// 126;
	// $panel_id =90;// dev db panel id// localhost 81;//67;// 126;
	$hios = array();
	$hio = array();
	$params = array();
	$required = array();

	//Get account facility
	$facility = array('facility_name' => 'Unknown', 'email' => NULL, 'phone' => NULL);
	$query = $db->query("SELECT * FROM `facilities` WHERE `facility_id` = ?", array($Account->facility_id));
	if($query && !$db->error() && $db->num_rows()){
		$result = $db->fetch_array();
		$facility = $result[0];
	}

	//Hole in one success alert
	if(isset($_SESSION['hio']['alert'])){
		$alert = $Account->alert($_SESSION['hio']['alert'], true);

		//Unset session
		unset($_SESSION['hio']);
	}

	//Add or edit
	if(ACTION == 'add' || (ACTION == 'edit' && ITEM_ID != '')){

		//Get record
		if(ACTION == 'edit'){
			$query = $db->query("SELECT `hio`.*, `invoices`.`invoice_number` FROM `hio` LEFT JOIN `invoices` ON `hio`.`invoice_id` = `invoices`.`invoice_id` WHERE `hio`.`account_id` = ? && `hio`.`hio_id` = ?", array(USER_LOGGED_IN, ITEM_ID));
			if(!$query || $db->error() || !$db->num_rows()){
				$notfound = true;
				$errors[] = 'Hole in one not found. Please select from the list below.';
			}else{
				$result = $db->fetch_array();
				$hio = $result[0];

				//Get dates
				$hio['event_dates'] = array();
				$query = $db->query("SELECT * FROM `hio_dates` WHERE `hio_id` = ? ORDER BY `event_date` ASC", array(ITEM_ID));
				if($query && !$db->error()){
					$result = $db->fetch_array();
					foreach($result as $date){
						$hio['event_dates'][] = date('F j, Y', strtotime($date['event_date']));
					}
				}

				//Get courses
				$hio['courses'] = array();
				$query = $db->query("SELECT * FROM `hio_courses` WHERE `hio_id` = ?", array(ITEM_ID));
				if($query && !$db->error()){
					$result = $db->fetch_array();
					foreach($result as $course){

						//Get holes
						$course['holes'] = array();
						$query = $db->query("SELECT * FROM `hio_holes` WHERE `course_id` = ?", array($course['course_id']));
						if($query && !$db->error()){
							$course['holes'] = $db->fetch_array();
						}

						$hio['courses'][] = $course;
					}
				}
			}
		}

		//Create new
		if(ACTION == 'add'){

			//Get waivers
			$waiver_forms = array();
			$query = $db->query("SELECT * FROM `reg_waivers` WHERE `event_type` = 3 && `showhide` = 0 ORDER BY `ordering`");
			if($query && !$db->error()){
				$waiver_forms = $db->fetch_array();
			}

			//Event Information
			if(isset($_POST['continue'])){

				//Set defaults
				$confirm = false;
				$payment = false;
				$approved = (MEMBER_ACCESS ? true : false); //Members get auto approved, hio accounts must pay first

				$dates = array();
				$courses = array();
				$row['premium_total'] = 0;
				$row['prize_total'] = 0;

				//Validation
				$required_fields = array('event_name', 'field');
				foreach($required_fields as $field){
					if(!isset($_POST[$field]) || trim($_POST[$field]) == ''){
						$errors['required'] = 'Please fill out all the required fields.';
						$required[] = $field;
					}
				}
				if($_POST['field'] != ''){
					if(!is_numeric($_POST['field'])){
						$errors['field'] = 'Invalid amount for number of golfers. Please enter a number.';
						$required[] = 'field';
					}else{
						if($_POST['field'] < 32){
							$errors['required'] = 'Minimum number of golfers for online registration is 32. Please <a href="' .$_sitepages['contact']['page_url']. '">contact our office</a> for a quote.';
							$required[] = 'field';
						}else if($_POST['field'] > 199){
							$errors['required'] = 'Maximum number of golfers for online registration is 199. Please <a href="' .$_sitepages['contact']['page_url']. '">contact our office</a> for a quote.';
							$required[] = 'field';
						}
					}
				}
				foreach($_POST['dates'] as $d=>$date){
					if(trim($date) != ''){
						$dates[] = $date;
						if(date("Ymd", strtotime($date)) <= date("Ymd")){
							$errors['dates'] = 'Hole in one events must be created in advance. Please review your selected dates to ensure they are no earlier than '.date("F j, Y", strtotime('+1 day')).'.';
							$required[] = $date;
						}
					}
				}
				if(empty($dates)){
					$errors['required'] = 'Please fill out all the required fields.';
					$required[] = 'dates';
				}

				//Loop through courses
				foreach($_POST['course_name'] as $c=>$course){

					//Loop through holes
					for($h=1; $h<=18; $h++){
						if($_POST['yards_m_'.$h][$c] != '' || $_POST['yards_w_'.$h][$c] != '' || $_POST['prize_'.$h][$c] != ''){

							//Validate course name
							if(trim($_POST['course_name'][$c]) == ''){
								$errors['required'] = 'Please fill out all the required fields.';
								$required[] = 'course_name_'.$c;
							}

							//Validate yards
							if(empty($_POST['yards_m_'.$h][$c]) && empty($_POST['yards_w_'.$h][$c])){
								$errors['yards'] = 'Please enter the number of yards for at least one gender.';
								$required[] = 'yards_m_'.$h.'_'.$c;
								$required[] = 'yards_w_'.$h.'_'.$c;
							}else{
								if($_POST['yards_m_'.$h][$c] != '' && $_POST['yards_m_'.$h][$c] < 160){
									$errors['mens'] = 'Minimum yards for men is 160.';
									$required[] = 'yards_m_'.$h.'_'.$c;
								}
								if($_POST['yards_w_'.$h][$c] != '' && $_POST['yards_w_'.$h][$c] < 150){
									$errors['womens'] = 'Minimum yards for women is 150.';
									$required[] = 'yards_w_'.$h.'_'.$c;
								}
							}

							//Validate prize
							if($_POST['prize_'.$h][$c] == ''){
								$errors['required'] = 'Please fill out all the required fields.';
								$required[] = 'prize_'.$h.'_'.$c;
							}else if($_POST['prize_'.$h][$c] < 500){
								$errors['prize_min'] = 'Minimum prize amount is $500.';
								$required[] = 'prize_'.$h.'_'.$c;
							}else if($_POST['prize_'.$h][$c] > 50000){
								$errors['prize_max'] = 'Prize amount cannot exceed $50,000 for online registration. Please <a href="' .$_sitepages['contact']['page_url']. '">contact our office</a> for a quote.';
								$required[] = 'prize_'.$h.'_'.$c;
							}

							//Format numbers
							$_POST['yards_m_'.$h][$c] = number_format($_POST['yards_m_'.$h][$c], 0, '.', '');
							$_POST['yards_w_'.$h][$c] = number_format($_POST['yards_w_'.$h][$c], 0, '.', '');
							$_POST['prize_'.$h][$c] = number_format($_POST['prize_'.$h][$c], 0, '.', '');

							//Calculate premium
							$prize = $_POST['prize_'.$h][$c];
							$premium = 0;

							//Get premiums
							$query = $db->query("SELECT * FROM `hio_rates` ORDER BY `prize_total` ASC");
							if($query && !$db->error() && $db->fetch_array()){
								$rates = $db->fetch_array();
								foreach($rates as $index=>$rate){

									//Low or high rate?
									$premium_rate = $rate['premium'];
									if($_POST['field'] >= 100){
										$premium_rate = $rate['premium_100'];
									}

									//Exact amount
									if(number_format($_POST['prize_'.$h][$c], 2, '.', '') == $rate['prize_total']){
										$premium += $premium_rate;
										break;
									}

									//Calculate amount
									if($rate['prize_total'] > $prize){

										$upper_prize = $rate['prize_total'];
										$lower_prize = (isset($rates[($index-1)]['prize_total']) ? $rates[($index-1)]['prize_total'] : 0);

										$upper_premium = $premium_rate;
										$lower_premium = (isset($rates[($index-1)]['premium']) ? $rates[($index-1)]['premium'] : 0);
										if($_POST['field'] >= 100){
											$lower_premium = (isset($rates[($index-1)]['premium_100']) ? $rates[($index-1)]['premium_100'] : 0);
										}

										$prize_difference = ($upper_prize-$lower_prize);
										$premium_difference = ($upper_premium-$lower_premium);

										$prize_increase = ($prize-$lower_prize);
										$percent_increase = ($prize_increase/$prize_difference);

										$premium += number_format($lower_premium+($premium_difference*$percent_increase), 2, '.', '');
										break;
									}

								}


							}

							//Save data to array
							$courses[$c]['course_name'] = $_POST['course_name'][$c];
							$courses[$c]['holes'][$h] = array(
								'yards_men' => $_POST['yards_m_'.$h][$c],
								'yards_women' => $_POST['yards_w_'.$h][$c],
								'prize' => $prize,
								'premium' => $premium
							);

							//Add to totals
							$row['premium_total'] += $premium;
							$row['prize_total'] += $prize;
						}
					}
				}

				//Validate selected courses
				if(empty($courses)){
					$errors[] = 'Please select at least one hole for one course to continue.';
				}

				//Validate premium
				if(!$errors && $row['premium_total'] <= 0){
					$errors[] = 'Error calculating premium. Please try again.';
				}

				//Validate waivers
				if(!empty($waiver_forms)){
					$waiver_error = false;
					foreach($waiver_forms as $waiver){
						if($waiver['required'] && !isset($_POST['waiver-'.$waiver['waiver_id']])){
							$waiver_error = true;
							$required[] = 'waiver-'.$waiver['waiver_id'];
						}
						$_SESSION['hio']['waivers'][$waiver['waiver_id']] = (isset($_POST['waiver-'.$waiver['waiver_id']]) ? 1 : 0);
					}
					if($waiver_error){
						$errors[] = 'Please ensure you read and agree/consent to all the required terms and conditions.';
					}
				}

				//Save session vars
				$_SESSION['hio']['event_name'] = $_POST['event_name'];
				$_SESSION['hio']['field'] = $_POST['field'];
				$_SESSION['hio']['dates'] = $dates;
				$_SESSION['hio']['courses'] = $courses;
				$_SESSION['hio']['prize_total'] = $row['prize_total'];
				$_SESSION['hio']['premium_total'] = $row['premium_total'];
				$_SESSION['hio']['comments'] = $_POST['comments'];
				$_SESSION['hio']['approved'] = $approved;

				//Continue to next step
				if(!$errors){
					$confirm = true;
				}

                if($errors){
                    $alert = $Account->alert(implode('<br/>', $errors), false);
				}

				// echo "<pre>";
				// print_r($_POST);
				// echo "</pre>";

			//Submit registration
			}else if(isset($_POST['process'])){

				//Validate Recaptcha
				require_once("includes/plugins/recaptcha/recaptchalib.php");
				$recaptcha_response = NULL;
				$reCaptcha = new ReCaptcha($global['recaptcha_secret']);
				$recaptcha_response = $reCaptcha->verifyResponse(
					$_SERVER["REMOTE_ADDR"],
					$_POST["g-recaptcha-response"]
				);
				if($recaptcha_response != NULL && $recaptcha_response->success){

					$db->new_transaction();

					//Create invoice first
					$invoice_id = NULL;

					//Insert to db
					$params = array(
						$facility['facility_name'].' (' .$Account->first_name.' '.$Account->last_name. ')',
						$Account->first_name,
						$Account->last_name,
						(!empty($facility['email']) ? $facility['email'] : $Account->email),
						(!empty($facility['phone']) ? $facility['phone'] : $Account->phone),
						number_format($_SESSION['hio']['premium_total']*count($_SESSION['hio']['dates']), 2, '.', ''),
						date('Y-m-d H:i:s'),
						NULL,
						USER_LOGGED_IN,
						date('Y-m-d H:i:s'),
						USER_LOGGED_IN
					);
					$query = $db->query("INSERT INTO `invoices`(`bill_to`, `first_name`, `last_name`, `email`, `phone`, `invoice_total`, `invoice_date`, `due_date`, `account_id`, `last_updated`, `updated_by`) VALUES(?,?,?,?,?,?,?,?,?,?,?)", $params);

					//Update invoice number
					$invoice_id = $db->insert_id();
					$invoice_number = 'PGA'.str_pad($invoice_id, 5, '0', STR_PAD_LEFT).'-03';
					$query = $db->query("UPDATE `invoices` SET `invoice_number` = ? WHERE `invoice_id` = ?", array($invoice_number, $invoice_id));

					//Insert registration
					$params = array(
						USER_LOGGED_IN,
						$Account->facility_id,
						$invoice_id,
						$_SESSION['hio']['event_name'],
						number_format($_SESSION['hio']['prize_total'], 2, '.', ''),
						number_format($_SESSION['hio']['premium_total']*count($_SESSION['hio']['dates']), 2, '.', ''),
						$_SESSION['hio']['field'],
						$_SESSION['hio']['comments'],
						true, //Approve all hio immediately regardless of member access
						date('Y-m-d H:i:s'),
						date('Y-m-d H:i:s'),
						date('Y-m-d H:i:s')
					);
					$query = $db->query("INSERT INTO `hio`(`account_id`, `facility_id`, `invoice_id`, `event_name`, `prize_total`, `premium_total`, `field`, `comments`, `approved`, `date_approved`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?)", $params);
					$hio_id = $db->insert_id();

					//Insert dates
					foreach($_SESSION['hio']['dates'] as $date){
						$params = array($hio_id, date('Y-m-d', strtotime($date)));
						$query = $db->query("INSERT INTO `hio_dates`(`hio_id`, `event_date`) VALUES(?,?)", $params);
					}

					//Insert courses
					foreach($_SESSION['hio']['courses'] as $course){
						$params = array($hio_id, $course['course_name']);
						$query = $db->query("INSERT INTO `hio_courses`(`hio_id`, `course_name`) VALUES(?,?)", $params);
						$course_id = $db->insert_id();

						//Insert holes
						foreach($course['holes'] as $hole=>$info){
							$params = array($course_id, $hole, $info['yards_men'], $info['yards_women'], $info['prize'], $info['premium']);
							$query = $db->query("INSERT INTO `hio_holes`(`course_id`, `hole`, `yards_men`, `yards_women`, `prize`, `premium`) VALUES(?,?,?,?,?,?)", $params);
						}
					}

					if(!$db->error()){
						$db->commit();

						//Set notification message
						$receipt_content = '';
						if($_SESSION['hio']['approved']){

							$_SESSION['hio']['alert'] = 'Hole in one event was successfully submitted and has been invoiced.<br /><br />
							<a href="' .$_sitepages['payments']['page_url']. '?id=i'.$invoice_id. '" class="button simple dinline-block button primary red">Pay Now<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
							<a href="' .$_sitepages['invoices']['page_url']. '?action=edit&id='.$invoice_id. '" class="button simple dinline-block primary black button">View Invoice<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
							<a href="' .$page['page_url']. '?action=add" class="button simple dinline-block primary red">New Event<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>';

						}else{
							$receipt_content = '<p>Thank you for choosing the PGA of Alberta for all your Hole-In-One needs. If you require HIO insurance $50,001 and up, please contact the PGA of Alberta office @ '.$global['contact_email'].'. <strong>Note:</strong> Registration will be approved upon payment receipt.</p>';
						}

						//Send notification
						$order_data = array(
							'first_name' => $Account->first_name,
							'last_name' => $Account->last_name,
							'facility_name' => $facility['facility_name'],
							'email' => $Account->email,
							'phone' => $Account->phone,
							'event_name' => $_SESSION['hio']['event_name'],
							'event_field' => $_SESSION['hio']['field'],
							'event_dates' => $_SESSION['hio']['dates'],
							'invoice_number' => (isset($invoice_number) ? $invoice_number : NULL),
							'comments' => $_SESSION['hio']['comments'],
							'prize_total' => $_SESSION['hio']['prize_total'],
							'premium_total' => $_SESSION['hio']['premium_total']
						);
						$cart_data = $_SESSION['hio']['courses'];
						$receipt = $Registration->hio_receipt($order_data, $cart_data, $receipt_content);
						send_email($Account->email, 'Hole In One Event', $receipt);

						//Send to admin and facility
						$admin_email = (!empty($reg_settings['email_hio']) ? $reg_settings['email_hio'] : $global['contact_email']);
						send_email($admin_email, 'Hole In One Event', $receipt);

						//Send to hio page
						if($_SESSION['hio']['approved']){
							header('Location: '.$page['page_url'].'?action=edit&id='.$hio_id);
							exit();

						//Send to payment page (hio accounts must pay immediately)
						}else{
							header('Location: '.$_sitepages['payments']['page_url']. '?id=i'.$invoice_id);
							exit();
						}

					//Error
					}else{
						$errors[] = 'Unable to insert registration. '.$db->error();
					}

				//Invalid recaptcha
				}else{
					$errors[] = 'Unable to validate recaptcha. Please try again.';
				}
			}

		}

	}

	//Get all hole in ones
	if(ACTION == '' || (ACTION == 'edit' && empty($hio))){

		//Pagination
		$pg = (isset($_GET['pg']) ? $_GET['pg'] : 1);
		$limit = 20;
		$totalresults = 0;

		$params = array(USER_LOGGED_IN);
		if(isset($_GET['search']) && trim($_GET['search']) != ''){
			$params[] = '%'.$_GET['search'].'%';
			$params[] = '%'.$_GET['search'].'%';
			$params[] = '%'.str_replace('$', '', $_GET['search']).'%';
			$params[] = '%'.str_replace('$', '', $_GET['search']).'%';
		}
		$query = $db->query("SELECT `hio`.*, `invoices`.`invoice_number`, ".
		"(SELECT `event_date` FROM `hio_dates` WHERE `hio_dates`.`hio_id` = `hio`.`hio_id` ORDER BY `hio_dates`.`event_date` ASC LIMIT 1) AS `start_date`, ".
		"(SELECT `event_date` FROM `hio_dates` WHERE `hio_dates`.`hio_id` = `hio`.`hio_id` ORDER BY `hio_dates`.`event_date` DESC LIMIT 1) AS `end_date`".
		"FROM `hio` ".
		"LEFT JOIN `invoices` ON `hio`.`invoice_id` = `invoices`.`invoice_id` ".
		"WHERE `hio`.`account_id` = ? ".
		(isset($_GET['search']) && trim($_GET['search']) != '' ? "&& (`hio`.`event_name` LIKE ? || `invoices`.`invoice_number` LIKE ? || `hio`.`prize_total` LIKE ? || `hio`.`premium_total` LIKE ?) " : "").
		"ORDER BY `start_date` DESC, `hio`.`event_name` DESC", $params);
		if($query && !$db->error()){
			$hios = $db->fetch_array();

			//Pagination
			$totalresults = count($hios);
			if($pg != 'all'){
				$start = (($pg-1)*$limit);
				$end = $limit;
			}else{
				$start = 0;
				$end = $totalresults;
			}
			$hios = array_slice($hios, $start, $end);

		}

	}

}

?>