<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?>">
	<?php
	if($panel['title'] || $panel['include_h1'] || $panel['content']) {
		echo '<div class="panel-wrapper">';

		if($panel['title'] || $panel['include_h1']){
			echo '<header class="panel-header">
				<div class="container container-xl">
					'.($panel['include_h1'] ? '<h1>'.fancy_text($page['page_title']).'</h1>' : '').'
					'.($panel['title'] ? '<div class="panel-title"><h2>'.fancy_text($panel['title']).'</h2></div>' : '').'
				</div>
			</header>';
		}

		if($panel['content']){
			echo '<div class="panel-content">
				<div class="container">
					<div class="panel-text">'.$panel['content'].'</div>
				</div>
			</div>';
		}

		echo '</div>';
	}

	if(!empty($panel['panel_promos'])) {
		echo '<div class="panel-promos">
			<div class="container">
				<div class="promo-boxes animate" data-animate=".promo-box">';

				foreach($panel['panel_promos'] as $promo){
					$imagedir = $promo['imagedir'] ?? 'images/promos/thumbs/';
					$promo['image'] = check_file($promo['image'], $imagedir);
					$promo['url_target'] = ($promo['url_target'] ?? 0);
					if ($promo['image']) {
						list($imgw, $imgh) = getimagesize($imagedir.$promo['image']);
					}

					echo '<div class="promo-box">
						<div class="promo-image-wrapper">
							<div class="promo-image">
								<img class="lazy-load" 
									 src="'.empty_src($imgw, $imgh).'" 
									 data-src="'.$path.$imagedir.$promo['image'].'" 
									 alt="'.($promo['image_alt'] ?: $promo['title']).'" 
									 width="'.$imgw.'" 
									 height="'.$imgh.'" />
							</div>';

							$content = '<div class="overlay"></div>'.
							'<div class="promo-title">'.create_button(false, $promo['url_target'], $promo['title'], 'promo-title-link').'</div>'.
							($promo['description'] ? '<div class="promo-text">'.$promo['description'].'</div>' : '').
							($promo['url'] ? create_button(false, $promo['url_target'], $promo['url_text'], 'promo-link') : '');

							// echo create_button($promo['url'], $promo['url_target'], $content, 'promo-hover').

						echo'</div>

						<div class="promo-content">
							<h3 class="promo-title">'.create_button($promo['url'], $promo['url_target'], $promo['title'], 'promo-title-link').'</h3>'.
							($promo['description'] ? '<div class="promo-text">'.$promo['description'].'</div>' : '').
							($promo['url'] ? '<a href="'.$promo['url'].'" class="button ternary btn-colour" '.($promo['url_target'] ? 'target="'.$promo['url_target'].'"' : '').'><span class="top-border"></span> <span class="bottom-border"></span> <span class="left-border"></span>
							<span class="right-border"></span>
						'.$promo["url_text"].'</a>' : null).
							// ($promo['url'] ? create_button($promo['url'], $promo['url_target'], $promo['url_text'], 'promo-link') : '').
						'</div>
					</div>';
				}

				echo '</div>
			</div>
		</div>';
	}
	?>
</section>