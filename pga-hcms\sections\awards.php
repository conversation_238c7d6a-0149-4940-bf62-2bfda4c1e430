<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Categories
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='stickyheader sortable'>";
		
			echo "<thead>";
			echo "<th width='10px' class='{sorter:false}'></th>";
			echo "<th width='40px' class='{sorter:false}'></th>";
			echo "<th width='auto' class='{sorter:false}'>Name</th>";
			echo "<th width='70px' class='{sorter:false}'>Visible</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "<th width='15px' align='right' class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				$records_arr[$row['parent_id']]['sub_items'] = (!isset($records_arr[$row['parent_id']]['sub_items']) ? array() : $records_arr[$row['parent_id']]['sub_items']);

				//display seo score
				if($cms_settings['enhanced_seo'] && (array_key_exists(3, $Account->roles) || array_key_exists(4, $Account->roles))){ //SEO and master permissions
					if($row['seo_score'] > 80){
						$seo_class = "seo-3 ";
					} else if($row['seo_score'] >= 50 && $row['seo_score'] <= 80){
						$seo_class = "seo-2 ";
					} else {
						$seo_class = "seo-1 ";
					}
					$seo_tooltip = "<span class='seo-tool tooltip' title='<h4>SEO Score: <strong>".number_format($row['seo_score'],1)."</strong></h4>'>&nbsp;</span>";
				} else {
					$seo_class = "";
					$seo_tooltip = "";
				}
				
				echo "<tr data-level='".$row['lvl']."' data-table='$record_db' data-column-name='$record_id' data-name='".$row['name']."' data-id='".$row[$record_id]."' class='".$seo_class.($row['parent_id'] != "" ? " push lvl".$row['lvl'] : "").(!empty($row['sub_items']) ? " has_sub" : "").($row['parent_id'] != "" && array_search($row[$record_id], array_keys($records_arr[$row['parent_id']]['sub_items'])) == (count($records_arr[$row['parent_id']]['sub_items'])-1) && empty($row['sub_items']) ? " last_child" : "")."' data-system-page='true'>";
					echo "<td class='handle'>$seo_tooltip<span class='fa fa-arrows'></span></td>";
					echo'<td class="nopadding-r">'.($row['image'] ? '<a href="'.$path.$row['image'].'" class="light-gallery" rel="prettyPhoto" title="'.$row['name'].'">'.render_gravatar($row['image']).'</a>' : '').'</td>';
					echo "<td class='show-lvl'>" .$row['name']. "</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
					echo "<td class='right'><a href='".$siteurl.$root.$awards_page.$row['page']."-".$row[$record_id]."/' target='_blank'><i class='fa fa-external-link'></i></a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
		echo "</div>";	
	echo "</div>";
	
//Image cropping
}else if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');

//Display form	
}else{

	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		$image = $data['image'];	
		if(!isset($_POST['save'])){
			$row = $data;
		}

	} else if(ACTION == 'add' && !isset($_POST['save'])){	
		$image = '';
		unset($row);
	}

	if(ITEM_ID != "") {
		echo "<p class='right'><small><strong>Link to Entry:</strong> ".$siteurl.$root.$awards_page.$row['page']."-".$row[$record_id]."/</small> &nbsp; <a href='".$siteurl.$root.$awards_page.$row['page']."-".$row[$record_id]."/' target='_blank'><i class='fa fa-external-link'></i></a></p><hr/>";
	}

	if(ACTION == 'edit') {
		echo $CMSBuilder->important("<strong>Deletion:</strong> If you delete a category, all winners and sub-categories within that category will also be deleted. <strong>This action is NOT undoable.</strong>");
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		//Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				<div class='panel-switch'>
					<label>Show $record_name</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " />
						<label for='showhide'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>$record_name Name <span class='required'>*</span></label>
					<input type='text' name='name' value='" .(isset($row['name']) ? $row['name'] : ''). "' id='button-text' class='input" .(in_array('name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Parent $record_name</label>
					<select name='parent_id' class='select'>
						<option value=''>-- None --</option>";
						foreach($records_arr as $parent) {
							if($parent[$record_id] != ITEM_ID && $parent['parent_id'] == ""){
								echo "<option value='".$parent[$record_id]."'".(isset($row['parent_id']) && $row['parent_id'] == $parent[$record_id] ? " selected" : "").">".$parent['name']."</option>";
								/*foreach($parent['sub_items'] as $parent2){
									if($parent2[$record_id] != ITEM_ID){
										echo "<option value='".$parent2[$record_id]."'".(isset($row['parent_id']) && $row['parent_id'] == $parent2[$record_id] ? " selected" : "").">&nbsp;&nbsp; &rsaquo; ".$parent2['name']."</option>";
									}
								}*/
							}
						}
					echo "</select>
				</div>";
				echo "<div class='form-field'>
					<label>Numerical Order" .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). "</label>
					<select name='ordering' class='select'>
						<option value='101'>Default</option>";
						for($i=1; $i<101; $i++){
							echo "<option value='" .$i. "' " .(isset($row['ordering']) && $row['ordering'] == $i ? "selected" : ""). ">" .$i. "</option>";	
						}
					echo "</select>
				</div>";
			echo "</div>";
		echo "</div>"; //Details

		//Image
		echo '<div class="panel">
			<div class="panel-header">Banner Image
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">';

				//Upload Image
				if($image){
					echo '<div class="img-holder">
						<button type="button" name="recrop" value="image" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
						<a href="'.$path.$imagedir.'1920/'.$image.'" class="light-gallery" rel="prettyphoto" target="_blank" title="">
							<img src="'.$path.$imagedir.'1024/'.$image.'" alt="" />
						</a>
						<input type="checkbox" class="checkbox" name="deleteimage" id="deleteimage" value="1">
						<label for="deleteimage">Delete Current Image</label>
					</div>';
				}

					[$max_W, $max_H] = CMSUploader::max_size('banner', 'image');
					echo '<div class="form-field">
						<label>Upload Image '.(in_array('image', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Upload Image', 'Image must be smaller than '.$_max_filesize['megabytes'].' and larger than '.$max_W.' x '.$max_H.'.').'</label>
						<input type="file" class="input'.(in_array('image', $required) ? ' required' : '').'" name="image" value="" />
					</div>
					
					<div class="form-field">
						<label>Alt Text <small>(SEO)</small>' .$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.'). '</label>
						<input type="text" name="image_alt" value="' .($row['image_alt'] ?? ''). '" class="input" />
					</div>
					
				</div>
			</div>
		</div>';
		//Page banner

		//Description
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Description
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<textarea name='description' class='tinymceEditor'>".(isset($row['description']) ? $row['description'] : "")."</textarea>";
			echo "</div>";
		echo "</div>"; //Description

		//SEO Content/Analysis
		if($cms_settings['enhanced_seo'] && (array_key_exists(3, $Account->roles) || array_key_exists(4, $Account->roles))){ //SEO and master permissions
			echo "<div class='tabs tab-ui'>";
				echo "<ul>";
					echo "<li><a href='#seo'>SEO Content</a></li>";
					echo (ITEM_ID != "" ? "<li><a href='#seoanalysis'>Page Analysis</a></li>" : "");
				echo "</ul>";
		
			//SEO
			echo "<div id='seo' class='clearfix'>";
			
				$page_url = $siteurl.$root.$awards_page.(ITEM_ID != "" ? $row['page']."-".$row[$record_id]."/" : "");
				$default_meta_title = (ITEM_ID != "" ? $row['name']." | " : "").(trim($global['meta_title']) != "" ? $global['meta_title'] : $global['company_name']);
				$maxchars = 160;

				echo "<div class='form-field'>
					<label>Focus Keyword " .$CMSBuilder->tooltip('Focus Keyword', 'The focus keyword will be used to determine the SEO ranking in the Page Analysis tab. It is recommended to use a unique keyword that is not common to any other page.'). "</label>
					<input type='text' name='focus_keyword' value='" .(isset($row['focus_keyword']) ? $row['focus_keyword'] : ''). "' class='input' />
					<label>SEO Title " .$CMSBuilder->tooltip('SEO Title', 'A keyword-rich page title that will appear at the very top of your browser window.'). "</label>
					<input id='seo-title' type='text' name='meta_title' value='" .(isset($row['meta_title']) && $row['meta_title'] != "" ? $row['meta_title'] : ''). "' class='input' />";
					echo "<label>SEO Description" .$CMSBuilder->tooltip('SEO Description', 'A keyword-rich description of the page used for search engine optimization. Will default to global website settings description if left blank.'). "</label>
					<textarea id='seo-description' name='meta_description' class='textarea'>" .(isset($row['meta_description']) ? $row['meta_description'] : ''). "</textarea>
				</div>";
				
				echo "<div class='google-preview seo-preview'>
					<p>This Page in Google Search Results:</p>
					<div>";
						echo "<h2 class='seo-title'>" .(isset($row['meta_title']) && $row['meta_title'] != "" ? $row['meta_title'] : $default_meta_title). "</h2>";
						echo "<h6 class='seo-slug'>" .$page_url. "</h6>";
						echo "<p class='seo-description'>".(isset($row['meta_description']) ? substr($row['meta_description'], 0, 160)." &hellip;" : (isset($row['description']) ? substr($row['description'], 0, 160)." &hellip;" : ""))."</p>";
						echo "<input id='default-url' type='hidden' name='default-url' value='".$page_url."' />";
						echo "<input id='default-meta-title' type='hidden' name='default-meta-title' value='".$default_meta_title."' />";
					echo "</div>
				</div>";
											
			echo "</div>";//SEO
			
			if(ITEM_ID != "") {
				echo "<div id='seoanalysis' class='clearfix'>";
					//get appropriate page url
					$row['page_title'] = $row['name'];
					$row['page_id'] = $row[$record_id];
					$row['slug'] = NULL;
					$item_table = $record_db;
					$item_key = $record_id;
					include("includes/widgets/seosummary.php");
				echo "</div>"; //seoanalysis
			}
			
			echo "</div>";//tab-iu SEO
		}//SEO permissions

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "<input type='hidden' name='keep_tags[]' value='description' />";

	echo "</form>";

}
?>