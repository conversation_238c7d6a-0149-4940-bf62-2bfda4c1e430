@charset "utf-8";
/* 
	temp.less
*/
/*------ imports ------*/
blockquote {
  font-size: var(--font-h6);
}
.partner-gallary .panel.partners,
.partner-gallary .panel.staff {
  --image-width: 150px;
  --image-width: clamp(150px, 0vw + 150px, 150px);
}
.partner-gallary .panel.partners:not(.gallery-listings) .panel-gallery:not(:only-child),
.partner-gallary .panel.staff:not(.gallery-listings) .panel-gallery:not(:only-child) {
  margin-top: 20px;
  margin-top: clamp(20px, 1.66945vw + 7.17863px, 30px);
}
.partner-gallary .panel.partners .light-gallery,
.partner-gallary .panel.staff .light-gallery {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row;
          flex-flow: row;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  gap: 1px;
  overflow: auto hidden;
}
.partner-gallary .panel.partners .light-gallery > .gal-item,
.partner-gallary .panel.staff .light-gallery > .gal-item {
  width: var(--image-width);
  -webkit-box-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
.partner-gallary .panel.partners .light-gallery.swiper,
.partner-gallary .panel.staff .light-gallery.swiper {
  display: block;
  overflow: hidden;
}
.partner-gallary .panel.partners .light-gallery.swiper .swiper-slide,
.partner-gallary .panel.staff .light-gallery.swiper .swiper-slide {
  width: var(--image-width);
}
.partner-gallary .panel.partners .swiper-wrapper,
.partner-gallary .panel.staff .swiper-wrapper {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.partner-gallary .panel.partners .swiper-scrollbar,
.partner-gallary .panel.staff .swiper-scrollbar {
  display: none;
  position: relative;
  margin-top: 20px;
  margin-top: clamp(20px, 6.6778vw - 31.28548px, 60px);
  margin-inline: auto;
  left: auto;
  right: auto;
  bottom: auto;
  width: calc(100% - var(--container-padding) * 2);
  height: 10px;
  max-width: 470px;
  background: none;
}
.partner-gallary .panel.partners .swiper-scrollbar::before,
.partner-gallary .panel.staff .swiper-scrollbar::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  margin: auto;
  background-color: #B3C6BB;
  content: '';
}
.partner-gallary .panel.partners .swiper-scrollbar .swiper-scrollbar-drag,
.partner-gallary .panel.staff .swiper-scrollbar .swiper-scrollbar-drag {
  border-radius: 3px;
  cursor: -webkit-grab;
  cursor: grab;
  background-color: #EF3E34;
  -webkit-transition: background-color 0.3s ease 0s;
  transition: background-color 0.3s ease 0s;
}
.partner-gallary .panel.partners .swiper-scrollbar .swiper-scrollbar-drag:hover,
.partner-gallary .panel.staff .swiper-scrollbar .swiper-scrollbar-drag:hover,
.partner-gallary .panel.partners .swiper-scrollbar .swiper-scrollbar-drag:focus,
.partner-gallary .panel.staff .swiper-scrollbar .swiper-scrollbar-drag:focus {
  background-color: #16212F;
}
.partner-gallary .panel.partners,
.partner-gallary .panel.staff,
.partner-gallary .panel.event {
  margin: 10px 0;
  padding: 50px 0;
}
.partner-gallary .panel.partners .container,
.partner-gallary .panel.staff .container,
.partner-gallary .panel.event .container {
  z-index: 20;
}
.partner-gallary .panel.partners .slick-track,
.partner-gallary .panel.staff .slick-track,
.partner-gallary .panel.event .slick-track {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  margin: 0 auto;
}
.partner-gallary .panel.partners .panel-carousel.card-items.simple,
.partner-gallary .panel.staff .panel-carousel.card-items.simple,
.partner-gallary .panel.event .panel-carousel.card-items.simple {
  margin-top: 40px;
  margin-bottom: 0;
}
.partner-gallary .panel.partners .slick-navigation,
.partner-gallary .panel.staff .slick-navigation,
.partner-gallary .panel.event .slick-navigation {
  margin-top: -78px;
  text-align: right;
  visibility: visible;
  position: relative;
}
.partner-gallary .gal-item {
  position: relative;
  padding: 20px;
}
.partner-gallary .gal-item a,
.partner-gallary .gal-item img {
  width: 100%;
  display: block;
}
.partner-gallary .gal-item .gal-link .overlay {
  background: none !important;
  z-index: 0;
  opacity: 0;
}
.partner-gallary .gal-item .gal-link::after {
  content: '' !important;
}
.partner-gallary .gal-item .gal-link:hover .overlay {
  opacity: 0.5;
}
.partner-gallary .gal-item .gal-link:hover::after {
  opacity: 1;
}
.job-posting-form .member_classification,
#job-posting .member_classification,
#classified-posting .member_classification {
  -ms-grid-columns: (1fr)[5];
  grid-template-columns: repeat(5, 1fr);
}
.job-posting-form .job_des,
#job-posting .job_des,
#classified-posting .job_des {
  -ms-grid-columns: (1fr)[1];
  grid-template-columns: repeat(1, 1fr);
}
.job-posting-form .form-buttons,
#job-posting .form-buttons,
#classified-posting .form-buttons {
  float: right;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.job-posting-form .input-file-container,
#job-posting .input-file-container,
#classified-posting .input-file-container {
  position: relative;
  width: 100%;
}
.job-posting-form .input-file,
#job-posting .input-file,
#classified-posting .input-file {
  display: none;
  /* Hide the actual file input */
}
.job-posting-form .input-file-trigger,
#job-posting .input-file-trigger,
#classified-posting .input-file-trigger {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  color: #fff;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid #ccc;
  cursor: pointer;
  font-size: 14px;
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}
.job-posting-form .input-file-trigger i,
#job-posting .input-file-trigger i,
#classified-posting .input-file-trigger i {
  background-color: #e74c3c;
  border-radius: 50%;
  padding: 8px;
  margin-left: 10px;
  color: #fff;
  font-size: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 32px;
  height: 32px;
}
.job-posting-form .input-file-trigger.required::after,
#job-posting .input-file-trigger.required::after,
#classified-posting .input-file-trigger.required::after {
  content: "*";
  color: red;
  margin-left: 6px;
}
.job-posting-form small,
#job-posting small,
#classified-posting small {
  float: right;
}
.job-posting-form .checkbox,
#job-posting .checkbox,
#classified-posting .checkbox,
.job-posting-form .radio,
#job-posting .radio,
#classified-posting .radio {
  display: none;
}
.job-posting-form .checkbox + label,
#job-posting .checkbox + label,
#classified-posting .checkbox + label,
.job-posting-form .radio + label,
#job-posting .radio + label,
#classified-posting .radio + label {
  position: relative;
  display: inline-block;
  padding: 1px 10px 1px 26px;
  cursor: pointer;
  line-height: 20px !important;
  width: auto !important;
  margin-bottom: 5px;
  font-weight: 400;
}
.job-posting-form .checkbox + label:before,
#job-posting .checkbox + label:before,
#classified-posting .checkbox + label:before,
.job-posting-form .radio + label:before,
#job-posting .radio + label:before,
#classified-posting .radio + label:before {
  font-family: FontAwesome;
  display: inline-block;
  font-size: 18px;
  font-style: normal;
  line-height: 18px;
  position: absolute;
  left: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ccc;
  text-align: center;
  color: #16212F;
  background: #FFFFFF;
}
.job-posting-form .radio + label:before,
#job-posting .radio + label:before,
#classified-posting .radio + label:before {
  border-radius: 50%;
  font-size: 10px;
  text-indent: 1px;
}
.job-posting-form .checkbox + label:before,
#job-posting .checkbox + label:before,
#classified-posting .checkbox + label:before,
.job-posting-form .radio + label:before,
#job-posting .radio + label:before,
#classified-posting .radio + label:before {
  content: " ";
}
.job-posting-form .checkbox:checked + label:before,
#job-posting .checkbox:checked + label:before,
#classified-posting .checkbox:checked + label:before {
  content: "\f00c";
}
.job-posting-form .radio:checked + label:before,
#job-posting .radio:checked + label:before,
#classified-posting .radio:checked + label:before {
  content: "\f111";
}
.job-posting-form .checkbox:disabled + label:before,
#job-posting .checkbox:disabled + label:before,
#classified-posting .checkbox:disabled + label:before,
.job-posting-form .radio:disabled + label:before,
#job-posting .radio:disabled + label:before,
#classified-posting .radio:disabled + label:before {
  background-color: #eee;
}
.color-red {
  color: #EF3E34;
}
#job-browse .container {
  margin-top: 40px;
  margin-top: clamp(40px, 3.3389vw + 14.35726px, 60px);
}
#job-browse .dropdown-checkbox-wrapper {
  width: auto;
  margin: 0 auto 10px;
  font-size: 18px;
  color: #999999;
  background-color: #FFFFFF;
  overflow: hidden;
}
#job-browse .dropdown-checkbox-wrapper .title,
#job-browse .dropdown-checkbox-wrapper .checklist {
  display: block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 10px 20px;
}
@media all and (max-width: 480px) {
  #job-browse .dropdown-checkbox-wrapper .title,
  #job-browse .dropdown-checkbox-wrapper .checklist {
    display: inline;
  }
}
#job-browse .dropdown-checkbox-wrapper .title {
  cursor: pointer;
  width: 91%;
}
#job-browse .dropdown-checkbox-wrapper .checklist {
  display: none;
  list-style-type: none;
  margin: 0;
  padding-top: 0;
  width: 100%;
  overflow: hidden;
  max-height: 400px;
  overflow-y: auto;
}
#job-browse .dropdown-checkbox-wrapper .checklist li {
  margin: 0 0 0 5px;
}
#job-browse .dropdown-checkbox-wrapper .checklist li label {
  width: 100% !important;
  display: inline-block;
  vertical-align: middle;
}
#job-browse .dropdown-checkbox-wrapper.compact .checklist li {
  width: calc(50% - 10px);
  margin: 0 5px 10px 5px;
  float: left;
}
#job-browse .dropdown-checkbox-wrapper.compact .checklist li:nth-child(even) {
  margin: 0 5px 10px 5px;
}
#job-browse .dropdown-checkbox-wrapper .checkbox,
#job-browse .dropdown-checkbox-wrapper .radio {
  display: none;
}
#job-browse .dropdown-checkbox-wrapper .checkbox + label,
#job-browse .dropdown-checkbox-wrapper .radio + label {
  position: relative;
  display: inline-block;
  padding: 1px 10px 1px 26px;
  cursor: pointer;
  line-height: 20px !important;
  width: auto !important;
  margin-bottom: 5px;
  font-weight: 400;
}
#job-browse .dropdown-checkbox-wrapper .checkbox + label:before,
#job-browse .dropdown-checkbox-wrapper .radio + label:before {
  font-family: FontAwesome;
  display: inline-block;
  font-size: 18px;
  font-style: normal;
  line-height: 18px;
  position: absolute;
  left: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ccc;
  text-align: center;
  color: #16212F;
  background: #FFFFFF;
}
#job-browse .dropdown-checkbox-wrapper .radio + label:before {
  border-radius: 50%;
  font-size: 10px;
  text-indent: 1px;
}
#job-browse .dropdown-checkbox-wrapper .checkbox + label:before,
#job-browse .dropdown-checkbox-wrapper .radio + label:before {
  content: " ";
}
#job-browse .dropdown-checkbox-wrapper .checkbox:checked + label:before {
  content: "\f00c";
}
#job-browse .dropdown-checkbox-wrapper .radio:checked + label:before {
  content: "\f111";
}
#job-browse .dropdown-checkbox-wrapper .checkbox:disabled + label:before,
#job-browse .dropdown-checkbox-wrapper .radio:disabled + label:before {
  background-color: #eee;
}
#job-browse .button.primary.light {
  color: #000000;
  --border-color-light: #000000;
  --border-color: #000000;
}
#job-browse .radio-buttons .radio + label:before {
  border-radius: 0%;
}
#job-browse .radio-buttons small {
  color: #000000;
  font-family: "Poppins", sans-serif;
}
#panel-60 .container {
  margin-top: 40px;
  margin-top: clamp(40px, 3.3389vw + 14.35726px, 60px);
}
#panel-60 #classifieds {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  --gap-x: 30px;
  --gap-x: clamp(30px, 2.23214vw + 7.14286px, 50px);
  gap: 0 var(--gap-x);
}
#panel-60 #filter-form {
  width: 290px;
  margin-bottom: 0;
}
#panel-60 #filter-form .button {
  margin-right: 10px;
}
@media all and (max-width: 480px) {
  #panel-60 #filter-form {
    width: 100%;
  }
}
#panel-60 .classifeids-listings-wrapper {
  width: calc(100% - 290px - var(--gap-x));
}
#panel-60 .classifeids-listings-wrapper .listing {
  padding: 10px 20px;
  border: 1px solid #CCCCCC;
  margin: 0 auto 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
#panel-60 .classifeids-listings-wrapper .listing p {
  padding: 0px;
}
#panel-60 .classifeids-listings-wrapper .listing .job-title {
  color: #000000;
}
@media all and (max-width: 480px) {
  #panel-60 .classifeids-listings-wrapper {
    width: 100%;
  }
}
#panel-60 .dropdown-checkbox-wrapper {
  width: auto;
  margin: 0 auto 10px;
  font-size: 18px;
  color: #999999;
  background-color: #FFFFFF;
  overflow: hidden;
}
#panel-60 .dropdown-checkbox-wrapper .title,
#panel-60 .dropdown-checkbox-wrapper .checklist {
  display: block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 10px 20px;
}
@media all and (max-width: 480px) {
  #panel-60 .dropdown-checkbox-wrapper .title,
  #panel-60 .dropdown-checkbox-wrapper .checklist {
    display: inline;
  }
}
#panel-60 .dropdown-checkbox-wrapper .title {
  cursor: pointer;
  width: 91%;
}
#panel-60 .dropdown-checkbox-wrapper .checklist {
  display: none;
  list-style-type: none;
  margin: 0;
  padding-top: 0;
  width: 100%;
  overflow: hidden;
  max-height: 400px;
  overflow-y: auto;
}
#panel-60 .dropdown-checkbox-wrapper .checklist li {
  margin: 0 0 0 5px;
}
#panel-60 .dropdown-checkbox-wrapper .checklist li label {
  width: 100% !important;
  display: inline-block;
  vertical-align: middle;
}
#panel-60 .dropdown-checkbox-wrapper.compact .checklist li {
  width: calc(50% - 10px);
  margin: 0 5px 10px 5px;
  float: left;
}
#panel-60 .dropdown-checkbox-wrapper.compact .checklist li:nth-child(even) {
  margin: 0 5px 10px 5px;
}
#panel-60 .dropdown-checkbox-wrapper .checkbox,
#panel-60 .dropdown-checkbox-wrapper .radio {
  display: none;
}
#panel-60 .dropdown-checkbox-wrapper .checkbox + label,
#panel-60 .dropdown-checkbox-wrapper .radio + label {
  position: relative;
  display: inline-block;
  padding: 1px 10px 1px 26px;
  cursor: pointer;
  line-height: 20px !important;
  width: auto !important;
  margin-bottom: 5px;
  font-weight: 400;
}
#panel-60 .dropdown-checkbox-wrapper .checkbox + label:before,
#panel-60 .dropdown-checkbox-wrapper .radio + label:before {
  font-family: FontAwesome;
  display: inline-block;
  font-size: 18px;
  font-style: normal;
  line-height: 18px;
  position: absolute;
  left: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ccc;
  text-align: center;
  color: #16212F;
  background: #FFFFFF;
}
#panel-60 .dropdown-checkbox-wrapper .radio + label:before {
  border-radius: 50%;
  font-size: 10px;
  text-indent: 1px;
}
#panel-60 .dropdown-checkbox-wrapper .checkbox + label:before,
#panel-60 .dropdown-checkbox-wrapper .radio + label:before {
  content: " ";
}
#panel-60 .dropdown-checkbox-wrapper .checkbox:checked + label:before {
  content: "\f00c";
}
#panel-60 .dropdown-checkbox-wrapper .radio:checked + label:before {
  content: "\f111";
}
#panel-60 .dropdown-checkbox-wrapper .checkbox:disabled + label:before,
#panel-60 .dropdown-checkbox-wrapper .radio:disabled + label:before {
  background-color: #eee;
}
#panel-60 .button.primary.light {
  color: #000000;
  --border-color-light: #000000;
  --border-color: #000000;
}
#panel-60 .radio-buttons .radio + label:before {
  border-radius: 0%;
}
#panel-60 .radio-buttons small {
  color: #000000;
  font-family: "Poppins", sans-serif;
}
@media all and (min-width: 1025px) {
  #panel-60 .container {
    --container-max-width: var(--container-width);
  }
  #panel-60 #filter-form {
    border: none;
  }
  #panel-60 #filter-form .input,
  #panel-60 #filter-form .title {
    height: 55px;
    line-height: 34px;
  }
  #panel-60 #filter-form .title {
    margin: 0;
  }
  #panel-60 #filter-form .button {
    height: auto;
  }
  #panel-60 #filter-form .back {
    padding-left: 20px;
    line-height: 53px;
  }
  #panel-60 #filter-form .nopadding {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  #panel-60 #filter-form .search-classified {
    border: 1px solid #ccc;
    margin-bottom: 10px;
  }
  #panel-60 #filter-form .search-classified input {
    border: none;
    width: 80%;
  }
  #panel-60 #filter-form .search-classified .button {
    width: 20%;
  }
  #panel-60 .form-buttons {
    margin-top: 20px;
  }
  #panel-60 .divider {
    margin: 0 5px;
    display: inline-block;
  }
  #panel-60 .found-text {
    background-color: rgba(22, 33, 47, 0.2);
  }
  #panel-60 form fieldset input.side,
  #panel-60 form fieldset label.side {
    float: left;
    width: calc(100% - 60px);
    font-size: 16px;
    font-weight: 400;
    color: #000000;
  }
  #panel-60 form fieldset input.side + .side,
  #panel-60 form fieldset label.side + .side {
    margin-bottom: 0;
    float: right;
    line-height: 53px;
    padding: 0;
  }
  #panel-60 form fieldset input.side + .side .fa,
  #panel-60 form fieldset label.side + .side .fa {
    margin: auto;
    left: 0;
    top: 0;
    -webkit-transition: top 0.3s, -webkit-transform 0.3s;
    transition: top 0.3s, -webkit-transform 0.3s;
    transition: top 0.3s, transform 0.3s;
    transition: top 0.3s, transform 0.3s, -webkit-transform 0.3s;
  }
  #panel-60 form fieldset input.side + .side.active,
  #panel-60 form fieldset label.side + .side.active {
    -webkit-transform: scaleY(-1);
            transform: scaleY(-1);
    top: 0;
  }
  #panel-60 .dropdown-checkbox-wrapper {
    width: auto;
    margin: 0 auto 10px;
    font-size: 18px;
    color: #999999;
    background-color: #FFFFFF;
    overflow: hidden;
    border: 1px solid #ccc;
  }
  #panel-60 .dropdown-checkbox-wrapper .title,
  #panel-60 .dropdown-checkbox-wrapper .checklist {
    display: block;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    padding: 10px 20px;
  }
  #panel-60 .dropdown-checkbox-wrapper .title {
    cursor: pointer;
  }
  #panel-60 .dropdown-checkbox-wrapper .checklist {
    display: none;
    list-style-type: none;
    margin: 0;
    padding-top: 0;
    width: 100%;
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
  }
  #panel-60 .dropdown-checkbox-wrapper .checklist li {
    margin: 0 0 0 5px;
  }
  #panel-60 .dropdown-checkbox-wrapper .checklist li label {
    width: 100% !important;
    display: inline-block;
    vertical-align: middle;
  }
  #panel-60 .dropdown-checkbox-wrapper.compact .checklist li {
    width: calc(50% - 10px);
    margin: 0 5px 10px 5px;
    float: left;
  }
  #panel-60 .dropdown-checkbox-wrapper.compact .checklist li:nth-child(even) {
    margin: 0 5px 10px 5px;
  }
}
@media all and (min-width: 1025px) and all and (max-width: 480px) {
  #panel-60 #filter-form .search-classified {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}
@media all and (min-width: 1025px) and all and (min-width: 481px) {
  #panel-60 #application-form .form-buttons {
    text-align: center;
  }
  #panel-60 #application-form .form-buttons .button {
    float: none;
    display: inline-block;
  }
  #panel-60 #application-form .form-buttons .g-recaptcha {
    float: none;
    -webkit-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
@media all and (max-width: 480px) {
  .search-classified {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}
@media all and (min-width: 1025px) {
  #job-browse .container {
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
            flex-flow: row wrap;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    --gap-x: 30px;
    --gap-x: clamp(30px, 2.23214vw + 7.14286px, 50px);
    gap: 0 var(--gap-x);
    --container-max-width: var(--container-width);
  }
  #job-browse #job-filters-wrapper {
    width: 290px;
    margin-bottom: 0;
  }
  #job-browse #job-filters-wrapper .button {
    margin-right: 10px;
  }
  #job-browse #job-listings-wrapper {
    width: calc(100% - 290px - var(--gap-x));
  }
  #job-browse #job-listings-wrapper .listing {
    padding: 10px 20px;
    border: 1px solid #CCCCCC;
    margin: 0 auto 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  #job-browse #job-listings-wrapper .listing p {
    padding: 0px;
  }
  #job-browse #job-listings-wrapper .listing .job-title {
    color: #000000;
  }
  #job-browse #filter-form {
    border: none;
  }
  #job-browse #filter-form .input,
  #job-browse #filter-form .title {
    height: 55px;
    line-height: 34px;
  }
  #job-browse #filter-form .title {
    margin: 0;
  }
  #job-browse #filter-form .button {
    height: auto;
  }
  #job-browse #filter-form .back {
    padding-left: 20px;
    line-height: 53px;
  }
  #job-browse #filter-form .nopadding {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  #job-browse #filter-form .search-job {
    border: 1px solid #ccc;
    margin-bottom: 10px;
  }
  #job-browse #filter-form .search-job input {
    border: none;
    width: 80%;
  }
  #job-browse #filter-form .search-job .button {
    width: 20%;
  }
  #job-browse .form-buttons {
    margin-top: 20px;
  }
  #job-browse .divider {
    margin: 0 5px;
    display: inline-block;
  }
  #job-browse .found-text {
    background-color: rgba(22, 33, 47, 0.2);
  }
  #job-browse form fieldset input.side,
  #job-browse form fieldset label.side {
    float: left;
    width: calc(100% - 60px);
    font-size: 16px;
    font-weight: 400;
    color: #000000;
  }
  #job-browse form fieldset input.side + .side,
  #job-browse form fieldset label.side + .side {
    margin-bottom: 0;
    float: right;
    line-height: 53px;
    padding: 0;
  }
  #job-browse form fieldset input.side + .side .fa,
  #job-browse form fieldset label.side + .side .fa {
    margin: auto;
    left: 0;
    top: 0;
    -webkit-transition: top 0.3s, -webkit-transform 0.3s;
    transition: top 0.3s, -webkit-transform 0.3s;
    transition: top 0.3s, transform 0.3s;
    transition: top 0.3s, transform 0.3s, -webkit-transform 0.3s;
  }
  #job-browse form fieldset input.side + .side.active,
  #job-browse form fieldset label.side + .side.active {
    -webkit-transform: scaleY(-1);
            transform: scaleY(-1);
    top: 0;
  }
  #job-browse .dropdown-checkbox-wrapper {
    width: auto;
    margin: 0 auto 10px;
    font-size: 18px;
    color: #999999;
    background-color: #FFFFFF;
    overflow: hidden;
    border: 1px solid #ccc;
  }
  #job-browse .dropdown-checkbox-wrapper .title,
  #job-browse .dropdown-checkbox-wrapper .checklist {
    display: block;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    padding: 10px 20px;
  }
  #job-browse .dropdown-checkbox-wrapper .title {
    cursor: pointer;
  }
  #job-browse .dropdown-checkbox-wrapper .checklist {
    display: none;
    list-style-type: none;
    margin: 0;
    padding-top: 0;
    width: 100%;
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
  }
  #job-browse .dropdown-checkbox-wrapper .checklist li {
    margin: 0 0 0 5px;
  }
  #job-browse .dropdown-checkbox-wrapper .checklist li label {
    width: 100% !important;
    display: inline-block;
    vertical-align: middle;
  }
  #job-browse .dropdown-checkbox-wrapper.compact .checklist li {
    width: calc(50% - 10px);
    margin: 0 5px 10px 5px;
    float: left;
  }
  #job-browse .dropdown-checkbox-wrapper.compact .checklist li:nth-child(even) {
    margin: 0 5px 10px 5px;
  }
}
@media all and (min-width: 1025px) and all and (min-width: 481px) {
  #job-browse #application-form .form-buttons {
    text-align: center;
  }
  #job-browse #application-form .form-buttons .button {
    float: none;
    display: inline-block;
  }
  #job-browse #application-form .form-buttons .g-recaptcha {
    float: none;
    -webkit-transform-origin: 50% 0;
    transform-origin: 50% 0;
  }
}
#careers .full-column {
  overflow: hidden;
}
#careers .left-column {
  position: relative;
  z-index: 1;
  width: 88%;
  float: left;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding-right: 250px;
}
#careers .left-column .form-buttons .button ~ .secondary {
  margin-left: 8px;
}
#careers .right-column {
  position: relative;
  z-index: 2;
  height: auto;
  float: right;
  padding-left: 40px;
  margin-left: -100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-left: 1px solid #16212F;
  background: none;
}
#careers .right-column.sticky {
  height: 100%;
}
#careers .right-column.stuck {
  position: fixed;
  float: none;
  top: 0;
  height: 100%;
  margin-left: 900px;
  padding: 100px 40px 50px 0;
}
#careers .right-column.bottom {
  position: absolute;
  float: none;
  top: auto;
  bottom: 0;
  height: auto;
  margin-left: 900px;
  padding: 0 40px 100px 0;
}
@media screen and (max-width: 1440px) {
  #careers .right-column.stuck {
    margin: 0;
    padding-right: 0;
    padding-left: 40px;
    right: 30px;
  }
  #careers .right-column.bottom {
    margin: 0;
    padding: 0 0 100px 40px;
    right: 30px;
  }
}
.checkbox:checked + label:after {
  content: "";
}
.success {
  background-color: #dff0d8;
  border: 1px solid #d6e9c6;
  color: #3c763d;
  padding: 15px;
  margin-bottom: 20px;
}
.error {
  background-color: #f2dede;
  border: 1px solid #ebccd1;
  color: #a94442;
  padding: 15px;
  margin-bottom: 20px;
}
#search-bar .input {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  width: 100%;
  height: 45px;
  padding: 10px 75px 10px 15px;
  border: 1px solid #000000;
  border-radius: 5px;
  background-color: #FFFFFF;
  color: #000000;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
#search-bar .input::-webkit-input-placeholder {
  color: #666666;
}
#search-bar .input::-moz-placeholder {
  color: #666666;
}
#search-bar .input:-ms-input-placeholder {
  color: #666666;
}
#search-bar .input::-ms-input-placeholder {
  color: #666666;
}
#search-bar .input::placeholder {
  color: #666666;
}
#search-bar .clear-search-btn {
  position: absolute;
  right: 40px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  text-decoration: none;
  font-size: 1.5em;
  color: #666666;
  cursor: pointer;
  line-height: 1;
  padding: 0 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
#search-bar .clear-search-btn:hover {
  color: #000000;
}
#search-bar .button {
  position: absolute;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
  color: #EF3E34;
  font-size: 1.2em;
  line-height: 1;
}
#search-bar .button:hover {
  color: #de1c12;
}
#search-bar .button i {
  display: block;
}
#job-postings-table td {
  padding: 0;
}
#classified-table tbody td {
  padding: 0;
}
.media_center {
  border: none !important;
}
.media_center .media img:where([width][height]) {
  height: revert-layer;
  width: -webkit-fill-available;
}
@media all and (max-width: 480px) {
  .media_center .media img:where([width][height]) {
    -o-object-fit: cover;
       object-fit: cover;
  }
}
blockquote {
  position: relative;
  margin-top: 20px;
  margin-left: 10px;
  margin-left: clamp(0px, -3.90625vw + 40px, 10px);
  margin-bottom: 20px;
  line-height: var(--line-height-normal);
  z-index: 0;
  font-style: italic;
  letter-spacing: -0.02em;
}
blockquote small {
  font-weight: 400;
  font-style: normal;
}
blockquote::before {
  content: "\201c";
  position: absolute;
  top: -25px;
  left: -25px;
  display: block;
  padding-right: 10px;
  font-size: 150px;
  font-family: "Lora", sans-serif;
  line-height: 0.7;
  z-index: -1;
  color: #FFFFFF;
  text-shadow: 0px 2px 2px #16212F;
  background: #FFFFFF;
}
@media all and (max-width: 480px) {
  blockquote::before {
    left: -27px;
    font-size: 70px;
  }
}
#panel-63 .panel-text {
  max-width: 100%;
}
#award_winners {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  --gap-x: 30px;
  --gap-x: clamp(30px, 2.23214vw + 7.14286px, 50px);
  gap: 0 var(--gap-x);
}
#sub-navigation {
  width: 290px;
  margin-bottom: 0;
}
#sub-navigation .button {
  margin-right: 10px;
}
#sub-navigation h4 {
  font-size: 24px;
}
.center {
  width: calc(100% - 290px - var(--gap-x));
}
@media all and (max-width: 480px) {
  .center {
    width: 100%;
  }
}
.center h4,
.center h3 {
  font-size: 24px;
}
.winners_list {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: left;
      -ms-flex-pack: left;
          justify-content: left;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
@media all and (max-width: 480px) {
  .winners_list {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.personnel {
  display: -moz-inline-box;
  display: inline-block;
  vertical-align: top;
  width: 170px;
  margin: 20px 10px;
  text-align: center;
}
.personnel .default-photo {
  display: table;
  width: 100%;
  height: 100%;
  color: #999999;
}
.personnel .default-photo span {
  display: table-cell;
  vertical-align: middle;
  font-style: normal;
  font-size: 38px;
}
.personnel .details {
  display: block;
}
.personnel .details h4 {
  margin-bottom: 5px;
  font-size: 20px;
}
.personnel .details h4 a {
  color: #000000;
}
.personnel .details h4 a:hover {
  color: #16212F;
}
.personnel .details span {
  color: #bfbfbf;
  font-size: 13px;
}
.personnel > div {
  display: table;
  table-layout: fixed;
}
.profile-photo {
  padding-bottom: 20px;
  text-align: right;
}
.profile-photo img {
  border: 1px solid #FFFFFF;
  width: 100%;
}
table.personnel-tbl {
  margin: 30px 0;
  width: 100%;
}
table.personnel-tbl td.description {
  padding: 40px;
}
.toggle .expander {
  display: inline-block;
  padding-top: 20px;
}
.toggle .expandable {
  height: 240px;
  overflow: hidden;
}
#contact-staff {
  margin: 20px 0 30px;
}
#contact-staff .personnel {
  display: table;
  width: 100%;
  margin: 20px 0 0;
}
#contact-staff .personnel .circular {
  display: table-cell;
  width: 100px;
  height: 100px;
}
#contact-staff .personnel .details {
  display: table-cell;
  text-align: left;
  vertical-align: middle;
  padding-left: 20px;
}
#contact-staff .personnel .details h4 {
  font-size: inherit;
  color: inherit;
  font-weight: bold;
  margin: 0;
}
@media all and (min-width: 769px) {
  .profile-photo {
    display: none;
  }
}
#sub-navigation ul {
  text-decoration: none;
  list-style: none;
  border: 1px solid #ccc;
  padding: 20px;
  margin: auto;
}
#sub-navigation ul li {
  border-bottom: 1px solid #ddd;
  padding: 15px;
}
@media all and (max-width: 480px) {
  #sub-navigation {
    display: none;
  }
}
#nav-menu {
  display: none;
}
@media all and (max-width: 480px) {
  #nav-menu {
    display: block;
  }
  #nav-menu #sub-navigation {
    display: none;
  }
}
.top_pgm {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  --gap-x: 30px;
  --gap-x: clamp(30px, 2.23214vw + 7.14286px, 50px);
  gap: 0 var(--gap-x);
}
.left_pgm {
  width: 290px;
  margin-bottom: 0;
}
.right_pgm {
  width: calc(100% - 290px - var(--gap-x));
}
@media all and (max-width: 480px) {
  .right_pgm {
    width: 100%;
  }
}
.right_pgm h4,
.right_pgm h3 {
  font-size: 24px;
}
.membership_cat {
  list-style-type: none;
}
.membership_cat li {
  width: auto;
  margin: 0 auto 10px;
  font-size: 18px;
  color: #999999;
  background-color: #FFFFFF;
  overflow: hidden;
  border: 1px solid #ccc;
  padding: 10px;
}
#panel-77 .content-tabs,
#panel-72 .content-tabs {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
          flex-flow: row wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  --gap-x: 30px;
  --gap-x: clamp(30px, 2.23214vw + 7.14286px, 50px);
  gap: 0 var(--gap-x);
  border: none !important;
}
#panel-77 .content-tabs .tabs-nav-wrapper,
#panel-72 .content-tabs .tabs-nav-wrapper {
  width: 290px;
  margin-bottom: 0;
}
#panel-77 .content-tabs .tabs-nav-wrapper li,
#panel-72 .content-tabs .tabs-nav-wrapper li {
  width: 100%;
  margin: 0 auto 10px;
  font-size: 18px;
  color: #999999;
  background-color: #FFFFFF;
  overflow: hidden;
  border: 1px solid #ccc;
  padding: 10px;
}
#panel-77 .content-tabs .tabs-nav-wrapper li a,
#panel-72 .content-tabs .tabs-nav-wrapper li a {
  background-color: transparent !important;
  border: none;
  color: #EF3E34;
}
#panel-77 .content-tabs .tabs-nav-wrapper li a:active,
#panel-72 .content-tabs .tabs-nav-wrapper li a:active,
#panel-77 .content-tabs .tabs-nav-wrapper li a:hover,
#panel-72 .content-tabs .tabs-nav-wrapper li a:hover {
  color: #EF3E34;
}
#panel-77 .content-tabs .tabs-panel,
#panel-72 .content-tabs .tabs-panel {
  width: calc(100% - 290px - var(--gap-x));
}
@media all and (max-width: 480px) {
  #panel-77 .content-tabs .tabs-panel,
  #panel-72 .content-tabs .tabs-panel {
    width: 100%;
  }
}
#panel-77 .content-tabs .tabs-panel h4,
#panel-72 .content-tabs .tabs-panel h4,
#panel-77 .content-tabs .tabs-panel h3,
#panel-72 .content-tabs .tabs-panel h3 {
  font-size: 24px;
}
#panel-77 .content-tabs .tabs-panel table,
#panel-72 .content-tabs .tabs-panel table {
  width: 100%;
  margin: 10px 0 30px;
  border: 1px solid white;
  border-collapse: collapse;
}
#panel-77 .content-tabs .tabs-panel table th,
#panel-72 .content-tabs .tabs-panel table th,
#panel-77 .content-tabs .tabs-panel table td,
#panel-72 .content-tabs .tabs-panel table td {
  background-color: #eeeeee;
}
#panel-77 .content-tabs .tabs-panel table h4,
#panel-72 .content-tabs .tabs-panel table h4 {
  font-size: 26px;
}
@media all and (max-width: 480px) {
  #panel-77 .content-tabs,
  #panel-72 .content-tabs {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
            flex-direction: column !important;
  }
}
#panel-77 .ui-state-disabled:active,
#panel-72 .ui-state-disabled:active,
#panel-77 .ui-button:focus,
#panel-72 .ui-button:focus {
  background: none !important;
}
#pd-search {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
#pd-search .form-field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
#pd-search .form-field .select {
  margin-left: 5px;
}
.parallax .panel-wrapper {
  text-align: -webkit-center !important;
}
#panel-89 table {
  width: 100%;
  margin: 10px 0 30px;
  border: 1px solid white;
  border-collapse: collapse;
}
#panel-89 table th,
#panel-89 table td {
  background-color: #eeeeee;
}
#panel-89 table h4 {
  font-size: 26px;
}
#compensation-survey {
  /*------ form styles ------*/
  /*------ slider ------*/
}
#compensation-survey fieldset {
  border: 1px solid #FFFFFF;
  margin-bottom: 20px;
}
#compensation-survey fieldset.required > .mce-tinymce {
  border: 1px solid #EF3E34;
}
#compensation-survey fieldset.required .mce-branding-powered-by {
  border-right: 1px solid #EF3E34;
}
#compensation-survey .form-column {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 48%;
}
#compensation-survey .form-column.f_left {
  padding-right: 5px;
}
#compensation-survey .form-column.f_right {
  padding-left: 5px;
}
#compensation-survey .form-column.half {
  width: 25%;
}
#compensation-survey .form-buttons {
  clear: both;
  margin-bottom: 10px;
}
#compensation-survey .form-buttons .button.f_left {
  margin-right: 5px;
}
#compensation-survey .form-buttons .button.f_right {
  margin-left: 5px;
}
#compensation-survey .form-uploader {
  overflow: hidden;
  padding-left: 20px;
}
#compensation-survey a.previous,
#compensation-survey a.addnew {
  line-height: 80px;
  padding: 0 30px;
}
#compensation-survey label {
  display: block;
  letter-spacing: 0;
  margin-bottom: 5px;
}
#compensation-survey .input,
#compensation-survey .select,
#compensation-survey .textarea {
  font-size: 18px;
  font-family: "Poppins", sans-serif;
  font-style: italic;
  color: #020202;
  letter-spacing: 0;
  text-shadow: none;
  font-weight: normal;
  background-color: #EEEEEE;
  border: 1px solid #EEEEEE;
  outline: none;
  height: 70px;
  width: 100%;
  padding: 10px 20px;
  margin-bottom: 10px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0px;
  -webkit-transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  -webkit-background-clip: border-box !important;
  -moz-background-clip: border-box !important;
  background-clip: border-box !important;
}
#compensation-survey .input::-webkit-input-placeholder,
#compensation-survey .select::-webkit-input-placeholder,
#compensation-survey .textarea::-webkit-input-placeholder {
  color: #020202;
  opacity: 1;
}
#compensation-survey .input:-moz-placeholder,
#compensation-survey .select:-moz-placeholder,
#compensation-survey .textarea:-moz-placeholder {
  color: #020202;
  opacity: 1;
}
#compensation-survey .input::-moz-placeholder,
#compensation-survey .select::-moz-placeholder,
#compensation-survey .textarea::-moz-placeholder {
  color: #020202;
  opacity: 1;
}
#compensation-survey .input:-ms-input-placeholder,
#compensation-survey .select:-ms-input-placeholder,
#compensation-survey .textarea:-ms-input-placeholder {
  color: #020202;
  opacity: 1;
}
#compensation-survey .input:focus::-webkit-input-placeholder,
#compensation-survey .select:focus::-webkit-input-placeholder,
#compensation-survey .textarea:focus::-webkit-input-placeholder {
  color: #eee !important;
}
#compensation-survey .input:focus:-moz-placeholder,
#compensation-survey .select:focus:-moz-placeholder,
#compensation-survey .textarea:focus:-moz-placeholder {
  color: #eee;
}
#compensation-survey .input:focus::-moz-placeholder,
#compensation-survey .select:focus::-moz-placeholder,
#compensation-survey .textarea:focus::-moz-placeholder {
  color: #eee;
}
#compensation-survey .input:focus:-ms-input-placeholder,
#compensation-survey .select:focus:-ms-input-placeholder,
#compensation-survey .textarea:focus:-ms-input-placeholder {
  color: #eee;
}
#compensation-survey .input.required,
#compensation-survey .select.required,
#compensation-survey .textarea.required {
  border-color: #EF3E34;
}
#compensation-survey .input.half,
#compensation-survey .select.half,
#compensation-survey .textarea.half {
  width: 49%;
}
#compensation-survey .select:focus {
  color: #020202 !important;
  background-color: #FFFFFF !important;
  border-color: #FFFFFF !important;
}
#compensation-survey .select:focus::-webkit-input-placeholder {
  color: #020202 !important;
}
#compensation-survey .select:focus:-moz-placeholder {
  color: #020202;
}
#compensation-survey .select:focus::-moz-placeholder {
  color: #020202;
}
#compensation-survey .select:focus:-ms-input-placeholder {
  color: #020202;
}
#compensation-survey p.jsvalidate.required {
  color: #EF3E34;
}
#compensation-survey .textarea {
  height: 230px;
  padding: 20px;
  resize: none;
}
#compensation-survey .texteditor {
  height: 400px;
}
#compensation-survey .select {
  background-image: url("/images/ui/select-arrow.png");
  background-position: right 32px;
  background-repeat: no-repeat;
  text-indent: 0.01px;
  text-overflow: "";
  padding-right: 34px;
}
#compensation-survey select::-ms-expand {
  display: none;
}
#compensation-survey :root .select {
  padding: 10px 20px 10px 20px \ ;
}
#compensation-survey .checkbox,
#compensation-survey .radio {
  display: none;
}
#compensation-survey .checkbox + label,
#compensation-survey .radio + label {
  position: relative;
  display: inline-block;
  padding: 1px 10px 1px 26px;
  cursor: pointer;
  line-height: 20px !important;
  width: auto !important;
  margin-bottom: 5px;
}
#compensation-survey .checkbox + label:before,
#compensation-survey .radio + label:before {
  font-family: FontAwesome;
  display: inline-block;
  font-size: 18px;
  font-style: normal;
  line-height: 18px;
  position: absolute;
  left: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #FFFFFF;
  text-align: center;
  color: #16212F;
  background: #FFFFFF;
}
#compensation-survey .radio + label:before {
  border-radius: 0px;
  font-size: 10px;
  text-indent: 1px;
}
#compensation-survey .checkbox + label:before,
#compensation-survey .radio + label:before {
  content: " ";
}
#compensation-survey .checkbox:checked + label:before {
  content: "\f00c";
}
#compensation-survey .radio:checked + label:before {
  content: "\f111";
}
#compensation-survey .checkbox:disabled + label:before,
#compensation-survey .radio:disabled + label:before {
  background-color: #eee;
}
#compensation-survey .input-file-container {
  display: block;
  position: relative;
  line-height: 28px;
}
#compensation-survey .input-file-trigger {
  display: block;
  overflow: hidden;
  padding: 20px;
  margin-bottom: 10px;
  height: 28px;
  background: #FFFFFF;
  color: inherit;
  font-size: 18px;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  border: 1px solid #FFFFFF;
  -webkit-transition: color 0.3s ease 0s;
  transition: color 0.3s ease 0s;
}
#compensation-survey .input-file-trigger .fa {
  margin-right: 10px;
}
#compensation-survey .input-file-trigger.required {
  border-color: #EF3E34;
}
#compensation-survey .ui-slider {
  position: relative;
  text-align: left;
}
#compensation-survey .ui-slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 1.2em;
  height: 1.2em;
  cursor: default;
  -ms-touch-action: none;
  touch-action: none;
}
#compensation-survey .ui-slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  font-size: 0.7em;
  display: block;
  border: 0;
  background-position: 0 0;
}
#compensation-survey .ui-slider.ui-state-disabled .ui-slider-handle,
#compensation-survey .ui-slider.ui-state-disabled .ui-slider-range {
  -webkit-filter: inherit;
          filter: inherit;
}
#compensation-survey .ui-slider-horizontal {
  height: 0.8em;
}
#compensation-survey .ui-slider-horizontal .ui-slider-handle {
  top: -0.3em;
  margin-left: -0.6em;
}
#compensation-survey .ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%;
}
#compensation-survey .ui-slider-horizontal .ui-slider-range-min {
  left: 0;
}
#compensation-survey .ui-slider-horizontal .ui-slider-range-max {
  right: 0;
}
#compensation-survey .input.slider-container {
  height: 24px;
  margin: 27.5px 0 33.5px;
  padding: 0;
  border-width: 1px 0;
}
#compensation-survey .input.slider-container .slider {
  height: 48px;
  margin: -13px 0 0;
  display: none;
  border: none;
  background: transparent;
}
#compensation-survey .input.slider-container .slider .handle {
  font-weight: bold;
  color: #fff;
  margin: 0 0 0 -24px;
  text-align: center;
  background: green;
  top: 0;
  padding: 0 10px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: auto;
  min-width: 48px;
  height: 100%;
  line-height: 48px;
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: background-color 0.3s, color 0.3s ease 0s;
  transition: background-color 0.3s, color 0.3s ease 0s;
}
#compensation-survey .input.slider-container .slider.ui-state-disabled {
  cursor: not-allowed;
}
#compensation-survey .input.slider-container .slider.ui-state-disabled .handle {
  background-color: #EF3E34;
  color: transparent;
  cursor: not-allowed;
}
#compensation-survey .input.slider-container .slider.ui-slider {
  display: block;
}
