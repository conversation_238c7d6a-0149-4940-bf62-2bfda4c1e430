<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['messages']){
	
	//Define vars
	$record_db = 'messages';
	$record_id = 'message_id';
	$record_name = 'Message';
	
	$filedir = "../docs/attachments/";
	$filetypes = array('pdf');
	$file = "";
	
	$errors = false;
	$required = array();
	
	//Get Records
	$records_arr = array();
	$params = array();
	if($searchterm != ""){
		$params[] = '%' .$searchterm. '%';
	}
	$query = $db->query("SELECT * FROM `$record_db`".($searchterm != "" ? " WHERE `subject` LIKE ?" : ""). " ORDER BY `date_added` DESC, `$record_id` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		$delete = $db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if($delete && !$db->error()){
			if($_POST['old_file'] != ''){
				if(file_exists($filedir.$_POST['old_file'])) {
					unlink($filedir.$_POST['old_file']);
				}
			}
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			sitemapXML();
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	
	//Save item
	}else if(isset($_POST['save'])){
		
		//Validate
		if(trim($_POST['subject']) == ''){
			$errors[] = 'Message subject is required.';
			$required[] = 'subject';
		}
		if(trim($_POST['content']) == ''){
			$errors[] = 'Message content is required.';
			$required[] = 'content';
		}
		if(!empty($_FILES['file']['size']) && $_FILES['file']['size'] > 20480000){
			$errors[] = 'File size is too large.';
		}
		if(!empty($_FILES['file']['name'])){
			$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
			if(!in_array($ext, $filetypes)){
				$errors[] = 'File type `' .$ext. '` is restricted. Allowed file types include '.implode(', ', $filetypes).'.';
			}
		}
		
		if(!$errors){
			
			//Upload file
			$file = ($_POST['old_file'] != '' ? $_POST['old_file'] : NULL);
			$uploaded = true;
			if(!empty($_FILES['file']['name'])){
				$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
				$filename = str_replace('.'.$ext, '', $_FILES['file']['name']);
				$newname = clean_url($filename).'-'.date('ymdhis').'.'.$ext;
				
				$fileUpload = new FileUpload();
				$fileUpload->load($_FILES['file']['tmp_name']);
				$fileUpload->save($filedir, $newname);
				if(file_exists($filedir.$newname)){
					$file = $newname;
					if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
						unlink($filedir.$_POST['old_file']);
					}
				}else{
					$uploaded = false;
				}
				
			}else{
				if(isset($_POST['deletefile']) && $_POST['old_file'] != ''){
					if(file_exists($filedir.$_POST['old_file'])) {
						unlink($filedir.$_POST['old_file']);
					}
					$file = NULL;
				}
			}
			
			$unsubscribe_code = get_unique_db_val($record_db, 'unsubscribe_code', 30);
			
			//Insert to db
			$params = array(
				ITEM_ID,
				$_POST['subject'],
				$_POST['content'],
				$file,
				$unsubscribe_code,
				date('Y-m-d H:i:s'),
				date('Y-m-d H:i:s'),
				USER_LOGGED_IN,
				
				$_POST['subject'],
				$_POST['content'],
				$file,
				date('Y-m-d H:i:s'),
				USER_LOGGED_IN
			);
			$query = $db->query("INSERT INTO `$record_db`(`$record_id`, `subject`, `content`, `filename`, `unsubscribe_code`, `date_added`, `last_updated`, `updated_by`) VALUES(?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `subject` = ?, `content` = ?, `filename` = ?, `last_updated` = ?, `updated_by` = ?", $params);
			if($query && !$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				if(!$uploaded){
					$CMSBuilder->set_system_alert('Attachment failed to upload.', false);
				}
				header("Location: " .PAGE_URL);
				exit();

			}else{
				$CMSBuilder->set_system_alert('Unable to save record. '.$db->error(), false);
			}
			
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);	
		}

		foreach($_POST AS $key=>$data){
			$row[$key] = $data;
		}
		
	}
}

?>