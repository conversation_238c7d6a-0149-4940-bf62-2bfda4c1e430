<?php

/*-----------------------------------/
* Handles the basic features of managing a registration system.
* This class is used to setup a Basic Registration system with features
* to manage Categories, Events, Promo Codes, Registrations, etc.
*
* <AUTHOR> Army
* @package	Core System
* @date		13-05-06
* @file		Registration.class.php
*/

class Registration{

	/*-----------------------------------/
	* @var reg_system_id
	* Unique ID that represents this registration system
	*/
	public $reg_id;

	/*-----------------------------------/
	* @var status
	* Represents the status of the registration system
	*/
	public $status;
	
	/*-----------------------------------/
	* @var package
	* Represents the package of the registration system
	* 'Platoon' or 'Battalion'
	*/
	public $package;
	
	/*-----------------------------------/
	* @var db
	* Mysqli database object
	*/
	public $db;

	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @return	Registration	New Registration object
	*/
	public function __construct($reg_system_id=1){
	
		//Set database instance
		if(class_exists('Database')){
			$this->db = new Database();
		}else{
			throw new Exception('Missing class file `Database`');
		}
		
		//Set account instance
		if(class_exists('Account')){
			$this->account = new Account();
		}else{
			throw new Exception('Missing class file `Account`');
		}
	
		//Set reg vars
		$this->account_id = ($this->account->login_status() ? $this->account->account_id : NULL);
		$this->reg_id = $reg_system_id;
		$this->status = $this->get_db_param('reg_settings','status','reg_system_id',$this->reg_id);
		$this->package = $this->get_db_param('reg_settings','package','reg_system_id',$this->reg_id);	
	}
	
	/*-----------------------------------/
	* Gets registration system settings
	*
	* <AUTHOR> Army
	* @return	Array	All information from settings table
	*/
	public function get_reg_settings(){
		$response = array();
		
		$params = array($this->reg_id);
		$query = $this->db->query("SELECT * FROM `reg_settings` WHERE `reg_system_id` = ?",$params);
		
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
			$response = $response[0];
		}
				
		return $response;
	}

	/*-----------------------------------/
	* Gets any database field
	*
	* <AUTHOR> Army
	* @param	$field	The field name to be returned
	* @return	mixed	The value of the requested field
	*/
	public function get_db_param($table, $field, $key, $value){
		$response = array();
		
		$params = array($value);
		$query = $this->db->query("SELECT `$field` FROM `$table` WHERE `$key` = ?", $params);
		if($query && !$this->db->error() && $this->db->num_rows()){
			$result = $this->db->fetch_array();
			$response = $result[0];
			return $response[$field];
		}
		
		return $response;
		
	}

	/*-----------------------------------/
	* Returns a list of the categories with the given parent category_id
	*
	* <AUTHOR> Army
	* @param	category_id		The parent category_id ('NULL' for the top-most categories)
	* @return	Array			An array of the results
	*/
	public function get_categories($parent_category_id=NULL){
		$response = array();
		
		$params = array(0, "Active", $this->reg_id);
		$query = $this->db->query("SELECT * FROM `reg_categories` WHERE `showhide` = ? && `status` = ? && `reg_system_id` = ? && (`parent_id` ".(is_null($parent_category_id) ? "IS NULL || `parent_id` = 0)" : "= $parent_category_id)"). " ORDER BY `ordering`", $params);
		if($query && !$this->db->error()){
			$result = $this->db->fetch_array();
			foreach($result as $category){
				array_push($response, $category);
			}
		}
		
		return $response;
	}
	
	/*-----------------------------------/
	* Returns specific category details
	*
	* <AUTHOR> Army
	* @param	category_id		The category id
	* @return	Array			An array of the results
	*/
	public function get_category($category_id){
		$response = array();
		
		$params = array(0, "Active", $category_id);
		$query = $this->db->query("SELECT * FROM `reg_categories` WHERE `showhide` = ? && `status` = ? && `category_id` = ?", $params);
		if($query && !$this->db->error()){
			$result = $this->db->fetch_array();
			$response = $result[0];
		}
						
		return $response; 
	}

	/*-----------------------------------/
	* Returns a list of the events with the given category_id
	*
	* <AUTHOR> Army
	* @param	category_id		The parent category_id
	* @param	sortby			The ordering condition to add to the query
	* @return	Array			An array of the results
	*/
	public function get_category_events($category_id, $lim = '', $sortby='reg_events.name, date_added DESC'){
		$response = array();
		
		$params = array(0, "Trashed", $category_id);
		$query = $this->db->query("SELECT `reg_events`.*, `reg_events`.`name` AS `event_name` FROM `reg_events` LEFT JOIN `reg_event_categories` ON `reg_events`.`event_id` = `reg_event_categories`.`event_id` WHERE `reg_events`.`showhide` = ? && `reg_events`.`status` != ? && `reg_event_categories`.`category_id` = ? GROUP BY `reg_events`.`event_id` ORDER BY $sortby".($lim != '' ? " LIMIT $lim" : ""), $params);
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
		}
		
		return $response;
	}
	
	/*-----------------------------------/
	* Returns a list of the occurrences with the given category_id
	*
	* <AUTHOR> Army
	* @param	status_arr		Array of occurrence statuses to filter by
	* @param	event_type		1 = Events 2 = Tournaments
	* @param	category_id		The parent category_id
	* @param	start_date		Date YYYY-MM-DD
	* @param	end_date		Date YYYY-MM-DD
	* @param	searchterm		String to search event and occurrence
	* @param	lim				Maximum number of rows to return
	* @param	sortby			The ordering condition to add to the query
	* @return	Array			An array of the results
	*/
	public function get_occurrences($status_arr=array(), $event_type=NULL, $category_id=NULL, $start_date=NULL, $end_date=NULL, $searchterm='', $lim='', $sortby='reg_occurrences.start_date ASC, reg_occurrences.end_date ASC'){
		$response = array();
		$params = array();
		$where = "";
		
		$member_category = NULL;
		$member_class = NULL;
		if(!is_null($this->account_id)){
			$member_category = $this->account->category_id;
			$member_class = $this->account->class_id;
		}
		
		$params[] = $member_category;
		$params[] = $member_class;
		$params[] = "Registered";
		$params[] = $this->account_id;
		$params[] = "Registered";
		$params[] = "Trashed";
		
		if(!empty($status_arr)){
			foreach($status_arr as $status){
				if($where != ""){
					$where .= " OR ";
				} else {
					$where .= "AND (";
				}
				$where .= "`reg_occurrences`.`occurrence_status` = ?";
				$params[] = $status;
			}
			$where .= ") ";
		}		
		if(!empty($event_type)){
			$where .= "AND `reg_events`.`event_type` = ? ";
			$params[] = $event_type;
		}
		if(!empty($category_id)){
			$where .= "AND `reg_event_categories`.`category_id` = ? ";
			$params[] = $category_id;
		}
		if(!empty($start_date)){
			$where .= "AND `reg_occurrences`.`start_date` >= ? ";
			$params[] = date('Y-m-d', strtotime($start_date));
		}
		if(!empty($end_date)){
			$where .= "AND `reg_occurrences`.`end_date` <= ? ";
			$params[] = date('Y-m-d', strtotime($end_date));
		}
		if(!empty($searchterm)){
			$where .= " AND (`reg_events`.`name` LIKE ? OR `reg_categories`.`name` LIKE ? OR `facilities`.`facility_name` LIKE ? OR `reg_locations`.`name` LIKE ?)";
			$params[] = '%'.$searchterm.'%';
			$params[] = '%'.$searchterm.'%';
			$params[] = '%'.$searchterm.'%';
			$params[] = '%'.$searchterm.'%';
		}
		
		$query = $this->db->query("SELECT `reg_events`.`name` AS `event_name`, `reg_events`.`page`, `reg_events`.`waiting_list`, `reg_events`.`gender`, `reg_events`.`event_type`, `reg_events`.`team_event`, `reg_events`.`role_id`, `reg_occurrences`.*, `reg_categories`.`name` AS `category_name`, `facilities`.`facility_name`, `reg_locations`.`location_id`, `reg_locations`.`name` AS `location_name`, ".
		"(SELECT `category_id` FROM `reg_event_eligibility` WHERE `reg_event_eligibility`.`event_id` = `reg_events`.`event_id` AND `reg_event_eligibility`.`category_id` = ?) AS `eligibility`, ".
		"(SELECT `class_id` FROM `reg_event_membership_classes` WHERE `reg_event_membership_classes`.`event_id` = `reg_events`.`event_id` AND `reg_event_membership_classes`.`class_id` = ?) AS `class_eligibility`, ".
		"(SELECT COUNT(*) FROM `reg_event_eligibility` WHERE `reg_event_eligibility`.`event_id` = `reg_events`.`event_id`) AS `eligible_categories`, ".
		"(SELECT COUNT(*) FROM `reg_event_membership_classes` WHERE `reg_event_membership_classes`.`event_id` = `reg_events`.`event_id`) AS `membership_classes`, ".
		"(SELECT COUNT(*) FROM `reg_attendees` WHERE `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` AND `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NULL) AS `attendance`, ".
		"(SELECT `account_id` FROM `reg_attendees` WHERE `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` AND `reg_attendees`.`account_id` = ? AND `reg_attendees`.`reg_status` = ? LIMIT 1) AS `registered` ".
		"FROM `reg_events` ".
		"LEFT JOIN `reg_event_categories` ON `reg_events`.`event_id` = `reg_event_categories`.`event_id` ".
		"LEFT JOIN `reg_categories` ON `reg_categories`.`category_id` = `reg_event_categories`.`category_id` ".
		"LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`event_id` = `reg_events`.`event_id` ".
		"LEFT JOIN `facilities` ON `reg_occurrences`.`facility_id` = `facilities`.`facility_id` ".
		"LEFT JOIN `reg_locations` ON `reg_occurrences`.`occurrence_id` = `reg_locations`.`occurrence_id` ".
		"WHERE `reg_events`.`showhide` = 0 AND `reg_events`.`status` != ? $where".
		"GROUP BY `reg_occurrences`.`occurrence_id` ORDER BY $sortby".($lim != '' ? " LIMIT $lim" : ""),
		$params);
		
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
		}
		
		return $response;
	}
	
	/*-----------------------------------/
	* Get event ticket pricing
	*
	* <AUTHOR> Army
	* @param	event_id		The event_id to be fetched
	* @return	Array			An array of the ticket pricing
	*/
	public function get_event_pricing($event_id){
		$response = array();
		
		$params = array($event_id);
		$query = $this->db->query("SELECT * FROM `reg_event_pricing` WHERE `reg_event_pricing`.`event_id` = ?", $params);
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
		}
					
		return $response;
	}
	
	/*-----------------------------------/
	* Get event location details
	*
	* <AUTHOR> Army
	* @param	location_id		The location_id to be fetched
	* @return	Array			An array of the location details
	*/
	public function get_event_location($location_id){
		$response = array();
		$query = $this->db->query("SELECT * FROM `reg_locations` WHERE `location_id` = ?", array($location_id));
		if($query && !$this->db->error() && $this->db->num_rows()){
			$result = $this->db->fetch_array();
			$response = $result[0];
		}
		return $response;
	}
	
	/*-----------------------------------/
	* Get event eligibility
	*
	* <AUTHOR> Army
	* @param	event_id		The event_id to be fetched
	* @return	Array			An array of the eligibility categories
	*/
	public function get_event_eligibility($event_id){
		$response = array();
		
		$params = array($event_id);
		$query = $this->db->query("SELECT `membership_categories`.* FROM `reg_event_eligibility` LEFT JOIN `membership_categories` ON `reg_event_eligibility`.`category_id` = `membership_categories`.`category_id` WHERE `reg_event_eligibility`.`event_id` = ?", $params);
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
		}
					
		return $response;
	}
	
	/*-----------------------------------/
	* Get event membership classes
	*
	* <AUTHOR> Army
	* @param	event_id		The event_id to be fetched
	* @return	Array			An array of membership classes
	*/
	public function get_event_membership_classes($event_id){
		$response = array();
		
		$params = array($event_id);
		$query = $this->db->query("SELECT `membership_classes`.* FROM `reg_event_membership_classes` LEFT JOIN `membership_classes` ON `reg_event_membership_classes`.`class_id` = `membership_classes`.`class_id` WHERE `reg_event_membership_classes`.`event_id` = ?", $params);
		if($query && !$this->db->error()){
			$result = $this->db->fetch_array();
			foreach($result as $row){
				$response[$row['class_id']] = $row;
			}
		}
					
		return $response;
	}
	
	/*-----------------------------------/
	* Get list of eligible attendees for a given event occurrence
	*
	* <AUTHOR> Army
	* @param	occurrence_id	The occurrence_id to be fetched
	* @return	Array			Array of the results 
	*/
	public function get_eligible_attendees($occurrence_id){
		$response = array();
				
		//Get event eligibility
		$eligibility_categories = array();
		$query = $this->db->query("SELECT `reg_event_eligibility`.* FROM `reg_event_eligibility` INNER JOIN `reg_occurrences` ON `reg_event_eligibility`.`event_id` = `reg_occurrences`.`event_id` WHERE `reg_occurrences`.`occurrence_id` = ?", array($occurrence_id));
		if($query && !$this->db->error()){
			$result = $this->db->fetch_array();
			foreach($result as $row){
				$eligibility_categories[] = $row['category_id'];
			}
			
			//Get member class restrictions
			$membership_classes = array();
			$this->db->query("SELECT `reg_event_membership_classes`.* FROM `reg_event_membership_classes` INNER JOIN `reg_occurrences` ON `reg_event_membership_classes`.`event_id` = `reg_occurrences`.`event_id` WHERE `reg_occurrences`.`occurrence_id` = ?", array($occurrence_id));
			if(!$this->db->error() && $this->db->num_rows()){
				$classes = $this->db->fetch_array();
				foreach($classes as $class){
					$membership_classes[] = $class['class_id'];
				}
			}
			
			//Get event restrictions
			$gender = NULL;
			$event_type = NULL;
			$query = $this->db->query("SELECT `reg_events`.`gender`, `reg_events`.`event_type`, `reg_events`.`team_event`, `reg_events`.`role_id` FROM `reg_events` LEFT JOIN `reg_occurrences` ON `reg_events`.`event_id` = `reg_occurrences`.`event_id` WHERE `reg_occurrences`.`occurrence_id` = ?", array($occurrence_id));
			if($query && !$this->db->error() && $this->db->num_rows()){
				$result = $this->db->fetch_array();
				$gender = $result[0]['gender'];
				$role_id = $result[0]['role_id'];
				$event_type = $result[0]['event_type'];
			}
				
			//Get eligible members
			$params = array();
			$params[] = date("Y-m-d");
			$params[] = "Trashed";
			if(!empty($role_id)){
				$params[] = $role_id;
			}
			$params[] = $occurrence_id;
			$params[] = "Active";
			$params[] = "Registered";
			if(!empty($eligibility_categories)){
				$params[] = implode(",", $eligibility_categories);
			}
			if(!empty($membership_classes)){
				$params[] = implode(",", $membership_classes);
			}
			if(!empty($gender)){
				$params[] = $gender;
			}
			$query = $this->db->query("SELECT `account_profiles`.`first_name`, `account_profiles`.`last_name`, `accounts`.`account_id`, `accounts`.`email`, `facilities`.`facility_name`, ".
			"(SELECT COUNT(*) FROM `invoices` WHERE `invoices`.`account_id` = `account_profiles`.`account_id` && `invoices`.`paid` = 0 && `invoices`.`due_date` IS NOT NULL && `invoices`.`due_date` <= ? && `invoices`.`status` != ?) AS `overdue_invoices` ".
			"FROM `account_profiles` ".
			"LEFT JOIN `accounts` ON `account_profiles`.`account_id` = `accounts`.`account_id` ".
			"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
			(!empty($role_id) ? "INNER JOIN `account_permissions` ON `account_profiles`.`account_id` = `account_permissions`.`account_id` && `account_permissions`.`role_id` = ? " : "").			  
			"LEFT JOIN `reg_attendees` ON `account_profiles`.`account_id` = `reg_attendees`.`account_id` && `reg_attendees`.`occurrence_id` = ? ".
			"WHERE `accounts`.`status` = ? && (`reg_attendees`.`attendee_id` IS NULL || (`reg_attendees`.`attendee_id` IS NOT NULL && `reg_attendees`.`reg_status` != ?)) ".
			(!empty($eligibility_categories) ? "&& FIND_IN_SET(`account_profiles`.`category_id`, ?) " : "").
			(!empty($membership_classes) ? "&& FIND_IN_SET(`account_profiles`.`class_id`, ?) " : "").
			(!empty($gender) ? "&& `account_profiles`.`gender` = ? " : "").
			($event_type == 2 && $role_id == 2 ? "&& `account_profiles`.`category_id` IS NOT NULL " : "").
			"GROUP BY `account_profiles`.`account_id` ORDER BY `account_profiles`.`last_name`, `account_profiles`.`first_name`", $params);
			if($query && !$this->db->error()){
				$response = $this->db->fetch_array();
			}
		}
					
		return $response;
	}
	
	/*-----------------------------------/
	* Get individual occurrence details
	*
	* <AUTHOR> Army
	* @param	occurrence_id	The id of the event occurrence
	* @param	account_id		The id of the user registering
	* @return	Array			An array of the results
	*/
	public function get_occurrence($occurrence_id, $account_id=NULL){
		$response = array();
		$params = array();
		
		//Get settings
		$reg_settings = $this->get_reg_settings();
		
		//Get account
		$account = array(
			'account_id' => NULL,
			'category_id' => NULL,
			'class_id' => NULL,
			'gender' => NULL
		);
		if(!is_null($account_id)){
			try{
				$account = $this->account->get_account_profile($account_id);
			}catch(Exception $e){
				trigger_error($e->getMessage()); //Report instead of returning as this would error the withdrawal
			}
		}else{
			if(!is_null($this->account_id)){
				$account = array(
					'account_id' => $this->account_id,
					'category_id' => $this->account->category_id,
					'class_id' => $this->account->class_id,
					'gender' => $this->account->gender
				);
			}
		}
		
		$params[] = $account['category_id'];
		$params[] = $account['class_id'];
		$params[] = "Registered";
		$params[] = $account['account_id'];
		$params[] = "Registered";
		$params[] = "Trashed";
		$params[] = $occurrence_id;
		
		$query = $this->db->query("SELECT `reg_events`.`name` AS `event_name`, `reg_events`.`event_type`, `reg_events`.`admin_fee`, `reg_events`.`admin_fee_type`, `reg_events`.`tournament_fee`, `reg_events`.`team_event`, `reg_events`.`page`, `reg_events`.`waiting_list`, `reg_events`.`attendee_sharing`, `reg_events`.`gender`, `reg_events`.`age`, `reg_events`.`role_id`, `reg_events`.`description`, `reg_events`.`banner_image`, `reg_events`.`banner_image_alt`, `reg_events`.`gallery_id`, `reg_events`.`meta_title`, `reg_events`.`meta_description`, `reg_events`.`showhide` AS `event_showhide`, `reg_occurrences`.*, `reg_categories`.`category_id`, `reg_categories`.`name` AS `category_name`, `facilities`.`facility_name`, `reg_locations`.`location_id`, `reg_locations`.`name` AS `location_name`, ".
		"(SELECT `category_id` FROM `reg_event_eligibility` WHERE `reg_event_eligibility`.`event_id` = `reg_events`.`event_id` AND `reg_event_eligibility`.`category_id` = ?) AS `eligibility`, ".
		"(SELECT `class_id` FROM `reg_event_membership_classes` WHERE `reg_event_membership_classes`.`event_id` = `reg_events`.`event_id` AND `reg_event_membership_classes`.`class_id` = ?) AS `class_eligibility`, ".
		"(SELECT COUNT(*) FROM `reg_event_eligibility` WHERE `reg_event_eligibility`.`event_id` = `reg_events`.`event_id`) AS `eligible_categories`, ".
		"(SELECT COUNT(*) FROM `reg_event_membership_classes` WHERE `reg_event_membership_classes`.`event_id` = `reg_events`.`event_id`) AS `membership_classes`, ".
		"(SELECT COUNT(*) FROM `reg_attendees` WHERE `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` AND `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NULL) AS `attendance`, ".
		"(SELECT `account_id` FROM `reg_attendees` WHERE `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` AND `reg_attendees`.`account_id` = ? AND `reg_attendees`.`reg_status` = ?) AS `registered` ".
		"FROM `reg_events` ".
		"LEFT JOIN `reg_event_categories` ON `reg_events`.`event_id` = `reg_event_categories`.`event_id` ".
		"LEFT JOIN `reg_categories` ON `reg_categories`.`category_id` = `reg_event_categories`.`category_id` ".
		"LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`event_id` = `reg_events`.`event_id` ".
		"LEFT JOIN `facilities` ON `reg_occurrences`.`facility_id` = `facilities`.`facility_id` ".
		"LEFT JOIN `reg_locations` ON `reg_locations`.`occurrence_id` = `reg_occurrences`.`occurrence_id` ".		
		"WHERE `reg_events`.`status` != ? && `reg_occurrences`.`occurrence_id` = ? GROUP BY `reg_occurrences`.`occurrence_id`", $params);
		if($query && !$this->db->error() && $this->db->num_rows()){
			$result = $this->db->fetch_array();
			$row = $result[0];
			
			$row['pricing'] = $this->get_event_pricing($row['event_id']);
			$row['addons'] = $this->get_occurrence_addons($occurrence_id);	
			
			//Determine registration and waitlist availability
			$row['reg_available'] = false;
			$row['reg_waitlist'] = false;
			$row['started'] = ($row['start_date'] <= date("Y-m-d") ? true : false);
			$row['open'] = ($row['occurrence_status'] == 'Open' && (strtotime($row['reg_open'].' '.$reg_settings['reg_open_time']) <= strtotime('now')) && (strtotime($row['reg_deadline'].' '.$reg_settings['reg_close_time']) > strtotime('now')) ? true : false);
			$row['full'] = (!is_null($row['max_capacity']) && $row['attendance'] >= $row['max_capacity'] ? true : false);
			$row['isgender'] = (is_null($row['gender']) || (!is_null($row['gender']) && $account['gender'] == $row['gender']) ? true : false);
			$row['isregistered'] = (!is_null($row['registered']) ? true : false);
			$row['spots_available'] = (!is_null($row['max_capacity']) ? $row['max_capacity']-$row['attendance'] : NULL);
			
			//Check eligibility
			$row['eligible'] = false;
			$row['isclass'] = false;
			if(empty($row['role_id']) || (!empty($row['role_id']) && $this->account->account_has_role($row['role_id'], $account['account_id']))){
				
				//Member eligibility check for tournaments
				if($row['role_id'] == 2 && $row['event_type'] == 2){
					
					//Check member classification restriction
					$row['isclass'] = ((!is_null($row['class_eligibility']) || $row['membership_classes'] == 0) ? true : false);
					
					//Check category restriction
					$row['eligible'] = ($row['isclass'] && !empty($account['category_id']) && (!is_null($row['eligibility']) || $row['eligible_categories'] == 0) ? true : false);					
					
				}else{
					$row['eligible'] = true;
					$row['isclass'] = true;
				}
			}
			
			//Tournaments
			if($row['event_type'] == 2){
				if($row['eligible'] && $row['isgender'] && !$row['isregistered'] && $row['open'] && !$row['started']){
					if(!$row['full']){
						$row['reg_available'] = true;
					}else{
						if($row['waiting_list'] == '1'){
							$row['reg_waitlist'] = true;
						}
					}
				}
				
			//Events
			}else{
				if($row['eligible'] && $row['isgender'] && $row['open'] && !$row['started']){
					if(!$row['full']){
						$row['reg_available'] = true;
					}else{
						if($row['waiting_list'] == '1'){
							$row['reg_waitlist'] = true;
						}
					}
				}
				
			}
			
			$response = $row;
			
		}
		
		return $response;
	}
	
	/*-----------------------------------/
	* Get event waiver forms
	*
	* <AUTHOR> Army
	* @param	event_id		The id of the event
	* @return	Array			Array of event waivers
	*/
	public function get_event_waivers($event_id){
		$response = array();
		
		//Get category waivers
		$params = array($event_id);
		$query = $this->db->query("SELECT `reg_waivers`.* FROM `reg_waivers` LEFT JOIN `reg_events` ON `reg_waivers`.`event_type` = `reg_events`.`event_type` WHERE `reg_events`.`event_id` = ? && `reg_waivers`.`showhide` = 0 GROUP BY `reg_waivers`.`waiver_id` ORDER BY `reg_waivers`.`ordering`", $params);
		if($query && !$this->db->error()){
			$response = array_merge($response, $this->db->fetch_array());
		}
		
		//Get assigned waivers
		$params = array($event_id);
		$query = $this->db->query("SELECT `reg_waivers`.* FROM `reg_waivers` LEFT JOIN `reg_event_waivers` ON `reg_waivers`.`waiver_id` = `reg_event_waivers`.`waiver_id` WHERE `reg_event_waivers`.`event_id` = ? && `reg_waivers`.`showhide` = 0 GROUP BY `reg_waivers`.`waiver_id` ORDER BY `reg_waivers`.`ordering`", $params);
		if($query && !$this->db->error()){
			$response = array_merge($response, $this->db->fetch_array());
		}	
			
		return $response;
	}
	
	/*-----------------------------------/
	* Get event required forms
	*
	* <AUTHOR> Army
	* @param	event_id		The id of the event
	* @return	Array			Array of event waivers
	*/
	public function get_event_forms($event_id, $account_id){
		$response = array();
		
		$params = array($account_id, date('Y').' 01-01', $event_id);
		$query = $this->db->query("SELECT `forms`.*, ".
		"(SELECT `page_id` FROM `pages_panels` WHERE `pages_panels`.`form_id` = `forms`.`form_id` LIMIT 1) AS `page_id`, ".
		"(SELECT `account_id` FROM `form_submissions` WHERE `form_submissions`.`form_id` = `forms`.`form_id` && `form_submissions`.`account_id` = ? && `form_submissions`.`draft` = false && `form_submissions`.`timestamp` >= ? LIMIT 1) AS `completed` ".
		"FROM `forms` ".
		"LEFT JOIN `reg_event_forms` ON `forms`.`form_id` = `reg_event_forms`.`form_id` ".
		"WHERE `reg_event_forms`.`event_id` = ? && `forms`.`showhide` = 0 GROUP BY `forms`.`form_id` ORDER BY `reg_event_forms`.`uid`", $params);
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
		}	
			
		return $response;
	}
	
	/*-----------------------------------/
	* Get event occurrence addons and options
	*
	* <AUTHOR> Army
	* @param	occurrence_id	The id of the event occurrence
	* @return	Array			Associative array of addons and their options
	*/
	public function get_occurrence_addons($occurrence_id){
		$response = array();
		
		$params = array($occurrence_id);
		$query = $this->db->query("SELECT `reg_occurrence_addons`.`occurrence_id`, `reg_occurrence_addons`.`required`, `reg_occurrence_addons`.`addon_id`, `reg_occurrence_addons`.`name` AS addon_name, `reg_occurrence_options`.`option_id`, `reg_occurrence_options`.`name` AS option_name, `reg_occurrence_options`.`price_adjustment` FROM `reg_occurrence_addons` LEFT JOIN `reg_occurrence_options` ON `reg_occurrence_options`.`addon_id` = `reg_occurrence_addons`.`addon_id` WHERE `reg_occurrence_addons`.`occurrence_id` = ? ORDER BY `reg_occurrence_addons`.`addon_id` ASC, `reg_occurrence_options`.`option_id` ASC", $params);
		if($query && !$this->db->error()){
			$addon_sql = $this->db->fetch_array();
			foreach($addon_sql as $menu){
				
				$response[$menu['addon_id']]['occurrence_id'] = $menu['occurrence_id'];
				$response[$menu['addon_id']]['addon_id'] = $menu['addon_id'];
				$response[$menu['addon_id']]['name'] = $menu['addon_name'];
				$response[$menu['addon_id']]['required'] = $menu['required'];

				$response[$menu['addon_id']]['options'][$menu['option_id']]['option_id'] = $menu['option_id'];
				$response[$menu['addon_id']]['options'][$menu['option_id']]['name'] = $menu['option_name'];
				$response[$menu['addon_id']]['options'][$menu['option_id']]['price_adjustment'] = $menu['price_adjustment'];
			}
		}

		return $response;
	}
	
	/*-----------------------------------/
	* Get occurrence sponsors
	*
	* <AUTHOR> Army
	* @param	occurrence_id	The id of the occurrence
	* @return	Array			Array of occurrence sponsors
	*/
	public function get_occurrence_sponsors($occurrence_id){
		$response = array();
		
		$params = array($occurrence_id);
		$query = $this->db->query("SELECT * FROM `reg_occurrence_sponsors` WHERE `occurrence_id` = ? ORDER BY `type` DESC, `uid`", $params);
		if($query && !$this->db->error()){
			$sponsors = $this->db->fetch_array();
			foreach($sponsors as $sponsor){
				$sponsor = array_merge($sponsor, $this->get_sponsor($sponsor['sponsor_id']));
				array_push($response, $sponsor);
			}
		}
			
		return $response;
	}
	
	/*-----------------------------------/
	* Get sponsor details
	*
	* <AUTHOR> Army
	* @param	sponsor_id		The id of the sponsor
	* @return	Array			Array of the results 
	*/
	public function get_sponsor($sponsor_id){
		$response = array();
		
		$params = array($sponsor_id);
		// $query = $this->db->query("SELECT * FROM `sponsors` WHERE `sponsor_id` = ?",$params);
		$query = $this->db->query("SELECT * FROM `partners` WHERE `partner_id` = ?",$params);
		
		if($query && !$this->db->error()){
			$sponsor_sql = $this->db->fetch_array();
			$response = $sponsor_sql[0];
		}
			
		return $response;
	}
	
	/*-----------------------------------/
	* Get a list of the attendee information required/optional
	*
	* <AUTHOR> Army
	* @param	event_id		The event_id to be fetched
	* @return	Array			An array of the attendee information to be shown
	*/
	public function get_attendee_information($event_id){
		$response = array();
			
		$params = array($event_id);
		$query = $this->db->query("SELECT * FROM `reg_event_attendee_information` WHERE `event_id` = ?", $params);
		if($query && !$this->db->error()){
			$information_sql = $this->db->fetch_array();
			$attendee_info = $information_sql[0];
			
			foreach($attendee_info as $key=>$val){
				if($key != "information_id" && $key != "event_id"){
					if($val > 0){
						if($val == 1){
							$val = "Required";
						}else{
							$val = "Optional";
						}
						$response[$key] = $val;
					}
				}
			}
		}
				
		return $response;
	}
	
	/*-----------------------------------/
	* Get attendees currently registered for an occurrence
	*
	* <AUTHOR> Army
	* @param	occurrence_id	The occurrence_id to be fetched
	* @return	Array			Array of the results 
	*/
	public function get_current_attendance($occurrence_id){
		$response = array();
		
		$params = array($occurrence_id, "Registered");
		$query = $this->db->query("SELECT `reg_attendees`.*, `facilities`.`facility_name` FROM `reg_attendees` ".
		"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"WHERE `reg_attendees`.`occurrence_id` = ? AND `reg_attendees`.`reg_status` = ? ".
		"GROUP BY `reg_attendees`.`attendee_id` ORDER BY `reg_attendees`.`last_name`, `reg_attendees`.`first_name`", $params);
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
		}
					
		return $response;
	}
	
	/*-----------------------------------/
	* Update attendee
	*
	* <AUTHOR> Army
	* @param	attendee_id		The attendee_id to be updated
	* @param	status			New registration status
	* @return	Boolean			True/false on success or error
	* @throws	Exception
	*/
	public function update_attendee_status($attendee_id, $status){
		global $siteurl;
		global $global;
		
		$params = array();
		$notifications = array();
		
		//Fetch settings
		$reg_settings = $this->get_reg_settings();
		$waitlist_autoreg = false;
		
		//Fetch attendee details
		$query = $this->db->query("SELECT `reg_attendees`.*, `reg_events`.`name` AS `event_name`, `reg_events`.`event_type`, `reg_events`.`team_event`, `reg_occurrences`.`occurrence_name`, `reg_occurrences`.`start_date` AS `event_start_date`, `reg_occurrences`.`end_date` AS `event_end_date`, `reg_registrations`.`registration_number`, ".
		"(SELECT `reg_event_categories`.`category_id` FROM `reg_event_categories` WHERE `reg_event_categories`.`event_id` = `reg_events`.`event_id`) AS `event_category` ".
		"FROM `reg_attendees` ".
		"LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ".
		"LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `reg_attendees`.`occurrence_id` ".
		"LEFT JOIN `reg_registrations` ON `reg_registrations`.`registration_id` = `reg_attendees`.`registration_id` ".
		"WHERE `reg_attendees`.`attendee_id` = ?", array($attendee_id));
		if($query && !$this->db->error() && $this->db->num_rows()){
			$result = $this->db->fetch_array();
			$attendee = $result[0];
			
			//Set admin notification email
			$admin_email = NULL;
			if($attendee['event_type'] == 1){
				$admin_email = $reg_settings['email_events'];
				if($attendee['event_category'] == 3){
					$admin_email = $reg_settings['email_conferences'];
				}
			}else if($attendee['event_type'] == 2){
				$admin_email = $reg_settings['email_tournaments'];
			}
			if(empty($admin_email)){
				$admin_email = $global['contact_email'];
			}
						
			//Start transaction
			$this->db->new_transaction();
			
			//Update attendee status
			$params[] = $status;
			if($status == 'Withdrawn'){
				$params[] = date("Y-m-d H:i:s");
			}
			$params[] = date("Y-m-d H:i:s");
			$params[] = $this->account_id;
			$params[] = $attendee_id;
			$query = $this->db->query("UPDATE `reg_attendees` SET `reg_status` = ?, " .($status == "Withdrawn" ? "`date_withdrawn` = ?, " : ""). "`last_updated` = ?, `updated_by` = ? WHERE `attendee_id` = ?", $params);
			
			//Attendee status has changed
			if($attendee['reg_status'] != $status){
				
				//Attendee has been withdrawn (tournaments only)
				if($status == 'Withdrawn' && $attendee['event_type'] == 2){

					//Check for partner
					$attendee['partner'] = array();
					$query = $this->db->query("SELECT * FROM `reg_attendees` WHERE (`attendee_id` = ? OR `partner_id` = ?) AND `reg_status` = ?", array($attendee['partner_id'], $attendee_id, 'Registered'));
					if($this->db->num_rows()){
						$result = $this->db->fetch_array();
						$attendee['partner'] = $result[0];

						//Withdrawn attendee is not registrant
						if(!empty($attendee['partner_id'])){

							//Notify registrant of partner withdrawal
							if(!empty($attendee['partner']['email'])){
								$subject = "Withdrawal Notification";
								$message = "<h3>" .$subject. "</h3>
								<p>This email is to inform you that your partner, <strong>" .$attendee['first_name']." ".$attendee['last_name']. "</strong>, has withdrawn from the following:</p>
								<p><strong>" .$attendee['event_name'].(trim($attendee['occurrence_name']) != "" ? " - ".$attendee['occurrence_name'] : ""). " on " .format_date_range($attendee['event_start_date'], $attendee['event_end_date']). ".</strong></p>
								<p>To select a new partner for this event, please <a href='" .$siteurl. "/login/'>login</a> to your account.</p>
								<p>If you have any questions or concerns regarding this message, please contact us.</p>";
								$notifications[] = array(
									'to' => $attendee['partner']['email'],
									'subject' => $subject,
									'message' => $message
								);
							}

						//Withdrawn attendee is registrant	
						}else{

							//If partner is an account holder, transfer registration and notify them
							if(!empty($attendee['partner']['account_id'])){

								//Switch registrant to partner
								$params = array(
									$attendee['partner']['ticket_type'],
									$attendee['partner']['ticket_price'],
									$attendee['partner']['attendee_id'],
									date("Y-m-d H:i:s"),
									$this->account_id,
									$attendee_id
								);
								$query = $this->db->query("UPDATE `reg_attendees` SET `ticket_type` = ?, `ticket_price` = ?, `partner_id` = ?, `last_updated` = ?, `updated_by` = ? WHERE `attendee_id` = ?", $params);

								//Switch partner to registrant
								$params = array(
									$attendee['ticket_type'],
									$attendee['ticket_price'],
									NULL,
									date("Y-m-d H:i:s"),
									$this->account_id,
									$attendee['partner']['attendee_id']
								);
								$query = $this->db->query("UPDATE `reg_attendees` SET `ticket_type` = ?, `ticket_price` = ?, `partner_id` = ?, `last_updated` = ?, `updated_by` = ? WHERE `attendee_id` = ?", $params);

								//Transfer registration to partner
								$params = array(
									$attendee['partner']['first_name'],
									$attendee['partner']['last_name'],
									$attendee['partner']['email'],
									$attendee['partner']['phone'],
									$attendee['partner']['account_id'],
									NULL,
									'0',
									$attendee['partner']['registration_id']								);
								$query = $this->db->query("UPDATE `reg_registrations` SET `first_name` = ?, `last_name` = ?, `email` = ?, `phone` = ?, `account_id` = ?, `billing_id` = ?, `paid` = ? WHERE `registration_id` = ?", $params);

								//Notify partner of registrant withdrawal
								$subject = "Withdrawal Notification";
								$message = "<h3>" .$subject. "</h3>
								<p>This email is to inform you that your partner, <strong>" .$attendee['first_name']." ".$attendee['last_name']. "</strong>, has withdrawn from the following:</p>
								<p><strong>" .$attendee['event_name'].(trim($attendee['occurrence_name']) != "" ? " - ".$attendee['occurrence_name'] : ""). " on " .format_date_range($attendee['event_start_date'], $attendee['event_end_date']). ".</strong></p>
								<p>To hold your spot in this tournament, payment in full is required. Please <a href='" .$siteurl. "/login/'>login</a> to your account and pay any outstanding balance within 24 hours. Otherwise, your spot will not be guaranteed.</p>
								<p>If you have any questions or concerns regarding this message, please contact us.</p>";
								if(!empty($attendee['partner']['email'])){
									$notifications[] = array(
										'to' => $attendee['partner']['email'],
										'subject' => $subject,
										'message' => $message
									);
								}

							//If partner is not an account holder, withdraw them as well and open up autoreg spot	
							}else{
								$waitlist_autoreg = true;
								$params = array('Withdrawn', date('Y-m-d H:i:s'), date('Y-m-d H:i:s'), $this->account_id, $attendee['partner']['attendee_id']);
								$query = $this->db->query("UPDATE `reg_attendees` SET `reg_status` = ?, `date_withdrawn` = ?, `last_updated` = ?, `updated_by` = ? WHERE `attendee_id` = ?", $params);
							}

						}

					//No partner found, open up autoreg spot	
					}else{
						$waitlist_autoreg = true;
					}				
					
				}
				
				//Notify attendee of status change
				$subject = ($status == 'Registered' ? 'Registration' : 'Withdrawal'). " Confirmation";
				$message = "<h3>" .$subject. "</h3>
				<p>This email is to inform you that the registration status for <strong>" .$attendee['first_name']." ".$attendee['last_name']. "</strong> has been changed from <strong>" .$attendee['reg_status']. "</strong> to <strong>" .$status. "</strong> for the following:</p>
				<p><strong>Registration No. " .$attendee['registration_number']. "</strong><br />
				<strong>" .$attendee['event_name'].(trim($attendee['occurrence_name']) != "" ? " - ".$attendee['occurrence_name'] : ""). " on " .format_date_range($attendee['event_start_date'], $attendee['event_end_date']). ".</strong></p>
				<p>If you have any questions or concerns regarding this message, please contact us.</p>";
				if(!empty($attendee['email'])){
					$notifications[] = array(
						'to' => $attendee['email'],
						'subject' => $subject,
						'message' => $message
					);
				}

				//Notify admin also
				if(!empty($admin_email)){
					$notifications[] = array(
						'to' => $admin_email,
						'subject' => $subject,
						'message' => $message
					);
				}
				
			}
			
			//Commit update
			if(!$this->db->error()){
				$this->db->commit();
				
				//Send notifications
				foreach($notifications as $notify){
					send_email($notify['to'], $notify['subject'], $notify['message']);
				}
				
				//Auto-register
				if($waitlist_autoreg){
					try{
						$this->wait_list_autoregister($attendee['occurrence_id']);
					}catch(Exception $e){
						trigger_error($e->getMessage()); //Report instead of returning as this would error the withdrawal
					}
				}

				return true;
				
			//Update error
			}else{
				throw new Exception('Registration::update_attendee_status - '.$this->db->error());
			}
		
		//Unable to fetch attendee
		}else{
			throw new Exception('Registration::update_attendee_status - '.$this->db->error());
		}
		
		return false;
		
	}
	
	/*-----------------------------------/
	* Get the number of requests in the waiting list for an event
	*
	* <AUTHOR> Army
	* @param	event_id		The event_id to be fetched
	* @return	int				Number of requests
	*/
	public function get_wait_list_count($event_id){
		
		$params = array($event_id);
		$query = $this->db->query("SELECT * FROM `reg_waiting_list` WHERE `event_id` = ?", $params);
		if($query && !$this->db->error()){
			$num_requests = $this->db->num_rows();
		}else{
			$num_requests = 0;
		}
				
		return $num_requests;
	}
	
	/*-----------------------------------/
	* Get the number of requests in the waiting list for an event
	*
	* <AUTHOR> Army
	* @param	occurrence_id	The occurrence_id to be fetched
	* @return	Array			Array of waiting list subscribers
	*/
	public function get_current_wait_list($occurrence_id){
		$response = array();
		
		$params = array($occurrence_id);
		$query = $this->db->query("SELECT `reg_waiting_list`.*, `facilities`.`facility_name`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `accounts`.`email`, `account_profiles`.`phone`, `account_profiles`.`gender`, `account_profiles`.`category_id` FROM `reg_waiting_list` ".
		"LEFT JOIN `account_profiles` ON `reg_waiting_list`.`account_id` = `account_profiles`.`account_id` ".
		"LEFT JOIN `accounts` ON `reg_waiting_list`.`account_id` = `accounts`.`account_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"WHERE `reg_waiting_list`.`occurrence_id` = ? ".
		"GROUP BY `reg_waiting_list`.`wait_id` ORDER BY -`reg_waiting_list`.`priority` DESC, `reg_waiting_list`.`date_added` ASC, `account_profiles`.`last_name`, `account_profiles`.`first_name`", $params);
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
		}
					
		return $response;
	}
	
	/*-----------------------------------/
	* Add user to event waiting list
	*
	* <AUTHOR> Army
	* @param	event_id		The id of the event
	* @param	occurrence_id	The id of the event occurrence
	* @param	account_id		The id of the user
	* @param	priority 		Set priority number to bump people up list
	* @return	Boolean			True/false on success or error
	* @throws	Exception
	*/
	public function wait_list_subscribe($event_id, $occurrence_id, $account_id, $priority=NULL){
		
		$params = array(
			$event_id, 
			$occurrence_id, 
			(!empty($account_id) ? $account_id : NULL),
			$priority,
			date("Y-m-d H:i:s"),
			date("Y-m-d H:i:s"),
			date("Y-m-d H:i:s")
		);
		$query = $this->db->query("INSERT INTO `reg_waiting_list`(`event_id`, `occurrence_id`, `account_id`, `priority`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `last_updated`=?", $params);
		if($query && !$this->db->error()){
			return true;
		}else{
			throw new Exception('Registration::wait_list_subscribe - '.$this->db->error());
		}		
		
	}
	
	/*-----------------------------------/
	* Remove user from event waiting list
	*
	* <AUTHOR> Army
	* @param	occurrence_id	The id of the event occurrence
	* @param	account_id		The id of the user
	* @return	Boolean			True/false on success or error
	* @throws	Exception
	*/
	public function wait_list_unsubscribe($occurrence_id, $account_id){
		
		$params = array($occurrence_id, $account_id);
		$query = $this->db->query("DELETE FROM `reg_waiting_list` WHERE `occurrence_id` = ? && `account_id` = ?", $params);
		if($query && !$this->db->error()){
			return true;
		}else{
			throw new Exception('Registration::wait_list_unsubscribe - '.$this->db->error());
		}		
		
	}
	
	/*-----------------------------------/
	* Get all waiting list subscriptions for a user
	*
	* <AUTHOR> Army
	* @param	account_id		The id of the user
	* @return	Boolean			True/false
	*/
	public function get_user_wait_lists($account_id=NULL){
		$response = array();
		
		$params = array((!empty($account_id) ? $account_id : $this->account_id));
		$query = $this->db->query("SELECT * FROM `reg_waiting_list` WHERE `reg_waiting_list`.`account_id` = ?", $params);
		if($query && !$this->db->error()){
			$result = $this->db->fetch_array();
			foreach($result as $row){
				$response[$row['occurrence_id']] = $row;
			}
		}
		
		return $response;		
	}
	
	/*-----------------------------------/
	* Auto-register attendees for a tournament from the waiting list based on available spots
	*
	* <AUTHOR> Army
	* @param	occurrence_id	The id of the event occurrence
	* @return	Integer			Number of successful registrations
	* @throws	Exception
	*/
	public function wait_list_autoregister($occurrence_id){
		global $siteurl;
		global $global;
		
		$params = array();
		$notifications = array();
		
		//Fetch settings
		$reg_settings = $this->get_reg_settings();
		
		//Get event
		$params = array('Registered', 'Trashed', 'Open', $occurrence_id);
		$query = $this->db->query("SELECT `reg_events`.*, `reg_occurrences`.*, ".
		"(SELECT COUNT(*) FROM `reg_attendees` WHERE `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` AND `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NULL) AS `attendance`, ".
		"(SELECT `reg_event_categories`.`category_id` FROM `reg_event_categories` WHERE `reg_event_categories`.`event_id` = `reg_events`.`event_id`) AS `event_category` ".
		"FROM `reg_occurrences` ".
		"LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `reg_occurrences`.`event_id` ". 
		"WHERE `reg_events`.`status` != ? AND `reg_occurrences`.`occurrence_status` = ? AND `reg_occurrences`.`occurrence_id` = ? ".
		"GROUP BY `reg_events`.`event_id` ORDER BY `reg_events`.`name` ASC, `reg_events`.`last_updated` DESC", $params);
		if($query && !$this->db->error() && $this->db->num_rows()){
			$result = $this->db->fetch_array();
			$event = $result[0];
			
			//Tournaments only, before registration deadline
			if($event['event_type'] == 2 && strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time']) > strtotime('now')){
						
				//Set admin notification email
				$admin_email = NULL;
				if($event['event_type'] == 1){
					$admin_email = $reg_settings['email_events'];
					if($event['event_category'] == 3){
						$admin_email = $reg_settings['email_conferences'];
					}
				}else if($event['event_type'] == 2){
					$admin_email = $reg_settings['email_tournaments'];
				}
				if(empty($admin_email)){
					$admin_email = $global['contact_email'];
				}

				//Check for available spots
				$registered = 0;
				$spots = $event['max_capacity']-$event['attendance'];
				if($spots > 0){

					//Get waitlist
					$waitlist = $this->get_current_wait_list($event['occurrence_id']);
					if(!empty($waitlist)){
						$eligible_attendees = array();

						//Get eligible registrants
						$get_eligible = $this->get_eligible_attendees($event['occurrence_id']);
						foreach($get_eligible as $eligible){
							if(!$eligible['overdue_invoices']){ //Cannot register with overdue invoices
								$eligible_attendees[$eligible['account_id']] = $eligible;
							}
						}

						//Get ticket price
						$pricing = $this->get_event_pricing($event['event_id']);
						if(!empty($pricing)){
							$ticket_type = $pricing[0]['price_type'];
							$ticket_price = $pricing[0]['price'];

							//Get taxes
							$taxes = $this->get_taxes($ticket_price, 'AB');

							//Set total
							$total = number_format($ticket_price+$taxes['taxes']+$event['tournament_fee'], 2, '.', '');

							//Loop through waitlist and register first eligible person
							foreach($waitlist as $wait){
								if(array_key_exists($wait['account_id'], $eligible_attendees)){

									//Insert registration
									$this->db->new_transaction();
									$params = array(
										$wait['first_name'], 
										$wait['last_name'], 
										$wait['email'], 
										$wait['phone'], 
										$taxes['taxes'], 
										$taxes['gst_rate'], 
										$taxes['pst_rate'],
										$taxes['hst_rate'],
										$event['tournament_fee'],
										$total, 
										date("Y-m-d H:i:s"), 
										$wait['account_id'], 
										'1', 
										'0'
									);
									$query = $this->db->query("INSERT INTO `reg_registrations`(`first_name`, `last_name`, `email`, `phone`, `taxes`, `gst_rate`, `pst_rate`, `hst_rate`, `fees`, `registration_total`, `registration_date`, `account_id`, `status`, `paid`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
									$reg_id = $this->db->insert_id();
									$reg_num = 'PGA'.str_pad($reg_id, 5, '0', STR_PAD_LEFT).'-02';

									//Update registration_number
									$query = $this->db->query("UPDATE `reg_registrations` SET `registration_number` = ? WHERE `registration_id` = ?", array($reg_num, $reg_id));

									//Insert attendee
									$params = array(
										$wait['account_id'],
										$reg_id,
										$event['event_id'],
										$event['occurrence_id'],
										'Registered',
										$wait['first_name'], 
										$wait['last_name'], 
										$wait['email'], 
										$wait['phone'],
										$wait['gender'],
										'1',
										$ticket_type, 
										$ticket_price,
										date("Y-m-d H:i:s")
									);
									$query = $this->db->query("INSERT INTO `reg_attendees`(`account_id`, `registration_id`, `event_id`, `occurrence_id`, `reg_status`, `first_name`, `last_name`, `email`, `phone`, `gender`, `attendee_sharing`, `ticket_type`, `ticket_price`, `date_added`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);

									//Remove from waiting list
									$query = $this->db->query("DELETE FROM `reg_waiting_list` WHERE `wait_id` = ?", array($wait['wait_id']));

									//No errors
									if(!$this->db->error()){
										$this->db->commit();

										//Send receipt
										$subject = 'Registration Confirmation';
										$order_data = array(
											'first_name' => $wait['first_name'],
											'last_name' => $wait['last_name'],
											'email' => $wait['email'],
											'phone' => $wait['phone'],
											'bill_address1' => '',
											'bill_address2' => '',
											'bill_city' => '',
											'bill_province' => '',
											'bill_postalcode' => '',
											'bill_country' => '',
											'payment_number' => '',
											'ccnumber' => '',
											'ccexpiry' => '',
											'registration_date' => date('Y-m-d H:i:s'),
											'registration_total' => $total,
											'discount' => 0,
											'promocode' => '',
											'admin_fee' => 0,
											'taxes' => $taxes['taxes'],
											'fees' => $event['tournament_fee']
										);
										$order_cart = array(0 => array(
											'event_type' => $event['event_type'],
											'event_name' => $event['name'],
											'category_id' => '',
											'start_date' => $event['start_date'],
											'end_date' => $event['end_date'],
											'attendees' => array(),
											'registration_number' => $reg_num,
											'subtotal' => $ticket_price,
										));
										$message = '<p>This email is to inform you that a spot opened up for the following tournament, and you have been successfully registered from the waiting list. To manage your registrations and payments, please <a href="' .$siteurl. '/login/">login</a> to your account.</p>';
										$receipt = $this->registration_receipt($order_data, $order_cart, $message);
										if(!empty($wait['email'])){
											send_email($wait['email'], $subject, $receipt);
										}

										//Notify admin also
										if(!empty($admin_email)){
											send_email($admin_email, $subject, $receipt);
										}
										
										//Registration is full again
										$registered++;
										if($registered >= $spots){
											break;
										}
									}

								}
							}

						}else{
							throw new Exception('Registration::wait_list_autoregister - Unable to fetch event pricing.');
						}

					}

				}
				
				//Return number of successful registrations
				return $registered;
				
			}else{
				throw new Exception('Registration::wait_list_autoregister - Event not eligible for auto-registration.');
			}

		}else{
			throw new Exception('Registration::wait_list_autoregister - Event not found. '.$this->db->error());
		}
		
		return false;
	}
	
	/*-----------------------------------/
	* Retrieve registration details
	*
	* <AUTHOR> Army
	* @param	reg_id			The ID of the registration
	* @return	Array			Array of all registration information or one row of registration info
	*/
	public function get_registration($reg_id){
		$response = array();
		
		$params = array($reg_id, $this->account_id, $this->account_id, 'Registered', 'Withdrawn');
		$query = $this->db->query("SELECT `reg_registrations`.* FROM `reg_registrations` ".
		"LEFT JOIN `reg_attendees` ON `reg_attendees`.`registration_id` = `reg_registrations`.`registration_id` ".
		"WHERE `reg_attendees`.`registration_id` = ? && (`reg_attendees`.`account_id` = ? || `reg_registrations`.`account_id` = ?) && (`reg_attendees`.`reg_status` = ? || `reg_attendees`.`reg_status` = ?)", $params);
		if($query && !$this->db->error() && $this->db->num_rows()){
			$result = $this->db->fetch_array();
			$response = $result[0];
			$response['payment_deadline'] = NULL;
			
			//Get all events
			$response['events'] = array();
			$get_events = $this->db->query("SELECT `reg_events`.`name`, `reg_events`.`event_type`, `reg_events`.`team_event`, `reg_occurrences`.*, `reg_attendees`.`occurrence_id`, `reg_event_categories`.`category_id` FROM `reg_attendees` ".
			"LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `reg_attendees`.`occurrence_id` ".
			"LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `reg_occurrences`.`event_id` ".
			"LEFT JOIN `reg_event_categories` ON `reg_events`.`event_id` = `reg_event_categories`.`event_id` ".
			"WHERE `reg_attendees`.`registration_id` = ? GROUP BY `reg_attendees`.`occurrence_id`", array($reg_id));
			if($get_events && !$this->db->error() && $this->db->num_rows()){
				$event_result = $this->db->fetch_array();
				foreach($event_result as $event){
					$response['events'][$event['event_id']] = $event;
					
					$response['events'][$event['event_id']]['attendees'] = array();
					$get_attendees = $this->db->query("SELECT * FROM `reg_attendees` WHERE `registration_id` = ? AND `occurrence_id` = ? ORDER BY `attendee_id`", array($response['registration_id'], $event['occurrence_id']));
					if($get_attendees && !$this->db->error() && $this->db->num_rows()){
						$response['events'][$event['event_id']]['attendees'] = $this->db->fetch_array();
						
						foreach($response['events'][$event['event_id']]['attendees'] as $key=>$attendee){
							$response['events'][$event['event_id']]['attendees'][$key]['addons'] = array();
							$get_attendee_options = $this->db->query("SELECT * FROM `reg_attendee_options` WHERE `attendee_id` = ?", array($attendee['attendee_id']));
							if($get_attendee_options && !$this->db->error() && $this->db->num_rows()){
								$response['events'][$event['event_id']]['attendees'][$key]['addons'] = $this->db->fetch_array();
							}
						}

					}
					
					if($response['paid'] == '0'){
						$response['payment_deadline'] = $event['payment_deadline'];
					}
				}
				
			}
			
			//Get payments
			$response['payments'] = array();
			$response['total_paid'] = 0;
			$response['admin_fee'] = 0;
			$get_payments = $this->db->query("SELECT * FROM `payments` WHERE `registration_id` = ? ORDER BY `payment_date` DESC", array($reg_id));
			if($get_payments && !$this->db->error() && $this->db->num_rows() > 0){
				$response['payments'] = $this->db->fetch_array();
				foreach($response['payments'] as $paid){
					if($paid['status'] == '1'){
						$response['total_paid'] += $paid['amount'];
						$response['admin_fee'] += $paid['admin_fee'];
					}
				}
			}
			
			//Get refunds
			$response['refunds'] = array();
			$response['total_refunded'] = 0;
			$get_refunds = $this->db->query("SELECT `refunds`.*, `payments`.`payment_number`, `payments`.`cctype`, `payments`.`ccnumber`, `payments`.`ccexpiry` FROM `refunds` LEFT JOIN `payments` ON `refunds`.`payment_id` = `payments`.`payment_id` WHERE `refunds`.`registration_id` = ? ORDER BY `refund_date` DESC", array($reg_id));
			if($get_refunds && !$this->db->error() && $this->db->num_rows() > 0){
				$response['refunds'] = $this->db->fetch_array();
				foreach($response['refunds'] as $refund){
					if($refund['status'] == '1'){
						$response['total_refunded'] += $refund['amount'];
					}
				}
			}
			
		}
		
		return $response;
	}
	
	/*-----------------------------------/
	* Formats registration receipt html
	*
	* <AUTHOR> Army
	* @param	order_data		Array of transactional data
	* @param	cart			Array of cart items
	* @param	content			HTML content to include with receipt
	* @return	html			Receipt html
	*/
	public function registration_receipt($order_data=array(), $cart=array(), $content=''){

		$html = '<h3>Registration Confirmation</h3>';
		$html .= $content;
		
		//Registration
		$html .= '<hr />
		<h6>Registration Information</h6>
		<table cellpadding="0" cellspacing="0" border="0" class="nobgs noresponsive" width="100%" style="border:0 !important;">
			<tr>
				<td valign="top" style="width:200px !important;">Registration Date:</td>
				<td>' .date('M j, Y g:iA', strtotime($order_data['registration_date'])). '</td>
			</tr>
			<tr>
				<td valign="top">Customer Name:</td>
				<td>' .$order_data['first_name']. ' ' .$order_data['last_name']. '</td>
			</tr>
			<tr>
				<td valign="top">Email Address:</td>
				<td>' .$order_data['email']. '</td>
			</tr>
			<tr>
				<td valign="top">Phone No:</td>
				<td>' .$order_data['phone']. '</td>
			</tr>';
			if($order_data['company'] != ''){
				$html .= '<tr>
					<td valign="top">Company Name:</td>
					<td>' .$order_data['company']. '</td>
				</tr>';
			}
			if($order_data['facility'] != ''){
				$html .= '<tr>
					<td valign="top">Facility Name:</td>
					<td>' .$order_data['facility']. '</td>
				</tr>';
			}
			if($order_data['address1'] != ''){
				$html .= '<tr>
					<td valign="top">Mailing Address:</td>
					<td>'.($order_data['address2'] != '' ? $order_data['address2'].' - ' : '').$order_data['address1']. '<br />'.
					$order_data['city']. ', ' .$order_data['province']. ', ' .$order_data['country']. '<br />'.
					$order_data['postal_code']. '</td>
				</tr>';
			}
			if($order_data['payment_number'] != '' && $order_data['bill_address1'] != ''){
				$html .= '<tr>
					<td valign="top">Billing Address:</td>
					<td>'.($order_data['bill_address2'] != '' ? $order_data['bill_address2'].' - ' : '').$order_data['bill_address1']. '<br />'.
					$order_data['bill_city']. ', ' .$order_data['bill_province']. ', ' .$order_data['bill_country']. '<br />'.
					$order_data['bill_postalcode']. '</td>
				</tr>';
			}
		$html .= '</table>';
		
		//Payment
		if($order_data['payment_number'] != '' && $order_data['ccnumber'] != ''){
			$html .= '<hr />
			<h6>Payment Information</h6>
			<table cellpadding="0" cellspacing="0" border="0" class="nobgs noresponsive" width="100%" style="border:0 !important;">
				<tr>
					<td valign="top" style="width:200px !important;">Cardholder:</td>
					<td>' .$order_data['ccname']. '</td>
				</tr>
				<tr>
					<td valign="top">Credit Card:</td>
					<td>' .$order_data['cctype']. ' **** **** **** ' .$order_data['ccnumber']. ' &nbsp; ' .substr($order_data['ccexpiry'], 0, 2).'/'.substr($order_data['ccexpiry'], 2). '</td>
				</tr>
				<tr>
					<td valign="top">Payment No:</td>
					<td>' .$order_data['payment_number']. '</td>
				</tr>
				<tr>
					<td valign="top">Response Code:</td>
					<td>' .$order_data['response_code']. '</td>
				</tr>
				<tr>
					<td valign="top">Transaction No:</td>
					<td>' .$order_data['txn_num']. '</td>
				</tr>
				<tr>
					<td valign="top">Authorization Code:</td>
					<td>' .$order_data['auth_code']. '</td>
				</tr>
				<tr>
					<td valign="top">Message:</td>
					<td>' .$order_data['message']. '</td>
				</tr>
			</table>';
		}

		//Cart
		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive">
			<tr>
				<th align="left">Event</th>
				<th class="right" width="100px">Total</th>
			</tr>';
			foreach($cart as $item){
				
				//Tournaments
				if($item['event_type'] == 2){
					$html .= '<tr>
						<td class="left">' .$item['event_name']. '<br />
							<small class="dblock">'. format_date_range($item['start_date'], $item['end_date']). '</small>';
							if($item['team_event'] && isset($item['attendees'][0]['partner'])){
								$html .= '<small class="dblock">Partner: '.$item['attendees'][0]['partner']['first_name'].' '.$item['attendees'][0]['partner']['last_name']. '</small>';
							}
						$html .= '<small class="dblock">'. $item['registration_number']. '</small>
						</td>
						<td class="right">$' .number_format($item['subtotal'], 2). '</td>
					</tr>';
				
				//Events
				}else{
					$html .= '<tr>
						<td class="left"><h6>' .$item['event_name']. '</h6>
							<small class="dblock">'. format_date_range($item['start_date'], $item['end_date']). '</small>
							<small class="dblock">'. $item['registration_number']. '</small>';
						$html .= "</td>
					</tr>";

					//Event attendees
					foreach($item['attendees'] as $attendee){
						$attendee_subtotal = $attendee['ticket_price'];
						$html .= '<tr>
							<td style="background:#fff !important; border-top:1px solid #eee;">' .$attendee['attendee_fields']['first_name'].' '.$attendee['attendee_fields']['last_name']. '<br />
							<small class="dblock">' .$attendee['ticket_type']. ': $' .number_format($attendee['ticket_price'], 2). '</small>';
							foreach($attendee['attendee_fields'] as $field=>$value){
								if($field != 'first_name' && $field != 'last_name' && trim($value) != ''){
									$html .= '<small class="dblock">' .ucwords(str_replace('_', ' ', $field)).': ' .$value. '</small>';
								}
							}
							if(isset($attendee['addons']) && !empty($attendee['addons'])){
								foreach($attendee['addons'] as $addon){
									if($addon['value'] != ''){
										$html .= '<small class="dblock">' .$addon['name'].': ' .$addon['value']. 
										(!empty($addon['price_adjustment']) && $addon['price_adjustment'] > 0 ? ' - $'.number_format($addon['price_adjustment'], 2) : ''). '</small>';
									}
									$attendee_subtotal += $addon['price_adjustment'];
								}
							}
							$html .= '</td>
							<td class="right" valign="top" style="background:#fff !important; border-top:1px solid #eee;">$' .number_format($attendee_subtotal, 2). '</td>
						</tr>';
					}
				}
			}
		$html .= '</table>';
		
		//Totals
		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive">';
			if($order_data['discount'] > 0){
				$html .= '<tr>
					<td align="left">' .($order_data['promocode'] != '' ? 'Discount Code: '.$order_data['promocode'] : '&nbsp;'). '</td>
					<td align="right">Discount:</td>
					<td class="right">$' .number_format($order_data['discount'], 2). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td align="right" colspan="2">Subtotal:</td>
				<td class="right">$' .number_format($order_data['registration_total']-$order_data['admin_fee']-$order_data['taxes'], 2). '</td>
			</tr>';
			$html .= '<tr>
				<td align="right" colspan="2">Taxes:</td>
				<td class="right">$' .number_format($order_data['taxes'], 2). '</td>
			</tr>';
			if(isset($order_data['fees']) && $order_data['fees'] > 0){
				$html .= '<tr>
					<td align="right" colspan="2">Skins:</td>
					<td class="right">$' .number_format($order_data['fees'], 2). '</td>
				</tr>';
			}
			if($order_data['admin_fee'] > 0){
				$html .= '<tr>
					<td align="right" colspan="2">Service Fee:</td>
					<td class="right">$' .number_format($order_data['admin_fee'], 2). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td align="right" colspan="2"><h6>Total:</h6></td>
				<td class="right" width="100px"><h6>$' .number_format($order_data['registration_total'], 2). '</h6></td>
			</tr>
		</table>';

		return($html);

	}
	
	/*-----------------------------------/
	* Formats payment receipt html
	*
	* <AUTHOR> Army
	* @param	order_data		Array of transactional data
	* @param	content			HTML content to include with receipt
	* @return	html			Receipt html
	*/
	public function payment_receipt($order_data=array(), $content=''){

		if(!isset($order_data['admin_fee'])){
			$order_data['admin_fee'] = 0;
		}
		
		$html = '<h3>Payment Confirmation</h3>';
		$html .= $content;
		
		//Order
		$html .= '<hr />
		<h6>Personal Information</h6>
		<table cellpadding="0" cellspacing="0" border="0" class="nobgs noresponsive" width="100%" style="border:0 !important;">
			<tr>
				<td valign="top" style="width:200px !important;">Payment Date:</td>
				<td>' .date('M j, Y g:iA', strtotime($order_data['payment_date'])). '</td>
			</tr>
			<tr>
				<td valign="top">Customer Name:</td>
				<td>' .$order_data['first_name']. ' ' .$order_data['last_name']. '</td>
			</tr>
			<tr>
				<td valign="top">Email Address:</td>
				<td>' .$order_data['email']. '</td>
			</tr>
			<tr>
				<td valign="top">Phone No:</td>
				<td>' .$order_data['phone']. '</td>
			</tr>';
			if($order_data['payment_type'] == 'Credit Card' && $order_data['bill_address1'] != ''){
				$html .= '<tr>
					<td valign="top">Billing Address:</td>
					<td>'.($order_data['bill_address2'] != '' ? $order_data['bill_address2'].' - ' : '').$order_data['bill_address1']. '<br />'.
					$order_data['bill_city']. ', ' .$order_data['bill_province']. ', ' .$order_data['bill_country']. '<br />'.
					$order_data['bill_postalcode']. '</td>
				</tr>';
			}
		$html .= '</table>';
		
		//Payment
		$html .= '<hr />
		<h6>Payment Information</h6>
		<table cellpadding="0" cellspacing="0" border="0" class="nobgs noresponsive" width="100%" style="border:0 !important;">';
		if($order_data['payment_type'] == 'Credit Card' && $order_data['ccnumber'] != ''){
			$html .= '<tr>
				<td valign="top" style="width:200px !important;">Cardholder:</td>
				<td>' .$order_data['ccname']. '</td>
			</tr>
			<tr>
				<td valign="top">Credit Card:</td>
				<td>' .$order_data['cctype']. ' **** **** **** ' .$order_data['ccnumber']. ' &nbsp; ' .substr($order_data['ccexpiry'], 0, 2).'/'.substr($order_data['ccexpiry'], 2). '</td>
			</tr>
			<tr>
				<td valign="top">Payment No:</td>
				<td>' .$order_data['payment_number']. '</td>
			</tr>
			<tr>
				<td valign="top">Response Code:</td>
				<td>' .$order_data['response_code']. '</td>
			</tr>
			<tr>
				<td valign="top">Transaction No:</td>
				<td>' .$order_data['txn_num']. '</td>
			</tr>
			<tr>
				<td valign="top">Authorization Code:</td>
				<td>' .$order_data['auth_code']. '</td>
			</tr>
			<tr>
				<td valign="top">Message:</td>
				<td>' .$order_data['message']. '</td>
			</tr>';
		}else{
			$html .= '<tr>
				<td valign="top" style="width:200px !important;">Payment No:</td>
				<td>' .$order_data['payment_number']. '</td>
			</tr>
			<tr>
				<td valign="top">Payment Type:</td>
				<td>' .$order_data['payment_type']. '</td>
			</tr>';
		}
		$html .= '</table>';

		//Cart
		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive">
			<tr>
				<th align="left">' .$order_data['record_name']. '</th>
				<th class="right" width="100px">Total</th>
			</tr>
			<tr>
				<td class="left">' .$order_data['record_number']. '</td>
				<td class="right">$' .number_format($order_data['ordertotal']-$order_data['admin_fee'], 2). '</td>
			</tr>
		</table>';
		if($order_data['admin_fee'] > 0){
			$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive">
				<tr>
					<td align="right">Subtotal:</td>
					<td class="right" width="120px">$' .number_format($order_data['ordertotal']-$order_data['admin_fee'], 2). '</td>
				</tr>
				<tr>
					<td align="right">Service Fee:</td>
					<td class="right">$' .number_format($order_data['admin_fee'], 2). '</td>
				</tr>
				<tr>
					<td align="right"><h6>Total:</h6></td>
					<td class="right"><h6>$' .number_format($order_data['ordertotal'], 2). '</h6></td>
				</tr>
			</table>';
		}

		return($html);

	}
	
	/*-----------------------------------/
	* Formats refund receipt html
	*
	* <AUTHOR> Army
	* @param	order_data		Array of transactional data
	* @param	content			HTML content to include with receipt
	* @return	html			Receipt html
	*/
	public function refund_receipt($order_data=array(), $content=''){

		$html = '<h3>Refund Confirmation</h3>';
		$html .= $content;
		
		//Order
		$html .= '<hr />
		<h6>Personal Information</h6>
		<table cellpadding="0" cellspacing="0" border="0" class="nobgs noresponsive" width="100%" style="border:0 !important;">
			<tr>
				<td valign="top" style="width:200px !important;">Refund Date:</td>
				<td>' .date('M j, Y g:iA', strtotime($order_data['refund_date'])). '</td>
			</tr>
			<tr>
				<td valign="top">Customer Name:</td>
				<td>' .$order_data['first_name']. ' ' .$order_data['last_name']. '</td>
			</tr>
			<tr>
				<td valign="top">Email Address:</td>
				<td>' .$order_data['email']. '</td>
			</tr>
			<tr>
				<td valign="top">Phone No:</td>
				<td>' .$order_data['phone']. '</td>
			</tr>';
		$html .= '</table>';
		
		//Refund
		$html .= '<hr />
		<h6>Refund Information</h6>
		<table cellpadding="0" cellspacing="0" border="0" class="nobgs noresponsive" width="100%" style="border:0 !important;">';
		if($order_data['refund_type'] == 'Credit Card' && $order_data['ccnumber'] != ''){
			$html .= '<tr>
				<td valign="top" style="width:200px !important;">Cardholder:</td>
				<td>' .$order_data['ccname']. '</td>
			</tr>
			<tr>
				<td valign="top">Credit Card:</td>
				<td>' .$order_data['cctype']. ' **** **** **** ' .$order_data['ccnumber']. ' &nbsp; ' .substr($order_data['ccexpiry'], 0, 2).'/'.substr($order_data['ccexpiry'], 2). '</td>
			</tr>
			<tr>
				<td valign="top">Refund No:</td>
				<td>' .$order_data['refund_number']. '</td>
			</tr>
			<tr>
				<td valign="top">Transaction No:</td>
				<td>' .$order_data['txn_num']. '</td>
			</tr>';
		}else{
			$html .= '<tr>
				<td valign="top" style="width:200px !important;">Refund No:</td>
				<td>' .$order_data['refund_number']. '</td>
			</tr>
			<tr>
				<td valign="top">Refund Type:</td>
				<td>' .$order_data['refund_type']. '</td>
			</tr>';
		}
		$html .= '</table>';

		//Cart
		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive">';
		
			//Specific attendee refunds
			if(isset($order_data['attendees']) && !empty($order_data['attendees']) && isset($order_data['attendee_refund_amount']) && $order_data['attendee_refund_amount'] > 0){
				$html .= '<tr>
					<th align="left">Registration</th>
					<th class="right" width="100px">Refunded</th>
				</tr>';
				foreach($order_data['attendees'] as $attendee){
					if($attendee['refund_amount'] > 0){
						$html .= '<tr>
							<td class="left">' .$attendee['first_name'].' '.$attendee['last_name']. ' - ' .$attendee['event_name']. '
								<small class="dblock">' .$order_data['record_name']. ' ' .$order_data['record_number']. '</small>
							</td>
							<td class="right">$' .number_format($attendee['refund_amount'], 2). '</td>
						</tr>';
					}
				}
				$html .= '<tr>
					<td class="right"><h6>Total Refunded:</h6></td>
					<td class="right"><h6>$' .number_format($order_data['ordertotal'], 2). '</h6></td>
				</tr>';
				
			//Standard refund
			}else{
				$html .= '<tr>
					<th align="left">' .$order_data['record_name']. '</th>
					<th class="right" width="100px">Total</th>
				</tr>
				<tr>
					<td class="left">' .$order_data['record_number']. '</td>
					<td class="right">$' .number_format($order_data['ordertotal'], 2). '</td>
				</tr>';
			}
		$html .= '</table>';

		return($html);

	}
	
	/*-----------------------------------/
	* Formats hole in one receipt
	*
	* <AUTHOR> Army
	* @param	order_data		Array of transactional data
	* @param	cart			Array of cart items
	* @param	content			HTML content to include with receipt
	* @return	html			Receipt html
	*/
	public function hio_receipt($order_data=array(), $cart=array(), $content=''){

		$html = '<h3>Hole In One Event</h3>';
		$html .= $content;
		
		//Contact
		$html .= '<hr />
		<h6>Contact Information</h6>
		<table cellpadding="0" cellspacing="0" border="0" class="nobgs noresponsive" width="100%" style="border:0 !important;">
			<tr>
				<td valign="top" style="width:150px !important;">Submitted By:</td>
				<td>' .$order_data['first_name']. ' ' .$order_data['last_name']. '</td>
			</tr>
			<tr>
				<td valign="top">Facility:</td>
				<td>' .$order_data['facility_name']. '</td>
			</tr>
			<tr>
				<td valign="top">Email Address:</td>
				<td>' .$order_data['email']. '</td>
			</tr>';
			if(!empty($order_data['phone'])){
				$html .= '<tr>
					<td valign="top">Phone No:</td>
					<td>' .$order_data['phone']. '</td>
				</tr>';
			}
		$html .= '</table>';
		
		//Event
		$html .= '<hr />
		<h6>Event Information</h6>
		<table cellpadding="0" cellspacing="0" border="0" class="nobgs noresponsive" width="100%" style="border:0 !important;">
			<tr>
				<td valign="top" style="width:150px !important;">Name:</td>
				<td>' .$order_data['event_name']. '</td>
			</tr>
			<tr>
				<td valign="top">Field:</td>
				<td>' .$order_data['event_field']. ' Golfers</td>
			</tr>
			<tr>
				<td valign="top">Date' .(count($order_data['event_dates']) > 1 ? 's' : ''). ':</td>
				<td>' .implode(';&nbsp; ', $order_data['event_dates']). '</td>
			</tr>
			<tr>
				<td valign="top">Invoice:</td>
				<td>' .(!empty($order_data['invoice_number']) ? $order_data['invoice_number'] : 'Pending Approval'). '</td>
			</tr>
		</table>';
		
		//Comments
		if(trim($order_data['comments']) != ''){
			$html .= '<p>' .nl2br($order_data['comments']). '</p>';	
		}

		//Cart
		foreach($cart as $course){
			$html .= '<h6>' .$course['course_name']. '</h6>
			<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive">
				<tr>
					<th align="left">Hole</th>
					<th align="left">Men</th>
					<th align="left">Women</th>
					<th align="right">Prize</th>
					<th align="right" width="100px">Premium</th>
				</tr>';
				foreach($course['holes'] as $hole=>$info){
					$html .= '<tr>
						<td>' .$hole. '</td>
						<td>' .(!empty($info['yards_men']) ? $info['yards_men'].' Yards' : ''). '</td>
						<td>' .(!empty($info['yards_women']) ? $info['yards_women'].' Yards' : ''). '</td>
						<td align="right">$' .number_format($info['prize'], 2). '</td>
						<td align="right">$' .number_format($info['premium'], 2). '</td>
					</tr>';
				}
			$html .= '</table>';
		}
		
		//Totals
		$html .= '<h6>Event Totals</h6>
		<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive">
			<tr>
				<td align="right">Total Prize Amount:</td>
				<td class="right" width="100px">$' .number_format($order_data['prize_total'], 2). '</td>
			</tr>
			<tr>
				<td align="right" valign="top">Premium Per Date:</td>
				<td class="right" valign="top">$' .number_format($order_data['premium_total'], 2). '</td>
			</tr>
			<tr>
				<td align="right" valign="top"><h6>Total Premium:</h6></td>
				<td class="right" valign="top"><h6>$' .number_format($order_data['premium_total']*count($order_data['event_dates']), 2). '</h6></td>
			</tr>
		</table>';
	

		return($html);

	}

	/*-----------------------------------/
	* Generate invoice html
	*
	* <AUTHOR> Army
	* @param	$invoice		Array of invoice data
	* @return	Html			Invoice html for output
	*/
	public function generate_invoice($invoice){
		global $global;
		
		//Format html	
		$html = '<table cellpadding="0" cellspacing="0" border="0" width="100%" style="line-height:16px;">
			<tr>
				<td width="120px"><img src="' .$_SERVER['DOCUMENT_ROOT'].'/images/ui/pga-logo.png" width="120px" /></td>
				<td align="center">
					<h4>PGA of Alberta</h4>
					<p style="font-size:10px;">' .$global['full_address']. '<br />
					Phone: ' .$global['contact_phone']. '
					' .(trim($global['contact_fax']) != '' ? ' / Fax: ' .$global['contact_fax'] : ''). '<br />
					' .(trim($global['contact_toll_free']) != '' ? 'Toll Free: ' .$global['contact_toll_free']. '<br />' : ''). '
					Email: ' .$global['contact_email']. '</p>
				</td>
				<td width="120px">&nbsp;</td>
			</tr>
		</table><br /><br />';
		
		//Registration information
		$html .= '<h4>Registration Information</h4>
		<table cellpadding="5" cellspacing="0" border="0" width="100%" style="border:1px solid #ccc;">
			<tr>
				<td width="120px" bgcolor="#eee">Registration No:</td>
				<td>' .$invoice['registration_number']. '</td>
			</tr>
			<tr>
				<td bgcolor="#eee">Registration Date:</td>
				<td>' .date("M j, Y", strtotime($invoice['registration_date'])). '</td>
			</tr>
			<tr>
				<td bgcolor="#eee">Contact Name:</td>
				<td>' .$invoice['first_name'].' '.$invoice['last_name']. '</td>
			</tr>
			<tr>
				<td bgcolor="#eee">Email Address:</td>
				<td>' .$invoice['email']. '</td>
			</tr>
			<tr>
				<td bgcolor="#eee">Phone No:</td>
				<td>' .$invoice['phone'].'</td>
			</tr>';
			if($invoice['company'] != ''){
				$html .= '<tr>
					<td bgcolor="#eee">Company Name:</td>
					<td>' .$invoice['company']. '</td>
				</tr>';
			}
			if($invoice['facility'] != ''){
				$html .= '<tr>
					<td bgcolor="#eee">Facility Name:</td>
					<td>' .$invoice['facility']. '</td>
				</tr>';
			}
		$html .= '</table><br />';
		
		//Events & Tournaments
		foreach($invoice['events'] as $event){
			$html .= '<h4>' .$event['name']. ' - ' .format_date_range($event['start_date'], $event['end_date']). '</h4>';
			
			foreach($event['attendees'] as $attendee){
				$html .= '<table cellpadding="5" cellspacing="0" border="0" width="100%" style="border:1px solid #ccc;">
					<tr>
						<td valign="top" bgcolor="#eee" width="120px">' .$attendee['first_name'].' '.$attendee['last_name']. '<br /><em>'.$attendee['reg_status']. '</em></td>
						<td valign="top" style="padding:0;">
							<table cellpadding="5" cellspacing="0" border="0">';
								if($event['event_type'] != 2){
									$html .= '<tr><td>' .$attendee['ticket_type']. ': $' .number_format($attendee['ticket_price'], 2).'</td></tr>';
								}
								if($attendee['email'] != ''){
									$html .= '<tr><td>Email: '.$attendee['email'].'</td></tr>';
								}
								if($attendee['phone'] != ''){
									$html .= '<tr><td>Phone: '.$attendee['phone'].'</td></tr>';
								}
								if($attendee['company'] != ''){
									$html .= '<tr><td>Company: '.$attendee['company'].'</td></tr>';
								}
								if($attendee['facility'] != ''){
									$html .= '<tr><td>Facility: '.$attendee['facility'].'</td></tr>';
								}
								if($attendee['position'] != ''){
									$html .= '<tr><td>Position: '.$attendee['position'].'</td></tr>';
								}
								if($attendee['comments'] != ''){
									$html .= '<tr><td>Comments: '.$attendee['comments'].'</td></tr>';
								}
								if(!empty($attendee['handicap'])){
									$html .= '<tr><td>Handicap: '.$attendee['handicap'].'</td></tr>';
								}
								$attendee_addons = 0;
								if(isset($attendee['addons']) && !empty($attendee['addons'])){
									foreach($attendee['addons'] as $addon){
										$html .= '<tr><td>' .$addon['name']. ': '.$addon['value']. ($addon['price_adjustment'] > 0 ? ' - $'.number_format($addon['price_adjustment'], 2) : '').'</td></tr>';
										$attendee_addons += $addon['price_adjustment'];
									}
								}
							$html .= '</table>
						</td>';
						if($event['event_type'] == 2){
							$html .= '<td valign="top" align="right">' .$attendee['ticket_type']. ':</td> 
							<td valign="top" align="right" width="100px">$' .number_format($attendee['ticket_price'], 2). '</td>';
						}else{
							$html .= '<td valign="top" align="right">Attendee Total:</td> 
							<td valign="top" align="right" width="100px">$' .number_format($attendee['ticket_price']+$attendee_addons, 2). '</td>';
						}
					$html .= '</tr>
				</table><br />';
			}
		}

		//Display totals
		$html .= '<h4>Registration Totals</h4>
		<table cellpadding="10" cellspacing="0" border="0" width="100%" style="border:1px solid #ccc;">';
			if(isset($invoice['discount']) && $invoice['discount'] > 0){
				$html .= '<tr>
					<td align="right">Discount:</td>
					<td align="right">$' .number_format($invoice['discount'], 2). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td align="right" bgcolor="#eee">Subtotal:</td>
				<td align="right" bgcolor="#eee" width="100px">$' .number_format($invoice['registration_total']-$invoice['taxes'], 2). '</td>
			</tr>';
			$html .= '<tr>
				<td align="right">Taxes:</td>
				<td align="right">$' .number_format($invoice['taxes'], 2). '</td>
			</tr>';
			if(isset($invoice['fees']) && $invoice['fees'] > 0){
				$html .= '<tr>
					<td align="right" bgcolor="#eee">Skins:</td>
					<td align="right" bgcolor="#eee">$' .number_format($invoice['fees'], 2). '</td>
				</tr>';
			}else{
				$html .= '<tr>
					<td align="right" bgcolor="#eee">Service Fee:</td>
					<td align="right" bgcolor="#eee">$' .number_format($invoice['admin_fee'], 2). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td align="right">Total:</td>
				<td align="right">$' .number_format($invoice['registration_total']+$invoice['admin_fee'], 2). '</td>
			</tr>
			<tr>
				<td align="right" bgcolor="#eee">Total Paid:</td>
				<td align="right" bgcolor="#eee">$' .number_format($invoice['total_paid']+$invoice['admin_fee'], 2). '</td>
			</tr>';
			if($invoice['total_refunded'] > 0){
				$html .= '<tr>
					<td align="right">Total Refunded:</td>
					<td align="right">-$' .number_format($invoice['total_refunded'], 2). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td align="right" bgcolor="' .($invoice['total_refunded'] > 0 ? '#eee' : '#fff'). '"><strong>Balance:</strong></td>
				<td align="right" bgcolor="' .($invoice['total_refunded'] > 0 ? '#eee' : '#fff'). '"><strong>$' .number_format($invoice['registration_total']-$invoice['total_paid']+$invoice['total_refunded'], 2). '</strong></td>
			</tr>
		</table>';
		
		return $html;
	}
	
	/*-----------------------------------/
	* Tracks amount of views for an event
	*
	* <AUTHOR> Army
	* @param	event_id		ID of the event being viewed
	* @return	boolean			True/false upon success status
	*/
	public function log_event_view($event_id){
		$params = array($event_id, $this->account_id, session_id(), $this->get_ip(), gethostbyaddr($this->get_ip()), date("Y-m-d H:i:s"));
		$query = $this->db->query("INSERT INTO `reg_event_views`(`event_id`, `account_id`, `session_id`, `ip_address`, `hostname`, `visit_time`) VALUES(?,?,?,?,?,?)", $params);
		if($query && !$this->db->error()){
			return true;
		}else{
			return false;
		}
	}

	/*-----------------------------------/
	* Get the registration payment options
	*
	* <AUTHOR> Army
	* @return	Array		Selected payment options
	*/
	public function get_payment_options(){
		$response = array();
		
		$params = array("Active");
		$query = $this->db->query("SELECT * FROM `payment_options` WHERE status = ?", $params);
		if($query && !$this->db->error()){
			$response = $this->db->fetch_array();
		}
				
		return $response;
	}
	
	/*-----------------------------------/
	* Get the total taxes for the registration order
	*
	* <AUTHOR> Army
	* @param	$subtotal	The subtotal of the order
	* @param	$province	The ship to province code
	* @return	Integer		Total taxes and rate percentages
	*/
	public function get_taxes($subtotal, $province){
		$taxes['taxes'] = 0.00;
		
		$reg_settings = $this->get_reg_settings();
		$gst = $reg_settings['federal_gst'];
		
		$params = array($province);
		$query = $this->db->query("SELECT * FROM `taxes` WHERE `state` = ?", $params);
		
		$pst = 0;
		$ishst = 0;
		if($query && !$this->db->error() && $this->db->num_rows() > 0){
			$taxes_sql = $this->db->fetch_array();
			$this_tax = $taxes_sql[0];
			$pst = $this_tax['rate'];
			$ishst = $this_tax['hst'];
		}
				
		$taxes['taxes'] = $subtotal*(($gst/100)+($pst/100));
		$taxes['gst_rate'] = $gst;
		if($ishst){
			$taxes['pst_rate'] = 0;
			$taxes['hst_rate'] = ($gst+$pst);
		}else{
			$taxes['pst_rate'] = $pst;
			$taxes['hst_rate'] = 0;
		}
		
		return $taxes;
	}	

	/*-----------------------------------/
	* Searches an array recursively for a preg match
	*
	* <AUTHOR> Army
	* @param	find		The search term
	* @param	in_array	The array to search in
	* @param	keys_found	The keys the term was found in
	* @return	int			Keys where the term was found
	*/
	private function array_search_preg($find, $in_array, $keys_found=array()){
		if(is_array($in_array)){
			foreach($in_array as $key => $val){
				if(is_array($val)){
					$keys_found = $this->array_search_preg($find, $val, $keys_found);
				} else {
					if(preg_match('/'.$find.'/i', $val)){
						$keys_found[] = $key;
					}
				}
			}
			return $keys_found;
		}
		return false;
	}	
	
	/*-----------------------------------/
	* Get the user's IP address
	*
	* <AUTHOR> Army
	* @return	String		IP Address
	*/
	public function get_ip(){
		foreach (array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR') as $key){
			if (array_key_exists($key, $_SERVER) === true){
				foreach (explode(',', $_SERVER[$key]) as $ip){
					if (filter_var($ip, FILTER_VALIDATE_IP) !== false){
						return $ip;
					}
				}
			}
		}
	}
	
	/*-----------------------------------/
	* Formats a timestamp to display as text
	*
	* <AUTHOR> Army
	* @param	$timestamp		Timestamp to be formatted
	* @param	$format			Date format to be returned, if over a week ago
	* @return	String			Formatted text
	*/
	public function ago($timestamp, $format='M j, Y'){
		$granularity=1;
		$difference = time() - strtotime($timestamp);
		if($difference < 0) return 'Just Now';
		elseif($difference < 864000){
			$periods = array('week' => 604800,'day' => 86400,'hour' => 3600,'minute' => 60,'second' => 1);
			$output = '';
			foreach($periods as $key => $value){
				if($difference >= $value){
					$time = round($difference / $value);
					$difference %= $value;
					$output .= ($output ? ' ' : '').$time.' ';
					$output .= (($time > 1) ? $key.'s' : $key);
					$granularity--;
				}
				if($granularity == 0) break;
			}
			return ($output ? $output : '0 seconds').' ago';
		}
		else return "on " .date($format, strtotime($timestamp));
	}
	
	/*-----------------------------------/
	* Return global website settings from db
	*
	* <AUTHOR> Army
	* @return	Array of global data
	*/
	public function global_settings(){
		$global_settings = array();
		
		$params = array(1);
		$query = $this->db->query("SELECT * FROM `global_settings` WHERE id = ?",$params);
		if($query && !$this->db->error() && $this->db->num_rows()){
			$global_settings = $this->db->fetch_array();
			$global_settings = $global_settings[0];
		}
		
		return $global_settings;
	}

}

?>