<?php if (!empty($page['page_form'])) { ?>
<form id="landing-form" name="landing-form" action="" method="post" data-recaptcha="#recaptcha-landing-form">
	<?php echo $page['form_title'] ? '<h2 class="landing-form-title">'.fancy_text($page['form_title']).'</h2>' : ''; ?>
	<?php echo $page['form_description'] ? '<div class="landing-form-description">'.nl2br($page['form_description']).'</div>' : ''; ?>

	<fieldset>
		<?php  
		$fcount = 0;
		foreach($page['page_form'] as $name => $field){
			$label = $field['label'].(!$field['required'] ? ' (optional)' : '');

			echo '<div class="form-field'.($field['type'] == 'textarea' ? ' full' : '').'">
				<label for="landing-form-field-'.$fcount.'">'.$label.'</label>';

				switch($field['type']){
					//Dropdown
					case 'select':
						echo '<select name="'.$name.'" id="landing-form-field-'.$fcount.'" class="select'.($field['required'] ? ' jsvalidate' : '').'"'.($field['required'] ? ' required' : '').'>
							<option value="">'.($label ?: '- Select -').'</option>';
							foreach ($field['options'] as $option){
								echo '<option value="'.$option.'">'.$option.'</option>';
							}
						echo '</select>';
					break;

					//Textbox
					case 'textarea':
						echo '<textarea name="'.$name.'" id="landing-form-field-'.$fcount.'" class="textarea'.($field['required'] ? ' jsvalidate' : '').'"'.($field['required'] ? ' required' : '').'></textarea>';
					break;

					//Text, Email, Phone, etc
					default:
						echo '<input type="'.$field['type'].'" name="'.$name.'" id="landing-form-field-'.$fcount.'" class="input'.($field['required'] ? ' jsvalidate' : '').'"'.($field['required'] ? ' required' : '').' />';
					break;
				}
			echo '</div>';

			$fcount++;
		}
		?>
	</fieldset>

	<div class="form-buttons right">
		<button type="submit" name="submitform" class="button submit"><span><?php echo $page['form_button_text'] ?: 'Submit'; ?></span></button>
	</div>

	<div class="recaptcha-modal hidden-modal" title="Verify You&rsquo;re Not a Robot">
		<div class="recaptcha-wrapper">
			<div id="recaptcha-landing-form" class="g-recaptcha" data-sitekey="<?php echo $global['recaptcha_key']; ?>"></div>
		</div>
	</div>

	<input type="hidden" name="g-recaptcha-response" value="" />
	<input type="hidden" name="xid" value="<?php echo $_COOKIE['xid']; ?>" />
	<input type="hidden" name="page_id" value="<?php echo PAGE_ID; ?>" />
	<input type="hidden" name="recipient" value="<?php echo $page['form_recipient']; ?>" />
	<input type="hidden" name="inquiry" value="Lead" />
</form>
<?php } ?>