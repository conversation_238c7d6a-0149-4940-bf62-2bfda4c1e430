<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	include("includes/widgets/searchform.php");
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Applications  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";
		
			echo "<thead>";
				echo "<th width='400px'>Applicant Name</th>";
				echo "<th>Job Posting</th>";
				echo "<th class='{sorter:\"monthDayYear\"}'>Application Date</th>";
				echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>".$row['first_name'].' '.$row['last_name']."</td>";
					echo "<td>".$row['title']."</td>";
					echo "<td>".date("F d, Y", strtotime($row['timestamp']))."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row['application_id']. "' class='button-sm'><i class='fa fa-eye'></i>View</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager(50);
		
		echo "</div>";	
	echo "</div>";

}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$row = $data;
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

	//Application details
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Application Details
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
				<tr>
					<td width='150px'>Applicant Name:</td>
					<td>".$row['first_name'].' '.$row['last_name']."</td>
				</tr>
				<tr>
					<td width='150px'>Applied To:</td>
					<td><a href='".$sitemap[11]['page_url'].'?action=edit&item_id='.$row['career_id']."'>".$row['title']."</a></td>
				</tr>
				<tr>
					<td>Application Date:</td>
					<td>".date("F d, Y g:iA",strtotime($row['timestamp'])). "</td>
				</tr>
				<tr>
					<td>Email:</td>
					<td>".$row['email']. "</td>
				</tr>
				<tr>
					<td>Phone:</td>
					<td>".formatPhoneNumber($row['phone']). "</td>
				</tr>
				<tr>
					<td>Resume:</td>
					<td>".($row['resume'] ? "<a href='".$root."download.php?file=".$row['resume']."&dir=resumes' target='_blank'><i class='fa fa-download'></i>&nbsp;Download" : "<em>None Provided</em>")."</td>
				</tr>";
			echo "</table>";
		echo "</div>";
	echo "</div>"; //Application details

	//Custom sticky footer
	echo '<footer id="cms-footer" class="resize">
	    <button type="button" name="delete" value="delete" class="button delete"><i class="fa fa-trash"></i>Delete</button>
		<a href="' .PAGE_URL. '" class="cancel">Cancel</a>
	</footer>';

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "<input type='hidden' name='keep_tags[]' value='TINYMCE_Editor' />";
	echo "</form>";

}

?>