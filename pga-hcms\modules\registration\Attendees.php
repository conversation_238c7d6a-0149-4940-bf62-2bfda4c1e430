<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['registration-attendees']) {

	//Define vars
	$record_db = 'reg_attendees';
	$record_id = 'attendee_id';
	$record_name = 'Attendee';

	$errors = false;
	$required = array();

	$accountpage = $sitemap[7];
	$regpage = $sitemap[69];

	//Register attendee
	if(ACTION == 'add'){

		include("modules/registration/Register.php");

	}else{

		//Get all events
		$events = array();
		$get_events = $db->query("SELECT * FROM `reg_events` WHERE `status` <> ? ORDER BY `event_type`, `name`, `event_id`", array('Trashed'));
		if($get_events && !$db->error() && $db->num_rows() > 0) {
			$events = $db->fetch_array();
		}

		//Get attendee statuses
		$reg_statuses = $db->get_enum_vals('reg_attendees', 'reg_status');

		$show_search_form = true;

		//Set defaults
		$db_columns = array(); // for SELECT in query
		$alias_columns = array(); // for listing label
		$table_columns = array(); // for listing value

		//Edit attendee
		if(ACTION == 'edit'){

			//Get Attendee
			$get_attendee = $db->query("SELECT `$record_db`.*, IFNULL(`$record_db`.`facility`, `facilities`.`facility_name`) AS `facility`, `reg_registrations`.`registration_number`, `reg_registrations`.`registration_date`, `reg_registrations`.`account_id` AS `registrant_id`, `reg_registrations`.`email` AS `registrant_email`, `reg_registrations`.`paid` FROM `$record_db` ".
			"LEFT JOIN `reg_registrations` ON `reg_registrations`.`registration_id` = `$record_db`.`registration_id` ".
			"LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` ".
			"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
			"WHERE `$record_db`.`$record_id` = ? GROUP BY `$record_db`.`$record_id`", array(ITEM_ID));
			if($get_attendee && !$db->error() && $db->num_rows()){
				$result = $db->fetch_array();
				$records_arr[ITEM_ID] = $result[0];

				//Get event pricing
				$records_arr[ITEM_ID]['event_pricing'] = $Registration->get_event_pricing($records_arr[ITEM_ID]['event_id']);
				$ticket_types = array_combine(array_column($records_arr[ITEM_ID]['event_pricing'], 'pricing_id'), $records_arr[ITEM_ID]['event_pricing']);

				//Get occurrence
				$get_occurrence = $db->query("SELECT `reg_occurrences`.*, `reg_events`.`name`, `reg_events`.`event_type`, `reg_events`.`team_event` FROM `reg_occurrences` LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `reg_occurrences`.`event_id` WHERE `occurrence_id` = ?", array($records_arr[ITEM_ID]['occurrence_id']));
				if($get_occurrence && !$db->error() && $db->num_rows()){
					$result = $db->fetch_array();
					$occurrence = $result[0];
				}

				//Get occurrence addons
				$occurrence_addons = $Registration->get_occurrence_addons($records_arr[ITEM_ID]['occurrence_id']);

				//Get selected addons
				$records_arr[ITEM_ID]['addons'] = array();
				$selected_addons_by_name = array(); //Selected addons (addon names are assumed as unique/used as keys)
				$selected_addons_by_id = array(); //Selected addons (option_ids used as keys)
				$get_addons = $db->query("SELECT * FROM `reg_attendee_options` WHERE `attendee_id` = ? ORDER BY `option_id`", array(ITEM_ID));
				if($get_addons && !$db->error()){
					$records_arr[ITEM_ID]['addons'] = $db->fetch_array();
					foreach($records_arr[ITEM_ID]['addons'] as $this_addon){
						$selected_addons_by_name[$this_addon['name']] = $this_addon;
						$selected_addons_by_id[$this_addon['option_id']] = $this_addon;
					}
				}

				//Compile deleted addons (addons that have been deleted after the user registered)
				$deleted_addons = array();
				$deleted_addon_names = array_diff(array_column($records_arr[ITEM_ID]['addons'], 'name'), array_column($occurrence_addons, 'name'));
				foreach($records_arr[ITEM_ID]['addons'] as $this_addon){
					if(in_array($this_addon['name'], $deleted_addon_names)){
						$deleted_addons[] = $this_addon;
					}
				}

				//Check for partner
				$records_arr[ITEM_ID]['partner'] = array();
				if(!empty($records_arr[ITEM_ID]['partner_id'])){
					$get_partner = $db->query("SELECT `$record_db`.* FROM `$record_db` WHERE `$record_db`.`$record_id` = ? && `$record_db`.`reg_status` = ?", array($records_arr[ITEM_ID]['partner_id'], 'Registered'));
					if($get_partner && !$db->error() && $db->num_rows()){
						$result = $db->fetch_array();
						$records_arr[ITEM_ID]['partner'] = $result[0];
					}
				}else{
					$get_partner = $db->query("SELECT `$record_db`.* FROM `$record_db` WHERE `$record_db`.`partner_id` = ? && `$record_db`.`reg_status` = ?", array($records_arr[ITEM_ID]['attendee_id'], 'Registered'));
					if($get_partner && !$db->error() && $db->num_rows()){
						$result = $db->fetch_array();
						$records_arr[ITEM_ID]['partner'] = $result[0];
					}
				}

				$data = $row = $records_arr[ITEM_ID];

			//Not found
			}else{
				$CMSBuilder->set_system_alert('Requested item was not found.', false);
				header('Location:' .PAGE_URL);
				exit();
			}

			//Save item
			if(isset($_POST['save'])){
				$alertmsg = '';
				$update_pricing = false;
				$changelog_fields = array(
					'reg_status' => array(
						'field' => 'reg_status',
						'label' => 'Registration Status'
					),
					'ticket_type' => array(
						'field' => 'ticket_type',
						'label' => 'Ticket Type'
					),
					'ticket_price' => array(
						'field' => 'ticket_price',
						'label' => 'Ticket Price'
					),
					'email' => array(
						'field' => 'email',
						'label' => 'Email Address'
					),
					'update_addon' => array(
						'field' => 'update_addon',
						'label' => 'Addon(s)'
					),
					'delete_addon' => array(
						'field' => 'delete_addon',
						'label' => 'Deleted Addon(s)'
					),
					'update_pricing' => array(
						'field' => NULL,
						'label' => 'Registration Pricing'
					),
					'notes' => array(
						'field' => 'notes',
						'label' => 'Notes'
					)
				); //Fields the change log should keep track off

				$_POST['ticket_type'] = $data['ticket_type'];
				$_POST['ticket_price'] = $data['ticket_price'];

				//Event is over so status, ticket type, and addons are not editable
				if(isset($occurrence['end_date']) && $occurrence['end_date'] < date("Y-m-d")){
					$_POST['reg_status'] = $row['reg_status'];
					$_POST['pricing_id'] = NULL;
					$_POST['update_addon'] = array();
					$_POST['delete_addon'] = array();
				}

				//Determine ticket type and pricing
				if($_POST['pricing_id'] ?? false){
					$reference_ticket = $ticket_types[$_POST['pricing_id']] ?? NULL;

					//Only update ticket type and pricing if selected ticket is valid
					if($reference_ticket){
						$_POST['ticket_type'] = $reference_ticket['price_type'];
						$_POST['ticket_price'] = $reference_ticket['price'];

						if($_POST['ticket_price'] != $data['ticket_price']){
							$update_pricing = true;
						}
					}
				}

				//Compile addons to be inserted
				$addons_to_insert = array();
				foreach(($_POST['update_addon'] ?? []) as $addon_id => $option_id){
					if(!$option_id){
						continue;
					}

					$reference_addon = $occurrence_addons[$addon_id] ?? [];
					$reference_option = $reference_addon['options'][$option_id] ?? [];

					//Only flag for insertion if the selected addon option is valid
					if($reference_addon && $reference_option){
						$selected_option = $selected_addons_by_name[$reference_addon['name']];

						$addons_to_insert[] = array(
							'reference_addon_id' => $addon_id, //Used for logging
							'option_id' => ($_POST['current_option_id'][$addon_id] ?? NULL), //ID of the option to update in `reg_attendee_options` table
							'attendee_id' => ITEM_ID,
							'name' => $reference_addon['name'] ?? NULL,
							'value' => $reference_option['name'] ?? NULL,
							'price_adjustment' => $reference_option['price_adjustment'] ?? NULL
						);

						if($selected_option['price_adjustment'] != $reference_option['price_adjustment']){
							$update_pricing = true;
						}
					}
				}

				//Compile addons to be deleted
				$addons_to_delete = array();
				foreach(($_POST['delete_addon'] ?? []) as $option_id){
					$reference_option = $deleted_addon_names[$selected_addons_by_id[$option_id]['name']] ?? [];

					//Only flag for deletion if the selected option is valid
					if($reference_option){
						$addons_to_delete[] = $option_id;

						if($reference_option['price_adjustment']){
							$update_pricing = true;
						}
					}
				}

				//Load partner profile
				$partner = array();
				if(isset($_POST['partner_id']) && !empty($_POST['partner_id'])){
					try{
						$profile = $Account->get_account_profile($_POST['partner_id']);
						$partner['first_name'] = (isset($profile['first_name']) ? $profile['first_name'] : NULL);
						$partner['last_name'] = (isset($profile['last_name']) ? $profile['last_name'] : NULL);
						$partner['email'] = (isset($profile['email']) ? $profile['email'] : NULL);
						$partner['phone'] = (isset($profile['phone']) ? $profile['phone'] : NULL);
						$partner['gender'] = (isset($profile['gender']) ? $profile['gender'] : NULL);
					}catch(Exception $e){
						$errors[] = 'Unable to load partner profile: '.$e->getMessage();
					}
				}

				//Validate ticket type
				if($_POST['pricing_id'] && !in_array($_POST['pricing_id'], array_column($ticket_types, 'pricing_id'))){
					$errors[] = 'Invalid ticket type selected.';
				}

				//Update status first
				if($row['reg_status'] != $_POST['reg_status']){
					try{
						$Registration->update_attendee_status(ITEM_ID, $_POST['reg_status']);
						$alertmsg .= $record_name.' was successfully ' .strtolower($_POST['reg_status']);

						//Check if registration was transfered to partner
						if($_POST['reg_status'] == 'Withdrawn'){
							if(empty($row['partner_id']) && !empty($row['partner']) && !empty($row['partner']['account_id'])){
								$alertmsg .= ' and registration has been transfered to partner';
							}
							$alertmsg .= '.';

							//Do you need to refund as well?
							if($row['paid'] == '1'){
								$alertmsg .= '<br /><br /><a href="' .$sitemap[71]['page_url']. '?action=process&item_id=' .$row['registration_id']. '&record=registration" class="button-sm"><i class="fa fa-undo"></i> Process Refund</a>';
							}
						}else{
							$alertmsg .= '.';
						}

						$row['reg_status'] = $_POST['reg_status'];

					}catch(Exception $e){
						$errors[] = 'Unable to save '.$record_name.'. '.$e->getMessage();
					}
				}

				if(!$errors){
					$db->new_transaction();

					//Update record
					$params = array(
						$_POST['email'],
						$_POST['notes'],
						$_POST['handicap'],
						$_POST['ticket_type'],
						$_POST['ticket_price'],
						ITEM_ID
					);
					$query = $db->query("UPDATE `$record_db` SET `email` = ?, `notes` = ?, `handicap` = ?, `ticket_type` = ?, `ticket_price` = ? WHERE `$record_id` = ?", $params);

					$row['email'] = $_POST['email'];
					$row['notes'] = $_POST['notes'];
					$row['handicap'] = $_POST['handicap'];

					//Insert addons
					foreach($addons_to_insert as $this_addon){
						$params = array(
							$this_addon['option_id'],
							$this_addon['attendee_id'],
							$this_addon['name'],
							$this_addon['value'],
							$this_addon['price_adjustment'],
							//update
							$this_addon['name'],
							$this_addon['value'],
							$this_addon['price_adjustment']
						);
						$db->query("INSERT INTO `reg_attendee_options` (`option_id`, `attendee_id`, `name`, `value`, `price_adjustment`) VALUES (?,?,?,?,?) ON DUPLICATE KEY UPDATE `name` = ?, `value` = ?, `price_adjustment` = ?", $params);
					}

					//Delete addons
					if($addons_to_delete){
						$params = $addons_to_delete;
						$placeholders = implode(',', array_fill_keys(array_keys($addons_to_delete), '?'));
						$db->query("DELETE FROM `reg_attendee_options` WHERE `attendee_id` = ? AND `option_id` IN (".$placeholders.")", $params);
					}

					//Register partner
					if($row['reg_status'] == 'Registered' && empty($row['partner_id']) && $occurrence['event_type'] == 2 && $occurrence['team_event'] && $occurrence['end_date'] >= date("Y-m-d")){
						if(!empty($_POST['partner_id']) || trim($_POST['partner_first_name']) != ""){

							//Insert attendee to registration
							$params = array(
								(!empty($_POST['partner_id']) ? $_POST['partner_id'] : NULL),
								$row['registration_id'],
								$occurrence['event_id'],
								$occurrence['occurrence_id'],
								ITEM_ID,
								'Registered',
								1,
								'Partner Fee',
								0,
								(isset($partner['first_name']) ? $partner['first_name'] : $_POST['partner_first_name']),
								(isset($partner['last_name']) ? $partner['last_name'] : $_POST['partner_last_name']),
								(isset($partner['email']) ? $partner['email'] : NULL),
								(isset($partner['phone']) ? $partner['phone'] : NULL),
								(isset($partner['gender']) ? $partner['gender'] : NULL),
								$_POST['partner_handicap'],
								date("Y-m-d H:i:s")
							);
							$query = $db->query("INSERT INTO `reg_attendees` (`account_id`, `registration_id`, `event_id`, `occurrence_id`, `partner_id`, `reg_status`, `attendee_sharing`, `ticket_type`, `ticket_price`, `first_name`, `last_name`, `email`, `phone`, `gender`, `handicap`, `date_added`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
							$partner['attendee_id'] = $db->insert_id();
							$row['partner'] = $partner;

							//Remove partner from waiting list (if applicable)
							if(!empty($_POST['partner_id'])){
								$params = array($occurrence['occurrence_id'], $_POST['partner_id']);
								$query = $db->query("DELETE FROM `reg_waiting_list` WHERE `occurrence_id` = ? && `account_id` = ?", $params);
							}
						}
					}

					//Commit
					if(!$db->error()){
						$db->commit();

						//Update registration pricing
						$registration = $taxes = $total = NULL;
						if($update_pricing){
							$registration = $Registration->get_registration($records_arr[ITEM_ID]['registration_id']);

							if($registration){
								$subtotal = 0;

								foreach($registration['events'] as $event){
									foreach($event['attendees'] as $attendee){
										$subtotal += $attendee['ticket_price'];

										foreach(($attendee['addons'] ?? []) as $att_addon){
											if($att_addon['value'] != ""){
												$subtotal += $att_addon['price_adjustment'];
											}
										}
									}
								}

								//Recalculate prices
								$subtotal -= $registration['discount'];
								$taxes = number_format($subtotal * (($registration['gst_rate'] + $registration['pst_rate']) / 100), 2, '.', '');
								$total = $subtotal + $registration['fees'] + $taxes;

								$params = array(
									$taxes,
									$total,
									$registration['registration_id']
								);
								$db->query("UPDATE `reg_registrations` SET `taxes` = ?, `registration_total` = ? WHERE `registration_id` = ?", $params);
							}
						}

						//Update change log
						foreach($changelog_fields as $key => $this_field){
							$description = '';

							//Determine what was changed
							//Updated addons
							if($key == 'update_addon'){
								if(!$addons_to_insert){
									continue;
								}

								$description = 'Updated '.$this_field['label'].':';
								foreach($addons_to_insert as $this_addon){
									$reference_addon = $occurrence_addons[$this_addon['reference_addon_id']] ?? [];
									$old_addon = $selected_addons_by_name[$reference_addon['name']];

									$description .= " \n- ";
									$description .= "`option_id`: ".$this_addon['option_id'];
									$description .= ", `name`: `".$old_addon['name']."`".($old_addon['name'] != $this_addon['name'] ? " changed to `".$this_addon['name']."`" : "");
									$description .= ", `value`: `".$old_addon['value']."`".($old_addon['value'] != $this_addon['value'] ? " changed to `".$this_addon['value']."`" : "");
									$description .= ", `price_adjustment`: `".$old_addon['price_adjustment']."`".($old_addon['price_adjustment'] != $this_addon['price_adjustment'] ? " changed to `".$this_addon['price_adjustment']."`" : "");
								}

							//Removed addons
							}else if($key == 'delete_addon'){
								if(!$addons_to_delete){
									continue;
								}

								$description = 'Removed '.$this_field['label'].':';
								foreach($addons_to_delete as $option_id){
									$reference_option = $selected_addons_by_id[$option_id] ?? [];

									$description .= " \n- ";
									$description .= "`option_id`: `".$option_id."`";
									$description .= ", `name`: `".$reference_option['name']."`";
									$description .= ", `value`: `".$reference_option['value']."`";
									$description .= ", `price_adjustment`: `".$reference_option['price_adjustment']."`";
								}

							//Registration pricing
							}else if($key == 'update_pricing'){
								if(!$update_pricing || !($registration ?? false)){
									continue;
								}

								$description = 'Updated '.$this_field['label'].':';
								$description .= " \n- ";
								$description .= "`registration_id`: `".$registration['registration_id']."`";
								$description .= ", `taxes`: `".$registration['taxes']."` changed to `".$taxes."`";
								$description .= ", `registration_total`: `".$registration['registration_total']."` changed to `".$total."`";

							//Other fields
							} else {
								$old_value = ($data[$this_field['field']] ?? '');
								$old_value_label = $old_value ?: '(empty)';
								$new_value = ($_POST[$this_field['field']] ?? '');
								$new_value_label = $new_value ?: '(empty)';

								$description = $old_value != $new_value ? $this_field['label'].': `'.$old_value_label.'` changed to `'.$new_value_label.'`.' : '';
							}

							//Log changes
							if($description){
								$params = array(
									ITEM_ID,
									$description,
									USER_LOGGED_IN,
									date("Y-m-d H:i:s")
								);
								$db->query("INSERT INTO `reg_attendee_changelog` (`attendee_id`, `description`, `account_id`, `date_added`) VALUES (?,?,?,?)", $params);
							}
						}

						//Send email notification
						$partner_email = false;
						if(isset($partner['email']) && !empty($partner['email'])){
							$message = '<h3>Registration Confirmation</h3>
							<p>This email is to inform you that <strong>' .$row['first_name'].' '.$row['last_name'].'</strong> has registered you as his/her partner for the following tournament:</p>
							<p><strong>' .$occurrence['name']. ' on ' .format_date_range($occurrence['start_date'], $occurrence['end_date']). '.</strong></p>
							<p>To view and manage your registrations, <a href="' .$siteurl.$root.get_page_url(22). '" class="button" target="_blank">login</a> to your account.</p>';
							$partner_email = send_email($partner['email'], 'Registration Confirmation', $message);
						}

						//Set alert
						if($alertmsg != ''){
							$CMSBuilder->set_system_alert($alertmsg, true);
						}else{
							$CMSBuilder->set_system_alert($record_name.' was successfully saved' .($partner_email ? ' and partner notification has been sent' : ''). '.', true);
						}

						$backlink = explode('&action=', $_SERVER['REQUEST_URI']);
						header("Location: " .$backlink[0]);
						exit();

					}else{
						$errors[] = 'Unable to update record: '.$db->error();
					}
				}

				//Errors
				if(!empty($errors)){
					$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
					foreach($_POST AS $key=>$data){
						$row[$key] = $data;
					}
				}

			}


		//View All Attendees
		}else{

			//Search Attendees - By Filters
			if(isset($_GET['advanced_search'])){
				$show_search_form = false;

				$search_results = array();
				$querytxt = "";
				$wheretxt = " WHERE `$record_db`.`reg_status` != ?";
				$params = array('Pending');
				$ccount = 0;

				//Columns
				if(isset($_GET['column']) && !empty($_GET['column'])){
					foreach($_GET['column'] as $db_name => $columns){
						foreach($columns as $column => $value){
							if($value != ''){
								if($column != 'facility'){
									$db_columns[$ccount] = $db_name.'.'.$column;
								}
								$table_columns[$ccount] = $value;
								$alias_columns[$ccount] = $column;
							}
							$ccount++;
						}
					}
				}

				//Search/filters
				if(isset($_GET['filter']) && !empty($_GET['filter'])){
					foreach($_GET['filter'] as $db_name => $filters){
						foreach($filters as $filter => $value){
							if(trim($value) != ''){

								$wheretxt .= ($wheretxt != "" ? " AND " : " WHERE ")."$db_name.$filter";

								if($filter == 'event_id' || $filter == 'occurrence_id' || $filter == 'reg_status' || $filter == 'gender'){
									$wheretxt .= " = ?";
									$params[] = $value;
								}else if($filter == 'start_date'){
									$wheretxt .= " >= ?";
									$params[] = $value;
								}else if($filter == 'end_date'){
									$wheretxt .= " <= ?";
									$params[] = $value;
								}else{
									$wheretxt .= " LIKE ?";
									$params[] = '%' .$value. '%';
								}

							}
						}
					}
				}

				$querytxt .= "SELECT `$record_db`.*, IFNULL(`$record_db`.`facility`, `facilities`.`facility_name`) AS `facility`, `reg_events`.`name` AS `event_name`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`, `reg_occurrences`.`occurrence_name`";
				$querytxt .= (!empty($db_columns) ? ",".implode(", ", $db_columns)." " : " ");
				$querytxt .= "FROM `$record_db` ";
				$querytxt .= "LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `$record_db`.`event_id` ";
				$querytxt .= "LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `$record_db`.`occurrence_id` ";
				$querytxt .= "LEFT JOIN `reg_registrations` ON `reg_registrations`.`registration_id` = `$record_db`.`registration_id` ";
				$querytxt .= "LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` ";
				$querytxt .= "LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ";
				$querytxt .= $wheretxt;
				$querytxt .= " GROUP BY `$record_db`.`$record_id` ORDER BY `reg_occurrences`.`occurrence_id`, `$record_db`.`registration_id`, `$record_db`.`partner_id`, `$record_db`.`last_name`, `$record_db`.`first_name`";
				$query = $db->query($querytxt, $params);

				if($query && !$db->error() && $db->num_rows() > 0) {
					$result = $db->fetch_array();
					foreach($result as $row) {
						$search_results[$row[$record_id]] = $row;
					}
				}

			}

			//Mass Mail
			if(isset($_POST['massmail'])) {

				$email_message = format_email($_POST['message']);

				//Format recipients
				$recipients = array();
				if(isset($_POST['attendee_emails']) && !empty($_POST['attendee_emails'])) {
					foreach($_POST['attendee_emails'] as $attendee_id) {
						$attendee = $search_results[$attendee_id];
						if(trim($attendee['email']) != ""){
							$recipients[$attendee['email']] = array('email'=>$attendee['email'], 'name'=>$attendee['first_name'].' '.$attendee['last_name']);
						}
					}
				}

				if(empty($recipients)) {
					$errors[] = 'There are no selected attendees. Please select at least one.';
				}else{

					//Send to admin also
					$recipients[$global['contact_email']] = array('email'=>$global['contact_email'], 'name'=>$global['company_name']);

					$sendgrid->set_template(false);

					//Send email
					try{
						$sendgrid->sendit($recipients, "Important Notice", $email_message, "Attendee Mass Mail");
					}catch(Exception $e){
						$errors[] = 'There was an error sending this mass mail: '.$e->getMessage();
					}
				}

				if(!$errors) {
					$CMSBuilder->set_system_alert('Mass mail successfully sent to '.(count($recipients)-1).' attendees.', true);
					header('Location:'.PAGE_URL."?".$_SERVER['QUERY_STRING']);
					exit();

				}else{
					$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
					foreach($_POST AS $key=>$data){
						$row[$key] = $data;
					}
				}
			}
		}
	}

}

?>