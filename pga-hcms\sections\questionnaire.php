<?php

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include('includes/widgets/searchform.php');
		echo '</div>
		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">'.$records_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">

				<thead>
				<th width="1px" data-sorter="false"></th>
				<th>Question</th>
				<th width="1px" class="center">Visible</th>
				<th width="1px" data-sorter="false"></th>
				</thead>

				<tbody>';

				foreach($records_arr as $row){
				echo '<tr data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-id="'.$row[$record_id].'">
				<td class="handle"><i class="fas fa-arrows-alt"></i></td>
				<td>'.$row['question'].'</td>
				<td class="center">'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
				<td><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
				</tr>';
				}

				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';


//Display form
}else{
	$data = $records_arr[ITEM_ID] ?? [];
	$row  = !isset($_POST['save']) ? $data : $_POST;

	// Important notice for edit mode
	if(ACTION == 'edit' && ITEM_ID){
		echo $CMSBuilder->important("<strong>Question Deletion:</strong> If you delete a question, all user answers to the question will also be deleted. <strong>This action is not undoable.</strong>");
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';

		// Question details
		echo '<div class="panel">
			<div class="panel-header">Question Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
				<label>Show Question</label>
				<div class="onoffswitch">
				<input type="checkbox" name="showhide" id="showhide" value="0"'.(empty($row['showhide']) ? ' checked' : '').' />
				<label for="showhide">
				<span class="inner"></span>
				<span class="switch"></span>
				</label>
				</div>
				</div>
			</div>

			<div class="panel-content flex-container">
				<div class="form-field">
				<label>Question <span class="required">*</span></label>
				<input type="text" name="question" value="'.($row['question'] ?? '').'" class="input'.(in_array('question', $required) ? ' required' : '').'" />
				</div>

				<div class="form-field">
				<label>Numerical Order'.$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.').'</label>
				<select name="ordering" class="select">
				<option value="101">Default</option>';

				for($i = 1; $i < 101; $i++){
				echo '<option value="'.$i.'"'.(($row['ordering'] ?? false) == $i ? ' selected' : '').'>'.$i.'</option>';
				}

				echo '</select>
				</div>
			</div>
		</div>';

		//Sticky footer
		include('includes/widgets/formbuttons.php');

		echo '<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'].'" />
	</form>';

}

?>