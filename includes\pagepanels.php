<?php

// No panels - use page content
if (empty($page['page_panels'])) {

	//Add lazy load attrs to all images
	$page['content'] = lazy_load_html($page['content'] ?: '');

	//Load navigation for sitemap page content
	if(PAGE_ID == $_sitepages['sitemap']['page_id']){
		$page['content'] .= '<ul id="sitemap">'.NAVIGATION.'</ul>';
	}

	//Create standard panel out of page content
	if($page['content'] || (PAGE_ID == $_sitepages['home']['page_id'] && SLIDESHOW)){
		$page['page_panels'][] = ['content' => $page['content']];
	}
}


$panelcount = 0;
if(!empty($page['page_panels'])){
	foreach($page['page_panels'] as $panel_id => $panel){

		// Metadata
		$panel['panel_id']   = $panel['panel_id'] ?? $panel_id;
		$panel['page_id']    = $panel['page_id'] ?? PAGE_ID;
		$panel['cta_id']     = $panel['cta_id'] ?? '';
		$panel['gallery_id'] = $panel['gallery_id'] ?? '';
		$panel['panel_type'] = $panel['panel_type'] ?? 'standard';
		$panel['promo_type'] = $panel['promo_type'] ?? 'standard';

		// Content
		$panel['show_title']    = $panel['show_title'] ?? 1;
		$panel['title']         = $panel['show_title'] ? ($panel['title'] ?? '') : '';
		$panel['subtitle']      = $panel['subtitle'] ?? '';
		$panel['content']       = $panel['content'] ?? '';
		$panel['google_map']    = $panel['google_map'] ?? 0;
		$panel['prepend_content'] = $panel['prepend_content'] ?? '';
		$panel['append_content']  = $panel['append_content'] ?? '';

		// Links & buttons
		$panel['url']        = $panel['url'] ?? '';
		$panel['url_text']   = $panel['url_text'] ?? '';
		$panel['url_target'] = $panel['url_target'] ?? 0;

		// Media & overlays
		$panel['theme']                  = $panel['theme'] ?? '';
		$panel['image_alt']              = $panel['image_alt'] ?? '' ?: str_replace(['{', '}'], ['', ''], $panel['title']);
		$panel['image']                  = $panel['image'] ?? '';
		$panel['image_mobile']           = $panel['image_mobile'] ?? '';
		$panel['image_desktop_position'] = $panel['image_desktop_position'] ?? '50%';
		$panel['image_mobile_position']  = $panel['image_mobile_position'] ?? '50%';
		$panel['side_image_position']    = $panel['side_image_position'] ?? 0;
		$panel['video_url']              = $panel['video_url'] ?? '';

		// Add lazy load attrs to all images
		$panel['content'] = lazy_load_html($panel['content'] ?: '');
		foreach (($panel['panel_tabs'] ?? []) as $i => $tab) {
			$panel['panel_tabs'][$i]['content'] = lazy_load_html($tab['content'] ?: '');
		}

		// Apply classes to panel
		$class = 'panel animate ';
		$class .= ($panel['panel_type'] == 'promo' && $panel['promo_type'] != 'standard' ? $panel['promo_type'].'-' : '').$panel['panel_type']; // prepend promo type to promos
		$class .= $panel['theme'] ? ' theme-'.$panel['theme'] : '';
		$class .= GOOGLE_MAP && !empty($global['locations'][0]['google_map']) && $panel['google_map'] ? ' has-map' : ''; // First location has a map
		$class .= !empty($panel['class']) ? ' '.trim($panel['class']) : '';
		$panel['class'] = $class;

		$panel['include_h1'] = !$panelcount && SLIDESHOW;

		//Panel logic
		switch($panel['panel_type']){

			//CTA & Parallax
			case 'parallax':
			case 'cta':

				//CTA data and images are different locations from parallax
				$panel_data = $panel['cta'] ?? $panel;
				$panel_dir  = 'images/'.(empty($panel['cta']) ? 'panels' : 'cta').'/';

				//Validate image files exist and determine position based on those that do
				$desktop_img = check_file($panel_data['image'], $panel_dir.'1920/');
				$desktop_pos = $panel_data['image_desktop_position'];
				$mobile_img  = check_file($panel_data['image_mobile'], $panel_dir.'768/');
				$mobile_pos  = $mobile_img ? $panel_data['image_mobile_position'] : $desktop_pos;
				$mobile_img  = $mobile_img ?: check_file($desktop_img, $panel_dir.'768/');

				//No image
				$panel['class'] .= !$desktop_img ? ' noimage' : '';

				include(include_path("includes/templates/panels/".$panel['panel_type'].".php"));
			break;

			//Side by side
			case 'side':

				//Check for different types of media
				$media = false;
				$imagedir = 'images/panels/';
				$panel['image'] = check_file($panel['image'], $imagedir.'480/');
				if($panel['image']){
					[$imgw, $imgh] = getimagesize($imagedir.'480/'.$panel['image']);

					$media = '<picture>'.
						img_sources($imagedir, $panel['image']).
						'<img class="lazy-load"
							  src="'.empty_src($imgw, $imgh).'"
							  data-src="'.$path.$imagedir.'480/'.$panel['image'].'"
							  alt="'.$panel['image_alt'].'"
							  title="'.$panel['image_alt'].'" />
					</picture>';
				}

				//Video popup
				if($panel['video_url'] && $panel['image']){

					$media = '<div class="panel-video">
						<a href="'.$panel['video_url'].'" class="light-iframe block" target="_blank" data-poster="'.$path.$imagedir.'480/'.$panel['image'].'">'.
							$media.
							'<div class="overlay overlay-black"></div>
						</a>
					</div>';

				//Image
				}else if($panel['image']){
					$media = '<div class="panel-image">'.$media.'</div>';
				}

				//Google Map
				if($panel['google_map']){
					ob_start();
					include(include_path("includes/templates/contact-map.php"));
					$media .= ob_get_clean();
				}

				//Add class depending on media
				$panel['class'] .= ' '.(!$media ? 'noimage' : ($panel['side_image_position'] ? 'rtl' : 'ltr').' '.($panel['side_image_position_mobile'] ? 'btt' : 'ttb'));

				include(include_path("includes/templates/panels/side.php"));
			break;

			case 'promo':
				$panel_include = "includes/templates/panels/".($panel['promo_type'] == 'standard' ? '' : $panel['promo_type'])."promo.php";
				include (file_exists($panel_include) ? $panel_include : 'includes/templates/panels/promo.php');
				break;

			case 'blog':
				// Grab category if exists
				if (empty($panel['blog_entries'])) {
					if ($panel['blog_category_id']) {
						$panel['blog_entries'] = $Blog->get_entries('category', $panel['blog_category_id'], 10);

						// Grab most recent entries
						} else {
							$panel['blog_entries'] = $Blog->get_entries('recent', '', 10);
						}
					}

					if ($panel['blog_entries']) {
						include(include_path("includes/templates/panels/blog.php"));
					}
				break;

			//Default
			default:
				$panel_include = include_path("includes/templates/panels/".$panel['panel_type'].".php");
				include(file_exists($panel_include) ? $panel_include : include_path("includes/templates/panels/standard.php"));
			break;
		}

		$panelcount++;
	}
}

?>