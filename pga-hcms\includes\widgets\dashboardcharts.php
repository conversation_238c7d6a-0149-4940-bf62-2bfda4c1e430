<?php

// Fetch all dataset labels
$labels   = $db->get_enum_vals('inquiries', 'inquiry'); // + ['New Dataset'];
$datasets = array_fill_keys($labels, []);
$dates    = [];

// Get last 30 days, fill each dataset with dates
$period = new DatePeriod(new DateTime('-29 days'), new DateInterval('P1D'), new DateTime('+1 day'));
foreach ($period as $i => $date) $dates[] = $date->format('m/d');
foreach ($datasets as $label => $stats) $datasets[$label] = array_fill_keys($dates, 0);

// Fill inquiry datasets
$params = [date("Y-m-d", strtotime("-29 days")), date("Y-m-d")];
$db->query("SELECT * FROM inquiries WHERE DATE(timestamp) BETWEEN ? AND ?", $params);
$result = $db->fetch_array();
foreach ($result as $stat) {
	$date = date('m/d', strtotime($stat['timestamp']));
	$datasets[$stat['inquiry']][$date]++;
}

?>
<div class="panel">
	<div class="panel-header">Conversions in Last 30 Days
		<a class="panel-toggle fas fa-chevron-up"></a>
	</div>

	<!-- Chart 1 - Overall Views and Visits -->
	<div class="panel-content chart">
		<table class="chart-stats" cellspacing="0" cellpadding="15" border="0">
			<tbody>
				<tr>

				<?php
				// Display label plurals
				foreach ($datasets as $label => $dataset) {
					$label = substr($label, -1) == 's' ? $label : (substr($label, -1) == 'y' ? substr($label, 0, -1).'ies' : $label.'s');
					echo '<th class="center"><div>Total<br/> '.$label.' <b>'.array_sum($dataset).'</b></div></th>';
				}
				?>

				</tr>
			</tbody>
		</table>

		<div class="chart-container">
			<canvas id="dashboard-chart" height="200" data-source='<?php echo json_encode($datasets); ?>'></canvas>
		</div>
	</div>
</div>
<script src="<?php echo $path; ?>includes/plugins/chartjs/dist/chart.min.js"></script>

<?php

// SEO average
if ($cms_settings['enhanced_seo'] && (MASTER_USER || SEO_USER)) {

	// Get site average for all items with seo score
	$tables_to_avg = array('pages', 'staff', 'careers');
	$site_avg = $Analyzer->get_site_average($tables_to_avg);

	if ($site_avg > 80) {
		$avg_class = "seo-pass";
	} else if ($site_avg >= 50 && $site_avg <= 80) {
		$avg_class = "seo-average";
	} else {
		$avg_class = "seo-fail";
	}
?>

<div class="dashboard-box progress-bar">
	<div class="percent-progress <?php echo $avg_class; ?>" style="--percent: <?php echo $site_avg; ?>%;"></div>
	<div class="percentage">
		<div class="numbers">
			<span><?php echo $site_avg; ?></span>
		</div>
		<p class="uppercase">
			Search Engine Optimization
			<small>Content Analysis (Site Average)</small>
		</p>
	</div>
</div>

<?php } ?>