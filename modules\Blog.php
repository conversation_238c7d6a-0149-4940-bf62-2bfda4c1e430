<?php

//Blog Page
if (PAGE_ID == $_sitepages['blog']['page_id'] || (PARENT_ID == $_sitepages['blog']['page_id'] && PAGE_ID == '')) {

	// Define vars
	$panel_id  = 44; // REPLACE_ME;
	$imagedir  = 'images/blog/entries/';
	$bannerdir = 'images/heroes/';
	$pagebits  = $SiteBuilder->get_pagebits($_sitepages['blog']['slug'] ?: $_sitepages['blog']['page']);

	// Pagination
	$pg      = $_GET['pg'] ?? 1;
	$pg      = !is_numeric($pg) || $pg < 1 ? 1 : $pg;
	$limit   = 6; // Entries per page
	$offset  = $limit*($pg-1);
	$pgstr   = $offset.', '.$limit;

	// Blog data
	$blog_categories = $Blog->get_categories();
	$blog_authors    = $Blog->get_authors();
	$blog_archives   = $Blog->get_archives();

	// Get a single page worth of entries
	$recent_entries  = $Blog->get_entries('recent', '', $limit);
	$popular_entries = $Blog->get_popular_entries('recent', '', 'DESC', $limit);


	// Check for existing blog filters (categories, authors, archives, etc)
	if (!empty($pagebits[2])) {
		$error404 = false;

		// Set default page data
		$page = $SiteBuilder->get_page_content($_sitepages['blog']['page_id']);
		$page['meta_canonical']   = $_sitepages['blog']['page_url'].$pagebits[2]."/";
		$page['page_url']         = $page['meta_canonical'];
		$page['redirect_to_slug'] = false;

		// Dissect page slug to fetch ID and path
		$slugbits  = explode('-', $pagebits[2]);
		$slug_id   = $slugbits[count($slugbits)-1];
		$slug_page = implode('-', array_slice($slugbits,0,-1));

		$author     = $blog_authors[$slug_id] ?? false;
		$category   = $blog_categories[$slug_id] ?? false;
		$archive    = $blog_archives[$pagebits[2]] ?? false;


		// Check for searchterm
		if ($blog_settings['show_author'] && $author && $author['page'] == $slug_page) {
			$filter_type   = 'author';
			$blog_entries  = $Blog->get_entries($filter_type, $slug_id, $pgstr);
			$total_entries = $author['total_entries'];

			// Set page data
			$page['name']       = $author['name'];
			$page['meta_title'] = $page['name'].' | '.$_sitepages['blog']['seo_title'];


		// Check if ID matches category
		} else if ($category) {
			$filter_type   = 'category';
			$blog_entries  = $Blog->get_entries($filter_type, $slug_id, $pgstr);
			$total_entries = $category['total_entries'];

			// Set page data
			$page['name']             = $category['name'];
			$page['meta_title']       = $category['meta_title'] ?: $page['name']." | ".$page['seo_title'];
			$page['meta_description'] = $category['meta_description'] ?: $page['meta_description'];

			// Set banner image
			if (check_file($category['image'], $bannerdir.'1920/')) {
				$page['banner_image']     = $category['image'];
				$page['banner_image_alt'] = $category['image_alt'] ?: $category['name'];
			}

			// Redirect if page is incorrect, accounting for blog entries in URL
			if ($category['page'] != $slug_page) {
				header("HTTP/1.1 301 Moved Permanently");
				header('Location: ' .$_sitepages['blog']['page_url'].$category['page'].'-'.$slug_id.'/'.$pagebits[3].'/');
				exit();
			}


		// Check if entire path matches valid archive data
		} else if ($archive) {
			$filter_type   = 'archive';
			$datestr       = date("Y-m-d", strtotime(substr($pagebits[2], 2).substr($pagebits[2], 0, -4).'01'));
			$blog_entries  = $Blog->get_entries($filter_type, $datestr, $pgstr);
			$total_entries = $archive['total_entries'];

			// Set page data
			$page['name']       = $archive['name'];
			$page['meta_title'] = $page['name'].' | '.$page['seo_title'];


		// Display recent entries
		} else if ($pagebits[2] == 'recent') {
			$blog_entries  = $recent_entries;
			$total_entries = count($recent_entries);

			// Set page data
			$page['name']       = 'Most Recent';
			$page['meta_title'] = $page['name']. ' | ' .$page['seo_title'];


		// Display popular entries
		} else if ($pagebits[2] == 'popular') {
			$blog_entries  = $popular_entries;
			$total_entries = count($popular_entries);
			$Blog->entry_ids = array_keys($popular_entries); //Set popular entry IDs for the prev/next links

			// Set page data
			$page['name']       = 'Most Popular';
			$page['meta_title'] = $page['name']. ' | ' .$page['seo_title'];


		// No blog data found
		} else {
			$error404 = true;
		}


		// Blog data found
		if (!$error404) {

			// Determine last page from total entries
			$last_pg = max(1, ceil($total_entries / $limit));

			// Redirect to last page, account for a single page
			if ($pg > $last_pg) {
				header('Location: '.$page['page_url'].'/'.($last_pg > 1 ? "?pg=".$last_pg : ''));
				exit();
			}

			// Update breadcrumbs
			array_pop($breadcrumbs);
			$breadcrumbs[] = array('url' => $page['page_url'], 'name' => $page['name']);
		}

	// Redirect to most recent from root blog path
	} else {
		header('HTTP/1.1 301 Moved Permanently');
		header('Location: '.$_sitepages['blog']['page_url']. 'recent/');
		exit();
	}


	// Display specific entry
	if (!$error404 && !empty($pagebits[3])) {
		$slugbits   = explode('-', $pagebits[3]);
		$entry_id   = $slugbits[count($slugbits)-1];
		$entry_page = implode('-', array_slice($slugbits,0,-1));
		$entry      = $Blog->get_entries('entry', $entry_id)[$entry_id] ?? false;

		// Check if path matches blog entry
		if ($entry) {

			// Track pageviews
			if (isset($_COOKIE['xid'])) {
				$Blog->log_entry_view($entry_id);
			}

			// Grab next/prev entries
			$next_prev = $Blog->get_prevnext($entry_id);

			// Grab blog information
			$author   = $blog_authors[$entry['author_id']];
			$category = $blog_categories[$entry['category_id']];

			// Set page data
			$page['redirect_to_slug'] = false;
			$page['page_url']         = $page['page_url'].$entry['page'].'-'.$entry_id.'/';
			$page['page_title']       = $entry['title'];
			$page['meta_title']       = $entry['meta_title'] ?: $entry['title']. ' | ' .$page['seo_title'];
			$page['meta_description'] = $entry['meta_description'] ?: $entry['description'] ?: $page['meta_description'];
			$page['meta_canonical']   = $_sitepages['blog']['page_url'].$entry['category_page'].'-'.$entry['category_id'].'/'.$entry['page'].'-'.$entry_id.'/';

			// Set banner image
			if (check_file($entry['image'], $bannerdir.'1920/')) {
				[$width, $height] = getimagesize($imagedir.$entry['image']);

				$page['banner_image']     = $entry['image'];
				$page['banner_image_alt'] = $entry['image_alt'] ?: $entry['title'];
				$page['og_image']         = $imagedir.$entry['image'];
				$page['og_image_type']    = mime_content_type($page['og_image']);
				$page['og_image_width']   = $width;
				$page['og_image_height']  = $height;
			}

			// Update breadcrumbs
			$breadcrumbs[] = array('url' => $page['page_url'], 'name' => $entry['title']);


			// Redirect to proper category
			if (isset($filter_type) && $filter_type == 'category' && $entry['category_page'].'-'.$entry['category_id'] != $pagebits[2]) {
				header("HTTP/1.1 301 Moved Permanently");
				header('Location: '.$page['meta_canonical']);

			// Redirect to proper entry
			} else if ($entry['page'] != $entry_page) {
				header("HTTP/1.1 301 Moved Permanently");
				header('Location: '.$page['page_url']);
				exit();
			}

		// Entry does not exist
		} else {
			$error404 = true;
		}
	}

	// 404 if entry, category, author, etc does not exist, and if child page of blog entries
	if ($error404 || !empty($pagebits[4])) {
		$page = $SiteBuilder->get_page_content('error');
	}
}

//Find and update root blog crumb - don't link to a redirect page
if(PAGE_ID == $_sitepages['blog']['page_id'] || PARENT_ID == $_sitepages['blog']['page_id'] && PAGE_ID == ''){
	foreach($breadcrumbs as $idx => $crumb){
		if($crumb['url'] == $_sitepages['blog']['page_url']){
			$breadcrumbs[$idx]['url'] .= 'recent/';
		}
	}
}
?>