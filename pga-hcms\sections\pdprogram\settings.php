<?php
	
//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

echo "<form action='' method='post' enctype='multipart/form-data'>";

	//PD settings
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Program Settings
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			echo "<div class='form-field'>
				<label>Program Year</label>
				<select name='current_year' class='select".(in_array('current_year', $required) ? ' required' : '')."'>";
				for($y=(date('Y')+1); $y>=2018; $y--){
					echo "<option value='".$y."'" .(isset($pd_settings['current_year']) && $pd_settings['current_year'] == $y ? ' selected' : ''). ">".$y."</option>";
				}
				echo "</select>
			</div>";
		echo "</div>";
	echo "</div>";

	//PD badge
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Program Badge
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			echo "<p><small>Upload a badge to be displayed on all Top 100 member profile pages for the selected year.</small></p>";
			$image = $pd_settings['badge'];
			if(isset($image) && $image != '' && file_exists($imagedir.$image)){
				echo "<div class='img-holder'>
					<a href='" .$path.$imagedir.$image. "' class='light-gallery' target='_blank' title=''>
						<img src='" .$path.$imagedir.$image. "' alt='' />
					</a>
					<input type='checkbox' class='checkbox' name='deleteimage' id='deleteimage' value='1'>
					<label for='deleteimage'>Delete Current Image</label>
				</div>";
			}
		[$max_W, $max_H] = CMSUploader::max_size('top_settings', 'image');
					echo '<div class="form-field">
						<label>Upload Image '.(in_array('image', $required) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Upload Image', 'Image must be smaller than '.$_max_filesize['megabytes'].' and larger than '.$max_W.' x '.$max_H.'.').'</label>
						<input type="file" class="input'.(in_array('image', $required) ? ' required' : '').'" name="image" value="" />
					</div>';
			echo "<div class='form-field'>
				<label>Badge Year</label>
				<select name='badge_year' class='select".(in_array('badge_year', $required) ? ' required' : '')."'>
				<option value=''>- Select -</option>";
				for($y=(date('Y')+1); $y>=2018; $y--){
					echo "<option value='".$y."'" .(isset($pd_settings['badge_year']) && $pd_settings['badge_year'] == $y ? ' selected' : ''). ">".$y."</option>";
				}
				echo "</select>
			</div>";
		echo "</div>";
	echo "</div>";
	
	//Sticky footer
	echo "<footer id='cms-footer' class='resize'>";
	echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
	echo "</footer>";

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid']. "'/>";	
echo "</form>";
if($CMSUploader->crop_queue()){
	include('includes/jcropimages.php');	
}
?>