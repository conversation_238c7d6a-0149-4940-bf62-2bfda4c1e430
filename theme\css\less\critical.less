@charset "utf-8";
/*
	critical.less

	Project: Theme 2

*/


/*------ imports ------*/

@import "global.less";
@import "animations.less";
@import "../../../core/less/landing-page.less";
@path: '/';


/*------ utilities ------*/

// Content container
.container{
	--container-max-width: var(--container-width);
	width: 100%;
	max-width: calc(var(--container-max-width) + var(--container-padding) * 2);
	margin: 0 auto;
	padding: 0 var(--container-padding);

	// Sizes
	&.container-lg{--container-max-width: var(--container-width-lg); }
	&.container-xl{--container-max-width: var(--container-width-xl); }

	& &{padding: 0; }
}

// Link inherits colour
a.sneaky-link:not(:hover){
	color: inherit;
}

// Lazy loaded images
img.lazy-load:not(.loaded){
	background-color: @color-gray-light;
}

// Responsive backgrounds
.responsive-bg{
	--src: var(--bg-src, none);
	--src-tablet-p: var(--bg-src-tablet-p, var(--src));
	--src-tablet-l: var(--bg-src-tablet-l, var(--src-tablet-p));
	--src-notebook: var(--bg-src-notebook, var(--src-tablet-l));
	--src-desktop: var(--bg-src-desktop, var(--src-notebook));
	--pos-desktop: var(--bg-pos, center);
	--pos-mobile: var(--bg-pos-mobile, var(--pos-desktop));
	.position();
	background-size: cover;
	background-repeat: no-repeat;
	background-position: var(--pos-mobile);
	z-index: -1;

	&.visible{
		background-image: var(--src);
		@media @tablet-p{background-image: var(--src-tablet-p); }
		@media @tablet-l{background-image: var(--src-tablet-l); }
		@media @notebook{background-image: var(--src-notebook); }
		@media @desktop{background-image: var(--src-desktop); }
	}

	.touch &{
		background-attachment: scroll !important;
	}

	@media @tablet-l{background-position: var(--pos-desktop); }
}

// Background videos centered and covered
video.video-bg{
	.position(0, 0, 0, 0, 100%);
	display: none;
	object-fit: cover;
	z-index: -1;
}

@media @notebook{
	.no-touch video.video-bg{
		display: block;
	}
}

// Theme options
.theme-theme1{--theme-bg: @color-overlay1; }
.theme-theme2{--theme-bg: @color-overlay2; }
.theme-gradient{--theme-bg: .gradient(@color-overlay-grad-in, @color-overlay-grad-out; var(--theme-gradient-deg, 90deg))[background-image];  }
.theme-black{--theme-bg: @color-overlay-dark; }

// Absolute overlay
.overlay{
	background: var(--theme-bg, none);
	opacity: 0.5;
	z-index: -1;
	.position();
	.trans(background-color);
	.trans(opacity);

	&.overlay-theme1{&:extend(.theme-theme1); }
	&.overlay-theme2{&:extend(.theme-theme2); }
	&.overlay-gradient{&:extend(.theme-gradient); }
	&.overlay-black{&:extend(.theme-black); }
	&.solid{opacity: 1;	}
}

.fancy-text{
	// font-family: @font-base;
	font-family: inherit;
	// font-style: italic;
	color: @color-theme2;
	// .regular();
	font-weight: inherit;
}

.title-decor{
	.fluid-property(--line-width, 12px, 20px, 1024px, 1920px);
	.fluid-property(--line-gap, 8px, 30px, 1024px, 1920px);
	--line-top: -30px;
	--line-bottom: 0;
	--line-left: calc((var(--line-width) + var(--line-gap)) * -1);
	position: relative;

	//stopped
	// &::before{
	// 	content: '';
	// 	position: absolute;
	// 	top: var(--line-top);
	// 	bottom: var(--line-bottom);
	// 	left: var(--line-left);
	// 	width: var(--line-width);
	// 	background-color: @color-theme3;
	// 	opacity: 0.5;
	// }
}


/*------ navigation ------*/

// Nav menus
.nav-menu{
	ul{
		.unstyled-list();
	}

	li, a{
		position: relative;
		display: block;
	}

	> ul{
		.flexbox(@flow: row nowrap; @cross: center;);

		> li{
			.flex(0 0 auto);
		}
	}

	ul ul{
		display: none;
		position: absolute;
		white-space: normal;
		// background-color: @color-gray-lighter;
		background-color: @color-theme1;
		.box-shadow(@blur: 25px;);

		a{
			width: 250px;
		}
	}

	.menu-header{
		display: none;
	}

	.more-icon::before{
		.font-awesome(f141);
	}
}

// Logo
:root{--logo-width: 67.48px;}
:root{--logo-width: 85px;}

#page-logo{
	&, img, svg{
		display: inline-block;
		width: var(--logo-width);
		height: auto;
		vertical-align: top;
	}

	svg{
		.trans(fill);
	}
}

@media @notebook{
	// :root{--logo-width: 93.19px;}
	:root{--logo-width: 130px;}

	#page-logo{
		.fluid-property(margin-left, 0px, 30px, 1024px, 1920px);
	}
}

// Main nav container
:root{--navbar-height: 85px;} // Height of #page-navbar (including paddings). Pushes the banner/slideshow contents down.

#page-navbar{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	padding-top: var(--container-padding);
	z-index: 100;
	.fluid-property(margin-top, 50px, 150px, 480px, 1024px);

	.container{
		&:extend(.container.container-xl);
	}

	.navbar-wrapper{

		.fluid-property(padding-block, 10px, 15px, 480px, 1024px);
		.fluid-property(padding-inline, 10px, 20px, 480px, 1024px);
		.flexbox(@flow: row nowrap; @main: space-between; @cross: center;);
		gap: 20px;
		border-radius: 3px;
		background-color: transparent;
		// .box-shadow(@blur: 25px; @color: fade(#000, 40%););
	}

	.navbar-menu{
		.flex(1 1 auto);
	}

	&.sticky{
		margin-top: 0;
	}

	&.theme-transparent, &.sticky{
		.navbar-wrapper{
			padding-inline: 0;
			box-shadow: none;
		}

		#page-logo svg{
			fill: @color-light;
		}
	}

	&.theme-transparent{
		.navbar-wrapper{
			background-color: transparent;
		}

		&:not(.sticky){
			--logo-width: 96.42px;

			.navbar-wrapper{
				padding: 0;
			}
		}
	}

	///
	// Default state with hero
    body:has(#page-hero) & {
        margin-top: 0;
    }

    // When hero has noimage class
    body:has(#page-hero.noimage) & {
        margin-top: 150px;

		@media @max-tablet-l{
			margin-top: 100px;
		}

		@media @max-tablet-p{
			margin-top: 75px;
		}

        // Reset margin when navbar is sticky
        &.sticky {
            margin-top: 0;
        }
    }

    // Slideshow case
    body:has(#slideshow) & {
        margin-top: 0;
    }
	///
}

// body:has(#slideshow) #page-navbar{
// 	margin-top: 0;
// }

// body:has(#page-hero.noimage) #page-navbar{
// 	margin-top: 150px;
// }

// body:has(#page-hero.noimage) #page-navbar.sticky{
// 	margin-top: 0px;
// }

// body:has(#page-hero) #page-navbar {
//     margin-top: 0;
// }

@media @notebook{
	:root{--navbar-height: 145px;}

	#page-navbar{
		background-color: transparent;
		.trans(background-color);

		&.theme-transparent, &.sticky{
			#page-logo{
				margin-left: 0;
			}
		}

		&.theme-transparent{
			&:not(.sticky){
				--logo-width: 153.2px;

				.container{
					position: relative;
				}

				#page-logo{
					img, svg{
						position: absolute;
						top: 50%;
						left: var(--container-padding);
						.translateY(-50%);
					}
				}
			}
		}

		&.sticky{
			position: fixed;
			height: auto;
			padding: 0;
			background-color: @color-theme1;
			border-bottom: 1px solid @color-light;
			.box-shadow(@blur: 25px;);
			.trans(transform);
			.trans(background-color);
			.trans(box-shadow);

			.navbar-wrapper{
				margin-top: 0;
				background-color: transparent;
			}

			// Hide contact info
			#page-contact-top{
				.translateY(-50px);
				opacity: 0;
				height: 0;
				margin: 0;
			}
		}

		&.hide{.translateY(-100%); } // Smart nav hides iteself on scroll down
	}
}

// Main menu
#main-navigation{
	.flexbox(@flow: row nowrap; @main: flex-end;);
	z-index: 200;
	.show-notebook();

	&:extend(.button.light);

	ul{
		gap: 5px;
	}

	a, .more-icon, li.highlight > a{
		padding: 12px 20px;
	}

	a, .more-icon{
		font-family: @font-base;
		font-size: 18px;
		line-height: var(--line-height-thin);
		.bold();
		text-transform: uppercase;
	}

	a{
		color: @color-light;
	}

	.more-icon{
		color: @color-theme2;
		display: inline-block;
		vertical-align: top;
		cursor: pointer;
	}

	// Hovered and active states
	a:hover, a:focus, .more-icon:hover, .more-icon:focus, li.active:not(.highlight) > a{
		// color: @color-theme1;
		color: @color-theme2;
	}

	// Highlighted links
	li.highlight > a{
		background-color: @color-theme4;
		color: @color-light;

		&:hover, &:focus{
			background-color: @color-theme1;
		}
	}

	li.highlight.active > a{
		background-color: @color-theme1;
	}

	> ul > li.highlight > a{
		border-radius: 3px;
	}

	:where(.sticky, .theme-transparent) &{
		li.highlight{
			&.active > a, > a:hover, > a:focus{
				background-color: @color-light;
				color: @color-theme4;
			}
		}
	}

	:where(.sticky) &{
		> ul > li:not(.highlight){
			> a, .more-icon{
				color: @color-theme3;
			}

			> a:hover, > a:focus, &.active > a{
				color: @color-light;
			}
		}
	}

	:where(.theme-transparent) &{
		> ul > li:not(.highlight){
			> a, .more-icon{
				color: @color-gray-lighter;
			}

			> a:hover, > a:focus, .more-icon:hover, .more-icon:focus, &.active > a{
				color: @color-light;
			}
		}
	}
}

// Mobile Nav button
#menu-toggle{
	--bar-height: 2px;
	--bar-gap: 7px;
	.inline-flexbox(@flow: column nowrap; @cross: flex-end;);
	gap: 7px;
	width: 25px;
	cursor: pointer;
	outline: none;
	.hide-notebook();

	span{
		display: block;
		width: 100%;
		height: var(--bar-height);
		border-radius: 5px;
		background-color: @color-light;
		.trans(width);
		.trans(background-color);
		.trans(transform);

		&:nth-child(1){
			transform-origin: 0% 100%;
		}

		// &:nth-child(3){
		// 	width: 60%;
		// 	transform-origin: 0% 0%;
		// }
	}

	&.close{
		// span{
		// 	&:nth-child(1){
		// 		.rotate(45deg);
		// 	}

		// 	&:nth-child(2){
		// 		background-color: transparent !important;
		// 	}

		// 	&:nth-child(3){
		// 		width: 100%;
		// 		.rotate(-45deg);
		// 	}
		// }
		display: none;
	}

	&:hover, &:focus{
		span{
			background-color: @color-theme1;
		}
	}

	&.open:hover, &.open:focus{
		span:nth-child(3){
			width: 100%;
		}
	}

	:where(.theme-transparent) &{
		span, &:hover span, &:focus span{
			background-color: @color-light;
		}
	}
}

// Mobile menu
#mobile-navigation{.deferred();}


/*------ header ------*/

// Header contact info
#page-contact-top{
	margin-bottom: 20px;
	.show-notebook();

	.page-contact{
		.flexbox(@main: flex-end;);
		gap: 5px 15px;
		&:extend(.font-caption);
		color: @color-light;
		line-height: var(--line-height-thin);

		.label,
		.tollfree,
		.fax,
		.mailto{
			display: none;
		}

		li{
			&::before{
				.trans(color);
			}

			&:hover, &:focus{
				color: @color-gray-light;
			}
		}

		a{
			color: @color-light;
		}

		:where(#page-navbar.theme-transparent) &{
			color: @color-gray-light;

			li:hover, li:focus{
				color: @color-light;
			}
		}
		.link, .search{
			vertical-align: middle;
			padding: 15px 10px;
			.fluid-property(font-size, 16px,18px);
			color:@color-yellow;
			.bold();
			&:hover, &:focus{
				color: @color-light;
			}
		}
		.search{
			color:@color-light;
		}
	}
}

@media @notebook{
	#page-contact-top{
		.trans(height);
		.trans(margin);
		.trans(opacity);
		.trans(transform);
	}
}

// Banner
#page-hero{
	position: relative;
	.flexbox(@cross: center;);
	.fluid-property(min-height, 500px, 666px, 480px, 1024px);
	// padding: calc(var(--navbar-height) + 80px) 0 80px;
	// background: var(--theme-bg);
	background: url('@{path}images/svg/banner.svg') no-repeat center center;
	// background: url('./images/svg/banner.svg') no-repeat center center;
	// background: url('./../../../images/svg/banner.svg') no-repeat center center;
	// transform: rotate(180deg);
	background-position: center center;
	// background-origin: round;
	// z-index: 0;
	z-index:-1;
	background-size:cover;

	min-height: 700px;

	// Fix for white gaps on large screens
	// width: 100vw; // Set width to viewport width
	margin-left: calc(-50vw + 50%); // Center the element
	box-sizing: border-box; // Ensure padding doesn't add to width
	overflow-x: hidden; // Prevent horizontal scrolling

	.page-hero-wrapper{
		width: 100%;
	}

	#page-header{
		.fluid-property(margin-top, 55px, 100px, 480px, 1024px);
	}

	.container{
		&:extend(.container.container-lg);
		position: relative;
		// max-width: 100%; // Ensure container doesn't exceed hero width
		// width: 100%; // Full width
	}

	.page-title-wrapper{
		display: flex;
		flex-direction: column;
		&:extend(.title-decor all);
		.text-shadow(@blur: 25px;);

		h1{
			margin-bottom: 0;
		}

		.fancy-text{
			color: @color-theme2;
		}

		.page-title{
			color:@color-light;
		}

		.page-subtitle{
			&:extend(.font-h5);
			margin-top: 5px;
			font-family: @font-alt;
			.extrabold();
			color: @color-light;
			line-height: var(--line-height-normal);
			order: -1;
		}
	}

	.page-buttons{
		.flexbox(@cross: flex-start;);
		gap: 10px;
		margin-top: 20px;
		.fluid-property(margin-top, 15px, 20px);

		.button{
			&:extend(.button.outline);
			&:extend(.button.hover-light);
		}
	}

	&.noimage{
		.fluid-property(margin-top, -80px, -150px, 480px, 1024px);
		// .fluid-property(min-height, 0px, 450px, 480px, 1024px);
		.fluid-property(min-height, 550px, 680px, 700px, 1024px);
		padding: calc(var(--navbar-height) + 40px) 0 40px;

		.page-title-wrapper{
			text-shadow: none;
		}
	}
}

@media @notebook{
	#page-hero{
		.container{
			.flexbox(@flow: row nowrap; @main: space-between;);
			gap: 40px;
		}

		.page-title-wrapper{
			width: 65%;
		}

		.page-buttons{
			flex-flow: column nowrap;
			align-items: stretch;
			max-width: 25%;
			// margin-top: 20px;
		}
	}
}

// Slideshow
#slideshow{
	position: relative;
	overflow: hidden;

	.slide-wrapper{
		position: relative;
		min-height: 100vh;
		.flexbox(column; flex-end; flex-start);

		.slide-media{
			.position();
			z-index: -1;
			background-color: @color-gray-lighter; // in case image loads slow

			.overlay{
				z-index: 1;
			}
		}

		.slide-content{
			position: relative;
			// .fluid-property(--padding-block, 90px, 240px);
			.fluid-property(--padding-block, 70px, 100px);
			// width: 100%;
			width: 40%;
			@media @max-notebook{
				width: 65%;
			}
			@media @max-tablet-l{
				width: 75%;
			}
			padding: calc(var(--navbar-height) + var(--padding-block)) 0 var(--padding-block);
			text-align: center;
			margin: auto;
			z-index: 1;

			.container{
				// &:extend(.container.container-lg);
				&:extend(.container-width all);
			}

			.slide-title, .slide-text{
				.text-shadow(@blur: 25px;);
			}

			.slide-title{
				h2{
					&:extend(.font-h1);
					color: @color-light;
					margin-bottom: 0;
				}

				.fancy-text{
					color: inherit;
				}
			}

			.slide-text{
				&:extend(--font-paragraph);
				font-family: @font-base;
				margin-top: 10px;
				.regular();
				color: @color-light;
				line-height: var(--line-height-thin);
			}

			.slide-buttons{
				.flexbox(@main: center; @cross: flex-start;);
				gap: 10px;
				margin-top: 40px;

				.button{
					&:extend(.button.hover-light);
				}

				.button ~ .button{
					&:extend(.button.outline);
				}
			}
		}
	}

	// Controls
	.slideshow-navigation{
		display: none;
	}

	.slideshow-pagination{
		.flexbox(row nowrap; center; center);
		position: absolute;
		bottom: 0;
		.fluid-property(padding-block, 20px, 50px);
		z-index: 1;

		.swiper-pagination-bullet{
			.fluid-property(width, 12px, 15px);
			.fluid-property(height, 12px, 15px);
			margin: 5px 2.5px;
			background: @color-gray-lighter;
			opacity: 0.5;
			.trans(background-color);
			.trans(opacity);
		}

		.swiper-pagination-bullet-active, .swiper-pagination-bullet:hover, .swiper-pagination-bullet:focus{
			background: @color-theme3;
			opacity: 0.75;
		}
	}

	&:not(.swiper-initialized) .slide ~ .slide{
		display: none; // Hide following slides till JS loads
	}
}

// Breadcrumbs
#breadcrumbs{
	.fluid-property(margin-block, 10px, 20px);
	&:extend(.font-caption);
	font-family: @font-alt;
	color: @color-gray;
	line-height: var(--line-height-normal);

	li{
		display: inline-block;
		vertical-align: top;
	}

	a{
		color: inherit;

		&:hover, &:focus{
			color: @color-gray-dark;
		}
	}

	.arrow{
		padding: 0 2.5px;
	}
}


/*------ body content ------*/

.panel{
	.fluid-property(--panel-margin; 30px; 50px);
	.fluid-property(--panel-margin-sm; 30px; 40px);
	margin-top: var(--panel-margin);

	&:first-child{
		margin-top: var(--panel-margin-sm);
	}
	&:last-child{
		margin-bottom: var(--panel-margin-sm);
	}

	h1.cms{
		&:extend(.font-h1);
	}

	h1{
		&:extend(.font-h6);
		.bold();
		color: @color-theme2;
		line-height: var(--line-height-normal);
	}

	&.standard, &.promo, &.mini-promo, &.gallery{
		.panel-header{
			margin: 0 0 20px;

			.container{
				--line-left: calc((var(--line-width) + var(--line-gap) - var(--container-padding)) * -1);

				&:extend(.title-decor all);
			}

			h2{
				margin: 0;
			}
		}
	}

	&.standard{
		.panel-text{
			max-width: 775px;
		}

		.panel-text:has(#register-form),
		.panel-text:has(.billing-profiles-list),
		.panel-text:has(.invoices-container),
		.panel-text:has(.invoice-details-container),
		.panel-text:has(.review-payment-container),
		.panel-text:has(.tournament-search-container),
		.panel-text:has(#tournament-information),
		.panel-text:has(#registration_content),
		.panel-text:has(.dashboard-container) {
			max-width: 1800px;
	   	}

		&.has-map{
			position: relative;
			z-index: 2;
		}
	}

	&:where(:not(.standard:first-child)){.deferred();}
}

.panel-tabs{.deferred(); }


/*------ forms ------*/

form, .input, .select, .textarea{.deferred();}


/*------ leadins ------*/

.leadin-popup{.deferred();}


/*------ widgets ------*/

// Contact info
.page-contact{
	.unstyled-list();
	line-height: var(--line-height-normal);

	li{
		.flexbox(row wrap; flex-start; baseline);

		.label{
			line-height: var(--line-height-thin);
			order: -1;
			.flex(0 0 100%);
		}

		&::before{
			margin-right: 8px;
			text-align: center;
		}

		.value{
			.flex(1 0 1px);
		}

		&.address{
			.line1,
			.line2,
			.line3{
				display: inline-block;
			}
		}

		&.address::before{.font-awesome(f3c5); }
		&.tollfree::before,
		&.phone::before{.font-awesome(f3cd); }
		&.fax::before{.font-awesome(f1ac); }
		&.mailto::before,
		&.email::before{.font-awesome(f0e0); }
	}
}


/*------ footer ------*/

#page-footer{.deferred();}