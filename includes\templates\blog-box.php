<div class="blog-entry <?php echo $entry['class']??''; ?>" style="background-image: url('<?php echo $entry['image']; ?>')">

	<div class="blog-content">
		<p><span class="blog-category-label">
			<?php echo $entry['category_name']; ?>
		</span></p>
		<h4>
			<a href="<?php echo $entry['url']; ?>"><?php echo $entry['title']; ?></a>
		</h4>
		<p class="blog-description">
			<?php
			$word_count = str_word_count($entry['description']);
			if ($word_count > 25) {
				$words = explode(' ', $entry['description']);
				$words = array_slice($words, 0, 25);
				echo implode(' ', $words) . '... ';
			} else {
				echo $entry['description'];
			}
			?>
		</p>
		<div class="blog-footer">
			<a href="<?php echo $entry['url']; ?>" class="button secondary light"><span class="left-border"></span><span class="right-border"></span>READ MORE</a>
			<span class="read-time"><?php echo ceil($word_count/200); ?> min read</span>
		</div>
	</div>
</div>
