<?php

define('DB_HOST', 'localhost'); //<INSERT IP ADDRESS>
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_TABLE', 'pga');
// define('DB_TABLE', 'pga_new');
// define('DB_PORT', '3307');

// dev portal
// // define('DB_HOST', '***************'); //<INSERT IP ADDRESS>
// define('DB_HOST', 'mariadb-131.wc1.lan3.stabletransit.com'); //***************
// define('DB_USER', '2007829_pgaadmin');
// define('DB_PASS', 'QDYSAW2mpsF7Purc8qaZbU');
// define('DB_TABLE', '2007829_pgahcms');

// live portal
// define('DB_HOST', 'mariadb-009.wc1.lan3.stabletransit.com'); //**************
// define('DB_USER', '2007829_pgauser');
// define('DB_PASS', 'r1V45^I74vpQM@0H');
// define('DB_TABLE', '2007829_sitecms');
// define('DB_PORT', ini_get('mysqli.default_port'));


define('DB_PORT', ini_get('mysqli.default_port'));

require_once($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Database.class.php");
$db = Database::get_instance();

?>