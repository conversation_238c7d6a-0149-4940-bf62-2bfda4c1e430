<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['top-100-points']) {
	
	//Define vars
	$record_db = 'pd_points';
	$record_id = 'points_id';
	$record_name = 'Points';
	$imagedir = "../images/heroes/";
	$CMSUploader = new CMSUploader('banner', $imagedir);
	
	$errors = false;
	$required = array();
	$required_fields = array('category_id' => 'Category', 'title' => 'Title', 'points' => 'Points'); 
	
	//Get current year
	$query = $db->query("SELECT `current_year` FROM `pd_settings` WHERE `id` = 1");
	$curryear = $db->fetch_array()[0]['current_year'];

	$year = (isset($_GET['year']) && !empty($_GET['year']) ? $_GET['year'] : $curryear);
	$mid = (isset($_GET['mid']) && !empty($_GET['mid']) ? $_GET['mid'] : NULL);
	
	//Member types
	$membership_types = array();
	$query = $db->query("SELECT `membership_id`, `membership_name` FROM `membership_types` ORDER BY `membership_name` ASC");
	if($query && !$db->error()){
		$membership_types = $db->fetch_array();
	}
	
	//Get records
	$records_arr = array();
		
	//Get selected record
	if(ITEM_ID != ''){
		
		$params = array($year, ITEM_ID);
		$query = $db->query("SELECT `$record_db`.*, `account_profiles`.`profile_id`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `facilities`.`facility_name`, `membership_types`.`membership_name`, `pd_categories`.`name` AS `category_name` FROM `$record_db` ".
		"LEFT JOIN `accounts` ON `$record_db`.`account_id` = `accounts`.`account_id` ".
		"LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"LEFT JOIN `membership_types` ON `account_profiles`.`membership_id` = `membership_types`.`membership_id` ".
		"LEFT JOIN `pd_categories` ON `$record_db`.`category_id` = `pd_categories`.`category_id` ".
		"WHERE `$record_db`.`year` = ? && `$record_db`.`account_id` = ?", $params);
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				
				if(!array_key_exists($row['account_id'], $records_arr)){
					$records_arr[$row['account_id']] = array(
						'first_name' => $row['first_name'],
						'last_name' => $row['last_name'],
						'facility_name' => $row['facility_name'],
						'membership_name' => $row['membership_name'],
						'profile_id' => $row['profile_id'],
						'pd_points' => array()
					);
				}
				$records_arr[$row['account_id']]['pd_points'][] = $row;
			}
		}else{
			$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
		}
	
	//Get all records
	}else if(ACTION == ''){
		$params = array($year);
		if($searchterm != ""){
			$params[] = ' ';
			$params[] = '%' .$searchterm. '%';
			$params[] = '%' .$searchterm. '%';
			$params[] = '%' .$searchterm. '%';
		}
		if(!empty($mid)){
			$params[] = $mid;
		}
		$query = $db->query("SELECT `$record_db`.*, SUM(`$record_db`.`points`) AS `points_total`, `account_profiles`.`profile_id`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `facilities`.`facility_name`, `membership_types`.`membership_name` FROM `$record_db` ".
		"LEFT JOIN `accounts` ON `$record_db`.`account_id` = `accounts`.`account_id` ".
		"LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"LEFT JOIN `membership_types` ON `account_profiles`.`membership_id` = `membership_types`.`membership_id` ".
		"WHERE `$record_db`.`year` = ? ".
		($searchterm != "" ? "&& CONCAT(`account_profiles`.`first_name`, ?,`account_profiles`.`last_name`) LIKE ? OR `facilities`.`facility_name` LIKE ? OR `membership_types`.`membership_name` LIKE ? " : "").
		(!empty($mid) ? "&& `account_profiles`.`membership_id` = ? " : "").
		"GROUP BY `accounts`.`account_id` ORDER BY `account_profiles`.`first_name` ASC, `account_profiles`.`last_name` ASC", $params);
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				$records_arr[$row[$record_id]] = $row;
			}
		}else{
			$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
		}
	}
	
	//Get libraries
	if(ACTION == 'add'){
		
		//Get categories
		$categories = array();
		$query = $db->query("SELECT * FROM `pd_categories` WHERE `status` = ? ORDER BY `parent_id` ASC, `ordering` ASC", array('Active'));
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				if(empty($row['parent_id'])){
					$row['category_name'] = $row['name'].' ('.$row['points'].' points)';
					$categories[$row['category_id']] = $row;
				}else{
					if(array_key_exists($row['parent_id'], $categories)){
						$row['category_name'] = $row['name'].' ('.$row['points'].' points' .($row['unit'] != '' ? '/'.$row['unit'] : ''). ')';
						$categories[$row['parent_id']]['sub_categories'][$row['category_id']] = $row;
						$categories[$row['category_id']] = $row;
					}

				}
			}
		}

		//Get active members
		$active_members = array();
		$query = $db->query("SELECT `account_profiles`.`account_id`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, IFNULL(`facilities`.`facility_name`, ?) AS `facility_name` FROM `account_profiles` ".
		"INNER JOIN `account_permissions` ON `account_profiles`.`account_id` = `account_permissions`.`account_id` && `account_permissions`.`role_id` = 2 ".
		"LEFT JOIN `accounts` ON `account_profiles`.`account_id` = `accounts`.`account_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"WHERE `accounts`.`status` = ? ".
		"ORDER BY `account_profiles`.`last_name`, `account_profiles`.`first_name`", array('No Facility', 'Active'));
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $member){
				$active_members[$member['account_id']] = $member;
			}
		}
					
	
	//Not found
	}else if(ACTION == 'edit'){
		if(ITEM_ID == '' || empty($records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}
	
	//Save item
	if(isset($_POST['save'])){
		
		//Award points
		if(ACTION == 'add'){

			//Validate
			$required_missing = false;
			if(!empty($required_fields)) {
				foreach($required_fields as $field_key => $field_name) {
					if(isset($_POST[$field_key])) {
						if(trim($_POST[$field_key]) == '') {
							$required_missing = true;
							array_push($required, $field_key);
						}
					} else {
						$required_missing = true;
						array_push($required, $field_key);
					}
				}
			}
			if($required_missing) {
				$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
			}
			
			//Validate selected members
			$selected_members = (isset($_POST['selected_members']) ? $_POST['selected_members'] : array());
			if(!empty($selected_members)){
												
				//Check against maximum point totals
				if(array_key_exists($_POST['category_id'], $categories)){
					$invalid_members = array();
					
					if(is_numeric($categories[$_POST['category_id']]['max_points'])){
						$params = array(
							$_POST['category_id'], 
							$year, 
							implode(',', $selected_members)
						);
						$query = $db->query("SELECT `account_id`, SUM(`points`) AS `total_points` FROM `$record_db` WHERE `category_id` = ? && `year`= ? && FIND_IN_SET(`account_id`, ?) GROUP BY `account_id`", $params);
						if($query && !$db->error()){
							if($db->num_rows()){
								$result = $db->fetch_array();
								foreach($result as $invalid){
									if($invalid['total_points'] >= $categories[$_POST['category_id']]['max_points']){
										$invalid_members[$invalid['account_id']] = $active_members[$invalid['account_id']]['last_name'].', '.$active_members[$invalid['account_id']]['first_name'];
									}
								}
								if(!empty($invalid_members)){
									$errors[] = 'The following members already exceed the maximum allowed points for selected category (' .$categories[$_POST['category_id']]['max_points']. ' points) and must be removed from your list before points can be saved:<br /><br />'.implode('<br />', $invalid_members);
								}
							}
							
						}else{
							$errors[] = 'Error validating member points. '.$db->error();
						}
					}
					
				}else{
					$errors[] = 'Category not found. Please select another.';
				}				
				
			}else{
				$errors[] = 'Please select at least one recipient.';
			}
			
			//Insert to db
			if(!$errors){			

				$db->new_transaction();
				
				$_OUTPUT = '';
				
				foreach($selected_members as $selected_id){
					$params = array(
						$selected_id,
						$_POST['category_id'],
						$categories[$_POST['category_id']]['points'],
						$year,
						$_POST['title'],
						$_POST['points'],
						date('Y-m-d H:i:s'),
						date('Y-m-d H:i:s')
					);
					$query = $db->query("INSERT INTO `$record_db`(`account_id`, `category_id`, `category_points`, `year`, `title`, `points`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?,?,?)", $params);
					
					$_OUTPUT .= "INSERT INTO `$record_db`(`account_id`, `category_id`, `category_points`, `year`, `title`, `points`, `date_added`, `last_updated`) VALUES('" .$selected_id. "','" .$_POST['category_id']. "','" .$categories[$_POST['category_id']]['points']. "','" .$year. "','" .$_POST['title']. "','" .$_POST['points']. "','" .date('Y-m-d H:i:s'). "','" .date('Y-m-d H:i:s'). "')<br />";
				}
				
				if(!$db->error()){
					$db->commit();
					$CMSBuilder->set_system_alert($record_name.' were successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				} else {
					$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
				}


			}else{
				$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			}
		
		//Delete points	
		}else{
			
			if(isset($_POST['delete']) && !empty($_POST['delete'])){
				
				$db->new_transaction();
				
				foreach($_POST['delete'] as $points_id){
					$params = array(ITEM_ID, $year, $points_id);
					$query = $db->query("DELETE FROM `$record_db` WHERE `account_id` = ? && `year` = ? && `points_id` = ?", $params);
				}
				
				if(!$db->error()){
					$db->commit();
					$CMSBuilder->set_system_alert($record_name.' were successfully saved.', true);
				} else {
					$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(), false);	
				}
			
			}else{
				$CMSBuilder->set_system_alert($record_name.' were successfully saved.', true);
			}
			
			header('Location: '.PAGE_URL.'?year='.$year);
			exit();
			
		}

	}
}

?>