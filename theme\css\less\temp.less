@charset "utf-8";
/* 
	temp.less
*/

/*------ imports ------*/
@import (reference) "layout.less";
.partner-gallary {
.panel{
    &.partners, &.staff{
		.fluid-property(--image-width, 150px, 150px);

		&:not(.gallery-listings) .panel-gallery{
			
			&:not(:only-child){
				.fluid-property(margin-top, 20px, 30px);
			}
		}

		.light-gallery{
			.flexbox(row);
			gap: 1px;
			overflow: auto hidden;

			> .gal-item{
				width: var(--image-width);
				.flex(0 0 auto);
				
			}

			&.swiper{
				display: block;
				overflow: hidden;

				.swiper-slide{
					width: var(--image-width);
				}
			}
		}
		.swiper-wrapper{
			align-items: center;
		}
		.swiper-scrollbar{
			display: none;
			position: relative;
			.fluid-property(margin-top; 20px; 60px);
			margin-inline: auto;
			left: auto;
			right: auto;
			bottom: auto;
			width: calc(100% - var(--container-padding) * 2);
			height: 10px;
			max-width: 470px;
			background: none;

			// Rail
			&::before{
				.position();
				height: 1px;
				margin: auto;
				background-color: @color-theme3;
				content: '';
			}

			.swiper-scrollbar-drag{
				border-radius: 3px;
				cursor: grab;
				background-color: @color-theme2;
				.trans(background-color);

				&:hover, &:focus{
					background-color: @color-theme1;
				}
			}
		}
	}

	&.partners, &.staff ,&.event{
		margin: 10px 0;
		padding: 50px 0;

		.container {
			z-index: 20;
		}

		.slick-track {
			.flexbox();
			margin: 0 auto;
		}

		.panel-carousel {
			&.card-items.simple {
				margin-top: 40px;
				margin-bottom: 0;
			}
		}

		.slick-navigation {
			margin-top: -78px;
			text-align: right;
			visibility: visible;
			position: relative;
		}
	}
}
.gal-item{
	position: relative;
	padding: 20px;
	a, img{
		width: 100%;
		display: block;
	}

	.gal-link{
		.overlay{
			// background-color: @color-overlay-dark;
			background: none !important;
			z-index: 0;
			opacity: 0;
		}

		&::after{
			// .position(0, 0, 0, 0, 1em);
			// .font-awesome(f065);
			// .fluid-size(22, 40);
			// text-align: center;
			// line-height: 1;
			// color: @color-light;
			// opacity: 0;
			// .text-shadow();
			// .trans(opacity);
			content: '' !important;
		}

		&:hover{
			.overlay{
				opacity: 0.5;
			}

			&::after{
				opacity: 1;
			}
		}
	}
}
}
.job-posting-form, #job-posting, #classified-posting{
	.member_classification{
		grid-template-columns: repeat(5, 1fr); 
	}
	.job_des{
		grid-template-columns: repeat(1, 1fr); 
	}
	.form-buttons{
		float: right;
		display: flex;
    	justify-content: space-between;
		// a{
		// 	--text-color: @color-coral-red;  
		// padding: 10px 20px 10px 0;  
		// font-size: 18px;
		// letter-spacing: -20;
		// border: none;
		// background: transparent;
		// color: var(--text-color);
		// position: relative;
		// display: inline-block;
		// transition: all 0.2s ease;
	
		// // Remove any inherited borders or lines
		// border-left: none;
		// border-right: none;
	
		// // Underline
		// &::before {
		// 	content: '';
		// 	position: absolute;
		// 	bottom: 0;
		// 	left: 10px;
		// 	width: 20px;  
		// 	height: 3px;
		// 	background-color: var(--text-color);
		// 	transition: width 0.2s ease-in-out;
		// }
	
		// // Arrow
		// &::after {
		// 	content: '>';
		// 	display: inline-block;
		// 	position: absolute;
		// 	right: 0;  
		// 	top: 50%;
		// 	transform: translateY(-50%);
		// 	opacity: 0;  
		// 	font-size: 18px;  
		// 	font-family: @font-base;
		// 	line-height: 1;
		// 	transition: all 0.2s ease;
		// }
	
		// // Hover effects
		// &:hover {
		// 	transform: translateX(-8px);
	
		// 	&::before {
		// 		width: calc(100% - 31px);  
		// 	}
	
		// 	&::after {
		// 		opacity: 1;
		// 		right: 0;  
		// 	}
		// }

		// &.light{
		// 	--text-color: @color-light;
		// }
		// }
	}
	.input-file-container {
    position: relative;
    width: 100%;
}

	.input-file {
		display: none; /* Hide the actual file input */
	}

	.input-file-trigger {
		display: flex;
		align-items: center;
		justify-content: space-between;
		// background-color: #1e2a38;
		color: #fff;
		padding: 12px 16px;
		border-radius: 6px;
		border: 1px solid #ccc;
		cursor: pointer;
		font-size: 14px;
		transition: background-color 0.3s ease;
		flex-direction: row-reverse;
	}

	.input-file-trigger i {
		background-color: #e74c3c;
		border-radius: 50%;
		padding: 8px;
		margin-left: 10px;
		color: #fff;
		font-size: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 32px;
		height: 32px;
	}

	.input-file-trigger.required::after {
		content: "*";
		color: red;
		margin-left: 6px;
	}

	small{
		float: right;
	}

	.checkbox, .radio{display:none;}
			.checkbox + label, .radio + label{position:relative; display:inline-block; padding:1px 10px 1px 26px; cursor:pointer; line-height:20px !important; width:auto !important; margin-bottom:5px;.regular();}
			.checkbox + label:before, .radio + label:before{
				font-family:FontAwesome; 
				display:inline-block; 
				font-size:18px;
				font-style:normal;
				line-height:18px;
				position:absolute; 
				left:0; 
				width:18px; 
				height:18px; 
				border:1px solid #ccc; 
				text-align:center;
				color:@color-theme1;
				background:@color-light;
			}
			.radio + label:before{border-radius:50%; font-size:10px; text-indent:1px;;}
			.checkbox + label:before, .radio + label:before{content:" ";}
			.checkbox:checked + label:before{content:"\f00c";} 
			.radio:checked + label:before{content:"\f111";}
			.checkbox:disabled + label:before, .radio:disabled + label:before{background-color:#eee;}
	

}
.color-red{
		color: #EF3E34;
	}
#job-browse{
		.container{
			// &:extend(.container-lg);
			.fluid-property(margin-top, 40px, 60px);
		}

		.dropdown-checkbox-wrapper{
			width:auto; margin:0 auto 10px; font-size:18px;  color:@color-gray; background-color:@color-light; overflow:hidden; 
			.title, .checklist{display:block; box-sizing:border-box; padding:10px 20px;@media @max-tablet-p{
					display:inline;
				}}
			.title{cursor:pointer;width:91%;
			}
			.checklist{display:none; list-style-type:none; margin:0; padding-top:0; width:100%; overflow:hidden; max-height:400px; overflow-y:auto;
				li{margin:0 0 0 5px;
					label{width:100% !important; display:inline-block; vertical-align:middle;}
				}
			}
			&.compact{
				.checklist{
					li{width:~"calc(50% - 10px)"; margin:0 5px 10px 5px; float:left;
						&:nth-child(even){margin:0 5px 10px 5px;}
					}
				}
			}
			.checkbox, .radio{display:none;}
			.checkbox + label, .radio + label{position:relative; display:inline-block; padding:1px 10px 1px 26px; cursor:pointer; line-height:20px !important; width:auto !important; margin-bottom:5px;.regular();}
			.checkbox + label:before, .radio + label:before{
				font-family:FontAwesome; 
				display:inline-block; 
				font-size:18px;
				font-style:normal;
				line-height:18px;
				position:absolute; 
				left:0; 
				width:18px; 
				height:18px; 
				border:1px solid #ccc; 
				text-align:center;
				color:@color-theme1;
				background:@color-light;
			}
			.radio + label:before{border-radius:50%; font-size:10px; text-indent:1px;;}
			.checkbox + label:before, .radio + label:before{content:" ";}
			.checkbox:checked + label:before{content:"\f00c";} 
			.radio:checked + label:before{content:"\f111";}
			.checkbox:disabled + label:before, .radio:disabled + label:before{background-color:#eee;}
		}
		.button.primary.light{
			color:@color-dark;
			--border-color-light: @color-dark;
   			--border-color: @color-dark;
		}
		.radio-buttons{
			.radio + label:before{border-radius:0%;}
			small{
				color:@color-dark;
				font-family: @font-base;
			}
		}
	}

#panel-60{
		.container{
			// &:extend(.container-lg);
			.fluid-property(margin-top, 40px, 60px);
		}

		#classifieds{
				.flexbox(@cross: flex-start;);
				.fluid-property(--gap-x, 30px, 50px, 1024px, 1920px);
				gap: 0 var(--gap-x);
			}
	
			#filter-form{
				width: 290px;
				margin-bottom: 0;
				.button{
					margin-right: 10px;
				}
				@media @max-tablet-p{
					width: 100%;
				}
			}
	
			.classifeids-listings-wrapper{
				width: ~"calc(100% - 290px - var(--gap-x))";
				.listing{
					padding: 10px 20px;
   					border: 1px solid #CCCCCC;
					margin: 0 auto 10px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					p{
						padding: 0px;
					}
					.job-title{
						color:@color-dark;
					}
				}

				@media @max-tablet-p{
					width: 100%;
				}
			}

		.dropdown-checkbox-wrapper{
			width:auto; margin:0 auto 10px; font-size:18px;  color:@color-gray; background-color:@color-light; overflow:hidden; 
			.title, .checklist{display:block; box-sizing:border-box; padding:10px 20px; @media @max-tablet-p{
					display:inline;
				}}
			.title{cursor:pointer;width:91%;}
			.checklist{display:none; list-style-type:none; margin:0; padding-top:0; width:100%; overflow:hidden; max-height:400px; overflow-y:auto;
				li{margin:0 0 0 5px;
					label{width:100% !important; display:inline-block; vertical-align:middle;}
				}
			}
			&.compact{
				.checklist{
					li{width:~"calc(50% - 10px)"; margin:0 5px 10px 5px; float:left;
						&:nth-child(even){margin:0 5px 10px 5px;}
					}
				}
			}
			.checkbox, .radio{display:none;}
			.checkbox + label, .radio + label{position:relative; display:inline-block; padding:1px 10px 1px 26px; cursor:pointer; line-height:20px !important; width:auto !important; margin-bottom:5px;.regular();}
			.checkbox + label:before, .radio + label:before{
				font-family:FontAwesome; 
				display:inline-block; 
				font-size:18px;
				font-style:normal;
				line-height:18px;
				position:absolute; 
				left:0; 
				width:18px; 
				height:18px; 
				border:1px solid #ccc; 
				text-align:center;
				color:@color-theme1;
				background:@color-light;
			}
			.radio + label:before{border-radius:50%; font-size:10px; text-indent:1px;;}
			.checkbox + label:before, .radio + label:before{content:" ";}
			.checkbox:checked + label:before{content:"\f00c";} 
			.radio:checked + label:before{content:"\f111";}
			.checkbox:disabled + label:before, .radio:disabled + label:before{background-color:#eee;}
		}
		.button.primary.light{
			color:@color-dark;
			--border-color-light: @color-dark;
   			--border-color: @color-dark;
		}
		.radio-buttons{
			.radio + label:before{border-radius:0%;}
			small{
				color:@color-dark;
				font-family: @font-base;
			}
		}
	}

	@media @notebook{
		#panel-60{
			.container{
			
				--container-max-width: var(--container-width);
			}

			

			#filter-form{
				.input, .title{height:55px; line-height:34px;}
				.title{margin:0;}
				.button{ height:auto;}
				.back{padding-left:20px; line-height:53px;}
				.nopadding{
					display: flex;
					justify-content: space-between;
				}
				border: none;
				.search-classified{
					input{
						border: none;
						width: 80%;
					}
					.button{
						width: 20%;
					}
					border:1px solid #ccc;
					margin-bottom: 10px;
					@media @max-tablet-p {
						display: flex;
					}
				}
			}
			.form-buttons{margin-top:20px;}
			.divider{margin:0 5px; display:inline-block;}
			.found-text{background-color:fade(@color-theme1, 20%);}

			form fieldset{
				input, label{
					&.side{float:left; width:~"calc(100% - 60px)";font-size:16px;.regular();color:@color-dark;
						+ .side{margin-bottom:0; float:right; line-height:53px; padding:0;
							.fa{margin:auto; left:0; top:0; transition:top 0.3s, transform 0.3s;}
							&.active{transform:scaleY(-1); top:0;}
						}
					}
				}
			}

			.dropdown-checkbox-wrapper{width:auto; margin:0 auto 10px; font-size:18px;  color:@color-gray; background-color:@color-light; overflow:hidden;border: 1px solid #ccc;; 
				.title, .checklist{display:block; box-sizing:border-box; padding:10px 20px;}
				.title{cursor:pointer;}
				.checklist{display:none; list-style-type:none; margin:0; padding-top:0; width:100%; overflow:hidden; max-height:400px; overflow-y:auto;
					li{margin:0 0 0 5px;
						label{width:100% !important; display:inline-block; vertical-align:middle;}
					}
				}
				
				&.compact{
					.checklist{
						li{width:~"calc(50% - 10px)"; margin:0 5px 10px 5px; float:left;
							&:nth-child(even){margin:0 5px 10px 5px;}
						}
					}
				}
			}

			@media @tablet-p{
				#application-form{
					.form-buttons{text-align:center;
						.button{float:none; display:inline-block;}
						.g-recaptcha{float:none;
							-webkit-transform-origin: 50% 0;
							-moz-transform-origin: 50% 0;
							-ms-transform-origin: 50% 0;
							-o-transform-origin: 50% 0;
							transform-origin: 50% 0;	
						}
					}
				}
			}

		}
	}

	
	@media @max-tablet-p {
		.search-classified{
			display: flex;		
		}
		
	}

@media @notebook{
		#job-browse{
			.container{
				.flexbox(@cross: flex-start;);
				.fluid-property(--gap-x, 30px, 50px, 1024px, 1920px);
				gap: 0 var(--gap-x);
				--container-max-width: var(--container-width);
			}
	
			#job-filters-wrapper{
				width: 290px;
				margin-bottom: 0;
				.button{
					margin-right: 10px;
				}
			}
	
			#job-listings-wrapper{
				width: ~"calc(100% - 290px - var(--gap-x))";
				.listing{
					padding: 10px 20px;
   					border: 1px solid #CCCCCC;
					margin: 0 auto 10px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					p{
						padding: 0px;
					}
					.job-title{
						color:@color-dark;
					}
				}
			}

			#filter-form{
				.input, .title{height:55px; line-height:34px;}
				.title{margin:0;}
				.button{ height:auto;}
				.back{padding-left:20px; line-height:53px;}
				.nopadding{
					display: flex;
					justify-content: space-between;
				}
				border: none;
				.search-job{
					input{
						border: none;
						width: 80%;
					}
					.button{
						width: 20%;
					}
					border:1px solid #ccc;
					margin-bottom: 10px;
				}
			}
			.form-buttons{margin-top:20px;}
			.divider{margin:0 5px; display:inline-block;}
			.found-text{background-color:fade(@color-theme1, 20%);}

			form fieldset{
				input, label{
					&.side{float:left; width:~"calc(100% - 60px)";font-size:16px;.regular();color:@color-dark;
						+ .side{margin-bottom:0; float:right; line-height:53px; padding:0;
							.fa{margin:auto; left:0; top:0; transition:top 0.3s, transform 0.3s;}
							&.active{transform:scaleY(-1); top:0;}
						}
					}
				}
			}

			.dropdown-checkbox-wrapper{width:auto; margin:0 auto 10px; font-size:18px;  color:@color-gray; background-color:@color-light; overflow:hidden;border: 1px solid #ccc;; 
				.title, .checklist{display:block; box-sizing:border-box; padding:10px 20px;}
				.title{cursor:pointer;}
				.checklist{display:none; list-style-type:none; margin:0; padding-top:0; width:100%; overflow:hidden; max-height:400px; overflow-y:auto;
					li{margin:0 0 0 5px;
						label{width:100% !important; display:inline-block; vertical-align:middle;}
					}
				}
				
				&.compact{
					.checklist{
						li{width:~"calc(50% - 10px)"; margin:0 5px 10px 5px; float:left;
							&:nth-child(even){margin:0 5px 10px 5px;}
						}
					}
				}
			}

			@media @tablet-p{
				#application-form{
					.form-buttons{text-align:center;
						.button{float:none; display:inline-block;}
						.g-recaptcha{float:none;
							-webkit-transform-origin: 50% 0;
							-moz-transform-origin: 50% 0;
							-ms-transform-origin: 50% 0;
							-o-transform-origin: 50% 0;
							transform-origin: 50% 0;	
						}
					}
				}
			}

		}
	}

	#careers{
		.full-column{overflow:hidden;}
		.left-column{position:relative; z-index:1; width:88%; float:left; box-sizing:border-box; padding-right:250px;
		.form-buttons{
			.button ~ .secondary{
				margin-left:8px;
			}
		}
		}
		.right-column{
			position:relative; 
			z-index:2; 
			// width:500px; 
			height:auto; 
			float:right; 
			padding-left:40px; 
			margin-left:-100%; 
			box-sizing:border-box; 
			border-left:1px solid @color-theme1; 
			background:none;	
			
			&.sticky{height:100%;}
			&.stuck{position:fixed; float:none; top:0; height:100%; margin-left:900px; padding:100px 40px 50px 0;}
			&.bottom{position:absolute; float:none; top:auto; bottom:0; height:auto; margin-left:900px; padding:0 40px 100px 0;}
			@media screen and(max-width:1440px){
				&.stuck{margin:0; padding-right:0; padding-left:40px; right:30px;}
				&.bottom{margin:0; padding:0 0 100px 40px; right:30px;}
			}
		}
	}
.checkbox:checked + label:after{content:"";} 

.success{
	background-color: #dff0d8;
	border: 1px solid #d6e9c6;
	color: #3c763d;
	padding: 15px;
	margin-bottom: 20px;
}
.error{
	background-color: #f2dede; border: 1px solid #ebccd1; color: #a94442; padding: 15px; margin-bottom: 20px;
}

#search-bar{
	 .input {
            flex-grow: 1;
            width: 100%;
            height: 45px;
            // Right padding needs space for both clear and search icons
            padding: 10px 75px 10px 15px;
            border: 1px solid @color-dark; // Black border
            border-radius: 5px;
            background-color: @color-light; // White background
            color: @color-dark; // Black text
            box-sizing: border-box;

            &::placeholder {
                color: @color-gray-dark;
            }
        }

        .clear-search-btn {
            position: absolute;
            right: 40px; // To the left of search icon
            top: 50%;
            transform: translateY(-50%);
            text-decoration: none;
            font-size: 1.5em;
            color: @color-gray-dark;
            cursor: pointer;
            line-height: 1;
            padding: 0 5px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                color: @color-dark;
            }
        }

        .button{
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            padding: 0;
            cursor: pointer;
            color: @color-theme2;
            font-size: 1.2em;
            line-height: 1;

            &:hover {
                color: darken(@color-theme2, 10%);
            }

            i {
                display: block;
            }
        }
}
#job-postings-table{
	td{
		padding: 0;
	}
}

#classified-table{
	tbody{
		td{
		padding: 0;
		}
	}
}

.media_center{
	border: none !important;
	.media{
		img:where([width][height]) {
			height: revert-layer;
			width: -webkit-fill-available;
			@media @max-tablet-p {
				object-fit: cover;
			}
		}
	}
}

blockquote{
	&:extend(.font-h6);
	position: relative;
	margin-top: 20px;
	.fluid-property(margin-left; 10px; 0px; @max-width: 1024px);
	margin-bottom: 20px;
	line-height: var(--line-height-normal);
	z-index: 0;
	font-style: italic;
	// max-width: 550px;
	.letter-spacing(@letter-spacing);
	
	// .bold();

	small{
		.regular(); 
		font-style: normal;
	}

	&::before {
		content: "\201c";
		position: absolute;
		top: -25px;
		left: -25px;
		display: block;
		padding-right: 10px;
		font-size: 150px;
		font-family: @font-alt;
		line-height: 0.7;
		z-index: -1;
		color: @color-light;
		text-shadow: 0px 2px 2px @color-theme1;
		background: @color-light;

		@media @max-tablet-p {
			left: -27px;
			font-size: 70px;
		}
	}




}
#panel-63{
	.panel-text{
		max-width: 100%;
	}
}
#award_winners{
	.flexbox(@cross: flex-start;);
				.fluid-property(--gap-x, 30px, 50px, 1024px, 1920px);
				gap: 0 var(--gap-x);
}

	
#sub-navigation{
	width: 290px;
	margin-bottom: 0;
	.button{
		margin-right: 10px;
	}
	h4{
		font-size: 24px;
	}
}

.center{
	width: ~"calc(100% - 290px - var(--gap-x))";
	@media @max-tablet-p {
		width: 100%;
	}
	h4,h3{
		font-size: 24px;
	}
}
.winners_list{
.flexbox(@main: left; @cross: flex-start;);
@media @max-tablet-p {
		justify-content: center;
	}
}
// /*------ directory ------*/
.personnel{display:-moz-inline-box; -moz-box-orient:vertical; display:inline-block; vertical-align:top; width: 170px; margin:20px 10px; text-align:center;
	// .circular{position:relative; width:220px; height:220px; overflow:hidden; display:inline-block; margin-bottom:20px; background:@color-light; .trans(border-color);
	// 	img{display:block; width:100%; height:100%;}
	// }
	// a.circular:hover{border-color:@color-theme1;}
	.default-photo{display:table; width:100%; height:100%; color:@color-gray;
		span{display:table-cell; vertical-align:middle; font-style:normal; font-size:38px;}
	}
	.details{display:block;
		h4{margin-bottom:5px;font-size:20px;
			a{color:@color-dark;
				&:hover{color:@color-theme1;}
			}
		}
		span{color:lighten(@color-gray, 15%);font-size: 13px;}
	}
}
.personnel > div{display:table; table-layout:fixed;}
.profile-photo{padding-bottom:20px; text-align:right;
	img{border:1px solid @color-light; width:100%;}
}
table.personnel-tbl{margin:30px 0; width:100%;
	td.description{padding:40px;}
}
.toggle{
	.expander{display:inline-block; padding-top:20px;}
	.expandable{height:240px; overflow:hidden;}
}

#contact-staff{margin:20px 0 30px;
	.personnel{display:table; width:100%; margin:20px 0 0;
		.circular{display:table-cell; width:100px; height:100px;}
		.details{display:table-cell; text-align:left; vertical-align:middle; padding-left:20px;
			h4{font-size:inherit; color:inherit; font-weight:bold; margin:0;}
		}
	}
}

@media @tablet-l{
	.profile-photo{display:none;}
}
#sub-navigation{
	ul{
	text-decoration: none;
    list-style: none;
    border: 1px solid #ccc;
    padding: 20px;
	margin: auto;
	li{
		border-bottom: 1px solid #ddd;
		padding: 15px;
	}
	}

	@media @max-tablet-p {
			display: none;
	}

}

#nav-menu{
	display: none;
	@media @max-tablet-p {
		display: block;
		#sub-navigation{
			display: none;
		}
	}
}
.top_pgm{
	.flexbox(@cross: flex-start;);
	.fluid-property(--gap-x, 30px, 50px, 1024px, 1920px);
	gap: 0 var(--gap-x);
}
.left_pgm{
	width: 290px;
	margin-bottom: 0;
}
.right_pgm{
	width: ~"calc(100% - 290px - var(--gap-x))";
	@media @max-tablet-p {
		width: 100%;
	}
	h4,h3{
		font-size: 24px;
	}
}
.membership_cat{
	list-style-type: none;
	li{
		width:auto; margin:0 auto 10px; font-size:18px;  color:@color-gray; background-color:@color-light; overflow:hidden;border: 1px solid #ccc;padding: 10px;
	}
}
#panel-77,#panel-72{
	.content-tabs{
		.flexbox(@cross: flex-start;);
		flex-wrap: nowrap;
		.fluid-property(--gap-x, 30px, 50px, 1024px, 1920px);
		gap: 0 var(--gap-x);
		.tabs-nav-wrapper{
			width: 290px;
		margin-bottom: 0;
		
		li{
			width:100%; margin:0 auto 10px; font-size:18px;  color:@color-gray; background-color:@color-light; overflow:hidden;border: 1px solid #ccc;padding: 10px;
			a{
				background-color: transparent !important;
				border: none;
				color: @color-coral-red;
				&:active,&:hover{
					color: @color-coral-red;
				}
			}
		}
		}

		.tabs-panel{
			width: ~"calc(100% - 290px - var(--gap-x))";
			@media @max-tablet-p {
				width: 100%;
			}
			h4,h3{
				font-size: 24px;
			}

			table{
				width: 100%;
				    margin: 10px 0 30px;
    		
				 border: 1px solid white;
 				 border-collapse: collapse;
				 th, td {
					background-color: #eeeeee;
					}
				h4{
					font-size: 26px;
				}
			}
		}
		border:none !important;
		
		@media @max-tablet-p {
			flex-direction: column !important;
		}
	}
	.ui-state-disabled:active,.ui-button:focus{
		background: none !important;
	}
	
}
#pd-search{
	display: flex;
	.form-field{
		display: flex;
		.select{
			margin-left: 5px;
		}
	}
}
.parallax {
	.panel-wrapper{
		text-align: -webkit-center !important;
	}
}
#panel-89{
	table{
		width: 100%;
			margin: 10px 0 30px;
	
			border: 1px solid white;
			border-collapse: collapse;
			th, td {
			background-color: #eeeeee;
			}
		h4{
			font-size: 26px;
		}
	}
}

#compensation-survey{
	/*------ form styles ------*/
fieldset{ border:1px solid @color-light; margin-bottom:20px;
	&.required{
		> .mce-tinymce{border: 1px solid @color-coral-red;}
		.mce-branding-powered-by{border-right: 1px solid @color-coral-red;}
	}
}
.form-column{box-sizing:border-box; width:48%;
	&.f_left{padding-right:5px;}
	&.f_right{padding-left:5px;}
	&.half{width:25%;}
}
.form-buttons{clear:both; margin-bottom:10px;
	.button.f_left{margin-right:5px;}
	.button.f_right{margin-left:5px;}
}
.form-uploader{overflow:hidden; padding-left:20px;}

a.previous, a.addnew{line-height:80px; padding:0 30px;}

label{display:block; letter-spacing:0; margin-bottom:5px;}
.input, .select, .textarea{
	font-size:18px; 
	font-family:@font-base; 
	font-style:italic;
	color:@font-color;
	letter-spacing:0; 
	text-shadow:none;
	font-weight:normal;
	background-color:@color-gray-lightest; 
	border:1px solid @color-gray-lightest;
	outline:none;
	height:70px; 
	width:100%;
	padding:10px 20px; 
	margin-bottom:10px;
	box-sizing:border-box;
	-webkit-appearance:none; 
	-moz-appearance:none; 
	appearance:none;
	border-radius:0px;
	
	-webkit-transition:background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
	-moz-transition:background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
	-o-transition:background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
	-ms-transition:background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
	transition:background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
	
	&::-webkit-input-placeholder{color:@font-color;opacity:1;}
	&:-moz-placeholder{color:@font-color;opacity:1;}
	&::-moz-placeholder{color:@font-color;opacity:1;}
	&:-ms-input-placeholder{color:@font-color;opacity:1;}
	
	// &:focus{color:#eee !important; background-color:@color-dark !important; border-color:@color-dark !important;}
	&:focus::-webkit-input-placeholder{color:#eee !important;}
	&:focus:-moz-placeholder{color:#eee;}
	&:focus::-moz-placeholder{color:#eee;}
	&:focus:-ms-input-placeholder{color:#eee;}
	
	&.required{border-color:@color-coral-red;}
	&.half{width:49%;}
	
	-webkit-background-clip:border-box !important;
	-moz-background-clip:border-box !important;
	background-clip:border-box !important;

}
.select{
	&:focus{color:@font-color !important; background-color:@color-light !important; border-color:@color-light !important;}
	&:focus::-webkit-input-placeholder{color:@font-color !important;}
	&:focus:-moz-placeholder{color:@font-color;}
	&:focus::-moz-placeholder{color:@font-color;}
	&:focus:-ms-input-placeholder{color:@font-color;}
}

p.jsvalidate.required{color:@color-coral-red;}
.textarea{height:230px; padding:20px; resize:none;}
.texteditor{height:400px;}

.select{background-image:url("@{path}images/ui/select-arrow.png"); background-position:right 32px; background-repeat:no-repeat; text-indent:0.01px; text-overflow:""; padding-right:34px;}
select::-ms-expand{display:none;}
:root .select{padding:10px 20px 10px 20px \ ;}

.checkbox, .radio{display:none;}
.checkbox + label, .radio + label{position:relative; display:inline-block; padding:1px 10px 1px 26px; cursor:pointer; line-height:20px !important; width:auto !important; margin-bottom:5px;}
.checkbox + label:before, .radio + label:before{
	font-family:FontAwesome; 
	display:inline-block; 
	font-size:18px;
	font-style:normal;
	line-height:18px;
	position:absolute; 
	left:0; 
	width:18px; 
	height:18px; 
	border:1px solid @color-light; 
	text-align:center;
	color:@color-theme1;
	background:@color-light;
}
.radio + label:before{border-radius:0px; font-size:10px; text-indent:1px;}
.checkbox + label:before, .radio + label:before{content:" ";}
.checkbox:checked + label:before{content:"\f00c";} 
.radio:checked + label:before{content:"\f111";}
.checkbox:disabled + label:before, .radio:disabled + label:before{background-color:#eee;}
 
.input-file-container{display:block; position:relative; line-height:28px;} 
.input-file-trigger{
	display:block; 
	overflow:hidden;
	padding:20px; 
	margin-bottom:10px; 
	height:28px; 
	background:@color-light; 
	color:inherit; 
	font-size:18px; 
	text-overflow:ellipsis;
	white-space:nowrap;
	cursor:pointer; 
	border:1px solid @color-light;
	.trans(color);
	.fa{margin-right:10px;}

	&.required{border-color:@color-coral-red;}
}

/*------ slider ------*/
.ui-slider {position: relative; text-align: left;}
.ui-slider .ui-slider-handle {position: absolute; z-index: 2; width: 1.2em; height: 1.2em; cursor: default; -ms-touch-action: none; touch-action: none;}
.ui-slider .ui-slider-range {position: absolute; z-index: 1; font-size: .7em; display: block; border: 0; background-position: 0 0;}
.ui-slider.ui-state-disabled .ui-slider-handle, .ui-slider.ui-state-disabled .ui-slider-range {filter: inherit;} 
.ui-slider-horizontal{height: .8em;}
.ui-slider-horizontal .ui-slider-handle{top: -.3em; margin-left: -.6em;}
.ui-slider-horizontal .ui-slider-range{top: 0; height: 100%;}
.ui-slider-horizontal .ui-slider-range-min{left: 0;}
.ui-slider-horizontal .ui-slider-range-max{right: 0;}

.input.slider-container{height: 24px; margin: 27.5px 0 33.5px; padding: 0; border-width: 1px 0;
	.slider{height: 48px;
	    margin: -13px 0 0;
	    display: none;
		border: none;
   		 background: transparent;
	    .handle{
			font-weight: bold;
			color: #fff;
			margin: 0 0 0 -24px;
			text-align: center;
			background:green;
			top: 0;
		    padding: 0 10px;
		    box-sizing: border-box;
		    width: auto;
		    min-width: 48px;
			height: 100%;
			line-height: 48px;
			cursor: pointer;
			.no-select();
			.trans(~"background-color 0.3s, color");}

		&.ui-state-disabled{cursor: not-allowed;

			.handle{background-color:@color-coral-red;
				color: transparent;
				cursor: not-allowed; }
		}

		&.ui-slider{display: block;}
	}
}
}




	

