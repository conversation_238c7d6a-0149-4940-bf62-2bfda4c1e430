!function(){var e={},n=function(n){for(var i=e[n],a=i.deps,r=i.defn,s=a.length,o=new Array(s),l=0;l<s;++l)o[l]=t(a[l]);var g=r.apply(null,o);if(void 0===g)throw"module ["+n+"] returned undefined";i.instance=g},i=function(n,i,t){if("string"!=typeof n)throw"module id must be a string";if(void 0===i)throw"no dependencies for "+n;if(void 0===t)throw"no definition function for "+n;e[n]={deps:i,defn:t,instance:void 0}},t=function(i){var t=e[i];if(void 0===t)throw"module ["+i+"] was undefined";return void 0===t.instance&&n(i),t.instance},a=function(e,n){for(var i=e.length,a=new Array(i),r=0;r<i;++r)a.push(t(e[r]));n.apply(null,n)};({}).bolt={module:{api:{define:i,require:a,demand:t}}};var r=i;!function(e,n){r(e,[],function(){return n})}("global!tinymce.util.Tools.resolve",tinymce.util.Tools.resolve),r("tinymce.core.PluginManager",["global!tinymce.util.Tools.resolve"],function(e){return e("tinymce.PluginManager")}),r("tinymce.core.util.Tools",["global!tinymce.util.Tools.resolve"],function(e){return e("tinymce.util.Tools")}),r("tinymce.plugins.responsivefilemanager.Plugin",["tinymce.core.PluginManager","tinymce.core.util.Tools"],function(e,n){return e.add("responsivefilemanager",function(e){function n(i){0===e.settings.external_filemanager_path.toLowerCase().indexOf(i.origin.toLowerCase())&&"responsivefilemanager"===i.data.sender&&(e.insertContent(i.data.html),e.windowManager.close(),window.removeEventListener?window.removeEventListener("message",n,!1):window.detachEvent("onmessage",n))}function i(){var i=window.innerWidth-30,t=window.innerHeight-60;i>1800&&(i=1800),t>1200&&(t=1200);var a=(i-20)%138;if((i=i-a+10)>600){var a=(i-20)%138;i=i-a+10}e.focus(!0);var r="RESPONSIVE FileManager";void 0!==e.settings.filemanager_title&&e.settings.filemanager_title&&(r=e.settings.filemanager_title);var s="key";void 0!==e.settings.filemanager_access_key&&e.settings.filemanager_access_key&&(s=e.settings.filemanager_access_key);var o="";void 0!==e.settings.filemanager_sort_by&&e.settings.filemanager_sort_by&&(o="&sort_by="+e.settings.filemanager_sort_by);var l="false";void 0!==e.settings.filemanager_descending&&e.settings.filemanager_descending&&(l=e.settings.filemanager_descending);var g="";void 0!==e.settings.filemanager_subfolder&&e.settings.filemanager_subfolder&&(g="&fldr="+e.settings.filemanager_subfolder);var d="";void 0!==e.settings.filemanager_crossdomain&&e.settings.filemanager_crossdomain&&(d="&crossdomain=1",window.addEventListener?window.addEventListener("message",n,!1):window.attachEvent("onmessage",n)),win=e.windowManager.open({title:r,file:e.settings.external_filemanager_path+"dialog.php?type=4&descending="+l+o+g+d+"&lang="+e.settings.language+"&akey="+s,width:i,height:t,inline:1,resizable:!0,maximizable:!0})}e.addButton("responsivefilemanager",{icon:"browse",tooltip:"Insert file",shortcut:"Ctrl+E",onclick:i}),e.addShortcut("Ctrl+E","",i),e.addMenuItem("responsivefilemanager",{icon:"browse",text:"Insert file",shortcut:"Ctrl+E",onclick:i,context:"insert"})}),function(){}}),t("tinymce.plugins.responsivefilemanager.Plugin")()}();