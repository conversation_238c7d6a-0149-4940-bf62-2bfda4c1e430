RewriteEngine On
RewriteBase /

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !index.php
RewriteCond %{REQUEST_URI} !(.*)/$
#RewriteRule ^(.*)$ /$1/ [L,R=301]
RewriteRule ^(.*)$ /pga/pga-hcms/$1/ [L,R=301]

#Redirect non-www to www:
#RewriteCond %{HTTP_HOST} !^www\. [NC]
#RewriteRule ^(.*)$ https://www.%{HTTP_HOST}/_PATH_TO_ROOT_/sitecms/$1 [R=301,L]

#Redirect from test link 
#RewriteCond %{HTTP_HOST} !^www\.website\.com(\/*)$ [NC]
#RewriteRule ^(.*)$ https://www\.website\.com/_PATH_TO_ROOT_/sitecms/$1 [L,R=301]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
#RewriteRule ^([^/\.]+) /_PATH_TO_ROOT_/sitecms/index.php
RewriteRule ^([^/\.]+) /pga/pga-hcms/index.php

DirectoryIndex index.html index.htm index.php index.cgi