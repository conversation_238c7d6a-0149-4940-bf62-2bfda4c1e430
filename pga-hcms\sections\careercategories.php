<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Career Categories  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter sortable'>";
		
			echo "<thead>";
			echo "<th width='10px' class='{sorter:false}'></th>";
			echo "<th width='350px'>Category</th>";
			echo "<th width='70px'>Visible</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr data-table='$record_db' data-column-name='$record_id' data-name='" . $row['category_name'] . "' data-id='" . $row[$record_id] . "'>";
					echo "<td class='handle'><span class='fa fa-arrows'></span></td>";
					echo "<td>" .$row['category_name']. "</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager(50);
		
		echo "</div>";	
	echo "</div>";

} else {

	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	} else if(ACTION == 'add' && !isset($_POST['save'])){	
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";
		// Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				<div class='panel-switch'>
					<label>Show $record_name</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " />
						<label for='showhide'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Title <span class='required'>*</span></label>
					<input type='text' name='category_name' value='" .(isset($row['category_name']) ? $row['category_name'] : ''). "' class='input".(in_array('category_name', $required) ? " required" : "")."' />
				</div>";
			echo "<div class='form-field'>
				<label>Numerical Order" .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). "</label>
				<select name='ordering' class='select'>
					<option value='101'>Default</option>";
					for($i=1; $i<101; $i++){
						echo "<option value='" .$i. "' " .(isset($row['ordering']) && $row['ordering'] == $i ? "selected" : ""). ">" .$i. "</option>";	
					}
				echo "</select>
			</div>";
			echo "</div>";
		echo "</div>"; // END Details

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";

}

?>