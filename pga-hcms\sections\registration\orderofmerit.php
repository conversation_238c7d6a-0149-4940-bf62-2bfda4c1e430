<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Get tournament categories
$categories = $Registration->get_categories(2);
	
//Search
echo "<div class='panel'>";
	echo "<div class='panel-header'>Generate Report
		<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
	</div>";

	echo "<div class='panel-content clearfix'>";
		echo "<form id='advanced-search-form' class='clearfix' action='".$root."reports/report-oom.php' method='get' enctype='multipart/form-data' target='_blank'>";
			echo "<div id='search-fields' class='column clearfix'>";
				echo "<div class='form-field'>
					<label>Year</label>
					<select name='year' class='select'>";
					for($y=date('Y'); $y>=2018; $y--){
						echo "<option value='".$y."'>".$y."</option>";
					}
					echo "</select><br />
					<label>Report</label>
					<select name='report' class='select nomargin'>
						<option value='points'>Overall Points</option>
						<option value='money'>Overall Money Earned</option>
						<option value='events'>Events Played</option>
						<option value='score'>Individual Scoring Average</option>
						<option value='overunder'>Individual Average Time Par</option>
					</select>
				</div>";
				echo "<div class='form-field'>
					<label>Categories <small>(Defaults to All)</small></label>
					<div style=''>";
						foreach($categories as $category){
							echo "<input type='checkbox' name='categories[]' id='category-".$category['category_id']."' class='checkbox' value='".$category['category_id']."' />
							<label for='category-".$category['category_id']."'>".$category['name']."</label><br />";
						}
					echo "</div>
				</div>";
			echo "</div>";

			echo "<div class='buttons-wrapper'>";
				echo "<div class='f_left'>";
					echo "<button type='submit' class='button'><i class='fa fa-file-pdf-o'></i>Generate</button>";
				echo "</div>";
			echo "</div>";

		echo "</form>";
	echo "</div>";
echo "</div>";


	echo "</div>";	
echo "</div>";

?>