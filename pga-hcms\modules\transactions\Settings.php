<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['transactions-settings']){
	//Define vars
	$errors = false;
	$required = array();

	//Get Payment Methods
	$payment_options = array();
	$reg_settings['payment_options'] = array();
	$get_payment_options = $db->query("SELECT * FROM `payment_options`");
	if($get_payment_options && !$db->error()) {
		$payment_options = $db->fetch_array();
		foreach($payment_options as $opt) {
			if($opt['status'] == 'Active'){
				$reg_settings['payment_options'][] = $opt['payment_id'];
			}
		}
	}

	//Get Tax Rates
	$reg_settings['tax_rates'] = array();
	$get_tax_rates = $db->query("SELECT * FROM `taxes` ORDER BY `state` ASC");
	if($get_tax_rates && !$db->error() && $db->num_rows() > 0) {
		$reg_settings['tax_rates'] = $db->fetch_array();
	}

	//Save changes
	if(isset($_POST['save'])) {
		
		//Defaults
		if(trim($_POST['federal_gst']) == ''){
			$_POST['federal_gst'] = 0;
		}
		if(trim($_POST['admin_fee']) == ''){
			$_POST['admin_fee'] = 0;
		}

		if(!$errors) {
			
			//Start transaction
			$db->new_transaction();

			//Update settings
			$params = array(
				$_POST['federal_gst'],
				$_POST['admin_fee'],
				$_POST['admin_fee_type'],
				(!empty($_POST['max_payment_amount']) ? $_POST['max_payment_amount'] : NULL),
				date("Y-m-d H:i:s"),
				$reg_settings['reg_system_id']
			);
			$query = $db->query("UPDATE `reg_settings` SET `federal_gst`=?, `admin_fee`=?, `admin_fee_type`=?, `max_payment_amount`=?, `last_updated`=? WHERE `reg_system_id`=?", $params);
		
			//Update Payment Methods
			$reg_settings['payment_options'] = (isset($_POST['payment_options']) ? $_POST['payment_options'] : array());
			$update = $db->query("UPDATE `payment_options` SET `status` = ?".(!empty($reg_settings['payment_options']) ? " WHERE `payment_id` NOT IN (".implode(",", $reg_settings['payment_options']).")" : ""), array('Inactive'));
			if(!empty($reg_settings['payment_options'])) {
				$update = $db->query("UPDATE `payment_options` SET `status` = ? WHERE `payment_id` IN (".implode(",", $reg_settings['payment_options']).")", array('Active'));
			}

			//Update Tax Rates
			foreach($reg_settings['tax_rates'] as $tax_row) {
				$state_code = strtolower($tax_row['state']);
				$tax_rate = $_POST["tax_rate_".$state_code];
				$tax_hst = (isset($_POST["hst_".$state_code]) ? '1' : '0');

				$update = $db->query("UPDATE `taxes` SET `rate`=?, `hst`=? WHERE `state`=?", array($tax_rate, $tax_hst, $tax_row['state']));
			}

			//Commit transaction
			if(!$db->error()){
				$db->commit(); 

				$CMSBuilder->set_system_alert('Transaction settings have been updated.', true);
				header('Location: '.PAGE_URL);
				exit();

			} else {
				$CMSBuilder->set_system_alert('Unable to save settings: '.$db->error(), false);
			}

		} else if($errors && is_array($errors)) {
			$CMSBuilder->set_system_alert(implode('<br/>', $errors), false);
		}
	}
}

?>