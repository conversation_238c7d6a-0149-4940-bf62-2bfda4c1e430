<?php

if(!empty($record)){

	//Payment form
	if(!$confirm){

		$html .= '<form name="payment-form" action="" method="post">';
			$html .= '<p><small>Required Fields</small> <strong class="color-red">*</strong></p>';

			//Registration
			if(!is_null(REG_ID)){
				$html .= '<h4>Registration Information</h4>
				<table cellpadding="0" cellspacing="0" border="0" width="100%" class="nobgs noresponsive noborder">
					<tr>
						<td width="200px">Registration No:</td>
						<td>' .$record['registration_number']. '</td>
					</tr>
					<tr>
						<td>Registration Date:</td>
						<td>' .date("M j, Y g:iA", strtotime($record['registration_date'])). '</td>
					</tr>
					<tr>
						<td>Balance Due:</td>
						<td>$' .number_format($record['payment_amount'], 2). '</td>
					</tr>
				</table>';

				//No balance to pay
				if($record['payment_amount'] <= 0){
					$html .= $Account->important('No outstanding balance for Registration No. '.$record['registration_number']);
					$html .= '<p><a href="' .$sitemap[60]['page_url']. '?action=edit&id=' .REG_ID. '">Back to Registration &rsaquo;</a></p>';

				//Balance exceeds maximum allowed
				}else if(!empty($reg_settings['max_payment_amount']) && $record['payment_amount'] > $reg_settings['max_payment_amount'] && $admin_fee == 0){
					$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> Balance due for Registration No. '.$record['registration_number']. ' exceeds the maximum online payment amount.');
					$html .= '<p><u>Pay by Cheque:</u> Please send a cheque to the PGA of Alberta Office, ' .$global['full_address']. '. Cheques are to be made payable to the PGA of Alberta. If you require assistance making a payment, please <a href="' .$_sitepages['contact']['page_url']. '">contact our office</a>.</p>';
					$html .= '<p><a href="' .$sitemap[60]['page_url']. '?action=edit&id=' .REG_ID. '">Back to Registration &rsaquo;</a></p>';
				}

			//Invoice
			}else if(!is_null(INVOICE_ID)){

				//Hio accounts redirected to pay
				if(!empty($record['hio_id']) && !$record['approved']){
					$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> Hole in one event was successfully submitted and will be approved upon receipt of payment.');
				}

				$html .= '<h4>Invoice Information</h4>
				<table cellpadding="0" cellspacing="0" border="0" width="100%" class="nobgs noresponsive noborder">
					<tr>
						<td width="200px">Invoice No:</td>
						<td>' .$record['invoice_number']. '</td>
					</tr>
					<tr>
						<td>Invoice Date:</td>
						<td>' .date("M j, Y g:iA", strtotime($record['invoice_date'])). '</td>
					</tr>
					<tr>
						<td>Balance Due:</td>
						<td>$' .number_format($record['payment_amount'], 2). '</td>
					</tr>
				</table>';

				//No balance to pay
				if($record['payment_amount'] <= 0){
					$html .= $Account->important('No outstanding balance for Invoice No. '.$record['invoice_number']);
					$html .= '<p><a href="' .$sitemap[76]['page_url']. '?action=edit&id=' .INVOICE_ID. '">Back to Invoice &rsaquo;</a></p>';

				//Balance exceeds maximum allowed
				}else if(!empty($reg_settings['max_payment_amount']) && $record['payment_amount'] > $reg_settings['max_payment_amount'] && $admin_fee == 0){
					$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> Balance due for Invoice No. '.$record['invoice_number']. ' exceeds the maximum online payment amount. Please <a href="' .$_sitepages['contact']['page_url']. '">contact us</a> to make a payment.');
					$html .= '<p><a href="' .$sitemap[76]['page_url']. '?action=edit&id=' .INVOICE_ID. '">Back to Invoice &rsaquo;</a></p>';
				}

			}

			//Balance owing
			if($record['payment_amount'] > 0 && ($record['payment_amount'] <= $reg_settings['max_payment_amount'] || empty($reg_settings['max_payment_amount']) || $admin_fee > 0)){

				//Personal
				$html .= '<h4>Personal Information</h4>
				<fieldset class="clearfix form-grid">
					<div class="form-column f_left">
						<label>First Name <strong class="color-red">*</strong></label>
						<input type="text" name="first_name" value="'.(isset($_SESSION['payment']['first_name']) ? $_SESSION['payment']['first_name'] : $Account->first_name).'" class="input'.(in_array('first_name', $required) ? ' required' : '').'" />
					</div>
					<div class="form-column f_right">
						<label>Last Name <strong class="color-red">*</strong></label>
						<input type="text" name="last_name" value="'.(isset($_SESSION['payment']['last_name']) ? $_SESSION['payment']['last_name'] : $Account->last_name).'" class="input'.(in_array('last_name', $required) ? ' required' : '').'" />
					</div>
					<div class="form-column f_left">
						<label>Email Address <strong class="color-red">*</strong></label>
						<input type="text" name="email" value="'.(isset($_SESSION['payment']['email']) ? $_SESSION['payment']['email'] : $Account->email).'" class="input'.(in_array('email', $required) ? ' required' : '').'" />
					</div>
					<div class="form-column f_right">
						<label>Phone Number <strong class="color-red">*</strong></label>
						<input type="text" name="phone" class="input' .(in_array('phone', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['payment']['phone']) ? $_SESSION['payment']['phone'] : $Account->phone). '" />
					</div>
				</fieldset>';

				//Billing
				$html .= '<h4>Billing Information</h4>';
				// TODO CHECK ACCOUNT ID FOR PRODUCTION
				if(!empty($billing_profiles) && USER_LOGGED_IN != 10213){ //Public account restricted
					foreach($billing_profiles as $billing){
						$card_errors = array();

						//Check card expiry
						$expiry_date = '20'.substr($billing['ccexpiry'], -2, 2).substr($billing['ccexpiry'], 0, 2).'01';
						if($expiry_date <= date("Ymd")){
							$card_errors[] = 'Credit card is expired. Please choose another.';
						}

						//Check card type
						$valid_card = false;
						$accepted_cards = array();
						foreach($payment_options as $payopt){
							$accepted_cards[] = $payopt['name'];
							if($billing['cctype'] == $payopt['type']){
								$valid_card = true;
							}
						}
						if(!$valid_card){
							$card_errors[] = 'Invalid card type. We currently only accept '.implode(' &amp; ', $accepted_cards).'.';
						}

						$billing_label = $billing['cctype']. ' **** **** **** '.$billing['ccnumber']. ' &nbsp; '.substr($billing['ccexpiry'], 0, 2).'/'.substr($billing['ccexpiry'], -2, 2);
						if(empty($card_errors)){
							$html .= '<fieldset class="form-grid">
								<input type="radio" class="radio billing-toggle" id="billing-' .$billing['billing_id']. '" name="billing_id" value="' .$billing['billing_id']. '" onclick="billingToggle(false);"' .(isset($_SESSION['payment']['billing_id']) && $_SESSION['payment']['billing_id'] == $billing['billing_id'] ? ' checked' : ''). ' />
								<label class="billing-new-1"for="billing-' .$billing['billing_id']. '">&nbsp;&nbsp;' .$billing_label. '</label>
							</fieldset>';
						}else{
							$html .= '<fieldset class="form-grid">
								<input type="radio" class="radio billing-toggle" id="billing-' .$billing['billing_id']. '" disabled />
								<label class="billing-new-1" for="billing-' .$billing['billing_id']. '">&nbsp;&nbsp;' .$billing_label. '</label>
								<small class="color-red"><i class="fa fa-exclamation-triangle"></i> ' .(implode(' ', $card_errors)). '</small>
							</fieldset>';
						}
					}
				}
				$html .= '<fieldset class="clearfix form-grid-container">';
					if(!empty($billing_profiles)){
						$html .= '<input type="radio" class="radio billing-toggle" name="billing_id" id="billing-new" value="" onclick="billingToggle(true);"' .(isset($_SESSION['payment']['billing_id']) && $_SESSION['payment']['billing_id'] == '' ? ' checked' : ''). ' />
						<label for="billing-new" class="billing-new">&nbsp;&nbsp;New Credit Card</label>';
					}

					//New profile form
					$html .= '<div id="billing-profile" class="clear" style="' .((isset($_SESSION['payment']['billing_id']) && $_SESSION['payment']['billing_id'] == '') || empty($billing_profiles) ? '' : 'display:none;'). '">';
						$html .= (!empty($billing_profiles) ? '' : '');

						// $html .= '<div class="form-column f_left ">
						// 		<div class="form-column f_left">
						// 	<label>Billing Address <strong class="color-red">*</strong></label>
						// 	<input type="text" name="address1" class="input' .(in_array('address1', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['payment']['bill_address1']) && $_SESSION['payment']['billing_id'] == '' ? $_SESSION['payment']['bill_address1'] : $Account->address1). '" />
						// 	</div>
						// 	<div class="form-column f_right">
						// 	<label>Unit No.</label>
						// 	<input type="text" name="address2" class="input' .(in_array('address2', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['payment']['bill_address2']) && $_SESSION['payment']['billing_id'] == '' ? $_SESSION['payment']['bill_address2'] : $Account->address2). '" />
						// 	</div>
						// 	<div class="form-column f_left form-field">
						// 	<label>City/Town <strong class="color-red">*</strong></label>
						// 	<input type="text" name="city" class="input' .(in_array('city', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['payment']['bill_city']) && $_SESSION['payment']['billing_id'] == '' ? $_SESSION['payment']['bill_city'] : $Account->city). '" />
						// 	</div>
						// </div>
						// <div class="form-column f_right">
						// 	<label>Province/State <strong class="color-red">*</strong></label>
						// 	<select name="province" class="select' .(in_array('province', $required) ? ' required' : ''). '">
						// 		<optgroup label="Canada">';
						// 		for($p=1; $p<=count($provinces); $p++){
						// 			$html .= '<option value="' .$provinces[$p][1]. '"' .((isset($_SESSION['payment']['bill_province']) && $_SESSION['payment']['bill_province'] == $provinces[$p][1]) && $_SESSION['payment']['billing_id'] == '' || $Account->province == $provinces[$p][1] ? ' selected' : ''). '>' .$provinces[$p][0]. '</option>';
						// 		}
						// 		$html .= '</optgroup>
						// 		<optgroup label="United States">';
						// 			for($p=1; $p<=count($states); $p++){
						// 				$html .= '<option value="' .$states[$p][1]. '"' .((isset($_SESSION['payment']['bill_province']) && $_SESSION['payment']['bill_province'] == $states[$p][1]) && $_SESSION['payment']['billing_id'] == '' || $Account->province == $states[$p][1] ? ' selected' : ''). '>' .$states[$p][0]. '</option>';
						// 			}
						// 		$html .= '</optgroup>
						// 	</select>
						// 	<label>Postal/Zip Code <strong class="color-red">*</strong></label>
						// 	<input type="text" name="postal_code" class="input' .(in_array('postal_code', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['payment']['bill_postalcode']) && $_SESSION['payment']['billing_id'] == '' ? $_SESSION['payment']['bill_postalcode'] : $Account->postal_code). '" />
						// 	<label>Country <strong class="color-red">*</strong></label>
						// 	<select name="country" class="select' .(in_array('country', $required) ? ' required' : ''). '">
						// 		<option value="CA"' .((isset($_SESSION['payment']['bill_country']) && $_SESSION['payment']['bill_country'] == "CA") && $_SESSION['payment']['billing_id'] == '' || $Account->country == "CA" ? ' selected' : ''). '>Canada</option>
						// 		<option value="US"' .((isset($_SESSION['payment']['bill_country']) && $_SESSION['payment']['bill_country'] == "US") && $_SESSION['payment']['billing_id'] == '' || $Account->country == "US" ? ' selected' : ''). '>United States</option>
						// 	</select>
						// </div><br class="clear" />';

					/////
					$html .= '<div class="form-grid no-border">
					<div class="form-field">
						<label for="bill_address1">Street Address <strong class="color-red">*</strong></label>
						<input type="text" name="address1" id="bill_address1" class="input' .(in_array('bill_address1', $required) ? ' required' : ''). '" value="' .htmlspecialchars($profile['bill_address1'] ?? $Account->address1 ?? '', ENT_QUOTES, 'UTF-8'). '" />
				</div>';
				// <div class="form-field">
				// 	<label for="bill_province">Province/State <strong class="color-red">*</strong></label>';

				//
				$html .= '<div class="form-field">
				<label for="bill_province">Province/State <strong class="color-red">*</strong></label>
				<select name="province" id="bill_province" class="select' .(in_array('bill_province', $required) ? ' required' : ''). '">
				<option value="">- Select -</option>';
				$selected_province = $profile['bill_province'] ?? $Account->province ?? '';
				if(isset($provinces)&&is_array($provinces)):
					$html .= '<optgroup label="Canada">';
					foreach($provinces as $code=>$name){
						// $html .= "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['province']??null,$Account->province??'',$code);
						$selected = ($selected_province == $code ? ' selected' : '');
						$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
						// echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";
					}
					$html .= '</optgroup>';
				endif;
				if(isset($states)&&is_array($states)):
					$html .= '<optgroup label="United States">';
					foreach($states as $code=>$name){
						//  $html .= "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['province']??null,$Account->province??'',$code);echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";
						$selected = ($selected_province == $code ? ' selected' : '');
						$html .= '<option value="' .$code. '"' .$selected. '>' .$name. '</option>';
					}
					$html .= '</optgroup>';
				endif;
				$html .= '</select></div>';


			// $html .= '</div>';
			$html .= '<div class="form-field">
				<label for="bill_address2">Unit No.</label>
				<input type="text" name="address2" id="bill_address2" class="input' .(in_array('bill_address2', $required) ? ' required' : ''). '" value="' .htmlspecialchars($profile['bill_address2'] ?? $Account->address2 ?? '', ENT_QUOTES, 'UTF-8'). '" />
			</div>';
			$html .= '<div class="form-field">
				<label for="bill_postalcode">Postal Code <strong class="color-red">*</strong></label>
				<input type="text" name="postal_code" id="bill_postalcode" class="input' .(in_array('bill_postalcode', $required) ? ' required' : ''). '" value="' .htmlspecialchars($profile['bill_postalcode'] ?? $Account->postal_code ?? '', ENT_QUOTES, 'UTF-8'). '" />
			</div>
			<div class="form-field">
				<label for="bill_city">City/Town <strong class="color-red">*</strong></label>
				<input type="text" name="city" id="bill_city" class="input' .(in_array('bill_city', $required) ? ' required' : ''). '" value="' .htmlspecialchars($profile['bill_city'] ?? $Account->city ?? '', ENT_QUOTES, 'UTF-8'). '" />
			</div>
			<div class="form-field">
				<label for="bill_country">Country <strong class="color-red">*</strong></label>
				<select name="country" id="bill_country" class="select country' .(in_array('bill_country', $required) ? ' required' : ''). '">
					<option value="">- Select -</option>';
					$selected_country = $profile['bill_country'] ?? $Account->country ?? '';
					if (isset($countries) && is_array($countries)) {
						foreach ($countries as $code => $name) {
							$code_esc = htmlspecialchars($code, ENT_QUOTES, 'UTF-8');
							$name_esc = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
							$selected = ($selected_country == $code ? ' selected' : '');
							$html .= '<option value="' .$code_esc. '"' .$selected. '>' .$name_esc. '</option>';
						}
					}
				$html .= '</select>
			</div>
		</div>';
						/////

				// 		$html .= '<hr />

				// 		<div class="form-column f_left">
				// 			<label>Cardholder Name <strong class="color-red">*</strong></label>
				// 			<input type="text" name="ccname" class="input' .(in_array('ccname', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['payment']['ccname']) && $_SESSION['payment']['billing_id'] == '' ? $_SESSION['payment']['ccname'] : ''). '" />

				// 			<label>Card Number <strong class="color-red">*</strong> &nbsp; ';
				// 			foreach($payment_options as $payopt){
				// 				$cctype = ($payopt['type'] == 'MC' ? 'mastercard' : strtolower($payopt['type']));
				// 				$html .= '<i class="fa fa-cc-' .$cctype. ' fa-lg" title="' .$payopt['name']. '"></i> ';
				// 			}
				// 			$html .= '</label>
				// 			<input type="text" name="ccnumber" class="input number' .(in_array('ccnumber', $required) ? ' required' : ''). '" value="' .(isset($_POST['ccnumber']) && $_SESSION['payment']['billing_id'] == '' ? $_POST['ccnumber'] : ''). '" maxlength="16" />
				// 		</div>
				// 		<div class="form-column f_right">
				// 			<label>Expiry Date <strong class="color-red">*</strong></label>
				// 			<select name="exp_month" class="select half f_left' .(in_array('exp_month', $required) ? ' required' : ''). '">
				// 			<option value="">- Month -</option>';
				// 			foreach($months as $key=>$value){
				// 				$key = str_pad($key, 2, '0', STR_PAD_LEFT);
				// 				$html .= '<option value="' .$key. '"' .(isset($_SESSION['payment']['exp_month']) && $_SESSION['payment']['exp_month'] == $key && $_SESSION['payment']['billing_id'] == '' ? ' selected' : ''). '>' .$value. ' (' .$key. ')</option>';
				// 			}
				// 			$html .= '</select>
				// 			<select name="exp_year" class="select half f_right' .(in_array('exp_year', $required) ? ' required' : ''). '">
				// 			<option value="">- Year -</option>';
				// 			for($y=date('Y'); $y<=(date('Y')+20); $y++){
				// 				$html .= '<option value="' .substr($y, -2). '"' .(isset($_SESSION['payment']['exp_year']) && $_SESSION['payment']['exp_year'] == substr($y, -2) && $_SESSION['payment']['billing_id'] == '' ? ' selected' : ''). '>' .$y. '</option>';
				// 			}
				// 			$html .= '</select>
				// 			<label>CVV Code <strong class="color-red">*</strong></label>
				// 			<input type="text" name="cvv" class="input number half' .(in_array('cvv', $required) ? ' required' : ''). '" value="' .(isset($_POST['cvv']) ? $_POST['cvv'] : ''). '" maxlength="4" />
				// 		</div>';

				// 		if(USER_LOGGED_IN != 10213){ //Public account restricted
				// 			$html .= '<div class="clear">
				// 				<input type="checkbox" name="ccsave" id="ccsave" class="checkbox" value="1"' .(!isset($_SESSION['payment']['ccsave']) || (isset($_SESSION['payment']['ccsave']) && $_SESSION['payment']['ccsave'] == true) ? ' checked' : ''). ' />
				// 				<label for="ccsave"><small>Save this credit card to my billing profiles</small></label>
				// 			</div>';
				// 		}

				// 	$html .= '</div>


				// </fieldset>';

				///
				$html .= '<hr>';
				$html .= '<div class="form-grid no-border">
				<div class="form-field">
					<label for="ccname">Cardholder Name <strong class="color-red">*</strong></label>
					<input type="text" name="ccname" id="ccname" class="input' .(in_array('ccname', $required) ? ' required' : ''). '" value="' .(isset($_SESSION['payment']['ccname']) && $_SESSION['payment']['billing_id'] == '' ? $_SESSION['payment']['ccname'] : ''). '" />
				</div>
				<div class="form-field expiry-date-fields" style="display: grid; grid-template-columns: 1fr 1fr; column-gap: 10px;">
					<div>
						<label for="exp_month">Expiry Date <strong class="color-red">*</strong></label>
						<select name="exp_month" id="exp_month" class="select' .(in_array('exp_month', $required) ? ' required' : ''). '">
						<option value="">MM</option>';
						$selected_month = $_POST['exp_month'] ?? $profile['exp_month'] ?? '';
						foreach($months as $key=>$value){
							$key_padded = str_pad((string)$key, 2, '0', STR_PAD_LEFT);
							$selected = ($selected_month == $key_padded ? ' selected' : '');
							$html .= '<option value="' .$key_padded. '"' .$selected. '>' .$key_padded. '</option>';
						}
						$html .= '</select>
					</div>
					<div>
						<label for="exp_year" style="visibility: hidden;">Expiry Year</label>
						<select name="exp_year" id="exp_year" class="select' .(in_array('exp_year', $required) ? ' required' : ''). '">
						<option value="">YYYY</option>';
						$selected_year = $_POST['exp_year'] ?? $profile['exp_year'] ?? '';
						for($y=date('Y'); $y<=(date('Y')+20); $y++){
							$year_short = substr((string)$y, -2);
							$selected = ($selected_year == $year_short ? ' selected' : '');
							$html .= '<option value="' .$year_short. '"' .$selected. '>' .$y. '</option>';
						}
						$html .= '</select>
					</div>
				</div>
				<div class="form-field">
					<label for="ccnumber">Card Number <strong class="color-red">*</strong> &nbsp; ';
					foreach($payment_options as $payopt){
						$cctype = ($payopt['type'] == 'MC' ? 'mastercard' : strtolower($payopt['type']));
						$html .= '<i class="fa fa-cc-' .$cctype. ' fa-lg" title="' .$payopt['name']. '"></i> ';
					}
					$html .= '</label>';
					// $html .= '<input type="text" name="ccnumber" id="ccnumber" class="input number' .(in_array('ccnumber', $required) ? ' required' : ''). '" value="' .(isset($_POST['ccnumber']) ? htmlspecialchars($_POST['ccnumber'], ENT_QUOTES, 'UTF-8') : (isset($profile['ccnumber']) ? '**** **** **** '.$profile['ccnumber'] : '')). '" maxlength="16" />';

					$html .= '<input type="text" name="ccnumber" id="ccnumber" class="input number' .(in_array('ccnumber', $required) ? ' required' : ''). '" value="' .(isset($_POST['ccnumber']) && $_SESSION['payment']['billing_id'] == '' ? $_POST['ccnumber'] : ''). '" maxlength="16" />';

				$html .= '</div>';
				$html .= '<div class="form-field">
					<label for="cvv">CVV Code <strong class="color-red">*</strong></label>
					<input type="text" name="cvv" id="cvv" class="input number half' .(in_array('cvv', $required) ? ' required' : ''). '" value="' .(isset($_POST['cvv']) ? htmlspecialchars($_POST['cvv'], ENT_QUOTES, 'UTF-8') : ''). '" maxlength="4" />
				</div>';

			// <div class="form-field">
			// 	<label for="bill_postalcode_2">Postal Code</label>
			// 	<input type="text" name="bill_postalcode_2" id="bill_postalcode_2" class="input" value="' .htmlspecialchars($_POST['bill_postalcode_2'] ?? $profile['bill_postalcode'] ?? $Account->postal_code ?? '', ENT_QUOTES, 'UTF-8'). '" />
			// </div>
			$html .= '</div>';

						if(USER_LOGGED_IN != 10213){ //Public account restricted
							$html .= '<div class="clear ccsave-container">
								<input type="checkbox" name="ccsave" id="ccsave" class="checkbox" value="1"' .(!isset($_SESSION['payment']['ccsave']) || (isset($_SESSION['payment']['ccsave']) && $_SESSION['payment']['ccsave'] == true) ? ' checked' : ''). ' />
								<label for="ccsave"><small>Save this credit card to my billing profiles</small></label>
							</div>';
						}
				///
				// $html .= '</fieldset>';
				// $html .= '</fieldset>';
				// $html .= '</fieldset>';
				// $html .= '</fieldset>';
				// $html .= '</fieldset>';
				$html .= '</div>';
				$html .= '</fieldset>';
				// $html .= '</div>';
				// $html .= '</div>';
				// $html .= '</div>';
				// $html .= '</div>';
				// $html .= '</div><div class="clear">';

				//Terms of use
				if(!empty($termsofuse)){
					$html .= '<div class="form-grid top-margin terms-conditions"><h4>Terms &amp; Conditions</h4>
					<div class="clearfix">
						<span class="' .(in_array('terms', $required) ? 'color-red' : ''). '">' .$_sitepages['terms']['page_title']. '</span> <strong class="color-red">*</strong><br />
						<input type="checkbox" class="checkbox" name="terms" id="terms" value="1"' .(isset($_SESSION['payment']['terms']) && $_SESSION['payment']['terms'] == true ? ' checked' : ''). ' />
						<label for="terms"><small class="terms">I agree/consent to the conditions outlined below:</small></label>
						<div class="terms-box nomargin">' .nl2br($termsofuse). '</div>
					</div>
					</div>';
				}

				$html .= '<div class="form-buttons">
					<button type="submit" name="continue" class="button solid f_right button primary red" value="1">Continue<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>';
					if(!is_null(REG_ID)){
						$html .= '<a href="' .$sitemap[60]['page_url']. '?action=edit&id=' .REG_ID. '" class="previous f_right button primary black">Cancel<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>';
					}else{
						$html .= '<a href="' .$sitemap[76]['page_url']. '?action=edit&id=' .INVOICE_ID. '" class="previous f_right button primary black">Cancel<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>';
					}
				$html .= '</div>';

			}

			$html .= '<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
		</form>';


	//Confirmation
	}else{

		$page['page_panels'][$panel_id]['title'] = 'Review &amp; Submit';
		$page['page_panels'][$panel_id]['show_title'] = true;

		//Checkout information
		$html = '<div class="review-payment-container">';
		$html .= '<table cellpadding="0" cellspacing="0" border="0" class="nobgs noborder" width="100%">
			<tr>
				<td valign="top">
					<h4>Personal Information</h4>'.
					$_SESSION['payment']['first_name'].' '.$_SESSION['payment']['last_name']. '<br />'.
					$_SESSION['payment']['email']. '<br />'.
					$_SESSION['payment']['phone']. '<br />
				</td>
				<td valign="top">
					<h4>Billing Information</h4>'.
					($_SESSION['payment']['bill_address2'] != '' ? $_SESSION['payment']['bill_address2'].' - ' : '').$_SESSION['payment']['bill_address1']. '<br />'.
					$_SESSION['payment']['bill_city']. ', ' .$_SESSION['payment']['bill_province']. ', ' .$_SESSION['payment']['bill_country']. '<br />'.
					$_SESSION['payment']['bill_postalcode']. '<br />
				</td>
				<td valign="top">
					<h4>Payment Information</h4>'.
					$_SESSION['payment']['ccname']. '<br />'.
					$_SESSION['payment']['cctype']. ' **** **** **** ' .substr($ccnumber, -4, 4). '<br />
					Expiry: ' .$_SESSION['payment']['exp_month'].'/'.$_SESSION['payment']['exp_year'].'<br />
				</td>
			</tr>
		</table>';

		//Checkout cart
		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive nomargin">
			<tr class="header-row" style="background: #eee;">
				<th class="left">' .(!is_null(REG_ID) ? 'Registration' : 'Invoice'). ' No.</th>
				<th class="right">Amount</th>
			</tr>
			<tr>
				<td>' .(!is_null(REG_ID) ? $record['registration_number'] : $record['invoice_number']). '</td>
				<td class="right">$' .number_format($record['payment_amount'], 2). '</td>
			</tr>
		</table>';
		if($admin_fee > 0){
			$html .= '<table class="noresponsive" width="100%" cellspacing="0" cellpadding="10" border="0">
				<tr>
					<td align="right">Subtotal:</td>
					<td class="right" width="120px">$' .number_format($record['payment_amount'], 2). '</td>
				</tr>
				<tr>
					<td align="right">Service Fee:</td>
					<td class="right">$' .number_format($admin_fee, 2). '</td>
				</tr>
				<tr>
					<td align="right"><h6>Total:</h6></td>
					<td class="right"><h6>$' .number_format($record['payment_amount']+$admin_fee, 2). '</h6></td>
				</tr>
			</table>';
		}

		//Process payment
		$html .= '<form name="confirm-form" action="" method="post">
			<div class="form-buttons">
				<button type="submit" name="process" class="button solid f_right primary red" value="1">Submit Payment<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
				<a onclick="document.editform.submit();" class="previous f_right button primary black">Go Back<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
			</div>
			<input type="hidden" name="ccnumber" value="' .$ccnumber. '" />
			<input type="hidden" name="cvv" value="' .$cvv. '" />
			<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
		</form>';

		//Edit information
		$html .= '<form name="editform" id="editform" action="" method="post">
			<input type="hidden" name="ccnumber" value="' .$ccnumber. '" />
			<input type="hidden" name="cvv" value="' .$cvv. '" />
			<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
		</form>';
		$html .= '</div>';
	}

}

//Set panel content
// $page['page_panels'][$panel_id]['content'] .= $html;

?>