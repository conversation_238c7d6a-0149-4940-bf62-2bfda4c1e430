<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

$row  = !isset($_POST['save']) ? ($row ?? []) : $_POST;

//Important
echo $CMSBuilder->important('<strong>' .$record_name. ' Deletion:</strong> If you delete a ' .strtolower($record_name). ', all fields within the ' .strtolower($record_name). ' will also be deleted. This action is NOT undoable.');

//Display form	
echo '<form action="" method="post" enctype="multipart/form-data">';

//Fieldset details
echo '<div class="panel">
	<div class="panel-header">'.$record_name.' Details
		<span class="panel-toggle fas fa-chevron-up"></span>
	</div>
	<div class="panel-content">
		<div class="flex-container">
			<div class="form-field">
				<label>Fieldset Name'.(in_array('label', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
				<input type="text" name="label" value="'.($row['label'] ?? '').'" class="input'.(in_array('label', $required) ? ' required' : '').'" />
			</div>
			<div class="form-field">
				<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
				<select name="ordering" class="select">
					<option value="101">Default</option>';
					for($i=1; $i<101; $i++){
						echo '<option value="' .$i. '"' .(($row['ordering'] ?? '') == $i ? ' selected' : ''). '>' .$i. '</option>';
					}
				echo '</select>
			</div>
		</div>
	</div>
	<div class="panel-content nopadding">
		<textarea name="description" class="textarea tinymceMini">'.($row['description'] ?? '').'</textarea>
	</div>
</div>';

//Sticky footer
echo '<footer id="cms-footer">
	<div class="flex-container">
		<div class="flex-column right">
			<button type="submit" class="button" name="save" value="save"><i class="fas fa-check"></i>Save Changes</button>
		</div>
		<div class="flex-column left">';
			if(ITEM_ID != ""){
				echo '<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn"' .(!($row['deletable'] ?? true) ? ' disabled' : ''). '><i class="fas fa-trash-alt"></i>Delete</button>';
			}
			echo '<a href="' .$formpage['page_url']. '?action=edit&item_id=' .FORM_ID. '" class="cancel">Cancel</a>
		</div>
	</div>
</footer>';

echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
</form>';

?>