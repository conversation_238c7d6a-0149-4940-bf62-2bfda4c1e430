<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['send_message']){
		
	//Define vars
	$record_db = 'messages';
	$record_id = 'message_id';

	$errors = false;
	$required = array();
	
	$messagespage = $CMSBuilder->get_section($_cmssections['send_message']);
	$messagecentre = get_page_url($_sitepages['message-centre']['page_id']);
			
	//Get available messages to send out
	$messages = array();
	$query = $db->query("SELECT * FROM `$record_db` WHERE `date_sent` IS NULL ORDER BY `$record_id`");
	if($query && !$db->error() && $db->num_rows() > 0) {
		$result = $db->fetch_array();
		foreach($result as $row){
			$messages[$row[$record_id]] = $row;
		}
	}
	
	//Get current tournaments
	$tournaments = array();
	$params = array('Active', date('Y').'-01-01');
	$query = $db->query("SELECT `reg_events`.`event_id`, `reg_events`.`name`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date` FROM `reg_events` LEFT JOIN `reg_occurrences` ON `reg_events`.`event_id` = `reg_occurrences`.`event_id` WHERE `reg_events`.`event_type` = 2 && `reg_events`.`status` = ? && `reg_occurrences`.`start_date` >= ? ORDER BY `reg_events`.`name`", $params);
	if($query && !$db->error()){
		$tournaments = $db->fetch_array();
	}
		
	//Get active members
	$members = array();
	$query = $db->query("SELECT `account_profiles`.`account_id`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, IFNULL(`facilities`.`facility_name`, ?) AS `facility_name` FROM `account_profiles` ".
	"INNER JOIN `account_permissions` ON `account_profiles`.`account_id` = `account_permissions`.`account_id` && `account_permissions`.`role_id` = 2 ".
	"LEFT JOIN `accounts` ON `account_profiles`.`account_id` = `accounts`.`account_id` ".
	"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
	"WHERE `accounts`.`status` = ? ".
	"ORDER BY `account_profiles`.`last_name`, `account_profiles`.`first_name`", array('No Facility', 'Active'));
	if($query && !$db->error()){
		$members = $db->fetch_array();
	}
		
	//Get member classes
	$classes = array();
	$query = $db->query("SELECT `class_id`, `class_name` FROM `membership_classes` ORDER BY `class_name` ASC");
	if($query && !$db->error()){
		$classes = $db->fetch_array();
	}
	
	//Audiences
	$audiences = array();
	$audiences['all'] = array('label' => 'All Members', 'recipients' => array(), 'accounts' => array());
	$audiences['classes'] = array('label' => 'Selected Member Types', 'recipients' => array(), 'accounts' => array());
	$audiences['tournaments'] = array('label' => 'Selected Tournaments', 'recipients' => array(), 'accounts' => array());
	$audiences['members'] = array('label' => 'Selected Members', 'recipients' => array(), 'accounts' => array());	
	
	//Send message
	if(isset($_POST['send'])){
		
		//Get recipients (ensure they are subscribed to email messages)
		if($_POST['audience'] != 'test'){
			
			$params = array(2, 'Active');
			if($_POST['audience'] == 'classes'){
				$params[] = implode(',', $_POST['classes']);
			}
			
			if($_POST['audience'] == 'tournaments'){
				$params[] = implode(',', $_POST['events']);
				$params[] = 'Registered';
			}
			
			if($_POST['audience'] == 'members'){
				$params[] = implode(',', $_POST['accounts']);
			}
			
			$query = $db->query("SELECT `account_profiles`.`first_name`, `account_profiles`.`last_name`, `account_profiles`.`mc_subscribe`, `accounts`.`email`, `accounts`.`account_id` ".
			"FROM `account_profiles` ".
			"LEFT JOIN `accounts` ON `accounts`.`account_id` = `account_profiles`.`account_id` ".
			"LEFT JOIN `account_permissions` ON `accounts`.`account_id` = `account_permissions`.`account_id` ".
			"LEFT JOIN `membership_classes` ON `account_profiles`.`class_id` = `membership_classes`.`class_id` ".
			"LEFT JOIN `reg_attendees` ON `accounts`.`account_id` = `reg_attendees`.`account_id` ".
			"WHERE `account_permissions`.`role_id` = ? && `accounts`.`status` = ?".
			($_POST['audience'] == 'classes' ? " && FIND_IN_SET(`account_profiles`.`class_id`, ?)" : ""). 
			($_POST['audience'] == 'tournaments' ? " && FIND_IN_SET(`reg_attendees`.`event_id`, ?) && `reg_attendees`.`reg_status` = ?" : ""). 
			($_POST['audience'] == 'members' ? " && FIND_IN_SET(`accounts`.`account_id`, ?)" : ""), 
			$params);
			
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $row){
					
					//Make sure email is valid
					if(checkmail($row['email'])){
						if($row['mc_subscribe'] && !empty($row['account_id'])){
							$audiences[$_POST['audience']]['recipients'][$row['email']] = array(
								'email'=>$row['email'], 
								'name'=>$row['first_name'].' '.$row['last_name'],
								'substitutions'=>array('%email%'=>$row['email'])
							);
						}
						$audiences[$_POST['audience']]['accounts'][$row['account_id']] = $row['email'];
					}
				}
			}
		}
				
		//Get message
		if($_POST[$record_id] != ''){
			if(array_key_exists($_POST[$record_id], $messages)){
				$message = $messages[$_POST[$record_id]];
			}else{
				$errors[] = 'Invalid message selected. Please try again.';
			}
		}else{
			$errors[] = 'Please select a message to send.';
		}
		
		if($_POST['audience'] == 'test' && trim($_POST['test_emails']) == ""){
			$errors[] = 'Please enter at least one test email to send to.';
		}
		
		if($_POST['audience'] != 'test' && empty($audiences[$_POST['audience']]['accounts'])){
			$errors[] = 'No recipients found for selected audience.';
		} 

		if(!$errors){
			
			//Format attachments
			$files = array();
			if(!empty($message['filename']) && file_exists('../docs/attachments/'.$message['filename'])){
				$files[] = 'docs/attachments/'.$message['filename'];
			}
			
			//Format message
			$message['content'] = '<div style="padding:30px;"><h3>'.$message['subject'].'</h3>'.$message['content'].'</div>';
						
			//Send as test
			if($_POST['audience'] == 'test'){
				
				if(send_email($_POST['test_emails'], $message['subject'], $message['content'], $files, 'includes/emailtemplates/newsletter.htm')){
					$CMSBuilder->set_system_alert('<em>'.$message['subject'].'</em> was successfully sent as a test to '.$_POST['test_emails'].'.<br/><strong>Note:</strong> Email statistics are not affected when sending test messages.', true);
				}else{
					$CMSBuilder->set_system_alert('Unable to send test message. Please try again.', false);
				}
			

			//Send to recipients
			}else if($_POST['audience'] != 'test'){
								
				//Set message content
				$email_message = format_email($message['content'], 'includes/emailtemplates/newsletter.htm');
				$email_message = str_replace('-unsubscribe-', $siteurl.$root."unsubscribe/?uc=".$message['unsubscribe_code']."&mc=1&e=%email%", $email_message);
				
				//Format attachments
				$attachments = array();
				if(!empty($files)){
					foreach($files as $filepath){
						$filebits = explode('/', $filepath);
						$filename = $filebits[count($filebits)-1];
						$filetype = pathinfo($file, PATHINFO_EXTENSION);
						$attachments[] = array('name'=>$filename, 'file'=>$filepath, 'mime_type'=>'application/'.$filetype);
					}
				}
			
				//Format recipients
				$recipients = $audiences[$_POST['audience']]['recipients'];
				
				//Send to admin also
				$recipients[$global['contact_email']] = array('email'=>$global['contact_email'], 'name'=>$global['company_name'], 'substitutions'=>array('%email%'=>''));
				
				//Set template
				$sendgrid->set_template(false);

				//Send email
				try{
					
					$sendgrid->sendit($recipients, $message['subject'], $email_message, 'Message Centre', array('item_id'=>'mc'.$message[$record_id]), $attachments);
					$CMSBuilder->set_system_alert('<em>'.$message['subject'].'</em> was successfully sent!<br/>Please allow up to 30 seconds for statistics to appear.', true);
					
					$db->new_transaction();
					
					//Insert recipients
					foreach($audiences[$_POST['audience']]['accounts'] as $account_id=>$account_email){
						if(!empty($account_id)){
							$params = array($message[$record_id], $account_id, $account_email);
							$insert = $db->query("INSERT INTO `message_recipients`(`$record_id`, `account_id`, `email`) VALUES(?,?,?)", $params);
						}
					}
					
					//Update message as sent
					$params = array(date("Y-m-d H:i:s"), $message[$record_id]);
					$update = $db->query("UPDATE `$record_db` SET `date_sent` = ? WHERE `$record_id` = ?", $params);
					
					if(!$db->error()){
						$db->commit();
					}else{
						$CMSBuilder->set_system_alert('Error inserting to message centre: '.$db->error(), false);
					}
					
					header("Location: " .$messagespage['page_url']);
					exit();
					
				}catch(Exception $e){
					$errors[] = 'There was an error sending this message: '.$e->getMessage();
				}
				
			}
		}

		if(!empty($errors) && is_array($errors)) {
			$row = array();
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}
	}
}

?>