<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<a href="' .PAGE_URL. '?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">'.$record_name.'s
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
				
				<thead>
					<th width="350px">'.$record_name.' Name</th>
					<th>Send to Email</th>
					<th width="1px" class="center">Visible</th>
					<th class="right" data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				foreach($records_arr as $row){
					echo '<tr data-table="' .$record_db. '" data-column-name="' .$record_id. '" data-name="' .$row['form_name']. '" data-id="' .$row[$record_id]. '">
						<td>' .$row['form_name']. '</td>
						<td>' .$row['send_to']. '</td>
						<td>'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td class="right">
							<a href="' .$submissionspage['page_url']. '?form_id=' .$row[$record_id]. '&advanced_search" class="button-sm"><i class="fas fa-eye"></i> View Submissions</a>
							<a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-edit"></i> Edit</a>
						</td>
					</tr>';
				}
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';

//Display form	
}else{
	
	$data = $records_arr[ITEM_ID] ?? [];
	$row  = !isset($_POST['save']) ? $data : $_POST;

	echo '<form action="" method="post" enctype="multipart/form-data">';

	//Form details
	echo '<div class="panel">
		<div class="panel-header">' .$record_name. '
			<span class="panel-toggle fas fa-chevron-up"></span>
			<div class="panel-switch">
				<label>Show Question</label>
				<div class="onoffswitch">
					<input type="checkbox" name="showhide" id="showhide" value="0"' .(($row['showhide'] ?? 0) ? '' : ' checked'). ' />
					<label for="showhide">
						<span class="inner"></span>
						<span class="switch"></span>
					</label>
				</div>
			</div>
		</div>
		<div class="panel-content">
			<div class="flex-container">
				<div class="form-field">
					<label>Form Name'.(in_array('form_name', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="form_name" value="'.($row['form_name'] ?? '').'" class="input'.(in_array('form_name', $required) ? ' required' : '').'" />
				</div>
				<div class="form-field">
					<label>Login Required ' .$CMSBuilder->tooltip('Login Required', 'Select &quot;Yes&quot; if users must login before submitting this form.'). '</label>
					<select name="login" class="select' .(in_array('login', $required) ? ' required' : ''). '">
						<option value="0"' .(isset($row['login']) && $row['login'] == '0' ? ' selected' : ''). '>No</option>
						<option value="1"' .(isset($row['login']) && $row['login'] == '1' ? ' selected' : ''). '>Yes</option>
					</select>
				</div>
				<div class="form-field">
					<label>Send to Email'.(in_array('send_to', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="send_to" value="'.($row['send_to'] ?? '').'" class="input'.(in_array('send_to', $required) ? ' required' : '').'" />

					<p><input type="checkbox" name="send_copy" id="send_copy" value="1" class="checkbox"' .(isset($row['send_copy']) && $row['send_copy'] ? ' checked' : ''). ' />
					<label for="send_copy"><small>Also send a copy to user (must be logged in)</small></label></p>
				</div>
			</div>
		</div>
	</div>';

	//Form fields
	echo '<div class="panel">
		<div class="panel-header">Form Fields
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table width="100%" cellspacing="0" cellpadding="0" border="0" class="sortable">
				<thead>
					<th width="1px" class="nopadding-r"></th>
					<th>Field Name</th>
					<th width="200px">Field Type</th>
					<th width="1px" class="center">Required</th>
					<th width="1px"></th>
				</thead>

				<tbody>';
					$fieldsets = 0;
					if(!empty($row['form_fields'])){
						foreach($row['form_fields'] as $fieldset_id=>$fieldset){
							$fieldsets++;
							if($fieldset_id > 0){
								echo '<tr data-level="1" data-table="form_fields" data-column-name="field_id" data-system-page="true" data-name="'.$fieldset['label'].'" data-id="'.$fieldset['field_id'].'">
									<td class="handle nopadding-r"><span class="fas fa-arrows-alt"></span></td>
									<td colspan="3" class="uppercase semibold">'.$fieldset['label'].'</td>
									<td class="right"><a href="'.$fieldsetpage['page_url'].'?form_id='.ITEM_ID.'&action=edit&item_id='.$fieldset['field_id'].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
								</tr>';
							}

							$fields = 0;
							if(isset($fieldset['form_fields']) && is_array($fieldset['form_fields'])){
								foreach($fieldset['form_fields'] as $field){
									$fields++;

									$classes = [];

									if($fields === 1){
										$classes[] = 'first-child';
									}
									if($fieldsets < count($row['form_fields']) && $fields == count($fieldset['form_fields'])){
										$classes[] = 'last-child';
									}

									echo '<tr data-level="'.($field['type'] != 'fieldset' ? 2 : 1).'" data-table="form_fields" data-column-name="field_id" data-system-page="true" data-name="'.$field['label'].'" data-id="'.$field['field_id'].'" class="'.implode(' ', $classes).'">
										<td class="handle nopadding-r"><span class="fas fa-arrows-alt"></span></td>
										<td>'.$field['label'].'</td>
										<td class="capitalize">'.$field['type'].'</td>
										<td class="center">'.($field['required'] ? '<span class="required">*</span>' : '').'</td>
										<td class="right"><a href="'.$fieldpage['page_url'].'?form_id='.ITEM_ID.'&action=edit&item_id='.$field['field_id'].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
									</tr>';
								}
							}
						}
					}else{
						echo '<tr><td colspan="5">No form fields have been created yet.</td></tr>';
					}
				echo '</tbody>
			</table>

			<div class="pager">
				<div class="right">';
					if(!ITEM_ID){
						echo '<button type="button" name="save" value="fieldset" class="button submit-button"><i class="fas fa-plus"></i>Add Fieldset</button>
						<button type="button" name="save" value="field" class="button submit-button"><i class="fas fa-plus"></i>Add Field</button>';
					}else{
						echo '<a href="'.$fieldsetpage['page_url'].'?form_id='.ITEM_ID.'&action=add" class="button"><i class="fas fa-plus"></i>Add Fieldset</a>
						<a href="'.$fieldpage['page_url'].'?form_id='.ITEM_ID.'&action=add" class="button"><i class="fas fa-plus"></i>Add Field</a>';
					}
				echo '</div>
			</div>
		</div>
	</div>';

	//Sticky footer
	include("includes/widgets/formbuttons.php");
	
	echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';
	
}

?>