<?php  


//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

error_reporting(0);
ini_set('display_errors', 'off');


	//Define vars
	$displayform = true;
	$approved = false;
	$errors = false;
	$required = [];
	// $required_fields = array('first_name', 'last_name', 'username', 'phone', 'email', 'confirm_email', 'password', 'confirm_password');
	$required_fields = array('first_name', 'last_name', 'username', 'group_id', 'email', 'confirm_email', 'password', 'confirm_password','company');

	$roles = array(2);// Frontend user role
	$response  = [
		'errors'         => false,
		'error_fields'   => [],
		'msg_validation' => ''
	];
	
	//Create account
	if(isset($_POST['register'])){
		// print_r($_POST);
		// exit;
		//Cookie validation
		if(!isset($_POST['xid']) || trim($_POST['xid']) == '' || $_POST['xid'] != $_COOKIE['xid']){
			$response['errors'][] = 'Invalid session. Please make sure cookies are enabled in your browser then try again.';
			unset($_POST);
			
		}else{
		
			//Validation
			foreach($required_fields as $field){
				if(!isset($_POST[$field]) || trim($_POST[$field]) == ''){
					$errors[0] = 'Please fill in all required fields.';
					array_push($required, $field);
				}
			}
			if(!in_array('email', $required) && !checkmail($_POST['email'])){
				$response['errors'][] = 'Please enter a valid email address.';
				$errors[] = 'Please enter a valid email address.';
				array_push($required, $field);
				array_push($required, 'email');
			}
			if(!in_array('email', $required) && $_POST['email'] != $_POST['confirm_email']){
				$response['errors'][] = 'Email addresses do not match.';
				$errors[] = 'Email addresses do not match.';
				array_push($required, 'email');
				array_push($required, 'confirm_email');
			}
			if(!in_array('password', $required) && $_POST['password'] != $_POST['confirm_password']){
				$response['errors'][] = 'Passwords do not match.';
				$errors[] = 'Passwords do not match.';
				array_push($required, 'password');
				array_push($required, 'confirm_password');
			}

			// Recaptcha
			if(validate_recaptcha()) {
				$response['errors'][] = 'Please verify you are not a robot.';
				$errors[] = 'Please verify you are not a robot.';
			}
			
		}

		if(!$errors){

			//
			//Account groups
			$account_groups = array();
			$query = $db->query("SELECT * FROM `account_groups` ORDER BY `group_name` ASC");
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $row){
					$account_groups[$row['group_id']] = $row;
				}
			}

			$group_id = NULL;
			$request = $_POST['group_id'];
			
			//Specific account group
			if(array_key_exists($_POST['group_id'], $account_groups)){
				$group_id = $_POST['group_id'];
				$request = $account_groups[$_POST['group_id']]['group_name'];
				
				// //Special role assigned to group
				// if(!empty($account_groups[$_POST['group_id']]['role_id'])){
				// 	$roles[] = $account_groups[$_POST['group_id']]['role_id'];
					
				// 	//Auto-approve hio accounts
				// 	/*if($account_groups[$_POST['group_id']]['role_id'] == 9){
				// 		$approved = true;
				// 	}*/
				// }
			}
			//

			//Set params
			$params[] = array(
				'param' => 'email', 
				'label' => 'Email Address', 
				'value' => ($_POST['email'] ?? ''), 
				'required' => (in_array('email', $required_fields)), 
				'unique' => true, 
				'validate' => 'email', 
				'hash' => false
			);
			$params[] = array(
				'param' => 'username', 
				'label' => 'Username', 
				'value' => ($_POST['username'] ?? ''), 
				'required' => (in_array('username', $required_fields)), 
				'unique' => true, 
				'validate' => false, 
				'hash' => false
			);
			$params[] = array(
				'param' => 'password', 
				'value' => ($_POST['password'] ?? ''), 
				'label' => 'Password', 
				'required' => (in_array('password', $required_fields)), 
				'unique' => false, 
				'validate' => 'password', 
				'hash' => true
			);
			$params[] =  array(
				'param' => 'first_name', 
				'label' => 'First Name', 
				'value' => ($_POST['first_name'] ?? ''), 
				'required' => (in_array('first_name', $required_fields)), 
				'unique' => false, 
				'validate' => false, 
				'hash' => false
			);
			$params[] =  array(
				'param' => 'last_name', 
				'label' => 'Last Name', 
				'value' => ($_POST['last_name'] ?? ''), 
				'required' => (in_array('last_name', $required_fields)), 
				'unique' => false, 
				'validate' => false, 
				'hash' => false
			);
			$params[] = array(
				'param' => 'company', 
				'label' => 'Company', 
				'value' => ($_POST['company'] ?? ''), 
				'required' => (in_array('company', $required_fields)), 
				'unique' => false, 
				'validate' => false, 
				'hash' => false
			);
			$params[] = array(
				'param' => 'group_id', 
				'label' => 'Reason for Request', 
				'value' => $group_id, 
				// 'required' => (in_array('group_id', $required_fields)), 
				'required' => false, 
				'unique' => false, 
				'validate' => false, 
				'hash' => false
			);
			$params[] = array(
				'param' => 'notes', 
				'label' => 'Additional Information', 
				'value' => $_POST['notes'], 
				'required' => (in_array('notes', $required_fields)), 
				'unique' => false, 
				'validate' => false, 
				'hash' => false
			);
			// $params[] = array(
			// 	'param' => 'phone', 
			// 	'label' => 'Phone Number', 
			// 	'value' => format_phone_number(($_POST['phone'] ?? '')), 
			// 	'required' => (in_array('phone', $required_fields)), 
			// 	'unique' => false, 
			// 	'validate' => 'phone', 
			// 	'hash' => false
			// );
			// $params[] = array(
			// 	'param' => 'address1', 
			// 	'label' => 'Street Address', 
			// 	'value' => ($_POST['address1'] ?? ''), 
			// 	'required' => (in_array('address1', $required_fields)), 
			// 	'unique' => false, 
			// 	'validate' => false, 
			// 	'hash' => false
			// );
			// $params[] = array(
			// 	'param' => 'address2', 
			// 	'label' => 'Address Line 2', 
			// 	'value' => ($_POST['address2'] ?? ''), 
			// 	'required' => (in_array('address2', $required_fields)), 
			// 	'unique' => false, 
			// 	'validate' => false, 
			// 	'hash' => false
			// );
			// $params[] = array(
			// 	'param' => 'city', 
			// 	'label' => 'City', 
			// 	'value' => ($_POST['city'] ?? ''), 
			// 	'required' => (in_array('city', $required_fields)), 
			// 	'unique' => false, 
			// 	'validate' => false, 
			// 	'hash' => false
			// );
			// $params[] = array(
			// 	'param' => 'province', 
			// 	'label' => 'Province/State', 
			// 	'value' => ($_POST['province'] ?? ''), 
			// 	'required' => (in_array('province', $required_fields)), 
			// 	'unique' => false, 
			// 	'validate' => false, 
			// 	'hash' => false
			// );
			// $params[] = array(
			// 	'param' => 'postalcode', 
			// 	'label' => 'Postal/Zip Code', 
			// 	'value' => ($_POST['postalcode'] ?? ''), 
			// 	'required' => (in_array('postalcode', $required_fields)), 
			// 	'unique' => false, 
			// 	'validate' => false, 
			// 	'hash' => false
			// );
			// $params[] = array(
			// 	'param' => 'country', 
			// 	'label' => 'Country', 
			// 	'value' => ($_POST['country'] ?? ''), 
			// 	'required' => (in_array('country', $required_fields)), 
			// 	'unique' => false, 
			// 	'validate' => false, 
			// 	'hash' => false
			// );
			$params[] = array(
				'param' => 'status', 
				'label' => 'Status', 
				'value' => ($approved ? 'Active' : 'Requested'), //Note: if confirmation email is enabled, status will be forced to Pending
				// 'value' => 'Active',
				'required' => true, 
				'unique' => false, 
				'validate' => false, 
				'hash' => false
			);
		
			$roles =[2]; // Frontend user role
			$account_id = null; 
			try{
				$account_id = $Account->register($params, $roles, ($approved ? true : false), false, false);
				$displayform = false;
				// Display error if both operations have failed
				// $response['msg_validation'] = 'Your account has been successfully created and is awaiting activation. A confirmation email has been sent to your inbox with instructions on how to activate your new account. <small>(Be sure to check your junk mail folder!)';
				
				$response['msg_validation'] = 'Your account has been successfully created and is awaiting activation';
					
				//If admin approval is required
				if(!$approved){

					//Notify admin
					$emailbody = '<h3>Account Request</h3>
					<p>
						Name: '.$_POST['first_name'].' '.$_POST['last_name'].'<br/>
						Email: <a href="mailto:'.$_POST['email'].'">'.$_POST['email'].'</a><br />
						Phone: '.$_POST['phone'].'
					</p>
					<p>Please login to your <a href="'.$siteurl.$cmspath.'">Content Management System</a> to review this request.</p>';
					$mail_sent = send_email($global['contact_email'], 'Account Request', $emailbody);	
				}

			}catch(Exception $e){
				$alert = $Account->alert($e->getMessage(), false);

				if(!is_array($response['errors'])) {
					$response['errors'] = [];
				}

				// echo "Error - ".$e->getMessage();
				// $errors[] = $e->getMessage();
				$response['errors'][] = $e->getMessage();
				// $response['errors'] = true;
				$response['error_fields'] = $required;
				// $response['errors'][] = 'An error occurred and your submission could not be processed. Please refresh the page and try again. If the issue persists, email us at <a href="mailto:'.$global['contact_email'].'">'.$global['contact_email'].'</a>.';
				$response['msg_validation'] = $e->getMessage();
			}

			//Update account roles
			if($account_id) {
				// Proceed with the role update
				try {
					$Account->update_account_roles($roles, $account_id);
				} catch(Exception $e) {
					$response['errors'][] = 'Unable to update account roles. ' . $e->getMessage();
				}
			} else {
				// $response['errors'][] = 'Account creation might have failed.';
				// $response['msg_validation'] = 'Account creation might have failed.!';

				if(!is_array($response['errors'])) {
					$response['errors'] = [];
				}
				$response['errors'][] = 'Account creation might have failed.';
				
				// Combine all error messages for display
				$response['msg_validation'] = implode("<br>", $response['errors']);
			}
			

		}else{
			$alert = $Account->alert(implode("<br />", $errors), false);
			$response['errors'] = true;
            $response['error_fields'] = $required;
            $response['msg_validation'] = implode("<br />", $errors);
		}
	}
	echo json_encode($response);

?>