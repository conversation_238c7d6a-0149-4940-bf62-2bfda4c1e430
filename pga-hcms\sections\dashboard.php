<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Include charts
include("includes/widgets/dashboardcharts.php");

?>

<div id="dashboard-overview">

	<?php
	$widgets = $CMSBuilder->get_widgets();
	if(!empty($widgets)){
	?>	
	<div class="cms-overview panel flex-column">
		<div class="panel-header">CMS Overview</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0">
			<?php
			foreach($widgets as $widget){
				echo '<tr>
					<td height="30px"><a href="' .$widget['url']. '"><i class="' .$widget['icon']. ' color-theme1"></i> &nbsp; ' .$widget['title']. '</a></td>
					<td align="center">' .$widget['value']. '</td>
				</tr>';
			}
			?>
			</table>
		</div>
	</div>
	<?php } ?>

	<?php include("includes/widgets/dashboardseo.php"); ?>
	<?php include("includes/widgets/dashboardleadins.php"); ?>
	
</div>