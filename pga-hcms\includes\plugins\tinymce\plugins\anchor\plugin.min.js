!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("3",tinymce.util.Tools.resolve),g("1",["3"],function(a){return a("tinymce.Env")}),g("2",["3"],function(a){return a("tinymce.PluginManager")}),g("0",["1","2"],function(a,b){return b.add("anchor",function(b){var c=function(a){return!a.attr("href")&&(a.attr("id")||a.attr("name"))&&!a.firstChild},d=function(a){return function(b){for(var d=0;d<b.length;d++)c(b[d])&&b[d].attr("contenteditable",a)}},e=function(a){return/^[A-Za-z][A-Za-z0-9\-:._]*$/.test(a)},f=function(){var a=b.selection.getNode(),c="A"==a.tagName&&""===b.dom.getAttrib(a,"href"),d="";c&&(d=a.id||a.name||""),b.windowManager.open({title:"Anchor",body:{type:"textbox",name:"id",size:40,label:"Id",value:d},onsubmit:function(d){var f=d.data.id;return e(f)?void(c?(a.removeAttribute("name"),a.id=f):(b.selection.collapse(!0),b.execCommand("mceInsertContent",!1,b.dom.createHTML("a",{id:f})))):(d.preventDefault(),void b.windowManager.alert("Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores."))}})};a.ceFalse&&b.on("PreInit",function(){b.parser.addNodeFilter("a",d("false")),b.serializer.addNodeFilter("a",d(null))}),b.addCommand("mceAnchor",f),b.addButton("anchor",{icon:"anchor",tooltip:"Anchor",onclick:f,stateSelector:"a:not([href])"}),b.addMenuItem("anchor",{icon:"anchor",text:"Anchor",context:"insert",onclick:f})}),function(){}}),d("0")()}();