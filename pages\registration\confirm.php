<?php

//Pay now or later
if($_SESSION['reg']['checkout']['payment'] == false){
	if(EVENT_TYPE == 2){
		$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> Your credit card will not be charged until the payment deadline outlined for each individual tournament.');
	}else{
		if(!$has_admin_fee && $ordertotal > $reg_settings['max_payment_amount'] && !empty($reg_settings['max_payment_amount'])){
			$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> Online payment is unavailable for this registration. Please note that payment by cheque will be required to secure your spot.');
		}else{
			$html .= $Account->important('<i class="fa fa-exclamation-triangle"></i> Your credit card will not be charged at this time. Please submit your payment before ' .date('F j, Y', strtotime($event['payment_deadline'])). ' to secure your spot.');
		}
	}
}

//Checkout information
$html .= '<hr /><table cellpadding="0" cellspacing="0" border="0" class="nobgs nomargin noborder" width="100%">
	<tr>';
		
		//Contact
		$html .= '<td valign="top" width="' .(EVENT_TYPE == 2 ? '33%' : '25%'). '">
			<h6>' .(EVENT_TYPE == 2 ? 'Personal' : 'Contact'). ' Information</h6><p>'.
			$_SESSION['reg']['checkout']['first_name'].' '.$_SESSION['reg']['checkout']['last_name']. '<br />'.
			(trim($_SESSION['reg']['checkout']['company']) != '' ? $_SESSION['reg']['checkout']['company'].'<br />' : '').
			$_SESSION['reg']['checkout']['email']. '<br />'.
			$_SESSION['reg']['checkout']['phone']. '</p>';
		$html .= '</td>';

		//Mailing
		if($_SESSION['reg']['checkout']['address1'] != ''){
			$html .= '<td valign="top">
				<h6>Mailing Address</h6><p>'.
				($_SESSION['reg']['checkout']['address2'] != '' ? $_SESSION['reg']['checkout']['address2'].' - ' : '').$_SESSION['reg']['checkout']['address1']. '<br />'.
				$_SESSION['reg']['checkout']['city']. ', ' .$_SESSION['reg']['checkout']['province']. ', ' .$_SESSION['reg']['checkout']['country']. '<br />'.
				$_SESSION['reg']['checkout']['postal_code']. '</p>
			</td>';
		}

		//Billing
		if($_SESSION['reg']['checkout']['bill_address1'] != ''){
			$html .= '<td valign="top">
				<h6>Billing Information</h6><p>'.
				($_SESSION['reg']['checkout']['bill_address2'] != '' ? $_SESSION['reg']['checkout']['bill_address2'].' - ' : '').$_SESSION['reg']['checkout']['bill_address1']. '<br />'.
				$_SESSION['reg']['checkout']['bill_city']. ', ' .$_SESSION['reg']['checkout']['bill_province']. ', ' .$_SESSION['reg']['checkout']['bill_country']. '<br />'.
				$_SESSION['reg']['checkout']['bill_postalcode']. '</p>
			</td>';
		}

		//Payment
		if($ccnumber != ''){
			$html .= '<td valign="top">
				<h6>Payment Information</h6><p>'.
				$_SESSION['reg']['checkout']['ccname']. '<br />'.
				$_SESSION['reg']['checkout']['cctype']. ' **** **** **** ' .substr($ccnumber, -4, 4). '<br />
				Expiry: ' .$_SESSION['reg']['checkout']['exp_month'].'/'.$_SESSION['reg']['checkout']['exp_year'].'</p>
			</td>';
		}

	$html .= '</tr>
</table>';

//Checkout cart
$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive nomargin">
	<tr class="header-row">
		<th class="left">' .(EVENT_TYPE == 2 ? 'Tournament' : 'Event'). '</th>
		<th class="right">Total</th>
	</tr>';
	foreach($checkout_cart as $item_id=>$item){
		
		//Tournaments
		if($item['event_type'] == 2){
			$html .= '<tr>
				<td class="left">' .$item['event_name']. '<br />
					<small class="dblock">'. format_date_range($item['start_date'], $item['end_date']). '</small>';
					
					//If team partner was selected
					if($item['team_event'] && isset($item['attendees'][0]['partner'])){
						$html .= '<small class="dblock">Partner: '.$item['attendees'][0]['partner']['first_name'].' '.$item['attendees'][0]['partner']['last_name']. '</small>';
					}
			
				$html .= '</td>
				<td class="right">$' .number_format($item['subtotal'], 2). '</td>
			</tr>';
			
		//Events	
		}else{
			$html .= '<tr>
				<td class="left"><h6>' .$item['event_name']. '</h6>
					<small class="dblock">'. format_date_range($item['start_date'], $item['end_date']). '</small>';
				$html .= "</td>
			</tr>";
			
			//Event attendees
			foreach($item['attendees'] as $attendee){
				$attendee_subtotal = $attendee['ticket_price'];
				$html .= '<tr>
					<td>' .$attendee['attendee_fields']['first_name'].' '.$attendee['attendee_fields']['last_name']. '<br />
					<small class="dblock">' .$attendee['ticket_type']. ': $' .number_format($attendee['ticket_price'], 2). '</small>';
					foreach($attendee['attendee_fields'] as $field=>$value){
						if($field != 'first_name' && $field != 'last_name' && trim($value) != ''){
							$html .= '<small class="dblock">' .ucwords(str_replace('_', ' ', $field)).': ' .$value. '</small>';
						}
					}
					if(isset($attendee['addons']) && !empty($attendee['addons'])){
						foreach($attendee['addons'] as $addon){
							if($addon['value'] != ''){
								$html .= '<small class="dblock">' .$addon['name'].': ' .$addon['value']. 
								(!empty($addon['price_adjustment']) && $addon['price_adjustment'] > 0 ? ' - $'.number_format($addon['price_adjustment'], 2) : ''). '</small>';
							}
							$attendee_subtotal += $addon['price_adjustment'];
						}
					}
					$html .= '</td>
					<td class="right" valign="top">$' .number_format($attendee_subtotal, 2). '</td>
				</tr>';
			}
		}
	}
$html .= '</table>';

//Promo codes
if($discount > 0 && $promocode != ""){
	$html .= '<div class="promo-code">Discount Code: ' .$promocode. '</div>';
}

//Display totals
$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive totals">';
	if($discount > 0){
		$html .= '<tr>
			<td align="right">Discount:</td>
			<td class="right">$' .number_format($discount, 2). '</td>
		</tr>';
	}
	$html .= '<tr>
		<td align="right">Subtotal:</td>
		<td class="right" width="120px">$' .number_format($subtotal, 2). '</td>
	</tr>';
	$html .= '<tr>
		<td align="right">Taxes:</td>
		<td class="right">$' .number_format($taxes, 2). '</td>
	</tr>';
	if($fees > 0){
		$html .= '<tr>
			<td align="right">Skins:</td>
			<td class="right" width="120px">$' .number_format($fees, 2). '</td>
		</tr>';
	}
	if($admin_fee > 0){
		$html .= '<tr>
			<td align="right">Service Fee:</td>
			<td class="right">$' .number_format($admin_fee, 2). '</td>
		</tr>';
	}
	$html .= '<tr>
		<td align="right"><h6>Total:</h6></td>
		<td class="right"><h6>$' .number_format($ordertotal+$admin_fee, 2). '</h6></td>
	</tr>
</table>';

//Process registration
$html .= '<form name="confirm-form" class="hidden-recaptcha" action="" method="post" data-recaptcha="#recaptcha-confirm">

	<div class="form-buttons">
		<button type="button" name="button" class="button solid f_right primary red">Submit Registration<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
		<a onclick="document.editform.submit();" class="previous f_right primary black button">Go Back<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a>
	</div>
	
	<div class="hidden">
		<div id="recaptcha-modal" class="hidden-modal" title="Verify You&rsquo;re Not a Robot">
			<div class="recaptcha-wrapper">
				<div id="recaptcha-confirm" class="g-recaptcha" data-sitekey="'.$global['recaptcha_key'].'"></div>
			</div>
		</div>
	</div>
	<input type="hidden" name="g-recaptcha-response" value="" />
	
	<input type="hidden" name="process" value="1" />
	<input type="hidden" name="ccnumber" value="' .$ccnumber. '" />
	<input type="hidden" name="cvv" value="' .$cvv. '" />
	<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
</form>';

//Edit information
$html .= '<form name="editform" id="editform" action="' .$_sitepages['reg_checkout']['page_url']. '" method="post">
	<input type="hidden" name="ccnumber" value="' .$ccnumber. '" />
	<input type="hidden" name="cvv" value="' .$cvv. '" />
	<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
</form>';

//Set panel content
$page['page_panels'][$panel_id]['content'] .= $html;

?>