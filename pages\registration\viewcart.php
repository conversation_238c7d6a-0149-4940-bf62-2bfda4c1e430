<?php

//Empty cart
if(empty($reg_cart)){
	$html .= $Account->important('<i class="fa fa-shopping-cart"></i>&nbsp; Your shopping cart is empty.');
	$html .= '<p>';
	if($_sitepages['tournaments']['showhide'] == '0'){
		$html .= '<a href="' .$_sitepages['tournaments']['page_url']. '">Browse Tournaments &rsaquo;</a><br />';	
	}
	if($_sitepages['events']['showhide'] == '0'){
		$html .= '<a href="' .$_sitepages['events']['page_url']. '">Browse Events &rsaquo;</a>';	
	}
	$html .= '</p>';
	
}else{
	
	//Cart alerts
	if(isset($_SESSION['cart_error'])){
		$html .= $Account->alert($_SESSION['cart_error'], false);
		unset($_SESSION['cart_error']);
	}
		
	//Sort cart items by event type
	$events = array();
	$tournaments = array();
	$unavailable = array();
	$atcapacity = array();
	foreach($reg_cart as $item_id=>$item){
		$checkevent = $Registration->get_occurrence($item['occurrence_id']);
		
		if($item['event_type'] == '2'){
			
			//Tournament availability
			$item['available'] = true;
			if(empty($checkevent) || !$checkevent['reg_available']){
				$unavailable[] = $item['occurrence_id'];
				$item['available'] = false;
			}
			
			$tournaments[$item_id] = $item;
			
		}else{
			
			//Event availability
			$item['available'] = true;
			$item['spots_available'] = NULL;
			if($checkevent){
				$item['spots_available'] = $checkevent['spots_available'];
				if(!$checkevent['reg_available']){
					$item['available'] = false;
				}
				if(!is_null($checkevent['spots_available']) && count($item['attendees']) > $checkevent['spots_available']){
					$atcapacity[] = $item['occurrence_id'];
					$item['available'] = false;
				}
			}else{
				$item['available'] = false;
			}
			
			$events[$item_id] = $item;
		}
		
	}
	
	//Events
	if(!empty($events)){
		$html .= '<h3>Events</h3>';
		
		$eventcount = 0;
		foreach($events as $item_id=>$item){
			$eventcount++;		
			
			//Unavailable
			if(isset($item['available']) && !$item['available']){
				$html .= $Account->alert((in_array($item_id, $atcapacity) ? ' Number of attendees exceeds the spots available for this event (' .$item['spots_available']. ' available).' : ' Event is no longer available for registration.'), false);
			}
			
			$html .= '<form name="cart-form" action="' .$_sitepages['reg_checkout']['page_url']. '" method="post">
				<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive nomargin">
					<tr>
						<th class="left">Event</th>
						<th class="right">Total</th>
					</tr>';
					$html .= '<tr>
						<td colspan="2"><h6>' .(isset($item['available']) && !$item['available'] ? '<i class="fa fa-exclamation-triangle color-red" title="Unavailable"></i> ' : '').$item['event_name']. '</h6>
							<small class="dblock">' .format_date_range($item['start_date'], $item['end_date']). '</small>
							<small class="dblock">
								<a onclick="editform_'.$item_id.'.submit();" class="action">Edit</a> &nbsp;
								<a onclick="deleteCartItem('.$item_id.');" class="action">Delete</a> &nbsp; 
								<a href="' .$item['event_url']. '" class="action">View</a>
							</small>
						</td>
					</tr>';
					foreach($item['attendees'] as $attendee){
						$attendee_subtotal = $attendee['ticket_price'];
						$html .= '<tr>
							<td>' .$attendee['attendee_fields']['first_name'].' '.$attendee['attendee_fields']['last_name'].'<br />
							<small class="dblock">'.$attendee['ticket_type']. ': $' .number_format($attendee['ticket_price'], 2). '</small>';
							foreach($attendee['attendee_fields'] as $field=>$value){
								if($field != 'first_name' && $field != 'last_name' && trim($value) != ''){
									$html .= '<small class="dblock">' .ucwords(str_replace('_', ' ', $field)).': ' .$value. '</small>';
								}
							}
							if(isset($attendee['addons']) && !empty($attendee['addons'])){
								foreach($attendee['addons'] as $addon){
									if(trim($addon['value']) != ''){
										$html .= '<small class="dblock">' .$addon['name'].': ' .$addon['value']. 
										(!empty($addon['price_adjustment']) && $addon['price_adjustment'] > 0 ? ' - $'.number_format($addon['price_adjustment'], 2) : ''). '</small>';
									}
									$attendee_subtotal += $addon['price_adjustment'];
								}
							}
							$html .= '</td>
							<td class="right" valign="top">$' .number_format($attendee_subtotal, 2). '</td>
						</tr>';
					}
				$html .= '</table>';
			
				//Promo codes
				$html .= '<div class="promo-code clearfix">
					<h6>Enter Discount Code</h6>
					<input type="text" name="promocode" class="input f_left" value="' .($item['discount'] > 0 ? $item['promocode'] : ''). '" autocomplete="off" />
					<input type="submit" name="applycode" class="button simple f_left nomargin" value="Apply" />
				</div>';

				//Display totals
				$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive">';
					if($item['discount'] > 0){
						$html .= '<tr>
							<td align="right">Discount:</td>
							<td class="right">$' .number_format($item['discount'], 2). '</td>
						</tr>';
					}
					$html .= '<tr>
						<td align="right">Subtotal:</td>
						<td class="right" width="120px">$' .number_format($item['subtotal'], 2). '</td>
					</tr>';
					$html .= '<tr>
						<td align="right">Taxes:</td>
						<td class="right">$' .number_format($item['taxes'], 2). '</td>
					</tr>';
					$html .= '<tr>
						<td align="right"><h6>Total:</h6></td>
						<td class="right"><h6>$' .number_format($item['total'], 2). '</h6></td>
					</tr>
				</table>';

				$html .= '<div class="form-buttons clearfix">
					<button type="submit" name="checkout" class="button solid f_right primary red" value="true"' .(isset($item['available']) && !$item['available'] ? ' disabled' : ''). '>Continue<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button>
				</div>

				<input type="hidden" name="cart_items[]" value="' .$item_id. '" />
				<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
			</form>';
			
			//Edit form
			$html .= '<form name="editform_'.$item_id.'" action="' .$_sitepages['registration']['page_url']. '" method="post">
				<input type="hidden" name="event_id" value="' .$item['event_id']. '" />
				<input type="hidden" name="occurrence_id" value="' .$item_id. '" />
				<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
			</form>';
			
			if($eventcount != count($events) || ($eventcount == count($events) && !empty($tournaments))){
				$html .= '<hr class="clear" />';	
			}
			
		}
	}
	
	//Tournaments
	if(!empty($tournaments)){
		$html .= '<h3>Tournaments</h3>';
		
		//Unavailable
		if(!empty($unavailable)){
			$html .= $Account->alert('<i class="fa fa-exclamation-triangle"></i> One or more of the items in your cart are no longer available for registration. You must delete these items before you can continue.', false);
		}
		
		include("includes/widgets/shoppingcart.php");
	}
	
}

//Set panel content
$page['page_panels'][62]['content'] .= $html;

?>