<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['keyword_inserts']){

	//Define vars
	$record_db 		= 'keyword_inserts';
	$record_id 		= 'keyword_insert_id';
	$record_name 	= 'Keyword Insert';

	//Validation
	$errors   = false;
	$required = [];
	$required_fields = [
		'name',
		'query_string_label'
	];

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.name",
		"$record_db.query_string_label"
	];

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get records
	$db->query("SELECT $record_db.* FROM $record_db $where ORDER BY $record_db.name ASC", $params);
	$records_arr = $db->fetch_assoc($record_id);
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}

	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){
			$row = $records_arr[ITEM_ID];
			
		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", array(ITEM_ID));
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}
		header("Location: " .PAGE_URL);
		exit();

	//Save item
	}else if(isset($_POST['save'])){

		//Validate required fields
		foreach($required_fields as $field){
			if(!($_POST[$field] ?? false)){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}	

		//Clean query string label and validate its uniqueness
		$is_unique = true;
		$_POST['query_string_label'] = clean_query_string($_POST['query_string_label']);
		foreach($records_arr as $check_label){ 
			if($check_label['keyword_insert_id'] != ITEM_ID && $check_label['query_string_label'] == $_POST['query_string_label']){
				$is_unique = false;
				break;
			}
		}

		if(!$is_unique){
			$errors[] = 'Another keyword insert already exists with the same query string label. Please provide a unique value.';
			array_push($required, 'query_string_label');
		}

		if(!$errors){

			//Insert to db
			$params = array(
				
				//Insert
				ITEM_ID, 
				$_POST['name'], 
				$_POST['query_string_label'],
				$_POST['default_value'], 
				$_POST['accepted_values'], 
				date("Y-m-d H:i:s"),
				
				//Update
				$_POST['name'], 
				$_POST['query_string_label'],
				$_POST['default_value'], 
				$_POST['accepted_values'], 
				date("Y-m-d H:i:s")
			);			

			$db->query("INSERT INTO $record_db($record_id, name, query_string_label, default_value, accepted_values, last_updated) VALUES(?,?,?,?,?,?) ON DUPLICATE KEY UPDATE name=?, query_string_label=?, default_value=?, accepted_values=?, last_updated=?", $params);
			$item_id = (ITEM_ID == "" ? $db->insert_id() : ITEM_ID);

			if(!$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	}

}

?>