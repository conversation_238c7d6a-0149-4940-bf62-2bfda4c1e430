@charset "utf-8";
/* 
	forms.less
	Project: Honeycomb CMS v4.0
	
*/

/*------ standard elements ------*/
label{
	display: block; 
	height: 20px; 
	margin-bottom: 5px;
}
.form-field{
	.f_left; //LEGACY
	max-width: 100%;
	margin: 0 10px 0 0;
}
.flex-container{
	.form-field, .img-holder{
		float: none;
	}
}

.input, .select, .textarea{
	font-size: 16px; 
	font-family: @font-base; 
	color: @color-theme3; 
	font-weight: 400; 
	border-radius: 0; 
	border: 1px solid @color-light; 
	background-color: #fff; 
	box-sizing: border-box;
	padding: 4px 8px; 
	width: 260px; 
	max-width: 100%;
	height: 40px; 
	margin-bottom: 20px; 
	outline: none;
	.trans(color);
	.trans(background-color);
	.trans(border-color);
	.no-appearance();
	
	&:focus{
		background-color: @color-theme1; 
		color: #fff; 
		border-color: @color-theme2;
	}
	&:disabled{
		background-color: @color-lightest !important; 
		color: @color-medium !important;
	}
}
.textarea{
	height: 125px; 
	resize: none;
}
.select{
	padding-right: 22px; 
	background-image: url("../images/angle-down.svg");
	background-position: ~"calc(100% - 6px)" ~"calc(50% + 1px)";
	background-repeat: no-repeat; 
	background-size: 10px auto; 
	text-overflow: "";
	line-height: 1.5;
}

.input_sm, 
.select_sm{width: 128px;}
.input_lg{width: 540px; }
.select_lg{height: 50px; }

#seo-description.textarea{height: 200px;}

input[type=file]{
	padding: 4px; 
	line-height: 30px; 
	font-size: 80%; 
	color: @color-medium;
}

.checkbox, .radio{
	display: none;
	
	+ label{
		.relative; 
		display: inline-block; 
		padding: 1px 10px 1px 26px; 
		margin: 0 0 2px;
		cursor: pointer; 
		line-height: 20px;
		color: @color-theme3;

		&.no-text{padding-left: 10px;}
		
		&:before{
			.absolute; 
			content: "";
			display: inline-block; 
			line-height: 19px;
			left: 0; 
			width: 18px; 
			height: 18px; 
			border: 1px solid @color-light; 
			text-align: center;
			color: @color-theme1;
			background: #fff;
		}
	}
	
	&:disabled + label:before{background-color: @color-lightest;}
}

.radio + label:before{border-radius: 50%;}
.radio:checked + label:after{
	&:extend(.radio + label:before);
	.scale(0.6);
	border-color: transparent;
	background: @color-theme1;
}
.checkbox:checked + label:before{.font-awesome(f00c); font-size: 14px;} 

.img-holder{
	.f_left; //LEGACY
	max-width: 260px; 
	min-height: 64px; 
	margin: 0 10px 20px 0;
	
	img{
		display: block; 
		width: 100%; 
		min-height: 64px; 
		border: 1px solid @color-light; 
		margin-bottom: 10px;
	}
}
.cropbox{border: 1px solid @color-light !important;}

.mce-tinymce{margin-bottom:10px !important;
	.mce-edit-area{padding: 10px;}
	.mce-ico.mce-i-flag::before{.font-awesome(f024);}
}
.panel-content.nopadding .mce-tinymce{ //full panel tinymce
	margin: 0 !important; 
	border-width: 1px 0 !important;
	overflow: hidden;
}


/*------ tag editor ------*/
.tag-editor-hidden-src{.sr-only(); }

ul.tag-editor{ 
	.flexbox(row wrap; flex-start; center;);
	align-content: flex-start;
	gap: 5px;
	padding: 8px;
	min-height: 40px;
	margin-left: 0;
	margin-right: 0;
	resize: vertical;
	list-style-type: none; 
	overflow: auto; 
	cursor: text; 

	li{
		.flexbox(row nowrap);
		margin: 0;
		overflow: hidden; 
		line-height: 22px; 

	}

	input{ 
		margin: 0; 
		padding: 0; 
		border: 0; 
		vertical-align: inherit; 
		outline: none; 
		cursor: text; 
		box-shadow: none; 
		background: none; 
		font: inherit; 
		letter-spacing: inherit;
	}

	.ui-sortable-placeholder{
		visibility: visible !important;
		border: 2px dashed @color-light; 
		max-height: 22px;
		padding-right: 12px;
	}
	 
	.tag-editor-tag{ 
		padding: 0 5px; 
		background: @color-lightest; 
		overflow: hidden; 
		cursor: pointer; 
		white-space: nowrap; 
		
		&.active{background: none !important;}
	}
	
	.tag-editor-delete{ 
		width: 1em; 
		padding: 0 4px; 
		font-size: 16px; 
		background: @color-lightest; 
		cursor: pointer; 
		text-align: center; 
		
		i:before{
			content: "\00d7"; 
			display: inline-block; 
			line-height: 20px; 
			font-style: normal; 
		}
		&:hover i:before{color: @color-medium;}
	}
	
	.tag-editor-tag.active + .tag-editor-delete, 
	.tag-editor-tag.active + .tag-editor-delete i{
		visibility: hidden; 
		cursor: text;
	}

	.tag-editor-spacer,
	li[style^="width"]{display: none; }

	&.full{width: 100%; }
}

td .tag-editor{
	min-height: 40px;
	height: auto; 
	margin: 0;
}


.ui-dialog ul.tag-editor{padding: 4px 8px; margin: 0;}


/*------ buttons ------*/
.button, a.button, .button-sm, a.button-sm{
	.relative;
	display: inline-block; 
	background: @color-theme1; 
	border: none; 
	color: #fff; 
	font-size: 16px; 
	padding: 0 15px 0 65px; 
	text-decoration: none; 
	height: 50px;
	line-height: 50px;
	white-space: nowrap; 
	cursor: pointer;
	outline: none;
	font-family: @font-alt;
	.text-caps;
	.extrabold;
	.trans(color);
	.trans(background-color);
	.trans(border-color);
	.trans(opacity);
	
	i{
		.absolute;
		display: block; 
		top: 0; 
		left: 0; 
		width: 50px; 
		height: 50px; 
		line-height: 50px; 
		text-align: center; 
		margin: 0; 
		background: darken(@color-theme1, 10%); 
		.trans(color);
		.trans(background-color);
		.trans(border-color);
		.trans(opacity);
	}
	
	&:hover, &:focus{
		background: @color-theme2; 
		color: #fff;
		
		i{background: @color-darkest;}
	}
	
	&:disabled, &.disabled, &.loading{
		background-color: @color-light !important; 
		color: @color-lightest !important; 
		cursor: default !important; 
		text-decoration: line-through;
		
		i{
			background-color: darken(@color-light, 5%) !important; 
			color: #fff !important; 
			cursor: default !important;
		}
	}

	&.loading{
		font-size: 0;
		text-decoration: none;
		pointer-events: none;

		i::before{
			display: inline-block;
			font-size: 16px; 
			.font-awesome(f110);
			.animation(fa-spin, 2s, linear infinite);
		}

		&::after{
			content: 'Loading...';
			font-size: 16px; 
		}
	}
	
	+ .button, + .button-sm{margin-left: 10px;}
}

.button-sm, a.button-sm{
	height: 40px; 
	line-height: 40px; 
	padding: 0 15px; 
	background: @color-theme1;
	
	i{
		.relative;
		display: inline;
		line-height: inherit; 
		margin-right: 15px; 
		background: none;
	}
		
	&:hover, &:focus{background: darken(@color-theme1, 10%);
		i{background: none;}
	}
	
	&:disabled, &.disabled{
		i{
			color: @color-lightest !important; 
			cursor: default !important;
			background: none !important;
		}
	}
	
	&.delete{ 
		i{background: @color-medium;} 
		&:hover i{background: @color-darkest;}
	}
} 

.actions-nav{
	.button-sm, a.button-sm{
		border: 1px solid @color-theme1;
		color: @color-theme1;
		background: transparent;
		
		&:hover, &:focus{
			color: #fff;
			background: @color-theme1;
		}
	}
}

.cancel{
	display: inline-block; 
	line-height: 50px; 
	padding: 0; 
	font-size: 16px;
}

.delete, a.delete{
	background: @color-medium;
	
	i{
		background: @color-light; 
		color: @color-dark;
	}
	
	&:hover, &:focus{
		background: @color-darkest;
		
		i{
			color: #fff; 
			background: lighten(@color-darkest, 10%);
		}
	}
}

.img-holder{.relative;
	.recrop-button{
		.absolute;
		top: 1px;
		left: 1px;
		background: rgba(44, 44, 44, 0.7);
		cursor: pointer;
		box-sizing: content-box;
		border: 0;
		padding: 8px;
		font-size: 16px;
		color: #fff;
	    .trans(all);

		&:hover{
			color: @color-theme1;
			background: @color-darkest;
		}
	}
}


/*------ datepicker ------*/
.ui-datepicker{
	width: 260px;
	padding: .2em 4px 0 4px;
	display: none;
	box-sizing: border-box;
	background: @color-theme2;
	.box-shadow(@color: rgba(0,0,0,0.16););

	.ui-datepicker-header{
		.relative;
		padding: .2em 0;
		background: @color-theme2;
		color: #fff;
		font-size: 18px;
		font-weight: 700;
	}

	.ui-datepicker-prev,
	.ui-datepicker-next{
		.absolute;
		top: 2px;
		width: 1.8em;
		height: 1.8em;

		span{
			.absolute;
			display: block;
			left: 50%;
			margin-left: -8px;
			top: 50%;
			margin-top: -8px;
			font-size: 0px;

			&::before{
				font-size: 16px;
				line-height: 1;
			}
		}
	}

	.ui-datepicker-prev span::before{.font-awesome(f137);}
	.ui-datepicker-next span::before{.font-awesome(f138);}

	.ui-datepicker-prev-hover,
	.ui-datepicker-next-hover{color: #fff;}
	.ui-datepicker-prev{left: 2px;}
	.ui-datepicker-next{right: 2px;}

	.ui-datepicker-title{
		margin: 0 2.3em;
		line-height: 1.8em;
		text-align: center;

		select{
			font-size: 1em;
			margin: 1px 0;
		}
	}

	select.ui-datepicker-month,
	select.ui-datepicker-year{width: 45%;}

	table{
		width: 100%;
		font-size: .9em;
		border-collapse: collapse;
		margin: 0 0 .4em;

		th{
			padding: .7em .3em;
			height: auto;
			text-align: center;
			font-weight: bold;
			border: 0;
			background: @color-lightest;
		}
		td{
			border: 0;
			padding: 2px;
			background: #fff !important;

			span, a{
				display: block;
				padding: .3em;
				text-align: right;
				text-decoration: none;
				border: 1px solid @color-lightest;
			}
			a{
				&.ui-state-active{
					border-color: @color-theme1;
				}
				&.ui-state-hover{
					background: @color-theme1;
					color: #fff;
					font-weight: 700;
				}
			}

			&.ui-datepicker-today a{
				background: @color-theme2;
				color: #fff;
				font-weight: 700;
			}
		}
	}

	.ui-datepicker-buttonpane{
		background-image: none;
		margin: .7em 0 0 0;
		padding: 0 .2em;
		border-left: 0;
		border-right: 0;
		border-bottom: 0;

		button{
			float: right;
			margin: .5em .2em .4em;
			cursor: pointer;
			padding: .2em .6em .3em .6em;
			width: auto;
			overflow: visible;

			&.ui-datepicker-current {float: left;}
		}

	}

	/* with multiple calendars */
	&.ui-datepicker-multi{width: auto;
		.ui-datepicker-group{float: left;
			table{
				width: 98%;
				margin: 0 auto .4em;
			}
		}
		.ui-datepicker-group-last .ui-datepicker-header,
		.ui-datepicker-group-middle .ui-datepicker-header{border-left-width: 0;}
		.ui-datepicker-buttonpane{clear: left;}
	}

	.ui-datepicker-multi-2 .ui-datepicker-group{width: 50%;}
	.ui-datepicker-multi-3 .ui-datepicker-group{width: 33.3%;}
	.ui-datepicker-multi-4 .ui-datepicker-group{width: 25%;}

	.ui-datepicker-row-break{
		clear: both;
		width: 100%;
		font-size: 0;
	}
}

/* with month and year select */
.ui-datepicker select{
	.select; 
	font-size: 14px !important; 
	padding-top: 0; 
	padding-bottom: 0; 
	height: 30px; 
	background-position: right -10px;
}


/*------ google map picker ------*/
.gllpLatlonPicker{
	.relative; 
	border: 0; 
	padding: 0; 
	margin: 0 15px 10px 0;
	max-width: 100%;
	align-self: flex-start;
}
.gllpMap{
	.relative; 
	z-index: 1; 
	width: 590px; 
	height: 235px;
	max-width: 100%; 
	overflow: hidden;
	resize: both;
}
.gllpSearch{
	.absolute; 
	top: 10px; 
	left: 10px; 
	z-index: 2; 

	// Emulate google maps controls
	border-radius: 2px;
	background: @color-white;
	box-shadow: 0px 1px 4px -1px rgb(0 0 0 / 30%);
	overflow: hidden;
	color: #666;

	.input{
		border: 0; 
		.placeholder({color: #999;}); 

		&, &:focus{
			background: none; 
			color: inherit;
		}

		&:focus{.placeholder({color: #666;}); }
	}

	.button-sm{
		position: relative;
		padding: 0;
		font-size: 18px;
		color: inherit;
		background: @color-white;

		&:hover,
		&:focus{color: #111; }

		&::after{
			.position(0, auto, 0, 0, 1px, 30px);
			background-color: #e6e6e6;
			content: '';
		}
	}
}

fieldset.gllpLatlonPicker{
	.f_left; //LEGACY
}


/*------ alerts ------*/
.success{color: @color-success;}
.error{color: @color-error;}
.alert{color: darken(@color-alert, 6%);}
.required{border-color: @color-error; color: @color-error;}

.system-alert{
	background: #fff; 
	margin: 0 0 20px; 
	opacity: 0; 
	overflow: hidden;
	.box-shadow(); 
	
	.title{
		padding: 20px 20px 20px 15px; 
		font-weight: 800; 
		text-transform: uppercase;
		color: #fff;
		font-size: 16px;
		font-family: @font-alt;
		.extrabold;
		
		i{
			padding-right: 10px; 
		}
	}
	.message{
		padding: 20px 15px; 
		color: @color-darkest;
	}
	
	&.important{
		.panel-toggle{
			color: #fff; 
			font-size: 16px;
		}
		.title{
			background: @color-theme1; 
			color: #fff;
		}
		&.error .title{background: @color-error;}
	}
	
	&.error{
		.title{background: @color-error;}
	}
	
	&.success{
		.title{background: @color-success;}
	}
	
	&.mini{
		.relative; 
		width: 100%; 
		color: #fff; 
		background: @color-darkest; 
		opacity:0.9; 
		.trans(all);

		p{padding-bottom: 10px;}
		
		.message{
			padding: 10px 15px; 
			color: #fff; 
			font-size: 12px; 
			font-weight: 600;
		}
		
		&.error, &.success{
			.title{
				padding-bottom: 0; 
				background: none;
			}
		}
		&.error .title i{color: @color-error;}
		&.success .title i{color: @color-success;}
	}
}

#system-mini-alerts{
	position: fixed; 
	z-index: 20; 
	top: 110px; 
	right: 20px; 
	width: 260px; 
	pointer-events: none;
}


/*------ login table ------*/
#login-wrapper{
	background: url("@{path}images/hexagons.jpg") center no-repeat #000 fixed; 
	background-size: cover;
	width: 100%; 
	height: 100%; 
	min-height: 400px;
}
#login-header{
	padding: 0 0 20px;
	margin: 0 0 25px;
	border-bottom: 1px solid @color-lightest;
	
	img{width: 100%;}
}
#login-form{
	.absolute; 
	top: 50%; 
	left: 50%; 
	width: 360px; 
	max-width: 100%;
	margin: 0; 
	padding: 25px 25px 40px;
	box-sizing: border-box;
	background: #fff;
	.transform(translate(-50%, -50%););
		
	.form-field{
		display: block;
		float: none;
		margin: 0 0 20px;
		
		label{
			.flexbox();
			font-family: @font-alt;
			.extrabold;
			.text-caps;

			a{
				display: inline-block;
				margin-left: auto;
				font-family: @font-base;
				text-transform: none;
				.regular;
			}
		}
		
		.input{
			width: 100%;
			height: 50px;
		}
	}
	
	.checkbox + label{padding-top: 15px;}
	
	#login-buttons{.flexbox();
		.button{margin-left: auto;}
	}
	
	#login-alert{
		background: lighten(@color-error, 55%); 
		color: @color-error; 
		font-weight: 600; 
		padding: 5px 10px; 
		margin-bottom: 20px;
		
		&.success{
			background: lighten(@color-success, 40%); 
			color: @color-success;
		}
	}
	
}


/*------ search form ------*/
#search-form{
	.relative; 
	.f_left; //LEGACY
	
	.input{
		.f_left; //LEGACY
		height: 50px; 
		padding-right: 20px;
	}
	.select{height: 50px;}
	
	.button{
		padding: 0; 
		width: 50px;
		
		i{background:@color-theme3;}
	
		&:hover, &:focus{
			i{background:@color-darkest;}
		}
	}
	
	#clear-search{
		.absolute; 
		z-index: 1; 
		left: 240px; 
		line-height: 50px;
	}
}

.multiple-search{
	.input, .select{margin-right: 10px;}
}


/*------ social inputs ------*/
.social{.relative;
	.input{
		.relative;
		z-index: 1;
		padding-left: 50px;
	}
	.fab{
		.absolute;
		display: block; 
		z-index: 2; 
		width: 38px; 
		height: 38px; 
		top: 1px; 
		left: 1px; 
		background-color: lighten(@color-theme1, 10%); 
		color: #fff; 
		font-size: 20px; 
		text-align: center; 
		line-height: 38px;
	}
}


/*------ img position picker ------*/
.img-position-picker{
	.relative; 
	float: left; //LEGACY
	clear: both; 
	margin-bottom: 20px;
	
	img{
		display: block; 
		width: 600px; 
		max-width: 100%;
	}
	&.img-picker-small img{width: 300px;}
	
	.radios-wrapper{
		.absolute; 
		top: 0; 
		left: 0; 
		width: 100%; 
		height: 100%;
		
		input[type="radio"]{display: none;}
		input[type="radio"] + label{
			.absolute;
			top: 0;
			left: 0;
			display: block;
			width: 33.333333333333%;
			height: 33.333333333333%;
			margin: 0;
			padding: 0;
			border: 1px solid #fff;
			z-index: 1;
			cursor: pointer;
		}
		input[type="radio"].left-0 + label{left: 0;}
		input[type="radio"].left-3 + label{left: 33.333333333333%;}
		input[type="radio"].left-6 + label{left: 66.666666666666%;}
		input[type="radio"].top-0 + label{top: 0;}
		input[type="radio"].top-3 + label{top: 33.333333333333%;}
		input[type="radio"].top-6 + label{top: 66.666666666666%;}
		input[type="radio"]:checked + label{background: fade(@color-theme1, 75%);}
	}
}


/*------ 5 star rating input ------*/
.rating-wrapper{
	margin: 0 0 20px; 
}
.rating-select{
	display: inline-block !important;
	font-size: 25px; //Scale
	padding: 6px 0 9px;
	line-height: 1em;
	vertical-align: top;
	white-space: nowrap;

	//Hide checkbox (display none may prevent form or label functionality)
	input{
		position: absolute;
		width: 0.1px;
		height: 0.1px;
		visibility: hidden;
		opacity: 0;
	}

	//Default label
	label{
		position: relative;
		display: block;
		color: @color-theme1;
		cursor: pointer;
		float: right;
		.trans(color);
		
		//Gutter
		&:not(:first-of-type){padding: 0 5px 0 0; }

		//Icons
		&::before, 
		&::after{
			display: block;
			.font-awesome(f005, 400); 
		}
		
		//Empty star
		&::before{.trans(opacity, 0.1s, ease); } 

		//Filled star
		&::after{
			opacity: 0;
			font-weight: 900;
			.trans(opacity, 0.15s);
			position: absolute;
			top: 0;
			left: 0;
		}
	}

	//Hoverstate
	label:hover,
	label:hover ~ label{color: @color-theme2; }

	//Fill star if selected or after a selection
	> input:checked ~ label{
		&::after{opacity: 1; }
		&::before{
			opacity: 0; 
			.trans(opacity, 0.1s, ease, 0.15s);
		}
	}

	input:disabled + label{color: @color-medium; }
}


/*------ actions nav ------*/
.actions-nav{
	.flexbox(@cross:center); 
	margin-bottom: 20px; 
	border-bottom: 1px solid @color-light;
	
	.column, .flex-column{margin-bottom: 20px;
		&.right{margin-left: auto;}
	}
	.nav-item{display: inline-block;}
	.nav-item ~ .nav-item{margin-left: 10px;}
	
	#search-form .input{margin-bottom: 0;}
}


/*------ multiselects ------*/
.multiselect{ 
	width: 235px; 
	height: 115px; 
	margin: 0 0 20px;
	padding: 10px;
	border: 1px solid @color-light;
	border-radius: 0;
	background: #fff;
	outline: none;
	border-radius: 0;
	font-size: 16px; 
	font-family: @font-base; 
	font-weight: 400; 
	line-height: 30px; 
	color: @color-dark; 
	text-overflow: ""; 
	list-style: none;
	.no-appearance(); 
	.trans(background-color); 

	.checkbox + label, .radio + label{
		padding-right: 0;
	}

	&.multiselect-lg{
		width: 352.5px;
		height: 230px;
	}
}
.multiselect-field{
	display: inline-block; 
	margin-right: 10px; 
	vertical-align: middle; 
}
.multiselect option{padding:4px 8px 4px 4px; }
.multiselect-arrows{
	display: inline-block; 
	margin: 5px 20px 0 10px; 
	vertical-align: middle; 
}
.multiselect-arrow, a.multiselect-arrow{
	display: block; 
	margin: 0 0 1px 0 !important; 
	padding: 0 20px;
}


/*------ dropzone ------*/
.dropzone{
	margin-bottom: 20px; 
	border-style: dashed; 
	border-color: @color-theme1; 
	.trans(all);
	
	input[type="file"]{display: none;}
	
	.dz-message.dz-default{
		margin: 45px 0; 
		color: @color-dark; 
		font-size: 16px;
		
		small{margin: 4px 0 5px;}
	}
	
	&.dz-drag-hover, &:hover{
		border-color: @color-theme2; 
		background: lighten(@color-theme1, 40%);
		
		.button-sm{background-color: @color-theme1;}
	}
	&.dz-drag-hover{border-style: solid;}
}
.dz-gallery{
	.flexbox();
	list-style: none; 
	margin: -6px;
	padding: 0;
	
	li{
		.relative; 
		margin: 0 6px 12px; 
		overflow: hidden;
		
		.container-checkbox{
			.absolute; 
			.no-appearance(); 
			width: 0; 
			height: 0; 
			opacity: 0;
			
			+ label{
				.relative; 
				display: block; 
				width: 150px; 
				height: auto; 
				min-height: 150px; 
				padding: 6px; 
				margin-bottom: 0;
				background-color: @color-lightest; 
				cursor: pointer;
				.trans(all); 
			}
			
			&:checked + label{background-color: @color-theme1;}
			&:not(:checked) + label img{.grayscale(1);}
		}
		
		img{
			.absolute; 
			top: 50%; 
			left: 50%; 
			display: block; 
			max-width: ~"calc(100% - 12px)"; 
			max-height: ~"calc(100% - 12px)"; 
			.transform(translate(~"-50%, -50%")); 
			.trans(all);
		}

		.sort-handler{
			.absolute; 
			top: 6px; 
			left: 6px; 
			padding: 9px; 
			background: fade(@color-darkest, 70%); 
			opacity: 0;
			cursor: pointer; 
			color: #fff; 
			text-align: center;
			.trans(all); 
			
			&:hover{background: @color-darkest;}
			&:active{cursor: grabbing;}
			
			i{color: inherit;}
		}
		
		.buttons{
			.absolute;
			left: 6px;
			right: 6px;
			bottom: 6px;
			background: fade(@color-darkest, 70%);
			opacity: 0; 
			text-align:center;
		    .translateY(20px);
			.trans(all);

			a{
				display: inline-block; 
				margin: 5px 0; 
				padding: 3px 5px; 
				color: #fff;
				
				i{font-size: 18px;}
				
				&:hover, &:active{color: @color-theme1;}
			}

			&:hover, &:active{background: #333;}
		}

		&:hover, &:active{
			.sort-handler{opacity: 1;}
			.buttons{
				opacity: 1;
			    .translateY(0);
			}
		}

		&.placeholder-item{
			display: block; 
			width: 192px; 
			border: 1px dashed #ddd; 
			background: @color-lightest;
		}
	}
}


/*------ image/icon picker ------*/
.item-picker{
	--width: 250px;
	--spacing: 6px;
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(var(--width), 1fr));
	gap: 20px;
	padding-bottom: 10px;

	input[type="checkbox"]{.sr-only(); }

	label{
		.flexbox(column; center; stretch;);
		.flex(0 0 auto);
		position: relative;
		height: auto;
		margin: 0;
		padding: var(--spacing);
		border: 1px solid @color-light;
		cursor: pointer;

		img{
			display: block; 
			padding-bottom: 10px; 
			width: 100%;
			height: auto;
		}

		i:not(.handle){
			display: block;
			font-size: 30px;
			line-height: 90px;
			text-align: center;
			color: @color-theme1;
		}

		.handle{
			color: @color-white;
			background-color: fade(@color-darkest, 70%);
			opacity: 0;
			font-size: 16px;
			line-height: 30px;
			text-align: center;
			.position(var(--spacing), auto, auto, var(--spacing), 30px);
			.flexbox(row; center; center;);
		}

		.title{font-weight: bold; }
	}

	// Hover state
	label:hover{
		border-color: @color-theme1; 
		.handle{opacity: 1; }
	}

	// Active state
	:checked + label{
		background-color: @color-theme1;
		border-color: @color-theme1;

		&, small, i:not(.handle){color: @color-white; }
	}

	.placeholder-item{border: 2px dashed @color-light; }

	// Icons instead of images
	&.icons{
		--width: 150px;
		--spacing: 10px;

		label{
			padding: 10px;

			i:not(.handle){
				font-size: inherit;
				line-height: inherit;
				padding-bottom: 10px; 
			}

			.handle{
				width: auto; 
				height: auto;
				line-height: inherit;
				background: none;
				font-size: inherit;
				opacity: 1;
				color: @color-light; 
			}
		}

		:checked + label{
			.handle{color: @color-white; }
		}
	}
}


/*------ autocomplete ------*/
.ui-autocomplete.ui-menu{
	.position(0, 0, auto, auto);
	display: block;
	// max-width: 256px; 
	max-height: 200px; 
	margin: 0; 
	padding: 0; 
	border: 1px solid @color-light;
	border-width: 0 1px 1px;
	overflow-y: auto; 
	background-color: @color-white;
	list-style: none; 
	z-index: 2; 
	
	li{display: block; }

	.ui-menu-item{
		padding: 4px 8px; 
		cursor: pointer; 
		color: @color-theme1;
		background-color: inherit;
		.trans(background-color);

		&:not(:last-child){border-bottom: 1px solid @color-light; }

		&:hover{background-color: @color-lightest; }
	}
}


/*------ iconpicker ------*/
.iconpicker-container{
	.flexbox();
	width: 260px;

	.input {
		width: auto;
		.flex(1 1 auto);
	}

	.iconpicker-component{
		display: block;
		width: 40px;
		height: 40px;
		background-color: @color-theme3;
		color: @color-white;
		line-height: 40px;
		text-align: center;
		.trans(background-color);

		i {
			font-style: normal;

			&[class=""]{&:before{.font-awesome(f002);}}
		}

		&:hover {
			background-color: @color-theme2;
		}
	}
}
.iconpicker-popover{
	&.popover{
		width: 266px;

		&.bottom > .arrow {
			top: -9px;
			border-bottom-color: @color-light;
			z-index: 2;	
		}
	}

	.popover-content{
		position: relative;
		border: 1px solid @color-light;
		z-index: 1;
	}

	.iconpicker-search{.input; width:100%;}
}



/*------ cropper ------*/
.cropper-wrapper{
	--overlay: rgba(0,0,0,0.6);
	position: relative; 
	background: repeating-conic-gradient(#808080 0% 25%, #666 0% 50%) 0% / 16px 16px;
	overflow: hidden; 

	img{
		display: block;
		margin: auto;
		max-width: 100%; // This rule is very important, please don't ignore this
	}

	.cropper-image{
		position: relative;
		height: 50vw;
		max-height: 600px;

		> img{
			max-height: 100%; 
			background-color: @color-medium;
		}

		// Loading icon
		&::before{
			font-size: 48px;
			line-height: 1em;
			text-align: center;
			color: @color-white;
			opacity: 0;
			z-index: 1;
			pointer-events: none;
			.trans(opacity, 0.3s, ease, .3s);
			.animation(fa-spin, 2s, linear infinite);
			.position(0,0,0,0,1em);
			.font-awesome(f110);
		}
	}

	.cropper-alert{
		.position(0, auto, auto);
		.flexbox(nowrap; flex-start; baseline;);
		gap: 10px;
		padding: 8px 12px;
		color: @color-white;
		background-color: var(--overlay);
		font-weight: 700;
		opacity: 0;
		white-space: nowrap;
		pointer-events: none;
		z-index: 1;
		.trans(opacity);

		&.active{opacity: 1; }
		a{pointer-events: auto; }
		a:hover{color: lighten(@color-theme1, 25%); }
	}

	.cropper-controls{
		.flexbox(row wrap; center;);
		position: relative;
		background-color: @color-white;
		gap: 10px;
		padding: 10px;

		.control{
			margin: 0;
		}

		.cropper-zoom{
			.flexbox(nowrap; flex-start; center);
			gap: 20px;
			padding: 0px 15px;
			font-family: 'Exo', sans-serif;
			font-weight: 800;
			text-transform: uppercase;
			background-color: @color-theme1;
			color: @color-white;
			pointer-events: auto;
			font-size: 16px;

			span{margin-top: 2px;}
		}

		input[type="range"] {
			width: 100%;
			background: none;
			color: inherit;
			--height: 14px;
			--track-height: 2px;

			.thumb(){
				.no-appearance();
				height: var(--height);
				width: var(--height);
				border-radius: 50%;
				border: 0;
				background-color: currentColor;
				cursor: pointer;
				margin-top: calc(var(--height)/-2 + var(--track-height)/2);
			}
			.track(){
				width: 100%;
				height: var(--track-height);
				border: 0;
				background-color: currentColor;
			}

			// Webkit
			&, &::-webkit-slider-thumb{.no-appearance(); }
			&::-webkit-slider-runnable-track{height: var(--height); }
			&::-webkit-slider-thumb{.thumb(); }
			&::-webkit-slider-runnable-track{.track(); }
			
			// Firefox
			height: var(--height);
			&::-moz-range-thumb{.thumb(); }
			&::-moz-range-track{.track(); }

		}
	}

	// Override styles
	.cropper-canvas, 
	.cropper-wrap-box{overflow: visible; }
	.cropper-point,
	.cropper-line{opacity: 1; }
	.cropper-point{background-color: @color-white; }
	.cropper-line{background-color: lighten(@color-theme1, 15%); }
	.line-n, 
	.line-s{height: 1px; }
	.line-e, 
	.line-w{width: 1px; }
	.line-n{top: -1px; }
	.line-e{right: -1px; }
	.line-s{bottom: -1px; }
	.line-w{left: -1px; }

	// Loading styles
	&:not(.cropper-init){
		.cropper-image::before{opacity: 1; }

		// mimic .cropper-modal
		.cropper-image::after{
			.position();
			background-color: rgba(0,0,0,0.5); 
			content: '';
		}
	}
}



// Logo picker (from Partners module)
#partner-picker + .thumbnails{
	.flexbox(row wrap);
	text-align: center;

	li{
		.flexbox(column nowrap);
		.flex(1 0 auto);
		min-width: 200px;
		
		.thumbnail{
			.flex(1 0 auto); 
			.flexbox(column nowrap);
		}
	}

	img{
		display: block;
		width: auto;
		margin: auto;
		max-width: 200px;
		max-height: 100px; 
	}
}

#field-options-table .delete-template-btn{ margin-bottom: 20px; }