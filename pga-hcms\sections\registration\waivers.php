<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Waivers  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter sortable'>";
		
			echo "<thead>";
			echo "<th width='10px' class='{sorter:false}'></th>";	
			echo "<th width='350px'>Title</th>";
			echo "<th width='350px'>Category</th>";
			echo "<th width='70px'>Visible</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr data-table='$record_db' data-column-name='$record_id' data-name='" .$row['title']. "' data-id='" .$row[$record_id]. "'>";
					echo "<td class='handle'><span class='fa fa-arrows'></span></td>";
					echo "<td>" .$row['title']. "</td>";
					echo "<td>" .($row['event_type'] == '1' ? 'Events' : ($row['event_type'] == '2' ? 'Tournaments' : ($row['event_type'] == '3' ? 'Hole In One' : ''))). "</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo "</div>";	
	echo "</div>";

}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$file = $data['file_name'];	
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		$file = '';		
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		//Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				<div class='panel-switch'>
					<label>Show $record_name</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " />
						<label for='showhide'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>$record_name Title <span class='required'>*</span></label>
					<input type='text' name='title' value='" .(isset($row['title']) ? $row['title'] : ''). "' class='input" .(in_array('title', $required) ? ' required' : ''). "' />
					<label>Content</label>
					<textarea name='description' class='textarea' style='width:782px; height:300px;'>" .(isset($row['description']) ? $row['description'] : ''). "</textarea>
				</div>";
				echo "<div class='form-field'>
					<label>Required " .$CMSBuilder->tooltip('Required', 'If a waiver is required, the user will not be able to register without agreeing to the conditions.'). "</label>
					<select name='required' class='select'>
						<option value='1'" .(isset($row['required']) && $row['required'] == true ? ' selected' : ''). ">Yes</option>
						<option value='0'" .(isset($row['required']) && $row['required'] == false ? ' selected' : ''). ">No</option>
					</select>
				</div>";
				echo "<div class='form-field'>
					<label>Assign to Category " .$CMSBuilder->tooltip('Assign to Category', 'Assign a waiver to a specific category to attach it to all events/tournaments within that category.'). "</label>
					<select name='event_type' class='select'>
						<option value=''>- None -</option>
						<option value='1'" .(isset($row['event_type']) && $row['event_type'] == '1' ? ' selected' : ''). ">Events</option>
						<option value='2'" .(isset($row['event_type']) && $row['event_type'] == '2' ? ' selected' : ''). ">Tournaments</option>
						<option value='3'" .(isset($row['event_type']) && $row['event_type'] == '3' ? ' selected' : ''). ">Hole In One</option>
					</select>
				</div>";			
				echo "<div class='form-field'>
					<label>Numerical Order" .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). "</label>
					<select name='ordering' class='select'>
						<option value='101'>- Default -</option>";
						for($i=1; $i<101; $i++){
							echo "<option value='" .$i. "' " .(isset($row['ordering']) && $row['ordering'] == $i ? "selected" : ""). ">" .$i. "</option>";	
						}
					echo "</select>
				</div>";
			echo "</div>";
		echo "</div>"; // END Details

		//Upload file
		echo "<div class='panel page-content" .(isset($row['type']) && $row['type'] == 1 ? " hidden" : ""). "'>";
			echo "<div class='panel-header'>PDF Document
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Upload File" .$CMSBuilder->tooltip('Upload File', 'File size must be smaller than 20MB.'). "</label>
					<input type='file' class='input" .(in_array('file', $required) ? ' required' : ''). "' name='file' value='' />
					<input type='hidden' name='old_file' value='" .(isset($file) && $file != '' && (file_exists($filedir.$file) || file_exists($tempdir.$file)) ? $file : ''). "' />
				</div>";
				if(isset($file) && $file != '' && file_exists($filedir.$file)){
					echo "<p class='clear'>
						<a href='".$path.$filedir.$file."' target='_blank'><i class='fa fa-download'></i> Download Current File</a>";
						echo " &nbsp; <input type='checkbox' class='checkbox' name='deletefile' id='deletefile' value='1'>
						<label for='deletefile'>Delete Current File</label>";
					echo "</p>";
				}
			echo "</div>";
		echo "</div>"; //Upload file

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";

}
?>