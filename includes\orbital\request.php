<?php

include($_SERVER['DOCUMENT_ROOT'].$root.'includes/orbital/config.php');

/************************ Send request ************************/
if(!function_exists('curl_request')){
	function curl_request($request){
		global $siteurl;
		global $merchant_id;
	
		//Replace html characters
		$request = str_replace("&rsquo;", "'", $request);
		
		$header = array(
			'POST/AUTHORIZE HTTP/1.0',
			'MIME-Version: 1.0',
			'Content-type: application/PTI43',
			'Content-length: ' .strlen($request),
			'Content-transfer-encoding: text',
			'Request-number: 1',
			'Document-type: Request',
			'Merchant-id: ' .$merchant_id,
			'Interface-Version: PGA eComm 1.0'
		);

		$ch = curl_init('https://orbital1.paymentech.net');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_REFERER, $siteurl);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $request);

		$result = curl_exec($ch);
		$xml = simplexml_load_string($result);
		$array = json_decode(json_encode((array) $xml), 1);
		
		curl_close($ch);

		return $array;
	}
}

/********************* Set default response ********************/
$trxnResponse = array(
	'status' => 0,
	'approved' => 0,
	'txn_num' => NULL,
	'txn_tag' => NULL,
	'response_code' => NULL,
	'auth_code' => NULL,
	'cvd_code' => NULL,
	'message' => NULL,
	'ref_number' => NULL
);

/************************ Save profile ************************/
if($request['type'] == 'profileAddRequest' || $request['type'] == 'profileChangeRequest'){
		
	//Format request
	$trxnRequest = '<Request>
		<Profile>
			<CustomerBin>' .$conn_bin. '</CustomerBin>
			<CustomerMerchantID>' .$merchant_id. '</CustomerMerchantID>
			<CustomerName>' .$request['name']. '</CustomerName>
			<CustomerRefNum>' .$request['ref_number']. '</CustomerRefNum>
			<CustomerAddress1>' .$request['bill_address1']. '</CustomerAddress1>
			<CustomerAddress2>' .$request['bill_address2']. '</CustomerAddress2>
			<CustomerCity>' .$request['bill_city']. '</CustomerCity>
			<CustomerState>' .substr($request['bill_province'], 0, 2). '</CustomerState>
			<CustomerZIP>' .str_replace(' ', '', $request['bill_postalcode']). '</CustomerZIP>
			<CustomerEmail>' .$request['email']. '</CustomerEmail>
			<CustomerPhone>' .stripPhoneNumber($request['phone']). '</CustomerPhone>
			<CustomerCountryCode>' .$request['bill_country']. '</CustomerCountryCode>
			<CustomerProfileAction>' .($request['type'] == 'profileAddRequest' ? 'C' : 'U'). '</CustomerProfileAction>
			<CustomerProfileOrderOverrideInd>NO</CustomerProfileOrderOverrideInd>
			<CustomerProfileFromOrderInd>' .($request['type'] == 'profileAddRequest' ? 'A' : 'S'). '</CustomerProfileFromOrderInd>
			<CustomerAccountType>CC</CustomerAccountType>
			<Status>A</Status>
			<CCAccountNum>' .$request['ccnumber']. '</CCAccountNum>
			<CCExpireDate>' .$request['exp_month'].$request['exp_year']. '</CCExpireDate>
		</Profile>
	</Request>';
	
	//Send request
	$trxnArray = curl_request($trxnRequest);
	
	if(is_array($trxnArray) && !empty($trxnArray)){
		if(isset($trxnArray['ProfileResp'])){
			if($trxnArray['ProfileResp']['ProfileProcStatus'] == 0){
				$trxnResponse['status'] = 1;
				$trxnResponse['ref_number'] = $trxnArray['ProfileResp']['CustomerRefNum']; 
			}else{
				$trxnResponse['message'] = $trxnArray['ProfileResp']['CustomerProfileMessage'];
			}
		}else if(isset($trxnArray['QuickResponse'])){
			$trxnResponse['status_code'] = $trxnArray['QuickResponse']['ProcStatus'];
			$trxnResponse['message'] = $trxnArray['QuickResponse']['StatusMsg'];
		}
	}
	
	
/************************ Delete profile ************************/
}else if($request['type'] == 'profileDeleteRequest'){
	
	//Format request
	$trxnRequest = '<Request>
		<Profile>
			<CustomerBin>' .$conn_bin. '</CustomerBin>
			<CustomerMerchantID>' .$merchant_id. '</CustomerMerchantID>
			<CustomerRefNum>' .$request['ref_number']. '</CustomerRefNum>
			<CustomerProfileAction>D</CustomerProfileAction>
			<CustomerProfileFromOrderInd>S</CustomerProfileFromOrderInd>
		</Profile>
	</Request>';
	
	//Send request
	$trxnArray = curl_request($trxnRequest);
	if(is_array($trxnArray) && !empty($trxnArray)){
		if(isset($trxnArray['ProfileResp'])){
			if($trxnArray['ProfileResp']['ProfileProcStatus'] == 0){
				$trxnResponse['status'] = 1;
			}else{
				$trxnResponse['status_code'] = $trxnArray['ProfileResp']['ProfileProcStatus'];
				$trxnResponse['message'] = $trxnArray['ProfileResp']['CustomerProfileMessage'];
			}
		}else if(isset($trxnArray['QuickResponse'])){
			$trxnResponse['status_code'] = $trxnArray['QuickResponse']['ProcStatus'];
			$trxnResponse['message'] = $trxnArray['QuickResponse']['StatusMsg'];
		}
	}


/************************ Auth transaction ************************/
}else if($request['type'] == 'newOrderRequest'){
	
	//Format request
	$trxnRequest = '<Request>
		<NewOrder>
			<IndustryType>EC</IndustryType>
			<MessageType>AC</MessageType>
			<BIN>' .$conn_bin. '</BIN>
			<MerchantID>' .$merchant_id. '</MerchantID>
			<TerminalID>' .$terminal_id. '</TerminalID>';
	
	//Use billing profile
	if(!empty($request['ref_number'])){
		$trxnRequest .= '<AccountNum />
		<Exp />
		<CurrencyCode>124</CurrencyCode>
		<CurrencyExponent>2</CurrencyExponent>
		<CardSecVal />
		<AVSzip />
		<AVSaddress1 />
		<AVSaddress2 />
		<AVScity />
		<AVSstate />
		<AVSphoneNum />
		<AVSname />
		<AVScountryCode />
		<CustomerRefNum>' .$request['ref_number']. '</CustomerRefNum>';
		
	//Charge new card
	}else{
		
		$trxnRequest .= '<AccountNum>' .$request['ccnumber']. '</AccountNum>
		<Exp>' .$request['exp_month'].$request['exp_year']. '</Exp>
		<CurrencyCode>124</CurrencyCode>
		<CurrencyExponent>2</CurrencyExponent>
		<CardSecVal>' .$request['cvv']. '</CardSecVal>
		<AVSzip>' .str_replace(' ', '', $request['bill_postalcode']). '</AVSzip>
		<AVSaddress1>' .$request['bill_address1']. '</AVSaddress1>
		<AVSaddress2>' .$request['bill_address2']. '</AVSaddress2>
		<AVScity>' .$request['bill_city']. '</AVScity>
		<AVSstate>' .substr($request['bill_province'], 0, 2). '</AVSstate>
		<AVSphoneNum>' .stripPhoneNumber($request['phone']). '</AVSphoneNum>
		<AVSname>' .$request['name']. '</AVSname>
		<AVScountryCode>' .(in_array($request['bill_country'], $orbital_countries) ? $request['bill_country'] : ''). '</AVScountryCode>';
		
		//Add new billing profile from order
		if($request['ccsave']){
			$trxnRequest .= '<CustomerProfileFromOrderInd>A</CustomerProfileFromOrderInd>
			<CustomerRefNum />
			<CustomerProfileOrderOverrideInd>NO</CustomerProfileOrderOverrideInd>
			<Status>A</Status>';
		}
	}
	
	$trxnRequest .= '<OrderID>' .$request['ordernum']. '</OrderID>
			<Amount>' .number_format($request['ordertotal'], 2, '', ''). '</Amount>
			<Comments></Comments>
			<TaxInd>1</TaxInd>
			<Tax>' .number_format($request['taxes'], 2, '', ''). '</Tax>
		</NewOrder>
	</Request>';
	
	//Send request
	$trxnArray = curl_request($trxnRequest);
	if(is_array($trxnArray) && !empty($trxnArray)){
		if(isset($trxnArray['NewOrderResp'])){
			
			//Success
			if($trxnArray['NewOrderResp']['ProcStatus'] == 0 && $trxnArray['NewOrderResp']['ApprovalStatus'] == 1){
				$trxnResponse['status'] = 1;
				$trxnResponse['approved'] = 1;	
				$trxnResponse['auth_code'] = (!empty($trxnArray['NewOrderResp']['AuthCode']) && !is_array($trxnArray['NewOrderResp']['AuthCode']) ? $trxnArray['NewOrderResp']['AuthCode'] : NULL);
				$trxnResponse['cvd_code'] = (!empty($trxnArray['NewOrderResp']['CVV2RespCode']) && !is_array($trxnArray['NewOrderResp']['CVV2RespCode']) ? $trxnArray['NewOrderResp']['CVV2RespCode'] : NULL);	
			}
			
			//Response
			$trxnResponse['txn_num'] = (!empty($trxnArray['NewOrderResp']['TxRefNum']) && !is_array($trxnArray['NewOrderResp']['TxRefNum']) ? $trxnArray['NewOrderResp']['TxRefNum'] : NULL);
			$trxnResponse['response_code'] = (!empty($trxnArray['NewOrderResp']['RespCode']) && !is_array($trxnArray['NewOrderResp']['RespCode']) ? $trxnArray['NewOrderResp']['RespCode'] : NULL);
			$trxnResponse['message'] = (!empty($trxnArray['NewOrderResp']['StatusMsg']) && !is_array($trxnArray['NewOrderResp']['StatusMsg']) ? $trxnArray['NewOrderResp']['StatusMsg'] : (!empty($trxnArray['NewOrderResp']['RespMsg']) && !is_array($trxnArray['NewOrderResp']['RespMsg']) ? $trxnArray['NewOrderResp']['RespMsg'] : ''));
			$trxnResponse['txn_tag'] = (!empty($trxnArray['NewOrderResp']['TxRefIdx']) && !is_array($trxnArray['NewOrderResp']['TxRefIdx']) ? $trxnArray['NewOrderResp']['TxRefIdx'] : NULL);
			
			//Customer profile was saved
			if($trxnArray['NewOrderResp']['ProfileProcStatus'] == 0){
				$trxnResponse['ref_number'] = (!empty($trxnArray['NewOrderResp']['CustomerRefNum']) ? $trxnArray['NewOrderResp']['CustomerRefNum'] : NULL);
				
				//If transaction failed, delete profile so they can try again
				if($trxnArray['NewOrderResp']['ApprovalStatus'] != 1 && !empty($trxnResponse['ref_number'])){
					$trxnProfileRequest = '<Request>
						<Profile>
							<CustomerBin>' .$conn_bin. '</CustomerBin>
							<CustomerMerchantID>' .$merchant_id. '</CustomerMerchantID>
							<CustomerRefNum>' .$trxnResponse['ref_number']. '</CustomerRefNum>
							<CustomerProfileAction>D</CustomerProfileAction>
							<CustomerProfileFromOrderInd>S</CustomerProfileFromOrderInd>
						</Profile>
					</Request>';
					
					$trxnProfileArray = curl_request($trxnProfileRequest);
					if(is_array($trxnProfileArray) && !empty($trxnProfileArray)){
						if(isset($trxnProfileArray['ProfileResp'])){
							if($trxnProfileArray['ProfileResp']['ProfileProcStatus'] == 0){
								$trxnResponse['ref_number'] = NULL;
							}else{
								trigger_error('Unable to delete billing profile: '.$trxnProfileArray['ProfileResp']['CustomerProfileMessage']);
							}
						}else if(isset($trxnArray['QuickResponse'])){
							trigger_error('Unable to delete billing profile: '.$trxnProfileArray['QuickResponse']['ProcStatus']).' - Unable to process request';
						}
					}
				}
			}
			
		//Bad request
		}else if(isset($trxnArray['QuickResponse'])){
			$trxnResponse['status_code'] = $trxnArray['QuickResponse']['ProcStatus'];
			$trxnResponse['message'] = $trxnArray['QuickResponse']['StatusMsg'];
		}
	}
	
	
/************************ Refund transaction ************************/
}else if($request['type'] == 'refundRequest'){	
	
	//Format request
	$trxnRequest = '<Request>
		<NewOrder>
			<IndustryType>EC</IndustryType>
			<MessageType>R</MessageType>
			<BIN>' .$conn_bin. '</BIN>
			<MerchantID>' .$merchant_id. '</MerchantID>
			<TerminalID>' .$terminal_id. '</TerminalID>
			<AccountNum></AccountNum>
			<Exp></Exp>
			<CurrencyCode>124</CurrencyCode>
			<CurrencyExponent>2</CurrencyExponent>
			<OrderID>' .$request['ordernum']. '</OrderID>
			<Amount>' .number_format($request['refund_amount'], 2, '', ''). '</Amount>
			<Comments></Comments>
			<TxRefNum>' .$request['txn_num']. '</TxRefNum>
		</NewOrder>
	</Request>';
	
	//Send request
	$trxnArray = curl_request($trxnRequest);
	if(is_array($trxnArray) && !empty($trxnArray)){
		if(isset($trxnArray['NewOrderResp'])){
			
			//Success
			if($trxnArray['NewOrderResp']['ProcStatus'] == 0 && $trxnArray['NewOrderResp']['ApprovalStatus'] == 1){
				$trxnResponse['status'] = 1;
				$trxnResponse['approved'] = 1;	
				$trxnResponse['auth_code'] = (!empty($trxnArray['NewOrderResp']['AuthCode']) && !is_array($trxnArray['NewOrderResp']['AuthCode']) ? $trxnArray['NewOrderResp']['AuthCode'] : NULL);	
			}
			
			//Response
			$trxnResponse['txn_num'] = (!empty($trxnArray['NewOrderResp']['TxRefNum']) && !is_array($trxnArray['NewOrderResp']['TxRefNum']) ? $trxnArray['NewOrderResp']['TxRefNum'] : NULL);
			$trxnResponse['response_code'] = (!empty($trxnArray['NewOrderResp']['RespCode']) && !is_array($trxnArray['NewOrderResp']['RespCode']) ? $trxnArray['NewOrderResp']['RespCode'] : NULL);
			$trxnResponse['message'] = (!empty($trxnArray['NewOrderResp']['StatusMsg']) && !is_array($trxnArray['NewOrderResp']['StatusMsg']) ? $trxnArray['NewOrderResp']['StatusMsg'] : (!empty($trxnArray['NewOrderResp']['RespMsg']) && !is_array($trxnArray['NewOrderResp']['RespMsg']) ? $trxnArray['NewOrderResp']['RespMsg'] : ''));
			$trxnResponse['txn_tag'] = (!empty($trxnArray['NewOrderResp']['TxRefIdx']) && !is_array($trxnArray['NewOrderResp']['TxRefIdx']) ? $trxnArray['NewOrderResp']['TxRefIdx'] : NULL);
			
		//Bad request
		}else if(isset($trxnArray['QuickResponse'])){
			$trxnResponse['status_code'] = $trxnArray['QuickResponse']['ProcStatus'];
			$trxnResponse['message'] = $trxnArray['QuickResponse']['StatusMsg'];
		}
	}
		
	
/************************ Void transaction ************************/
}else if($request['type'] == 'reversalRequest'){
	
	//Format request
	$trxnRequest = '<Request>
		<Reversal>
			<TxRefNum>' .$request['txn_num']. '</TxRefNum>
			<TxRefIdx>' .$request['txn_tag']. '</TxRefIdx>
			<AdjustedAmt>' .number_format($request['refund_amount'], 2, '', ''). '</AdjustedAmt>
			<OrderID>' .$request['ordernum']. '</OrderID>
			<BIN>' .$conn_bin. '</BIN>
			<MerchantID>' .$merchant_id. '</MerchantID>
			<TerminalID>' .$terminal_id. '</TerminalID>
		</Reversal>
	</Request>';
		
	//Send request
	$trxnArray = curl_request($trxnRequest);
	if(is_array($trxnArray) && !empty($trxnArray)){
		if(isset($trxnArray['ReversalResp'])){
			if($trxnArray['ReversalResp']['ProcStatus'] == 0){
				$trxnResponse['status'] = 1;
			}else{
				$trxnResponse['status_code'] = $trxnArray['ReversalResp']['ProcStatus'];
			}
			
			//Response
			$trxnResponse['txn_num'] = (!empty($trxnArray['ReversalResp']['TxRefNum']) && !is_array($trxnArray['ReversalResp']['TxRefNum']) ? $trxnArray['ReversalResp']['TxRefNum'] : NULL);
			$trxnResponse['txn_tag'] = (!empty($trxnArray['ReversalResp']['TxRefIdx']) && !is_array($trxnArray['ReversalResp']['TxRefIdx']) ? $trxnArray['ReversalResp']['TxRefIdx'] : NULL);
			$trxnResponse['message'] = (!empty($trxnArray['ReversalResp']['StatusMsg']) && !is_array($trxnArray['ReversalResp']['StatusMsg']) ? $trxnArray['ReversalResp']['StatusMsg'] : '');
		
		//Bad request
		}else if(isset($trxnArray['QuickResponse'])){
			$trxnResponse['status_code'] = $trxnArray['QuickResponse']['ProcStatus'];
			$trxnResponse['message'] = $trxnArray['QuickResponse']['StatusMsg'];
		}
	}

}

//Clear result
$trxnArray = array();
$trxnResult = NULL;
	
?>