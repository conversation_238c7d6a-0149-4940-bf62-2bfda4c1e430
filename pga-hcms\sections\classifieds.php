<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Classified Ads  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";
		
			echo "<thead>";
			echo "<th width='350px'>Title</th>";
			echo "<th width='200px'>Location</th>";
			echo "<th width='200px'>Posted By</th>";
			echo "<th width='70px'>Visible</th>";
			echo "<th width='70px' class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				//Display seo score
				if($cms_settings['enhanced_seo'] && (array_key_exists(3, $Account->roles) || array_key_exists(4, $Account->roles))){ //SEO and master permissions
					if($row['seo_score'] > 80){
						$seo_class = "seo-3";
					}else if($row['seo_score'] >= 50 && $row['seo_score'] <= 80){
						$seo_class = "seo-2";
					}else{
						$seo_class = "seo-1";
					}
					$seo_tooltip = "<span class='seo-tool tooltip' title='<h4>SEO Score: <strong>".number_format($row['seo_score'],1)."</strong></h4>'>&nbsp;</span>";
				}else{
					$seo_class = "";
					$seo_tooltip = "";
				}

				echo "<tr class='".$seo_class."'>";
					echo "<td>".$seo_tooltip.$row['title']. "</td>";
					echo "<td>".$row['facility_name']."</td>";
					echo "<td>".(!empty($row['posted_by']) ? $row['posted_by'] : $row['first_name'].' '.$row['last_name'])."</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager(50);
		
		echo "</div>";	
	echo "</div>";

} else {

	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		$file = $data['file_name'];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	} else if(ACTION == 'add' && !isset($_POST['save'])){	
		$file = '';
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";
		// Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Ad Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				<div class='panel-switch'>
					<label>Show $record_name</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " />
						<label for='showhide'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Title <span class='required'>*</span></label>
					<input type='text' name='title' id='button-text' value='" .(isset($row['title']) ? $row['title'] : ''). "' class='input".(in_array('title', $required) ? " required" : "")."' />
				</div>
				<div class='form-field'>
					<label>Facility <span class='required'>*</span></label>
					<select name='facility_id' class='select".(in_array('facility_id', $required) ? " required" : "")."'>
						<option value=''>- Select -</option>";
						foreach($facilities as $facility){
							echo "<option value='".$facility['facility_id']."'".(isset($row['facility_id']) && $row['facility_id'] == $facility['facility_id'] ? " selected" : "").">".$facility['facility_name']."</option>";
						}
					echo "</select>
				</div>
				<div class='form-field'>
					<label>Availability <span class='required'>*</span>" .$CMSBuilder->tooltip('Availability', '<b>Public:</b><br>Everyone can apply and view the position.<br><br><b>Members Only: </b><br>Only registered PGA Members can view and apply to the position.'). "</label>
					<select name='public' class='select'>
						<option value='1'".(isset($row['public']) && $row['public'] == 1 ? " selected" : "").">Public</option>
						<option value='0'".(isset($row['public']) && $row['public'] == 0 ? " selected" : "").">Members Only</option>
					</select>
				</div>";
			echo "</div>";
		echo "</div>"; // END Details

		//Contact Information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Contact Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>
				<div class='form-field'>
					<label for='first_name'>First Name </small><span class='required'>*</span></label>
					<input type='text' name='first_name' value='" .(isset($row['first_name']) ? $row['first_name'] : ""). "' class='input".(in_array('first_name', $required) ? " required" : "")."' />
				</div>

				<div class='form-field'>
					<label for='last_name'>Last Name </small><span class='required'>*</span></label>
					<input type='text' name='last_name' value='" .(isset($row['last_name']) ? $row['last_name'] : ""). "' class='input".(in_array('last_name', $required) ? " required" : "")."' />
				</div>

				<div class='form-field'>
					<label for='phone'>Phone </small><span class='required'>*</span></label>
					<input type='text' name='phone' value='" .(isset($row['phone']) ? $row['phone'] : ""). "' class='input".(in_array('phone', $required) ? " required" : "")."' />
				</div>

				<div class='form-field'>
					<label for='email'>Email </small><span class='required'>*</span></label>
					<input type='text' name='email' value='" .(isset($row['email']) ? $row['email'] : ""). "' class='input".(in_array('email', $required) ? " required" : "")."' />
				</div>
			</div>
		</div>"; //Contact Information
	
		//Upload file
		echo "<div class='panel page-content" .(isset($row['type']) && $row['type'] == 1 ? " hidden" : ""). "'>";
			echo "<div class='panel-header'>PDF Document
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Upload File" .$CMSBuilder->tooltip('Upload File', 'File size must be smaller than 20MB.'). "</label>
					<input type='file' class='input" .(in_array('file', $required) ? ' required' : ''). "' name='file' value='' />
					<input type='hidden' name='old_file' value='" .(isset($file) && $file != '' && file_exists($filedir.$file) ? $file : ''). "' />
				</div>";
				if(isset($file) && $file != ''){
					echo "<p class='clear'>";
					
						//Public directory
						if(file_exists($filedir.$file)){
							echo "<a href='".$path.$filedir.$file."' target='_blank'><i class='fa fa-download'></i> Download Current File</a>";
						}
					
						echo " &nbsp; <input type='checkbox' class='checkbox' name='deletefile' id='deletefile' value='1'>
						<label for='deletefile'>Delete Current File</label>";
					
					echo "</p>";
				}
			echo "</div>";
		echo "</div>"; //Upload file

		// Content
		echo "<div class='panel'>";
			echo "<div class='panel-header".(in_array('description', $required) ? " required" : "")."'>Description <span class='required'>*</span>
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content'>";
				echo "<textarea name='description' class='tinymceMini'>".(isset($row['description']) ? $row['description'] : "")."</textarea>";
			echo "</div>";
		echo "</div>"; // END Content
	
		// //SEO Content/Analysis
		// if($cms_settings['enhanced_seo'] && (array_key_exists(3, $Account->roles) || array_key_exists(4, $Account->roles))){ //SEO and master permissions
		// 		include('includes/widgets/seotabs.php');
		// }//seo permissions

		

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "<input type='hidden' name='keep_tags[]' value='description' />";
//SEO Content/Analysis
		include('includes/widgets/seotabs.php');
		//Sticky footer
		include("includes/widgets/formbuttons.php");
	echo "</form>";

}

?>