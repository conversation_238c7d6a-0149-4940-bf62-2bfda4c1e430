<?php

//Dashboard widget
if(SECTION_ID == 4){
	$total_records = $db->get_record_count('blog_comments');
	$CMSBuilder->set_widget($_cmssections['blog_comments'], 'Total '.$sitemap[$_cmssections['blog']]['name'].' Comments', $total_records);
}

if(SECTION_ID == $_cmssections['blog_comments']){

	//Define vars
	$record_db = 'blog_comments';
	$record_id = 'comment_id';
	$blog_page = get_page_url($_sitepages['blog']);
	
	//Get blog comments (pending)
	$params = array(0);

	if($searchterm != ""){
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
	}
	$db->query("SELECT `$record_db`.*, `blog_entries`.`title`, `blog_entries`.`page`, `blog_entries`.`post_date` FROM `$record_db` LEFT JOIN `blog_entries` ON `blog_entries`.`entry_id` = `$record_db`.`entry_id` WHERE approved = ?" .($searchterm != "" ? " AND `name` LIKE ? OR `message` LIKE ? OR `email` LIKE ?" : ""). " ORDER BY `$record_db`.`date_added` DESC, `$record_id`", $params);
	$blogcomments_pending = $db->fetch_assoc($record_id);

	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}
	
	//Get blog comments (approved)
	$params = array(1);

	if($searchterm != ""){
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
	}
	$db->query("SELECT `$record_db`.*, `blog_entries`.`title`, `blog_entries`.`page`, `blog_entries`.`post_date` FROM `$record_db` LEFT JOIN `blog_entries` ON `blog_entries`.`entry_id` = `$record_db`.`entry_id` WHERE approved = ?" .($searchterm != "" ? " AND `name` LIKE ? OR `message` LIKE ? OR `email` LIKE ?" : ""). " ORDER BY `$record_db`.`date_added` DESC, `$record_id`", $params);
	$blogcomments_approved = $db->fetch_assoc($record_id);

	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}
	
}

?>