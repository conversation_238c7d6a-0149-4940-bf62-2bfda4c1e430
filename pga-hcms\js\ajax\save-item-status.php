<?php

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

if(isset($_POST) && USER_LOGGED_IN){
		
	extract($_POST); //get plain variables

	if(!isset($item_col)){
		$item_col = "showhide";
	}
	
	//update item showhide status
	$params = array($item_status, $item_id);
	$db->query("UPDATE $table SET $item_col = ? WHERE $table_id = ?",$params);
	
	if(!$db->error()){
		echo $CMSBuilder->mini_alert("<p>Item status successfully saved!</p>",true);
	} else {
		echo $CMSBuilder->mini_alert("<p>There was an error updating this item status: ".$db->error()."</p>",false);
	}
	
} else {
	echo 'error';
}
	
?>