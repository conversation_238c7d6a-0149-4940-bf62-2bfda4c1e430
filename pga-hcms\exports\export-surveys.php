<?php  

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../includes/utils.php");

if(isset($_GET) && USER_LOGGED_IN){
	
	//Ensure correct role
	if(!$CMSBuilder->check_permissions(77) && !MASTER_USER){
		die('Access Denied');
	}

	//Define vars
	$record_db = 'compensation_survey';
	$record_id = 'survey_id';
	$record_name = 'Submission';
	$records_arr = array();

	$db_columns = array(); //for SELECT in query
	$table_columns = array(); //for listing label

	$params = array();
	$wheretxt = "";
	$querytxt = "";

	//Set columns to get
	$db_columns = array(
		'pga_number',
		'first_name',
		'last_name',
		'class_name',
		'facility_name',
		'facility_city',
		'facility_region',
		'facility_type',
		'facility_fee',
		'work_days1',
		'work_hours1',
		'work_days2',
		'work_hours2',
		'lesson_hours',
		'lesson_percent',
		'lesson_rate',
		'lesson_rate_time',
		'lesson_total',
		'monthly_salary',
		'months',
		'vacation_days',
		'vacation_total',
		'amount_association',
		'amount_medical',
		'amount_tournament',
		'amount_meal',
		'amount_education',
		'amount_clothing',
		'amount_equipment',
		'amount_vehicle',
		'amount_bonus',
		'salary_total',
		'benefits_total',
		'total',
		'approved',
		'date_approved',
		'date_added',
		'last_updated',
		'year'
	);
	$table_columns = array(
		'Member No.',
		'First Name',
		'Last Name',
		'Classification',
		'Facility Name',
		'Facility City',
		'Facility Region',
		'Facility Type',
		'W/D Green Fee',
		'Apr-Oct Work Days Per Week',
		'Apr-Oct Work Hours Per Week',
		'Nov-Mar Work Days Per Week',
		'Nov-Mar Work Hours Per Week',
		'Lesson Hours Per Week',
		'Lesson % Taught During Work Hours',
		'Lesson Rate',
		'Lesson Interval',
		'Total Teaching Revenue',
		'Monthly Salary',
		'Length of Employment',
		'Vacation Days',
		'Vacation Total',
		'Association Dues',
		'Medical/Dental',
		'Tournament Allowance',
		'Meal Allowance',
		'Education Allowance',
		'Clothing Allowance',
		'Equipment Allowance',
		'Car Allowance',
		'Bonus/Profit Sharing',
		'Total Annual Salary',
		'Total Additional Benefits',
		'Total Salary & Benefits',
		'Approved',
		'Date Approved',
		'Date Submitted',
		'Last Updated',
		'Year'
	);
	
	//Search term
	if(isset($_GET['search']) && $_GET['search'] != ""){
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."(CONCAT(`account_profiles`.`first_name`, ? ,`account_profiles`.`last_name`) LIKE ? || `facilities`.`facility_name` LIKE ? || `membership_classes`.`class_name` LIKE ?)";
		$params[] = ' ';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
	}

	//Filter by dates
	$date_range = '';
	if(isset($_GET['start_date']) && $_GET['start_date'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`date_added` >= ?";
		$params[] = date('Y-m-d 00:00:00', strtotime($_GET['start_date']));
		$date_range .= date('M j, Y', strtotime($_GET['start_date']));
	}
	if(isset($_GET['end_date']) && $_GET['end_date'] != '') {
		$wheretxt .= (trim($wheretxt) == "" ? " WHERE " : " AND ")."`$record_db`.`date_added` <= ?";
		$params[] = date('Y-m-d 23:59:59', strtotime($_GET['end_date']));
		$date_range .= (!empty($date_range) ? ' - ' : '').date('M j, Y', strtotime($_GET['end_date']));
	}

	//Create query
	$querytxt = "SELECT `$record_db`.*, `accounts`.`email`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `account_profiles`.`pga_number`, `membership_classes`.`class_name`, `facilities`.`facility_name`, `facilities`.`city` AS `facility_city`, `facilities`.`region` AS `facility_region`, `facilities`.`type` AS `facility_type` FROM `$record_db` ".
	"LEFT JOIN `accounts` ON `accounts`.`account_id` = `$record_db`.`account_id` ".
	"LEFT JOIN `account_profiles` ON `account_profiles`.`account_id` = `$record_db`.`account_id` ".
	"LEFT JOIN `membership_classes` ON `membership_classes`.`class_id` = `$record_db`.`class_id` ".
	"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `$record_db`.`facility_id`".
	$wheretxt." ORDER BY `$record_db`.`date_added` DESC";
	$query = $db->query($querytxt, $params);
	if($query && !$db->error() && $db->num_rows() > 0){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}
	
	//Legacy query
	$querytxt = "SELECT `compensation_archive`.*, `accounts`.`email`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `account_profiles`.`pga_number`, `membership_classes`.`class_name`, `facilities`.`facility_name`, `facilities`.`city` AS `facility_city`, `facilities`.`region` AS `facility_region`, `facilities`.`type` AS `facility_type` FROM `compensation_archive` ".
	"LEFT JOIN `accounts` ON `accounts`.`account_id` = `compensation_archive`.`account_id` ".
	"LEFT JOIN `account_profiles` ON `account_profiles`.`account_id` = `compensation_archive`.`account_id` ".
	"LEFT JOIN `membership_classes` ON `membership_classes`.`class_id` = `compensation_archive`.`class_id` ".
	"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `compensation_archive`.`facility_id`".
	str_replace($record_db, 'compensation_archive', $wheretxt)." ORDER BY `compensation_archive`.`date_added` DESC";
	$query = $db->query($querytxt, $params);
	if($query && !$db->error() && $db->num_rows() > 0){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}

	//Compile records
	$csv_rows = array();
	foreach($records_arr as $row) {
		$data = array();
		foreach($db_columns as $column) {
			if($column == 'date_approved' || $column == 'date_added' || $column == 'last_updated') {
				$data[] = ($row[$column] != '' && $row[$column] != '0000-00-00' && $row[$column] != '0000-00-00 00:00:00' ? date('Y-m-d', strtotime($row[$column])) : "");
			} else if($column == 'year') {
				$data[] = ($row['date_added'] != '' && $row['date_added'] != '0000-00-00' && $row['date_added'] != '0000-00-00 00:00:00' ? date('Y', strtotime($row['date_added'])) : "");
			} else if($column == 'approved') {
				$data[] = ($row[$column] == '1' ? 'Yes' : 'No');
			} else {
				$data[] = $row[$column];
			}
		}
		$csv_rows[] = $data;
	}

	//Output CSV
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=compensation-".date("Ymd").".csv");
	header("Content-Transfer-Encoding: binary ");
	
	$fp = fopen('php://output', 'w');
	
	//Data
	fputcsv($fp, str_replace("&rsquo;", "'", $table_columns));
	foreach($csv_rows as $row) {
		fputcsv($fp, str_replace("&rsquo;", "'", $row));
	}
	fputcsv($fp, array(''));
	
	//Footer
	$footer = array('Date Exported: ', date('M j, Y'));
	fputcsv($fp, $footer);
	if(!empty($date_range)){
		$footer = array('Date Filter: ', $date_range);
		fputcsv($fp, $footer);
	}
	if(isset($_GET['search']) && $_GET['search'] != ""){
		$footer = array('Search Filter: ', '`'.str_replace("&rsquo;", "'", $_GET['search']).'`');
		fputcsv($fp, $footer);
	}
	
	fclose($fp);

} 

?>