<?php

if(PAGE_ID == $_sitepages['reg_checkout']['page_id']){
	//Define vars
	$panel_id = 93;//61;
	$required = array();
	$errors = array();
	$waiver_forms = array();
	$pay_later_option = true;
	$pay_now_option = true;
	$payment_options = $Registration->get_payment_options();
	$has_admin_fee = false;
			
	//Compile checkout cart
	$checkout_cart = array();
	$team_events = array();
	$ordertotal = 0;
	if(isset($_POST['checkout']) || isset($_POST['applycode'])){
		if(isset($_POST['cart_items'])){
			$_SESSION['reg']['checkout']['cart_items'] = $_POST['cart_items'];
		}
	}
	if(isset($_SESSION['reg']['checkout']['cart_items']) && !empty($_SESSION['reg']['checkout']['cart_items'])){
		foreach($_SESSION['reg']['checkout']['cart_items'] as $item_id){
			$item = $ShoppingCart->get_cart_item($item_id);
			if(!empty($item)){
				$checkout_cart[$item_id] = $item;
				
				//Set event type
				if(!defined('EVENT_TYPE')){
					define('EVENT_TYPE', $item['event_type']);
				}
				
				//Check for team events
				if($item['event_type'] == 2 && $item['team_event']){
					$team_events[$item_id] = $item;
				}
				
				//Check for waivers
				$waivers = $Registration->get_event_waivers($item['event_id']);
				foreach($waivers as $waiver){
					$waiver_forms[$waiver['waiver_id']] = $waiver;
				}
				
				//Check if pay later option
				if(EVENT_TYPE == 2 && $item['payment_deadline'] <= date("Y-m-d")){
					$pay_later_option = false;
				}
				
				//Check if has admin fee
				if($item['admin_fee'] > 0){
					$has_admin_fee = true;
				}
				
				//Apply promo code
				if(EVENT_TYPE != 2){
					if(isset($_POST['applycode'])){
						$promocode = $_POST['promocode'];
						$discount = $ShoppingCart->get_discount($promocode, USER_LOGGED_IN, $item['event_id']);
						if(!empty($discount)){
							$item['promocode'] = strtoupper($promocode);
							$item['subtotal'] = ($item['subtotal']+$item['discount']); //Put subtotal back up to full value since we are replacing current discount, not adding to it
							$item['discount'] = 0;
							
							//Apply discount to each attendee
							foreach($item['attendees'] as $aid=>$attendee){
								$attendee_subtotal = $attendee['ticket_price'];
								if(isset($attendee['addons']) && !empty($attendee['addons'])){
									foreach($attendee['addons'] as $addon){
										$attendee_subtotal += $addon['price_adjustment'];
									}
								}
								
								//Discount percentage
								if($discount['discount_type'] == 'Percent'){
									$item['attendees'][$aid]['discount'] = number_format($attendee_subtotal*($discount['discount']/100), 2, '.', '');
									$item['discount'] += $item['attendees'][$aid]['discount'];

								//Discount amount
								}else{
									if($discount['discount'] > $item['subtotal']){
										$discount['discount'] = $item['subtotal']; //Cannot exceed total dollar amount
									}
									$percent_each = ($discount['discount']/$item['subtotal']);
									$item['attendees'][$aid]['discount'] = number_format($attendee_subtotal*$percent_each, 2, '.', '');
									$item['discount'] += $item['attendees'][$aid]['discount'];
								}
								
							}
							
						}else{
							$item['promocode'] = '';
							$item['discount'] = 0;
							foreach($item['attendees'] as $aid=>$attendee){
								$item['attendees'][$aid]['discount'] = 0;
							}
							$_SESSION['cart_error'] = 'Unable to apply discount. Invalid code' .($promocode != '' ? '`'.$promocode.'`' : ''). '.';
						}
						
						try{
							$ShoppingCart->update_cart_item($item, $item_id);
						}catch(Exception $e){
							$_SESSION['cart_error'] = 'Unable to apply discount. '.$e->getMessage();
						}
						
						
						header('Location: '.$_sitepages['reg_cart']['page_url']);
						exit();
					}
				}
				
				//Determine order total
				$ordertotal += $item['total'];
				
			}
		}
	}
	
	//Must be logged in
	if(!USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.base64_encode($_SERVER['REQUEST_URI']));
		exit();
	}
			
	//If no cart items set, redirect
	if(empty($checkout_cart)){
		unset($_SESSION['reg']);
		header('Location: ' .$_sitepages['reg_cart']['page_url']);
		exit();
	}
	
	//Check order total against maximum allowed (unless admin fee is being applied)
	if(!$has_admin_fee && $ordertotal > $reg_settings['max_payment_amount'] && !empty($reg_settings['max_payment_amount'])){
		$pay_now_option = false;
		$pay_later_option = true; //They have to be allowed to pay later if they can't pay now
		
	}else if($ordertotal == 0){ //No payment required so treat as pay now
		$pay_now_option = true;
		$pay_later_option = false;
	}
	
	//Terms of use
	if($_sitepages['terms']['showhide'] < 2){
		$termspanels = $SiteBuilder->get_page_panels($_sitepages['terms']['page_id']);
		if(isset($termspanels[93])){
			$waiver_forms[] = array(
				'waiver_id' => 'terms',
				'event_type' => NULL,
				'title' => $_sitepages['terms']['page_title'],
				'description' => (trim($termspanels[93]['title']) != '' && $termspanels[93]['show_title'] ? strtoupper($termspanels[93]['title']).':<br />' : '').strip_tags($termspanels[93]['content']),
				'file_name' => NULL,
				'required' => 1,
				'showhide' => 0
			);
		}
	}
	
	//Fetch billing profiles
	$billing_profiles = array();
	if(USER_LOGGED_IN){
		$billing_profiles = $Account->get_account_billing_profiles(USER_LOGGED_IN);
	}
	
	//Submit payment
	if(isset($_POST['payment'])){
		
		//Session cookie
		if(empty($_POST['xid']) || ($_POST['xid'] != $_COOKIE['xid'])){
			$errors[] = 'Invalid session cookie. Please make sure cookies are enabled in your browser then try again.';
			
		}else{
			
			//Set state/region
			if($_POST['country'] == 'US'){
				$_POST['province'] = $_POST['state'];
			}else if($_POST['country'] != 'CA' && $_POST['country'] != 'US' && $_POST['country'] != ''){
				$_POST['province'] = $_POST['region'];
			}
			if($_POST['bill_country'] == 'US'){
				$_POST['bill_province'] = $_POST['bill_state'];
			}else if($_POST['bill_country'] != 'CA' && $_POST['bill_country'] != 'US' && $_POST['bill_country'] != ''){
				$_POST['bill_province'] = $_POST['bill_region'];
			}
			
			//Validation
			$required_fields = array('first_name', 'last_name', 'email', 'phone');
			if(EVENT_TYPE != 2){
				$required_fields = array_merge($required_fields, array('address1', 'city', 'province', 'postal_code', 'country'));	
			}
			if($ordertotal > 0){
				if(!isset($_POST['billing_id']) || empty($_POST['billing_id'])){
					if(EVENT_TYPE == 2 || (EVENT_TYPE != 2 && $pay_now_option)){
						$required_fields = array_merge($required_fields, array('bill_address1', 'bill_city', 'bill_province', 'bill_postalcode', 'bill_country', 'ccname', 'ccnumber', 'exp_month', 'exp_year', 'cvv'));	
					}
				}else{
					$required_fields[] = 'billing_id';
				}
			}
			foreach($required_fields as $field){
				if(!isset($_POST[$field]) || $_POST[$field] == ''){
					$errors[0] = 'Please fill out all the required fields.';
					$required[] = $field;
				}
				if($field == 'email' && !checkmail($_POST[$field])) {
					$errors[] = 'Please enter a valid email address.';
					$required[] = $field;
				}
				if($field == 'phone'){
					if(!detectPhone($_POST[$field])){
						$errors[] = 'Please enter a valid phone number.';
						$required[] = $field;
					}else{
						$_POST['phone'] = formatPhoneNumber($_POST['phone']);
					}
				}
				if($field == 'ccnumber' && $_POST[$field] != ''){
					$valid_card = false;
					$accepted_cards = array();
					foreach($payment_options as $payopt){
						$accepted_cards[] = $payopt['name'];
						if(get_card_type($_POST[$field]) == $payopt['type']){
							$valid_card = true;
						}
					}
					if(!$valid_card){
						$errors[] = 'Invalid card type. We currently only accept '.implode(' &amp; ', $accepted_cards).'.';
						$required[] = $field;	
					}
				}
			}
				
			//Save session vars
			$_SESSION['reg']['checkout']['payment'] = (!$pay_later_option ? 1 : (!$pay_now_option ? 0 : $_POST['payment']));
			$_SESSION['reg']['checkout']['first_name'] = $_POST['first_name'];
			$_SESSION['reg']['checkout']['last_name'] = $_POST['last_name'];
			$_SESSION['reg']['checkout']['email'] = $_POST['email'];
			$_SESSION['reg']['checkout']['phone'] = $_POST['phone'];
			$_SESSION['reg']['checkout']['company'] = (isset($_POST['company']) ? $_POST['company'] : '');
			$_SESSION['reg']['checkout']['facility'] = (isset($_POST['facility']) ? $_POST['facility'] : '');
			$_SESSION['reg']['checkout']['address1'] = (isset($_POST['address1']) ? $_POST['address1'] : '');
			$_SESSION['reg']['checkout']['address2'] = (isset($_POST['address2']) ? $_POST['address2'] : '');
			$_SESSION['reg']['checkout']['city'] = (isset($_POST['city']) ? $_POST['city'] : '');
			$_SESSION['reg']['checkout']['province'] = (isset($_POST['province']) ? $_POST['province'] : '');
			$_SESSION['reg']['checkout']['postal_code'] = (isset($_POST['postal_code']) ? $_POST['postal_code'] : '');
			$_SESSION['reg']['checkout']['country'] = (isset($_POST['country']) ? $_POST['country'] : '');
			
			//Set billing
			if(!isset($_POST['billing_id']) || empty($_POST['billing_id']) || $ordertotal == 0){
				$_SESSION['reg']['checkout']['billing_id'] = '';
				$_SESSION['reg']['checkout']['ref_number'] = '';
				$_SESSION['reg']['checkout']['bill_address1'] = (isset($_POST['bill_address1']) ? $_POST['bill_address1'] : '');
				$_SESSION['reg']['checkout']['bill_address2'] = (isset($_POST['bill_address2']) ? $_POST['bill_address2'] : '');
				$_SESSION['reg']['checkout']['bill_city'] = (isset($_POST['bill_city']) ? $_POST['bill_city'] : '');
				$_SESSION['reg']['checkout']['bill_province'] = (isset($_POST['bill_province']) ? $_POST['bill_province'] : '');
				$_SESSION['reg']['checkout']['bill_postalcode'] = (isset($_POST['bill_postalcode']) ? $_POST['bill_postalcode'] : '');
				$_SESSION['reg']['checkout']['bill_country'] = (isset($_POST['bill_country']) ? $_POST['bill_country'] : '');
				$_SESSION['reg']['checkout']['ccname'] = (isset($_POST['ccname']) ? $_POST['ccname'] : '');
				$_SESSION['reg']['checkout']['cctype'] = (isset($_POST['ccnumber']) ? get_card_type($_POST['ccnumber']) : '');
				$_SESSION['reg']['checkout']['ccnumber'] = NULL;
				$_SESSION['reg']['checkout']['exp_month'] = (isset($_POST['exp_month']) ? $_POST['exp_month'] : '');
				$_SESSION['reg']['checkout']['exp_year'] = (isset($_POST['exp_year']) ? $_POST['exp_year'] : '');
				$_SESSION['reg']['checkout']['ccsave'] = (isset($_POST['ccsave']) ? 1 : 0);

			//Use profile
			}else{
				$profile = $billing_profiles[$_POST['billing_id']];

				$_SESSION['reg']['checkout']['billing_id'] = $_POST['billing_id'];
				$_SESSION['reg']['checkout']['ref_number'] = $profile['ref_number'];
				$_SESSION['reg']['checkout']['bill_address1'] = $profile['bill_address1'];
				$_SESSION['reg']['checkout']['bill_address2'] = $profile['bill_address2'];
				$_SESSION['reg']['checkout']['bill_city'] = $profile['bill_city'];
				$_SESSION['reg']['checkout']['bill_province'] = $profile['bill_province'];
				$_SESSION['reg']['checkout']['bill_postalcode'] = $profile['bill_postalcode'];
				$_SESSION['reg']['checkout']['bill_country'] = $profile['bill_country'];
				$_SESSION['reg']['checkout']['ccname'] = $profile['ccname'];
				$_SESSION['reg']['checkout']['cctype'] = $profile['cctype'];
				$_SESSION['reg']['checkout']['ccnumber'] = $profile['ccnumber']; //Last 4 digits only
				$_SESSION['reg']['checkout']['exp_month'] = substr($profile['ccexpiry'], 0, 2);
				$_SESSION['reg']['checkout']['exp_year'] = substr($profile['ccexpiry'], -2, 2);
				$_SESSION['reg']['checkout']['ccsave'] = 0;
			}
			
			//Validate waivers
			if(!empty($waiver_forms)){
				$waiver_error = false;
				foreach($waiver_forms as $waiver){
					if($waiver['required'] && !isset($_POST['waiver-'.$waiver['waiver_id']])){
						$waiver_error = true;
						$required[] = 'waiver-'.$waiver['waiver_id'];
					}
					$_SESSION['reg']['checkout']['waivers'][$waiver['waiver_id']] = (isset($_POST['waiver-'.$waiver['waiver_id']]) ? 1 : 0);
				}
				if($waiver_error){
					$errors[] = 'Please ensure you read and agree/consent to all the required terms and conditions.';
				}
			}
			
			//Set partners for team events
			foreach($team_events as $item_id=>$item){
				if(isset($_POST['partner_id'][$item_id])){
					$update_item = $item;
					
					//Selected member
					if($_POST['partner_id'][$item_id] != ''){
						$partner_account = $Account->get_account_profile($_POST['partner_id'][$item_id]);
						if(!empty($partner_account)){
							$update_item['attendees'][0]['partner'] = array(
								'account_id' => $_POST['partner_id'][$item_id],
								'first_name' => $partner_account['first_name'],
								'last_name' => $partner_account['last_name'],
								'email' => $partner_account['email'],
								'phone' => $partner_account['phone'],
								'gender' => $partner_account['gender'],
								'handicap' => (isset($_POST['partner_handicap'][$item_id]) ? $_POST['partner_handicap'][$item_id] : ''),
								'attendee_sharing' => '1',
								'ticket_type' => 'Partner Fee',
								'ticket_price' => 0,
								'addons' => array()
							);
						}
						
					//Non member
					}else{
						if($_POST['partner_first_name'][$item_id] != '' && $_POST['partner_last_name'][$item_id] != ''){
							$update_item['attendees'][0]['partner'] = array(
								'account_id' => NULL,
								'first_name' => $_POST['partner_first_name'][$item_id],
								'last_name' => $_POST['partner_last_name'][$item_id],
								'email' => NULL,
								'phone' => NULL,
								'gender' => NULL,
								'handicap' => (isset($_POST['partner_handicap'][$item_id]) ? $_POST['partner_handicap'][$item_id] : ''),
								'attendee_sharing' => '1',
								'ticket_type' => 'Partner Fee',
								'ticket_price' => 0,
								'addons' => array()
							);
						}
					}
					
					//Update cart item with partner
					$team_events[$item_id] = $update_item;
					$ShoppingCart->update_cart_item($update_item, $item_id);
				}
			}
			
			//Send to confirm page
			if(empty($errors)){
				echo '<html>
				<head><title>Loading... Please wait...</title></head> 
				<body>
				<form name="ccform" id="ccform" action="' .$_sitepages['reg_confirm']['page_url']. '" method="post">
					<input type="hidden" name="ccnumber" value="' .$_POST['ccnumber']. '" />
					<input type="hidden" name="cvv" value="' .$_POST['cvv']. '" />
					<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
				</form><script type="text/javascript">document.ccform.submit();</script>
				</body>
				</html>';
				exit();
			}
			
		}
		
	}
		
	//Errors
	if(!empty($errors)){
		$alert = $Account->alert(implode('<br />', $errors), false);
	}
	
}

?>