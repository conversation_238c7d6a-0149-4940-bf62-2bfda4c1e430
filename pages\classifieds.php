<?php

//List all Classifieds
if(!isset($classified_id) || $classified_id == ''){ 	
	$html = '';
	//Load Panel Content
	// $html = (trim($page['page_panels'][$panel_id]['content']) != '' ? $page['page_panels'][$panel_id]['content'].'<hr />' : '');
	$html .= '<div id="classifieds">';
	
	//Create Filtering form
	$html .= '<form id="filter-form" class="right-column">';
	
		//Login prompt
		if(!USER_LOGGED_IN){
			$html .= '<p><small>Members <a href="' .$_sitepages['login']['page_url']. '?redirect=' .urlencode($_SERVER['REQUEST_URI']). '">login here</a> for full access.</small></p>';
		}
	
		$html .= '<p>
			<a href="' .$_sitepages['my_classifields']['page_url']. '">Create a Listing</a> 
			<span class="divider color-light">|</span> 
			<a href="' .$_sitepages['my_classifields']['page_url']. '">Edit My Listings</a>
		</p>';
	
		$html .= '<h6>Ad Search</h6>
		<fieldset class="search-classified">
			<input type="text" placeholder="Search" name="filter_search" value="'.(isset($_GET['filter_search']) ? $_GET['filter_search'] : '').'" class="input side nomargin" id="filter_search">
			<button type="submit" name="submit" id="submit" class="button solid side submit nomargin"><i class="fa fa-search"></i></button>
		</fieldset>

		<fieldset>';

		//Filter by Facility
		if(!empty($facilities)){
			$html .= '<div class="dropdown-checkbox-wrapper">
				<label class="title side" data-default="Showing all Facilities">Showing all Facilities</label>
				<a class="button solid side submit"><i class="fa fa-angle-down"></i></a>

				<ul id="facility-dropdown" class="checklist clearfix">';

				//Create a checkbox for each facility in the database
				foreach ($facilities as $key => $facility){
					$html .= '<li title="'.$facility['facility_name'].'"><input name="facility_ids[]" id="facility'.$key.'" class="checkbox" type="checkbox" value="'.$facility['facility_id'].'"'. (isset($_GET['facility_ids']) && in_array($facility['facility_id'], $_GET['facility_ids']) ? 'checked' : '') .'><label for="facility'.$key.'">'.$facility['facility_name'].'</label></li>';
				}

			$html .= '</ul>
			</div>';
		}

		//Filter by Province
		$html .= '<div class="dropdown-checkbox-wrapper">
			<label class="title side" data-default="Showing all Provinces">Showing all Provinces</label>
			<a class="button solid side submit"><i class="fa fa-angle-down"></i></a>

			<ul id="province-dropdown" class="checklist clearfix">
				<li title="Alberta" ><input name="province[]" id="province1" class="checkbox" type="checkbox" value="AB"'. (isset($_GET['province']) && in_array('AB', $_GET['province']) ? 'checked' : '') .'><label for="province1">Alberta</label></li>
				<li title="Outside" ><input name="province[]" id="province2" class="checkbox" type="checkbox" value="Outside"'. (isset($_GET['province']) && in_array('Outside', $_GET['province']) ? 'checked' : '') .'><label for="province2">Outside Province</label></li>
			</ul>
			
			</div>
		
			<p class="nopadding"><button type="submit" name="submit" id="submit" class="button primary back-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Filter</button>
			<a href="'.$page['page_url'].'" class="button secondary">Clear</a></p>
		</fieldset>
	</form>';

	//Create a listing for each classified in the database
	$html .= '<div class="classifeids-listings-wrapper">';
	if(!empty($classifieds)){
		foreach($classifieds as $classified){ 
			$html .= '<div class="listing" id="classified-'.$classified['classified_id'].'">
				<div class="job_details">
				<p><a class="job-title" href="'.$page['page_url'].$classified['page'].'-'.$classified['classified_id'].'/'.'">'.$classified['title'].'</a></p>
				<p>'.$classified['facility_name'].' in '.$classified['city'].', '.$classified['province'].'</p>
				<p><small>Posted on '. date("M j, Y", strtotime($classified['date_added'])) .'</small></p>
				</div>
				<a href="'.$page['page_url'].$classified['page'].'-'.$classified['classified_id'].'/'.'" class="button solid side submit"><i class="fa fa-angle-right"></i></a>
			</div>';
		}
		
		//Pager
		$html .= '<p class="pager clear"><small>';
			$html .= 'Displaying '.($pg == 'all' ? '1 - '.$total_classifieds : (1+($limit*($pg-1))).' - '.(count($classifieds)+($limit*($pg-1)))).' (of '.$total_classifieds.' Total)<br />';
			if($total_classifieds > $limit && $pg != 'all'){
				$tagend = round($total_classifieds % $limit, 0);
				$splits = round(($total_classifieds - $tagend)/$limit, 0);
				$num_pages = ($tagend == 0 ? $splits : $splits+1);	
				$pos = $pg;
				$startpos = ($pos*$limit)-$limit;
				$filtering = '';
				foreach($_GET as $key=>$data){
					if(is_array($data)){
						foreach($data as $value){
							$filtering .= '&'.$key.'[]='.$value;	
						}
					}else{
						if($key != 'pg'){
							$filtering .= '&'.$key.'='.$data;	
						}
					}
				}

				$html .= ($pos > 1 ? '<a href="'.$page['page_url'].'?pg='.($pos-1).$filtering.'">&lsaquo; Prev</a> ' : '');
				for($i=1; $i<=$num_pages; $i++){
					$html .= ($i != $pos ? '<a href="'.$page['page_url'].'?pg='.$i.$filtering.'">'.$i.'</a> ' : '<span><u>'.$i.'</u></span> ');
				}
				$html .= ($pos < $num_pages ? '<a href="'.$page['page_url'].'?pg='.($pos+1).$filtering.'">Next &rsaquo;</a> ' : '');
			}
		$html .= '</small></p>';

	 //No classifieds found
	}else{
		$html .= '<p>No classifieds found.' .($search ? ' Please try a new search.' : ''). '</p>';
	}
	$html .= '</div>';
	$html .= '</div>';
	$html .= '</div>';
	
	//Set panel content
	// $page['page_panels'][$panel_id]['show_title_first'] = true;
	// $page['page_panels'][$panel_id]['content'] = $html;
	$page['page_panels'][$panel_id]['content'] .= $html;
	// $page['page_panels'][$panel_id]['sidebar_first'] = true;

	
//Display single classified	
}
else if (USER_LOGGED_IN || $classified['public'] == 1) {

	$html = '<h2 class="underline">Ad Details</h2>
	<section id="classifieds">';

		$html .= '<aside class="right-column">
			<h6>Facility</h6>
			<p><a href="'.$parent['page_url'].'?facility_ids%5B%5D='.$classified['facility_id'].'">'.$classified['facility_name'].'</a></p>
			<hr />

			<h6>Location</h6>
			<p><a href="'.$parent['page_url'].'?filter_search='.$classified['city'].'">'.$classified['city'].'</a>, <a href="'.$parent['page_url'].'?province%5B%5D='.$classified['province'].'">'.$classified['province'].'</a></p>
			<hr />
			
			<h6>Date Posted</h6>
			<p>'.date("F j, Y", strtotime($classified['date_added'])).'</p>
			<hr />
			
			<h6>Contact Information</h6>
			<p>
				'.$classified['first_name'].' '.$classified['last_name'].'<br>
				<a href="mailto:'.$classified['email'].'">'.$classified['email'].'</a><br>
				' .(trim($classified['phone']) != '' ? '<a href="tel:'.$classified['phone'].'">'.formatPhoneNumber($classified['phone']).'</a>' : ''). '
			</p>
			<hr />
		</aside>

		<section class="left-column">
			<h3>' .$classified['title']. '</h3>'.$classified['description'];
			if($classified['file_name'] != '' && file_exists('uploads/files/'.$classified['file_name'])){
				$html .= '<p><a href="' .$path. 'uploads/files/' .$classified['file_name']. '" target="_blank"><i class="fa fa-file-pdf-o"></i>&nbsp; ' .$classified['title']. '</a></p>';
			}

			$html .= '<div class="form-buttons clearfix"><hr />';
				if ($classified['account_id'] && USER_LOGGED_IN == $classified['account_id']) {				
					$html .= '<a href="'.$_sitepages['my_classifields']['page_url'].'?edit='.$classified['classified_id'].'" class="button solid f_left">Edit</a>
					<a class="button secondary" href="'.$parent['page_url'].'">&lsaquo; Back to All</a>';
				}else{
					$html .= '<a href="'.$parent['page_url'].'" class="button secondary"> Back to All</a>';
				}
			$html .= '</div>
		</section>
	</section>';
	
	// //Set page content
	$page['content'] = $html;
}

//Page panels
include('includes/pagepanels.php');

?>
