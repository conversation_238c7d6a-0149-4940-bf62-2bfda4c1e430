<?php if(LANDING){ ?>
	<?php $noimage = $page['image_showhide'] || !check_file($page['banner_image'], 'images/heroes/480/'); ?>
	<section id="page-hero" class="landing-banner animate theme-<?php echo ($page['theme'] ?: $global['theme']).($noimage ? ' noimage' : ''); ?>">
		<div class="page-hero-wrapper">
			<div id="page-logo">
				<?php load_svg('logo'); ?>
			</div>

			<?php if(!$noimage){ ?>
			<div id="page-hero-image">
				<div class="responsive-bg" style="<?php echo responsive_bg('images/heroes/', $page['banner_image']); ?>"></div>
				<div class="overlay overlay-<?php echo $page['theme'] ?: $global['theme']; ?>"></div>
			</div>
			<?php } ?>

			<header id="page-header">
				<div class="container">
					<div class="page-title-wrapper">
						<div class="page-title"><h1><?php echo fancy_text($page['page_title']); ?></h1></div>
						<?php echo ($page['description'] ? '<div class="page-subtitle">'.fancy_text($page['description']).'</div>' : ''); ?>
					</div>

					<?php
					echo ($page['button_url'] ? '<div class="page-buttons">'.create_button($page['button_url'], $page['button_target'], $page['button_text']).'</div>' : '');
					?>
				</div>
			</header>

			<?php
			if(!empty($page['page_form'])){
				include('includes/templates/landing/landing-form.php');
			}
			?>
		</div>
	</section>
<?php } ?>