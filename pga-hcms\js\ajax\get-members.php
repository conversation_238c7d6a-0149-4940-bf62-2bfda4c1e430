<?php
//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

$response = array();

if(isset($_POST) && USER_LOGGED_IN){

	$searchterm = $_POST['searchterm'];
	$params = array('Incomplete', 'Trashed', ' ', '%'.$searchterm.'%');
	
	$query = $db->query("SELECT `account_profiles`.`account_id`, `account_profiles`.`first_name`, `account_profiles`.`last_name`, `account_profiles`.`photo`, `facilities`.`facility_name` ".
	"FROM `account_profiles` ".
	"LEFT JOIN `accounts` ON `account_profiles`.`account_id` = `accounts`.`account_id` ".
	"LEFT JOIN `account_permissions` ON `account_profiles`.`account_id` = `account_permissions`.`account_id` ".
	"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
	"WHERE `account_permissions`.`role_id` = 2 && `accounts`.`status` != ? && `accounts`.`status` != ? && CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) LIKE ? ".
	"GROUP BY `account_profiles`.`account_id` ORDER BY `account_profiles`.`account_id`", $params);
	if($query && !$db->error() && $db->num_rows() > 0) {
		$accounts = $db->fetch_array();
		foreach($accounts as $account) {
			$autofill_item = array();
			$autofill_item['account_id'] = $account['account_id'];
			$autofill_item['first_name'] = str_replace("&rsquo;", "'", $account['first_name']);
			$autofill_item['last_name'] = str_replace("&rsquo;", "'", $account['last_name']);
			$autofill_item['facility_name'] = str_replace("&rsquo;", "'", $account['facility_name']);
			$autofill_item['photo'] = ($account['photo'] != '' && file_exists('../../../images/users/'.$account['photo']) ? '<img src="' .$root. 'images/users/' .$account['photo']. '" alt="" />' : '');
			$autofill_item['label'] = str_replace("&rsquo;", "'", $account['first_name']." ".$account['last_name']).", ".(!empty($account['facility_name']) ? $account['facility_name'] : "No Facility");
			$response[] = $autofill_item;
		}
	}
}

print json_encode($response);
	
?>