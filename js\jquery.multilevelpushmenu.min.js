!function(t){t.fn.multilevelpushmenu=function(e){"use strict";var i=arguments,n=null;return this.each(function(){function s(t){t.stopPropagation&&t.preventDefault?(t.stopPropagation(),t.preventDefault()):(t.cancelBubble=!0,t.returnValue=!1)}function a(e,i){if(void 0==e||void 0==i)return!1;e.on(i,function(n,s){e.hide();try{s=s||{pageX:n.pageX,pageY:n.pageY};var a=document.elementFromPoint(s.pageX,s.pageY);a=3==a.nodeType?a.parentNode:a,t(a).trigger(i,s)}catch(e){t.error("Error while propagating event: "+e.message)}finally{e.show()}})}function l(e,i){void 0==e.level&&(e.level=0),t.each(e,function(){var n=t("<div />").attr({class:"levelHolderClass"+("rtl"==R.settings.direction?" rtl":" ltr"),"data-level":e.level,style:("rtl"==R.settings.direction?"margin-right: ":"margin-left: ")+(0!=e.level||R.settings.collapsed?"-200%":0)}).appendTo(i);w(R.settings.menuWidth)||y(R.settings.menuWidth)&&R.settings.menuWidth;n.bind(j,function(t){}),void 0!=this.id&&n.attr({id:this.id});var s=t("<h4 />").attr({style:"text-align: "+("rtl"==R.settings.direction?"right":"left")}).text(this.title).appendTo(n);t("<i />").prop({class:("rtl"==R.settings.direction?"floatLeft":"floatRight")+" cursorPointer "+this.icon}).prependTo(s).bind(M,function(t){o(t,n,e)}),e.level>0&&d(n);t("<ul />").appendTo(n);t.each(this.items,function(){p(this,n,-1)})})}function r(e){void 0==e.level&&(e.level=0),t.each(e,function(){var i=t("<div />").attr({class:"levelHolderClass"+("rtl"==R.settings.direction?" rtl":" ltr"),"data-level":e.level,style:("rtl"==R.settings.direction?"margin-right: ":"margin-left: ")+(0!=e.level||R.settings.collapsed?"-200%":0)}).appendTo(e);w(R.settings.menuWidth)||y(R.settings.menuWidth)&&R.settings.menuWidth;i.bind(j,function(t){});var n=e.children("h4");n.attr({style:"text-align: "+("rtl"==R.settings.direction?"right":"left")}),n.appendTo(i);var s=n.children("i");s.addClass(("rtl"==R.settings.direction?"floatLeft":"floatRight")+" cursorPointer"),s.bind(M,function(t){o(t,i,e)}),e.level>0&&d(i);var a=e.children("ul");a.appendTo(i),t.each(a.children("li"),function(){var n=t(this);n.attr({style:"text-align: "+("rtl"==R.settings.direction?"right":"left")});var s=n.children("a");s.children("i").addClass("rtl"==R.settings.direction?"floatLeft":"floatRight"),n.children("ul").length>0?(s.bind(M,function(t){c(t,i,n)}),g(s),n.level=e.level+1,r(n)):s.bind(M,function(t){h(t,i,n)})})})}function o(e,i,n){if(t(R).find("div.levelHolderClass").is(":animated"))return!1;R.settings.onTitleItemClick.apply(this,Array.prototype.slice.call([e,i,R.settings])),s(e);var a="rtl"==R.settings.direction?parseInt(i.css("margin-right"))<0:parseInt(i.css("margin-left"))<0;if(0==n.level&&a)x();else{var l=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return"rtl"==R.settings.direction?t(this).attr("data-level")>i.attr("data-level")&&parseInt(t(this).css("margin-right"))>=0:t(this).attr("data-level")>i.attr("data-level")&&parseInt(t(this).css("margin-left"))>=0}),r=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return"rtl"==R.settings.direction?t(this).attr("data-level")<=i.attr("data-level")&&parseInt(t(this).css("margin-right"))>=0:t(this).attr("data-level")<=i.attr("data-level")&&parseInt(t(this).css("margin-left"))>=0});0==l.length&&1==r.length?C():C(parseInt(i.attr("data-level")))}i.css("visibility","visible"),i.find("."+R.settings.backItemClass).css("visibility","visible"),i.find("ul").css("visibility","visible"),i.removeClass(R.settings.menuInactiveClass)}function d(e){var i=t("<div />").attr({class:R.settings.backItemClass}).appendTo(e),n=t("<a />").prop({href:"#"}).text(R.settings.backText).appendTo(i);t("<i />").prop({class:("rtl"==R.settings.direction?"floatLeft ":"floatRight ")+R.settings.backItemIcon}).prependTo(n);n.bind(M,function(i){!function(e,i){if(t(R).find("div.levelHolderClass").is(":animated"))return!1;R.settings.onBackItemClick.apply(this,Array.prototype.slice.call([e,i,R.settings])),s(e),C(parseInt(i.attr("data-level")-1))}(i,e)})}function c(e,i,n){if(t(R).find("div.levelHolderClass").is(":animated"))return!1;R.settings.onGroupItemClick.apply(this,Array.prototype.slice.call([e,i,n,R.settings])),x(n.find("div:first")),R.settings.preventGroupItemClick&&s(e)}function g(e){t("<i />").attr({class:("rtl"==R.settings.direction?" floatRight iconSpacing_rtl ":" floatLeft iconSpacing_ltr ")+R.settings.groupIcon}).prependTo(e)}function p(){var e=arguments[0],i=arguments[1],n=arguments[2],s=i.find("ul:first"),a=t("<li />");n<s.find("li").length&&n>=0?a.insertBefore(s.find("li").eq(n)):a.appendTo(s),a.attr({style:"text-align: "+("rtl"==R.settings.direction?"right":"left")}),void 0!=e.id&&a.attr({id:e.id});var r=t("<a />").prop({href:e.link}).text(e.name).appendTo(a);t("<i />").prop({class:("rtl"==R.settings.direction?"floatLeft ":"floatRight ")+e.icon}).prependTo(r);e.items?(r.bind(M,function(t){c(t,i,a)}),g(r),e.items.level=parseInt(i.attr("data-level"),10)+1,l(e.items,a)):r.bind(M,function(t){h(t,i,a)})}function h(t,e,i){R.settings.onItemClick.apply(this,Array.prototype.slice.call([t,e,i,R.settings])),R.settings.preventItemClick&&s(t)}function u(){var e=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return"rtl"==R.settings.direction?parseInt(t(this).css("margin-right"))>=0&&t(this).position().left<R.settings.container.width()-R.settings.overlapWidth:parseInt(t(this).css("margin-left"))>=0&&t(this).position().left>=0});return e.length<1&&(e=!1),e}function v(){var e=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return"rtl"==R.settings.direction?t(this).position().left>R.settings.container.width()||parseInt(t(this).css("margin-right"))<0:t(this).position().left<0||parseInt(t(this).css("margin-left"))<0});return e.length<1&&(e=!1),e}function m(){if(!R.redraw){R.redraw=!0;var e,i=arguments[0],n=arguments[1],s=arguments[2],a=void 0==i?Math.max.apply(null,t("#"+R.settings.menuID+" div.levelHolderClass").map(function(){return t(this).width()}).get())-0:i-0,l=Math.max.apply(null,t("#"+R.settings.menuID+" div.levelHolderClass").map(function(){return t(this).attr("data-level")}).get()),r=w(R.settings.menuWidth)||y(R.settings.menuWidth)&&R.settings.menuWidth>0,o=w(R.settings.menuHeight)||y(R.settings.menuHeight)&&R.settings.menuHeight>0,d=void 0==s?t("#"+R.settings.menuID+" div.levelHolderClass"):s;r||void 0==R.menuWidth||(a=R.menuWidth),r&&void 0==i?d.width(R.settings.menuWidth):d.width(a),r&&((0==d.width()||w(R.settings.menuWidth)&&-1!=R.settings.menuWidth.indexOf("%"))&&void 0==i&&(d.css("min-width",""),d.width(parseInt(R.settings.container.parent().width()*parseInt(R.settings.menuWidth)/100))),a=d.width()-0,d.css("min-width",d.width()-0+"px"));var c=r&&void 0==i?d.width()-0+l*(R.settings.overlapWidth+0):a+l*(R.settings.overlapWidth+0),g=void 0==n?Math.max.apply(null,t("#"+R.settings.menuID+" div.levelHolderClass").map(function(){return t(this).height()}).get()):n;R.settings.container.css("min-height",""),R.settings.container.children("nav:first").css("min-height",""),o?(R.settings.container.height(R.settings.menuHeight),R.settings.container.css("min-height",R.settings.menuHeight),R.settings.container.children("nav:first").css("min-height",R.settings.menuHeight),t("#"+R.settings.menuID).height(R.settings.menuHeight),g=R.settings.container.height()):t("#"+R.settings.menuID).height(g),R.settings.container.css("min-height",g+"px"),R.settings.container.children("nav:first").css("min-height",g+"px"),R.settings.container.width(c),R.settings.container.height(g);var p=t("#"+R.settings.menuID+" div.levelHolderClass:first"),h=u(),m=v(),b=D(),I=1==b.length?b.attr("data-level"):0;h&&h.each(function(){"overlap"==R.settings.mode&&t(this).width(t(this).width()+(parseInt(I,10)-parseInt(t(this).attr("data-level"),10))*(R.settings.overlapWidth+0))}),m&&m.each(function(){"rtl"==R.settings.direction?t(this).css("margin-right",t(this).attr("data-level")!=p.attr("data-level")||R.settings.fullCollapse?-2*t(this).width():-1*t(this).width()+R.settings.overlapWidth):t(this).css("margin-left",t(this).attr("data-level")!=p.attr("data-level")||R.settings.fullCollapse?-2*t(this).width():-1*t(this).width()+R.settings.overlapWidth)}),e=p.width()+parseInt(p.css("rtl"==R.settings.direction?"margin-right":"margin-left"),10),f(R.settings.container,e),R.menuWidth=a,R.menuHeight=g,R.redraw=!1}}function f(t,e){if(void 0==t||void 0==e)return!1;t.css("min-width",""),t.css("min-width",e+"px"),t.children("nav:first").css("min-width",""),t.children("nav:first").css("min-width",e+"px"),t.width(e)}function y(t){return"number"==typeof t&&parseFloat(t)==parseInt(t,10)&&!isNaN(t)}function w(t){return"string"==typeof t&&(-1!=t.indexOf("%")||-1!=t.indexOf("px")||-1!=t.indexOf("em"))}function b(){e&&void 0!=e.menu?function(){var e=t("<nav />").prop({id:R.settings.menuID,className:R.settings.wrapperClass}).appendTo(R.settings.container);l(R.settings.menu,e)}():function(){var t=R.settings.container.find("nav").length>0?R.settings.container.find("nav"):R.settings.menu;if(0==t.length)return!1;t.prop({id:R.settings.menuID,className:R.settings.wrapperClass}),r(t)}();return a(R.settings.container,M),m(),function(){var e=t("#"+R.settings.menuID+" div.levelHolderClass:first");R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return"rtl"==R.settings.direction?(t(this).position().left>R.settings.container.width()||parseInt(t(this).css("margin-right"))<0)&&t(this).attr("data-level")>e.attr("data-level"):(t(this).position().left<0||parseInt(t(this).css("margin-left"))<0)&&t(this).attr("data-level")>e.attr("data-level")}).each(function(){"rtl"==R.settings.direction?t(this).css("margin-right",t(this).attr("data-level")!=e.attr("data-level")||R.settings.collapsed?-2*t(this).width():0):t(this).css("margin-left",t(this).attr("data-level")!=e.attr("data-level")||R.settings.collapsed?-2*t(this).width():0)}),"rtl"==R.settings.direction?e.css("margin-right",R.settings.collapsed?-2*e.width():0):e.css("margin-left",R.settings.collapsed?-2*e.width():0)}(),function(e){if(e){var i=t("#"+R.settings.menuID+" div.levelHolderClass:first");i.find("ul").hide(),i.addClass(R.settings.menuInactiveClass),"rtl"==R.settings.direction?i.stop().animate({marginRight:-1*i.width()+(R.settings.fullCollapse?0:R.settings.overlapWidth)}):i.stop().animate({marginLeft:-1*i.width()+(R.settings.fullCollapse?0:R.settings.overlapWidth)})}}(R.settings.collapsed),R.settings.onMenuReady.apply(this,Array.prototype.slice.call([R.settings])),L}function I(e){if(null==R.settings.containersToPush)return!1;t.each(R.settings.containersToPush,function(){var i=parseInt(t(this).css("margin-left")),n=y(i)?i:0,s=parseInt(t(this).css("margin-right")),a=y(s)?s:0;t(this).stop().animate({marginLeft:n+("rtl"==R.settings.direction?-1:1)*e,marginRight:a+("rtl"==R.settings.direction?1:-1)*e})})}function C(){if(t(R).find("div.levelHolderClass").is(":animated"))return!1;R.settings.onCollapseMenuStart.apply(this,Array.prototype.slice.call([R.settings]));var e,i,n,s,a=arguments[0],l=arguments[1],r={},o=t("#"+R.settings.menuID+" div.levelHolderClass:first"),d=void 0==a;if(r.collapsingEnded=!1,"object"==typeof a)a=a.attr("data-level");else if("string"==typeof a){var c=W(a);a=c&&1==c.length?c.attr("data-level"):o.attr("data-level")}else(void 0==a||!y(a)||a<0)&&(a=o.attr("data-level"));void 0==l&&"object"!=typeof l?l=[{method:R.settings.onCollapseMenuEnd,args:[R.settings]}]:t.merge(l,[{method:R.settings.onCollapseMenuEnd,args:[R.settings]}]);var g=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return"rtl"==R.settings.direction?t(this).attr("data-level")>a&&parseInt(t(this).css("margin-right"))>=0&&t(this).position().left<R.settings.container.width()-R.settings.overlapWidth:t(this).attr("data-level")>a&&parseInt(t(this).css("margin-left"))>=0&&t(this).position().left>=0}),p=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return"rtl"==R.settings.direction?t(this).attr("data-level")<=a&&parseInt(t(this).css("margin-right"))>=0&&t(this).position().left<R.settings.container.width()-R.settings.overlapWidth:t(this).attr("data-level")<=a&&parseInt(t(this).css("margin-left"))>=0&&t(this).position().left>=0});return p.length>0&&(r.prevAnimEnded=!1,g.each(function(n,s){e=0,i="overlap"==R.settings.mode?t(s).width()-(g.length+p.length-t(s).attr("data-level")-1)*(R.settings.overlapWidth+e)-e:t(s).width()-e,"rtl"==R.settings.direction?t(s).stop().animate({marginRight:-1*i,width:i}):t(s).stop().animate({marginLeft:-1*i,width:i})}),r.nextAnimEnded=!(g.length>0),g.last().queue(function(){r.nextAnimEnded=!0,E(r,l)}),p.each(function(l,r){e=0;var c=p.filter(function(){return t(this).attr("data-level")==a});if(c.css("visibility","visible"),c.find("."+R.settings.backItemClass).css("visibility","visible"),c.find("ul").css("visibility","visible"),c.removeClass(R.settings.menuInactiveClass),i="overlap"==R.settings.mode?t(r).width()-g.length*(R.settings.overlapWidth+e)-e:t(r).width()-e,"rtl"==R.settings.direction?t(r).stop().animate({width:i,marginRight:t(r).attr("data-level")==o.attr("data-level")&&d?R.settings.fullCollapse?-1*t(r).width():-1*t(r).width()+("overlap"==R.settings.mode?g.length+1:1)*R.settings.overlapWidth:0},function(){t(r).attr("data-level")==o.attr("data-level")&&d&&o.children("ul").first().hide(500,function(){o.addClass(R.settings.menuInactiveClass)}),s=o.width()+parseInt(o.css("margin-right"),10),f(R.settings.container,s)}):t(r).stop().animate({width:i,marginLeft:t(r).attr("data-level")==o.attr("data-level")&&d?R.settings.fullCollapse?-1*t(r).width():-1*t(r).width()+("overlap"==R.settings.mode?g.length+1:1)*R.settings.overlapWidth:0},function(){t(r).attr("data-level")==o.attr("data-level")&&d&&o.children("ul").first().hide(500,function(){o.addClass(R.settings.menuInactiveClass)}),s=o.width()+parseInt(o.css("margin-left"),10),f(R.settings.container,s)}),n="overlap"==R.settings.mode?g.length*(R.settings.overlapWidth+e)*-1:0,t(r).attr("data-level")==o.attr("data-level")&&d){I(R.settings.fullCollapse?-1*(o.width()-e):-1*(o.width()-e)+R.settings.overlapWidth)}else I(n)}),p.last().queue(function(){r.prevAnimEnded=!0,E(r,l)})),r.collapsingEnded=!0,E(r,l),L}function k(){if(t(R).find("div.levelHolderClass").is(":animated"))return!1;R.settings.onExpandMenuStart.apply(this,Array.prototype.slice.call([R.settings]));var e,i,n,s,a=arguments[0],l=arguments[1],r={},o=t("#"+R.settings.menuID+" div.levelHolderClass:first"),d=void 0==a,c="rtl"==R.settings.direction?parseInt(o.css("margin-right"),10)<0||o.position().left>=R.settings.container.width()-R.settings.overlapWidth:parseInt(o.css("margin-left"),10)<0||o.position().left<0;if(r.expandingEnded=!1,void 0==l&&"object"!=typeof l?l=[{method:R.settings.onExpandMenuEnd,args:[R.settings]}]:t.merge(l,[{method:R.settings.onExpandMenuEnd,args:[R.settings]}]),d){r.baseAnimEnded=!1,o.removeClass(R.settings.menuInactiveClass),s=o.width(),f(R.settings.container,s),"rtl"==R.settings.direction?o.stop().animate({marginRight:0},function(){o.children("ul").first().show(500,function(){r.baseAnimEnded=!0,E(r,l)})}):o.stop().animate({marginLeft:0},function(){o.children("ul").first().show(500,function(){r.baseAnimEnded=!0,E(r,l)})}),n=R.settings.fullCollapse?o.width():o.width()-R.settings.overlapWidth;T(o)||I(n)}else{var g;if("object"==typeof a?g=a:"string"==typeof a?g=W(a):(g=null,t.error("Provided menu selector is not valid")),g&&1==g.length){var p=D(),h=1==p.length?p.attr("data-level"):0,u=g.width(),v=H(g);if(r.setToOpenAnimEnded=!1,v){var m=t(v).length-1;o.find("ul").each(function(){t(this).show(0)}),t(v).find("div").css("visibility","visible"),t(v).each(function(n,s){i=u-(e=0)+(m-t(s).attr("data-level"))*(R.settings.overlapWidth+e),R.settings.container.width()<i&&"overlap"==R.settings.mode&&f(R.settings.container,i),"rtl"==R.settings.direction?t(s).stop().animate({marginRight:0,width:"overlap"==R.settings.mode?i:u-e},function(){t(s).addClass(R.settings.menuInactiveClass)}):t(s).stop().animate({marginLeft:0,width:"overlap"==R.settings.mode?i:u-e},function(){t(s).addClass(R.settings.menuInactiveClass)})}),t(v).last().queue(function(){t(this).removeClass(R.settings.menuInactiveClass),r.setToOpenAnimEnded=!0,E(r,l)}),c&&I(n=R.settings.fullCollapse?o.width():o.width()-R.settings.overlapWidth),"overlap"==R.settings.mode&&I(c?u+(m-(R.settings.fullCollapse?0:1))*(R.settings.overlapWidth+e):(m-h)*(R.settings.overlapWidth+e)),g.css("visibility","visible"),g.find("."+R.settings.backItemClass).css("visibility","visible"),g.find("ul").css("visibility","visible"),g.removeClass(R.settings.menuInactiveClass)}else t.error("Invalid menu object provided")}else t.error("No or too many menus named "+a)}r.expandingEnded=!0,E(r,l)}function x(){var e,i,n,s,a=arguments[0],l=D();return"object"==typeof a?e=a:"string"==typeof a?(s=W(a))?e=s.eq(0):t.error(a+" menu level does not exist!"):e=t("#"+R.settings.menuID+" div.levelHolderClass:first"),i=A(e,l,!0),(n=i.length>0?Math.max.apply(null,i.map(function(){return t(this).attr("data-level")}).get()):0)<l.attr("data-level")?C(n,[{method:k,args:arguments}]):k.apply(this,Array.prototype.slice.call(arguments)),L}function W(){var e=arguments[0],i=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return t(this).children("h4").text()==e});return n=i.length>0&&i}function H(){var e,i,s=arguments[0];return void 0==s||1!=s.length?n=!1:(e=s.parents("div.levelHolderClass"),i=t.merge(e.get().reverse(),s.get()),n=i)}function A(){var e,i,s,a,l,r,o,d=arguments[0],c=arguments[1],g=void 0!=arguments[2]&&arguments[2];return void 0==d||void 0==c?n=!1:(e=1==d.length?d.parents("div.levelHolderClass"):null,i=1==c.length?c.parents("div.levelHolderClass"):null,s=null!=e?t.merge(e.get().reverse(),d.get()):[],a=null!=i?t.merge(i.get().reverse(),c.get()):[],l=s.length>=a.length?s:a,r=l===s?a:s,o=t(l).filter(function(){return g?-1!=t.inArray(this,r):-1==t.inArray(this,r)}),n=o)}function D(){var e=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass").filter(function(){return"rtl"==R.settings.direction?parseInt(t(this).css("margin-right"))>=0&&t(this).position().left<R.settings.container.width()-R.settings.overlapWidth:parseInt(t(this).css("margin-left"))>=0&&t(this).position().left>=0}),i=Math.max.apply(null,e.map(function(){return t(this).attr("data-level")}).get()),s=e.filter(function(){return t(this).attr("data-level")==i});return n=s}function T(){var t=arguments[0];if(void 0==t)return!1;return"rtl"==R.settings.direction?parseInt(t.css("margin-right"))>=0&&t.position().left<R.settings.container.width()-R.settings.overlapWidth:parseInt(t.css("margin-left"))>=0&&t.position().left>=0}function E(e,i){var n=!0;t.each(e,function(t,e){n=n&&e}),n&&window.setTimeout(function(){t.each(i,function(t,e){e.method.apply(this,Array.prototype.slice.call(e.args))})},1)}var M,j,R=this,L=t(this),q=L.length>0?L:t("body"),z=e&&void 0!=e.menu?e.menu:L.find("nav"),_=t.extend({container:q,containersToPush:null,menuID:(void 0!=q.prop("id")&&""!=q.prop("id")?q.prop("id"):this.nodeName.toLowerCase())+"_multilevelpushmenu",wrapperClass:"multilevelpushmenu_wrapper",menuInactiveClass:"multilevelpushmenu_inactive",menu:z,menuWidth:0,menuHeight:0,collapsed:!1,fullCollapse:!1,direction:"ltr",backText:"Back",backItemClass:"backItemClass",backItemIcon:"fas fa-angle-right",groupIcon:"fas fa-angle-left",mode:"overlap",overlapWidth:40,preventItemClick:!0,preventGroupItemClick:!0,swipe:"both",onCollapseMenuStart:function(){},onCollapseMenuEnd:function(){},onExpandMenuStart:function(){},onExpandMenuEnd:function(){},onGroupItemClick:function(){},onItemClick:function(){},onTitleItemClick:function(){},onBackItemClick:function(){},onMenuReady:function(){},onMenuSwipe:function(){}},e);t.data(R,"plugin_multilevelpushmenu")||(t.data(R,"plugin_multilevelpushmenu",_),R.settings=t.data(R,"plugin_multilevelpushmenu"));var N={init:function(){return b.apply(this,Array.prototype.slice.call(arguments))},collapse:function(){return C.apply(this,Array.prototype.slice.call(arguments))},expand:function(){return x.apply(this,Array.prototype.slice.call(arguments))},menuexpanded:function(){return T.apply(this,Array.prototype.slice.call(arguments))},activemenu:function(){return D.apply(this,Array.prototype.slice.call(arguments))},findmenusbytitle:function(){return W.apply(this,Array.prototype.slice.call(arguments))},finditemsbyname:function(){return function(){var e=arguments[0],i=R.settings.container.find("#"+R.settings.menuID+" div.levelHolderClass li").filter(function(){return t(this).children("a").text()==e});return n=i.length>0&&i}.apply(this,Array.prototype.slice.call(arguments))},pathtoroot:function(){return H.apply(this,Array.prototype.slice.call(arguments))},comparepaths:function(){return A.apply(this,Array.prototype.slice.call(arguments))},option:function(){return function(){var e=!1;return void 0!=R.settings[arguments[0]]?(void 0!=arguments[1]&&(R.settings[arguments[0]]=arguments[1]),e=R.settings[arguments[0]]):t.error("No option "+arguments[0]+" found in jQuery.multilevelpushmenu"),e}.apply(this,Array.prototype.slice.call(arguments))},additems:function(){return function(){var e=arguments[0],i=arguments[1],n=arguments[2];return!(void 0==i||"object"!=typeof e||!i)&&(void 0==e.level&&(e.level=parseInt(i.attr("data-level"),10)),void 0==n&&(n=0),i.find("ul:first"),t.each(e,function(){void 0!=this.name&&p(this,i,n)}),m(R.menuWidth),L)}.apply(this,Array.prototype.slice.call(arguments))},removeitems:function(){return function(){var e=arguments[0];if(void 0==e||"object"!=typeof e||0==e.length)return!1;e.remove();var i=D();if(1==i.length){i.css("visibility","visible"),i.find("."+R.settings.backItemClass).css("visibility","visible"),i.find("ul").css("visibility","visible"),i.removeClass(R.settings.menuInactiveClass);var n=i.width()-R.menuWidth;if(0!=n){var s=u();s&&s.each(function(){t(this).width(t(this).width()-n)})}}return m(R.menuWidth),L}.apply(this,Array.prototype.slice.call(arguments))},redraw:function(){return m.apply(this,Array.prototype.slice.call(arguments))},visiblemenus:function(){return u.apply(this,Array.prototype.slice.call(arguments))},hiddenmenus:function(){return v.apply(this,Array.prototype.slice.call(arguments))},propagateevent:function(){return a.apply(this,Array.prototype.slice.call(arguments))}};return!function(){var t=!1;return function(e){(/(android|ipad|playbook|silk|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(e.substr(0,4)))&&(t=!0)}(navigator.userAgent||navigator.vendor||window.opera),t}()?(M="click",j="mousedown"):(M="click",j="touchmove"),N[e]?n=N[e].apply(this,Array.prototype.slice.call(i,1)):"object"!=typeof e&&e?(t.error("No "+e+" method found in jQuery.multilevelpushmenu"),void(n||(n=this))):n=N.init.apply(this,arguments)}),n}}(jQuery);