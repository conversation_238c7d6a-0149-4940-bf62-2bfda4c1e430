<?php 

//All Career Listings
if(PAGE_ID == $_sitepages['job-postings']['page_id']){

	

	$panel_id = 17; //ID of the Careers Panel

	//Pagination
	$pg = (isset($_GET['pg']) ? $_GET['pg'] : 1);
	$limit = 20;
	
	$andor = false; //Filter query for either or both the checklist and the searchbar ()
	$search = false; //Confirm whether that the search bar is being utilized (for highlighted)

	//Initilize data vars
	$careers = array();
	$categories = array();
	$facilities = array();
	$params = array();  //Query parameters
	$searchable_params = array(
	//Columns to search
		'title',
		'city',
		'province',
		'category_name',
		'facility_name'
	);

	if(isset($career_id)){
		unset($career_id);
	}

	//Build filtered query
	$q = "SELECT `careers`.*, `career_categories`.`category_name`, `facilities`.`facility_name`, `facilities`.`city`, `facilities`.`province`, `career_classes`.`class_id` FROM `careers` ". 
	"LEFT JOIN `career_categories` ON `career_categories`.`category_id` = `careers`.`category_id` ". 
	"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `careers`.`facility_id` ".
	"LEFT JOIN `career_classes` ON `careers`.`career_id` = `career_classes`.`career_id` ";
	
	//Filter by classification
	if(isset($_GET['class_ids']) && !empty($_GET['class_ids'])){
		$q .= "AND `career_classes`.`class_id` IN(";
		foreach ($_GET['class_ids'] as $value){
			$q .= "?,";
			$params[] = $value;
		}
		$q = substr($q, 0, -1).") "; //Remove extra comma and close filter
	}
	
	//Where clause
	$params[] = date("Y-m-d");  //Query parameters
	$q .= "WHERE `careers`.`showhide` = 0 AND `careers`.`approved` = 1 AND `careers`.`closing_date` ".((MEMBER_ACCESS && isset($_GET['status']) && $_GET['status'] == 'archived') ? "<= ?" : "> ?")." AND (`careers`.`category_id` IS NULL || (`careers`.`category_id` IS NOT NULL && `career_categories`.`showhide` = 0))";


	//If user is not logged in, filter out all members only posts
	if(!USER_LOGGED_IN){
		$q .= " AND `careers`.`public` = 1";
	}

	//Filter by classification
	if(isset($_GET['class_ids']) && !empty($_GET['class_ids'])){
		$andor = true;
		$q .= " AND `career_classes`.`class_id` IS NOT NULL";
	}
	
	//Filter by category
	if(isset($_GET['category_ids']) && !empty($_GET['category_ids'])){
		$andor = true;
		$q .= " AND `careers`.`category_id` IN (";
		foreach ($_GET['category_ids'] as $value){
			$q .= "?,";
			$params[] = $value;
		}
		$q = substr($q, 0, -1).")"; //Remove extra comma and close filter
	
	}
	
	//Filter by facility
	if(isset($_GET['facility_ids']) && !empty($_GET['facility_ids'])){
		$andor = true;
		$q .= " AND `careers`.`facility_id` IN (";
		foreach ($_GET['facility_ids'] as $value){
			$q .= "?,";
			$params[] = $value;
		}
		$q = substr($q, 0, -1).")";

	}

	//Filter by location
	if(isset($_GET['province']) && !empty($_GET['province'])){
		$andor = true;
		$q .= " AND `facilities`.`province` IN (";
		foreach ($_GET['province'] as $value){
			if($value == 'Outside'){
				foreach($provinces as $prov){
					if($prov[1] != 'AB'){
						$q .= "?,";
						$params[] = $prov[1];
					}
				}
			}else{
				$q .= "?,";
				$params[] = $value;
			}
		}
		$q = substr($q, 0, -1).")";
	}

	//Text search
	if(isset($_GET['filter_search']) && $_GET['filter_search']){
		$search = true;
		//$q .= ($andor ? " OR (" : " AND (");
		$q .= " AND (";
		foreach ($searchable_params as $value){
			$q .= $value . " LIKE ? OR ";
			$params[] = '%'.$_GET['filter_search'].'%';
		}
		$q = substr($q, 0, -4). ")"; //Remove excess " OR "
	}

	$q .= " GROUP BY `careers`.`career_id` ORDER BY `careers`.`posted_date` DESC, `careers`.`career_id` DESC"; //Close query
	
	$career_query = $db->query($q, $params);
	if($career_query && !$db->error()){
		$total_careers = $db->num_rows();
		if($total_careers > 0){
			$careers = $db->fetch_array();
			
			//Pagination
			if($pg != 'all'){
				$start = (($pg-1)*$limit);
				$end = $limit;
			}else{
				$start = 0;
				$end = $total_careers;
			}
			$careers = array_slice($careers, $start, $end);
			
		}
		if($search){
			
			//Highlight searched text
			foreach ($careers as $key => $career){
				$salary[$key] = str_replace('-', '', $career['salary']);
				
				foreach ($career as $key2 => $value){
					if(in_array($key2, $searchable_params)){
						//Search for text
						$pos = strpos(strtolower($value), strtolower($_GET['filter_search']));

						if($pos !== false){
							//Surround found text in a span
							$careers[$key][$key2] = substr_replace($value, '<span class="found-text">'.substr($value, $pos, strlen($_GET['filter_search'])).'</span>', $pos, strlen($_GET['filter_search']));
						}
					}
				}
			}			
		}
	}

	//Load category data
	$category_query = $db->query("SELECT * FROM career_categories WHERE career_categories.showhide = 0 ORDER BY ordering");
	if($category_query && !$db->error()){
		if($db->num_rows() > 0){
			$categories = $db->fetch_array();
		}
	}

	//Load facility data
	$facility_query = $db->query("SELECT * FROM facilities WHERE facilities.showhide = 0 ORDER BY facility_name");
	if($facility_query && !$db->error()){
		if($db->num_rows() > 0){
			$facilities = $db->fetch_array();
		}
	}
	
	//Load classes
	$classes = array();
	$classes_query = $db->query("SELECT * FROM `membership_classes` WHERE `job_filter` = 1");
	if($classes_query && !$db->error() && $db->num_rows() > 0) {
		$classes = $db->fetch_array();
	}
	// print_r($careers);
	// exit;

//Single Career Display
}else if(PARENT_ID == $_sitepages['job-postings']['page_id'] && PAGE_ID == ''){
	$pagebits = $SiteBuilder->get_pagebits((trim($_sitepages['job-postings']['slug']) != '' ? $_sitepages['job-postings']['slug'] : $_sitepages['job-postings']['page']));
	
	if(!isset($pagebits[3]) || $pagebits[3] == ''){
		
		//Grab career ID from URL
		$career_bits = explode("-", $pagebits[2]);
		$career_id = $career_bits[count($career_bits)-1];
		$bannerdir = 'images/svg/';
		//Grab career data
		$query = $db->query("SELECT `careers`.*, `career_categories`.`category_name`, `facilities`.`facility_name`, `facilities`.`city`, `facilities`.`province` FROM `careers` ".
		"LEFT JOIN `career_categories` ON `career_categories`.`category_id` = `careers`.`category_id` ".
		"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `careers`.`facility_id` ".
		"WHERE `careers`.`showhide` = 0 AND (`careers`.`category_id` IS NULL || (`careers`.`category_id` IS NOT NULL && `career_categories`.`showhide` = 0)) AND `career_id` = ?", array($career_id));
		if($query && !$db->error()){
			if($db->num_rows() > 0){

				//Load career data
				$career = $db->fetch_array();
				$career = $career[0];
				
				if(USER_LOGGED_IN || $career['public'] == 1){
					$error404 = false;
					$parent = $SiteBuilder->get_page_content($page['parent_id']); //Grab main page content
					$page['page_title'] = $parent['name'];
					$page['name'] = $career['title'];
					$last_path_bit = array_pop($breadcrumbs);
					$last_path_bit['name'] = $career['title'];
					$breadcrumbs[] = $last_path_bit;
					$page['content'] = '';
					$page['meta_canonical'] = $parent['page_url'] .$career['page']. '-' .$career['career_id']. '/';
					$page['meta_title'] = $career['meta_title'] ?: ($career['title']. ' | ' .$parent['page_title']);
					$page['meta_description'] = $career['meta_description'] ?: $parent['meta_description'];
					$page['banner_image'] = $parent['banner_image'];
					$page['banner_image_alt'] = $parent['banner_image_alt'];
					$page['image']       =  '<img src="' .$path. $bannerdir.'/banner.svg" />';
					
					$page['page_panels'] = array();

					//Send to correct path
					if(($career['page']. '-' .$career['career_id']) != $pagebits[2]){
						header("HTTP/1.1 301 Moved Permanently");
						header('Location: ' .$parent['page_url'] .$career['page']. '-' .$career['career_id']. '/');
						exit();
					}
				}

			}else{
				unset($career_id);
			}
		}else{
			unset($career_id);
		}

	}
}
?>