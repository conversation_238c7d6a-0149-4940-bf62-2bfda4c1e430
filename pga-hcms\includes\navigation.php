<?php
	
//Ungrouped navigation sections
echo '<nav id="menu-icons">
	<ul>';
		foreach($navigation['ungrouped'] as $nav){
			echo '<li>
				<a href="' .$nav['page_url']. '" class="' .($nav['section_id'] == $section['section_id'] ? 'active' : ''). '">';
					if($nav['section_id'] == $_cmssections['account']){
						echo '<div class="initials"><span>'.substr($Account->first_name, 0, 1).substr($Account->last_name, 0, 1).'</span><i class="hexagon"></i></div>';
					}else{
						echo '<i class="' .$nav['icon']. '"></i>';
					}
				echo $nav['name']. '</a>';

				//Sub sections
				if(is_array($nav['sub_sections']) && !empty($nav['sub_sections'])){
					echo '<ul>';
						foreach($nav['sub_sections'] as $sub){
							echo '<li>
								<a href="' .$sub['page_url']. '" class="' .($sub['section_id'] == $section['section_id'] ? 'active' : ''). '"><i class="' .$sub['icon']. '"></i>' .$sub['name']. '</a>
							</li>';
						}
					echo '</ul>';
				}

			echo '</li>';
		}

		//Logout
		echo '<li>
			<a href="' .$path. 'modules/Logout.php" class="confirm-submit-btn" data-confirm="Are you sure you want to logout?">Logout<i class="fas fa-sign-out-alt"></i></a>
		</li>';

	echo '</ul>
</nav>';

//Grouped navigation sections
echo '<nav id="menu-list">';
	if(!empty($navigation['grouped'])){

		echo '<ul>';
		foreach($navigation['grouped'] as $group){

			//Display group heading
			echo '<li class="menu-header">' .(!empty($group['group_name']) ? '<h6>' .$group['group_name']. '</h6>' : ''). '</li>';

			//Display group sections
			foreach($group['sections'] as $nav){
				echo '<li class="' .(is_array($nav['sub_sections']) && !empty($nav['sub_sections']) ? 'accordion' : '').($nav['section_id'] == $section['parent_id'] ? ' expanded' : ''). '">
					<a href="' .$nav['page_url']. '" class="' .($nav['section_id'] == $section['section_id'] ? 'active' : ''). '">
						<i class="' .$nav['icon']. '"></i>' .$nav['name']. '
					</a>';

					//Sub sections
					if(is_array($nav['sub_sections']) && !empty($nav['sub_sections'])){
						echo '<ul class="ui-accordion-content-active">';
							foreach($nav['sub_sections'] as $sub){
								echo '<li>
									<a href="' .$sub['page_url']. '" class="' .($sub['section_id'] == $section['section_id'] ? 'active' : ''). '">' .$sub['name']. '</a
								</li>';
							}
						echo '</ul>';
					}

				echo '</li>';
			}
		}
		echo '</ul>';
	}
echo '</nav>';

?>