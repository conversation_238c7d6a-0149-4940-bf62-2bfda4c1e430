<?php

//My Classifieds
if(PAGE_ID == $_sitepages['my_classifields']['page_id']){
	
	//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
		exit();
	}

	//Restrict to members
	if(!defined('MEMBER_ACCESS') || !MEMBER_ACCESS){
		exit();
	}
	
	//Set vars
	$panel_id = 61;
	$classified = array();
	$classifieds = array();
	$searchable_params = array('title', 'facility_name');
	$required = [];
	$file_fields = [
		'file'
	];

	$filedir = 'uploads/files/';
	
	//Pagination
	$pg = (isset($_GET['pg']) ? $_GET['pg'] : 1);
	$limit = 20;

	$totalresults = 0;
		
	//Deletion alert
	if(isset($_SESSION['deleted'])){
		$alert = $Account->alert('Classified ad was successfully deleted.', true);
		unset($_SESSION['deleted']);
	}

	//Build query
	$params[] = USER_LOGGED_IN;
	$q = "SELECT `classifieds`.*, `facilities`.`facility_name`, `facilities`.`city`, `facilities`.`province` FROM `classifieds` LEFT JOIN `facilities` ON `facilities`.`facility_id` = `classifieds`.`facility_id` WHERE `classifieds`.`account_id` = ?";

	//Text search
	if(isset($_GET['search']) && $_GET['search']){
		$q .= " AND (";
		foreach ($searchable_params as $value){
			$q .= "`classifieds`.`$value` LIKE ? OR ";
			$params[] = '%'.$_GET['search'].'%';
		}
		$q = substr($q, 0, -4). ")"; //Remove excess " OR "

	};

	//Build filtered query
	$query = $db->query($q, $params);
	if($query && !$db->error()){
		if($db->num_rows() > 0){
			$result = $db->fetch_array();
			foreach ($result as $row) {
				$classifieds[$row['classified_id']] = $row;
			}
			
			//Pagination
			if(ITEM_ID == ''){
				$totalresults = count($result);
				if($pg != 'all'){
					$start = (($pg-1)*$limit);
					$end = $limit;
				}else{
					$start = 0;
					$end = $totalresults;
				}
				$classifieds = array_slice($classifieds, $start, $end);
			}
		}
	}

	//Add or Edit Post
	if(ACTION == 'add' || (ACTION == 'edit' && in_array(ITEM_ID, array_keys($classifieds)))){
		
		//Edit
		if(ACTION == 'edit'){
			$classified_id = ITEM_ID;
			$classified = $classifieds[$classified_id];
			$classified['texteditor'] = $classified['description'];
			$file = $classified['file_name'];
		}

		//Load facility data
		$facility_query = $db->query("SELECT * FROM facilities ORDER BY facility_name;");
		if($facility_query && !$db->error()){
			if($db->num_rows() > 0){
				$facilities = $db->fetch_array();
			}
		}

		//Validation
		$required_fields = array('title', 'facility_id', 'first_name', 'last_name', 'email', 'phone', 'texteditor');
		if(isset($_POST['post']) && empty($errors)){
			foreach($required_fields as $field){
				if(!isset($_POST[$field]) || !trim(strip_tags(str_replace('&nbsp;', ' ', $_POST[$field])))){
					$errors[0] = 'Please fill out all the required fields.';
					$required[] = $field;
				}
				if($field == 'phone' && !detectPhone($_POST[$field])){
					$errors[] = 'Please enter a valid phone number.';
					$required[] = $field;
				}
				if($field == 'email' && !checkMail($_POST[$field])){
					$errors[] = 'Please enter a valid email.';
					$required[] = $field;
				}
			}
			if($_FILES['file']['name'] != ''){
				$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

				if($_FILES['file']['error'] == 1){
					$errors[] = 'Unable to upload file. Please try again.';
					$required[] = 'file';
				}else if($_FILES['file']['size'] > 2097152){
					$errors[] = 'Attached file is too large. File size cannot exceed 2MB.';
					$required[] = 'file';
				}else if($ext != 'pdf'){
					$errors[] = 'Invalid file type. Only PDF Documents are accepted.';
					$required[] = 'file';
				}
			}

			//Trim complex content
			$_POST['texteditor'] = (str_replace('<p>&nbsp;</p>', '', $_POST['texteditor']));

			//Valid submission
			if(empty($errors)){
				
				//Upload file
				$file = ($_POST['old_file'] != '' ? $_POST['old_file'] : NULL);
				if(!empty($_FILES['file']['name'])){
					$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
					$filename = clean_url($_POST['title']).'-'.date("ymdhis").'.'.$ext;

					// include("modules/classes/FileUpload.class.php");
					// $fileUpload = new FileUpload();
					// $fileUpload->load($_FILES['file']['tmp_name']);
					// $fileUpload->save($filedir, $newname);
					// if(file_exists($filedir.$newname)){
					// 	$file = $newname;
					// 	if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
					// 		unlink($filedir.$_POST['old_file']);
					// 	}
					// }

						foreach ($file_fields as $field) {
								$$field = NULL;
							
								if (!empty($_FILES[$field]['name'])) {
									// Check for errors during upload
									if ($_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
										$errors[] = "Error uploading file: " . $_FILES[$field]['name'] . " - " . $_FILES[$field]['error'];
										continue; // Skip this file and continue with the next
									}
							
									// Attempt to copy the file
									if (!@copy($_FILES[$field]['tmp_name'], $filedir . $filename)) {
										$errors[] = "Failed to upload the file: " . $_FILES[$field]['name'];
									} else {
										// Validate file exists
										$$field = check_file($filename, $filedir) ?: NULL;
							
										// Add to array for email attachment
										if ($$field) {
											$attachments[] = $filedir . $filename;
										}
									}
								}
								
						}

				}else{
					if(isset($_POST['deletefile']) && $_POST['old_file'] != ''){
						if(file_exists($filedir.$_POST['old_file'])) {
							unlink($filedir.$_POST['old_file']);
						}
						$file = NULL;
					}
				}

				if(is_null($file) || file_exists($filedir.$file)){
					$params = array(
						(isset($classified_id) ? $classified_id : NULL),	
						$_POST['title'], 							
						clean_url($_POST['title']), 				
						$_POST['facility_id'], 							
						$_POST['texteditor'], 							
						USER_LOGGED_IN, 								
						$_POST['first_name'],						
						$_POST['last_name'],						
						$_POST['email'],								
						formatPhoneNumber($_POST['phone']),							
						(isset($_POST['public']) ? 0 : 1),				
						(isset($_POST['showhide']) ? 1 : 0),				
						date("Y-m-d H:i:s"),	
						date("Y-m-d H:i:s"),	
						$file,

						$_POST['title'], 								
						clean_url($_POST['title']), 					
						$_POST['facility_id'], 							
						$_POST['texteditor'], 							
						$_POST['first_name'],							
						$_POST['last_name'],							
						$_POST['email'],								
						formatPhoneNumber($_POST['phone']),				
						(isset($_POST['public']) ? 0 : 1),			
						(isset($_POST['showhide']) ? 1 : 0),			
						date("Y-m-d H:i:s"),
						$file
					);

					$query = $db->query("INSERT INTO `classifieds` (`classified_id`, `title`, `page`, `facility_id`, `description`, `account_id`, `first_name`, `last_name`, `email`, `phone`, `public`, `showhide`, `date_added`, `last_updated`, `file_name`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `title` = ?, `page` = ?, `facility_id` = ?, `description` = ?, `first_name` = ?, `last_name` = ?, `email` = ?, `phone` = ?, `public` = ?, `showhide` = ?, `last_updated` = ?, `file_name` = ?", $params);
					if($query && !$db->error()){
						$id = $db->insert_id();

						$alert = $Account->alert('Ad was successfully '.(ACTION == 'edit' ? 'updated' : 'created').'. Go <a href="'.$_sitepages['classifields']['page_url'].'">back to all</a>'.(isset($_POST['showhide']) ? '' : ', or <a href="'.$_sitepages['classifields']['page_url'] .clean_url($_POST['title']). '-' .$id. '/'.'">view your ad</a>').'.', true);

						if(ACTION == 'add'){
							unset($_POST);
							$classified = array();
						}

					}else{
						$errors[] = 'Unable to submit classified. '.$db->error();
					}
				}else{
					$errors[] = 'Unable to upload file. Please try again.';
					$required[] = 'file';
				}
			}
			
			//Overwrite default values with POST data
			if(!empty($_POST)){
				foreach($_POST AS $key=>$data){
					if($key == 'description'){
						$key = 'texteditor';
					}
					$classified[$key] = $data;
				}
			}

		//Delete
		}else if(isset($_POST['delete'])) {
			$query = $db->query("DELETE from `classifieds` WHERE `classified_id` = ? && `account_id` = ?", array($classified_id, USER_LOGGED_IN));
			if($query && !$db->error()){
				$_SESSION['deleted'] = true;
				
				//Clean up files
				if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
					unlink($filedir.$_POST['old_file']);
				}
				
				header('Location:'.$_sitepages['my_classifields']['page_url']);
				exit();
			}else{
				$errors[] = 'Unable to delete classified. '.$db->error();
			}
		}
		
	//Not found	
	}else if(ACTION == 'edit' && !in_array(ITEM_ID, array_keys($classifieds))){
		$notfound = true;
		$errors[] = 'Classified not found. Please select from the list below.';
	}

}

?>