<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

// if(SECTION_ID == 73){
if(SECTION_ID == $_cmssections['transactions-payments']){
	
	//Define vars
	$record_db = 'payments';
	$record_id = 'payment_id';
	$record_name = 'Payment';
	$errors = false;
	
	$regpage = $sitemap[69];
	$accountpage = $sitemap[7];
	
	//Process payment
	if(ACTION == 'process'){
		include("modules/transactions/Process.php");
	
	//Manage payments
	}else{
		
		//Get GL Accounts
		$glaccounts = array();
		$gldefaults = array();
		$query = $db->query("SELECT * FROM `gl_accounts` ORDER BY `gl_number`");
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				$glaccounts[$row['gl_id']] = $row;
				if(!empty($row['item_no'])){
					$gldefaults[$row['item_no']] = $row;
				}
			}
		}
		
		//Get Records
		$records_arr = array();
		$params = array(' ');
		$where = "";

		if(ITEM_ID != '' && ACTION == 'edit'){
			$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`$record_id` = ?";
			$params[] = ITEM_ID;

		}else{
			if($searchterm != ""){
				$where .= (trim($where) == "" ? " WHERE " : " AND ")."(`$record_db`.`payment_number` LIKE ? OR `reg_registrations`.`registration_number` LIKE ? OR CONCAT(`reg_registrations`.`first_name`, ?, `reg_registrations`.`last_name`) LIKE ? OR `invoices`.`invoice_number` LIKE ? OR CONCAT(`invoices`.`first_name`, ?, `invoices`.`last_name`) LIKE ? OR `$record_db`.`ccname` LIKE ? OR `$record_db`.`amount` LIKE ?)";
				$params[] = '%' .$searchterm. '%';
				$params[] = '%' .$searchterm. '%';
				$params[] = ' ';
				$params[] = '%' .$searchterm. '%';
				$params[] = '%' .$searchterm. '%';
				$params[] = ' ';
				$params[] = '%' .$searchterm. '%';
				$params[] = '%' .$searchterm. '%';
				$params[] = '%' .$searchterm. '%';
			}
			if(!isset($_SESSION['search_start_date'][SECTION_ID]) || isset($_POST['clear-search'])){
				$_SESSION['search_start_date'][SECTION_ID] = date('Y-m-d');
			}
			if(!isset($_SESSION['search_end_date'][SECTION_ID]) || isset($_POST['clear-search'])){
				$_SESSION['search_end_date'][SECTION_ID] = date('Y-m-d');
			}
			if(isset($_GET['start_date'])){
				$_SESSION['search_start_date'][SECTION_ID] = $_GET['start_date'];
			}
			if(isset($_GET['end_date'])){
				$_SESSION['search_end_date'][SECTION_ID] = $_GET['end_date'];
			}
			if(isset($_SESSION['search_start_date'][SECTION_ID]) && $_SESSION['search_start_date'][SECTION_ID] != '') {
				$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`payment_date` >= ?";
				$params[] = date('Y-m-d 00:00:00', strtotime($_SESSION['search_start_date'][SECTION_ID]));
			}
			if(isset($_SESSION['search_end_date'][SECTION_ID]) && $_SESSION['search_end_date'][SECTION_ID] != '') {
				$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`payment_date` <= ?";
				$params[] = date('Y-m-d 23:59:59', strtotime($_SESSION['search_end_date'][SECTION_ID]));
			}
			
			if(isset($_GET['status'])){
				$_SESSION['search_status'][SECTION_ID] = $_GET['status'];
			}
			if(isset($_POST['clear-search'])){
				$_SESSION['search_status'][SECTION_ID] = '';
			}
			if(isset($_SESSION['search_status'][SECTION_ID]) && $_SESSION['search_status'][SECTION_ID] != ''){
				$where .= (trim($where) == "" ? " WHERE " : " AND ")."`$record_db`.`status` = ?";
				$params[] = $_SESSION['search_status'][SECTION_ID];
			}
			
			if(isset($_GET['gl_account'])){
				$_SESSION['search_glnum'][SECTION_ID] = $_GET['gl_account'];
			}
			if(isset($_POST['clear-search'])){
				$_SESSION['search_glnum'][SECTION_ID] = '';
			}
			
		}
		$query = $db->query("SELECT `$record_db`.*, `reg_registrations`.`registration_number`, `invoices`.`invoice_number`, IFNULL(`reg_events`.`gl_id`, `invoices`.`gl_id`) AS `gl_id`, IFNULL(`reg_registrations`.`account_id`, `invoices`.`account_id`) AS `account_id`, IFNULL(`reg_registrations`.`first_name`, `invoices`.`first_name`) AS `first_name`, IFNULL(`reg_registrations`.`last_name`, `invoices`.`last_name`) AS `last_name`, IFNULL(`reg_registrations`.`email`, `invoices`.`email`) AS `email`, IFNULL(`reg_registrations`.`phone`, `invoices`.`phone`) AS `phone`, CONCAT(`account_profiles`.`first_name`, ?, `account_profiles`.`last_name`) AS `processed_by_name` FROM `$record_db` ".
		"LEFT JOIN `reg_registrations` ON `$record_db`.`registration_id` = `reg_registrations`.`registration_id` ".
		"LEFT JOIN `reg_attendees` ON `reg_registrations`.`registration_id` = `reg_attendees`.`registration_id` ".
		"LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ".
		"LEFT JOIN `invoices` ON `$record_db`.`invoice_id` = `invoices`.`invoice_id` ".
		"LEFT JOIN `account_profiles` ON `$record_db`.`processed_by` = `account_profiles`.`account_id`".
		$where. " GROUP BY `$record_db`.`$record_id` ORDER BY `$record_db`.`payment_date` DESC", $params);
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
								
				//Determine gl account
				$row['gl_account'] = NULL;
				if(!empty($row['gl_id'])){
					$row['gl_account'] = $glaccounts[$row['gl_id']]['gl_number'];
				}else{
					if(!empty($row['registration_id'])){
						$item_no = substr($row['registration_number'], -1);
						$row['gl_account'] = $gldefaults[$item_no]['gl_number'];
					}else if(!empty($row['invoice_number'])){
						$item_no = substr($row['invoice_number'], -1);
						$row['gl_account'] = $gldefaults[$item_no]['gl_number'];
					}
				}
				
				if(isset($_SESSION['search_glnum'][SECTION_ID]) && $_SESSION['search_glnum'][SECTION_ID] != ""){
					if($_SESSION['search_glnum'][SECTION_ID] == $row['gl_account']){
						$records_arr[$row[$record_id]] = $row;	
					}
				}else{
					$records_arr[$row[$record_id]] = $row;
				}
				
			}
		}else{
			$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
		}

		//Not found
		if(ACTION == 'edit'){
			if(!array_key_exists(ITEM_ID, $records_arr)){
				$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
				header('Location:' .PAGE_URL);
				exit();
			}else{
				$row = $records_arr[ITEM_ID];
			}
		}

		//Limit actions
		if(ACTION != '' && ACTION != 'edit'){
			header('Location:' .PAGE_URL);
			exit();
		}

		//Save item
		if(isset($_POST['save'])){

			if(ACTION == 'edit' && ITEM_ID != ''){
				$query = $db->query("UPDATE `$record_db` SET `notes` = ? WHERE `$record_id` = ?", array($_POST['notes'], ITEM_ID));	
				if($query && !$db->error()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}else{
					$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
				}
			}
		}
		
	}

}
?>