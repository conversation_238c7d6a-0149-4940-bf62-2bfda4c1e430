<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){
	
	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<a href="' .PAGE_URL. '?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';
	
	//All records
	echo '<div class="panel">
		<div class="panel-header">' .$record_name. 's 
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">
			
				<thead>
					<th width="1px" data-sorter="false"></th>
					<th width="25px" class="center nopadding-h"><i class="fas fa-building"></i>' .$CMSBuilder->tooltip('Head Office', 'Select your head office location. The contact information for this location will be used as the main point of contact.'). '</th>
					<th width="250px">Location Name</th>
					<th>Address</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				foreach($records_arr as $row){
					echo '<tr data-table="' .$record_db. '" data-column-name="' .$record_id. '" data-name="' .$row['location_name']. '" data-id="' .$row[$record_id]. '">
						<td class="handle"><span class="fas fa-arrows-alt"></span></td>
						<td class="center nopadding-h"><input type="radio" name="head_office" id="head-office-' .$row[$record_id]. '" class="head-office radio' .($row['head_office'] ? ' active' : ''). '" value="' .$row[$record_id]. '"' .($row['head_office'] ? ' checked' : ''). ' /><label for="head-office-' .$row[$record_id]. '"></label></td>
						<td>' .$row['location_name']. '</td>
						<td>'.
							$row['line1'].
							//Line 2 is smalltext unless line 1 is falsey
							($row['line1'] && $row['line2'] ? '<br><small>' .$row['line2']. '</small>' : $row['line2']).
							//No address
							(!$row['line1'] && !$row['line2'] ? '<small>N/A</small>' : '').
						'</td>
						<td>' .$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']). '</td>
						<td class="right"><a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-edit"></i> Edit</a></td>
					</tr>';
				}
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';


//Display form
}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		$location_numbers = $data['location_numbers'];
		$location_contacts = $data['location_contacts'];
		$location_hours = $data['location_hours'];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		//General
		echo '<div class="panel">
			<div class="panel-header">General Information
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show '.$record_name.'</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"' .(($row['showhide'] ?? 0) ? '' : ' checked'). ' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>
			<div class="panel-content">
				<div class="flex-container">
				
					<div class="form-field">
						<label>Location Name' .(in_array('location_name', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<input type="text" name="location_name" class="input' .(in_array('location_name', $required) ? ' required' : ''). '" value="' .($row['location_name'] ?? ''). '" />
					</div>
					
					<div class="form-field">
						<label>Email Address' .(in_array('email', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<input type="text" name="email" value="' .($row['email'] ?? ''). '" class="input' .(in_array('email', $required) ? ' required' : ''). '" />
					</div>
					
					<div class="form-field">
						<label class="flex-container">
							<span class="flex-column left">Google Place ID' .(in_array('google_place_id', $required_fields) ? ' <span class="required">*</span>' : ''). '</span>
							<small class="flex-column right"><a href="https://developers.google.com/maps/documentation/javascript/examples/places-placeid-finder" target="_blank">Get Place ID <i class="fas fa-external-link-alt"></i></a></small>
						</label>
						<input type="text" name="google_place_id" class="input' .(in_array('google_place_id', $required) ? ' required' : ''). '" value="' .($row['google_place_id'] ?? ''). '" />
					</div>
					
					<div class="form-field">
						<label>Numerical Order' .(in_array('ordering', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
						<select name="ordering" class="select">
							<option value="101">Default</option>';
							for($i=1; $i<101; $i++){
								echo '<option value="' .$i. '"' .(isset($row['ordering']) && $row['ordering'] == $i ? ' selected' : ''). '>' .$i. '</option>';	
							}
						echo '</select>
					</div>
					
				</div>
			</div>
		</div>'; //General

		//Contact numbers
		echo '<div class="panel">
			<div class="panel-header">Contact Numbers
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">
				<div class="flex-container">
				
					<div class="form-field">
						<label>Phone Number' .(in_array('phone', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<input type="text" name="phone" value="' .($row['phone'] ?? ''). '" class="input' .(in_array('phone', $required) ? ' required' : ''). '" placeholder="(xxx) xxx-xxxx" />
					</div>
					
					<div class="form-field">
						<label>Fax Number' .(in_array('fax', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<input type="text" name="fax" value="' .($row['fax'] ?? ''). '" class="input" placeholder="(xxx) xxx-xxxx" />
					</div>
					
					<div class="form-field">
						<label>Toll Free Number' .(in_array('toll_free', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
						<input type="text" name="toll_free" value="' .($row['toll_free'] ?? ''). '" class="input" placeholder="x (xxx) xxx-xxxx" />
					</div>
					
				</div>
				<div class="flex-container">';
	
					//These numbers are used for Organization microdata on the contact page 
					if(!empty($location_numbers)){
						$count = 0;
						foreach($location_numbers as $phone){
							
							echo '<div class="form-field">
								<label>' .ucwords($phone['type']). '</label>
								<input type="text" name="number[]" value="' .($phone['phone'] ?? ''). '" class="input" placeholder="(xxx) xxx-xxxx" style="margin-bottom:10px" />
								<p>
									<input type="checkbox" class="checkbox" id="tf' .$count. '" name="tollfree_' .$count. '" value="1"' .(($phone['tollfree'] ?? 0) ? ' checked' : ''). ' /> 
									<label for="tf' .$count. '"><small>Toll Free</small></label>
									<input type="checkbox" class="checkbox" id="hi' .$count. '" name="hearingimpaired_' .$count. '" value="1"' .(($phone['hearingimpaired'] ?? 0) ? ' checked' : ''). ' />
									<label for="hi' .$count. '"><small>Hearing Impaired</small></label>
								</p>
								<input type="hidden" name="number_id[]" value="' .($phone['number_id'] ?? ''). '" />
								<input type="hidden" name="type[]" value="' .$phone['type']. '" />
							</div>';
							
							$count++;
						}
					}

				echo '</div>
			</div>
		</div>'; //Contact numbers

		//Additional Contacts
		echo '<div class="panel">
			<div class="panel-header">Additional Contacts' .$CMSBuilder->tooltip('Additional Contacts', '<p>Add additional contact phone numbers and email addresses to the contact page of your website.</p>Note: This is for display purposes only and will <strong>NOT</strong> create new email accounts.'). '
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">
				<table id="location-contacts-table" width="100%" cellspacing="0" cellpadding="0" border="0">
					<thead>
						<tr>
							<th width="260px" class="nopadding-r">Label</th>
							<th width="260px" class="nopadding-r">Email/Phone Number</th>
							<th></th>
						</tr>
					</thead>
					<tbody>';
						if(!empty($location_contacts)){
							foreach($location_contacts as $contact){
								echo '<tr class="location-contact-row">
									<td width="260px" class="nopadding-r"><input type="text" name="contact_label[]" value="' .$contact['label']. '" class="input nomargin" /></td>
									<td width="260px" class="nopadding-r"><input type="text" name="contact_value[]" value="' .$contact['value']. '" class="input nomargin" /></td>
									<td class="right">
										<button type="button" class="button-sm delete-template-btn"><i class="fas fa-trash-alt nomargin"></i></button>
										<input type="hidden" name="contact_id[]" value="' .$contact['contact_id']. '" />
									</td>
								</tr>';
							}
						}
						echo '<tr class="copy-btn-row right">
							<td colspan="3"><button type="button" class="copy-template-btn button-sm"><i class="fas fa-plus"></i>Add Contact</button></td>
						</tr>
					</tbody>
				</table>
				
			</div>
		</div>';

		//Location
		echo '<div class="panel">
			<div class="panel-header">Address &amp; Map
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show Google Map' .$CMSBuilder->tooltip('Show Google Map', 'Display this location on an interactive map (google map must be enabled in settings). You can set your location by using the locate function or by clicking and dragging the map pin.'). '</label>
					<div class="onoffswitch">
						<input type="checkbox" name="google_map" id="google_map" value="1"' .(($row['google_map'] ?? false) ? ' checked' : ''). ' />
						<label for="google_map">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>
			<div class="panel-content">
				<div class="flex-container">
				
					<div id="gllpLatlonPicker" class="gllpLatlonPicker flex-column">
						<div class="gllpMap">Google Maps</div>
						<div class="gllpSearch flex-container input-button">
							<input type="text" class="gllpSearchField input places-autocomplete" placeholder="Find an address..." data-address="#address" data-clear="#clear-location"/>
							<button type="button" class="gllpSearchButton button-sm" id="gllpSearchButton"><i class="fas fa-search nomargin"></i></button>
						</div>
						<input type="hidden" name="gpslat" class="gllpLatitude" value="'.(($row['gpslat'] ?? '') != '' ? $row['gpslat'] : '53.563967'). '"/>
						<input type="hidden" name="gpslong" class="gllpLongitude" value="' .(($row['gpslong'] ?? '') != '' ? $row['gpslong'] : '-113.490357'). '"/>
						<input type="hidden" name="zoom" class="gllpZoom" value="' .(($row['zoom'] ?? 0) != 0 ? $row['zoom'] : '12'). '"/>
					</div>
					
					<div class="flex-container flex-column auto-width">
					
						<div class="form-field">
							<label>
								Street Address'.(in_array('address', $required_fields) ? ' <span class="required">*</span>' : '').'
								<small class="f_right" id="clear-location"><a>Clear Fields</a></small>
							</label>
							<input type="text" id="address" name="address" value="' .($row['address'] ?? ''). '" class="input" />
							<label>Unit No.' .(in_array('address2', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<input type="text" name="address2" value="' .($row['address2'] ?? ''). '" class="input" />
							<label>City/Town' .(in_array('city', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<input type="text" name="city" value="' .($row['city'] ?? ''). '" class="input" />
						</div>
						
						<div class="form-field">
							<label>Province/State' .(in_array('province', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<select name="province" class="select">
								<option value="">- Select -</option>
								<optgroup label="Canada">';
								foreach($provinces as $code=>$name){
									echo '<option value="' .$code. '"' .(($row['province'] ?? '') == $code ? ' selected' : ''). '>' .$name. '</option>';	
								}
								echo '</optgroup>
								<optgroup label="United States">';
								foreach($states as $code=>$name){
									echo '<option value="' .$code. '"' .(($row['province'] ?? '') == $code ? ' selected' : ''). '>' .$name. '</option>';	
								}
								echo '</optgroup>
							</select>

							<label>Country' .(in_array('country', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<select name="country" class="select">
								<option value="">- Select -</option>
								<option value="Canada"' .(($row['country'] ?? '') == 'Canada' ? ' selected' : ''). '>Canada</option>
								<option value="United States"' .(($row['country'] ?? '') == 'United States' ? ' selected' : ''). '>United States</option>
							</select>
							<label>Postal/Zip Code' .(in_array('postal_code', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<input type="text" name="postal_code" value="' .($row['postal_code'] ?? ''). '" class="input" />
						</div>
						
					</div>
					
				</div>
			</div>
		</div>'; //Location

		//Business hours
		echo '<div class="panel">
			<div class="panel-header">Business Hours
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show Hours' .$CMSBuilder->tooltip('Show Hours', 'Display your business hours on the contact page.'). '</label>
					<div class="onoffswitch">
						<input type="checkbox" name="show_hours" id="show_hours" value="1"' .(($row['show_hours'] ?? 0) ? ' checked' : ''). ' />
						<label for="show_hours">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>
			<div class="panel-content nopadding">';

			if(is_array($location_hours)){
				echo '<table cellpadding="0" cellspacing="0" border="0" id="hours">';

					for($h=1; $h<=count($location_hours); $h++){
						$hours = $location_hours[$h-1];
						$start_time = date('G:i', strtotime($hours['start_time']));
						$end_time = date('G:i', strtotime($hours['end_time']));

						echo '<tr id="hours-row-'.$h.'">
							<td width="140px">' .$hours['day']. '</td>
							<td width="120px"><select name="start_time'.$h.'" class="select select_sm nomargin"' .($hours['closed'] ? ' disabled' : ''). '>';
								for($s=0; $s<24; $s++) {
									echo '<option value="' .$s. ':00"' .($start_time == $s.":00" ? " selected" : ""). '>' .date("g:i a", strtotime($s.":00")). '</option>';
									echo '<option value="' .$s. ':30"' .($start_time == $s.":30" ? " selected" : ""). '>' .date("g:i a", strtotime($s.":30")). '</option>';
								}
								echo '</select>
							</td>
							<td width="10px" class="nopadding">to</td>
							<td width="120px"><select name="end_time'.$h.'" class="select select_sm nomargin"' .($hours['closed'] ? ' disabled' : ''). '>';
								for($e=0; $e<24; $e++) {
									echo '<option value="' .$e. ':00"' .($end_time == $e.':00' ? ' selected' : ''). '>' .date("g:i a", strtotime($e.":00")). '</option>';
									echo '<option value="' .$e. ':30"' .($end_time == $e.':30' ? 'selected' : ''). '>' .date("g:i a", strtotime($e.":30")). '</option>';
								}
								echo '</select>
							</td>
							<td class="nopadding-l"><input type="checkbox" class="checkbox disable" name="closed' .$h. '" id="closed' .$h. '" value="1" data-disable="#hours-row-'.$h.' .select"' .($hours['closed'] ? ' checked' : ''). ' /><label for="closed' .$h. '">Closed</label>
							<input type="hidden" name="hours_id' .$h. '" value="' .$hours['hours_id']. '" /></td>
						</tr>';
					}

						echo '<tr>
							<td>Open Text' .$CMSBuilder->tooltip('Open Text', 'Text to display when you are currently open. Defaults to &quot;Currently Open&quot;.'). '</td>
							<td colspan="4"><input type="text" name="open_text" class="input nomargin" value="' .($row['open_text'] ?? ''). '" /></td>
						</tr>
						<tr>
							<td>Closing Text' .$CMSBuilder->tooltip('Closing Text', 'Text to display one hour before closing. Defaults to &quot;Closing Soon&quot;.'). '</td>
							<td colspan="4"><input type="text" name="closing_text" class="input nomargin" value="' .($row['closing_text'] ?? ''). '" /></td>
						</tr>
						<tr>
							<td>Closed Text' .$CMSBuilder->tooltip('Closed Text', 'Text to display when you are currently closed. Defaults to &quot;Currently Closed&quot;.'). '</td>
							<td colspan="4"><input type="text" name="closed_text" class="input nomargin" value="' .($row['closed_text'] ?? ''). '" /></td>
						</tr>
						<tr>
							<td>Disclaimer' .$CMSBuilder->tooltip('Disclaimer', 'Business hours disclaimer<br /><small>(e.g. Closed on Holidays)</small>'). '</td>
							<td colspan="4"><input type="text" name="hours_disclaimer" class="input nomargin" value="' .($row['hours_disclaimer'] ?? ''). '" /></td>
						</tr>

					</table>';
				
				}

			echo "</div>";
		echo "</div>"; //Business hours

		//Sticky footer
		echo '<footer id="cms-footer">
			<div class="flex-container">
				<div class="flex-column right">
					<button type="submit" class="button" name="save" value="save"><i class="fas fa-check"></i>Save Changes</button>
				</div>
				<div class="flex-column left">';
					if(ITEM_ID != ""){
						echo ($row['head_office'] ? $CMSBuilder->tooltip('Delete Location', 'Head office location cannot be deleted. If you wish to remove this location, set it to hidden instead.').'&nbsp;' : '');
						echo '<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn"' .($row['head_office'] ? ' disabled' : ''). '><i class="fas fa-trash-alt"></i>Delete</button>';
					}
					echo '<a href="' .PAGE_URL. '" class="cancel">Cancel</a>
				</div>
			</div>
		</footer>';

		echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

	//Templates
	echo '<table class="hidden">
		<tr id="location-contact-template" class="location-contact-row">
			<td width="260px" class="nopadding-r"><input type="text" name="contact_label[]" value="" class="input nomargin" /></td>
			<td width="260px" class="nopadding-r"><input type="text" name="contact_value[]" value="" class="input nomargin" /></td>
			<td class="right">
				<button type="button" class="button-sm delete-template-btn"><i class="fas fa-trash-alt nomargin"></i></button>
				<input type="hidden" name="contact_id[]" value="" />
			</td>
		</tr>
	</table>';

}

?>