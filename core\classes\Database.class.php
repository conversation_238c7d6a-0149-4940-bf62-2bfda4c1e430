<?php

/*-----------------------------------/
* MySQLi class for handling database interactions
* <AUTHOR> Army
* @date		14-17-11
* @file		Database.class.php
*/

class Database{
	
	/*-----------------------------------/
	* @var Database
	* Instance of self
	*/
	protected static $Database;
	
	/*-----------------------------------/
	* @var mysqli
	* MySQLi instance - can utilize native mysqli functions
	*/
	public $mysqli;
	
	/*-----------------------------------/
	* @var mysqli
	* String - Last query called, with params replaced
	*/
	public $last_query;
	
	/*-----------------------------------/
	* @var mysqli
	* String - Number of rows affected by an INSERT/UPDATE/DELETE
	*/
	protected $affected_rows;
	
	/*-----------------------------------/
	* @var querystr
	* String - Last query called
	*/
	protected $querystr;
	
	/*-----------------------------------/
	* @var result
	* Array - Result of the last query
	*/
	protected $result;
	
	/*-----------------------------------/
	* @var error
	* String - Last mysqli/statement error
	*/
	protected $error;
	
	/*-----------------------------------/
	* @var transaction_status
	* Boolean - A transaction is in progress
	*/
	protected $transaction_status;
	
	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @return	Object		New Database object
	*/
	public function __construct(){
		$this->transaction_status = false;
		$this->connect();
	}
	
	/*-----------------------------------/
	* Connect to your database
	*
	* <AUTHOR> Army
	* @return	New database connection
	*/
	public function connect(){
		$this->mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_TABLE, DB_PORT);
		if($this->mysqli->connect_errno > 0){
			die('Cannot connect to database [' . $this->mysqli->connect_error . ']');
		}
		$this->mysqli->set_charset('utf8');	
		$this->mysqli->query("SET SESSION sql_mode=''");
	}
	
	/*-----------------------------------/
	* Gets the current Database instance
	*
	* <AUTHOR> Army
	* @return	Object	Current Database instance
	*/
	public static function get_instance(){
		if(!self::$Database){
			self::$Database = new Database();
		}

		return self::$Database;
	}
	
	/*-----------------------------------/
	* Runs a query statement
	*
	* <AUTHOR> Army
	* @param	$query		The query string to be run
	* @return	Boolean		True/False on success or failure
	*/
	public function query($query, $bind_params = []){
		
		$success = false;

		//Clear SQL comments
		$query = preg_replace('/(?:^|\n)(.*)(?:--|#).*/', '$1', $query);

		//Clear tabs, newlines, and excess whitespace
		$query = preg_replace('/\s+/', ' ', str_replace(["\n", "\t"], " ", $query));

		$last_query = $query;

		//Reset query-related parameters
		$this->querystr      = $this->mysqli->real_escape_string($query);
		$this->affected_rows = 0;
		$this->result        = [];
		
		//Prepare query
		if($stmt = $this->mysqli->prepare($this->querystr)){
			
			//Bind params if necessary
			if(!empty($bind_params)){
				$params = [''];

				foreach($bind_params as $key => $val){
					$params[0] .= $this->get_param_type($val);
					$params[] = $bind_params[$key];
				}

				//Call stmt method with a dynamic number of params
				call_user_func_array([$stmt, 'bind_param'], $this->get_ref_values($params));
			}
			
			//Execute query
			if($stmt->execute()){
				
				// Update affected rows
				$this->affected_rows = $stmt->affected_rows;
				
				//Fetch results
				$this->result = $this->bind_results($stmt);
				
				//Clear error if not running a transaction so it won't carry over to next query you run
				if(!$this->transaction_status){
					$this->error = NULL;
				}
				
				$success = true;
				
			}else{
				$this->affected_rows = -1;
				$this->error = 'Stmt Error: ' .$stmt->error;
				trigger_error("Problem executing query (" .$this->querystr. "): " . $this->error);
			}
		}else{
			$this->affected_rows = -1;
			$this->error = 'Mysqli Error: ' .$this->mysqli->error;
			trigger_error("Problem preparing query (" .$this->querystr. "): " . $this->error);
		}

		// Replace first occurrence of ?
		foreach($bind_params as $key => $val){
			$type = $this->get_param_type($val);
			$type = is_numeric($val) || is_null($val) ? 'n' : $type;
			$val  = is_null($val) ? 'NULL' : $val;
			$pos  = strpos($last_query, '?');
			if($pos !== false) {
				$last_query = substr_replace($last_query, $type == 's' ? '"'.str_replace('?', '&#63;', $val).'"' : $val, $pos, 1);
			}
		}

		$this->last_query = $last_query;
		
		return $success;	
	}
	
	/*-----------------------------------/
	* Binds results of the prepared query
	*
	* <AUTHOR> Army
	* @return	Array		Array of data from query
	*/
	public function bind_results(mysqli_stmt $stmt){
		$meta    = $stmt->result_metadata();
		$params  = [];
		$results = [];
		$row     = [];

		//No sql error and meta is false, so most likely an update/insert/delete which has no results
		if($meta && $stmt->sqlstate){

			//Build result array
			while($field = $meta->fetch_field()){
				$row[$field->name] = null;
				$params[] = &$row[$field->name];
			}
			
			//Call stmt method with a dynamic number of params
			call_user_func_array([$stmt, 'bind_result'], $params);
			
			while($stmt->fetch()){
				$x = [];

				foreach($row as $key => $val){
					$x[$key] = $val;
				}

				$results[] = $x;
			}
			
			//Close connection
			$stmt->close();
		}

		return $results;
	}

	/*-----------------------------------/
	* Shorthand insert query using an associative array of fields and values
	*
	* <AUTHOR> Army
	* @param	$record_db		Table name
	* @param	$insert_params	Array of fields as keys and values as columns to be inserted
	* @param	$update_params	Array of fields as keys and values as columns to be updated, in the case of duplicate keys
	* @return	Int|NULL		ID of affected record, or NULL on failure
	*/
	public function insert(string $record_db, array $insert_params, array $update_params = []){
		$insert_fields = "`".implode("`,`", array_keys($insert_params))."`";
		$insert_values = implode(",", array_fill_keys(array_keys($insert_params), "?"));
		$update_fields = "`".implode("` = ?, `", array_keys($update_params))."` = ?";
		$params        = array_merge(array_values($insert_params), array_values($update_params));

		$success = $this->query("INSERT INTO `$record_db` ($insert_fields) VALUES ($insert_values)".($update_params ? " ON DUPLICATE KEY UPDATE $update_fields" : ""), $params);

		return $success ? $this->insert_id() : NULL;
	}
	
	/*-----------------------------------/
	* Return results of your query in a numeric array
	*
	* <AUTHOR> Army
	* @return	Array		Array of results for executed query
	*/
	public function fetch_array(){
		return $this->result;
	}
	
	/*-----------------------------------/
	* Return results of your query in an associative array
	*
	* <AUTHOR> Army
	* @param	$record_id	The ID of the column to use as an array key
	* @return	Array		Array of results for executed query
	*/
	public function fetch_assoc($record_id){
		return array_column($this->result, null, $record_id);
	}
	
	/*-----------------------------------/
	* Gets the number of rows returned for last query run
	*
	* <AUTHOR> Army
	* @return	Int		Number of rows returned
	*/
	public function num_rows(){
		return count($this->result);
	}
	
	/*-----------------------------------/
	* Gets the auto increment value of last value inserted for last query run
	*
	* <AUTHOR> Army
	* @return	Int		Auto increment value (ID)
	*/
	public function insert_id(){
		return $this->mysqli->insert_id;
	}
	
	/*-----------------------------------/
	* Gets number of rows affected by the last query (INSERT/UPDATE/DELETE)
	*
	* <AUTHOR> Army
	* @return	Int		Number of affected rows (-1 on error, 0 if the query was not executed)
	*/
	public function affected_rows(){
		return $this->affected_rows;
	}
	
	/*-----------------------------------/
	* Gets any errors for last query run
	*
	* <AUTHOR> Army
	* @return	String		Error message
	*/
	public function error(){
		return $this->error;
	}
	
	/*-----------------------------------/
	* Gets last query string submitted
	*
	* <AUTHOR> Army
	* @return	String		Query string
	*/
	public function last_query($bind_params = true){
		return $bind_params ? $this->last_query : $this->querystr;
	}
	
	
	/*-----------------------------------/
	* Returns true or false if a transaction is in progress
	*
	* <AUTHOR> Army
	* @return	String		Query string
	*/
	public function get_transaction_status(){
		return $this->transaction_status;
	}
	
	/*-----------------------------------/
	* Starts a new mysqli transaction clearing old transaction data and turning off autocommit
	* IMPORTANT: Transactions will only work on innoDB tables
	*
	* <AUTHOR> Army
	*/
	public function new_transaction(){
		
		//close any transactions already started but not finished
		if($this->transaction_status){
			$this->rollback();
		}
		
		$this->mysqli->autocommit(false);
		$this->transaction_status = true;
	}
	
	/*-----------------------------------/
	* Commits and ends current mysqli transaction and sets autocommit back to true
	*
	* <AUTHOR> Army
	*/
	public function commit(){
		$this->mysqli->commit();
		$this->transaction_status = false;
		$this->mysqli->autocommit(true);
	}
	
	/*-----------------------------------/
	* Clears and ends uncommitted transaction and sets autocommit back to true
	*
	* <AUTHOR> Army
	*/
	public function rollback(){
		$this->mysqli->rollback();
		$this->transaction_status = false;
		$this->mysqli->autocommit(true);
		$this->error = NULL;
	}
	
	/*-----------------------------------/
	* Keep unused connections open on long-running scripts, or reconnect timed out connections
	*
	* <AUTHOR> Army
	* @return	Boolean		True if connection is up
	*/
	public function ping(){
		return $this->mysqli->ping();
	}

	/*-----------------------------------/
	* Gets the value of a single field from a single record
	*
	* <AUTHOR> Army
	* @param	$record_db	Table name
	* @param	$column		Table column to fetch
	* @param	$record_id	The column name of the requested record's ID
	* @param	$item_id	The ID of the record to fetch
	* @return	mixed		The value of the requested field, NULL on error
	*/
	public function get_db_param($record_db, $column, $record_id, $item_id){
		$this->query("SELECT `$column` FROM `$record_db` WHERE `$record_id` = ?", [$item_id]);
		return $this->fetch_array()[0][$column] ?? NULL;
	}
	
	/*-----------------------------------/
	* Determine the type of a variable
	*
	* <AUTHOR> Army
	* @param	$var		Pass a variable to determine the type
	* @return	String		Variable type (string - s, boolean - i, integer - i, blob - b, double - d)
	*/
	public function get_param_type($var){
		switch(gettype($var)){
			case 'NULL':
			case 'string':
				return 's';
				break;

			case 'boolean':
			case 'integer':
				return 'i';
				break;

			case 'blob':
				return 'b';
				break;

			case 'double':
				return 'd';
				break;
		}

		return '';
	}
	
	/*-----------------------------------/
	* Get reference values for an array as reference is required for PHP 5.3+
	*
	* <AUTHOR> Army
	* @param	$array		Array to manipulate
	* @return	Array		Array of references
	*/
	protected function get_ref_values($array){
		if(strnatcmp(phpversion(), '5.3') >= 0){
			$refs = [];
			foreach($array as $key => $value){
				$refs[$key] = & $array[$key];
			}
			return $refs;
		}
		return $array;
	}
	
	/*-----------------------------------/
	* Get an array of enum values for a given table field
	*
	* <AUTHOR> Army
	* @param	$record_db	Table name
	* @param	$record_id	Field name
	* @return	Array		Array of values
	*/
	public function get_enum_vals($record_db, $record_id){
		$prev_result = $this->result;
		$this->query("SHOW COLUMNS FROM `$record_db` WHERE FIELD = ?", [$record_id]);
		if($values = $this->fetch_array()[0]['Type'] ?? false){
			$response = explode("','",preg_replace("/(enum|set)\('(.+?)'\)/","\\2", $values));
		}

		$this->result = $prev_result; // Reset result param

		return $response ?? [];
	}

	/*-----------------------------------/
	* Get an array of column names for a given table
	*
	* <AUTHOR> Army
	* @param	$record_db	Table name
	* @param	$skip_cols	Columns to exclude
	* @return	Array		Array of columns
	*/
	public function get_db_columns($record_db, $skip_cols=array()){
		$response = array();
		$query = $this->query("SHOW COLUMNS FROM `".$record_db."`");
		if($query){
			$result = $this->fetch_array();
			foreach($result as $row){
				if(in_array($row['Field'], $skip_cols)){continue;}
				$response[] = $row['Field'];
			}
		}

		return $response;
	}
	
	/*-----------------------------------/
	* Gets total row count for given table
	*
	* <AUTHOR> Army
	* @param	$record_db		Table name
	* @return	Number|NULL		Total rows in table, NULL on error
	*/
	public function get_record_count($record_db){
		$this->query("SELECT COUNT(*) AS `total` FROM `$record_db`");
		return $this->fetch_array()[0]['total'] ?? NULL;
	}

	/*-----------------------------------/
	* Public destructor function frees results and closes connection
	*
	* <AUTHOR> Army
	*/
	public function __destruct(){
		if($this->mysqli){
			$this->mysqli->close();
		}
	}
}


?>