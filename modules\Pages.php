<?php

//Build navigation
$navmenu = '';
$footermenu = '';
foreach($navigation as $nav){
	$navmenu .= build_menu($nav, 1);
	$nav['sub_pages'] = [];
	$footermenu .= build_menu($nav, 2);
}

//Preload images
$_preloadimages = [];

//Home page slideshow
$slideshow = array();
if(PAGE_ID == $_sitepages['home']['page_id']){
	$db->query("SELECT * FROM `slideshow` WHERE `showhide` = 0 ORDER BY `ordering`");
	$slideshow = $db->fetch_assoc('slide_id');
	
	$slidecount = 0;
	foreach($slideshow as $slide_id => $slide){
		
		//Keyword inserts
		$slide['theme'] = $slide['theme'] ?: $page['theme'];
		$slide['title'] = $SiteBuilder->replace_keyword_inserts($slide['title']);
		$slide['content'] = $SiteBuilder->replace_keyword_inserts($slide['content']);
		$slide['url_text'] = $SiteBuilder->replace_keyword_inserts($slide['url_text']);
		$slide['url_text2'] = $SiteBuilder->replace_keyword_inserts($slide['url_text2']);
		$slideshow[$slide_id] = $slide;
		
		//Preload first slide image
		$slidecount++;
		if($slidecount == 1 && check_file($slide['image'], 'images/slideshow/480/')){
			$page['lcp'] = $path.'images/slideshow/480/'.$slide['image'];
		}
	}
}

//Page banner
if((PAGE_ID != $_sitepages['home']['page_id'] || empty($slideshow)) && !LANDING){
	if(!$page['image_showhide'] && check_file($page['banner_image'], 'images/heroes/480/')){
		$page['lcp'] = $path.'images/heroes/480/'.$page['banner_image'];
	}
}

//Check for page plugins
$lightgallery = PARENT_ID == $_sitepages['gallery']['page_id'] && PAGE_ID == '';
if(!empty($page['page_panels'])){
	foreach($page['page_panels'] as $panel){
		if(($panel['panel_type'] ?? '') == 'gallery'){
			$lightgallery = true;
			break;
		}
		if(($panel['panel_type'] ?? '') == 'side' && $panel['video_url']){
			$lightgallery = true;
			break;
		}
	}
}

//PD program
if(PAGE_ID == 59){
	
	$pd_settings = array('current_year' => date('Y'));
	$query = $db->query("SELECT * FROM `pd_settings` WHERE `id` = 1");
	if($query && !$db->error() && $db->num_rows()){
		$pd_settings = $db->fetch_array()[0];
	}
	
	$membership_types = array();
	$query = $db->query("SELECT `membership_name`, `membership_id` FROM `membership_types`");
	if($query && !$db->error()){
		$membership_types = $db->fetch_array();
	}
}

//Define page constants
define('NAVIGATION', $navmenu);
define('FOOTER_NAVIGATION', $footermenu);
define('SLIDESHOW', !empty($slideshow));
define('LOAD_SWIPER', $lightgallery || count($slideshow) > 1);
define('LOAD_GALLERY', $lightgallery);

?>