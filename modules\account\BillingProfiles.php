<?php
//Display billing profiles
if(PAGE_ID == $_sitepages['billing-profiles']['page_id']){

	//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
		exit();
	}

	//Define vars
	$panel_id = 62;
	$profiles = array();
	$errors = array();
	$required = array();
	$payment_options = $Registration->get_payment_options();

	//Deletion alert
	if(isset($_SESSION['deleted'])){
		$alert = $Account->alert('Billing profile was successfully deleted.', true);
		unset($_SESSION['deleted']);
	}

	//Get all profiles
	$query = $db->query("SELECT * FROM `account_billing_profiles` WHERE `account_id` = ? ORDER BY `date_added` ASC", array(USER_LOGGED_IN));
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$profiles[$row['billing_id']] = $row;
			$profiles[$row['billing_id']]['exp_month'] = substr($row['ccexpiry'], 0, 2);
			$profiles[$row['billing_id']]['exp_year'] = substr($row['ccexpiry'], -2, 2);

			//Check card expiry
			$profiles[$row['billing_id']]['expired'] = false;
			$expiry_date = '20'.substr($row['ccexpiry'], -2, 2).substr($row['ccexpiry'], 0, 2).'01';
			if($expiry_date <= date("Ymd")){
				$profiles[$row['billing_id']]['expired'] = true;
			}
		}
	}else{
		$errors[] = 'Error retrieving billing profiles: '.$db->error();
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $profiles)){
			$errors[] = 'Billing profile not found. Please select from the list below.';
		}else{
			$profile = $profiles[ITEM_ID];
		}
	}

	if(ACTION == 'add' || ACTION == 'edit'){

		//Save profile
		if(isset($_POST['save'])){

			//Set state/region
			if($_POST['bill_country'] == 'US'){
				// $_POST['bill_province'] = $_POST['bill_state'];
			}else if($_POST['bill_country'] != 'CA' && $_POST['bill_country'] != 'US' && $_POST['bill_country'] != ''){
				$_POST['bill_province'] = $_POST['bill_region'];
			}

			//Set state/region
			// if($_POST['bill_country'] != 'CA' && $_POST['bill_country'] != 'US' && $_POST['bill_country'] != ''){
			// 	$_POST['bill_province'] = $_POST['bill_region'];  // Only handle non-US/CA countries
			// }

			//Validate
			$required_fields = array('bill_address1', 'bill_city', 'bill_province', 'bill_postalcode', 'bill_country', 'ccname', 'ccnumber', 'exp_month', 'exp_year');
			foreach($required_fields as $field){
				if(!isset($_POST[$field]) || trim($_POST[$field]) == ''){
					$errors[0] = 'Please fill out all the required fields.';
					array_push($required, $field);
				}
			}

			//Check card expiry
			if(!empty($_POST['exp_month']) && !empty($_POST['exp_year'])){
				$expiry_date = '20'.$_POST['exp_year'].$_POST['exp_month'].'01';
				if($expiry_date <= date("Ymd")){
					$errors[] = 'Credit card is expired. Please check the expiry date.';
					array_push($required, 'exp_month');
					array_push($required, 'exp_year');
				}
			}

			//Check card type
			if(!empty($_POST['ccnumber']) && !strstr($_POST['ccnumber'], '*')){
				$valid_card = false;
				$accepted_cards = array();
				foreach($payment_options as $payopt){
					$accepted_cards[] = $payopt['name'];
					if(get_card_type($_POST['ccnumber']) == $payopt['type']){
						$valid_card = true;
					}
				}
				if(!$valid_card){
					$errors[] = 'Invalid card type. We currently only accept '.implode(' &amp; ', $accepted_cards).'.';
					$required[] = 'ccnumber';
				}
			}

			// echo "<pre> POST - ";print_r($_POST);
			// echo "<pre> Errors- ";print_r($errors);

			//All valid
			if(empty($errors)){
				//Set request data
				$request = array(
					'type' => (ACTION == 'add' ? 'profileAddRequest' : 'profileChangeRequest'),
					'name' => $_POST['ccname'],
					'email' => $Account->email,
					'phone' => $Account->phone,
					'bill_address1' => $_POST['bill_address1'],
					'bill_address2' => $_POST['bill_address2'],
					'bill_city' => $_POST['bill_city'],
					'bill_province' => $_POST['bill_province'],
					'bill_country' => $_POST['bill_country'],
					'bill_postalcode' => $_POST['bill_postalcode'],
					'exp_month' => $_POST['exp_month'],
					'exp_year' => $_POST['exp_year'],
					'ref_number' => (ACTION == 'add' ? '' : $profile['ref_number'])
				);
				if(!strstr($_POST['ccnumber'], '*')){
					$request['ccnumber'] = $_POST['ccnumber'];
				}

				// TODO enable for production
				//Process request
				// include("includes/orbital/request.php");

				//Save to database
				// TODO enable for production
				// if($trxnResponse['status'] == 1 && !empty($trxnResponse['ref_number'])){

				// temp var
				$trxnResponse = [];
				$trxnResponse['ref_number'] = '';

					$params = array(
						ITEM_ID,
						USER_LOGGED_IN,
						$trxnResponse['ref_number'],
						$request['bill_address1'],
						$request['bill_address2'],
						$request['bill_city'],
						$request['bill_province'],
						$request['bill_postalcode'],
						$request['bill_country'],
						$_POST['ccname'],
						get_card_type($_POST['ccnumber']),
						substr($_POST['ccnumber'], -4, 4),
						$request['exp_month'].$request['exp_year'],
						date("Y-m-d H:i:s"),
						date("Y-m-d H:i:s"),

						$request['bill_address1'],
						$request['bill_address2'],
						$request['bill_city'],
						$request['bill_province'],
						$request['bill_postalcode'],
						$request['bill_country'],
						$_POST['ccname'],
						(strstr($_POST['ccnumber'], '*') ? $profile['cctype'] : get_card_type($_POST['ccnumber'])),
						(strstr($_POST['ccnumber'], '*') ? $profile['ccnumber'] : substr($_POST['ccnumber'], -4, 4)),
						$request['exp_month'].$request['exp_year'],
						date("Y-m-d H:i:s")
					);
					$query = $db->query("INSERT INTO `account_billing_profiles`(`billing_id`, `account_id`, `ref_number`, `bill_address1`, `bill_address2`, `bill_city`, `bill_province`, `bill_postalcode`, `bill_country`, `ccname`, `cctype`, `ccnumber`, `ccexpiry`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `bill_address1`=?, `bill_address2`=?, `bill_city`=?, `bill_province`=?, `bill_postalcode`=?, `bill_country`=?, `ccname`=?, `cctype`=?, `ccnumber`=?, `ccexpiry`=?, `last_updated`=?", $params);
					if($query && !$db->error()){
						$alert = $Account->alert('Billing profile has been successfully saved.', true);
						header('Location: '.$page['page_url']);
						exit();
					}else{
						$errors[] = 'Error saving billing profile: '.$db->error();
						$alert 	= $Account->alert(' '.implode("<br />", $errors), false);
						header('Location: '.$page['page_url']);
						exit();
					}

				// TODO enable for production
				//Trxn error
				// }else{
				// 	$errors[] = 'Error saving billing profile: '.$trxnResponse['message'];
				// }
			} else {
				$alert 	= $Account->alert(" " .implode("<br />", $errors), false);

				// header('Location: '.$page['page_url']);
				// exit();
			}

			//Save posted vars
			foreach($_POST as $key=>$data){
				$profile[$key] = $data;
			}


		//Delete profile
		}else if(isset($_POST['delete'])){

			//Set request data
			$request = array(
				'type' => 'profileDeleteRequest',
				'ref_number' => $profile['ref_number']
			);

			// TODO enable for production
			//Process request
			// include("includes/orbital/request.php");

			//Delete from database
			// TODO enable for production
			// if($trxnResponse['status'] == 1 || (isset($trxnResponse['status_code']) && $trxnResponse['status_code'] == '9581')){
				$query = $db->query("DELETE FROM `account_billing_profiles` WHERE `account_id` = ? && `billing_id` = ?", array(USER_LOGGED_IN, ITEM_ID));
				if($query && !$db->error()){
					$alert = $Account->alert('Billing profile has been successfully deleted.', true);
					$_SESSION['deleted'] = true;
					header('Location: '.$page['page_url']);
					exit();
				// TODO enable for production
				}else{
					$errors[] = 'Error deleting billing profile: '.$db->error();
				}

			// TODO enable for production
			//Trxn error
			// }else{
			// 	$errors[] = 'Error deleting billing profile: '.$trxnResponse['message'];
			// }

		}

	}

}
?>