<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('inquiries');
	$CMSBuilder->set_widget($_cmssections['inquiries'], 'Total Inquiries', $total_records);
}

if(SECTION_ID == $_cmssections['inquiries']){
	
	//Define vars
	$record_db   = 'inquiries';
	$record_id   = 'inquiry_id';
	$record_name = 'Inquiry';

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = [
		"$record_db.name",
		"$record_db.email",
		"$record_db.inquiry",
		"$record_db.subject"
	];	

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get inquiries
	$db->query("SELECT * FROM $record_db $where ORDER BY timestamp DESC", $params);
	$records_arr = $db->fetch_assoc($record_id);

	$read = count(array_filter(array_column($records_arr, 'date_reviewed')));
	$unread = count($records_arr) - $read;

	//Not found
	if(ACTION == 'edit'){
		if(!isset($records_arr[ITEM_ID])){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();

		// Update unread record
		} else if (!$records_arr[ITEM_ID]['date_reviewed']) {
			$db->query("UPDATE $record_db SET date_reviewed = ? WHERE $record_id = ?", [date('Y-m-d H:i:s'), ITEM_ID]);
		}
	}
	
	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", [ITEM_ID]);
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	}

	// Export listings
	if(ACTION == '' && isset($_POST['export'])){
		include('exports/export-inquiries.php');
	}

	// Mark all as read
	if(ACTION == '' && isset($_POST['read'])){
		$ids = implode(',', array_keys($records_arr));

		if ($ids) {
			$db->query("UPDATE $record_db SET date_reviewed = ? WHERE $record_id IN ($ids)", [date('Y-m-d H:i:s')]);
			header("Location: " .PAGE_URL);
			exit();
		}
	}
}

?>