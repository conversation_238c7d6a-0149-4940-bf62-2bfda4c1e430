<?php
	
//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>".EVENT_CODE." Categories  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='stickyheader sortable'>";
		
			echo "<thead>";
			echo "<th width='10px' class='{sorter:false}'></th>";
			echo "<th width='0px' class='nopadding'></th>";
			echo "<th width='350px'>Name</th>";
			echo "<th class='{sorter:false}'>Parent ".$record_name."</th>";
			echo "<th width='70px'>Visible</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				$records_arr[$row['parent_id']]['sub_items'] = (!isset($records_arr[$row['parent_id']]['sub_items']) ? array() : $records_arr[$row['parent_id']]['sub_items']);

				echo "<tr data-level='".$row['lvl']."' data-table='$record_db' data-column-name='$record_id' data-name='".$row['name']."' data-id='".$row[$record_id]."' class='".($row['parent_id'] != "" ? " push lvl".$row['lvl'] : "").(!empty($row['sub_items']) ? " has_sub" : "").($row['parent_id'] != "" && array_search($row[$record_id], array_keys($records_arr[$row['parent_id']]['sub_items'])) == (count($records_arr[$row['parent_id']]['sub_items'])-1) && empty($row['sub_items']) ? " last_child" : "")."' data-system-page='true'>";
					echo "<td class='handle'><span class='fa fa-arrows'></span></td>";
					echo "<td class='nopadding' width='0px'></td>";
					echo "<td class='show-lvl'>" .$row['name']. "</td>";
					echo "<td>" .(isset($row['parent_name']) ? $row['parent_name'] : ''). "</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
		echo "</div>";	
	echo "</div>";

} else {

	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	} else if(ACTION == 'add' && !isset($_POST['save'])){	
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		// Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				<div class='panel-switch'>
					<label>Show $record_name</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " />
						<label for='showhide'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>$record_name Name <span class='required'>*</span></label>
					<input type='text' name='name' value='" .(isset($row['name']) ? $row['name'] : ''). "' class='input" .(in_array('name', $required) ? ' required' : ''). "' />
				</div>";
				if(ITEM_ID == '' || ITEM_ID > 2){
					echo "<div class='form-field'>
						<label>Parent $record_name <span class='required'>*</span></label>
						<select name='parent_id' class='select" .(in_array('parent_id', $required) ? ' required' : ''). "'>
							<option value=''>- Select -</option>";
							foreach($records_arr as $parent) {
								if($parent[$record_id] != ITEM_ID && $parent['parent_id'] == ""){
									echo "<option value='".$parent[$record_id]."'".(isset($row['parent_id']) && $row['parent_id'] == $parent[$record_id] ? " selected" : "").">".$parent['name']."</option>";
								}
							}
						echo "</select>
					</div>";
				}
				echo "<div class='form-field'>
					<label>Numerical Order" .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). "</label>
					<select name='ordering' class='select'>
						<option value='101'>Default</option>";
						for($i=1; $i<101; $i++){
							echo "<option value='" .$i. "' " .(isset($row['ordering']) && $row['ordering'] == $i ? "selected" : ""). ">" .$i. "</option>";	
						}
					echo "</select>
				</div>";
			echo "</div>";
		echo "</div>"; // END Details

		// //Sticky footer
		// echo "<footer id='cms-footer' class='resize'>";
		// 	echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
		// 	if(ITEM_ID != ""){
		// 		echo (!$row['deletable'] ? $CMSBuilder->tooltip('Delete Category', 'Due to the dynamic nature of this category, it cannot be deleted. If you wish to remove this category, set it to hidden instead.').'&nbsp;' : '');
		// 		echo "<button type='button' name='delete' value='delete' class='button delete'" .(!$row['deletable'] ? " disabled" : ""). "><i class='fa fa-trash'></i>Delete</button>";
		// 	}
		// 	echo "<a href='" .PAGE_URL. "' class='cancel'>Cancel</a>";
		// echo "</footer>";

		//Sticky footer
		include('includes/widgets/formbuttons.php');

        // echo '<div class="form-actions">
        //     <a href="'.PAGE_URL.'" class="button">Cancel</a>
        //     <button type="submit" name="save" value="1" class="button primary">Save Facility</button>
        // </div>';

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";

}

?>