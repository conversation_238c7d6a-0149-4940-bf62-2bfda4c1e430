<?php

//Dashboard widget
if(SECTION_ID == 4){
	$total_records = $db->get_record_count('partner_categories');
	$CMSBuilder->set_widget($_cmssections['partner_categories'], 'Total Partner Categories', $total_records);
}

if(SECTION_ID == $_cmssections['partner_categories']){

	//Define vars
	$record_db    = 'partner_categories';
	$record_id    = 'category_id';
	$record_name  = 'Category';
	$records_name = 'Categories';
	
	//Validation
	$errors   = false;
	$required = [];
	$required_fields = ['name'];

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = ["$record_db.name"];


	//Build search query
	if ($searchterm){
		foreach ($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get Records
	$db->query("SELECT * FROM partner_categories $where ORDER BY ordering", $params);
	$records_arr = $db->fetch_assoc($record_id);
	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}


	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];
		}
	}

	
	//Delete item
	if(isset($_POST['delete'])){

		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()){
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		
		header("Location: " .PAGE_URL);
		exit();


	//Save item
	}else if(isset($_POST['save'])){

		//Required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}
		
		if(!$errors){

			// Format validated data
			$pagename = clean_url($_POST['name']);
			$content  = trim(str_replace(['&nbsp;'], '', strip_tags($_POST['content']))) ? $_POST['content'] : NULL;

			//Insert to db
			$params = array(
				ITEM_ID, 
				$_POST['name'], 
				$pagename, 
				$content, 
				!isset($_POST['showhide']),
				$_POST['ordering'], 

				$_POST['name'], 
				$pagename, 
				$content, 
				!isset($_POST['showhide']),
				$_POST['ordering']
			);

			$db->query("INSERT INTO `$record_db`(`$record_id`, `name`, `page`, `content`, `showhide`, `ordering`) VALUES(?,?,?,?,?,?) ON DUPLICATE KEY UPDATE name = ?, page = ?, content = ?, showhide = ?, ordering = ?", $params);
			if(!$db->error()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();

			}else{
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);	
		}

		foreach($_POST AS $key => $value){
			$row[$key] = $value;
		}
	}
}

?>