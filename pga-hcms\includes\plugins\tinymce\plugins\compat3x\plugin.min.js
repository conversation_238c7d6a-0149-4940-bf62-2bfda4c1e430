!function(a){function b(){}function c(a){!f&&window&&window.console&&(f=!0,console.log("Deprecated TinyMCE API call: "+a))}function d(a,d,e,f){a=a||this;var g=[];return d?(this.add=function(b,h,i){function j(c){var g=[];if("string"==typeof e&&(e=e.split(" ")),e&&"function"!=typeof e)for(var i=0;i<e.length;i++)g.push(c[e[i]]);("function"!=typeof e||(g=e(d,c,a)))&&(e||(g=[c]),g.unshift(f||a),b.apply(h||f||a,g)===!1&&c.stopImmediatePropagation())}c("<target>.on"+d+".add(..)"),a.on(d,j,i);var k={original:b,patched:j};return g.push(k),j},this.addToTop=function(a,b){this.add(a,b,!0)},this.remove=function(b){return g.forEach(function(c,e){if(c.original===b)return g.splice(e,1),a.off(d,c.patched)}),a.off(d,b)},void(this.dispatch=function(){return a.fire(d),!0})):void(this.add=this.addToTop=this.remove=this.dispatch=b)}function e(e){function f(b){var c=e.settings.language||"en",d=[c,b].join("."),f=a.i18n.translate(d);return d!==f?f:a.i18n.translate(b)}function g(b,c){a.each(b.split(" "),function(a){e["on"+a]=new d(e,a,c)})}function h(a,b,c){return[b.level,c]}function i(a){return function(b,c){if(!c.selection&&!a||c.selection==a)return[c]}}function j(){function b(){return j()}var d={},e="add addMenu addSeparator collapse createMenu destroy displayColor expand focus getLength hasMenus hideMenu isActive isCollapsed isDisabled isRendered isSelected mark postRender remove removeAll renderHTML renderMenu renderNode renderTo select selectByIndex setActive setAriaProperty setColor setDisabled setSelected setState showMenu update";return c("editor.controlManager.*"),a.each(e.split(" "),function(a){d[a]=b}),d}if(!e.controlManager){e.controlManager={buttons:{},setDisabled:function(a,b){c("controlManager.setDisabled(..)"),this.buttons[a]&&this.buttons[a].disabled(b)},setActive:function(a,b){c("controlManager.setActive(..)"),this.buttons[a]&&this.buttons[a].active(b)},onAdd:new d,onPostRender:new d,add:function(a){return a},createButton:j,createColorSplitButton:j,createControl:j,createDropMenu:j,createListBox:j,createMenuButton:j,createSeparator:j,createSplitButton:j,createToolbar:j,createToolbarGroup:j,destroy:b,get:b,setControlType:j},g("PreInit BeforeRenderUI PostRender Load Init Remove Activate Deactivate","editor"),g("Click MouseUp MouseDown DblClick KeyDown KeyUp KeyPress ContextMenu Paste Submit Reset"),g("BeforeExecCommand ExecCommand","command ui value args"),g("PreProcess PostProcess LoadContent SaveContent Change"),g("BeforeSetContent BeforeGetContent SetContent GetContent",i(!1)),g("SetProgressState","state time"),g("VisualAid","element hasVisual"),g("Undo Redo",h),g("NodeChange",function(a,b){return[e.controlManager,b.element,e.selection.isCollapsed(),b]});var k=e.addButton;e.addButton=function(a,b){function c(){if(e.controlManager.buttons[a]=this,d)return d.apply(this,arguments)}var d;for(var g in b)"onpostrender"===g.toLowerCase()&&(d=b[g],b.onPostRender=c);return d||(b.onPostRender=c),b.title&&(b.title=f(b.title)),k.call(this,a,b)},e.on("init",function(){var a=e.undoManager,b=e.selection;a.onUndo=new d(e,"Undo",h,null,a),a.onRedo=new d(e,"Redo",h,null,a),a.onBeforeAdd=new d(e,"BeforeAddUndo",null,a),a.onAdd=new d(e,"AddUndo",null,a),b.onBeforeGetContent=new d(e,"BeforeGetContent",i(!0),b),b.onGetContent=new d(e,"GetContent",i(!0),b),b.onBeforeSetContent=new d(e,"BeforeSetContent",i(!0),b),b.onSetContent=new d(e,"SetContent",i(!0),b)}),e.on("BeforeRenderUI",function(){var b=e.windowManager;b.onOpen=new d,b.onClose=new d,b.createInstance=function(b,d,e,f,g,h){c("windowManager.createInstance(..)");var i=a.resolve(b);return new i(d,e,f,g,h)}})}}var f;a.util.Dispatcher=d,a.onBeforeUnload=new d(a,"BeforeUnload"),a.onAddEditor=new d(a,"AddEditor","editor"),a.onRemoveEditor=new d(a,"RemoveEditor","editor"),a.util.Cookie={get:b,getHash:b,remove:b,set:b,setHash:b},a.on("SetupEditor",e),a.PluginManager.add("compat3x",e),a.addI18n=function(b,c){var d=a.util.I18n,e=a.each;return"string"==typeof b&&b.indexOf(".")===-1?void d.add(b,c):void(a.is(b,"string")?e(c,function(a,c){d.data[b+"."+c]=a}):e(b,function(a,b){e(a,function(a,c){e(a,function(a,e){"common"===c?d.data[b+"."+e]=a:d.data[b+"."+c+"."+e]=a})})}))}}(tinymce);