@charset "utf-8";
/*
	definitions.less

*/
// @import url("https://use.typekit.net/trk8sml.css");
// @import url('https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@font-face: {
	font-family: "Lora", serif;
	font-weight: 400;
	font-style: normal;
}
@font-face: {
	font-family: "Lora", serif;
	font-weight: 400;
	font-style: italic;
}
@font-face: {
	font-family: "Lora", serif;
	font-weight: 700;
	font-style: normal;
}
@font-face: {
	font-family: "Lora", serif;
	font-weight: 700;
	font-style: italic;
}

@font-face: {
	font-family: "Poppins", sans-serif;
	font-weight: 400;
	font-style: normal;
}

@font-face: {
	font-family: "Contralto Big", sans-serif;
	font-weight: 400;
	font-style: normal;
}

// Fonts
@font-base: "Poppins", sans-serif;
@font-alt: "Lora", sans-serif;

// @font-color: #1B2D26;
@font-color:#020202;
@font-color-light: #999999;

@letter-spacing-thin: -50;
@letter-spacing: -20;

// Shades
@color-light: #FFFFFF; // Background, Banner/Slideshow Title, Parallax Title, CTA Title, Mini Promos, Footer Contents, Decorative Lines
@color-gray-lightest: #EEEEEE; // Inactive Tabs
@color-gray-lighter: #DDDDDD; // Parallax Text, HRs
@color-gray-light: #CCCCCC; // Header Contact Info, Form Fields
@color-gray: #999999; // Breadcrumbs, Small, Disclaimer
@color-gray-dark: #666666; // Contact Page Labels
@color-gray-darker: #333333; // Mobile Body Text, Panel Titles
@color-gray-darkest: #111111;
@color-dark: #000000; // Shadows
@color-yellow: #FFC302;

// Swatches
@color-dark-navy-blue: #16212F;
@color-dark-navy-blue-light: #1A2636;
@color-coral-red: #EF3E34;
@color-green-lighter: #B3C6BB;
@color-lime: #72AA32;

// Utilities
@color-theme1: @color-dark-navy-blue; // Panel Titles
@color-theme2: @color-coral-red; // Panel H1s, Panel Subtitles, Promo Box Titles, Button Texts
@color-theme3: @color-green-lighter; // Decorative Lines, Mini Promo Icons, Banner/Slideshow Description
@color-theme4: @color-lime; // Links, Buttons, Map Marker, Contact Page Info
@color-error: #EA7B88;
@color-success: #00c700;

@color-grad-in: @color-theme1;
@color-grad-out: @color-theme2;

// Overlays
@color-overlay1: @color-theme1;
@color-overlay2: @color-theme2;
@color-overlay-grad-in: fade(@color-overlay1, 75%);
@color-overlay-grad-out: fade(@color-overlay2, 75%);
@color-overlay-dark: @color-dark;

// CSS Vars
:root{

	// --text-wrap: 100%; // Aligned text wrapping (optional)

	// Centered content containers
	--container-width: 930px;
	--container-width-lg: 1240px;
	--container-width-xl: 1560px;

	// Calculated container margins
	--container-margin: ~"max"(0px, (100% - var(--container-width) - var(--container-padding)*2) / 2);
	--container-margin-lg: ~"max"(0px, (100% - var(--container-width-lg) - var(--container-padding)*2) / 2);
	--container-margin-xl: ~"max"(0px, (100% - var(--container-width-xl) - var(--container-padding)*2) / 2);

	.fluid-property(--container-padding; 20px; 30px; @max-width: 1024px);


	// Typography
	.fluid-property(--font-h1; 48px; 61px;);
	.fluid-property(--font-h2; 40px; 49px;);
	.fluid-property(--font-h3; 33px; 39px;);
	.fluid-property(--font-h4; 28px; 31px;);
	.fluid-property(--font-h5; 23px; 25px;);
	.fluid-property(--font-h6; 19px; 20px;);

	.fluid-property(--font-blockquote; 22px; 26px;);
	.fluid-property(--font-paragraph; 16px; 16px;);
	.fluid-property(--font-caption; 13px; 13px;);

	// --font-paragraph: 15px;
	// --font-caption: 13px;
	--font-footnote: 11px;

	// @media @tablet-l{--font-paragraph: 16px;}

	--line-height-thin: 1.2;
	--line-height-normal: 1.4;
	--line-height-thick: 1.7;
}