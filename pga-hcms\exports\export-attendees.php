<?php  

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../includes/utils.php");

if(isset($_POST) && USER_LOGGED_IN){

	//Define vars
	$record_db = 'reg_attendees';
	$record_id = 'attendee_id';
	$record_name = 'Attendee';
	$records_arr = array();

	$db_columns = array(); //for SELECT in query
	$table_columns = array(); //for listing label
	$alias_columns = array(); //for listing value
	$rearrange_columns = false;

	$wheretxt = "";
	$querytxt = "";
	
	$title = '';
	$date_range = '';

	//Export with selected columns/filters
	if(isset($_GET['advanced_search'])) {
		
		$params = array(', '); //for concat

		//Set default columns to get
		$db_columns = array(
			$record_db.'.first_name',
			$record_db.'.last_name'
		);
		$table_columns = array(
			'Full Name'
		);
		$alias_columns = array(
			'full_name'
		);
		
		$ccount = count($db_columns);
		
		//Columns
		if(isset($_GET['column']) && !empty($_GET['column'])) {
			foreach($_GET['column'] as $db_name => $columns) {
				foreach($columns as $column => $value) {
					if($value != '') {
						if($column == 'name'){
							array_splice($db_columns, 0, 0, $db_name.'.'.$column);
							array_splice($table_columns, 0, 0, $value);
							array_splice($alias_columns, 0, 0, $column);
						}else if($column == 'occurrence_id'){
							$offset = ($alias_columns[0] == 'name' ? 1 : 0);
							array_splice($db_columns, $offset, 0, $db_name.'.'.$column);
							array_splice($table_columns, $offset, 0, $value);
							array_splice($alias_columns, $offset, 0, $column);
						}else{
							if($column != 'facility'){
								$db_columns[$ccount] = $db_name.'.'.$column;
							}
							$table_columns[$ccount] = $value;
							$alias_columns[$ccount] = $column;
						}
						
					}
					$ccount++;
				}
			}
		}

		//Search/filters
		if(isset($_GET['filter']) && !empty($_GET['filter'])) {
			foreach($_GET['filter'] as $db_name => $filters) {
				foreach($filters as $filter => $value) {
					if(trim($value) != '') {
						$wheretxt .= ($wheretxt != "" ? " AND " : " WHERE ")."$db_name.$filter";

						if($filter == 'event_id' || $filter == 'occurrence_id' || $filter == 'reg_status' || $filter == 'gender') {
							$wheretxt .= " = ?";
							$params[] = $value;
						} else if($filter == 'start_date') {
							$wheretxt .= " >= ?";
							$params[] = $value;
						} else if($filter == 'end_date') {
							$wheretxt .= " <= ?";
							$params[] = $value;
						} else {
							$wheretxt .= " LIKE ?";
							$params[] = '%'.$value.'%';
						}
					}
				}
			}
		}

		//Create query
		$querytxt .= "SELECT $record_db.*, IFNULL(`$record_db`.`facility`, `facilities`.`facility_name`) AS `facility`, ";
		$querytxt .= "reg_events.event_type, reg_events.team_event, reg_occurrences.start_date, reg_occurrences.end_date, CONCAT(reg_attendees.last_name,?,reg_attendees.first_name) AS full_name";
		$querytxt .= (!empty($db_columns) ? ",".implode(", ", $db_columns)." " : " ");
		$querytxt .= "FROM $record_db ";
		$querytxt .= "LEFT JOIN reg_events ON reg_events.event_id = $record_db.event_id ";
		$querytxt .= "LEFT JOIN reg_occurrences ON reg_occurrences.occurrence_id = $record_db.occurrence_id ";
		$querytxt .= "LEFT JOIN reg_registrations ON reg_registrations.registration_id = $record_db.registration_id ";
		$querytxt .= "LEFT JOIN accounts ON accounts.account_id = $record_db.account_id ";
		$querytxt .= "LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` ";
		$querytxt .= "LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ";
		$querytxt .= $wheretxt;
		$querytxt .= " GROUP BY $record_db.$record_id ORDER BY reg_occurrences.occurrence_id, reg_attendees.registration_id, reg_attendees.partner_id, reg_attendees.last_name, reg_attendees.first_name";
		$query = $db->query($querytxt, $params);
		if($query && !$db->error() && $db->num_rows() > 0) {
			$result = $db->fetch_array();
			foreach($result as $row){
				$records_arr[$row[$record_id]] = $row;
			}
		}
		
	}

	//Compile records
	$csv_rows = array();
	$count = 0;
	$number = NULL;
	$occurrence_id = NULL;
	foreach($records_arr as $row) {
		
		if($row['occurrence_id'] != $occurrence_id){
			$occurrence_id = $row['occurrence_id'];
			$count = 1;
			$number = $count;
		}else{
			if($row['event_type'] == 2 && $row['team_event']){
				if(empty($row['partner_id'])){
					$count++;
					$number = $count;
				}else{
					$number = NULL;
				}
			}else{
				$count++;
				$number = $count;
			}
		}
		
		if($row['reg_status'] == 'Registered'){
			$data = array($number);
		}else{
			$data = array('');
		}
		
		foreach($alias_columns as $key => $column) {
			if($column == 'date_withdrawn' || $column == 'registration_date') {
				$data[] = ($row[$column] != '' && $row[$column] != '0000-00-00' && $row[$column] != '0000-00-00 00:00:00' ? date('Y-m-d', strtotime($row[$column])) : "");
			} else if($column == 'attendee_sharing') {
				$attendee_sharing = ($row[$column] == -1 ? $reg_settings['attendee_sharing'] : $row[$column]);
				$data[] = ($attendee_sharing ? "Enabled" : "Disabled");
			} else if($column == 'occurrence_id') {
				$data[] = format_date_range($row['start_date'], $row['end_date']);
			} else if($column == 'ticket_price' || $column == 'discount') {
				$data[] = '$'.number_format($row[$column], 2);
			} else if($column == 'paid') {
				$data[] = ($row['paid'] == '1' ? 'Yes' : 'No');
			} else if($column == 'handicap') {
				$data[] = ($row['handicap'] != '' ? $row['handicap'] : '0');
			} else {
				$data[] = $row[$column];
			}
		}
		$csv_rows[] = $data;
	}

	array_unshift($table_columns, 'No.'); //add number column
		
	//Output csv
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=attendees-".date("Ymd").".csv");
	header("Content-Transfer-Encoding: binary ");

	$fp = fopen('php://output', 'w');
	fputcsv($fp, str_replace("&rsquo;", "'", $table_columns));
	foreach($csv_rows as $row) {
		fputcsv($fp, str_replace("&rsquo;", "'", $row));
	}
	fclose($fp);
	


}
?>