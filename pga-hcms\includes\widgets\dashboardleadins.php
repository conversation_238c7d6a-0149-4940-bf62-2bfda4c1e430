<?php if($CMSBuilder->get_section_status($_cmssections['leadins']) == 'Enabled'){ ?>

	<?php 

	//Get leadins with most conversions
	$leadins = array();
	$db->query("SELECT `pages_leadins`.`leadin_id`, `pages_leadins`.`title`, `pages_leadins`.`url_target`, 
		CASE WHEN 
			`pages_leadins`.`url_target` > 2 THEN `leadin_submissions`.`total` 
		ELSE SUM(`leadin_events`.`event` = ?) 
	END AS `conversions` 
	FROM `pages_leadins` 
		LEFT JOIN (SELECT COUNT(*) AS `total`, `leadin_id` FROM `leadin_submissions` GROUP BY `leadin_id`) AS `leadin_submissions` ON `leadin_submissions`.`leadin_id` = `pages_leadins`.`leadin_id` 
		LEFT JOIN `leadin_events` ON `leadin_events`.`leadin_id` = `pages_leadins`.`leadin_id` 
	GROUP BY `pages_leadins`.`leadin_id` 
	ORDER BY `conversions` DESC 
	LIMIT 10", array('link-click'));
	$result = $db->fetch_assoc('leadin_id');
	foreach($result as $leadin){
		if($leadin['conversions']){
			$leadins[] = $leadin;
		}
	}
	?>

	<?php if(!empty($leadins)){ ?>
		<div class="cms-overview panel flex-column">
			<div class="panel-header">Attention Boxes <?php echo $CMSBuilder->tooltip('Attention Boxes', 'The attention boxes below have the highest conversions (clicks/form submissions).'); ?></div>
			<div class="panel-content nopadding">
		        <table cellpadding="0" cellspacing="0" border="0">
		        <?php
				$submissions_page = $CMSBuilder->get_section($_cmssections['leadin_stats']);
		        foreach($leadins as $leadin){
		        	$leadin['page_url'] = $submissions_page['page_url']."?page_id=".$leadin['leadin_id'];
		        	echo '<tr>
						<td><a href="' .$leadin['page_url']. '"><i class="fas fa-edit color-theme1"></i> &nbsp; ' .$leadin['title']. '</a></td>
						<td align="right">' .($leadin['conversions'] ?: ''). '</td>
		        	</tr>';
		        }
		        ?>
		        </table>
		    </div>
		</div>
	<?php } ?>

<?php } ?>