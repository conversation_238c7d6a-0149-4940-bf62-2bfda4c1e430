<?php
// js/ajax/delete-facility-image.php - <PERSON>les deletion of facility logo OR banner

//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

// global $path;

// --- Environment Setup ---
// Include necessary files relative to *this* file's location
// $base_path = realpath(dirname(__FILE__) . '/../../') . '/'; // Adjust if structure differs
// require_once($base_path . 'config/config.php');
// require_once($base_path . 'config/database.php');
// Include Account class if needed for login checks/permissions
// require_once($path . 'core/classes/Account.class.php');
// Include ImageUpload class IF the delete helper needs it (unlikely)
// require_once($base_path . 'modules/classes/ImageUpload.class.php');
// Include the *NEW* FacilityImageHandler or use direct logic here
// require_once($base_path . 'modules/account/FacilityImageHandler.php'); // If you create one

// --- START SESSION --- (Crucial for login checks)
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Make globals available IF needed by included functions/classes
global $path, $db, $Account; // $path might be needed for delete paths

// --- Response Array ---
$response = ['errors' => false, 'message' => ''];
header('Content-Type: application/json'); // Set header early

// --- Instantiate Account Object (if not already global/available) ---
// This depends heavily on your framework's bootstrap process
// if (!isset($Account) || !is_object($Account)) {
//      $current_account_id = $_SESSION['auth']['account_id'] ?? null; // Example session check
//      if ($current_account_id) {
//          try { $Account = new Account(null, $current_account_id); } catch (Exception $e) { $Account = null; }
//      } else { $Account = null; }
// }

// // --- Login Check ---
if (!isset($Account) || !$Account->login_status()) {
    $response['errors'] = true;
    $response['message'] = 'Access denied. Session expired or not logged in.';
    error_log("Delete Facility Image AJAX: Access Denied (Not Logged In).");
    echo json_encode($response);
    exit;
}

// --- Request Method Check ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['errors'] = true;
    $response['message'] = 'Invalid request method.';
    error_log("Delete Facility Image AJAX: Invalid Method - " . $_SERVER['REQUEST_METHOD']);
    echo json_encode($response);
    exit;
}

// --- XSSID/CSRF Check (Implement if needed) ---
/*
if (!isset($_POST['xssid']) || !isset($_COOKIE['xssid']) || $_POST['xssid'] !== $_COOKIE['xssid']) {
    $response['errors'] = true;
    $response['message'] = 'Invalid security token.';
    error_log("Delete Facility Image AJAX: Invalid XSSID Token.");
    echo json_encode($response);
    exit;
}
*/

// --- Get Data from POST ---
$filename = trim($_POST['filename'] ?? '');
$image_type = trim($_POST['imagetype'] ?? ''); // 'logo' or 'banner'
$facility_id = (int)($_POST['facilityid'] ?? 0);
$account_id = $Account->account_id; // Logged-in user

// echo "Delete Facility Image AJAX: Received request - Filename:[$filename], Type:[$image_type], FacilityID:[$facility_id], AccountID:[$account_id]";

// echo json_encode($_POST, true);
// echo $account_id . " - ";
// echo json_encode($Account, true);
// $Account 

// exit;


error_log("Delete Facility Image AJAX: Received request - Filename:[$filename], Type:[$image_type], FacilityID:[$facility_id], AccountID:[$account_id]");

// --- Validation ---
if (empty($filename) || empty($image_type) || $facility_id <= 0) {
    $response['errors'] = true;
    $response['message'] = 'Missing required data for deletion.';
    error_log("Delete Facility Image AJAX: Missing data - " . print_r($_POST, true));
    echo json_encode($response);
    exit;
}
if ($image_type !== 'logo' && $image_type !== 'banner') {
     $response['errors'] = true;
     $response['message'] = 'Invalid image type specified.';
     error_log("Delete Facility Image AJAX: Invalid image type - " . $image_type);
     echo json_encode($response);
     exit;
}

// --- Permission Check (Crucial!) ---
// Verify if the logged-in user ($account_id) is allowed to edit this specific facility ($facility_id)
$can_edit_this_facility = false;
if (!empty($Account->facility_id) && $Account->facility_id == $facility_id) { // Check if it's their OWN facility
    // Add your role/class based check here if needed
    // e.g., fetch facility_access flag or check membership_classes as done on the display page
    $can_edit_this_facility = true; // Simplified check - IMPLEMENT YOUR FULL CHECK
}
if (!$can_edit_this_facility) {
     $response['errors'] = true;
     $response['message'] = 'Permission denied to modify this facility.';
     error_log("Delete Facility Image AJAX: Permission denied for AccountID: $account_id to edit FacilityID: $facility_id.");
     echo json_encode($response);
     exit;
}
// --- End Permission Check ---


// --- Determine Paths and DB Column ---
$db_column = '';
$relative_dir = '';
$delete_paths = []; // Store all paths to delete (e.g., multiple banner sizes)

if ($image_type === 'logo') {
    $db_column = 'logo';
    $relative_dir = 'images/facility/logos/';
    $abs_path = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $path . $relative_dir . $filename;
    $delete_paths[] = str_replace('/', DIRECTORY_SEPARATOR, $abs_path);
} elseif ($image_type === 'banner') {
    $db_column = 'image'; // Banner uses 'image' column in facilities table
    $relative_dir_base = 'images/facility/banners/';
    // If you stored multiple banner sizes, you need to delete all of them
    $cropsizes_dirs = ['1920/', '1366/', '1024/', '768/', '480/']; // Match the dirs used in processing
    foreach ($cropsizes_dirs as $dir_suffix) {
         $abs_path = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $path . $relative_dir_base . $dir_suffix . $filename;
         $delete_paths[] = str_replace('/', DIRECTORY_SEPARATOR, $abs_path);
    }
    // If you only store one banner:
    // $relative_dir = 'images/facility/banners/';
    // $abs_path = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $path . $relative_dir . $filename;
    // $delete_paths[] = str_replace('/', DIRECTORY_SEPARATOR, $abs_path);
}

// --- Delete Physical Files ---
$files_deleted_successfully = true; // Assume success unless unlink fails
error_log("Delete Facility Image AJAX: Attempting to delete physical files for $filename:");
foreach ($delete_paths as $path_to_delete) {
    error_log(" - Checking: " . $path_to_delete);
    if (file_exists($path_to_delete)) {
        if (!@unlink($path_to_delete)) {
            error_log("Delete Facility Image AJAX: FAILED to unlink: " . $path_to_delete . " (Check permissions!)");
            $files_deleted_successfully = false; // Mark as failed but continue trying to delete others
            // Optionally set a specific error message here
            // $response['message'] = 'Server error: Could not delete all image files.';
        } else {
            error_log("Delete Facility Image AJAX: Successfully unlinked: " . $path_to_delete);
        }
    } else {
         error_log("Delete Facility Image AJAX: File not found (already deleted?): " . $path_to_delete);
    }
}
// Decide if file deletion failure should stop the DB update
// For consistency, we'll usually try to update the DB even if file deletion fails
// if (!$files_deleted_successfully) {
//     $response['errors'] = true;
//     // $response['message'] already set maybe
//     echo json_encode($response);
//     exit;
// }

// --- Update Database ---
if (isset($db) && is_object($db) && !empty($db_column)) {
    try {
        $sql = "UPDATE `facilities` SET `" . $db_column . "` = ? WHERE `facility_id` = ?";
        $params = [NULL, $facility_id]; // Set the column to NULL

        error_log("Delete Facility Image AJAX: Attempting DB update. SQL: $sql PARAMS: " . print_r($params, true));
        $query = $db->query($sql, $params);

        if ($query && !$db->error()) {
            error_log("Delete Facility Image AJAX: Database update successful.");
            $response['message'] = ucfirst($image_type) . ' deleted successfully.';
            // No content needed as JS handles UI
        } else {
             throw new Exception("Database update query failed: " . ($db->error() ?? 'Unknown DB error'));
        }
    } catch (Exception $e) {
        $response['errors'] = true;
        $response['message'] = 'Database update failed: ' . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
        error_log("Delete Facility Image AJAX: Database update exception: " . $e->getMessage());
        // State is now inconsistent: files might be deleted, but DB still has reference.
    }
} else {
    $response['errors'] = true;
    $response['message'] = 'Database connection error or invalid image type.';
    error_log("Delete Facility Image AJAX: DB object missing or invalid image type for DB update.");
}

// --- Return JSON Response ---
error_log("Delete Facility Image AJAX: Sending final response: " . json_encode($response));
echo json_encode($response);
exit; // Explicitly exit

?>