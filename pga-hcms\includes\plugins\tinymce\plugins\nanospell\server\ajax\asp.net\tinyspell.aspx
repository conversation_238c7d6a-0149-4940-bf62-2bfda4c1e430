﻿<%@ Page Language="C#" AutoEventWireup="true"  MaintainScrollPositionOnPostback="false" StylesheetTheme="" Theme="" EnableTheming="false"  ValidateRequest="false"  %><%@ OutputCache Location="None" VaryByParam="None" %><script type="text/C#" runat="server">
  protected void Page_Init(object sndr, EventArgs e)
    {
       Response.Write(TinyMCESpell.RPC.HandlePostRequest(Context));   
       Response.Flush();
       Response.End();
    }                                                                                                
</script><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="NotRendered1" runat="server">
    <title>Themes Compatibility</title>
</head>
<body><form runat="server" id="NotRendered2"></form></body>
</html>