<?php
// pages/account/profile.php - Displays the Edit Profile form

// --- Environment Setup ---
// Ensure session is started BEFORE any output
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

global $path, $db, $Account, $_sitepages, $provinces, $states; 

// --- Data Fetching & Login Check ---
// This block runs on EVERY load of this page (GET or after POST redirect)

$Account = null; // Initialize $Account to null
$current_account_id = null;
$login_check_ok = false;

// Determine logged-in user ID (Adjust based on your auth mechanism)
if (defined('USER_LOGGED_IN') && USER_LOGGED_IN) {
    $current_account_id = USER_LOGGED_IN;
    $login_check_ok = true;
} elseif (isset($_SESSION['auth']['account_id'])) { // Example fallback check
     $current_account_id = $_SESSION['auth']['account_id'];
     $login_check_ok = true;
} // Add other checks if necessary

if (!$login_check_ok) {
    // Not logged in - Redirect or display error message
    echo "<p style='color:red; font-weight:bold;'>Access Denied. Please log in.</p>";
    exit;
}

// --- Instantiate or Reload the Account Object ---
try {
    $Account = new Account(null, $current_account_id); // Assumes constructor loads profile
} catch (Exception $e) {
    error_log("Error loading Account object for profile page: " . $e->getMessage());
    echo "<p style='color:red;'>Error loading account data. Please try again later.</p>";
    exit;
}
// --- End Account Object Load ---

// --- Retrieve session data for feedback/repopulation ---
$success_message = $_SESSION['profile_success_message'] ?? null;
$error_message = $_SESSION['profile_error_message'] ?? null;
$submitted_data = $_SESSION['profile_form_data'] ?? null; // Data submitted on last attempt (if error)

unset($_SESSION['profile_success_message']);
unset($_SESSION['profile_error_message']);
unset($_SESSION['profile_form_data']);

// --- Helper function definitions ---
// (Ideally move to a shared include file)
if (!function_exists('echo_selected_with_post')) {
    function echo_selected_with_post($submitted_value, $current_value, $option_value) {
        $value_to_check = $current_value;
        if ($submitted_value !== null) {
            $value_to_check = $submitted_value;
        }
        if ((string)$value_to_check == (string)$option_value) {
            echo ' selected';
        }
    }
}

if (!function_exists('get_form_value')) {
    function get_form_value($field_name, $submitted_data_array, $account_object, $is_textarea = false) {
        $value = null;
        if (isset($submitted_data_array[$field_name])) {
            $value = $submitted_data_array[$field_name];
        } elseif (isset($account_object->$field_name)) {
             $value = $account_object->$field_name;
        }
        return $is_textarea ? ($value ?? '') : htmlspecialchars($value ?? '', ENT_QUOTES, 'UTF-8');
    }
}
// Helper function specifically for array inputs like q_answer
if (!function_exists('get_form_array_value')) {
    function get_form_array_value($array_name, $key, $submitted_data_array, $current_data_array) {
         $value = null;
         // Check submitted data first (e.g., $_POST['q_answer'][1])
         if (isset($submitted_data_array[$array_name][$key])) {
              $value = $submitted_data_array[$array_name][$key];
         }
         // Fallback to current data (e.g., $profile_answers[1])
         elseif (isset($current_data_array[$key])) {
              $value = $current_data_array[$key];
         }
         return htmlspecialchars($value ?? '', ENT_QUOTES, 'UTF-8');
    }
}


// --- Prepare specific values for the form ---
// Format dates
$member_since_formatted = '';
$member_since_source = $submitted_data['pga_member_since'] ?? $Account->pga_member_since ?? '';
if (!empty($member_since_source) && $member_since_source != '0000-00-00') {
    $member_since_timestamp = strtotime($member_since_source);
    if ($member_since_timestamp) {
        $member_since_formatted = date('Y-m-d', $member_since_timestamp);
    }
}

// Image paths
$imagedir = 'images/users/thumbs/'; // Display thumbnail
$imagepath = (isset($path) ? $path : '/') . $imagedir; // $path should be global

// Assume $provinces and $states arrays are available globally or included
global $provinces, $states, $db; // Ensure $db is available
// Define $countries if not global
$countries = ['CA' => 'Canada', 'US' => 'United States']; // Add more if needed

// global $path;
// $facility_data_obj = ... (object containing facility data)
$logo_display_path = $path.'/images/facility/logos/';
$banner_display_path = $path.'/images/facility/banners/';

// $facility_id = $facility_data_obj->facility_id ?? null; // Get the facility ID

// --- Define URLs ---
// Processing script URL (This form posts to itself, processing handled by included module)
$processing_script_url = $path.'account/edit-facility/'; // Action is empty, handled by index.php including modules/account/profile.php
// URL for the main account page
$account_page_url = (isset($path) ? $path : '/') . 'account/'; // Adjust if needed


// --- Fetch Profile Questions ---
$profile_questions = [];
if (isset($db) && is_object($db)) {
    $query = $db->query("SELECT `question_id`, `question` FROM `account_profile_questions` WHERE `showhide` = 0 ORDER BY `ordering` ASC"); // Ensure ASC order
    if ($query && !$db->error()) {
        // Use fetch_array assuming it gets all rows as numerically indexed array of assoc arrays
        $question_rows = $db->fetch_array($query);
        if (is_array($question_rows)) {
            foreach ($question_rows as $row) {
                $profile_questions[$row['question_id']] = $row['question'];
            }
        }
    } else {
        error_log("Error fetching profile questions: " . ($db->error() ?? 'Query failed'));
    }
} else {
    error_log("Database object not available to fetch profile questions.");
}
// --- End Fetch Profile Questions ---


// --- Fetch Existing Answers ---
// $profile_answers = [];
// if (isset($db) && is_object($db) && isset($Account->account_id)) {
//      $query_answers = $db->query("SELECT `question_id`, `answer` FROM `account_profile_answers` WHERE `account_id` = ?", [$Account->account_id]);
//      if ($query_answers && !$db->error()) {
//           // Use fetch_array and loop through results
//           $answer_rows = $db->fetch_array($query_answers);
//           if (is_array($answer_rows)) {
//               foreach ($answer_rows as $row) {
//                   $profile_answers[$row['question_id']] = $row['answer'];
//               }
//           }
//      } else {
//           error_log("Error fetching profile answers: " . ($db->error() ?? 'Query failed'));
//      }
// }
// --- End Fetch Existing Answers ---



// --- FACILITY ACCESS CHECK ---
$can_edit_facility = false;
if (isset($Account->facility_id) && !empty($Account->facility_id) && isset($db) && is_object($db)) {
// if (isset($db) && is_object($db)) {

    $facility_classes_with_access = [];
    // Fetch class_ids that have facility_access (similar to your old module)
    $query_fc = $db->query("SELECT `class_id` FROM `membership_classes` WHERE `facility_access` = 1");

    $query = $db->query("SELECT `question_id`, `question` FROM `account_profile_questions` WHERE `showhide` = 0 ORDER BY `ordering` ASC"); // Ensure ASC order
    if ($query_fc && !$db->error()) {
        $fc_rows = $db->fetch_array($query_fc); // Assumes fetch_array gets all rows
        if ($fc_rows) {
            foreach ($fc_rows as $fc_row) {
                $facility_classes_with_access[] = $fc_row['class_id'];
            }
        }
    } else {
        error_log("Facility Access Check: DB error fetching facility classes: " . ($db->error() ?? 'Query failed'));
    }

    // Check if current user's class has access OR if direct facility_access flag is set
    if (($Account->facility_access ?? 0) == 1 || in_array($Account->class_id ?? null, $facility_classes_with_access)) {
        $can_edit_facility = true;
    }
}

if (!$can_edit_facility) {
    echo "<p style='color:red; font-weight:bold;'>You do not have permission to edit facility details. This might be because no facility is assigned to your account, or your account type does not grant facility editing privileges.</p>";
    // Optionally offer a link to contact support or go back to account dashboard
    echo '<p><a href="' . htmlspecialchars((isset($path) ? $path : '/') . 'account/', ENT_QUOTES, 'UTF-8') . '">‹ Back to My Account</a></p>';
    exit;
}
// --- End FACILITY ACCESS CHECK ---


// --- Fetch Current Facility Data for the Logged-in User ---
$facility_data_obj = null; // Will store facility data as an object for get_form_value
if (isset($Account->facility_id) && !empty($Account->facility_id) && isset($db) && is_object($db)) {
    $query_facility = $db->query("SELECT * FROM `facilities` WHERE `facility_id` = ?", [$Account->facility_id]);
    if ($query_facility && !$db->error() && $db->num_rows() > 0) {
        $facility_data_rows = $db->fetch_array($query_facility);
        $facility_data_obj = (object)$facility_data_rows[0]; // Cast to object
    } else {
        error_log("Error fetching facility data for facility_id " . $Account->facility_id . ": " . ($db->error() ?? 'Facility not found or query failed'));
    }
}

if (!$facility_data_obj) {
    echo "<p style='color:red;'>Could not load data for your assigned facility (ID: " . htmlspecialchars($Account->facility_id ?? 'N/A', ENT_QUOTES, 'UTF-8') . "). Please contact support.</p>";
    exit;
}
// --- End Fetch Facility Data ---

// --- Display Messages ---
if (!empty($success_message)) {
    echo '<div class="alert alert-success" style="background-color: #dff0d8; border: 1px solid #d6e9c6; color: #3c763d; padding: 15px; margin-bottom: 20px;">' . htmlspecialchars($success_message, ENT_QUOTES, 'UTF-8') . '</div>';
}
if (!empty($error_message)) {
    echo '<div class="alert alert-danger" style="background-color: #f2dede; border: 1px solid #ebccd1; color: #a94442; padding: 15px; margin-bottom: 20px;">' . $error_message . '</div>'; // Error message might contain HTML (<br>), don't escape fully
}



// --- Prepare specific values for the form ---

// Combine province and state arrays for easier lookup (Code => Name)
// Assumes $provinces and $states are already available globally and have the format [CODE => Name]
$all_regions = ($provinces ?? []) + ($states ?? []); // Simple merge using +, adjust if structure differs

// Find the CODE corresponding to the stored full province name
$current_province_code = null; // Initialize
$current_province_name = $facility_data_obj->province ?? null;
if ($current_province_name !== null) {
    // array_search finds the key (code) for a given value (name) in the combined array
    $found_code = array_search($current_province_name, $all_regions);
    if ($found_code !== false) {
        $current_province_code = $found_code;
    } else {
        error_log("Could not find province/state code for stored name: " . $current_province_name);
        // Fallback: maybe the code itself was stored? Check if the name exists as a code.
        if(isset($all_regions[$current_province_name])) {
            $current_province_code = $current_province_name;
        }
    }
}
// $current_province_code now holds 'AB' or 'NY' or null if not found

	//Filters
	$facility_types = $db->get_enum_vals('facilities', 'type');
	$facility_regions = $db->get_enum_vals('facilities', 'region');

    // echo "<pre>";
    // print_r($facility_types);
    // print_r($facility_regions);
    // print_r($facility_data_obj);

// --- Display Form ---
?>


<h3><?php echo htmlspecialchars(get_form_value('facility_name', $submitted_data, $facility_data_obj), ENT_QUOTES, 'UTF-8'); ?></h3> <?php // Changed from "Edit Profile" ?>

<form name="facility-form" id="facility-form" method="post" action="<?php echo htmlspecialchars($processing_script_url, ENT_QUOTES, 'UTF-8'); ?>" enctype="multipart/form-data">

    <div class="form-grid">
        <div class="form-field">
            <label for="type">Facility Type</label>
            <select name="type" id="type" class="select">
                <option value="">- Select Type -</option>
                <?php if (!empty($facility_types)): foreach ($facility_types as $ftype): ?>
                    <option value="<?php echo htmlspecialchars($ftype, ENT_QUOTES, 'UTF-8'); ?>" <?php echo_selected_with_post($submitted_data['type'] ?? null, $facility_data_obj->type ?? '', $ftype); ?>>
                        <?php echo htmlspecialchars($ftype, ENT_QUOTES, 'UTF-8'); ?>
                    </option>
                <?php endforeach; endif; ?>
            </select>
        </div>
        <div class="form-field">
            <label for="content">Facility Description</label>
            <textarea name="content" id="content" class="textarea" rows="5"><?php echo get_form_value('content', $submitted_data, $facility_data_obj, true); ?></textarea>
        </div>
    </div>

    <h4 style="margin-top: 30px;">Facility Address</h4>
    <div class="form-grid">
        <div class="form-field"><label for="address1">Street Address</label><input type="text" name="address1" id="address1" class="input" value="<?php echo get_form_value('address1', $submitted_data, $facility_data_obj); ?>" /></div>
        <!-- <div class="form-field"><label for="province">Province/State</label>
        <select name="province" id="province" class="select">
            <option value="">- Select -</option>
            <?php if(isset($provinces)&&is_array($provinces)):?>
                <optgroup label="Canada">
                    <?php foreach($provinces as $code=>$name){ echo "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['province']??null,$facility_data_obj->province??'',$code);echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";}?></optgroup><?php endif;?><?php if(isset($states)&&is_array($states)):?><optgroup label="United States"><?php foreach($states as $code=>$name){ echo "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['province']??null,$facility_data_obj->province??'',$code);echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";}?>
                </optgroup>
            <?php endif;?>
        </select>
        </div> -->

        <div class="form-field">
    <label for="province">Province/State</label>
    <select name="province" id="province" class="select">
        <option value="">- Select -</option>
        <?php if (!empty($provinces) && is_array($provinces)): ?>
            <optgroup label="Canada">
                <?php
                foreach ($provinces as $code => $name) {
                    echo "<option value='" . htmlspecialchars($code, ENT_QUOTES, 'UTF-8') . "'";
                    // Use $current_province_code for comparison
                    echo_selected_with_post($submitted_data['province'] ?? null, $current_province_code, $code);
                    echo ">" . htmlspecialchars($name, ENT_QUOTES, 'UTF-8') . "</option>";
                }
                ?>
            </optgroup>
        <?php endif; ?>
        <?php if (!empty($states) && is_array($states)): ?>
            <optgroup label="United States">
                <?php
                foreach ($states as $code => $name) {
                    echo "<option value='" . htmlspecialchars($code, ENT_QUOTES, 'UTF-8') . "'";
                    // Use $current_province_code for comparison
                    echo_selected_with_post($submitted_data['province'] ?? null, $current_province_code, $code);
                    echo ">" . htmlspecialchars($name, ENT_QUOTES, 'UTF-8') . "</option>";
                }
                ?>
            </optgroup>
        <?php endif; ?>
    </select>
</div>


        <div class="form-field"><label for="address2">Unit No.</label><input type="text" name="address2" id="address2" class="input" value="<?php echo get_form_value('address2', $submitted_data, $facility_data_obj); ?>" /></div>
        <div class="form-field"><label for="postal_code">Postal/Zip Code</label><input type="text" name="postal_code" id="postal_code" class="input" value="<?php echo get_form_value('postal_code', $submitted_data, $facility_data_obj); ?>" /></div>
        <div class="form-field"><label for="city">City/Town</label><input type="text" name="city" id="city" class="input" value="<?php echo get_form_value('city', $submitted_data, $facility_data_obj); ?>" /></div>
        <div class="form-field"><label for="region">Region</label> <select name="region" id="region" class="select"> <option value="">- Select Region -</option> <?php if (!empty($facility_regions)): foreach ($facility_regions as $fregion):?> <option value="<?php echo htmlspecialchars($fregion,ENT_QUOTES,'UTF-8');?>" <?php echo_selected_with_post($submitted_data['region']??null,$facility_data_obj->region??'',$fregion);?>><?php echo htmlspecialchars($fregion,ENT_QUOTES,'UTF-8');?></option> <?php endforeach; endif;?></select></div>
        <div class="form-field"><label for="country">Country</label><select name="country" id="country" class="select"><option value="">- Select -</option><?php foreach($countries as $code=>$name){ echo "<option value='".htmlspecialchars($code,ENT_QUOTES,'UTF-8')."'";echo_selected_with_post($submitted_data['country']??null,$facility_data_obj->country??'',$code);echo ">".htmlspecialchars($name,ENT_QUOTES,'UTF-8')."</option>";}?></select></div>
    </div>

    <h4 style="margin-top: 30px;">Contact Details</h4>
    <div class="form-grid">
        <div class="form-field"><label for="email">Email Address</label><input type="email" name="email" id="email" class="input" value="<?php echo get_form_value('email', $submitted_data, $facility_data_obj); ?>" /></div>
        <div class="form-field"><label for="website">Website</label><input type="url" name="website" id="website" class="input" placeholder="https://" value="<?php echo get_form_value('website', $submitted_data, $facility_data_obj); ?>" /></div>
        <div class="form-field"><label for="phone">Phone Number</label><input type="tel" name="phone" id="phone" class="input" value="<?php echo get_form_value('phone', $submitted_data, $facility_data_obj); ?>" /></div>
    </div>

    <!-- <div class="form-grid" style="margin-top: 30px; align-items: flex-start;">
        <div> <?php // Left Column: Facility Logo ?>
            <h4>Facility Logo</h4>
            <div class="form-field photo-upload-area">
                <?php $current_logo = $facility_data_obj->logo ?? null; $logo_src = null; if (!empty($current_logo) && file_exists($_SERVER['DOCUMENT_ROOT'] . $logo_display_path . $current_logo)) { $logo_src = htmlspecialchars($logo_display_path . $current_logo, ENT_QUOTES, 'UTF-8').'?t='.time(); } ?>
                <?php if ($logo_src): ?> <div class="current-photo"> <img src="<?php echo $logo_src; ?>" alt="Current Facility Logo" class="profile-thumbnail" style="max-width: 150px; max-height: 150px; display: block; margin-bottom: 10px;" /> <div class="photo-actions"> <input type="checkbox" name="delete_logo" id="delete_logo" value="1" /> <label for="delete_logo"><small>Delete Current Logo</small></label> <input type="hidden" name="old_logo" value="<?php echo htmlspecialchars($current_logo, ENT_QUOTES, 'UTF-8'); ?>" /> </div> </div>
                <?php else: ?> <p class="no-photo-message" style="min-height: 170px;">No current logo.</p> <input type="hidden" name="old_logo" value="" />
                <?php endif; ?>
                <div class="upload-field" style="margin-top: 15px;"> <label for="logo">Upload New Logo</label> <input type="file" name="logo" id="logo" class="input-file" accept="image/jpeg, image/png, image/gif" /> <p class="field-help"><small>(JPG/PNG/GIF, Up to 2MB)</small></p> </div>
            </div>
        </div>

        <div> <?php // Right Column: Banner Image ?>
            <h4>Banner Image</h4>
            <div class="form-field photo-upload-area">
                <?php $current_banner = $facility_data_obj->image ?? null; $banner_src = null; if (!empty($current_banner) && file_exists($_SERVER['DOCUMENT_ROOT'] . $banner_display_path . $current_banner)) { $banner_src = htmlspecialchars($banner_display_path . $current_banner, ENT_QUOTES, 'UTF-8').'?t='.time(); } ?>
                 <?php if ($banner_src): ?> <div class="current-photo"> <img src="<?php echo $banner_src; ?>" alt="Current Banner Image" class="profile-thumbnail" style="max-width: 250px; max-height: 150px; display: block; margin-bottom: 10px;" /> <div class="photo-actions">  <input type="checkbox" name="delete_banner" id="delete_banner" value="1" /> <label for="delete_banner"><small>Delete Current Banner</small></label> <input type="hidden" name="old_banner" value="<?php echo htmlspecialchars($current_banner, ENT_QUOTES, 'UTF-8'); ?>" /> </div> </div>
                <?php else: ?> <p class="no-photo-message" style="min-height: 170px;">No current banner.</p> <input type="hidden" name="old_banner" value="" />
                <?php endif; ?>
                <div class="upload-field" style="margin-top: 15px;"> <label for="banner">Upload New Banner</label> <input type="file" name="banner" id="banner" class="input-file" accept="image/jpeg, image/png, image/gif" /> <p class="field-help"><small>(JPG/PNG/GIF, Recommended 1920x560, Up to 2MB)</small></p> </div>
            </div>
        </div>
    </div> -->

    <?php /* ----- Photo & Social Media Section ----- */ ?>
    <div class="form-grid" style="margin-top: 30px; align-items: flex-start;">
        <div> <?php // Left Column: Facility Logo ?>
            <h4>Facility Logo</h4>
            <div class="form-field photo-upload-area">
                <?php
                $current_logo = $facility_data_obj->logo ?? null;
                $logo_src = null;
                // Construct full server path for file_exists check
                $logo_server_path = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $logo_display_path . $current_logo;
                if (!empty($current_logo) && file_exists($logo_server_path)) {
                    // Use relative web path for src attribute
                    $logo_src = htmlspecialchars($logo_display_path . $current_logo, ENT_QUOTES, 'UTF-8').'?t='.time();
                }
                ?>
                <label for="logo">Upload New Logo</label> <?php // Label moved above image ?>

                <?php if ($logo_src): ?>
                    <div class="current-photo">
                        <img src="<?php echo $logo_src; ?>" alt="Current Facility Logo" class="profile-thumbnail" style="max-width: 150px; max-height: 150px; display: block; margin-bottom: 10px;" />
                        <div class="photo-actions">
                            <?php // AJAX Delete Button ?>
                            <button type="button" id="delete-facility-logo-btn" class="btn btn-danger"
                                    data-filename="<?php echo htmlspecialchars($current_logo, ENT_QUOTES, 'UTF-8'); ?>"
                                    data-imagetype="logo" <?php // Add type identifier for JS/AJAX ?>
                                    data-facilityid="<?php echo htmlspecialchars($Account->facility_id, ENT_QUOTES, 'UTF-8'); ?>"
                                    style="margin-top: 5px; margin-right: 10px; /* Add styles */">
                                <i class="fas fa-trash-alt"></i> Delete (AJAX)
                            </button>
                            <?php // Checkbox Fallback ?>
                            <input type="checkbox" name="delete_logo" id="delete_logo" value="1" style="margin-left:10px;"/>
                            <label for="delete_logo"><small>Delete on Save (Fallback)</small></label>
                            <input type="hidden" name="old_logo" value="<?php echo htmlspecialchars($current_logo, ENT_QUOTES, 'UTF-8'); ?>" />
                        </div>
                    </div>
                <?php else: ?>
                    <p class="no-photo-message" style="min-height: 50px; margin-bottom:10px;">No current logo.</p> <?php // Adjusted height ?>
                    <input type="hidden" name="old_logo" value="" />
                <?php endif; ?>

                <div class="upload-field" style="margin-top: 15px;">
                    <input type="file" name="logo" id="logo" class="input-file" accept="image/jpeg, image/png, image/gif" />
                    <p class="field-help"><small>(JPG/PNG/GIF, Up to 2MB)</small></p>
                </div>
            </div>
        </div>

        <div> <?php // Right Column: Banner Image ?>
            <h4>Banner Image</h4>
            <div class="form-field photo-upload-area">
                <?php
                $current_banner = $facility_data_obj->image ?? null; // Banner uses 'image' field
                $banner_src = null;
                 // Construct full server path for file_exists check
                $banner_server_path = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\') . $banner_display_path . $current_banner;
                 if (!empty($current_banner) && file_exists($banner_server_path)) {
                     // Use relative web path for src attribute
                     $banner_src = htmlspecialchars($banner_display_path . $current_banner, ENT_QUOTES, 'UTF-8').'?t='.time();
                 }
                ?>
                 <label for="banner">Upload New Banner</label> <?php // Label moved above image ?>

                 <?php if ($banner_src): ?>
                     <div class="current-photo">
                         <img src="<?php echo $banner_src; ?>" alt="Current Banner Image" class="profile-thumbnail" style="max-width: 250px; max-height: 150px; display: block; margin-bottom: 10px;" />
                         <div class="photo-actions">
                             <?php // AJAX Delete Button ?>
                             <button type="button" id="delete-facility-banner-btn" class="btn btn-danger"
                                     data-filename="<?php echo htmlspecialchars($current_banner, ENT_QUOTES, 'UTF-8'); ?>"
                                     data-imagetype="banner" <?php // Add type identifier for JS/AJAX ?>
                                     data-facilityid="<?php echo htmlspecialchars($Account->facility_id, ENT_QUOTES, 'UTF-8'); ?>"
                                     style="margin-top: 5px; margin-right: 10px; /* Add styles */">
                                 <i class="fas fa-trash-alt"></i> Delete (AJAX)
                             </button>
                              <?php // Checkbox Fallback ?>
                             <input type="checkbox" name="delete_banner" id="delete_banner" value="1" style="margin-left:10px;" />
                             <label for="delete_banner"><small>Delete on Save (Fallback)</small></label>
                             <input type="hidden" name="old_banner" value="<?php echo htmlspecialchars($current_banner, ENT_QUOTES, 'UTF-8'); ?>" />
                         </div>
                     </div>
                 <?php else: ?>
                     <p class="no-photo-message" style="min-height: 50px; margin-bottom:10px;">No current banner.</p> <?php // Adjusted height ?>
                     <input type="hidden" name="old_banner" value="" />
                 <?php endif; ?>

                 <div class="upload-field" style="margin-top: 15px;">
                     <input type="file" name="banner" id="banner" class="input-file" accept="image/jpeg, image/png, image/gif" />
                     <p class="field-help"><small>(JPG/PNG/GIF, Recommended 1920x560, Up to 2MB)</small></p>
                 </div>
            </div>
        </div>
    </div>


    <?php /* ----- Action Buttons ----- */ ?>
    <div class="form-actions" style="margin-top: 30px; display: flex; justify-content: space-between; align-items: center;">
        <a href="<?php echo htmlspecialchars($account_page_url, ENT_QUOTES, 'UTF-8'); ?>" class="button primary black back-button"> BACK TO MY ACCOUNT <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> </a>
        <button type="submit" id="save-facility-btn" name="submitform" class="button primary red save-button" value="Save Changes"> SAVE CHANGES <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> </button>
	</div>

    <?php /* ----- Hidden Fields ----- */ ?>
	<input type="hidden" name="update_facility" value="1" />
    <input type="hidden" name="facility_id_to_update" value="<?php echo htmlspecialchars($facility_data_obj->facility_id ?? '', ENT_QUOTES, 'UTF-8'); ?>" />
    <?php if(isset($_COOKIE['xid'])): ?>
    <input type="hidden" name="xid" value="<?php echo htmlspecialchars($_COOKIE['xid'], ENT_QUOTES, 'UTF-8'); ?>" />
    <?php endif; ?>
</form>
