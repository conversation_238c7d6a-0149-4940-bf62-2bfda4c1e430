<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Image Cropping
if($CMSUploader->crop_queue()){
	$redirect = $gallerysection['page_url']. '?action=edit&item_id=' .$_POST['gallery_id'];
	include('includes/jcropimages.php');

//Display form
}else{
	$image = '';
	if(ACTION == 'edit'){
		$data  = $records_arr[ITEM_ID];
		$image = $data['image'];

		if(!isset($_POST['save'])) {
			$row = $data;
		}
	}

	echo '<form action="" method="post" enctype="multipart/form-data">

		<div class="panel">
			<div class="panel-header">Photo Details
				<span class="panel-toggle fas fa-chevron-up"></span>

				<div class="panel-switch">
					<label>Show Photo</label>

					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"' .(isset($row['showhide']) && $row['showhide'] ? '' : ' checked'). ' />

						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>

			<div class="panel-content flex-container">
				<div class="form-field">
					<label>Photo Gallery <span class="required">*</span>' .$CMSBuilder->tooltip('Photo Gallery', 'This image will be displayed in the gallery defined here. Images must be added to a gallery in order to be displayed.').'</label>
					<select name="gallery_id" class="select' .(in_array('gallery_id', $required) ? ' required' : ''). '">
						<option value=""'.(!isset($row) ? ' selected' : '').'>- Select -</option>';
					foreach($galleries as $gallery) {
						echo '<option value="' .$gallery['gallery_id']. '"' .($row['gallery_id'] == $gallery['gallery_id'] ? ' selected' : ''). '>' .$gallery['name']. '</option>';
						}
					echo '</select>
				</div>
				<div class="form-field">
					<label>Photo Caption</label>
					<input type="text" name="caption" value="' .(isset($row['caption']) ? $row['caption'] : ''). '" class="input" />
				</div>
				<div class="form-field">
					<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
					<select name="ordering" class="select">
						<option value="101">Default</option>';
						for($i=1; $i<101; $i++) {
							echo '<option value="' .$i. '" ' .($row['ordering'] == $i ? 'selected' : ''). '>' .$i. '</option>';
						}
					echo '</select>
				</div>
			</div>
		</div>

		<div class="panel">
			<div class="panel-header">Photo
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">';

					echo $CMSBuilder->img_holder($image, $imagedir.'thumbs/');

					[$max_W, $max_H] = CMSUploader::max_size('gallery-photo', 'image');
					echo '<div class="form-field">
						<label>Upload Image ' .$CMSBuilder->tooltip('Upload Image', 'Image must be at least '.$max_W.' x '.$max_H.' and smaller than '.$_max_filesize['megabytes'].'.'). '</label>
						<input type="file" class="input' .(in_array('image', $required) ? ' required' : ''). '" name="image" value="" />
					</div>

					<div class="form-field">
						<label>Alt Text <small>(SEO)</small> '.$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.').'</label>
						<input type="text" name="image_alt" value="'.(isset($row['image_alt']) ? $row['image_alt'] : '').'" class="input" />
					</div>
				</div>
			</div>
		</div>';

		//Sticky footer
		echo '<footer id="cms-footer">
			<div class="flex-container">
				<div class="flex-column right">
					<button type="submit" name="save" value="save" class="button"><i class="fas fa-check"></i>Save Changes</button>
				</div>
				<div class="flex-column left">
		    		<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn"><i class="fas fa-trash-alt"></i>Delete</button>
		    		<a href="'.$gallerysection['page_url'].'?action=edit&item_id='.$row['gallery_id'].'" class="cancel">Cancel</a>
				</div>
			</div>
		</footer>

		<input type="hidden" name="xssid" value="' .$_COOKIE['xssid'] .'" />
	</form>';

}

?>