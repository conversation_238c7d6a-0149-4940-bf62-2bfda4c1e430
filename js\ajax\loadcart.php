<?php

//System files
include("../../config/config.php");
include ('../../config/database.php');
include('../../includes/functions.php');
include('../../includes/utils.php');

$ajaxcart = true;
$data = array('html' => '', 'cart_total' => count($reg_cart));
$tournaments = array();

//Sort cart items
if(!empty($reg_cart)){
	foreach($reg_cart as $item_id=>$item){
		if($item['event_type'] == '2'){
			$tournaments[$item_id] = $item;
		}
	}
}

//Display tournament cart
if(!empty($tournaments)){	
	$html = '';
	include("../../includes/widgets/shoppingcart.php");
	$data['html'] .= $html;
	
}else{
	$data['html'] = $Account->important('<i class="fa fa-shopping-cart"></i>&nbsp; Your shopping cart is empty.');
}

print_r(json_encode($data));

?>