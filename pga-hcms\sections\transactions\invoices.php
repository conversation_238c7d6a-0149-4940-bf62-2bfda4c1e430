<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	//Search
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Search ".$record_name."s
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";

		echo "<div class='panel-content clearfix'>";
			echo "<form id='advanced-search-form' class='clearfix' action='' method='get' enctype='multipart/form-data'>";
				echo "<div id='search-fields' class='column clearfix'>";
					echo "<div class='form-field'>
						<label>Search All</label>
						<input type='text' name='search' value='".$searchterm."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Start Date </label>
						<input type='text' name='start_date' value='".(isset($_SESSION['search_start_date'][SECTION_ID]) ? $_SESSION['search_start_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";

					echo "<div class='form-field'>
						<label>End Date </label>
						<input type='text' name='end_date' value='".(isset($_SESSION['search_end_date'][SECTION_ID]) ? $_SESSION['search_end_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";
	
					echo "<div class='form-field'>
						<label>Status </label>
						<select name='status' class='select'>
							<option value=''>All</option>
							<option value='1'".(isset($_SESSION['search_status'][SECTION_ID]) && $_SESSION['search_status'][SECTION_ID] == '1' ? " selected" : "").">Paid</option>
							<option value='0'".(isset($_SESSION['search_status'][SECTION_ID]) && $_SESSION['search_status'][SECTION_ID] == '0' ? " selected" : "").">Unpaid</option>
						</select>
					</div>";
				echo "</div>";

				echo "<div class='buttons-wrapper'>";
					echo "<div class='f_right'>";
						echo "<button type='button' class='button' onclick='exportForm(this.form);'><i class='fa fa-download'></i>Export</button> &nbsp;";
						echo "<button type='submit' class='button'><i class='fa fa-search'></i>Search</button>";
					echo "</div>";
					echo "<button type='button' class='button reset' onclick='document.getElementById(\"clear-search-form\").submit();'><i class='fa fa-times'></i>Clear</button>";
				echo "</div>";
	
			echo "</form>";
			echo "<form id='clear-search-form' name='clear-search-form' class='hidden' action='".PAGE_URL."' method='post'>
				<input type='hidden' name='clear-search' value='Clear' />
				<input type='hidden' name='search' value='' />
				<input type='hidden' name='start_date' value='' />
				<input type='hidden' name='end_date' value='' />
				<input type='hidden' name='xssid' value='".$_COOKIE['xssid']."' />
			</form>";
		echo "</div>";
	echo "</div>";

	//Export Form
	echo "<script>
		function exportForm(this_form) {
			this_form.target=\"_blank\"; 
			this_form.action=\"".$path."exports/export-invoices.php\"; 
			this_form.submit(); 
			this_form.target=\"\"; 
			this_form.action=\"\";
		}
	</script>";

	//Display records
	echo "<div class='panel'>";
		echo "<div class='panel-header'>".$record_name."s
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

			echo "<thead>";
			echo "<th width='150px'>No.</th>";
			echo "<th width='auto'>Bill To</th>";
			echo "<th width='auto'>Phone</th>";
			echo "<th width='auto'>Email</th>";
			echo "<th width='150px' class='right'>Total</th>";
			echo "<th width='150px' class='center'>Paid</th>";
			echo "<th width='150px' class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";

			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .$row['invoice_number']. "</td>";
					echo "<td>" .$row['bill_to']. "</td>";	
					echo "<td>" .$row['phone']. "</td>";
					echo "<td>" .$row['email']. "</td>";
					echo "<td class='right'>$" .number_format($row['invoice_total'], 2). "</td>";
					echo "<td class='center'>" .($row['paid'] == '1' ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-eye'></i>View</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo "</div>";	
	echo "</div>";
	

//Display form	
}else{

	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){	
		unset($row);
	}

	echo "<form id='invoice-form' action='' method='post' enctype='multipart/form-data'>";
	
	if(ACTION == 'add'){
	
		//Contact
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Contact Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field ui-front'>
					<label>Account Search</label>
					<input type='text' name='term' value='' class='account_suggest input' />
				</div>";
				echo "<hr class='clear' />";
				echo "<div class='form-field'>
					<label>First Name <span class='required'>*</span></label>
					<input type='text' name='first_name' value='" .(isset($row['first_name']) ? $row['first_name'] : ''). "' class='input first_name" .(in_array('first_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Last Name <span class='required'>*</span></label>
					<input type='text' name='last_name' value='" .(isset($row['last_name']) ? $row['last_name'] : ''). "' class='input last_name" .(in_array('last_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Email Address <span class='required'>*</span>" .$CMSBuilder->tooltip('Email Adress', 'Notification will be sent to this email address on invoice creation.'). "</label>
					<input type='text' name='email' value='" .(isset($row['email']) ? $row['email'] : ''). "' class='input email" .(in_array('email', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Phone Number <span class='required'>*</span></label>
					<input type='text' name='phone' value='" .(isset($row['phone']) ? $row['phone'] : ''). "' class='input phone" .(in_array('phone', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Account No.</label>
					<input type='text' name='account_id' class='input number account_id' value='" .(isset($row['account_id']) ? $row['account_id'] : '') ."' />
				</div>";				
			echo "</div>";
		echo "</div>";

		//Invoice
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Invoice Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='0'>
					<tr>
						<td width='200px'>Bill To <span class='required'>*</span></td>
						<td><input type='text' name='bill_to' value='" .(isset($row['bill_to']) ? $row['bill_to'] : ''). "' class='input full_name nomargin" .(in_array('bill_to', $required) ? ' required' : ''). "' /></td>
					</tr>
					<tr>
						<td>Payment Due Date " .$CMSBuilder->tooltip('Payment Due Date', 'Leave blank if no due date is required.'). "</td>
						<td><input type='text' name='due_date' value='" .(isset($row['due_date']) ? $row['due_date'] : '') ."' class='input datepicker nomargin' /></td>
					</tr>
					<tr>
						<td>GL Account</td>
						<td><select name='gl_id' class='select nomargin'>";
						foreach($glaccounts as $gl){
							echo "<option value='" .($gl['item_no'] == 4 ? '' : $gl['gl_id']). "'" .((isset($row['gl_id']) && $row['gl_id'] == $gl['gl_id']) || (!isset($row['gl_id']) && $gl['item_no'] == 4) ? ' selected' : ''). ">" .$gl['gl_name']. "</option>";
						}	
						echo "</select></td>
					</tr>
					<tr>
						<td>Invoice Amount ($)</td>
						<td><input type='text' name='amount' id='amount' value='" .(isset($row['amount']) ? $row['amount'] : '') ."' class='input number nomargin calc" .(in_array('amount', $required) ? ' required' : ''). "' /></td>
					</tr>
					<tr>
						<td>Taxes ($)</td>
						<td><input type='text' name='taxes' id='taxes' value='" .(isset($row['taxes']) ? $row['taxes'] : '') ."' class='input number nomargin calc' /></td>
					</tr>
					<tr>
						<td height='40px'><strong>Invoice Total:</strong></td>
						<td><strong id='invoice_total'>$0.00</strong></td>
					</tr>
				</table>";
			echo "</div>";
		echo "</div>";
		
		//Comments
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Comments " .$CMSBuilder->tooltip('Comments', 'Enter a description or any special instructions you would like to appear on the invoice.'). "
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<textarea name='comments' class='textarea'>" .(isset($row['comments']) ? $row['comments'] : ''). "</textarea>";
			echo "</div>";
		echo "</div>";
	
		
	//Edit form	
	}else{
		
		//Invoice
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
					<tr>
						<td width='150px'>Invoice No:</td>
						<td>".$row['invoice_number']."</td>
					</tr>
					<tr>
						<td>Invoice Date:</td>
						<td>".date('M j, Y g:iA', strtotime($row['invoice_date']))."</td>
					</tr>
					<tr>
						<td>Bill To:</td>
						<td>".$row['bill_to']."</td>
					</tr>
					<tr>
						<td>Payment Due Date:</td>
						<td>".(!empty($row['due_date']) ? date('M j, Y g:iA', strtotime($row['due_date'])) : 'None')."</td>
					</tr>
					<tr>
						<td>Payment Status:</td>
						<td>".($row['paid'] == '1' ? 'Processed' : 'Pending')."</td>
					</tr>
					<tr>
						<td>Created By:</td>
						<td>";
						if(!empty($row['updated_by'])){
							echo $row['updated_by_name'];
						}else{
							echo "Unknown";
						}
						echo "</td>
					</tr>";
					if(!empty($row['hio_id'])){
						echo "<tr>
							<td>Hole In One Event:</td>
							<td><a href='" .$sitemap[83]['page_url']."?action=edit&item_id=".$row['hio_id']. "'>".$row['event_name']."</a></td>
						</tr>";
					}
					echo "<tr>
						<td>GL Account:</td>
						<td><select name='gl_id' class='select nomargin'>";
						$default = (!empty($row['hio_id']) ? 3 : 4);
						foreach($glaccounts as $gl){
							echo "<option value='" .($gl['item_no'] == $default ? '' : $gl['gl_id']). "'" .((isset($row['gl_id']) && $row['gl_id'] == $gl['gl_id']) || (!isset($row['gl_id']) && $gl['item_no'] == $default) ? ' selected' : ''). ">" .$gl['gl_name']. "</option>";
						}	
						echo "</select></td>
					</tr>
				</table>";
			echo "</div>";
		echo "</div>";
		
		//Billing
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Contact Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
					<tr>
						<td width='150px'>Name:</td>
						<td>".$row['first_name']." ".$row['last_name']."</td>
					</tr>
					<tr>
						<td>Email:</td>
						<td>".$row['email']."</td>
					</tr>
					<tr>
						<td>Phone:</td>
						<td>".$row['phone']."</td>
					</tr>";
					if(!empty($row['account_id'])){
						echo "<tr>
							<td>Account No:</td>
							<td><a href='" .$sitemap[$_cmssections['manage_users']]['page_url']. "?action=edit&item_id=" .$row['account_id']. "'>" .$row['account_id']."</a></td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>";
		 
		//Totals
		echo "<div id='registration-totals' class='panel'>";
			echo "<div class='panel-header'>Totals 
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table width='100%' cellpadding='0' cellspacing='0'>";
					echo "<tbody>";
						echo "<tr>
							<td class='right'><strong>Amount:</strong></td>
							<td class='right'>$".number_format($row['invoice_total']-$row['taxes'],2)."</td>
						</tr>";
						echo "<tr>
							<td class='right'><strong>Taxes:</strong></td>
							<td class='right'>$".number_format($row['taxes'],2)."</td>
						</tr>";
						echo "<tr id='ordertotal'>
							<td class='right'><strong>Total:</strong></td>
							<td class='right' width='100px'><strong>$".number_format($row['invoice_total'],2)."</strong></td>
						</tr>";
					echo "</tbody>";
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Totals
	
		//Payments
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Payments
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table width='100%' cellpadding='0' cellspacing='0'>";
					echo "<thead>";
						echo "<tr>";
							echo "<th width='150px'>No.</th>";
							echo "<th width='150px'>Date</th>";					
							echo "<th width='250px'>Type</th>";
							echo "<th>Response</th>";
							echo "<th class='right' width='100px'>Status</th>";
							echo "<th class='right' width='100px'>Amount</th>";
						echo "</tr>";
					echo "</thead>";

					echo "<tbody>";
					if(!empty($row['payments'])){
						foreach($row['payments'] as $payment){	
							echo "<tr>";
								echo "<td><a href='" .$sitemap[$_cmssections['transactions-payments']]['page_url']. "?action=edit&item_id=" .$payment['payment_id']. "'>".$payment['payment_number']."</a></td>";
								echo "<td>".date('M j, Y g:iA', strtotime($payment['payment_date']))."</td>";
								echo "<td>";
								if($payment['payment_type'] == 'Credit Card' && !empty($payment['ccnumber'])){
									echo $payment['cctype']." **** **** **** ".$payment['ccnumber']. " &nbsp; " .substr($payment['ccexpiry'], 0, 2)."/".substr($payment['ccexpiry'], -2, 2); 
								}else{
									echo $payment['payment_type'];
								}
							echo "</td>";
								echo "<td>".$payment['message']."</td>";
								echo "<td class='right'>" .($payment['status'] == '1' ? "Processed" : "Failed"). "</td>";
								echo "<td class='right'>$".number_format($payment['amount'], 2)."</td>";
							echo "</tr>";
						}
						echo "<tr>
							<td colspan='5' class='right'><strong>Total Paid:</strong></td>
							<td class='right'>$".number_format($row['total_paid'],2)."</td>
						</tr>";
					}else{
						echo "<tr><td colspan='4'><em>No payments have been received for this invoice.</em></td></td>";
					}
					echo "</tbody>";
				echo "</table>";
				
				if(($row['total_paid']-$row['total_refunded']) < $row['invoice_total']){
					echo "<div class='pager left'><a href='" .$sitemap[$_cmssections['transactions-payments']]['page_url']. "?action=process&item_id=".ITEM_ID."&record=invoice' class='button'><i class='fa fa-credit-card'></i>Pay Now</a></div>";
				}else{
					echo "<div class='pager left'><a class='button disabled'><i class='fa fa-credit-card'></i>Pay Now</a> ".$CMSBuilder->tooltip('Pay Now', 'This invoice is paid in full.')."</div>";
				}

			echo "</div>"; 
		echo "</div>"; //Payments

		//Refunds
		if($row['total_paid'] > 0 && $row['invoice_total'] > 0){
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Refunds
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content nopadding'>";
					echo "<table width='100%' cellpadding='0' cellspacing='0'>";
						echo "<thead>";
							echo "<tr>";
								echo "<th width='150px'>No.</th>";
								echo "<th width='150px'>Date</th>";					
								echo "<th width='250px'>Type</th>";
								echo "<th>Response</th>";
								echo "<th class='right' width='100px'>Status</th>";
								echo "<th class='right' width='100px'>Amount</th>";
							echo "</tr>";
						echo "</thead>";
	
						echo "<tbody>";
							if(!empty($row['refunds'])){
								foreach($row['refunds'] as $refund){	
									echo "<tr>";
										echo "<td><a href='" .$sitemap[$_cmssections['transactions-refunds']]['page_url']. "?action=edit&item_id=" .$refund['refund_id']. "'>".$refund['refund_number']."</a></td>";
										echo "<td>".date('M j, Y g:iA', strtotime($refund['refund_date']))."</td>";
										echo "<td>";
										if($refund['refund_type'] == 'Credit Card' && !empty($refund['ccnumber'])){
											echo $refund['cctype']." **** **** **** ".$refund['ccnumber']. " &nbsp; " .substr($refund['ccexpiry'], 0, 2)."/".substr($refund['ccexpiry'], -2, 2); 
										}else{
											echo $refund['refund_type'];
										}
										echo "</td>";
										echo "<td>".$refund['message']."</td>";
										echo "<td class='right'>" .($refund['status'] == '1' ? "Processed" : "Failed"). "</td>";
										echo "<td class='right'>$".number_format($refund['amount'], 2)."</td>";
									echo "</tr>";
								}
								echo "<tr>
									<td colspan='5' class='right'><strong>Total Refunded:</strong></td>
									<td class='right'>$".number_format($row['total_refunded'],2)."</td>
								</tr>";
							}else{
								echo "<tr><td colspan='4'><em>No refunds have been applied to this invoice.</em></td></td>";
							}
						echo "</tbody>";
					echo "</table>";
			
					if($row['total_paid'] > $row['total_refunded']){
						echo "<div class='pager left'><a href='" .$sitemap[$_cmssections['transactions-refunds']]['page_url']. "?action=process&item_id=".ITEM_ID."&record=invoice' class='button'><i class='fa fa-undo'></i>Refund</a></div>";
					}else{
						echo "<div class='pager left'><a class='button disabled'><i class='fa fa-undo'></i>Refund</a> ".$CMSBuilder->tooltip('Refund Invoice', 'This transaction is not eligible for a refund.')."</div>";
					}
			
				echo "</div>"; 
			echo "</div>"; // Refunds

		}
		
		//Comments
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Comments " .$CMSBuilder->tooltip('Comments', 'Enter a description or any special instructions you would like to appear on the invoice.'). "
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<textarea name='comments' class='textarea'>" .(isset($row['comments']) ? $row['comments'] : ''). "</textarea>";
			echo "</div>";
		echo "</div>";
		
	}
	
	// //Sticky footer
	// echo "<footer id='cms-footer' class='resize'>";
	// 	if(ITEM_ID == ""){
	// 		echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save &amp; Send</button>";
	// 	}else{
	// 		echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
	// 		echo "<button type='submit' class='button f_right' name='download' value='download'><i class='fa fa-file-pdf-o'></i>Download</button>";
	// 		echo "<button type='button' class='button delete' name='delete' value='delete'><i class='fa fa-trash'></i>Delete</button>";
	// 	}
	// 	echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
	// echo "</footer>";

	//Sticky footer
	include('includes/widgets/formbuttons.php');
	
	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "</form>";

?>
<script type="text/javascript">

function monify(val){
	return "$" + parseFloat(val).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
}

$('#invoice-form input.calc').bind('change', function(){

	var amount = $('#amount').val();
	var taxes = $('#taxes').val();
	amount = (amount != '' && amount != undefined ? parseFloat(amount) : 0);
	taxes = (taxes != '' && taxes != undefined ? parseFloat(taxes) : 0);
	
	//Autocalculate taxes
	if($(this).attr('id') == 'amount'){
		var gst_rate = <?php echo ($tax_rates['gst_rate']/100); ?>;
		var pst_rate = <?php echo ($tax_rates['pst_rate']/100); ?>;
		var tax_rate = parseFloat(gst_rate+pst_rate);
		taxes = parseFloat(amount*tax_rate);
	}
	
	var total = parseFloat(amount+taxes);
	
	$('#taxes').val(taxes.toFixed(2));
	$('#invoice_total').html(monify(total));
	
});
</script>

<?php } ?>