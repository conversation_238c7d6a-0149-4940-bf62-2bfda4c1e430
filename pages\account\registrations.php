<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Display all
if(ACTION == '' || empty($registration)){

	$html .= $page['page_panels'][$panel_id]['content'];

	// Initialize variables for sidebar and content
	$subnav = '';
	$mblnav = '';
	$content = '';

	// Get the current section (default to tournaments if not set)
	$current_section = isset($_GET['section']) ? $_GET['section'] : 'tournaments';

	// Build the sidebar navigation
	$subnav .= '<div id="account_navigation" class="f_left">';

	// Search bar in sidebar
	$subnav .= '<div class="sidebar-search">
		<form name="search-form" id="sidebar-search" action="" method="get">
			<input type="hidden" name="section" value="' . $current_section . '" />
			<div class="search-input-wrap">
				<input type="text" name="search" class="input" value="' .(isset($_GET['search']) ? $_GET['search'] : ''). '" placeholder="Search" />
				<button type="submit" class="button solid"><i class="fa fa-search"></i></button>
			</div>
		</form>
	</div>';

	// Year selector inside sidebar
	$subnav .= '<div class="sidebar-year-selector">
		<select name="year" class="select" onchange="window.location=\'' . $page['page_url'] . '?section=' . $current_section . '&year=\'+this.value;">
			<option value="' .date('Y'). '">Current Season</option>';
			for($y=(date('Y')-1); $y>=2014; $y--){
				$subnav .= '<option value="' .$y. '"' .(isset($_GET['year']) && $_GET['year'] == $y ? ' selected' : ''). '>' .$y. '</option>';
			}
		$subnav .= '</select>
	</div>';

	// Navigation items
	$subnav .= '<nav class="sub-navigation">';
		// <h4>My Account</h4>
	$subnav .= '<ul>';

	// Define navigation items
	$nav_items = array(
		// 'season' => 'Current Season',
		// 'education' => 'Education',
		'tournaments' => 'Tournaments',
		// 'insurance' => 'Hole-in-One Insurance',
		'events' => 'Events',
		'waitlists' => 'Waitlists',
		'downloads' => 'Downloads'
	);

	// Mobile view: Search, year selector, and navigation dropdown
	$mblnav .= '<div class="mobile-only">
		<form name="mobile-search-form" id="mobile-search-bar" action="" method="get">
			<input type="hidden" name="section" value="' . $current_section . '" />
			<div class="search-input-wrap">
				<input type="text" name="search" class="input" value="' .(isset($_GET['search']) ? $_GET['search'] : ''). '" placeholder="Search" />
				<button type="submit" class="button solid"><i class="fa fa-search"></i></button>
			</div>
		</form>
		' . $mobile_year_selector . '
	</div>
	<select class="select mobile-only" id="nav-menu" onchange="window.location=\'' . $page['page_url'] . '?section=\'+this.value;">';

	// Build navigation links
	foreach($nav_items as $key => $name) {
		$is_active = ($current_section == $key);
		$subnav .= '<li><a href="' . $page['page_url'] . '?section=' . $key . '"' . ($is_active ? ' class="active"' : '') . '>' . $name . '</a></li>';
		$mblnav .= '<option value="' . $key . '"' . ($is_active ? ' selected' : '') . '>' . $name . '</option>';
	}

	$subnav .= '</ul>
	</nav>';
	$mblnav .= '</select>';

	$subnav .= '<button type="button" class="button primary black back-button light" style="margin-top: 15px;">Back to My Account<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></button></div>';

	// Set a variable for mobile year selector that will be used in mobile view
	$mobile_year_selector = '<div class="mobile-year-selector">
		<select name="year" class="select" onchange="window.location=\'' . $page['page_url'] . '?section=' . $current_section . '&year=\'+this.value;">
			<option value="' .date('Y'). '">Current Season</option>';
			for($y=(date('Y')-1); $y>=2014; $y--){
				$mobile_year_selector .= '<option value="' .$y. '"' .(isset($_GET['year']) && $_GET['year'] == $y ? ' selected' : ''). '>' .$y. '</option>';
			}
		$mobile_year_selector .= '</select>
	</div>';

	// Content area - show based on selected section
	$content .= '<div class="f_right registration-content">';

	// Show appropriate section based on selection
	switch($current_section) {
		// case 'season':
		// 	// $content .= '<h3>Current Season</h3>';
		// 	$content .= '<div class="content-area">';
		// 	$content .= '<p>View your current season information.</p>';
		// 	$content .= '</div>';
		// 	break;

		case 'tournaments':
			// $content .= '<h3>Tournaments</h3>';
			$content .= '<div class="content-area">';
			$content .= '<table cellpadding="10" cellspacing="0" border="0" width="100%">';
			if(!empty($tournaments)){
				$content .= '<tr>
					<th align="left" width="160px">Reg No.</th>
					<th align="left">Tournament</th>';
					// <th align="left">Status</th>
				// <th align="left">Payment</th>
					// <th align="left">Entry Fee</th>
					// <th width="160px">Action</th>
				$content .= '</tr>';
				foreach($tournaments as $tournament){
					$content .= '<tr class="registration-row">
						<td class="reg-number"><a href="' .$page['page_url']. '?action=edit&id=' .$tournament['registration_id']. '">' .$tournament['registration_number']. '</a></td>
						<td class="event-info">
							<div class="event-name">' .$tournament['event_name']. '</div>
							<div class="event-category"><span class="category-badge">' .$tournament['event_category']. '</span></div>
							<div class="event-date">' .format_date_range($tournament['start_date'], $tournament['end_date']). '</div>
						</td>';
						// <td>' .$tournament['reg_status']. '</td>
						// <td>';
						// if($tournament['paid'] == '1'){
						// 	$content .= 'Processed';
						// }else{
						// 	if($tournament['reg_status'] != 'Registered'){
						// 		$content .= 'Cancelled';
						// 	}else{
						// 		if(!empty($tournament['payment_deadline']) && $tournament['payment_deadline'] <= date("Y-m-d")){
						// 			$content .= '<span class="color-red">Overdue</span>';
						// 		}else{
						// 			$content .= 'Pending';
						// 		}
						// 	}
						// }
						// $content .= '</td>
						// // <td>$' .number_format($tournament['ticket_price'], 2). '</td>';
						$content .= '<td class="action-cell">
							<a href="' .$page['page_url']. '?action=edit&id=' .$tournament['registration_id']. '" class="button simple action-button">

								<i class="fa fa-arrow-right arrow-icon"></i>
							</a>
						</td>
					</tr>';
				}
			}else{
				$content .= '<tr>
					<td class="nobg">' .(isset($_GET['search']) && trim($_GET['search']) != '' ? 'No tournaments found matching `<strong>' .$_GET['search']. '</strong>`.' : 'You are not registered for any tournaments.'). '</td>
				</tr>';
			}
			$content .= '</table>';
			$content .= '</div>';
			break;

		case 'education':
			// $content .= '<h3>Education</h3>';
			$content .= '<div class="content-area">';
			$content .= '<p>Educational resources and events will be displayed here.</p>';
			$content .= '</div>';
			break;

		case 'insurance':
			// $content .= '<h3>Hole-in-One Insurance</h3>';
			$content .= '<div class="content-area">';
			$content .= '<p>Your hole-in-one insurance information will be displayed here.</p>';
			$content .= '</div>';
			break;

		case 'events':
			// $content .= '<h3>Events</h3>';
			$content .= '<div class="content-area">';
			$content .= '<table cellpadding="10" cellspacing="0" border="0" width="100%">';
			if(!empty($events)){
				$content .= '<tr>
					<th align="left" width="160px">Reg No.</th>
					<th align="left">Event</th>';
					// <th align="left">Status</th>
					// <th align="eft">Payment</th>
					// <th align="left">Total</th>
				// <th width="120px">Action</th>
				$content .= '</tr>';
				foreach($events as $event){
					$content .= '<tr class="registration-row">
						<td class="reg-number"><a href="' .$page['page_url']. '?action=edit&id=' .$event['registration_id']. '">' .$event['registration_number']. '</a></td>
						<td class="event-info">
							<div class="event-name">' .$event['event_name']. '</div>
							<div class="event-category"><span class="category-badge">' .$event['event_category']. '</span></div>
							<div class="event-date">' .format_date_range($event['start_date'], $event['end_date']). '</div>
						</td>';
						// <td>' .$event['reg_status']. '</td>
						// <td>';
						// if($event['paid'] == '1'){
						// 	$content .= 'Processed';
						// }else{
						// 	if($event['reg_status'] != 'Registered'){
						// 		$content .= 'Cancelled';
						// 	}else{
						// 		if(!empty($event['payment_deadline']) && $event['payment_deadline'] <= date("Y-m-d")){
						// 			$content .= '<span class="color-red">Overdue</span>';
						// 		}else{
						// 			$content .= 'Pending';
						// 		}
						// 	}
						// }
						// $content .= '</td>
						// <td>$' .number_format($event['registration_total']+$event['reg_admin_fee'], 2). '</td>';
						$content .= '<td class="action-cell">
							<a href="' .$page['page_url']. '?action=edit&id=' .$event['registration_id']. '" class="button simple action-button">
								<i class="fa fa-arrow-right arrow-icon"></i>
							</a>
						</td>
					</tr>';
				}
			}else{
				$content .= '<tr>
					<td class="nobg">' .(isset($_GET['search']) && trim($_GET['search']) != '' ? 'No events found matching `<strong>' .$_GET['search']. '</strong>`.' : 'You are not registered for any events.'). '</td>
				</tr>';
			}
			$content .= '</table>';
			$content .= '</div>';
			break;

		case 'waitlists':
			// $content .= '<h3>Waitlists</h3>';
			$content .= '<div class="content-area">';
			$content .= '<table cellpadding="10" cellspacing="0" border="0" width="100%">';
			if(!empty($waitinglists)){
				$content .= '<tr>
					<th align="left">Event/Tournament</th>
					<th align="left">Subscribe Date</th>
					<th width="120px">&nbsp;</th>
				</tr>';
				foreach($waitinglists as $waitlist){
					$content .= '<tr class="registration-row">
						<td class="event-info">
							<div class="event-name">' .$waitlist['event_name']. '</div>
							<div class="event-category"><span class="category-badge">Waitlist</span></div>
							<div class="event-date">' .format_date_range($waitlist['start_date'], $waitlist['end_date']). '</div>
						</td>
						<td>' .date("M j, Y", strtotime($waitlist['date_added'])). '</td>
						<td class="action-cell">
							<form method="post" action="">
								<button type="button" name="delete" class="button simple delete action-button">
									<i class="fa fa-trash edit-icon"></i>
									<i class="fa fa-arrow-right arrow-icon"></i>
								</button>
								<input type="hidden" name="unsubscribe" value="' .$waitlist['occurrence_id']. '" />
								<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
							</form>
						</td>
					</tr>';
				}
			}else{
				$content .= '<tr>
					<td class="nobg">' .(isset($_GET['search']) && trim($_GET['search']) != '' ? 'No waiting lists found matching `<strong>' .$_GET['search']. '</strong>`.' : 'You are not subscribed to any waiting lists.'). '</td>
				</tr>';
			}
			$content .= '</table>';
			$content .= '</div>';
			break;

		case 'downloads':
			// $content .= '<h3>Downloads</h3>';
			$content .= '<div class="content-area">';
			$year = (isset($_GET['year']) ? $_GET['year'] : date('Y'));
			$content .= '<table cellpadding="10" cellspacing="0" border="0" width="100%">
				<tr>
					<td class="nobg">';
					if($year >= 2018){

				//PD Points
				$content .= '<a href="' .$path.'reports/report-pd.php?year='.$year.'&id='.($_SESSION['profile_id'] ?? '').'" class="download-link" target="_blank"><i class="fa fa-file-pdf-o"></i> <span>' .$year. ' Top 100 Program Points</span><i class="fa fa-arrow-right arrow-icon"></i></a><br />';

				//Tax Receipt (don't display this year's receipt until october)
				if(!empty($tournaments) && ($year < date('Y') || ($year == date('Y') && date('n') >= 10))){
					$content .= '<a href="' .$path.'reports/report-receipt.php?year='.$year.'&id='.($_SESSION['profile_id'] ?? '').'" class="download-link" target="_blank"><i class="fa fa-file-pdf-o"></i> <span>' .$year. ' Tournament Expense Receipt</span><i class="fa fa-arrow-right arrow-icon"></i></a>';
				}

			}else{
				$content .= 'No downloads to display.';
			}
			$content .= '</td>
				</tr>
			</table>';
			$content .= '</div>';
			break;
	}

	$content .= '</div>';

// Combine all elements and set as HTML content
	$html .= '<div id="registration_content" class="clearfix">';
	$html .= $mblnav.$subnav.$content;
	$html .= '</div>';

//Selected registration
}else if(ACTION == 'edit'){
	// No changes to the edit mode

	//Panel title
	$page['page_panels'][$panel_id]['title'] = 'Registration';
	$page['page_panels'][$panel_id]['show_title'] = true;

	//Registration information
	$html .= '<h4>Registration Information</h4>
	<table cellpadding="0" cellspacing="0" border="0" width="100%" class="nobgs noresponsive noborder registration-info">
		<tr>
			<td width="200px">Registration No:</td>
			<td>' .$registration['registration_number']. '</td>
		</tr>
		<tr>
			<td>Registration Date:</td>
			<td>' .date("M j, Y g:iA", strtotime($registration['registration_date'])). '</td>
		</tr>
		<tr>
			<td>Contact Name:</td>
			<td>' .$registration['first_name'].' '.$registration['last_name']. '</td>
		</tr>
		<tr>
			<td>Email Address:</td>
			<td><a href="mailto:' .$registration['email']. '">' .$registration['email'].'</a></td>
		</tr>
		<tr>
			<td>Phone Number:</td>
			<td><a href="tel://' .$registration['phone']. '">' .$registration['phone'].'</a></td>
		</tr>';
		if($registration['company'] != ''){
			$html .= '<tr>
				<td>Company Name:</td>
				<td>' .$registration['company']. '</td>
			</tr>';
		}
		if($registration['facility'] != ''){
			$html .= '<tr>
				<td>Facility Name:</td>
				<td>' .$registration['facility']. '</td>
			</tr>';
		}
	$html .= '</table>';

	//Download
	$html .= '<p><a href="' .$page['page_url']. '?action=edit&id=' .ITEM_ID. '&download=true"><i class="fa fa-file-pdf-o"></i>&nbsp; Download Registration</a></p>';

	//User is the registrant and registration is active, show billing
	if($registration['account_id'] == USER_LOGGED_IN && $registration['event_type'] == 2 && $active_registrants > 0){

		//Billing informtion
		$billing = array();
		if($registration['paid'] == '0' && !empty($registration['payment_deadline']) && $registration['payment_deadline'] > date("Y-m-d")){
			$html .= '<h4>Payment Information</h4>';
			if(array_key_exists($registration['billing_id'], $billing_profiles)){
				$billing = $billing_profiles[$registration['billing_id']];
				$html .= '<small>Automatic billing is scheduled for ' .date("M j, Y", strtotime($registration['payment_deadline'])). '.</small><br />
				<p>'
					.$billing['cctype']." **** **** **** ".$billing['ccnumber']. " &nbsp; " .substr($billing['ccexpiry'], 0, 2)."/".substr($billing['ccexpiry'], -2, 2)
					.($billing['expired'] ? ' &nbsp; <small class="color-red"><i class="fa fa-exclamation-triangle"></i> Credit card is expired</small>' : ''). '
				</p>';
			}else{
				$html .= '<small>Payment deadline for this registration is ' .date("M j, Y", strtotime($registration['payment_deadline'])). '.</small><br />
				<p><i class="fa fa-exclamation-triangle"></i> No billing profile has been selected for this registration. Please select one now.</p>';
			}
			$html .= '<p class="nopadding"><a onclick="dialogModal(\'#billing-profiles\');" class="button simple dinline-block">' .(!empty($billing) ? 'Change' : 'Select'). '</a></p>';
			$html .= '<br />';
		}

		//Billing profile select
		$html .= '<div class="hidden">
			<div id="billing-profiles" class="hidden-modal" title="Billing Profiles" data-modal-width="600">';
			if(!empty($billing_profiles)){
				$html .= '<form action="" method="post">';
					foreach($billing_profiles as $bill){
						$html .= '<p><input type="radio" class="radio" name="billing_id" id="bill-' .$bill['billing_id']. '" value="' .$bill['billing_id']. '"' .($registration['billing_id'] == $bill['billing_id'] ? ' checked' : ''). ' />
						<label for="bill-' .$bill['billing_id']. '">' .$bill['cctype']." **** **** **** ".$bill['ccnumber']. " &nbsp; " .substr($bill['ccexpiry'], 0, 2)."/".substr($bill['ccexpiry'], -2, 2). '</label></p>
						<hr />';
					}
					$html .= '<p class="form-buttons">
						<button type="submit" name="billing" class="button solid inline f_right" value="update">Update</button>
						<a href="' .$_sitepages['billing-profiles']['page_url']. '?action=add" class="previous f_right">+ Create Profile</a>
					</p>
					<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
				</form>';
			}else{
				$html .= 'No billing profiles to display. &nbsp; <a href="' .$_sitepages['billing-profiles']['page_url']. '?action=add">+ Create Profile</a>';
			}
			$html .= '</div>
		</div>';
	}

	//Events/tournaments
	foreach($registration['events'] as $event){
		$html .= '<h4>' .$event['name']. ' - ' .format_date_range($event['start_date'], $event['end_date']). '</h4>';

		$registered = 0;
		foreach($event['attendees'] as $attendee){
			$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noheaders nobgs reg-table">';
				$html .= '<tr>
					<td valign="top">' .$attendee['first_name'].' '.$attendee['last_name']. ' - '.$attendee['reg_status'];
						if($event['event_type'] != 2){
							$html .= '<small class="dblock">' .$attendee['ticket_type']. ': $' .number_format($attendee['ticket_price'], 2).'</small>';
						}
						if($attendee['email'] != ''){
							$html .= '<small class="dblock">Email: '.$attendee['email'].'</small>';
						}
						if($attendee['phone'] != ''){
							$html .= '<small class="dblock">Phone: '.$attendee['phone'].'</small>';
						}
						if($attendee['company'] != ''){
							$html .= '<small class="dblock">Company: '.$attendee['company'].'</small>';
						}
						if($attendee['facility'] != ''){
							$html .= '<small class="dblock">Facility: '.$attendee['facility'].'</small>';
						}
						if($attendee['position'] != ''){
							$html .= '<small class="dblock">Position: '.$attendee['position'].'</small>';
						}
						if($attendee['comments'] != ''){
							$html .= '<small class="dblock">Comments: '.$attendee['comments'].'</small>';
						}
						if(!empty($attendee['handicap'])){
							$html .= '<small class="dblock">Handicap: '.$attendee['handicap'].'</small>';
						}
						$attendee_addons = 0;
						if(isset($attendee['addons']) && !empty($attendee['addons'])){
							foreach($attendee['addons'] as $addon){
								$html .= '<small class="dblock">' .$addon['name']. ': '.$addon['value']. ($addon['price_adjustment'] > 0 ? ' - $'.number_format($addon['price_adjustment'], 2) : '').'</small>';
								$attendee_addons += $addon['price_adjustment'];
							}
						}
					$html .= '</td>';
					if($event['event_type'] == 2){
						$html .= '<td valign="top" align="right">' .$attendee['ticket_type']. ':</td>
						<td valign="top" align="right" width="120px">$' .number_format($attendee['ticket_price'], 2). '</td>';
					}else{
						$html .= '<td valign="top" align="right">Attendee Total:</td>
						<td valign="top" align="right" width="120px">$' .number_format($attendee['ticket_price']+$attendee_addons, 2). '</td>';
					}
				$html .= '</tr>';

				//Withdraw
				if($attendee['reg_status'] != 'Withdrawn' && $event['start_date'] > date("Y-m-d") && ($registration['account_id'] == USER_LOGGED_IN || $attendee['account_id'] == USER_LOGGED_IN)){

					//Tournament withdrawal
					if($event['event_type'] == 2){

						//Registration deadline has passed
						if(strtotime($event['reg_deadline'].' '.$reg_settings['reg_close_time']) <= strtotime('now')){
							$html .= '<tr>
								<td colspan="3"><a onclick="dialogAlert(\'Tournament Withdrawal\', \'Online withdrawal for this event is closed. If you wish to withdraw from this event, please contact our office by phone ' .$global['contact_phone']. ' or by email ' .$global['contact_email']. '.\');" class="button simple dinline-block red border">Withdraw</a></td>
							</tr>';
						}else{
							$html .= '<tr>
								<td colspan="3"><a href="' .$_sitepages['withdrawal']['page_url']. '?id=' .$attendee['attendee_id']. '" class="button simple dinline-block red-border">Withdraw</a></td>
							</tr>';
						}

					//Event withdrawal must contact office
					}else{
						$html .= '<tr>
							<td colspan="3"><a onclick="dialogAlert(\'Event Withdrawal\', \'Online withdrawal for this event is unavailable. If you wish to withdraw from this event, please contact our office by phone ' .$global['contact_phone']. ' or by email ' .$global['contact_email']. '.\');" class="button simple dinline-block red-border">Withdraw</a></td>
						</tr>';
					}
				}

			$html .= '</table>';
			if($attendee['reg_status'] == 'Registered'){
				$registered++;
			}
		}

		//If team event, check if partner needs to be selected
		if($event['event_type'] == 2 && $event['team_event'] && $registered < 2 && $active_registrants > 0 && $event['start_date'] > date("Y-m-d") && $registration['account_id'] == USER_LOGGED_IN){
			$html .= '<form action="" method="post">
				<fieldset class="clearfix">
					<div class="form-column f_left">
						<label>Select Partner (Eligible Member)</label>
						<select name="partner_id_' .$event['event_id']. '" class="select">
							<option value="">- Please Choose -</option>';
							$members = $Registration->get_eligible_attendees($event['occurrence_id']);
							foreach($members as $member){
								if($member['account_id'] != USER_LOGGED_IN && !$member['overdue_invoices']){
									$html .= '<option value="' .$member['account_id']. '">' .$member['last_name'].', '.$member['first_name']. ' - ' .(!empty($member['facility_name']) ? $member['facility_name'] : 'No Facility'). '</option>';
								}
							}
						$html .= '</select>
					</div>
					<div class="clear clearfix">
						<div class="form-column f_left">
							<label><strong>OR</strong> Enter Partner Name (Non-Member)</label>
							<input type="text" name="partner_first_name_' .$event['event_id']. '" class="input half f_left" placeholder="First Name" value="" />
							<input type="text" name="partner_last_name_' .$event['event_id']. '" class="input half f_right" placeholder="Last Name" value="" />
						</div>
						<div class="form-column f_right">
							<label>Partner Handicap</label>
							<input type="text" name="partner_handicap_' .$event['event_id']. '" class="input" value="" />
						</div>
					</div>
					<button type="submit" name="register" value="true" class="button solid inline nomargin">Register Partner</button>
				</fieldset>
				<input type="hidden" name="team_event_id" value="' .$event['event_id']. '" />
				<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
			</form>';
		}
	}

	//User is the registrant, show transactions
	if($registration['account_id'] == USER_LOGGED_IN){

		//Promo code
		if($registration['discount'] > 0 && $registration['promocode'] != ''){
			$html .= '<div class="promo-code">Discount Code: ' .$registration['promocode']. '</div>';
		}

		//Display totals
		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive registration-info">';
			if($registration['discount'] > 0){
				$html .= '<tr>
					<td align="right">Discount:</td>
					<td class="right">$' .number_format($registration['discount'], 2). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td align="right">Subtotal:</td>
				<td class="right" width="120px">$' .number_format($registration['registration_total']-$registration['taxes']-$registration['fees'], 2). '</td>
			</tr>';
			$html .= '<tr>
				<td align="right">Taxes:</td>
				<td class="right">$' .number_format($registration['taxes'], 2). '</td>
			</tr>';
			if($registration['fees'] > 0){
				$html .= '<tr>
					<td align="right">Skins:</td>
					<td class="right">$' .number_format($registration['fees'], 2). '</td>
				</tr>';
			}
			$html .= '<tr>
				<td align="right">Service Fee:</td>
				<td class="right">$' .number_format($registration['admin_fee'], 2). '</td>
			</tr>';
			$html .= '<tr>
				<td align="right"><h6>Total:</h6></td>
				<td class="right"><h6>$' .number_format($registration['registration_total']+$registration['admin_fee'], 2). '</h6></td>
			</tr>
		</table>';

		//Transactions
		$html .= '<div class="content-tabs form-tabs clear clearfix" data-tabs-limit="5">
			<nav class="tabs-nav-wrapper clearfix">
				<ul class="tabs-nav clearfix">
					<li><a href="#payments">Payments</a></li>
					' .(!empty($registration['refunds']) ? '<li><a href="#refunds">Refunds</a></li>' : ''). '
				</ul>
			</nav>';

			//Payments
			$html .= '<div id="payments" class="tabs-panel">
				<table cellpadding="10" cellspacing="0" border="0" width="100%" class="nomargin">';

				if(!empty($registration['payments'])){
					$html .= '<tr>
						<th align="left">No.</th>
						<th align="left">Date</th>
						<th align="left">Type</th>
						<th align="right">Status</th>
						<th align="right" width="120px">Amount</th>
					</tr>';
					foreach($registration['payments'] as $payment){
						$html .= '<tr>
							<td>' .$payment['payment_number']. '</td>
							<td>' .date('M j, Y g:iA', strtotime($payment['payment_date'])). '</td>
							<td>';
							if($payment['payment_type'] == 'Credit Card' && !empty($payment['ccnumber'])){
								$html .= $payment['cctype']." **** **** **** ".$payment['ccnumber']. " &nbsp; " .substr($payment['ccexpiry'], 0, 2)."/".substr($payment['ccexpiry'], -2, 2);
							}else{
								$html .= $payment['payment_type'];
							}
							$html .= '</td>
							<td align="right">' .($payment['status'] == '1' ? 'Processed' : 'Failed'). '</td>
							<td align="right">$' .number_format($payment['amount']+$payment['admin_fee'], 2). '</td>
						</tr>';
					}
				}else{
					$html .= '<tr>
						<td class="nobg">No transactions to display.</td>
					</tr>';
				}
				$html .= '</table>
				<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive noborder nobgs nomargin registration-info">
					<tr>
						<td align="right"><h6>Total Paid:</h6></td>
						<td class="right" width="120px"><h6>$' .number_format($registration['total_paid']+$registration['admin_fee'], 2). '</h6></td>
					</tr>';

					//Balance due
					$registration['balance'] = $registration['total_paid']-$registration['total_refunded'];
					if(!$registration['paid'] && $registration['balance'] < $registration['registration_total'] && $active_registrants > 0){
						$overdue = (!empty($registration['payment_deadline']) && $registration['payment_deadline'] <= date("Y-m-d") ? true : false);
						$html .= '<tr>
							<td align="right"><h5 class="' .($overdue ? 'color-red' : ''). '">' .($overdue ? 'Overdue' : 'Balance Due'). ':</h5></td>
							<td class="right" width="150px"><h5 class="' .($overdue ? 'color-red' : ''). '">$' .number_format($registration['registration_total']-$registration['balance'], 2). '</h5></td>
						</tr>';
						$html .= '<tr>
							<td colspan="2" align="right"><a href="' .$_sitepages['payments']['page_url']. '?id=r' .$registration['registration_id']. '" class="button solid nomargin primary red f_right">Pay Now<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a></td>
						</tr>';
					}

				$html .= '</table>
			</div>';

			//Refunds
			if(!empty($registration['refunds'])){
				$html .= '<div id="refunds" class="tabs-panel">
					<table cellpadding="10" cellspacing="0" border="0" width="100%" class="nomargin">';

					$html .= '<tr>
						<th align="left">No.</th>
						<th align="left">Date</th>
						<th align="left">Type</th>
						<th align="right">Status</th>
						<th align="right" width="120px">Amount</th>
					</tr>';
					foreach($registration['refunds'] as $refund){
						$html .= '<tr>
							<td>' .$refund['refund_number']. '</td>
							<td>' .date('M j, Y g:iA', strtotime($refund['refund_date'])). '</td>
							<td>';
							if($refund['refund_type'] == 'Credit Card' && !empty($refund['ccnumber'])){
								$html .= $refund['cctype']." **** **** **** ".$refund['ccnumber']. " &nbsp; " .substr($refund['ccexpiry'], 0, 2)."/".substr($refund['ccexpiry'], -2, 2);
							}else{
								$html .= $refund['refund_type'];
							}
							$html .= '</td>
							<td align="right">' .($refund['status'] == '1' ? 'Processed' : 'Failed'). '</td>
							<td align="right">$' .number_format($refund['amount'], 2). '</td>
						</tr>';
					}
					$html .= '</table>
					<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive noborder nobgs nomargin">
						<tr>
							<td align="right"><h6>Total Refunded:</h6></td>
							<td class="right" width="120px"><h6>$' .number_format($registration['total_refunded'], 2). '</h6></td>
						</tr>
					</table>
				</div>';
			}

		$html .= '</div>';
	}


}

// Set panel content
// $page['page_panels'][$panel_id]['content'] = $html;

?>