<?php
if(PAGE_ID == $_sitepages['login']['page_id']){
	
	//Check for login alert
	if(isset($_SESSION['login_alert'])){
		$alert = $Account->alert($_SESSION['login_alert']['message'], $_SESSION['login_alert']['status']);
		unset($_SESSION['login_alert']);
	}
	
	//Check for activation code
	if(isset($_GET['activate']) && !empty($_GET['activate'])){
		$public_key = $_GET['activate'];

		//If already logged in another account
		if(USER_LOGGED_IN){
			try{
				$Account->logout();
				header('Location:' .$_sitepages['login']['page_url'] .'?activate='.$public_key);
				exit();
			}catch(Exception $e){
			}
		}

		//Activate new account
		try{
			$Account->activate_account($public_key);
			$alert = $Account->alert('Account has been activated and you can now login.', true);
		}catch(Exception $e){
			$alert = $Account->alert($e->getMessage(), false);
		}
	}

	//Check if already logged in
	if(USER_LOGGED_IN){
		header('Location:' .$_sitepages['account']['page_url']);
		exit();
		
	}else{

		//Login user
		if(isset($_POST['login'])){
			
			//Cookie validation
			if($_POST['xid'] != "" && $_POST['xid'] == $_COOKIE['xid']){
			
				$user_login = str_replace("'", "&rsquo;", stripslashes($_POST['user_login']));
				$user_password = str_replace("'", "&rsquo;", stripslashes($_POST['user_password']));
				$user_reme = (isset($_POST['user_reme']) ? $_POST['user_reme'] : 0);

				//Attempt login with username or email
				$credentials = array(
					'unique_id' => array('param' => 'username', 'value' => $user_login),
					'unique_id_alt' => array('param' => 'email', 'value' => $user_login),
					'password' => array('param' => 'password', 'value' => $user_password)
				);
				try{
					$Account->login($credentials, $user_reme);
					$Account = new Account();
					if(isset($_GET['redirect'])){
						header('Location:'.urldecode($_GET['redirect']));
						exit();
					}else{
						header('Location:' .$_sitepages['account']['page_url']);
						exit();
					}
				}catch(Exception $e){
					$alert = $Account->alert($e->getMessage(), false);
				}

			}else{
				$alert = $Account->alert('Invalid session. Please make sure cookies are enabled in your browser then try again.', false);
			}
		}
	}
}

?>