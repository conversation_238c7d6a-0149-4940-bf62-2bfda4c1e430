<?php


// if (isset($page['page_panels'][$panel_id])) {
// 	$html = '';

// 	if ($partners) {
// 		foreach ($partners as $partner_id => $partner) {

// 			if ($tabbed) {
// 				$html = '';
// 			}

// 			ob_start();
// 			include('includes/templates/partner-listing.php');
// 			$html .= ob_get_contents();
// 			ob_end_clean();

// 			if ($tabbed) {
// 				$page['page_panels'][$panel_id]['panel_tabs'][$partner['tab_id']]['content'] .= $html;
// 			}
// 		}

// 		// Tabs
// 		if ($tabbed) {
// 			foreach ($categories as $cat_id => $cat) {
// 				$page['page_panels'][$panel_id]['panel_tabs'][$cat['page'].'-'.$cat_id]['content'] = '<div class="partner-listings">'.
// 					$page['page_panels'][$panel_id]['panel_tabs'][$cat['page'].'-'.$cat_id]['content'].
// 				'</div>';
// 			}

// 		// Content (single category)
// 		} else {
// 			$html = '<h2 class="cat-name center">'.$partner['category'].'</h2>
// 			<div class="cat-text">'.$cat['content'].'</div>
// 			<div class="partner-listings">'.$html.'</div>';
			
// 			$page['page_panels'][$panel_id]['content'] .= $html;
// 		}
// 	}
	
// }

// include 'includes/pagepanels.php';

?>


<?php

$html = '';

if (!empty($categories) && !empty($partners)) {
    $html .= '<section id="partners-section" class="partners-container container container-lg">';

    foreach ($categories as $cat_id => $category) {
        $categoryPartners = array_filter($partners, function ($partner) use ($cat_id) {
            return $partner['category_id'] == $cat_id;
        });

        if (count($categoryPartners) > 0) {
            $html .= '<h2 class="partner-category-title container">' . fancy_text(htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8')) . '</h2>';
            $html .= '<div class="partner-category-content container">' . $category['content'] . '</div>';
            $html .= '<div class="partner-grid">';

            foreach ($categoryPartners as $partner) {
                ob_start();
                $partner_id = $partner['partner_id'];
                include('includes/templates/partner-listing.php');
                $html .= ob_get_clean();
            }

            $html .= '</div>';
        }
    }

    $html .= '</section>';
}

if (isset($page['page_panels'][$panel_id])) {
    $page['page_panels'][$panel_id]['append_content'] .= $html;
}

include 'includes/pagepanels.php';
?>
