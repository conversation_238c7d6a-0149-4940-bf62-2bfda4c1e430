<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['settings']){

	//Define vars
	$record_db	 = 'global_settings';
	$record_id	 = 'id';
	$record_name = 'Global website settings';

	//Validation
	$errors   	 = false;
	$required 	 = [];
	$required_fields = ['company_name'];

	$global_emails = [
		// <fieldname> => <section_id> (if appliciable)
		'email_contactform' => ['Contact Form', false],
		'email_leadinform'  => ['Attention Box Form', $_cmssections['leadins']],
		'email_careers'     => ['Career Applications', $_cmssections['careers']],
		'email_jobsubmissions'     => ['Job Submissions', $_cmssections['careers']],
	];

	//Save changes
	if(isset($_POST['save'])){

		//Validate required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				array_push($required, $field);
			}
		}

		if(!$errors){

			//Save to db
			$db->new_transaction();

			//Update global settings
			$params = array(
			$_POST['company_name'],
			$_POST['disclaimer'],
			$_POST['theme'],
			$_POST['email_contactform'],
			$_POST['email_careers'] ?? NULL,
			$_POST['email_jobsubmissions'] ?? NULL, // <--- added here
			$_POST['email_leadinform'] ?? NULL,
			$_POST['smtp_server'] ?? NULL,
			$_POST['smtp_email'] ?? NULL,
			$_POST['smtp_pass'] ?? NULL,
			$_POST['failed_login_attempts'],
			$_POST['failed_login_timeframe'],
			$_POST['lockout_duration'],
			$_POST['rating_badge'] ?? 0,
			$_POST['min_rating_display'] ?? 4,
			$_POST['google_map'] ?? 0,
			$_POST['google_api_key'],
			$_POST['recaptcha_key'],
			$_POST['recaptcha_secret'],
			$_POST['timezone'] ?: NULL,
			$_POST['meta_title'],
			$_POST['meta_description']
			);


					$db->query(
			"UPDATE $record_db SET 
				company_name = ?, 
				disclaimer = ?, 
				theme = ?, 
				email_contactform = ?, 
				email_careers = ?, 
				email_jobsubmissions = ?, 
				email_leadinform = ?, 
				smtp_server = ?, 
				smtp_email = ?, 
				smtp_pass = ?, 
				failed_login_attempts = ?, 
				failed_login_timeframe = ?, 
				lockout_duration = ?, 
				rating_badge = ?, 
				min_rating_display = ?, 
				google_map = ?, 
				google_api_key = ?, 
				recaptcha_key = ?, 
				recaptcha_secret = ?, 
				timezone = ?, 
				meta_title = ?, 
				meta_description = ? 
			WHERE $record_id = 1", 
			$params
		);

			//Update social links
			foreach($global['global_social'] as $key=>$social){
				$db->query("UPDATE global_social SET url = ? WHERE id = ?", array($_POST['social_'.$social['id']], $social['id']));
			}

			//Commit transaction
			if(!$db->error()){
				$db->commit();

				$CMSBuilder->set_system_alert($record_name.' have been updated.', true);
				header('Location: '.PAGE_URL);
				exit();

			//Transaction error
			}else{
				$db->rollback();
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data) {
				$row[$key] = $data;
			}
		}
	}
}

?>