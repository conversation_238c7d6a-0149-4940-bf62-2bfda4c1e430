@charset "utf-8";
/* 
	definitions.less
	Project: Honeycomb CMS v4.0
*/

@path: "../";

@tablet-p: ~"all and (min-width: 481px)";
@tablet-l: ~"all and (min-width: 769px)";
@notebook: ~"all and (min-width: 1025px)";
@desktop: ~"all and (min-width: 1367px)";
@widescreen: ~"all and (min-width: 1921px)";
@max-tablet-p: ~"all and (max-width: 480px)";
@max-tablet-l: ~"all and (max-width: 768px)";
@max-notebook: ~"all and (max-width: 1024px)";
@max-desktop: ~"all and (max-width: 1366px)";
@max-widescreen: ~"all and (max-width: 1920px)";

@font-base: 'Open Sans', sans-serif;
@font-alt: 'Exo', sans-serif;

@color-white: #FFF;
@color-lightest: #EEE;
@color-light: #CCC;
@color-medium: #999999;
@color-dark: #666666; 
@color-darkest: #001E2F; 
@color-black: #111;

@color-gray-lighter: #DDDDDD;

@color-theme1: #53C5EB;
@color-theme2: #003A5E;
@color-theme3: #657788;

@color-grad-in: @color-theme1;
@color-grad-out: @color-theme2;

@color-success: #65B561;
@color-error: #CC0000;
@color-alert: #FFCC00;