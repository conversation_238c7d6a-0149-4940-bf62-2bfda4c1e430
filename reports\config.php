<?php
// $path = "/";
$path = "/pga/";

//System files
include($_SERVER['DOCUMENT_ROOT'].$path."/config/config.php");
include($_SERVER['DOCUMENT_ROOT'].$path."/config/database.php");
include($_SERVER['DOCUMENT_ROOT'].$path."/includes/functions.php");
include($_SERVER['DOCUMENT_ROOT'].$path."/includes/utils.php");

// echo "doc root - ".$_SERVER['DOCUMENT_ROOT']."\n";
// echo "path - ".$_SERVER['PATH_INFO']."\n";


//Instantiate mPDF
require_once($_SERVER['DOCUMENT_ROOT'].$path."includes/plugins/mpdf60/mpdf.php");

$mpdf = new mPDF('utf-8',array(216,279.4),8,'Arial',20,20,16,16,5,7,'P');
$mpdf->SetHTMLFooter('<div style="text-align:center; color:#999;"><em>Professional Golfers&rsquo; Association of Alberta</em></div>');
$mpdf->showImageErrors = false;
$mpdf->SetDisplayMode('fullpage');
$mpdf->list_indent_first_level = 0;

//Logo url for reports
$imagepath = $_SERVER['DOCUMENT_ROOT'].$path."images/";
$logo = $imagepath."ui/pga-logo.png";

//Css styles for reports
$css = array(
	'table' => 'border-collapse:collapse;',
	'th' => 'background:#333; color:#fff;',
	'td' => 'background:#fff;',
	'td-alt' => 'background:#eee;',
	'heading' => 'display:block; margin:0 1px; padding:7px 5px 5px; background:#ef3e34; color:#fff; text-transform:uppercase; text-align:center;'
);

?>