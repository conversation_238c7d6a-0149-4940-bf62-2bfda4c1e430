<?php

$html = '';
	
//Registration alerts
$html .= (isset($alert) ? $alert : '');

//Registration steps
switch(PAGE_ID){

	case $_sitepages['registration']['page_id']:
	// case 49:
		include("pages/registration/register.php");
	break;
	case $_sitepages['reg_cart']['page_id']:
	// case 53:
		include("pages/registration/viewcart.php");
	break;
	case $_sitepages['reg_checkout']['page_id']:
	// case  54:
		include("pages/registration/checkout.php");
	break;
	case $_sitepages['reg_confirm']['page_id']:
	// case 55:
		include("pages/registration/confirm.php");
	break;
	case $_sitepages['reg_success']['page_id']:
	// case 56:
		include("pages/registration/success.php");
	break;
	case $_sitepages['reg_error']['page_id']:
	// case 57:
		include("pages/registration/error.php");
	break;

}

//Page panels
include("includes/pagepanels.php");

?>