<?php  
error_reporting(0);
ini_set('display_errors', 'off');

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../includes/utils.php");

if(isset($_GET) && USER_LOGGED_IN){
	
	// Define vars
	$search_record_db = 'invoice_payments';
	$search_record_id = 'invoice_id';
	// $search_record_name = 'Donation Payments (Processed)';
	$search_record_name = 'Donation Payments';
	$search_records_arr = array();

	$db_columns = array(); // for SELECT in query
	$table_columns = array(); // for listing label
	$alias_columns = array(); // for listing value
	$rearrange_columns = false;

	$search_term = '';
	$start_date =  '';
	$end_date =  '';

	$params = array();
	// $where = " WHERE `status` = true";
	$where = " WHERE ";

	// Set columns to get
	$db_columns = array(
		'invoice_id',
		'donation_purpose',
		'amount',
		'name',
		'email',
		'phone',	
		'address',	
		'address2',	
		'city',
		'province', 	
		'postal_code',
		'country',
		'cc_type',
		'cc_number',
		'cc_expiry',	
		'timestamp',
    'status',
    'approved',
		'response_code',
		'txn_num',
		'txn_tag',
		'auth_code',
		'message',
		'cvd_code',
		'ref_number',
		'comments'
	);
	$table_columns = array(
		'Payment No.',
		'Donation Purpose',
		'Amount',
		'Name',
		'Email',
		'Phone',	
		'Address',	
		'Address 2',	
		'City',
		'Province', 	
		'Postal Code',
		'Country',
		'Card Type',
		'Card Pan',
		'Card Expiry',	
		'Payment Date',
    'Status',
    'Approved',
    'Response Code',
		'Transaction No.',
		'Transaction Tag',
		'Auth Code',
		'Message',
		'CVD Code',
		'REF Number',
		'Comments'
	);

	foreach($db_columns as $key => $column) {
		$alias_columns[$key] = (strpos($column, '.') !== false ? substr($column, (strpos($column, '.')+1)) : $column);
	}

	// Search by keyword
	if(isset($_GET['search']) && $_GET['search'] != '') {
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$params[] = '%' .$_GET['search']. '%';
		$where .= "  (`donation_purpose` LIKE ? OR `invoice_id` LIKE ? OR `name` LIKE ? OR `email` LIKE ? OR `amount` LIKE ?)";
	} else {
		$search_term = false;
	}

	// Filter by dates
	$date_range = '';
	if(isset($_GET['start_date']) && $_GET['start_date'] != '') {
		if($search_term === false ){
			$where .= " `timestamp` >= ?";
		}else{
			$where .= " AND `timestamp` >= ?";
		}
		$params[] = date('Y-m-d 00:00:00', strtotime($_GET['start_date']));
		$date_range .= date('M j, Y', strtotime($_GET['start_date']));
	} else {
		$start_date =  false;
	}
	if(isset($_GET['end_date']) && $_GET['end_date'] != '') {
		if($search_term === false && $start_date === false){
			$where .= "  `timestamp` <= ?";
		} else {
			$where .= " AND `timestamp` <= ?";
		}	
		$params[] = date('Y-m-d 23:59:59', strtotime($_GET['end_date']));
		$date_range .= (!empty($date_range) ? ' - ' : '').date('M j, Y', strtotime($_GET['end_date']));
	} else {
		$end_date =  false;
	}

	if($search_term === false && $start_date === false && $end_date === false){
		$where = ' ';
	}

	// Create query
	$query = $db->query("SELECT * FROM `$search_record_db`" .$where. " ORDER BY `timestamp` DESC", $params);
	if($query && !$db->error() && $db->num_rows() > 0){
		$result = $db->fetch_array();
		foreach($result as $row){
			$search_records_arr[$row[$search_record_id]] = $row;
		}
	}

	/* COMPILE RECORDS */
	$csv_rows = array();
	foreach($search_records_arr as $row) {
		$data = array();
		foreach($alias_columns as $key => $column) {
			
			$row[$column] = str_replace("&rsquo;", "'", $row[$column]);
			
			if($column == 'timestamp') {
				$data[] = ($row[$column] != '' && $row[$column] != '0000-00-00' && $row[$column] != '0000-00-00 00:00:00' ? date('Y-m-d H:i:s', strtotime($row[$column])) : "");
			} else if($column == 'amount') {
				$data[] = number_format($row[$column],2);
			} else {
				$data[] = $row[$column];
			}
		}

		$csv_rows[] = $data;		
	}

	/* OUTPUT CSV */
	header("Pragma: public");
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
	header("Content-Type: application/force-download");
	header("Content-Type: text/csv");
	header("Content-Type: application/octet-stream");
	header("Content-Type: application/download");;
	header("Content-Disposition: attachment;filename=donations-".date("Ymd-His").".csv");
	header("Content-Transfer-Encoding: binary ");

	$fp = fopen('php://output', 'w');
	
	// Headers
	$headers = array($search_record_name);
	fputcsv($fp, $headers);
	$headers = array('Date Exported: '.date('M j, Y'));
	fputcsv($fp, $headers);
	if(!empty($date_range)){
		$headers = array('Date Range: '.$date_range);
		fputcsv($fp, $headers);
	}
	if(isset($_GET['search']) && !empty($_GET['search'])){
		$headers = array('Search Term: '.$_GET['search']);
		fputcsv($fp, $headers);
	}
	fputcsv($fp, array(''));	
	
	fputcsv($fp, $table_columns);
	foreach($csv_rows as $row) {
		fputcsv($fp, $row);
	}
	fclose($fp);

} // END loggedin and posted fields
?>
