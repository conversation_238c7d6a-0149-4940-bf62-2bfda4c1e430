<?php

/**
 * Deals with like functionality associated with forms. Cannot be instantiated.
 * Future changes: Auto Detect crop size changes - eg; start a crop when swapping from content to landing page
 * 				   Fit images to a box (as in, both a height and a width instead of one or the other).  Useful for things like logos, products, etc.
 *
 * <AUTHOR> Army <<EMAIL>>
 * @date	06-17-21
 * @file	CMSUploader.class.php
 */
class CMSUploader{

	/*-----------------------------------/
	* @var _croptypes
	* Array of all cropping type arrays, organized by crop type and image fieldname
	*/
	private $_croptypes;

	/*-----------------------------------/
	* @var crop_type
	* Cropp type array organized by image fieldname
	*/
	private $crop_type = false;

	/*-----------------------------------/
	* @var crop_type_key
	* Key of cropping type array
	*/
	private $crop_type_key = '';

	/*-----------------------------------/
	* @var crop_type_key
	* Array of cropping parameters for a specific image field of a specific crop type
	*/
	private $crop_params;

	/*-----------------------------------/
	* @var crop_queue
	* Array of images uploaded and waiting to be cropped
	*/
	private $crop_queue = [];

	/*-----------------------------------/
	* @var position_queue
	* Array of images cropped and waiting to be positioned
	*/
	private $position_queue = [];

	/*-----------------------------------/
	* @var imagedirs
	* Array of upload folders for each image field found in the $crop_type
	*/
	private $imagedirs = [];


	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @param	$crop_type_key	Key of crop types array
	* @param	$imagedir		Global upload directory (optional)
	* @return	CMSUploader		New CMSUploader object
	* @throws	Exception
	*/
	public function __construct(string $crop_type_key = '', string $imagedir = '') {
		global $_croptypes;

		// Requires ImageUpload instance
		if(class_exists('ImageUpload')) {
			$this->imageUpload = new ImageUpload();
		} else {
			throw new Exception('Cannot instantiate CMSUploader: missing ImageUpload class');
		}

		// Set class parameters
		$this->_croptypes = &$_croptypes;
		if ($crop_type_key) {
			$this->set_crop_type($crop_type_key, $imagedir);
		}
    }


	/*-----------------------------------/
	* Validates and sets the crop key
	*
	* <AUTHOR> Army
	* @param	$crop_type_key		Name of crop type
	*/
	public function set_crop_type(string $crop_type_key, string $imagedir = '') {
		$crop_type = $this->_croptypes[$crop_type_key] ?? [];

		$this->crop_type_key = $crop_type_key;
		$this->crop_type     = $crop_type;

		// Validate
		if (empty($crop_type)) {
			trigger_error('Error setting crop type: "'.$crop_type_key.'" crop type does not exist.');

		} else {
			// Set imagedir for all fields if provided
			if ($imagedir) {
				foreach ($this->crop_type as $fieldname => $crop_params) {
					$this->set_imagedir($fieldname, $imagedir);
				}
			}
		}
	}


	/*-----------------------------------/
	* Validates and sets the image directory for a specific field
	*
	* <AUTHOR> Army
	* @param	$fieldname		Name of image field
	* @param	$imagedir		Relative path to upload directory
	*/
	public function set_imagedir(string $fieldname, string $imagedir) {

		// Validate
		if (empty($this->crop_type)) {
			trigger_error('Cannot set image directory, missing crop type.', E_USER_WARNING);
		} else if (empty($this->crop_type[$fieldname])) {
			trigger_error('Cannot set image directory, no params found for "'.$this->crop_type_key.'['.$fieldname.']"', E_USER_WARNING);
		} else if (!is_dir($imagedir)) {
			trigger_error('Cannot set image directory, "'.$imagedir.'" does not exist or is not a directory', E_USER_WARNING);
		}

		$this->imagedirs[$fieldname] = $imagedir;
	}


	/*-----------------------------------/
	* Set an array of crop data for a specific image field
	*
	* <AUTHOR> Army
	* @param	$fieldname		Name of image in $_FILES array
	* @return	Array of crop size parameters
	* @throws	Exception
	*/
	private function set_crop_params(string $fieldname) {
		$crop_params = $this->crop_type[$fieldname] ?? false;

		// Validate
		if (!$crop_params) {
			throw new Exception('Cannot set crop parameters for image, no params found for "'.$this->crop_type_key.'['.$fieldname.']".');
		}

		$this->crop_params = $crop_params;
	}


	/*-----------------------------------/
	* Return largest width and height for any cropsize for a specific image field
	* Can be used without instantiating the class
	*
	* <AUTHOR> Army
	* @param	$crop_type_key		Key of $_croptypes to search in
	* @param	$fieldname			Name of image in $_FILES array
	* @return	Width and height of largest crop dimensions in an unindexed array
	*/
	public static function max_size($crop_type_key, $fieldname) {
		global $_croptypes;

		$crop_params = $_croptypes[$crop_type_key][$fieldname] ?? false;
		$max_W = $max_H = 0;

		// Validate
		if (!$crop_params) {
			trigger_error('Cannot determine max size, no params found for '.$crop_type_key.'['.$fieldname.'].');

		} else {
			// Loop through directories
			foreach ($crop_params as $size) {
				$max_W = max($size['width'] ?: 0, $max_W);
				$max_H = max($size['height'] ?: 0, $max_H);
			}
		}

		// Return width, height
		return [$max_W, $max_H];
	}


	/*-----------------------------------/
	* Display image dimensions in a readable format.  Pass in two numbers,
	* an array of dimensions, or a crop type key and fieldname for auto-detection.
	* Can be used without instantiating the class
	*
	* <AUTHOR> Army
	* @param	$w		Pixel Width | Crop Type Key | Dimensional Array
	* @param	$h		Pixel height | Image Fieldname
	* @return	Label generated based on provided dimensions or parameters
	*/
	public static function size_label($w, $h = '') {

		// Collect into array
		$size = [$w, $h];

		// Dimensions passed as array
		if (is_array($w)) {
			$size = [$w[0], $w[1]];

		// Find max dimensions from crop type key and image fieldname
		} else if (is_string($w) && is_string($h)) {
			$size = self::max_size($w, $h);
		}

		// Set vars
		[$w, $h] = $size;

		// Validate variables are numeric or falsey
		if ($w && !is_numeric($w) || $h && !is_numeric($h)) {
			trigger_error('Cannot generate size label: Width and height are not valid dimensions or crop parameters');
		}

		return $w && $h ? ($w.'x'.$h) : ($w ? $w.'px wide' : $h.'px tall');
	}


	/*-----------------------------------/
	* Generates a cropping label based on the name of the directroy
	* Can be used without instantiating the class
	*
	* <AUTHOR> Army
	* @param	$dir		Name of directory
	* @return	Label generated based on directory name
	*/
	public static function smart_label($dir) {
		$labels = [
			'thumbs'   => 'Thumbnail',
			'featured' => 'Featured Image'
		];

		return $labels[trim($dir, '/')] ?? ucwords(trim(preg_replace('/\W/', ' ', $dir))) ?: 'Crop Image';
	}


	/*-----------------------------------/
	* Calculates the coordinates and dimensions of a box that has been fit to and centered within another box
	* Can be used without instantiating the class
	*
	* <AUTHOR> Army
	* @param	$box		Array of width and height of container.  Can also be a path to an image.
	* @param	$resize		Array of width and height of box to be resized and centered.
	* @return	Array of coordinates and dimensions of new fitted box.
	*/
	public static function crop_coords($box, array $resize) {

		$coords 				= ['x1' => 0, 'y1' => 0, 'x2' => 0, 'y2' => 0];
		[$box_w, $box_h]        = is_string($box) && $box ? getimagesize($box) : $box;
		[$resize_w, $resize_h]  = $resize;

		if ($box_w && !is_numeric($box_w) || $box_h && !is_numeric($box_h)) {
			trigger_error('Cannot calculate cropping coordinates: Box width/height are not valid dimensions or filename does not point to an image.');

		} else if ($resize_w && !is_numeric($resize_w) || $resize_h && !is_numeric($resize_h)) {
			trigger_error('Cannot calculate cropping coordinates: Resize width/height are not valid dimensions.');

		} else {

			if($resize_w > $box_h) {
				$new_h = $resize_h * ($box_w / $resize_w);
				$new_w = $box_w;

				if($new_h > $box_h) {
					$new_w = $new_w * ($box_h / $new_h);
					$new_h = $box_h;
				}
			} else {
				$new_w = $resize_w * ($box_h / $resize_h);
				$new_h = $box_h;

				if($new_w > $box_w) {
					$new_h = $new_h * ($box_w / $new_w);
					$new_w = $box_w;
				}
			}

			$x = abs($new_w - $box_w) / 2;
			$y = abs($new_h - $box_h) / 2;
			$coords['x1'] = $x;
			$coords['y1'] = $y;
			$coords['x2'] = $x + $new_w;
			$coords['y2'] = $y + $new_h;

		}

		return $coords;
	}


	/*-----------------------------------/
	* Loops through all fieldnames of a crop type, and uploads images found in $_FILES with the same name.
	* This uploads an image for all cropping directories at once.
	*
	* <AUTHOR> Army
	* @param	$filename			Filename of uploaded image
	* @param	$field_filenames	Array of filenames to remove with the image fields they belong to as keys
	*                        	    e.g. [
	*                        	    	'image'  => 'old_image.jpg', 	<-- Will be marked for deletion
	*                        	    	'avatar' => 'old_avatar.png', 	<-- Will be marked for deletion
	*                        	    	'banner' => NULL, 				<-- Empty values are ignored
	*                        	    	'panel_title' => 'Panel Title' 	<-- Key not found in $this->crop_type, will be ignored
	*                        	    ]
	* @return	Array of uploaded images with the fieldname as the key
	* @throws	Exception
	*/
	public function bulk_upload(string $filename, array $field_filenames = []) {

		// Validate
		if (empty($this->crop_type)) {
			throw new Exception('Cannot upload images, missing crop type.');
		} else {
			foreach ($this->crop_type as $fieldname => $crop_params) {
				if (empty($this->imagedirs[$fieldname])) {
					throw new Exception('Cannot upload images, directories are not set for some fields of "'.$this->crop_type_key.'".');
				}
			}
		}

		$uploaded = [];
		$i = 0;

		foreach ($this->crop_type as $fieldname => $crop_params) {
			$i++;

			// Ensure filename is unique by adding iterator to filename when uploading multiple images
			$post = count($this->crop_type) > 1 ? '-'.$i : '';

			// Upload new images
			if (!empty($_FILES[$fieldname]['name'])) {
				$uploaded[$fieldname] = $this->upload_image($fieldname, $filename.$post, $field_filenames[$fieldname] ?? '');

			// Return existing image data or NULL
			} else {
				$imagedir = $this->imagedirs[$fieldname] ?? false;
				$field_filename = $field_filenames[$fieldname] ?? NULL ?: NULL;

				if ($imagedir && check_file($field_filename, $imagedir)) {
					$uploaded[$fieldname] = $field_filename;
				} else {
					$uploaded[$fieldname] = NULL;
				}
			}
		}

		return $uploaded;
	}


	/*-----------------------------------/
	* Uploads a single image to root and cropping directories and removes old versions
	*
	* <AUTHOR> Army
	* @param	$fieldname		The field name of the image in the _FILES array
	* @param	$image_name		String that will be sanitized and prepended onto filename
	* @param	$old_image		Name of previously uploaded image to delete.  Required if $use_existing is true
	* @param	$use_existing	FALSE (default) to pull image data from _FILES array, TRUE to use existing image at $imagedir
	* @returns	Filename of uploaded image or NULL
	* @throws	Exception
	*/
	public function upload_image(string $fieldname, string $image_name = '', string $old_image = '', bool $use_existing = false) {

		$imagedir = $this->imagedirs[$fieldname] ?? false;

		// Validate
		if (empty($this->crop_type)) {
			throw new Exception('Cannot upload image, invalid crop type "'.$this->crop_type_key.'".');
		} else if (!$imagedir) {
			throw new Exception('Cannot upload image, please set an image directory for "'.$fieldname.'".');
		} else if (!$use_existing && empty($_FILES[$fieldname]['name'])) {
			throw new Exception('Cannot upload image, no file data found for index "'.$fieldname.'".');
		} else if ($use_existing && !$old_image) {
			throw new Exception('Cannot recrop existing image, please provide current image name in $old_image.');
		} else if ($use_existing && !check_file($old_image, $imagedir)) {
			throw new Exception('Cannot recrop existing image, "'.$old_image.'" does not exist in "'.$imagedir.'".');
		}

		// Set cropping parameters based on image field
		$this->set_crop_params($fieldname);

		// Sanitize user input and generate unique image name. Use existing image or pull from _FILES
		$image_name = $image_name ? clean_url($image_name) : '';
		$ext        = pathinfo($use_existing ? $old_image : $_FILES[$fieldname]['name'], PATHINFO_EXTENSION);
		$newname    = ($image_name ? $image_name.'-' : $image_name).date("ymdhis").'.'.$ext;
		$filepath   = $use_existing ? $imagedir.$old_image : $_FILES[$fieldname]['tmp_name'];

		// Shrink image to fill box if it's larger than the max crop dimensions (min 1920x1920)
		[$max_W, $max_H] = self::max_size($this->crop_type_key, $fieldname);
		$max_W = max($max_W, 1920);
		$max_H = max($max_H, 1920);

		// Upload fresh image to directory root & delete old image
		$this->imageUpload->load($filepath);
		$this->imageUpload->smartFill($max_W, $max_H);
		$this->imageUpload->save($imagedir, $newname);
		$this->delete_image($fieldname, $old_image);

		// Validate image exists and loop through cropping directiories
		if ($image = check_file($newname, $imagedir)) {
			foreach ($this->crop_params as $size) {

				// Re-point imageUploader to root image
				$this->imageUpload->load($imagedir.$image);

				// Smartcrop image (before saving)
				if (!empty($size['width']) && !empty($size['height'])) {
					$this->imageUpload->smartCrop($size['width'], $size['height']);

				// Dimensions are flexible
				} else if (!empty($size['width'])) {
					$this->imageUpload->resizeToWidth($size['width']);
				} else if (!empty($size['height'])) {
					$this->imageUpload->resizeToHeight($size['height']);
				}

				// Upload image to cropping directory and delete old image
				$this->imageUpload->save($imagedir.$size['dir'], $newname);
				$this->delete_image($fieldname, $old_image, $size['dir']);

				// Manually crop image (after saving)
				if (empty($size['auto'])) {
					$size['label']    = $size['label'] ?? self::smart_label($size['dir']);
					$size['root_dir'] = $imagedir;
					$size['dir']      = $imagedir.$size['dir'];
					$size['src']      = $image;

					// Add to queue
					$this->crop_queue[] = $size;
				}
			}
		}

		return $image ?: NULL;
	}


	/*-----------------------------------/
	* Delete multiple image field's images from the root folder and cropping directories
	* Invalid key-value pairs in $field_filenames will be ignored and will not throw an error.
	* e.g. keys found in $field_filenames but not $this->crop_type, or vice versa; empty values
	*
	* <AUTHOR> Army
	* @param	$field_filenames	Array of filenames to remove with the image fields they belong to as keys
	*                        	    e.g. See bulk_upload
	* @return	Array of deleted images with the fieldname as the key, or FALSE if error occurred
	*/
	public function bulk_delete(array $field_filenames) {

		// Validate
		if (empty($this->crop_type)) {
			trigger_error('Cannot delete images, missing crop type.', E_USER_WARNING);
			return false;

		} else {
			foreach ($this->crop_type as $fieldname => $crop_params) {
				if (empty($this->imagedirs[$fieldname])) {
					trigger_error('Cannot delete images, directories are not set for some fields of "'.$this->crop_type_key.'".', E_USER_WARNING);
					return false;
				}
			}
		}

		$deleted = [];

		// Both arrays share at least 1 key with value
		if (array_filter(array_intersect_key($field_filenames, $this->crop_type))) {

			// Loop through image fields of $this->crop_type
			foreach ($this->crop_type as $fieldname => $crop_params) {
				if ($filename = $field_filenames[$fieldname] ?? false) {

					// Root image
					if($this->delete_image($fieldname, $filename)) {
						$deleted[$fieldname] = $filename;
					}

					// Cropping directories
					foreach ($crop_params as $size) {
						$this->delete_image($fieldname, $filename, $size['dir']);
					}
				}
			}
		}

		return $deleted;
	}


	/*-----------------------------------/
	* Delete an image
	*
	* WARNING: This function may return FALSE, but may also return a non-Boolean value
	* which evaluates to FALSE.  Use the === operator for comparisons.
	*
	* <AUTHOR> Army
	* @param	$fieldname		Name of image field file belongs to
	* @param	$filename		Name of file to remove
	* @param	$cropdir		Subfolder of image directory to remove from
	* @returns	TRUE on success, FALSE on fail, NULL if file does not exist or filename is empty
	*/
	public function delete_image(string $fieldname, string $filename, string $cropdir = '') {

		$imagedir = trim($this->imagedirs[$fieldname].$cropdir);

		// Validate
		if (empty($this->imagedirs[$fieldname])) {
			trigger_error('Cannot delete image, please set an image directory for "'.$fieldname.'".', E_USER_WARNING);

		} else if (!is_dir($imagedir)) {
			trigger_error('Cannot delete image, directory "'.$imagedir.$cropdir.'" does not exist.', E_USER_WARNING);

		} else {
			// Remove image if it exists
			return check_file($filename, $imagedir) ? unlink($imagedir.$filename) : NULL;
		}

		return false;
	}


	/*-----------------------------------/
	* Sets position parameters for multiple field's images at once.  Invalid key-value pairs in $field_filenames
	* will be ignored and will not throw an error.
	* e.g. keys found in $field_filenames but not $this->crop_type, or vice versa; empty values
	*
	* <AUTHOR> Army
	* @param	$field_filenames	Array of filenames to position, with the image fieldnames they belong to as keys
	*                        	    e.g. See bulk_upload
	*/
	public function set_position_params(array $field_filenames) {

		// Validate
		if (empty($this->crop_type)) {
			trigger_error('Cannot set position parameters, missing crop type.', E_USER_WARNING);

		} else {

			foreach ($this->crop_type as $fieldname => $crop_params) {
				$imagedir = $this->imagedirs[$fieldname] ?? false;
				$filename = $field_filenames[$fieldname] ?? false;

				// Validate
				if (!$imagedir) {
					trigger_error('Cannot set position parameters, please set an image directory for "'.$fieldname.'".', E_USER_WARNING);
				} else if (!isset($_POST['imgdir'])) {
					trigger_error('Cannot set position parameters, $_POST[\'imgdir\'] does not exist.', E_USER_WARNING);
				} else if (!isset($_POST['imgsrc'])) {
					trigger_error('Cannot set position parameters, $_POST[\'imgsrc\'] does not exist.', E_USER_WARNING);
				} else {

					// Add position parameters if enabled, and file+directory is valid
					if (in_array($filename, $_POST['imgsrc'])) {
						foreach ($crop_params as $size) {
							if (!empty($size['position']) && in_array($imagedir.$size['dir'], $_POST['imgdir'])) {
								$this->position_queue[] = [
									'name'  => $size['position'],
									'img'   => $imagedir.$size['dir'].$filename,
									'label' => $size['position_label'] ?? 'Background Positioning'
								];
							}
						}
					}
				}
			}
		}
	}


	/*-----------------------------------/
	* Return array of uploaded images waiting to be cropped
	*
	* <AUTHOR> Army
	* @return	Array of uploaded images
	*/
	public function crop_queue() {
		return $this->crop_queue ?? false;
	}


	/*-----------------------------------/
	* Return array of cropped images waiting to be positioned
	*
	* <AUTHOR> Army
	* @return	Array of cropped images
	*/
	public function position_queue() {
		return $this->position_queue ?? false;
	}


	/*-----------------------------------/
	* Manually add an array to the crop queue.  Only use for otherwise impossible operations.
	* See /sitecms/includes/cropsizes.php for valid indices of $size
	*
	* <AUTHOR> Army
	*/
	public function add_to_crop_queue(array $size) {
		$required = ['dir', 'width', 'height'];
		foreach ($required as $index) {
			if (isset($size[$index])) {
				$this->crop_queue[] = $size;
			} else {
				trigger_error('Cannot add to crop queue, $size requires the following indicies: '.implode(', ', $required).'.', E_USER_WARNING);
			}
		}
	}


	/*-----------------------------------/
	* Manually add an array to the position queue.  Only use for otherwise impossible operations.
	*
	* <AUTHOR> Army
	* @param	$position	Array of parameters for positioning the cropped image.
	*                       	e.g. [
	*                       		'img'   => 'path/to/image.jpg', <-- Path to image to be positioned, relative to $root
	*                       		'name'  => 'image_position', 	<-- Field in DB to be set
	*                       		'label' => 'Position Image', 	<-- Optional label.  Empty string for no label, default value if undefined.
	*                       	]
	*/
	public function add_to_position_queue(array $position) {
		$required = ['name', 'img'];
		foreach ($required as $index) {
			if (isset($position[$index])) {
				$position['label'] = $position['label'] ?? 'Background Positioning';
				$this->crop_queue[] = $position;
			} else {
				trigger_error('Cannot add to position queue, $position requires the following indicies: '.implode(', ', $required).'.', E_USER_WARNING);
			}
		}
	}
}

?>