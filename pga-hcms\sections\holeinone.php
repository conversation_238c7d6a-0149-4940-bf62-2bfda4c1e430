<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	//Search
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Search Submissions
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";

		echo "<div class='panel-content clearfix'>";
			echo "<form id='advanced-search-form' class='clearfix' action='' method='get' enctype='multipart/form-data'>";
				echo "<div id='search-fields' class='column clearfix'>";
					echo "<div class='form-field'>
						<label>Search All</label>
						<input type='text' name='search' value='".$searchterm."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Start Date </label>
						<input type='text' name='start_date' value='".(isset($_SESSION['search_start_date'][SECTION_ID]) ? $_SESSION['search_start_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";

					echo "<div class='form-field'>
						<label>End Date </label>
						<input type='text' name='end_date' value='".(isset($_SESSION['search_end_date'][SECTION_ID]) ? $_SESSION['search_end_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";
				echo "</div>";

				echo "<div class='buttons-wrapper'>";
					echo "<div class='f_right'>";
						echo "<button type='button' class='button' onclick='exportForm(this.form);'><i class='fa fa-download'></i>Export</button> &nbsp;";
						echo "<button type='submit' class='button'><i class='fa fa-search'></i>Search</button>";
					echo "</div>";
					echo "<button type='button' class='button reset' onclick='document.getElementById(\"clear-search-form\").submit();'><i class='fa fa-times'></i>Clear</button>";
				echo "</div>";
	
			echo "</form>";
			echo "<form id='clear-search-form' name='clear-search-form' class='hidden' action='".PAGE_URL."' method='post'>
				<input type='hidden' name='clear-search' value='Clear' />
				<input type='hidden' name='search' value='' />
				<input type='hidden' name='start_date' value='' />
				<input type='hidden' name='end_date' value='' />
				<input type='hidden' name='xssid' value='".$_COOKIE['xssid']."' />
			</form>";
		echo "</div>";
	echo "</div>";

	//Export Form
	echo "<script>
		function exportForm(this_form) {
			this_form.target=\"_blank\"; 
			this_form.action=\"".$path."exports/export-holeinone.php\"; 
			this_form.submit(); 
			this_form.target=\"\"; 
			this_form.action=\"\";
		}
	</script>";

	//Display records
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Submissions
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

			echo "<thead>";
			echo "<th width='200px'>Member</th>";
			echo "<th width='auto'>Facility</th>";
			echo "<th width='auto'>Event Name</th>";
			echo "<th width='200px' class='{sorter:\"monthDayYear\"}'>Event Date</th>";
			echo "<th width='200px' class='{sorter:\"monthDayYear\"}'>Date Submitted</th>";
			echo "<th width='70px'>Approved</th>";
			echo "<th width='150px' class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";

			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .(!empty($row['account_id']) ? $row['first_name']." ".$row['last_name'] : "Unknown"). "</td>";
					echo "<td>" .$row['facility_name']. "</td>";	
					echo "<td>" .$row['event_name']. "</td>";
					echo "<td>" .format_date_range($row['start_date'], $row['end_date'], 'M'). "</td>";
					echo "<td>" .date('M j, Y', strtotime($row['date_added'])). "</td>";
					echo "<td>" .($row['approved'] == '1' ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo "</div>";	
	echo "</div>";
	

//Display form	
}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}
	}
	
	if($row['approved']){
		echo $CMSBuilder->important('If you update the total premium, the invoice total will also be updated and a notification email will be sent.');
	}else{
		echo $CMSBuilder->important($record_name.' is currently pending and will be automatically approved upon receipt of payment.');
	}
	
	echo "<form id='hio-form' action='' method='post' enctype='multipart/form-data'>";
	
	//Event
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Event Information
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>";
			/*if(!isset($row['approved']) || !$row['approved']){
				echo "<div class='panel-switch'>
					<label>Approve &amp; Invoice " .$CMSBuilder->tooltip('Approve &amp; Invoice', 'Once the event is approved, an invoice will be automatically generated for the total premium amount and notification will be sent.'). "</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='approved' id='approved' value='1'" .(isset($row['approved']) && $row['approved'] ? " checked" : ""). " />
						<label for='approved'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>";
			}*/
		echo "</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
				echo "<tr>
					<td width='150px'>Date Submitted:</td>
					<td>" .date('M j, Y g:iA', strtotime($row['date_added'])). "</td>
				</tr>
				<tr>
					<td>Submitted By:</td>
					<td>" .(!empty($row['account_id']) ? $row['first_name']." ".$row['last_name'] : "Unknown"). "</td>
				</tr>";
				if($row['approved']){
					echo "<tr>
						<td>Date Approved:</td>
						<td>" .date('M j, Y g:iA', strtotime($row['date_approved'])). "</td>
					</tr>";
					try{
						$approved_by = $Account->get_account_profile($row['approved_by']);
						echo "<tr>
							<td>Approved By:</td>
							<td>" .$approved_by['first_name']. " " .$approved_by['last_name']. "</td>
						</tr>";
					}catch(Exception $e){
					}
				}
				if(!empty($row['invoice_id'])){
					echo "<tr>
						<td>Invoice No:</td>
						<td><a href='" .$sitemap[80]['page_url']."?action=edit&item_id=".$row['invoice_id']. "'>" .$row['invoice_number']. "</a></td>
					</tr>";
				}
				if(!empty($row['account_id'])){
					echo "<tr>
						<td>Account No:</td>
						<td><a href='" .$sitemap[7]['page_url']. "?action=edit&item_id=" .$row['account_id']. "'>" .$row['account_id']."</a></td>
					</tr>";
				}
				echo "<tr>
					<td>Facility Name:</td>
					<td>" .$row['facility_name']. "</td>
				</tr>
				<tr>
					<td>Event Name:</td>
					<td><input type='text' name='event_name' class='input nomargin" .(in_array('event_name', $required) ? ' required' : ''). "' value='" .(isset($_POST['event_name']) ? $_POST['event_name'] : $row['event_name']). "' /></td>
				</tr>
				<tr>
					<td>Event Field:</td>
					<td><input type='text' name='field' class='input input_sm number nomargin" .(in_array('field', $required) ? ' required' : ''). "' value='" .(isset($_POST['field']) ? $_POST['field'] : $row['field']). "' /> Golfers</td>
				</tr>";
			echo "</table>";
		echo "</div>";
	echo "</div>"; //Event
	
	//Dates
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Event Dates
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			if(!empty($row['event_dates'])){	
				foreach($row['event_dates'] as $date){
					echo "<div class='form-field'>
						<input type='text' name='event_dates[]' class='input datepicker' value='" .date('Y-m-d', strtotime($date)). "' autocomplete='off' />
					</div>";	
				}
			}else{
				echo "<div class='form-field'>
					<input type='text' name='event_dates[]' class='input datepicker required' value='' autocomplete='off' />
				</div>";	
			}
		echo "</div>
	</div>";
	
	//Courses
	foreach($row['courses'] as $course){
		echo "<div class='panel'>";
			echo "<div class='panel-header'>" .$course['course_name']. "
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
					echo "<thead>
						<tr>
							<th width='150px'>Hole</th>
							<th width='150px'>Men</th>
							<th width='150px'>Women</th>
							<th class='right'>Prize Amount</th>
							<th width='150px' class='right'>Premium Per Date</th>
							<th width='20px'><i class='fa fa-trash fa-lg'></i></th>
						</tr>
					</thead>
					<tbody>";
		
					//Holes
					foreach($course['holes'] as $hole){
						$id = $hole['hole_id'];
						$holenum = (isset($_POST['hole'][$id]) ? $_POST['hole'][$id] : $hole['hole']);
						$yards_men = (isset($_POST['yards_men'][$id]) ? $_POST['yards_men'][$id] : $hole['yards_men']);
						$yards_women = (isset($_POST['yards_women'][$id]) ? $_POST['yards_women'][$id] : $hole['yards_women']);
						$prize = (isset($_POST['prize'][$id]) ? $_POST['prize'][$id] : $hole['prize']);
						$premium = (isset($_POST['premium'][$id]) ? $_POST['premium'][$id] : $hole['premium']);
						echo "<tr>
							<td>
								<input type='text' name='hole[".$id."]' class='input input_xs number nomargin" .(in_array('hole'.$id, $required) ? ' required' : ''). "' value='" .$holenum. "' />
							</td>
							<td>
								<input type='text' name='yards_men[".$id."]' class='input input_xs number nomargin" .(in_array('yards_men'.$id, $required) ? ' required' : ''). "' value='" .$yards_men. "' /> Yards
							</td>
							<td>
								<input type='text' name='yards_women[".$id."]' class='input input_xs number nomargin" .(in_array('yards_women'.$id, $required) ? ' required' : ''). "' value='" .$yards_women. "' /> Yards
							</td>
							<td align='right'>
								$<input type='text' name='prize[".$id."]' class='input input_sm number nomargin prize" .(in_array('prize'.$id, $required) ? ' required' : ''). "' value='" .$prize. "' />
							</td>
							<td align='right'>
								$<input type='text' name='premium[".$id."]' class='input input_sm number nomargin premium" .(in_array('premium'.$id, $required) ? ' required' : ''). "' value='" .$premium. "' />
							</td>
							<td>
								<input type='checkbox' name='delete_holes[]' class='checkbox calc' id='deletehole-".$id."' value='".$id."' />
								<label for='deletehole-".$id."'></label>
							</td>
						</tr>";
					}
					echo "</tbody>
				</table>";
			echo "</div>";
		echo "</div>"; //Personal
	}
	
	//Totals
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Event Totals
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
				echo "<tr>
					<td align='right'>Total Prize Amount:</td>
					<td align='right' id='prize_total'>$" .number_format($row['prize_total'], 2). "</td>
				</tr>
				<tr>
					<td align='right'>Premium Per Date:</td>
					<td align='right' id='premium_per_date'>$" .number_format($row['premium_total']/count($row['event_dates']), 2). "</td>
				</tr>
				<tr>
					<td align='right'><strong>Total Premium:</strong></td>
					<td align='right' width='150px'><strong id='premium_total'>$" .number_format($row['premium_total'], 2). "</strong></td>
				</tr>";
			echo "</table>";
		echo "</div>";
	echo "</div>";
	
	//Comments
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Comments
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			echo "<div class='form-field'>
				<textarea name='comments' class='textarea'>" .(isset($_POST['comments']) ? $_POST['comments'] : $row['comments']). "</textarea>
			</div>";
		echo "</div>";
	echo "</div>";
		
	//Sticky footer
	echo "<footer id='cms-footer' class='resize'>";
		echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
		echo "<button type='button' name='delete' value='delete' class='button delete'><i class='fa fa-trash'></i>Delete</button>";
		echo "<a href='" .PAGE_URL. "' class='cancel'>Cancel</a>";
	echo "</footer>";
	
	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "</form>";

?>

<script type="text/javascript">
	
//Strip all non-numeritical characters and convert to a number
function numberfy(val){
	val = val || "0";
	return parseFloat(val.replace(/[^0-9.]/g,'')) || 0;
}

//Format number to 2 decimal points and add a dollar sign
function monify(val){
	return "$" + parseFloat(val).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
}

//Hole in one values
$('#hio-form').on('input blur', 'input, select', function(){
	
	if($(this).hasClass('checkbox')){
		if($(this).is(':checked')){
	 		$(this).parents('tr').find('td .input').attr('disabled', 'disabled');
		}else{
			$(this).parents('tr').find('td .input').removeAttr('disabled');
		}
	};
	
	var prizeTotal = 0;
	var premiumPerDate = 0;
	var premiumTotal = 0;
	var totalDates = 0;
	
	$('input.datepicker').each(function(){
		if($(this).val() != "" && !$(this).is(':disabled')){
			totalDates++;
		}
	});
	
	$('input.prize').each(function(){
		if($(this).val() != "" && !$(this).is(':disabled')){
			prizeTotal = prizeTotal+numberfy($(this).val());
		}
	});
	
	$('input.premium').each(function(){
		if($(this).val() != "" && !$(this).is(':disabled')){
			premiumPerDate = premiumPerDate+numberfy($(this).val());
		}
	});
	
	premiumTotal = parseFloat(premiumPerDate*totalDates);
	
	$('#prize_total').html(monify(prizeTotal));
	$('#premium_per_date').html(monify(premiumPerDate));
	$('#premium_total').html(monify(premiumTotal));
});
</script>

<?php } ?>