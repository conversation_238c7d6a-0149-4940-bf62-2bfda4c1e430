<?php

//Search page
if(PAGE_ID == $_sitepages['search']['page_id']){
	
	//Define vars
	$panel_id = 143;
	$searchterm = (isset($_GET['search']) ? urldecode(trim($_GET['search'])) : '');
	$searchresults = array();
	$pg = (isset($_GET['pg']) ? $_GET['pg'] : 1);
	$limit = 20;
	
	if($searchterm != '' && strlen($searchterm) >= 3){
		
		//Facilities
		$params = array(
			'%'.$searchterm.'%', 
			'%'.$searchterm.'%', 
			'%'.$searchterm.'%', 
		);
		$query = $db->query("SELECT `facility_name`, `page`, `facility_id`, `region`, `city`, `province` FROM `facilities` WHERE `showhide` = 0 && (`facility_name` LIKE ? || `city` LIKE ? || `province` LIKE ?)", $params);
		if($query && !$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$searchresults = array_merge($searchresults, $result);
		}
		
		//Directory
		$params = array('Active', ' ',
			'%'.$searchterm.'%',
			'%'.$searchterm.'%',
		);
		$query = $db->query("SELECT `account_profiles`.`first_name`, `account_profiles`.`last_name`, `account_profiles`.`title`, `account_profiles`.`profile_id`, `membership_classes`.`class_name`, `facilities`.`facility_name`, `account_permissions`.`role_id` ".
		"FROM `account_profiles` ".
		"LEFT JOIN `accounts` ON `account_profiles`.`account_id` = `accounts`.`account_id` ".
		"LEFT JOIN `account_permissions` ON `account_profiles`.`account_id` = `account_permissions`.`account_id` ".
		"LEFT JOIN `membership_classes` ON `account_profiles`.`class_id` = `membership_classes`.`class_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"WHERE `accounts`.`status` = ? && `accounts`.`showhide` = 0 && (`account_permissions`.`role_id` = 2 || `account_permissions`.`role_id` = 6) && (CONCAT(`first_name`, ?, `last_name`) LIKE ? || `class_name` LIKE ?)", $params);
		if($query && !$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$searchresults = array_merge($searchresults, $result);
		}
		
		//Tournaments & Events
		$result = $Registration->get_occurrences(array('Open'), NULL, NULL, NULL, NULL, $searchterm, '', 'reg_occurrences.start_date DESC, reg_occurrences.end_date DESC');
		$searchresults = array_merge($searchresults, $result);
		
		//Articles
		$params = array(
			'%'.$searchterm.'%'
		);
		$query = $db->query("SELECT `newsletters`.`title`, `newsletters`.`page`, `newsletters`.`newsletter_id`, `newsletters`.`post_date`, `newsletter_categories`.`name` AS `category_name` FROM `newsletters` ".
		"LEFT JOIN `newsletter_categories` ON `newsletters`.`category_id` = `newsletter_categories`.`category_id` ".
		"WHERE `newsletters`.`showhide` = 0 " .(!MEMBER_ACCESS ? "&& `newsletters`.`public` = 1 " : ""). "&& (`newsletters`.`title` LIKE ?) ". 
		"GROUP BY `newsletters`.`newsletter_id` ORDER BY `newsletters`.`post_date` DESC, `newsletters`.`ordering` ASC", $params);
		if($query && !$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$searchresults = array_merge($searchresults, $result);
		}
				
		//Careers
		$params = array(date('Y-m-d'),
			'%'.$searchterm.'%',
			'%'.$searchterm.'%',
			'%'.$searchterm.'%'
		);
		$query = $db->query("SELECT `careers`.`title`, `careers`.`page`, `careers`.`career_id`, `careers`.`posted_date`, `careers`.`closing_date`, `career_categories`.`category_name`, `facilities`.`facility_name`, `facilities`.`city`, `facilities`.`province` FROM `careers` ". 
		"LEFT JOIN `career_categories` ON `career_categories`.`category_id` = `careers`.`category_id` ". 
		"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `careers`.`facility_id` ".
		"WHERE `careers`.`showhide` = 0 AND `careers`.`approved` = 1 AND `careers`.`closing_date` <= ? ".
		(!USER_LOGGED_IN ? "AND `careers`.`public` = 1" : "").
		"AND (`careers`.`title` LIKE ? || `career_categories`.`category_name` LIKE ? || `facilities`.`facility_name` LIKE ?) ".
		"ORDER BY `careers`.`posted_date` DESC, `careers`.`career_id` DESC", $params);
		if($query && !$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$searchresults = array_merge($searchresults, $result);
		}
		
		//Classifieds
		$params = array(
			'%'.$searchterm.'%',
			'%'.$searchterm.'%'
		);
		$query = $db->query("SELECT `classifieds`.`title`, `classifieds`.`page`, `classifieds`.`classified_id`, `classifieds`.`date_added`, `facilities`.`facility_name` FROM `classifieds` ". 
		"LEFT JOIN `facilities` ON `facilities`.`facility_id` = `classifieds`.`facility_id` ".
		"WHERE `classifieds`.`showhide` = 0 ".
		(!USER_LOGGED_IN ? " AND `classifieds`.`public` = 1" : "").
		"AND (`classifieds`.`title` LIKE ? || `facilities`.`facility_name` LIKE ?) ".
		"ORDER BY `classifieds`.`date_added`", $params);	
		if($query && !$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$searchresults = array_merge($searchresults, $result);
		}
		
		//Awards
		$params = array(
			'%'.$searchterm.'%'
		);
		$query = $db->query("SELECT `name`, `page`, `award_id` FROM `awards` WHERE `showhide` = 0 && `parent_id` IS NULL && `name` LIKE ? ORDER BY `ordering`", $params);
		if($query && !$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$searchresults = array_merge($searchresults, $result);
		}
		
		//Award Winners
		$params = array(' ',
			'%'.$searchterm.'%'
		);
		$query = $db->query("SELECT `award_winners`.`year`, `award_winners`.`type`, `award_winners`.`winner_id`, `award_winners`.`award_id`, `awards`.`name` AS `award_name`, `awards`.`page`, ".
		"IFNULL(`award_winners`.`first_name`, `account_profiles`.`first_name`) AS `first_name`, ".
		"IFNULL(`award_winners`.`last_name`, `account_profiles`.`last_name`) AS `last_name` ".
		"FROM `award_winners` ".
		"LEFT JOIN `accounts` ON `award_winners`.`account_id` = `accounts`.`account_id` ".
		"LEFT JOIN `account_profiles` ON `award_winners`.`account_id` = `account_profiles`.`account_id` ".
		"LEFT JOIN `awards` ON `award_winners`.`award_id` = `awards`.`award_id` ".
		"WHERE `award_winners`.`showhide` = 0 && (CONCAT(IFNULL(`award_winners`.`first_name`, `account_profiles`.`first_name`), ?, IFNULL(`award_winners`.`last_name`, `account_profiles`.`last_name`)) LIKE ?) ".
		"ORDER BY `award_winners`.`year` DESC, `awards`.`ordering`", $params);
		if($query && !$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$searchresults = array_merge($searchresults, $result);
		}
		
		//Pages
		$result = array();
		foreach($sitemap as $key => $search_page){
			if(stripos($search_page['seo_title'], $searchterm) !== false ||
			   stripos($search_page['meta_description'], $searchterm) !== false){
			   $result[$key] = $search_page;
			}
		}
		$searchresults = array_merge($searchresults, $result);
				
		//Scholarhip & Bursary
		$params = array(' ',
			'%'.$searchterm.'%',
			'%'.$searchterm.'%'
		);
		$query = $db->query("SELECT `scholarship_bursary`.`winner_id` AS `scholarship_id`, `scholarship_bursary`.`award`, `scholarship_bursary`.`type`, `scholarship_bursary`.`year`, ".
		"IFNULL(`scholarship_bursary`.`first_name`, `account_profiles`.`first_name`) AS `first_name`, ".
		"IFNULL(`scholarship_bursary`.`last_name`, `account_profiles`.`last_name`) AS `last_name` ".
		"FROM `scholarship_bursary` ".
		"LEFT JOIN `accounts` ON `scholarship_bursary`.`account_id` = `accounts`.`account_id` ".
		"LEFT JOIN `account_profiles` ON `scholarship_bursary`.`account_id` = `account_profiles`.`account_id` ".
		"WHERE `scholarship_bursary`.`showhide` = 0 && (CONCAT(IFNULL(`scholarship_bursary`.`first_name`, `account_profiles`.`first_name`), ?, IFNULL(`scholarship_bursary`.`last_name`, `account_profiles`.`last_name`)) LIKE ? || `award` LIKE ?) ORDER BY `year` DESC, `type`", $params);
		if($query && !$db->error() && $db->num_rows()){
			$result = $db->fetch_array();
			$searchresults = array_merge($searchresults, $result);
		}
				
		//Pagination
		$totalresults = count($searchresults);
		if($pg != 'all'){
			$start = (($pg-1)*$limit);
			$end = $limit;
		}else{
			$start = 0;
			$end = $totalresults;
		}		
		$searchresults = array_slice($searchresults, $start, $end);
		
		
	}
	
}

?>