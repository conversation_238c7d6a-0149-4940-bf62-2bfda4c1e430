<?php

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

if(isset($_POST) && USER_LOGGED_IN){

	// get vars
	$newsletter_id = (isset($_POST['newsletter_id']) ? $_POST['newsletter_id'] : '');
	$item_id = (isset($_POST['item_id']) ? $_POST['item_id'] : '');
	$email = (isset($_POST['email']) ? $_POST['email'] : "");
	$limit = (isset($_POST['limit']) ? $_POST['limit'] : "");
	$start = (isset($_POST['start']) && $_POST['start'] != "" ? $_POST['start'] : "0");

	// get search vars
	$searchterm = (isset($_POST['searchterm']) ? str_replace("'", "&rsquo;", stripslashes($_POST['searchterm'])) : "");
	$searchfilters = (isset($_POST['searchfilters']) ? $_POST['searchfilters'] : "");
	$filterwhere = "";

	if(trim($searchfilters) != ''){
		$filters = explode('::', $searchfilters);
		foreach($filters as $val){
			if($val != ""){
				$filterwhere .= "`$val` > 0 && ";	
			}
		}
		$filterwhere = substr($filterwhere, 0, -4);
	}

	//specific email
	if(trim($email) != "") {
		$params = array();
		$params[] = (!empty($newsletter_id) ? $newsletter_id : $item_id);
		$params[] = $email;
		$query = $db->query("SELECT `event`, `date`, `url`, `reason` FROM `email_events` WHERE ".(!empty($newsletter_id) ? "`newsletter_id` = ? " : "`item_id` = ? ")."&& `email` = ? ORDER BY `date` DESC", $params);
		if($query && !$db->error()) {
			if($db->num_rows() > 0) {
				$result = $db->fetch_array();
				foreach($result as $row) {
					echo ucwords(str_replace("_"," ",$row['event'])). ' at ' .$row['date'].($row['reason'] != "" ? " (Reason: ".$row['reason'].")" : "");
					echo (trim($row['url']) != '' ? ' <a href="' .$row['url']. '" target="_blank">' .(strlen($row['url']) > 100 ? wordwrap($row['url'], 100, "...<br />", true) : $row['url']). '</a>' : '');
					echo '<br />';
				}
			}
		}

	// all emails
	} else {

		//build query
		$querystr = "SELECT ";
		$querystr .= "SUM(`event` = ?) AS `processed`,";
		$querystr .= "SUM(`event` = ?) AS `delivered`,";
		$querystr .= "SUM(`event` = ?) AS `opens`,";
		$querystr .= "SUM(`event` = ?) AS `clicks`,";
		$querystr .= "SUM(`event` = ?) AS `bounces`,";
		$querystr .= "SUM(`event` = ?) AS `complaints`,";
		$querystr .= "SUM(`event` = ?) AS `unsubscribes`,";
		$querystr .= "`email` FROM `email_events` WHERE ".(!empty($newsletter_id) ? "`newsletter_id` = ? " : "`item_id` = ? ").(trim($searchterm) != "" ? " AND `email` LIKE ?" : ""). " GROUP BY `email`" .(trim($filterwhere) != "" ? " HAVING (" .$filterwhere. ")" : "");

		//get total
		$params = array('processed', 'delivered', 'open', 'click', 'bounce', 'spamreport', 'unsubscribe');
		$params[] = (!empty($newsletter_id) ? $newsletter_id : $item_id);
		if(trim($searchterm) != "") {
			$params[] = '%'.$searchterm.'%';
		}
		$query = $db->query($querystr, $params);
		if($query && !$db->error()) {
			$total = $db->num_rows();
			$prev = $start-$limit;
			$next = $start+$limit;
		}

		echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

		echo "<thead>";
			echo "<th width='40px' class='{sorter:false}'></th>";	
			echo "<th width='20px' class='{sorter:false}'></th>";	
			echo "<th class='{sorter:false}'>Email</th>";
			echo "<th width='70px' class='{sorter:false}'>Processed</th>";	
			echo "<th width='70px' class='{sorter:false}'>Delivered</th>";	
			echo "<th width='70px' class='{sorter:false}'>Opens</th>";	
			echo "<th width='70px' class='{sorter:false}'>Clicks</th>";	
			echo "<th width='70px' class='{sorter:false}'>Complaints</th>";	
			echo "<th width='70px' class='{sorter:false}'>Unsubscribed</th>";	
		echo "</thead>";

		echo "<tbody>";

		//run pager query
		$querystr .= " ORDER BY `email` ASC LIMIT " .$start. ", " .$limit;

		$query = $db->query($querystr, $params);
		if($query && !$db->error()) {
			if($db->num_rows() > 0) {
				$result = $db->fetch_array();
				$count = 0;

				//loop through results
				foreach($result as $row) {
					if($row['email'] != ''){
						$count++;

						echo "<tr class='has_sub'>";
						echo "<td width='30px'>".renderGravatar($sendgrid->get_gravatar($row['email'], 30))."</td>";
						echo "<td width='20px'><a onclick=\"loadEmailEvents('" .$row['email']. "', " .$count. ");\"><span class='fa fa-info-circle'></span></a></td>";
						echo "<td>" .$row['email'] ."</td>";
						echo "<td class='center'>" .($row['processed'] > 0 ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
						echo "<td class='center'>" .($row['delivered'] > 0 ? "<span class='show'>Yes</span>" : ($row['bounces'] > 0 ? "<span class='fa fa-refresh' title='Bounced'></span>" : "<span class='hide'>No</span>")). "</td>";
						echo "<td class='center'>" .$row['opens']. "</td>";
						echo "<td class='center'>" .$row['clicks']. "</small></td>";
						echo "<td class='center'>" .($row['complaints'] > 0 ? "<span class='show'>Yes</span>" : "-"). "</td>";
						echo "<td class='center'>" .($row['unsubscribes'] > 0 ? "<span class='show'>Yes</span>" : "-"). "</td>";
						echo "</tr>";
						
						echo "<tr id='expand-" .$count. "' class='stats-expanded'><td colspan='9'><div></div></td></tr>";
					}
				}

			// no results
			} else {
				echo "<tr><td colspan='9'>No matching records found.</td></tr>";
			}
		}

		echo "</tbody>";
		echo "</table>";

		// pager
		echo '<div class="pager clearfix" data-pagesize="' .$limit. '">';
		echo '<span class="pagedisplay">
			Displaying '.($start+1).' - '.($next < $total ? $next : $total).' ('.$total.' Total)
		</span>';
		echo '<div class="pagebuttons clearfix">';
			echo '<span class="button-sm first '.($start > 0 ? '' : 'disabled').'" '.($start > 0 ? 'onclick="loadEmailStats(0, ' .$limit. ');"' : '').'>&laquo;</span>';
			echo '<span class="button-sm prev '.($start > 0 ? '' : 'disabled').'" '.($start > 0 ? 'onclick="loadEmailStats(' .$prev. ', ' .$limit. ');"' : '').'>&lsaquo;</span>';
			echo '<select class="gotoPage select" onchange="loadEmailStats(this.value, ' .$limit. ');">';
				$page = 0;
				for($i=0; $i<=$total; $i+=$limit){
					$page++;
					echo "<option value='" .$i. "'" .($start == $i ? " selected" : ""). ">" .$page. "</option>";
				}
			echo '</select>';
			echo '<span class="button-sm next '.(($start/$limit)+1 < $page ? '' : 'disabled').'" '.(($start/$limit)+1 < $page ? 'onclick="loadEmailStats(' .$next. ', ' .$limit. ');"' : '').'>&rsaquo;</span>';
			echo '<span class="button-sm last '.(($start/$limit)+1 < $page ? '' : 'disabled').'" '.(($start/$limit)+1 < $page ? 'onclick="loadEmailStats('.($page-1).', ' .$limit. ');' : '').'">&raquo;</span>';
		echo '</div>';
		echo '</div>';			
	}

} else {
	echo "Access Denied.";	
}
?>