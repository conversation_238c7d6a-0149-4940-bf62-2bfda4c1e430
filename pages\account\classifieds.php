<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Restrict to members
if(!defined('MEMBER_ACCESS') || !MEMBER_ACCESS){
	exit();
}

//Manage classified
if(ACTION == 'add' || (ACTION == 'edit' && array_key_exists(ITEM_ID, $classifieds))){
	// echo ACTION;
	$panel_id = 61;
	//Panel title
	$page['page_panels'][$panel_id]['title'] = (ACTION == 'add' ? 'Post a' : 'Edit').' Classified';
	$page['page_panels'][$panel_id]['show_title'] = true;

	//Display form
	//Display form
$form = '<form method="post" action="" class="" id="classified-posting" enctype="multipart/form-data">
	

	<div class="form-grid">

		<div class="form-field">
			<label>Title <strong class="color-red">*</strong></label>
			<input type="text" name="title" class="input' .(in_array('title', $required) ? ' required' : ''). '" value="' .(isset($_POST['title']) ? $_POST['title'] : (isset($classified['title']) ? $classified['title'] : '')). '" />

			<label>Facility <strong class="color-red">*</strong></label>
			<select class="select' .(in_array('facility_id', $required) ? ' required' : ''). '" name="facility_id" id="facility_id">
				<option value="">- Select a Facility -</option>'; 
				foreach($facilities as $facility){
					$form .= '<option '.(!empty($classified) && $classified['facility_id'] == $facility['facility_id'] ? 'selected' : (!isset($_POST['facility_id']) && $Account->facility_id == $facility['facility_id'] ? 'selected' : '')).' value="'.$facility['facility_id'].'">'.$facility['facility_name'].'</option>';
				}
			$form .= '</select>

			<label>Attach File <small>(PDF Document, 2MB maximum)</small></label>
			<div class="input-file-container">  
				<input class="input-file" id="file" name="file" type="file" />
				<label tabindex="0" for="file" class="input-file-trigger' .(in_array('file', $required) ? ' required' : ''). '"><i class="fa fa-upload"></i>Select a file...</label>
			</div>';
			if(isset($file) && $file != '' && file_exists($filedir.$file)){
				$form .= '<input type="checkbox" class="checkbox" name="deletefile" id="deletefile" value="1">
				<label for="deletefile"><small>Delete Current File</small></label>';
				$form .= '<small><a href="' .$path.$filedir.$file. '" target="_blank"><i class="fa fa-file-pdf-o"></i> View Current File</a></small>';
				$form .= '<input type="hidden" name="old_file" value="' .$file. '" />';
			}else{
				$form .= '<input type="hidden" name="old_file" value="" />';
			}

			$form .= '<input type="checkbox" class="checkbox" id="public" value="0" name="public" '.((isset($classified['public']) && $classified['public'] == 0) ? 'checked' : '').'>
			<label for="public"><small>Restrict to Registered Members</small></label>

			<input type="checkbox" class="checkbox" id="showhide" value="1" name="showhide" '.((isset($classified['showhide']) && $classified['showhide'] == 1) ? 'checked' : '').'>
			<label for="showhide"><small>Hide Ad</small></label>
		</div>

		<div class="form-field">
			<label>First Name <strong class="color-red">*</strong></label>
			<input type="text" name="first_name" class="input' .(in_array('first_name', $required) ? ' required' : ''). '" value="'. (isset($classified['first_name']) ? $classified['first_name'] : $Account->first_name). '"/>

			<label>Last Name <strong class="color-red">*</strong></label>
			<input type="text" name="last_name" class="input' .(in_array('last_name', $required) ? ' required' : ''). '" value="' .(isset($classified['last_name']) ? $classified['last_name'] : $Account->last_name). '"/>

			<label>Email <strong class="color-red">*</strong></label>
			<input type="email" name="email" class="input' .(in_array('email', $required) ? ' required' : ''). '" value="' .(isset($classified['email']) ? $classified['email']: $Account->email). '"/>

			<label>Phone Number <strong class="color-red">*</strong></label>
			<input type="text" name="phone" class="input phone' .(in_array('phone', $required) ? ' required' : ''). '" value="' .(isset($classified['phone']) ? $classified['phone'] : $Account->phone). '"/>
		</div>

	</div>

	<h4>Ad Description <strong class="color-red">*</strong></h4>
	<fieldset class="' .(in_array('texteditor', $required) ? 'required' : ''). '">
		<textarea name="texteditor" id="content" class="textarea texteditor' .(in_array('texteditor', $required) ? ' required' : ''). '">' .(isset($classified['texteditor']) ? $classified['texteditor'] : ''). '</textarea>
	</fieldset>

	<div class="form-buttons">
		<button type="submit" name="post" value="post" class="button primary red"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> '.(ACTION == 'edit' ? 'Save Changes' : 'Post Ad').'</button>';
	$form .= (ACTION == 'edit' ? '<button type="button" id="delete-button" class="button primary red back-button confirm delete-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Delete</button>' : '');
	$form .= '<a href="' .$_sitepages['classifields']['page_url']. '" class="button primary black back-button"> <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span> Cancel</a>
	</div>

	<input type="hidden" name="xid" value="'.$_COOKIE['xid'].'" />
</form>';


	$html .= $form;

//View classifieds	
}else{

	// $html .= $page['page_panels'][$panel_id]['content'];
	
	//Add button
	$html .= '<a href="'.$_sitepages['my_classifields']['page_url'].'?action=add" class="button f_right solid inline action-btn">Create New Ad</a>';

	//Search form
$html .= '<form name="search-form" id="directory-search-bar" action="" method="get" class="clearfix directory-search-form">
	<div class="search-input-container">
		<input type="text" name="search" class="input directory-search-input" value="' . htmlspecialchars((isset($_GET['search']) ? $_GET['search'] : ''), ENT_QUOTES, 'UTF-8') . '" placeholder="Search" />

		' . (isset($_GET['search']) && trim($_GET['search']) != '' ? '
			<a href="' . htmlspecialchars($_sitepages['classifields']['page_url'], ENT_QUOTES, 'UTF-8') . '" title="Clear Search" class="clear-search-btn">×</a>
		' : '') . '

		<button type="submit" class="button search-icon-btn" title="Search">
			<i class="fa fa-search"></i>
		</button>
	</div>

	<button type="submit" class="button primary visually-hidden">Search</button>
</form>';


	$html .= '<table id="classified-table" cellpadding="10" cellspacing="0" border="0" width="100%" class="directory-table">';
	if(!empty($classifieds)){
		$html .= '<tr>
			<th class="left">Title</th>
			<th width="200px" class="left">Facility</th>
			<th width="140px" class="left">Availability</th>
			<th width="60px" class="left">&nbsp;</th>
		</tr>';

		foreach($classifieds as $classified){
			$html .= '<tr>
				<td>'
					.($classified['showhide'] ? '' : '<a href="'.$_sitepages['classifields']['page_url'] .$classified['page']. '-' .$classified['classified_id']. '/'.'">')
					.$classified['title']
					.($classified['showhide'] ? ' <small>(Hidden)</small>' : '</a>').'
				</td>
				<td>'.$classified['facility_name'].'</td>
				<td>'.($classified['public'] ? 'Public' : 'Members Only').'</td>
				<td align="center" ><a href="' .$_sitepages['my_classifields']['page_url'].'?action=edit&id='.$classified['classified_id'].'" class="button simple"><i class="fa fa-edit"></i></a></td>
			</tr>';
		}

	}else{
		$html .= '<tr><td class="nobg" colspan="4">No classifieds found' .(isset($_GET['search']) && trim($_GET['search']) != '' ? ' matching <strong>`'.$_GET['search'].'`</strong>' : ''). '.</td></tr>';

	}
	$totalresults =0;
	//Pager
	if($totalresults > 0){
		$searchterm = (isset($_GET['search']) ? $_GET['search'] : '');
		$html .= '<tr>
			<td class="pager" colspan="4">
				<small>';
					$html .= 'Displaying '.($pg == 'all' ? '1 - '.$totalresults : (1+($limit*($pg-1))).' - '.(count($classifieds)+($limit*($pg-1)))).' (of '.$totalresults.' Total)<br />';
					if($totalresults > $limit && $pg != 'all'){
						$tagend = round($totalresults % $limit, 0);
						$splits = round(($totalresults - $tagend)/$limit, 0);
						$num_pages = ($tagend == 0 ? $splits : $splits+1);	
						$pos = $pg;
						$startpos = ($pos*$limit)-$limit;

						$html .= ($pos > 1 ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos-1).'">&lsaquo; Prev</a> ' : '');
						for($i=1; $i<=$num_pages; $i++){
							$html .= ($i != $pos ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.$i.'">'.$i.'</a> ' : '<span><u>'.$i.'</u></span> ');
						}
						$html .= ($pos < $num_pages ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos+1).'">Next &rsaquo;</a> ' : '');
					}
				$html .= '</small>
			</td>
		</tr>';
	}
	
	$html .= '</table>';

}

//Set panel content
// $page['page_panels'][$panel_id]['content'] = $html;

?>