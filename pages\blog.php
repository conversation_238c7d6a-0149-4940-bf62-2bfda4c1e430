<?php

//display all
if(!isset($entry_id)){

	$html = '';

	// Add custom CSS and JS for blog page
	// $html .= '<link rel="stylesheet" href="' . $path . 'theme/css/blog-custom.css">';
	// $html .= '<script src="' . $path . 'js/blog-slider.js"></script>';

	// Add container with blog-page class
	$html .= '<div class="blog-page">';

	//blog navigation
	$html .= '<div id="blog-leftcol" class="f_left blog-sidebar">';
		// Add search bar
		$html .= '<div class="blog-search">';
			$html .= '<input type="text" placeholder="Search" />';
			$html .= '<i class="fas fa-search search-icon"></i>';
		$html .= '</div>';

		// Add category filter dropdown
		$html .= '<div class="blog-category-filter">';
			$html .= '<select>';
				$html .= '<option value="">Most Recent</option>';
				if(count($blog_categories) > 0){
					foreach($blog_categories as $cat){
						if($blog_settings['empty_categories'] || (!$blog_settings['empty_categories'] && $cat['total_entries'] > 0)){
							$html .= '<option value="' . $cat['category_id'] . '">' . $cat['name'] . ' (' . $cat['total_entries'] . ')</option>';
						}
					}
				}
			$html .= '</select>';
		$html .= '</div>';

		$html .= '<div class="blog-posts-list">';

		// Add Swiper container
		$html .= '<div class="blog-swiper-container">';
			$html .= '<div class="swiper-wrapper">';

			// Create slides for each blog entry
			if(count($blog_entries) > 0){
				foreach($blog_entries as $entry){
					$entry_url = $_sitepages['blog']['page_url']. ($pagebits[2] != '' ? $pagebits[2] : $entry['page']. '-' .$entry['category_id']). '/' .$entry['page']. '-' .$entry['entry_id']. '/';

					$html .= '<div class="swiper-slide" data-entry-id="' . $entry['entry_id'] . '" data-category-id="' . $entry['category_id'] . '">';
						$html .= '<div class="blog-post-item">';
						$html .= '<div class="post-title">' . $entry['title'] . '</div>';

						$html .= '<div class="post-date">' . date("d F, Y", strtotime($entry['post_date'])) . '</div>';
							// if(isset($blog_categories[$entry['category_id']]['name'])) {
							// 	$html .= '<div class="post-category">' . $blog_categories[$entry['category_id']]['name'] . '</div>';
							// }
						$html .= '<i class="fa fa-chevron-right post-chevron"></i>';
						$html .= '</div>';
					$html .= '</div>';
				}
			}

			$html .= '</div>'; // close swiper-wrapper

			// Remove scrollbar as it's not needed and conflicts with custom navigation
			// $html .= '<div class="swiper-scrollbar"></div>';

			// Add navigation arrows with vertical design
			$html .= '<div class="swiper-button-next blog-nav-arrow" aria-label="Next slide"></div>';
			$html .= '<div class="swiper-button-prev blog-nav-arrow" aria-label="Previous slide"></div>';

		$html .= '</div>'; // close blog-swiper-container
		$html .= '</div>'; // close blog-posts-list

		// Keep the original navigation for fallback
		$html .= '<nav id="blog-navigation" style="display: none;">';

		//newest entry
		if(count($recent_entries) > 0){
			$html .= '<h6>Newest Entry</h6>';

			$html .= '<ul class="blog-categories">';
			foreach($recent_entries as $recent){
				$html .= '<li><a href="' .$_sitepages['blog']['page_url']. 'recent/' .$recent['page']. '-' .$recent['entry_id']. '/">' .$recent['title']. '</a></li>';
				break;
			}
			$html .= '</ul>';
		}

		//categories
		$html .= '<h6>Categories</h6>';

		$html .= '<ul class="blog-categories">';
		if(count($recent_entries) > 0){
			$html .= '<li><a href="' .$_sitepages['blog']['page_url']. 'recent/" class="'.($pagebits[2] == 'recent' ? 'active' : '').'">Most Recent ('.count($recent_entries).')</a></li>';
		}
		if(count($popular_entries) > 0){
			$html .= '<li><a href="' .$_sitepages['blog']['page_url']. 'popular/" class="'.($pagebits[2] == 'popular' ? 'active' : '').'">Most Popular ('.count($popular_entries).')</a></li>';
		}
		if(count($blog_categories) > 0){
			foreach($blog_categories as $cat){
				if($blog_settings['empty_categories'] || (!$blog_settings['empty_categories'] && $cat['total_entries'] > 0)){
					$html .= '<li><a href="' .$_sitepages['blog']['page_url']. $cat['page']. '-' .$cat['category_id']. '/" class="' .(isset($filter_type) && $filter_type == 'category' && $slug_id == $cat['category_id'] ? 'active' : ''). '">' .$cat['name']. ' (' .$cat['total_entries']. ')</a></li>';
				}
			}
		}
		$html .= '</ul>';

		//authors
		if($blog_settings['show_author'] == 1 && !empty($blog_authors)) {
			$html .= '<h6>Authors</h6>';

			$html .= '<ul class="blog-categories">';
			foreach($blog_authors as $authors){
				$html .= '<li><a href="' .$_sitepages['blog']['page_url']. $authors['page']. '-' . $authors['author_id'] . '/" class="' .(isset($filter_type) && $filter_type == 'author' && $slug_id == $authors['author_id'] ? 'active' : ''). '">' .$authors['name']. ' (' .$authors['total_entries']. ')</a></li>';
			}
			$html .= '</ul>';
		}

		//archives
		if(count($blog_archives) > 0){
			$html .= '<h6>Archived</h6>';

			$html .= '<ul class="blog-categories">';
			foreach($blog_archives as $archive){
				$html .= '<li><a href="' .$_sitepages['blog']['page_url']. date("mY", strtotime($archive['datetime'])). '/" class="' .($pagebits[2] == date("mY", strtotime($archive['datetime'])) ? 'active' : ''). '">' .date("F Y", strtotime($archive['datetime'])). ' (' .$archive['total_entries']. ')</a></li>';
			}
			$html .= '</ul>';
		}

		$html .= '</nav>';
	$html .= '</div>'; //close #blog-leftcol

	//display entries

	// Add mobile dropdown (only visible on small screens)
// Add mobile dropdown (only visible on small screens)
$html .= '<div class="blog-mobile-dropdown">';
    // Add search bar with autocomplete dropdown
    $html .= '<div class="blog-search">';
        $html .= '<input type="text" placeholder="Search" />';
        $html .= '<i class="fas fa-search search-icon"></i>';
        $html .= '<div class="search-results-dropdown"></div>';
    $html .= '</div>';

    // Keep separate dropdown
    $html .= '<select id="mobile-blog-select">';
        $html .= '<option value="">Select a blog post</option>';
        // Create options for each blog entry
        if(count($blog_entries) > 0){
            foreach($blog_entries as $entry){
                $html .= '<option value="' . $entry['entry_id'] . '">' . $entry['title'] . ' - ' . date("d F, Y", strtotime($entry['post_date'])) . '</option>';
            }
        }
    $html .= '</select>';
$html .= '</div>';

	$html .= '<div id="blog-rightcol" class="f_right blog-content">';

		if(count($blog_entries) > 0){
			// First, output the date and title for the first entry
			$firstEntry = reset($blog_entries);
			$html .= '<div class="blog-date">Posted on ' .date("d F, Y", strtotime($firstEntry['post_date'])). '</div>';
			$html .= '<h2 class="blog-title">' .$firstEntry['title']. '</h2>';

			// Now generate all blog entries, but only show the first one
			foreach($blog_entries as $index => $entry){
				$entry_url = $_sitepages['blog']['page_url']. ($pagebits[2] != '' ? $pagebits[2] : $entry['page']. '-' .$entry['category_id']). '/' .$entry['page']. '-' .$entry['entry_id']. '/';

				// Only show the first entry, hide others
				$display = ($index === 0) ? 'block' : 'none';

				$html .= '<div class="blog-entry" data-entry-id="' . $entry['entry_id'] . '" style="display: ' . $display . ';">';
				if($entry['image'] != '' && file_exists($imagedir.$entry['image'])){
					$html .= '<div class="blog-entry-media">';

							$html .= '<div class="blog-thumb">';
								$html .= '<img src="' .$path. $imagedir . $entry['image'] . '" alt="' . ($entry['image_alt'] ?: $entry['title']) . '" />';
							$html .= '</div>';
					$html .= '</div>';
				}

					$html .= '<div class="blog-entry-content">';
						// $html .= '<h4><a href="' .$entry_url. '">' .$entry['title']. '</a></h4>';
						// $html .= '<div class="blog-date">Posted on ' .date("d F, Y", strtotime($entry['post_date'])). '</div>';

						$html .= '<hr />';

						// if(isset($blog_categories[$entry['category_id']]['name'])){
						// 	$html .= '<div class="blog-category"><a href="' .$_sitepages['blog']['page_url']. $entry['page']. '-' .$entry['category_id']. '/">' .$blog_categories[$entry['category_id']]['name']. '</a></div>';
						// }

						$html .= '<div class="blog-description">' .nl2br($entry['content']). '</div>';

						$html .= '<div class="blog-actions">';
							$html .= '<a href="' .$entry_url. '">Read More</a>';
							if($blog_settings['comments']) {
								$html .= ' | <a href="' .$entry_url. '">Comments (' .$entry['total_comments']. ')</a>';
							}
						$html .= '</div>';
					$html .= '</div>';
				$html .= '</div>';
			}
		}else{
			$html .= '<p class="center">No entries to display.</p>';
		}

		// Example Pagination
		if ($last_pg > 1) {

			// Always show at least 5 pages
			$start = max(1, min($last_pg-4, $pg-2));
			$end   = min($last_pg, $start+4);

			$html .= '<nav class="pagination">'.
				($start > 1 ? '<a class="page-link" href="'.$page['page_url'].'?pg=1">1</a>' : '').
				($start > 2 ? '<span class="ellip">...</span>' : '');

			foreach (range($start, $end) as $i) {
				$html .= $i == $pg ? '<span class="page-link active">'.$i.'</span>' : '<a class="page-link" href="'.$page['page_url'].'?pg='.$i.'">'.$i.'</a>';
			}

				$html .= ($end < $last_pg-1 ? '<span class="ellip">...</span>' : '').
				($end < $last_pg ? '<a class="page-link" href="'.$page['page_url'].'?pg='.$last_pg.'">'.$last_pg.'</a>' : '').
			'</nav>';
		}

	$html .= '</div>'; //close #blog-rightcol

	$html .= '</div>'; //close .blog-page

	// Instead of using prepend_content, we'll use the standard content approach
	// This ensures the footer appears properly at the bottom of the page
	$page['page_panels'][$panel_id]['content'] = (trim($page['page_panels'][$panel_id]['content']) != '' ? '<hr/>' : '').$html;

	// Add a class to style the blog panel
	$page['page_panels'][$panel_id]['class'] = isset($page['page_panels'][$panel_id]['class']) ?
		$page['page_panels'][$panel_id]['class'] . ' blog-page-panel wide-panel' : 'blog-page-panel wide-panel';

//display selected
}else{

	$html = '';

	//blog entry
	$html .= '<article id="blog-entry">';
		$html .= '<p><a href="' .$_sitepages['blog']['page_url']. $pagebits[2]. '/">&lsaquo; Back to ' .$page['name']. '</a></p>';
		$html .= '<header><h2>' .$entry['title']. '</h2><h6>' .date("d F, Y", strtotime($entry['post_date'])). '</h6></header>';
		if($blog_settings['show_author'] && !is_null($entry['author_id'])){
			$html .= '<p><a href="' .$_sitepages['blog']['page_url']. $entry['author_page'].'-'.$entry['author_id']. '/">' .$entry['author_name']. '</a></p>';
		}
		$html .= $entry['content'];

		//schema markup (image must be included in order for the schema markup to be valid)
		if(!empty($entry['image']) && file_exists($imagedir.$entry['image'])) {
			$entry['schema_markup'] = [
				"@context" => "https://schema.org",
				"@type"	=> "BlogPosting",
				"mainEntityOfPage" => [
					"@type"	=> "WebPage",
					"@id" => $siteurl.$page['meta_canonical']
				],
				"headline" => $entry['title'],
				"description" => $entry['description'],
				"datePublished" => date('c', strtotime($entry['date_added'])),
				"dateModified" => date('c', strtotime($entry['last_modified'])),
				"publisher"	=> [
					"@type" => "Organization",
					"name" => $global['company_name'],
					"logo" => [
						"@type"	=> "ImageObject",
						"url" => $siteurl.$path."images/logo.png"
					]
				],
				"image"	=> [
					$siteurl.$path.$imagedir.$entry['image']
				],
			];

			if($blog_settings['show_author'] == 1 && !is_null($entry['author_id'])){
				$entry['schema_markup']['author'] = [
					"@type"	=> "Person",
					"name" => $entry['author_name']
				];
			} else {
				$entry['schema_markup']['author'] = [
					"@type"	=> "Organization",
					"name" => $global['company_name'],
					"url" => $siteurl,
				];
			}

			$html .= '<script type="application/ld+json">' . json_encode($entry['schema_markup']) . '</script>';
		}
	$html .= '</article>';

	//prev/next post
	if(!empty($next_prev)){
		if(isset($next_prev['prev']) && !empty($next_prev['prev'])){
			$prev_url = $_sitepages['blog']['page_url']. ($pagebits[2] != '' ? $pagebits[2] : $next_prev['prev']['page']. '-' .$next_prev['prev']['category_id']). '/' .$next_prev['prev']['page']. '-' .$next_prev['prev']['entry_id']. '/';

			$html .= "<a href='$prev_url' class='f_left'>&lsaquo; Previous Entry</a>";
		}
		if(isset($next_prev['next']) && !empty($next_prev['next'])){
			$next_url = $_sitepages['blog']['page_url']. ($pagebits[2] != '' ? $pagebits[2] : $next_prev['next']['page']. '-' .$next_prev['next']['category_id']). '/' .$next_prev['next']['page']. '-' .$next_prev['next']['entry_id']. '/';

			$html .= "<a href='$next_url' class='f_right'>Next Entry &rsaquo;</a>";
		}
	}

	//sharethis
	if($blog_settings['social_sharing']){
		$html .= '<div id="blog-share">';
			$html .= '<div id="blog-sharethis-buttons" class="sharethis-buttons"></div>';
		$html .= '</div>'; //close #blog-share
	}

	//blog comments
	if($blog_settings['comments']){
		$html .= '<hr />';

        $html .= '<div id="blog-comments-wrapper">';
            $html .= '<h3>Comments</h3>';

            $html .= '<div id="blog-comments">';
			if(count($entry['comments']) > 0){
	            foreach($entry['comments'] as $comment){
	                $html .= '<div class="blog-comment">';
	                    $html .= '<h5 class="blog-comment-title">' .(trim($comment['name']) != '' ? $comment['name'] : 'Anonymous'). ' &nbsp; <small class="tmlight">' .$comment['date_added']. '</small></h5>';
	                    $html .= '<p class="blog-comment-content">' .nl2br($comment['message']). '</p>';
	                $html .= '</div>'; //close .blog-comment
	            }
	        } else {
	        	$html .= '<p class="no-comments"><small><em>No comments to display.  Be the first!</em></small></p>';
	        }
            $html .= '</div>'; //close #blog-comments
        $html .= '</div>'; //close #blog-comments-wrapper

		//comment form
		$html .= '<form name="blog-comment-form" id="blog-comment-form" action="" method="post" class="clearfix" data-recaptcha="#recaptcha-blog-comment-form">';
			$html .= '<h3>Leave a Comment</h3>';

			$html .= ($blog_settings['comment_approval'] ? '<p>Your comment will be submitted for approval before it is posted.</p>' : '');

			$html .= '<textarea name="comment" class="textarea jsvalidate" placeholder="Comment"></textarea><br />';
			$html .= '<input type="text" name="name" class="input" value="" placeholder="Your Name" /><br />';
			$html .= '<input type="text" name="email" class="input jsvalidate" value="" placeholder="Your Email" /><br />';

			$html .= '<br class="clear" />';

			$html .= '<button type="submit" name="submitform" class="button submit">Post Comment</button>';
			$html .= '<input type="hidden" name="entry_id" value="' .$entry_id. '" />';
			$html .= '<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />';
			$html .= '<input type="hidden" name="g-recaptcha-response" value="" />';

			$html .= '<div class="hidden">';
				$html .= '<div id="recaptcha-modal" class="hidden-modal" title="Verify You&rsquo;re Not a Robot">';
					$html .= '<div class="recaptcha-wrapper">';
						$html .= '<div id="recaptcha-blog-comment-form" class="g-recaptcha" data-sitekey="'.$global['recaptcha_key'].'"></div>';
					$html .= '</div>'; //close .recaptcha-wrapper
				$html .= '</div>'; //close .recaptcha-modal
			$html .= '</div>'; //close .hidden

		$html .= '</form>';
	}

	$page['page_panels'][$panel_id]['content'] = $html;
}

//panels
include("includes/pagepanels.php");

?>