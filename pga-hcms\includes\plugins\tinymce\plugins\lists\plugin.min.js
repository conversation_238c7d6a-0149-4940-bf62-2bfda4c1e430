!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("9",tinymce.util.Tools.resolve),g("1",["9"],function(a){return a("tinymce.PluginManager")}),g("2",["9"],function(a){return a("tinymce.util.Tools")}),g("3",["9"],function(a){return a("tinymce.util.VK")}),g("a",["9"],function(a){return a("tinymce.dom.DOMUtils")}),g("8",[],function(){var a=function(a){return a&&3===a.nodeType},b=function(a){return a&&/^(OL|UL|DL)$/.test(a.nodeName)},c=function(a){return a&&/^(LI|DT|DD)$/.test(a.nodeName)},d=function(a){return a&&"BR"===a.nodeName},e=function(a){return a.parentNode.firstChild===a},f=function(a){return a.parentNode.lastChild===a},g=function(a,b){return b&&!!a.schema.getTextBlockElements()[b.nodeName]},h=function(a,b){return!!d(b)&&!(!a.isBlock(b.nextSibling)||d(b.previousSibling))},i=function(a,b,c){var d=a.isEmpty(b);return!(c&&a.select("span[data-mce-type=bookmark]",b).length>0)&&d},j=function(a,b){return a.isChildOf(b,a.getRoot())};return{isTextNode:a,isListNode:b,isListItemNode:c,isBr:d,isFirstChild:e,isLastChild:f,isTextBlock:g,isBogusBr:h,isEmpty:i,isChildOfBody:j}}),g("h",["9"],function(a){return a("tinymce.dom.RangeUtils")}),g("j",["h","8"],function(a,b){var c=function(c,d){var e=a.getNode(c,d);if(b.isListItemNode(c)&&b.isTextNode(e)){var f=d>=c.childNodes.length?e.data.length:0;return{container:e,offset:f}}return{container:c,offset:d}},d=function(a){var b=a.cloneRange(),d=c(a.startContainer,a.startOffset);b.setStart(d.container,d.offset);var e=c(a.endContainer,a.endOffset);return b.setEnd(e.container,e.offset),b};return{getNormalizedEndPoint:c,normalizeRange:d}}),g("b",["a","8","j"],function(a,b,c){var d=a.DOM,e=function(a){var b={},c=function(c){var e,f,g;f=a[c?"startContainer":"endContainer"],g=a[c?"startOffset":"endOffset"],1===f.nodeType&&(e=d.create("span",{"data-mce-type":"bookmark"}),f.hasChildNodes()?(g=Math.min(g,f.childNodes.length-1),c?f.insertBefore(e,f.childNodes[g]):d.insertAfter(e,f.childNodes[g])):f.appendChild(e),f=e,g=0),b[c?"startContainer":"endContainer"]=f,b[c?"startOffset":"endOffset"]=g};return c(!0),a.collapsed||c(),b},f=function(a){function b(b){var c,e,f,g=function(a){for(var b=a.parentNode.firstChild,c=0;b;){if(b===a)return c;1===b.nodeType&&"bookmark"===b.getAttribute("data-mce-type")||c++,b=b.nextSibling}return-1};c=f=a[b?"startContainer":"endContainer"],e=a[b?"startOffset":"endOffset"],c&&(1===c.nodeType&&(e=g(c),c=c.parentNode,d.remove(f)),a[b?"startContainer":"endContainer"]=c,a[b?"startOffset":"endOffset"]=e)}b(!0),b();var e=d.createRng();return e.setStart(a.startContainer,a.startOffset),a.endContainer&&e.setEnd(a.endContainer,a.endOffset),c.normalizeRange(e)};return{createBookmark:e,resolveBookmark:f}}),g("c",["2","8"],function(a,b){var c=function(c){return a.grep(c.selection.getSelectedBlocks(),function(a){return b.isListItemNode(a)})};return{getSelectedListItems:c}}),g("4",["a","b","8","c"],function(a,b,c,d){var e=a.DOM,f=function(a,b){var d;if(c.isListNode(a)){for(;d=a.firstChild;)b.appendChild(d);e.remove(a)}},g=function(a){var b,d,g;return"DT"===a.nodeName?(e.rename(a,"DD"),!0):(b=a.previousSibling,b&&c.isListNode(b)?(b.appendChild(a),!0):b&&"LI"===b.nodeName&&c.isListNode(b.lastChild)?(b.lastChild.appendChild(a),f(a.lastChild,b.lastChild),!0):(b=a.nextSibling,b&&c.isListNode(b)?(b.insertBefore(a,b.firstChild),!0):(b=a.previousSibling,!(!b||"LI"!==b.nodeName)&&(d=e.create(a.parentNode.nodeName),g=e.getStyle(a.parentNode,"listStyleType"),g&&e.setStyle(d,"listStyleType",g),b.appendChild(d),d.appendChild(a),f(a.lastChild,d),!0))))},h=function(a){var c=d.getSelectedListItems(a);if(c.length){for(var e=b.createBookmark(a.selection.getRng(!0)),f=0;f<c.length&&(g(c[f])||0!==f);f++);return a.selection.setRng(b.resolveBookmark(e)),a.nodeChanged(),!0}};return{indentSelection:h}}),g("d",["a","2","8"],function(a,b,c){var d=a.DOM,e=function(a,b){var e,f=b.parentNode;"LI"===f.nodeName&&f.firstChild===b&&(e=f.previousSibling,e&&"LI"===e.nodeName?(e.appendChild(b),c.isEmpty(a,f)&&d.remove(f)):d.setStyle(f,"listStyleType","none")),c.isListNode(f)&&(e=f.previousSibling,e&&"LI"===e.nodeName&&e.appendChild(b))},f=function(a,c){b.each(b.grep(a.select("ol,ul",c)),function(b){e(a,b)})};return{normalizeList:e,normalizeLists:f}}),g("k",["9"],function(a){return a("tinymce.Env")}),g("f",["a","k"],function(a,b){var c=a.DOM,d=function(a,d,e){var f,g,h,i=c.createFragment(),j=a.schema.getBlockElements();if(a.settings.forced_root_block&&(e=e||a.settings.forced_root_block),e&&(g=c.create(e),g.tagName===a.settings.forced_root_block&&c.setAttribs(g,a.settings.forced_root_block_attrs),i.appendChild(g)),d)for(;f=d.firstChild;){var k=f.nodeName;h||"SPAN"===k&&"bookmark"===f.getAttribute("data-mce-type")||(h=!0),j[k]?(i.appendChild(f),g=null):e?(g||(g=c.create(e),i.appendChild(g)),g.appendChild(f)):i.appendChild(f)}return a.settings.forced_root_block?h||b.ie&&!(b.ie>10)||g.appendChild(c.create("br",{"data-mce-bogus":"1"})):i.appendChild(c.create("br")),i};return{createNewTextBlock:d}}),g("e",["a","8","f","2"],function(a,b,c,d){var e=a.DOM,f=function(a,f,g,h){var i,j,k,l,m=function(a){d.each(k,function(b){a.parentNode.insertBefore(b,g.parentNode)}),e.remove(a)};for(k=e.select('span[data-mce-type="bookmark"]',f),h=h||c.createNewTextBlock(a,g),i=e.createRng(),i.setStartAfter(g),i.setEndAfter(f),j=i.extractContents(),l=j.firstChild;l;l=l.firstChild)if("LI"===l.nodeName&&a.dom.isEmpty(l)){e.remove(l);break}a.dom.isEmpty(j)||e.insertAfter(j,f),e.insertAfter(h,f),b.isEmpty(a.dom,g.parentNode)&&m(g.parentNode),e.remove(g),b.isEmpty(a.dom,f)&&e.remove(f)};return{splitList:f}}),g("5",["a","b","8","d","c","e","f"],function(a,b,c,d,e,f,g){var h=a.DOM,i=function(a,b){c.isEmpty(a,b)&&h.remove(b)},j=function(a,b){var e,j=b.parentNode,k=j.parentNode;return j===a.getBody()||("DD"===b.nodeName?(h.rename(b,"DT"),!0):c.isFirstChild(b)&&c.isLastChild(b)?("LI"===k.nodeName?(h.insertAfter(b,k),i(a.dom,k),h.remove(j)):c.isListNode(k)?h.remove(j,!0):(k.insertBefore(g.createNewTextBlock(a,b),j),h.remove(j)),!0):c.isFirstChild(b)?("LI"===k.nodeName?(h.insertAfter(b,k),b.appendChild(j),i(a.dom,k)):c.isListNode(k)?k.insertBefore(b,j):(k.insertBefore(g.createNewTextBlock(a,b),j),h.remove(b)),!0):c.isLastChild(b)?("LI"===k.nodeName?h.insertAfter(b,k):c.isListNode(k)?h.insertAfter(b,j):(h.insertAfter(g.createNewTextBlock(a,b),j),h.remove(b)),!0):("LI"===k.nodeName?(j=k,e=g.createNewTextBlock(a,b,"LI")):e=c.isListNode(k)?g.createNewTextBlock(a,b,"LI"):g.createNewTextBlock(a,b),f.splitList(a,j,b,e),d.normalizeLists(a.dom,j.parentNode),!0))},k=function(a){var c=e.getSelectedListItems(a);if(c.length){var d,f,g=b.createBookmark(a.selection.getRng(!0)),h=a.getBody();for(d=c.length;d--;)for(var i=c[d].parentNode;i&&i!==h;){for(f=c.length;f--;)if(c[f]===i){c.splice(d,1);break}i=i.parentNode}for(d=0;d<c.length&&(j(a,c[d])||0!==d);d++);return a.selection.setRng(b.resolveBookmark(g)),a.nodeChanged(),!0}};return{outdent:j,outdentSelection:k}}),g("g",["9"],function(a){return a("tinymce.dom.BookmarkManager")}),g("6",["g","2","5","b","8","d","c","e"],function(a,b,c,d,e,f,g,h){var i=function(a,b,c){var d=c["list-style-type"]?c["list-style-type"]:null;a.setStyle(b,"list-style-type",d)},j=function(a,c){b.each(c,function(b,c){a.setAttribute(c,b)})},k=function(a,c,d){j(c,d["list-attributes"]),b.each(a.select("li",c),function(a){j(a,d["list-item-attributes"])})},l=function(a,b,c){i(a,b,c),k(a,b,c)},m=function(a,b,c){var d,f,g=a.getBody();for(d=b[c?"startContainer":"endContainer"],f=b[c?"startOffset":"endOffset"],1===d.nodeType&&(d=d.childNodes[Math.min(f,d.childNodes.length-1)]||d);d.parentNode!==g;){if(e.isTextBlock(a,d))return d;if(/^(TD|TH)$/.test(d.parentNode.nodeName))return d;d=d.parentNode}return d},n=function(c,d){for(var f,g=[],h=c.getBody(),i=c.dom,j=m(c,d,!0),k=m(c,d,!1),l=[],n=j;n&&(l.push(n),n!==k);n=n.nextSibling);return b.each(l,function(b){if(e.isTextBlock(c,b))return g.push(b),void(f=null);if(i.isBlock(b)||e.isBr(b))return e.isBr(b)&&i.remove(b),void(f=null);var d=b.nextSibling;return a.isBookmarkNode(b)&&(e.isTextBlock(c,d)||!d&&b.parentNode===h)?void(f=null):(f||(f=i.create("p"),b.parentNode.insertBefore(f,b),g.push(f)),void f.appendChild(b))}),g},o=function(a,c,f){var g,h=a.selection.getRng(!0),i="LI",j=a.dom;f=f?f:{},"false"!==j.getContentEditable(a.selection.getNode())&&(c=c.toUpperCase(),"DL"===c&&(i="DT"),g=d.createBookmark(h),b.each(n(a,h),function(b){var d,g,h=function(a){var b=j.getStyle(a,"list-style-type"),c=f?f["list-style-type"]:"";return c=null===c?"":c,b===c};g=b.previousSibling,g&&e.isListNode(g)&&g.nodeName===c&&h(g)?(d=g,b=j.rename(b,i),g.appendChild(b)):(d=j.create(c),b.parentNode.insertBefore(d,b),d.appendChild(b),b=j.rename(b,i)),l(j,d,f),u(a.dom,d)}),a.selection.setRng(d.resolveBookmark(g)))},p=function(a){var i=d.createBookmark(a.selection.getRng(!0)),j=a.getBody(),k=g.getSelectedListItems(a),l=b.grep(k,function(b){return a.dom.isEmpty(b)});k=b.grep(k,function(b){return!a.dom.isEmpty(b)}),b.each(l,function(b){if(e.isEmpty(a.dom,b))return void c.outdent(a,b)}),b.each(k,function(b){var c,d;if(b.parentNode!==a.getBody()){for(c=b;c&&c!==j;c=c.parentNode)e.isListNode(c)&&(d=c);h.splitList(a,d,b),f.normalizeLists(a.dom,d.parentNode)}}),a.selection.setRng(d.resolveBookmark(i))},q=function(a,b){return a&&b&&e.isListNode(a)&&a.nodeName===b.nodeName},r=function(a,b,c){var d=a.getStyle(b,"list-style-type",!0),e=a.getStyle(c,"list-style-type",!0);return d===e},s=function(a,b){return a.className===b.className},t=function(a,b,c){return q(b,c)&&r(a,b,c)&&s(b,c)},u=function(a,b){var c,d;if(c=b.nextSibling,t(a,b,c)){for(;d=c.firstChild;)b.appendChild(d);a.remove(c)}if(c=b.previousSibling,t(a,b,c)){for(;d=c.lastChild;)b.insertBefore(d,b.firstChild);a.remove(c)}},v=function(a,b,c){var e=a.dom.getParent(a.selection.getStart(),"OL,UL,DL");if(c=c?c:{},e!==a.getBody())if(e)if(e.nodeName===b)p(a,b);else{var f=d.createBookmark(a.selection.getRng(!0));l(a.dom,e,c),u(a.dom,a.dom.rename(e,b)),a.selection.setRng(d.resolveBookmark(f))}else o(a,b,c)};return{toggleList:v,removeList:p,mergeWithAdjacentLists:u}}),g("i",["9"],function(a){return a("tinymce.dom.TreeWalker")}),g("7",["h","i","3","6","b","8","d","j","c"],function(a,b,c,d,e,f,g,h,i){var j=function(c,d,e){var g,h,i=d.startContainer,j=d.startOffset;if(3===i.nodeType&&(e?j<i.data.length:j>0))return i;for(g=c.schema.getNonEmptyElements(),1===i.nodeType&&(i=a.getNode(i,j)),h=new b(i,c.getBody()),e&&f.isBogusBr(c.dom,i)&&h.next();i=h[e?"next":"prev2"]();){if("LI"===i.nodeName&&!i.hasChildNodes())return i;if(g[i.nodeName])return i;if(3===i.nodeType&&i.data.length>0)return i}},k=function(a,b,c){var d,e,g=b.parentNode;if(f.isChildOfBody(a,b)&&f.isChildOfBody(a,c)){if(f.isListNode(c.lastChild)&&(e=c.lastChild),g===c.lastChild&&f.isBr(g.previousSibling)&&a.remove(g.previousSibling),d=c.lastChild,d&&f.isBr(d)&&b.hasChildNodes()&&a.remove(d),f.isEmpty(a,c,!0)&&a.$(c).empty(),!f.isEmpty(a,b,!0))for(;d=b.firstChild;)c.appendChild(d);e&&c.appendChild(e),a.remove(b),f.isEmpty(a,g)&&g!==a.getRoot()&&a.remove(g)}},l=function(a,b){var c,g,i,l=a.dom,m=a.selection,n=l.getParent(m.getStart(),"LI");if(n){if(c=n.parentNode,c===a.getBody()&&f.isEmpty(l,c))return!0;if(g=h.normalizeRange(m.getRng(!0)),i=l.getParent(j(a,g,b),"LI"),i&&i!==n){var o=e.createBookmark(g);return b?k(l,i,n):k(l,n,i),a.selection.setRng(e.resolveBookmark(o)),!0}if(!i&&!b&&d.removeList(a,c.nodeName))return!0}return!1},m=function(a,b){var c=a.dom,e=c.getParent(a.selection.getStart(),c.isBlock);if(e&&c.isEmpty(e)){var f=h.normalizeRange(a.selection.getRng(!0)),g=c.getParent(j(a,f,b),"LI");if(g)return a.undoManager.transact(function(){c.remove(e),d.mergeWithAdjacentLists(c,g.parentNode),a.selection.select(g,!0),a.selection.collapse(b)}),!0}return!1},n=function(a,b){return l(a,b)||m(a,b)},o=function(a){var b=a.dom.getParent(a.selection.getStart(),"LI,DT,DD");return!!(b||i.getSelectedListItems(a).length>0)&&(a.undoManager.transact(function(){a.execCommand("Delete"),g.normalizeLists(a.dom,a.getBody())}),!0)},p=function(a,b){return a.selection.isCollapsed()?n(a,b):o(a)},q=function(a){a.on("keydown",function(b){b.keyCode===c.BACKSPACE?p(a,!1)&&b.preventDefault():b.keyCode===c.DELETE&&p(a,!0)&&b.preventDefault()})};return{setup:q,backspaceDelete:p}}),g("0",["1","2","3","4","5","6","7","8"],function(a,b,c,d,e,f,g,h){var i=function(a,b){return function(){var c=a.dom.getParent(a.selection.getStart(),"UL,OL,DL");return c&&c.nodeName===b}},j=function(a){a.on("BeforeExecCommand",function(b){var c,f=b.command.toLowerCase();if("indent"===f?d.indentSelection(a)&&(c=!0):"outdent"===f&&e.outdentSelection(a)&&(c=!0),c)return a.fire("ExecCommand",{command:b.command}),b.preventDefault(),!0}),a.addCommand("InsertUnorderedList",function(b,c){f.toggleList(a,"UL",c)}),a.addCommand("InsertOrderedList",function(b,c){f.toggleList(a,"OL",c)}),a.addCommand("InsertDefinitionList",function(b,c){f.toggleList(a,"DL",c)})},k=function(a){a.addQueryStateHandler("InsertUnorderedList",i(a,"UL")),a.addQueryStateHandler("InsertOrderedList",i(a,"OL")),a.addQueryStateHandler("InsertDefinitionList",i(a,"DL"))},l=function(a){a.on("keydown",function(b){9!==b.keyCode||c.metaKeyPressed(b)||a.dom.getParent(a.selection.getStart(),"LI,DT,DD")&&(b.preventDefault(),b.shiftKey?e.outdentSelection(a):d.indentSelection(a))})},m=function(a){var c=function(c){return function(){var d=this;a.on("NodeChange",function(a){var e=b.grep(a.parents,h.isListNode);d.active(e.length>0&&e[0].nodeName===c)})}},d=function(a,c){var d=a.settings.plugins?a.settings.plugins:"";return b.inArray(d.split(/[ ,]/),c)!==-1};d(a,"advlist")||(a.addButton("numlist",{title:"Numbered list",cmd:"InsertOrderedList",onPostRender:c("OL")}),a.addButton("bullist",{title:"Bullet list",cmd:"InsertUnorderedList",onPostRender:c("UL")})),a.addButton("indent",{icon:"indent",title:"Increase indent",cmd:"Indent",onPostRender:function(b){var c=b.control;a.on("nodechange",function(){for(var b=a.selection.getSelectedBlocks(),d=!1,e=0,f=b.length;!d&&e<f;e++){var g=b[e].nodeName;d="LI"===g&&h.isFirstChild(b[e])||"UL"===g||"OL"===g||"DD"===g}c.disabled(d)})}})};return a.add("lists",function(a){return m(a),g.setup(a),a.on("init",function(){j(a),k(a),a.getParam("lists_indent_on_tab",!0)&&l(a)}),{backspaceDelete:function(b){g.backspaceDelete(a,b)}}}),function(){}}),d("0")()}();