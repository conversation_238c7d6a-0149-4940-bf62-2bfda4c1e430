#Enable browser caching on launch
#<IfModule mod_expires.c>
  #ExpiresActive On
  #ExpiresByType image/jpg "access plus 1 year"
  #ExpiresByType image/jpeg "access plus 1 year"
  #ExpiresByType image/gif "access plus 1 year"
  #ExpiresByType image/png "access plus 1 year"
  #ExpiresByType image/svg+xml "access plus 1 year"
  #ExpiresByType text/css "access plus 1 year"
  #ExpiresByType text/html "access plus 1 year"
  #ExpiresByType application/pdf "access plus 1 year"
  #ExpiresByType text/x-javascript "access plus 1 year"
  #ExpiresByType application/x-javascript "access plus 1 year"
  #ExpiresByType application/x-shockwave-flash "access plus 1 year"
  #ExpiresByType image/x-icon "access plus 1 year"
  #ExpiresDefault "access plus 1 year"
#</IfModule>

#GZIP Compression
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
  AddOutputFilterByType DEFLATE application/x-font
  AddOutputFilterByType DEFLATE application/x-font-opentype
  AddOutputFilterByType DEFLATE application/x-font-otf
  AddOutputFilterByType DEFLATE application/x-font-truetype
  AddOutputFilterByType DEFLATE application/x-font-ttf
  AddOutputFilterByType DEFLATE application/x-javascript
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE font/opentype
  AddOutputFilterByType DEFLATE font/otf
  AddOutputFilterByType DEFLATE font/ttf
  AddOutputFilterByType DEFLATE image/svg+xml
  AddOutputFilterByType DEFLATE image/x-icon
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/javascript
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/xml

  #Remove browser bugs (only needed for really old browsers)
  BrowserMatch ^Mozilla/4 gzip-only-text/html
  BrowserMatch ^Mozilla/4\.0[678] no-gzip
  BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
  Header append Vary User-Agent
</IfModule>

#Disable iframe access outside of this domain
<IfModule mod_headers.c>
  Header always append X-Frame-Options SAMEORIGIN
</IfModule>

#Referrer Policy 
<IfModule mod_headers.c>
  Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

#Disable directory listing
Options -Indexes

#HSTS - Uncomment if site has SSL (and set session.cookie_secure = 1)
#Cloudflare - if site is using cloudflare, HSTS settings need to be updated through there instead
#<IfModule mod_headers.c>
  #Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
#</IfModule>

#Server settings
php_value session.cookie_httponly 1
php_value session.cookie_secure 0
php_value upload_max_filesize 10M
php_value post_max_size 20M
php_value max_input_time 300
php_value max_execution_time 300
php_value memory_limit 120M

RewriteEngine On
RewriteBase /

#Secure php.ini and .htaccess
RewriteRule ^(php\.ini|\.htaccess) - [NC,F]

#Redirect capitalized URLs to all lowercase - NOTE: not tested with short URLs (ie: website.com/NbjqWo)
#RewriteCond expr "tolower(%{REQUEST_URI}) =~ /(.*)/"
#RewriteRule [A-Z] https://%{HTTP_HOST}%1 [R=301,L]

#Redirect home straight to landing page
#RewriteCond %{REQUEST_URI} ^/home(\/*)$ [OR]
#RewriteCond %{HTTP_HOST} ^website\.com/home(\/*)$ [NC]
#RewriteRule ^home(\/*)$ https://%{HTTP_HOST}/ [L,R=301]

#Force trailing slash
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !index.php
RewriteCond %{REQUEST_URI} !(.*)/$
#RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1/ [L,R=301]
RewriteRule ^(.*)$ https://%{HTTP_HOST}/pga/$1/ [L,R=301]

#Redirect non-www to www:
#RewriteCond %{HTTP_HOST} !^www\. [NC]
#RewriteRule ^(.*)$ https://www.%{HTTP_HOST}/$1 [R=301,L]

#Redirect from test link 
#RewriteCond %{HTTP_HOST} !^www\.website\.com(\/*)$ [NC]
#RewriteRule ^(.*)$ https://www\.website\.com/$1 [L,R=301]

#Redirect to router file
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
#RewriteRule ^([^/\.]+) /index.php
RewriteRule ^([^/\.]+) /pga/index.php

#Force https (rackspace hosting):
#RewriteCond %{HTTPS} off 
#RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

DirectoryIndex index.html index.htm index.php index.cgi