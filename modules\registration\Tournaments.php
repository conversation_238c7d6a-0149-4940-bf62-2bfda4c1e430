<?php

//Browse tournaments
if(PAGE_ID == $_sitepages['tournaments']['page_id'] || (PARENT_ID == $_sitepages['tournaments']['page_id'] && PAGE_ID == '')){
	$pagebits = $SiteBuilder->get_pagebits((trim($_sitepages['tournaments']['slug']) != '' ? $_sitepages['tournaments']['slug'] : $_sitepages['tournaments']['page']));
		
	//Page defaults
	$page_id = $_sitepages['tournaments']['page_id'];
	// $panel_id = 60;
	$panel_id = 97;
	
	//Get tournament categories
	$categories = $Registration->get_categories(2);
	$search_category = (isset($_GET['category']) && is_numeric($_GET['category']) ? $_GET['category'] : NULL);
	$search_term = (isset($_GET['search']) && trim($_GET['search']) != '' ? $_GET['search'] : NULL);
	$start_date = (isset($_GET['year']) && !empty($_GET['year']) ? $_GET['year'].'-01-01' : date('Y').'-01-01');
	$end_date = (isset($_GET['year']) && !empty($_GET['year']) ?  $_GET['year'].'-12-31' : NULL);
	
	//Browse all
	if(PAGE_ID == $page_id){
		
		$tournaments = array();
		if(!empty($search_category)){
			$tournaments = $Registration->get_occurrences(array('Open'), 2, $search_category, $start_date, $end_date, $search_term);
		}else{
			$tournaments = $Registration->get_occurrences(array('Open'), 2, NULL, $start_date, $end_date, $search_term);
		}
				
	}
	
	//Browse selected
	if(PARENT_ID == $page_id && PAGE_ID == ''){
		if(!isset($pagebits[3]) || $pagebits[3] == ''){
			
			if(isset($pagebits[2]) && $pagebits[2] != ''){
				
				$event_bits = explode("-", $pagebits[2]);
				$occurrence_id = $event_bits[count($event_bits)-1];
				$event = $Registration->get_occurrence($occurrence_id);
				if(!empty($event) && $event['event_showhide'] == 0){
					$error404 = false;
					$event['page_url'] = $_sitepages['tournaments']['page_url'].$event['page'].'-'.$occurrence_id.'/';
					
					//Correct path
					if($event['page'].'-'.$occurrence_id != $pagebits[2]){
						header("HTTP/1.1 301 Moved Permanently");
						header('Location: ' .$event['page_url']);
						exit();
					}
					
					//Get facility
					$query = $db->query("SELECT * FROM `facilities` WHERE `facility_id` = ?", array($event['facility_id']));
					if($query && !$db->error() && $db->num_rows()){
						$result = $db->fetch_array();
						$facility = $result[0];
						
						//Format address
						$facility['full_address'] = array();
						if(trim($facility['address2']) != '') {
							$facility['full_address'][] = $facility['address2'];
						}
						if(trim($facility['address1']) != '') {
							$facility['full_address'][] = $facility['address1'];
						}
						if(trim($facility['city']) != '') {
							$facility['full_address'][] = $facility['city'];
						}
						if(trim($facility['province']) != '') {
							$facility['full_address'][] = $facility['province'];
						}
						$facility['full_address'] = implode(', ', $facility['full_address']);
						$facility['full_address'] .= (trim($facility['postal_code']) != '' ? ' '.$facility['postal_code'] : '');
						
					}
					
					//Set page
					$page = $SiteBuilder->get_page_content($page_id);
					$page['page_title'] = (!empty($event['category_name']) ? $event['category_name'] : 'Tournament');
					$page['description'] = format_date_range($event['start_date'], $event['end_date']);
					$page['content'] = '';
					$page['meta_canonical'] = $event['page_url'];
					$page['meta_title'] = ($event['meta_title'] != '' ? $event['meta_title'] : $event['event_name'].' | '.$page['meta_title']);
					$page['meta_description'] = ($event['meta_description'] != '' ? $event['meta_description'] : 'Register for '.$event['event_name']);
					if(isset($facility) && $facility['logo'] != '' && file_exists('images/logos/'.$facility['logo'])){
						$page['logo'] = $path.'images/logos/'.$facility['logo'];
					}
					if($event['banner_image'] != '' && file_exists('images/heroes/1920/'.$event['banner_image'])){
						$page['banner_image'] = $event['banner_image'];
						$page['banner_image_alt'] = $event['banner_image'];
					}else if($facility['image'] != '' && file_exists('images/heroes/1920/'.$facility['image'])){
						$page['banner_image'] = $facility['image'];
						$page['banner_image_alt'] = (trim($facility['image_alt']) != '' ? $facility['image_alt'] : $facility['facility_name']);
					}
					
					//Set breadcrumb
					array_pop($breadcrumbs);
					array_push($breadcrumbs, array('url' => $event['page_url'], 'name' => $event['event_name']));
					
					//Eligibility categories
					$event['eligibility_categories'] = $Registration->get_event_eligibility($event['event_id']);
					
					//Eligibility membership classes
					$event['event_membership_classes'] = $Registration->get_event_membership_classes($event['event_id']);
					
					//Sponsors
					$get_sponsors = $Registration->get_occurrence_sponsors($occurrence_id);
					$event['sponsors'] = [];
					foreach($get_sponsors as $sponsor){
						$event['sponsors'][$sponsor['type'] ?: 'Tournament'][] = $sponsor;
					}
					
					//Attached gallery
					if(!empty($event['gallery_id'])){
						$gallery = $SiteBuilder->get_attached_gallery($event['gallery_id']);
						$page['page_panels']['facility_gallery'] = array(
							'panel_id' => 'facility_gallery',
							'panel_type' => 'gallery',
							'title' => $gallery['name'],
							'show_title' => true,
							'gallery_id' => $event['gallery_id'],
							'gallery_limit' => '',
							'gallery' => $gallery
						);
					}
					
					//Attached cta
					if(!empty($event['cta_id'])){
						$cta = $SiteBuilder->get_attached_cta($event['cta_id']);
						$page['page_panels']['cta'] = array(
							'panel_id' => 'cta',
							'panel_type' => 'cta',
							'title' => $cta['title'],
							'show_title' => true,
							'cta_id' => 2,
							'cta' => $cta
						);
					}

					//Upcoming events
					$page['page_panels']['events'] = array(
						'panel_id' => 'events',
						'panel_type' => 'events',
						'title' => 'More Upcoming Events',
						'show_title' => 1,
						'content' => ''
					);
					
					//Log page view
					$logview = $Registration->log_event_view($event['event_id'], NULL, session_id());
					
				}
				
			}
		}
	}
}

?>