<?php 

//TODO - edit link 

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");
require_once("../../../core/classes/ImageUpload.class.php");

//Define vars
$image    = $_FILES['file'] ?? false;
$imgtype  = getimagesize($image['tmp_name'])[2];
$response = ['errors' => false, 'content' => ''];

//Dynamic vars
$record_db  = $_POST['record_db'] ?? 'galleries_photos';
$record_id  = $_POST['record_id'] ?? 'photo_id';
$parent_id  = $_POST['parent_id'] ?? 'gallery_id';
$imagedir   = '../../../'.($_POST['imagedir'] ?? 'images/galleries/');
$crop_type  = $_POST['crop_type'] ?? 'gallery-photo';
$i          = $_POST['i'] ?? '';
$photo_name = (!empty($_POST['photo_name']) ? clean_url($_POST['photo_name']).'-' : '').$i;
$edit_url   = $_POST['edit_url'] ?? '';


// Validate user session
if (!defined('USER_LOGGED_IN') || !USER_LOGGED_IN || $_POST['xssid'] != $_COOKIE['xssid']) {
	$response['errors'][] = 'User login has expired.  Please refresh the page and log in to upload files.';
}

// Validate name
if (empty($image['name'])) {
	$response['errors'][] = 'No photo was detected.  Refresh the page and try again.';
}
if (is_array($image['name'])) {
	$response['errors'][] = 'Mutliple photos detected.  Refresh the page and try again.';
}
// Validate size
if (($image['size'] ?? 0) >= $_max_filesize['bytes']) {
	$response['errors'][] = 'Photo is too large.  Uploaded images must be smaller than '.$_max_filesize['megabytes'].'.';
}

// Validate type
if ($imgtype != IMAGETYPE_JPEG && $imgtype != IMAGETYPE_GIF && $imgtype != IMAGETYPE_PNG) {
	$response['errors'][] = 'Photo is not an accepted type.  Images must be in .jpg, .png, or .gif format.';
}

// Upload file
if (!$response['errors']) {
	$db->new_transaction();

	//Save image in DB first
	$db->insert($record_db, [
		$parent_id     => $_POST[$parent_id],
		'last_updated' => date('Y-m-d H:i:s')
	]);

	if(!$db->error()){
		$item_id         = $db->insert_id();
		$CMSUploader     = new CMSUploader($crop_type, $imagedir);
		$_FILES['image'] = $image;

		try{
			$images  = $CMSUploader->bulk_upload($photo_name);
			$newname = $images['image'];
		}catch(Exception $e){}

		if($newname) {

			//Update db
			$db->query("UPDATE `$record_db` SET `image` = ?, `last_updated` = ? WHERE `$record_id` = ?", [$newname, date('Y-m-d H:i:s'), $item_id]);

			if(!$db->error()) {
				$db->commit();

				header('HTTP/1.1 200 OK',true,200);
				$response['image'] = $newname;
				$response['id'] = $item_id;
				$response['content'] = '<li class="dz-uploaded-image">
					<input type="checkbox" name="'.$record_id.'[]" value="'.$item_id.'" id="gallery-image-'.$item_id.'" class="container-checkbox" checked />
					<label for="gallery-image-'.$item_id.'">
						<img src="'.$root.preg_replace('/\.\.\//', '', $imagedir).'thumbs/'.$newname.'" alt="Photo" />
						<a href="#" class="sort-handler"><i class="fas fa-arrows-alt"></i></a>
						<div class="buttons">
							<a href="'.$root.preg_replace('/\.\.\//', '', $imagedir).$newname.'" class="light-gallery"><i class="fas fa-expand"></i></a>
							<a href="'.preg_replace('(%item_id%)', $item_id, $edit_url).'" class="edit-btn"><i class="fas fa-edit"></i></a>
							<a href="#" class="delete-btn confirm-submit-btn" data-confirm="Are you sure you want to delete this image?" data-confirm-callback="deleteGalleryImage" data-id="'.$item_id.'"><i class="fas fa-trash-alt"></i></a>
						</div>
					</label>
				</li>';
			}

		// Upload failed
		}else{
			$response['errors'][] = 'Photo could not be uploaded.  Please try again later.';
		}
	}

	// DB failed
	if($db->error()) {
		$response['errors'][] = 'Photo could not be processed.  Please try again later.';
	}
}

//Errors occurred
if($response['errors']) {
	header("HTTP/1.0 400 Bad Request");
	$response['errors'][] = 'Click to Remove.';
	echo implode(' ', $response['errors']);

//Return json response
} else {
	echo json_encode($response);
}

?>