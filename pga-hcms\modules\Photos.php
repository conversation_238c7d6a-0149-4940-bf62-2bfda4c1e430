<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('galleries_photos');
	$CMSBuilder->set_widget($_cmssections['photos'], 'Total Gallery Photos', $total_records, 'fas fa-image', $sitemap[$_cmssections['galleries']]['page_url']);
}

if(SECTION_ID == $_cmssections['photos']){

	//Define vars
	$record_db   = 'galleries_photos';
	$record_id   = 'photo_id';
	$record_name = 'Gallery Photo';
	
	//Validation
	$errors   = false;
	$required = [];
	$required_fields = ['gallery_id'];

	//Image Uploader
	$imagedir     = "../images/galleries/";
	$CMSUploader  = new CMSUploader('gallery-photo', $imagedir);

	//Filtering
	$where      = '';
	$params     = [];
	$gallery_id = $_GET['gallery_id'] ?? false;

	//Redirects
	$gallerysection = $CMSBuilder->get_section($_cmssections['galleries']); 
	$redirect       = $gallerysection['page_url']. "?action=edit&item_id=".($gallery_id ?: $_POST['gallery_id'] ?? '');


	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Filter query	
	}else if ($gallery_id) {
		$where = " WHERE `$record_db`.`gallery_id` = ?";
		$params[] = $gallery_id;
	}

	//Get Records
	$db->query("SELECT `$record_db`.*, `galleries`.`name` AS `gallery_name` FROM `$record_db` 
	LEFT JOIN `galleries` ON `$record_db`.`gallery_id` = `galleries`.`gallery_id` $where 
	ORDER BY `galleries`.`name` ASC, `$record_db`.`ordering`", $params);
	$records_arr = $db->fetch_assoc($record_id);
	foreach ($records_arr as $item_id => &$record) {
		$record['image'] = check_file($record['image'], $imagedir);
		$record['back_link'] = $gallerysection['page_url'].'?action=edit&item_id='.$record['gallery_id']; // sticky footer
		unset($record);
	}

	if($db->error()) {
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);
	}


	//Fetch galleries
	$db->query("SELECT * FROM `galleries` ORDER BY `name` ASC");
	$galleries = $db->fetch_assoc('gallery_id');


	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .$redirect);
			exit();
		}
	}


	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));

		//Delete images
		if(!$db->error()) {
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

		} else {
			$CMSBuilder->set_system_alert('Unable to delete record.', false);
		}

		header("Location: " .$redirect);
		exit();


	//Save item
	}else if(isset($_POST['save'])){

		//Required fields
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Validate image
		if(empty($records_arr[ITEM_ID]['image']) && empty($_FILES['image']['name'])) {
			$errors[] = 'Please select an image to upload.';
			$required[] = 'image';
		} else if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']) {
			$errors[] = 'Image filesize is too large.';
			$required[] = 'image';
		}

		if(!$errors){

			//Delete old images
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}
			
			//Upload banner image
			try{
				$images = $CMSUploader->bulk_upload($galleries[$_POST['gallery_id']]['name'], $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			//Insert to db
			$params = array(
				ITEM_ID,
				$_POST['gallery_id'],
				$_POST['caption'],
				$images['image'],
				$_POST['image_alt'],
				!isset($_POST['showhide']),
				$_POST['ordering'],
				date("Y-m-d H:i:s"),

				$_POST['gallery_id'],
				$_POST['caption'],
				$images['image'],
				$_POST['image_alt'],
				!isset($_POST['showhide']),
				$_POST['ordering'],
				date("Y-m-d H:i:s")
			);

			$db->query("INSERT INTO `$record_db`(`$record_id`, `gallery_id`, `caption`, `image`, `image_alt`, `showhide`, `ordering`, `last_updated`) VALUES(?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `gallery_id` = ?, `caption` = ?, `image` = ?, `image_alt` = ?, `showhide` = ?, `ordering` = ?, `last_updated` = ?", $params);
			if(!$db->error()){
				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert('Photo was successfully saved.', true);
					header("Location: " .$gallerysection['page_url']. "?action=edit&item_id=".$_POST['gallery_id']);
					exit();
				}

			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST as $key => $data){
				$row[$key] = $data;
			}
		}


	//Handle images
	}else{
		include('modules/CropImages.php');
	}
}

?>