<?php  

include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

$referer_url = get_referer_url();
$page_id = (isset($referer_url['path']) ? $SiteBuilder->get_page_id($referer_url['path']) : NULL);
$parent_id = (isset($referer_url['path']) ? $SiteBuilder->get_parent_id($referer_url['path']) : NULL);
$service = ($_POST['service'] ?? NULL);
$share_url = ($_POST['url'] ?? NULL);

$referer_url['pathbits'] = array_values(array_filter($referer_url['pathbits']));
$pagebits = $referer_url['pathbits'][count($referer_url['pathbits'])-1];
$pagebits = explode('-', $pagebits);
$item_id = $pagebits[count($pagebits)-1];	

if($_POST['xid'] == $_COOKIE['xid']){
	if($parent_id == $_sitepages['blog']['page_id'] && empty($page_id)){
		if($Blog->share_entry($item_id, $service, $share_url)){
			echo 'success';
		}else{
			echo 'error';	
		}
	}	
}else{
	echo 'Please make sure cookies are enabled on your browser then try again.';
}

?>