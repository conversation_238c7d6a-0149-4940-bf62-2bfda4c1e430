<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

// if(SECTION_ID == 73 || SECTION_ID == 71){
if(SECTION_ID == $_cmssections['transactions-payments'] || SECTION_ID == $_cmssections['transactions-refunds']){
	
	//Set vars
	$confirm = false;
	$errors = false;
	$required = array();
	
	//Set record type
	define('RECORD', (isset($_GET['record']) ? $_GET['record'] : ''));

	if(RECORD == 'registration'){
		$record_db = 'reg_registrations';
		$record_id = 'registration_id';
		$record_name = 'Registration';
		// $section_id = 69;
		$section_id = 73;

	}else if(RECORD == 'invoice'){
		$record_db = 'invoices';
		$record_id = 'invoice_id';
		$record_name = 'Invoice';
		// $section_id = 80;
		$section_id = 79;

	}else{
		// $CMSBuilder->set_system_alert('Requested item was not found!. Please select from the list below.', false);
		// header('Location:'.$sitemap[69]['page_url']);
		// header('Location:'.$sitemap[73]['page_url']);
		// exit();
		return;
	}

	//Get Payment Methods
	$payment_options = $Registration->get_payment_options();
		
	//Get record
	$row = array();
	$query = $db->query("SELECT `$record_db`.*, `$record_db`.`" .RECORD. "_number` AS `record_number`, `$record_db`.`" .RECORD. "_date` AS `record_date`, `$record_db`.`" .RECORD. "_total` AS `record_total`, `account_profiles`.`address1` AS `bill_address1`, `account_profiles`.`address2` AS `bill_address2`, `account_profiles`.`city` AS `bill_city`, `account_profiles`.`province` AS `bill_province`, `account_profiles`.`postalcode` AS `bill_postalcode`, `account_profiles`.`country` AS `bill_country` FROM `$record_db` ".
	"LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` ".
	"WHERE `$record_id` = ?", array(ITEM_ID));
	if($query && !$db->error() && $db->num_rows()){
		$result = $db->fetch_array();
		$row = $result[0];
		
		//Default values
		$row['total_paid'] = 0;
		$row['total_refunded'] = 0;
		$row['total_admin_fee'] = 0;
		$row['balance'] = 0;
		
		//Get payments
		$row['payments'] = array();
		$get_payments = $db->query("SELECT *, (SELECT SUM(`amount`) FROM `refunds` WHERE `payment_id` = `payments`.`payment_id` && `status` = 1) AS `refund_amount` FROM `payments` WHERE `$record_id` = ? ORDER BY `payment_date` DESC", array(ITEM_ID));
		if($get_payments && !$db->error() && $db->num_rows() > 0){
			$get_payments = $db->fetch_array();
			foreach($get_payments as $payrecord){
				$row['payments'][$payrecord['payment_id']] = $payrecord;
				if($payrecord['status'] == '1'){
					$row['total_paid'] += $payrecord['amount'];
					$row['total_refunded'] += $payrecord['refund_amount'];
					$row['total_admin_fee'] += $payrecord['admin_fee'];
				}
			}
		}
		
		//Payment processsing
		// if(SECTION_ID == 73){
		if(SECTION_ID == $_cmssections['transactions-payments']){

			//Set record id
			if(isset($_SESSION['cms']['payment']['record_id']) && $_SESSION['cms']['payment']['record_id'] != ITEM_ID){
				unset($_SESSION['cms']['payment']);	
			}
			$_SESSION['cms']['payment']['record_id'] = ITEM_ID;
			
			//Determine balance owing
			$row['balance'] = $row['record_total']-$row['total_paid']+$row['total_refunded'];
			if($row['balance'] <= 0){
				$CMSBuilder->set_system_alert($record_name.' has already been paid in full.', false);
				header('Location:'.$sitemap[$section_id]['page_url'].'?action=edit&item_id='.ITEM_ID);
				exit();
			}
			
			//Check for admin fee
			$row['event_type'] = NULL;
			$row['admin_fee'] = $reg_settings['admin_fee'];
			$admin_fee_type = $reg_settings['admin_fee_type'];			
											
			if(RECORD == 'registration'){
				$get_event = $db->query("SELECT `reg_events`.`event_id`, `reg_events`.`event_type`, `reg_events`.`admin_fee`, `reg_events`.`admin_fee_type` FROM `reg_events` LEFT JOIN `reg_attendees` ON `reg_events`.`event_id` = `reg_attendees`.`event_id` WHERE `reg_attendees`.`$record_id` = ? GROUP BY `$record_id`", array(ITEM_ID));
				if($get_event && !$db->error() && $db->num_rows()){
					$event = $db->fetch_array();
					$row['event_type'] = $event[0]['event_type'];

					if(!is_null($event[0]['admin_fee'])){
						$row['admin_fee'] = $event[0]['admin_fee'];
						$admin_fee_type = $event[0]['admin_fee_type'];							
					}
				}

			}else if(RECORD == 'invoice'){					

				//Check for hio admin fee override
				$db->query("SELECT `$record_id` FROM `hio` WHERE `$record_id` = ?", array($row[$record_id]));
				if($db->num_rows() && $row['balance'] > $reg_settings['max_payment_amount']){

					$db->query("SELECT * FROM `hio_settings` WHERE `id`=1");
					if($db->num_rows()){
						$hiofees = $db->fetch_array()[0];
						if(!is_null($hiofees['admin_fee'])){
							$row['admin_fee'] = $hiofees['admin_fee'];
							$admin_fee_type = $hiofees['admin_fee_type'];
						}
					}

				}
			}
							
			//Calculate admin fee
			$admin_fee_percentage = 0;
			if($admin_fee_type == 'Percent'){
				$admin_fee_percentage = $row['admin_fee'];
				$row['admin_fee'] = number_format($row['balance']*($admin_fee_percentage/100), 2, '.', '');
			}

			//Fetch billing profiles
			$billing_profiles = $Account->get_account_billing_profiles($row['account_id']);
			if(isset($row['billing_id']) && array_key_exists($row['billing_id'], $billing_profiles) && !isset($_SESSION['cms']['payment']['billing_id'])){
				$_SESSION['cms']['payment']['billing_id'] = $row['billing_id'];
			}

			//Validate payment
			if(isset($_POST['continue'])){
				
				//Set state/region
				if($_POST['bill_country'] == 'US'){
					$_POST['bill_province'] = $_POST['bill_state'];
				}else if($_POST['bill_country'] != 'CA' && $_POST['bill_country'] != 'US' && $_POST['bill_country'] != ''){
					$_POST['bill_province'] = $_POST['bill_region'];
				}

				//Validation
				$required_fields = array('first_name', 'last_name', 'email', 'phone', 'payment_type', 'payment_amount');
				if($_POST['payment_type'] == 'Credit Card'){
					if(!isset($_POST['billing_id']) || empty($_POST['billing_id'])){
						$required_fields = array_merge($required_fields, array('bill_address1', 'bill_city', 'bill_province', 'bill_postalcode', 'bill_country', 'ccname', 'ccnumber', 'exp_month', 'exp_year', 'cvv'));	
					}else{
						$required_fields[] = 'billing_id';
					}
				}
				foreach($required_fields as $field){
					if(!isset($_POST[$field]) || $_POST[$field] == ''){
						$errors[0] = 'Please fill out all the required fields.';
						$required[] = $field;
					}
					if($field == 'email' && !checkmail($_POST[$field])) {
						$errors[] = 'Please enter a valid email address.';
						$required[] = $field;
					}
					if($field == 'phone'){
						if(!detectPhone($_POST[$field])){
							$errors[] = 'Please enter a valid phone number.';
							$required[] = $field;
						}else{
							$_POST['phone'] = formatPhoneNumber($_POST['phone']);
						}
					}
					if($field == 'payment_amount'){
						if(!is_numeric($_POST[$field]) || $_POST[$field] <= 0 || $_POST[$field] == 0.00){
							$errors[] = 'Please enter a valid payment amount.';
							$required[] = $field;
						}else if($_POST[$field] > $row['balance']){
							$errors[] = 'Payment amount cannot be greater than balance due ($' .number_format($row['balance'], 2). ').';
							$required[] = $field;
						}
					}
					if($field == 'admin_fee'){
						if(!is_numeric($_POST[$field])){
							$errors[] = 'Please enter a valid service fee.';
							$required[] = $field;
						}
					}
					if($field == 'ccnumber' && $_POST[$field] != ''){
						$valid_card = false;
						$accepted_cards = array();
						foreach($payment_options as $payopt){
							$accepted_cards[] = $payopt['name'];
							if(get_card_type($_POST[$field]) == $payopt['type']){
								$valid_card = true;
							}
						}
						if(!$valid_card){
							$errors[] = 'Invalid card type. We currently only accept '.implode(' &amp; ', $accepted_cards).'.';
							$required[] = $field;	
						}
					}
				}

				//Save session vars
				$_SESSION['cms']['payment']['first_name'] = $_POST['first_name'];
				$_SESSION['cms']['payment']['last_name'] = $_POST['last_name'];
				$_SESSION['cms']['payment']['email'] = $_POST['email'];
				$_SESSION['cms']['payment']['phone'] = $_POST['phone'];
				$_SESSION['cms']['payment']['payment_type'] = $_POST['payment_type'];
				$_SESSION['cms']['payment']['payment_amount'] = $_POST['payment_amount'];
				$_SESSION['cms']['payment']['admin_fee'] = ($_POST['payment_type'] == 'Credit Card' ? $_POST['admin_fee'] : 0);
				$_SESSION['cms']['payment']['notes'] = $_POST['notes'];

				//Set billing
				if(!isset($_POST['billing_id']) || empty($_POST['billing_id'])){
					$_SESSION['cms']['payment']['billing_id'] = '';
					$_SESSION['cms']['payment']['ref_number'] = '';
					$_SESSION['cms']['payment']['bill_address1'] = $_POST['bill_address1'];
					$_SESSION['cms']['payment']['bill_address2'] = $_POST['bill_address2'];
					$_SESSION['cms']['payment']['bill_city'] = $_POST['bill_city'];
					$_SESSION['cms']['payment']['bill_province'] = $_POST['bill_province'];
					$_SESSION['cms']['payment']['bill_postalcode'] = $_POST['bill_postalcode'];
					$_SESSION['cms']['payment']['bill_country'] = $_POST['bill_country'];
					$_SESSION['cms']['payment']['ccname'] = $_POST['ccname'];
					$_SESSION['cms']['payment']['cctype'] = get_card_type($_POST['ccnumber']);
					$_SESSION['cms']['payment']['ccnumber'] = NULL;
					$_SESSION['cms']['payment']['exp_month'] = $_POST['exp_month'];
					$_SESSION['cms']['payment']['exp_year'] = $_POST['exp_year'];
					$_SESSION['cms']['payment']['ccsave'] = (isset($_POST['ccsave']) ? 1 : 0);

				//Use profile
				}else{
					$profile = $billing_profiles[$_POST['billing_id']];

					$_SESSION['cms']['payment']['billing_id'] = $_POST['billing_id'];
					$_SESSION['cms']['payment']['ref_number'] = $profile['ref_number'];
					$_SESSION['cms']['payment']['bill_address1'] = $profile['bill_address1'];
					$_SESSION['cms']['payment']['bill_address2'] = $profile['bill_address2'];
					$_SESSION['cms']['payment']['bill_city'] = $profile['bill_city'];
					$_SESSION['cms']['payment']['bill_province'] = $profile['bill_province'];
					$_SESSION['cms']['payment']['bill_postalcode'] = $profile['bill_postalcode'];
					$_SESSION['cms']['payment']['bill_country'] = $profile['bill_country'];
					$_SESSION['cms']['payment']['ccname'] = $profile['ccname'];
					$_SESSION['cms']['payment']['cctype'] = $profile['cctype'];
					$_SESSION['cms']['payment']['ccnumber'] = $profile['ccnumber']; //Last 4 digits only
					$_SESSION['cms']['payment']['exp_month'] = substr($profile['ccexpiry'], 0, 2);
					$_SESSION['cms']['payment']['exp_year'] = substr($profile['ccexpiry'], -2, 2);
					$_SESSION['cms']['payment']['ccsave'] = 0;
				}

				//Send to confirm page
				if(empty($errors)){
					$confirm = true;
				}

			}

			//Retrive sensitive info
			$ccnumber = (isset($_POST['ccnumber']) ? $_POST['ccnumber'] : '');
			$cvv = (isset($_POST['cvv']) ? $_POST['cvv'] : '');
			if(!empty($_SESSION['cms']['payment']['billing_id']) && !empty($_SESSION['cms']['payment']['ccnumber'])){
				$ccnumber = $_SESSION['cms']['payment']['ccnumber']; //Last 4 digits only
			}

			//Process payment
			if(isset($_POST['process'])){
				$success = false;

				//Double check there is a balance
				if($row['balance'] > 0){
					$ordertotal = number_format($_SESSION['cms']['payment']['payment_amount']+$_SESSION['cms']['payment']['admin_fee'], 2, '.', '');

					//Credit card payment
					if($_SESSION['cms']['payment']['payment_type'] == 'Credit Card'){
						$params = array(
							ITEM_ID,
							$_SESSION['cms']['payment']['bill_address1'],
							$_SESSION['cms']['payment']['bill_address2'],
							$_SESSION['cms']['payment']['bill_city'],
							$_SESSION['cms']['payment']['bill_province'],
							$_SESSION['cms']['payment']['bill_postalcode'],
							$_SESSION['cms']['payment']['bill_country'],
							$_SESSION['cms']['payment']['ccname'],
							$_SESSION['cms']['payment']['cctype'],
							substr($ccnumber, -4, 4),
							$_SESSION['cms']['payment']['exp_month'].$_SESSION['cms']['payment']['exp_year'],
							$_SESSION['cms']['payment']['payment_type'],
							$_SESSION['cms']['payment']['admin_fee'],
							$_SESSION['cms']['payment']['payment_amount'],
							$_SESSION['cms']['payment']['notes'],
							USER_LOGGED_IN,
							date("Y-m-d H:i:s")
						);
						$query = $query = $db->query("INSERT INTO `payments`(`$record_id`, `bill_address1`, `bill_address2`, `bill_city`, `bill_province`, `bill_postalcode`, `bill_country`, `ccname`, `cctype`, `ccnumber`, `ccexpiry`, `payment_type`, `admin_fee`, `amount`, `notes`, `processed_by`, `payment_date`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
						if($query && !$db->error()){
							$order_id = $db->insert_id();
							$ordernum = 'A'.date("ymd").'-'.str_pad($order_id, 5, '0', STR_PAD_LEFT);

							//Format request using billing profile
							if(!empty($_SESSION['cms']['payment']['billing_id'])){
								$request = array(
									'type' => 'newOrderRequest',
									'ordernum' => $ordernum,
									'ordertotal' => $ordertotal,
									'taxes' => 0,
									'ref_number' => $_SESSION['cms']['payment']['ref_number'],
									'ccsave' => 0
								);

							//Format request with new billing data
							}else{
								$request = array(
									'type' => 'newOrderRequest',
									'name' => $_SESSION['cms']['payment']['first_name'].' '.$_SESSION['cms']['payment']['last_name'],
									'email' => $_SESSION['cms']['payment']['email'],
									'phone' => $_SESSION['cms']['payment']['phone'],
									'bill_address1' => $_SESSION['cms']['payment']['bill_address1'],
									'bill_address2' => $_SESSION['cms']['payment']['bill_address2'],
									'bill_city' => $_SESSION['cms']['payment']['bill_city'],
									'bill_province' => $_SESSION['cms']['payment']['bill_province'],
									'bill_postalcode' => $_SESSION['cms']['payment']['bill_postalcode'],
									'bill_country' => $_SESSION['cms']['payment']['bill_country'],
									'ccnumber' => $ccnumber,
									'exp_month' => $_SESSION['cms']['payment']['exp_month'],
									'exp_year' => $_SESSION['cms']['payment']['exp_year'],
									'cvv' => $cvv,
									'ordernum' => $ordernum,
									'ordertotal' => $ordertotal,
									'taxes' => 0,
									'ccsave' => $_SESSION['cms']['payment']['ccsave']
								);
							}

							//Send request
							include("../includes/orbital/request.php");

							//Success response
							if($trxnResponse['status'] == 1 && $trxnResponse['approved'] == 1){
								$success = true;
								$status = '1';
								$paid = '1';
							}else{
								$success = false;
								$status = '0';
								$paid = '0';
								$errors[] = $trxnResponse['message'];
							}

							//Update payment response
							$response = array(
								$status,
								$trxnResponse['response_code'],
								$trxnResponse['txn_num'],
								$trxnResponse['txn_tag'],
								$trxnResponse['auth_code'],
								$trxnResponse['cvd_code'],
								$trxnResponse['message'],
								$ordernum,
								$order_id
							);
							$query = $db->query("UPDATE `payments` SET `status`=?, `response_code`=?, `txn_num`=?, `txn_tag`=?, `auth_code`=?, `cvd_code`=?, `message`=?, `payment_number`=? WHERE `payment_id`=?", $response);
							if(!$query || $db->error()){
								trigger_error('Error updating payment response for ' .$ordernum. ': '.$db->error());
							}

							//Success
							if($success){

								//Update record as paid
								if($_SESSION['cms']['payment']['payment_amount'] >= $row['balance']){
									$query = $db->query("UPDATE `$record_db` SET `paid`=? WHERE `$record_id`=?", array(1, ITEM_ID));
									if(!$query || !$db->error()){
										trigger_error('Error updating payment status for ' .$ordernum. ': '.$db->error());
									}
								}

								//Save billing profile to database if necessary
								if($_SESSION['cms']['payment']['ccsave'] && !empty($ccnumber)){
									$params = array(
										$row['account_id'],
										$_SESSION['cms']['payment']['bill_address1'],
										$_SESSION['cms']['payment']['bill_address2'],
										$_SESSION['cms']['payment']['bill_city'],
										$_SESSION['cms']['payment']['bill_province'],
										$_SESSION['cms']['payment']['bill_postalcode'],
										$_SESSION['cms']['payment']['bill_country'],
										$_SESSION['cms']['payment']['ccname'],
										$_SESSION['cms']['payment']['cctype'],
										substr($ccnumber, -4, 4),
										$_SESSION['cms']['payment']['exp_month'].$_SESSION['cms']['payment']['exp_year'],
										date("Y-m-d H:i:s"),
										date("Y-m-d H:i:s")
									);

									//Profile was already created with order processing
									if(isset($trxnResponse['ref_number']) && !empty($trxnResponse['ref_number'])){
										$params[] = $trxnResponse['ref_number'];

									//Profile has not been created so send the request now	
									}else{

										//Format request
										$request = array(
											'type' => 'profileAddRequest',
											'name' => $_SESSION['cms']['payment']['ccname'],
											'email' => $_SESSION['cms']['payment']['email'],
											'phone' => $_SESSION['cms']['payment']['phone'],
											'bill_address1' => $_SESSION['cms']['payment']['bill_address1'],
											'bill_address2' => $_SESSION['cms']['payment']['bill_address2'],
											'bill_city' => $_SESSION['cms']['payment']['bill_city'],
											'bill_province' => $_SESSION['cms']['payment']['bill_province'],
											'bill_country' => $_SESSION['cms']['payment']['bill_country'],
											'bill_postalcode' => $_SESSION['cms']['payment']['bill_postalcode'],
											'ccnumber' => $ccnumber,
											'exp_month' => $_SESSION['cms']['payment']['exp_month'],
											'exp_year' => $_SESSION['cms']['payment']['exp_year'],
											'ref_number' => ''
										);

										//Process request
										include("../includes/orbital/request.php");
										if($trxnResponse['status'] == 1){
											$params[] = $trxnResponse['ref_number'];
										}
									}

									//Save to database
									if(!empty($trxnResponse['ref_number'])){
										$query = $db->query("INSERT INTO `account_billing_profiles`(`account_id`, `bill_address1`, `bill_address2`, `bill_city`, `bill_province`, `bill_postalcode`, `bill_country`, `ccname`, `cctype`, `ccnumber`, `ccexpiry`, `date_added`, `last_updated`, `ref_number`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $params);
										if(!$query || $db->error()){
											trigger_error('Error inserting billing profile: '.$db->error());
										}
									}
								}
								
								//Send receipt
								$order_data = $_SESSION['cms']['payment'];
								$order_data['record_name'] = $record_name.' No.';
								$order_data['record_number'] = $row['record_number'];
								$order_data['payment_date'] = date('Y-m-d H:i:s');
								$order_data['payment_number'] = $ordernum;
								$order_data['ccnumber'] = substr($ccnumber, -4, 4);
								$order_data['ccexpiry'] = $_SESSION['cms']['payment']['exp_month'].$_SESSION['cms']['payment']['exp_year'];
								$order_data['ordertotal'] = $ordertotal;
								$order_data = array_merge($order_data, $trxnResponse);
								$payment_receipt = $Registration->payment_receipt($order_data);
								$send_email = send_email($_SESSION['cms']['payment']['email'], 'Payment Confirmation', $payment_receipt);
								
								//Clear data
								unset($ccnumber);
								unset($cvv);
								unset($_SESSION['cms']['payment']);

								//Send back to record page
								$CMSBuilder->set_system_alert($record_name.' payment has been successfully processed'.($send_email ? ' and email confirmation has been sent' : '').'.', true);
								header('Location: '.$sitemap[$section_id]['page_url'].'?action=edit&item_id='.ITEM_ID);
								exit();
							}

						//Insert error
						}else{
							$errors[] = 'Unable to insert payment: '.$db->error();
						}

					//No processing required	
					}else{

						$db->new_transaction();

						//Insert payment data
						$params = array(
							ITEM_ID,
							$_SESSION['cms']['payment']['payment_type'],
							$ordertotal,
							$_SESSION['cms']['payment']['notes'],
							USER_LOGGED_IN,
							date("Y-m-d H:i:s")
						);
						$query = $query = $db->query("INSERT INTO `payments`(`$record_id`, `payment_type`, `amount`, `notes`, `processed_by`, `payment_date`) VALUES(?,?,?,?,?,?)", $params);
						$order_id = $db->insert_id();
						$ordernum = 'A'.date("ymd").'-'.str_pad($order_id, 5, '0', STR_PAD_LEFT);

						//Update payment response
						$update = $db->query("UPDATE `payments` SET `status`=?, `payment_number`=? WHERE `payment_id`=?", array(1, $ordernum, $order_id));

						//Update record as paid
						if($_SESSION['cms']['payment']['payment_amount'] >= $row['balance']){
							$query = $db->query("UPDATE `$record_db` SET `paid`=? WHERE `$record_id`=?", array(1, ITEM_ID));
						}

						//No errors
						if(!$db->error()){
							$db->commit();
							
							//Send receipt
							$order_data = $_SESSION['cms']['payment'];
							$order_data['record_name'] = $record_name.' No.';
							$order_data['record_number'] = $row['record_number'];
							$order_data['payment_number'] = $ordernum;
							$order_data['payment_date'] = date('Y-m-d H:i:s');
							$order_data['ordertotal'] = $ordertotal;
							$payment_receipt = $Registration->payment_receipt($order_data);
							$send_email = send_email($_SESSION['cms']['payment']['email'], 'Payment Confirmation', $payment_receipt);

							//Clear data
							unset($_SESSION['cms']['payment']);

							//Send back to record page
							$CMSBuilder->set_system_alert($record_name.' payment has been successfully processed'.($send_email ? ' and email confirmation has been sent' : '').'.', true);
							header('Location: '.$sitemap[$section_id]['page_url'].'?action=edit&item_id='.ITEM_ID);
							exit();

						}else{
							$errors[] = 'Unable to process payment: '.$db->error();
						}
					}

				//No balance
				}else{
					$errors[] = $record_name.' has already been paid in full.';
				}

			}


		//Refund processing
		// }else if(SECTION_ID == 71){
		}else if(SECTION_ID == $_cmssections['transactions-refunds']){
						
			//Set record id
			if(isset($_SESSION['cms']['refund']['record_id']) && $_SESSION['cms']['refund']['record_id'] != ITEM_ID){
				unset($_SESSION['cms']['refund']);	
			}
			$_SESSION['cms']['refund']['record_id'] = ITEM_ID;

			//No payments available for refund
			if($row['total_paid'] <= 0 || $row['total_refunded'] >= ($row['total_paid']+$row['total_admin_fee'])){
				$CMSBuilder->set_system_alert('Refunds are unavailable for '.$record_name.'.', false);
				header('Location:'.$sitemap[$section_id]['page_url'].'?action=edit&item_id='.ITEM_ID);
				exit();
			}
			
			//Determine admin fee percentage
			$admin_fee_percent = number_format($row['total_admin_fee']/($row['total_paid']+$row['total_admin_fee']), 2);
							
			//Get attendees
			$row['attendees'] = array();
			$row['attendee_available_amount'] = 0;
			if(!empty($row['registration_id'])){

				$params = array($row['registration_id'], 'Incomplete');
				$query = $db->query("SELECT `reg_attendees`.*, `reg_events`.`name` AS `event_name`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`, ".
				"(SELECT SUM(`price_adjustment`) FROM `reg_attendee_options` WHERE `reg_attendee_options`.`attendee_id` = `reg_attendees`.`attendee_id`) AS `total_addons`, ".
				"(SELECT SUM(`amount`) FROM `refund_attendees` WHERE `refund_attendees`.`attendee_id` = `reg_attendees`.`attendee_id`) AS `total_refunded` ".
				"FROM `reg_attendees` ".
				"LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ".
				"LEFT JOIN `reg_occurrences` ON `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` ".
				"WHERE `reg_attendees`.`registration_id` = ? && `reg_attendees`.`reg_status` != ?", $params);
				if($query && !$db->error()){							
					$attresult = $db->fetch_array();
					foreach($attresult as $att){
						$att['taxes'] = ($att['ticket_price']+$att['total_addons'])*(($row['gst_rate']+$row['pst_rate'])/100);
						$att['admin_fee'] = number_format(($att['ticket_price']+$att['taxes'])*$admin_fee_percent, 2);
						$att['balance'] = number_format(($att['ticket_price']+$att['total_addons']+$att['taxes']+$att['admin_fee']+$att['tournament_fee'])-$att['total_refunded'], 2, '.', '');
						$row['attendees'][$att['attendee_id']] = $att;
						
						$row['attendee_available_amount'] += $att['balance'];
					}
				}
			}
							
			//Validate refund
			if(isset($_POST['continue'])){
				
				//Validation
				$required_fields = array('first_name', 'last_name', 'email', 'phone', 'payment_id', 'refund_type', 'refund_amount');
				foreach($required_fields as $field){
					if(!isset($_POST[$field]) || $_POST[$field] == ''){
						$errors[0] = 'Please fill out all the required fields.';
						$required[] = $field;
					}
					if($field == 'email' && !checkmail($_POST[$field])) {
						$errors[] = 'Please enter a valid email address.';
						$required[] = $field;
					}
					if($field == 'phone'){
						if(!detectPhone($_POST[$field])){
							$errors[] = 'Please enter a valid phone number.';
							$required[] = $field;
						}else{
							$_POST['phone'] = formatPhoneNumber($_POST['phone']);
						}
					}
					if($field == 'refund_amount'){
						if(!is_numeric($_POST[$field]) || $_POST[$field] <= 0){
							$errors[1] = 'Please enter a valid refund amount.';
							$required[] = $field;
						}
					}
				}
				$attendee_refund_amount = 0;
				if(!empty($row['attendees'])){
					if(isset($_POST['attendee_refunds'])){
						foreach($_POST['attendee_refunds'] as $aid=>$amount){
							$amount = (!empty($amount) ? $amount : 0);
							$row['attendees'][$aid]['refund_amount'] = $amount;
							if(!is_numeric($amount)){
								$errors[1] = 'Please enter a valid refund amount.';
								$required[] = 'attendee_refunds_'.$aid;
							}else{
								if($amount > $row['attendees'][$aid]['balance']){
									$errors[2] = 'Attendee refund amount cannot exceed total available.';
									$required[] = 'attendee_refunds_'.$aid;
								}else{
									$attendee_refund_amount += $amount;
								}
							}
						}
					}
				}
				if(empty($errors)){
					$refund_payment = $row['payments'][$_POST['payment_id']];
					
					//Validate refund amount
					if($_POST['refund_amount'] > (($refund_payment['amount']+$refund_payment['admin_fee'])-$refund_payment['refund_amount'])){
						$errors[] = 'Refund amount cannot exceed payment amount ($' .number_format($refund_payment['amount']-$refund_payment['refund_amount'], 2).').';
						$required[] = 'refund_amount';
					}
					if($_POST['refund_amount'] < $attendee_refund_amount){
						$errors[] = 'Refund amount cannot be less than the total attendee refund amount ($' .number_format($attendee_refund_amount, 2).').';
						$required[] = 'refund_amount';
					}
					
					//Validate refund type
					if($_POST['refund_type'] == 'Credit Card' && $refund_payment['payment_type'] != 'Credit Card'){
						$errors[] = 'Selected payment is ineligible for a credit card refund. Please select another method.';
						$required[] = 'refund_type';
					}
					
				}
				
				//Save session vars
				$_SESSION['cms']['refund']['first_name'] = $_POST['first_name'];
				$_SESSION['cms']['refund']['last_name'] = $_POST['last_name'];
				$_SESSION['cms']['refund']['email'] = $_POST['email'];
				$_SESSION['cms']['refund']['phone'] = $_POST['phone'];
				$_SESSION['cms']['refund']['payment_id'] = $_POST['payment_id'];
				$_SESSION['cms']['refund']['payment_number'] = (isset($refund_payment['payment_number']) ? $refund_payment['payment_number'] : '');
				$_SESSION['cms']['refund']['payment_date'] = (isset($refund_payment['payment_date']) ? $refund_payment['payment_date'] : '');
				$_SESSION['cms']['refund']['refund_type'] = $_POST['refund_type'];
				$_SESSION['cms']['refund']['refund_amount'] = $_POST['refund_amount'];
				$_SESSION['cms']['refund']['txn_num'] = $_POST['txn_num'];
				$_SESSION['cms']['refund']['txn_tag'] = $_POST['txn_tag'];
				$_SESSION['cms']['refund']['notes'] = $_POST['notes'];
				$_SESSION['cms']['refund']['attendees'] = $row['attendees'];
				$_SESSION['cms']['refund']['attendee_refund_amount'] = $attendee_refund_amount;
				
				//Send to confirm
				if(empty($errors)){
					$confirm = true;
				}
				
			}
			
			//Process refund
			if(isset($_POST['process'])){
				$success = false;
				
				//Insert refund data
				$params = array(
					ITEM_ID,
					$_SESSION['cms']['refund']['payment_id'],
					$_SESSION['cms']['refund']['refund_type'],
					$_SESSION['cms']['refund']['refund_amount'],
					$_SESSION['cms']['refund']['notes'],
					USER_LOGGED_IN,
					date("Y-m-d H:i:s")
				);
				$query = $query = $db->query("INSERT INTO `refunds`(`$record_id`, `payment_id`, `refund_type`, `amount`, `notes`, `processed_by`, `refund_date`) VALUES(?,?,?,?,?,?,?)", $params);
				if($query && !$db->error()){
					$order_id = $db->insert_id();
					$ordernum = 'R'.date("ymd").'-'.str_pad($order_id, 5, '0', STR_PAD_LEFT);
					
					//If refunding credit card
					if($_SESSION['cms']['refund']['refund_type'] == 'Credit Card'){
						
						$autosettle_start = date('Ymd', strtotime('-1 day')).'23'; //Yesterday at 11pm
						$autosettle_end = date('Ymd').'23'; //Today at 11pm
						$payment_datetime = date('YmdG', strtotime($_SESSION['cms']['refund']['payment_date']));
						
						//Format void request (if transaction is before autosettle at 11pm)
						if($payment_datetime >= $autosettle_start && $payment_datetime < $autosettle_end){
							$request = array(
								'type' => 'reversalRequest',
								'ordernum' => $_SESSION['cms']['refund']['payment_number'],
								'refund_amount' => $_SESSION['cms']['refund']['refund_amount'],
								'txn_num' => $_SESSION['cms']['refund']['txn_num'],
								'txn_tag' => $_SESSION['cms']['refund']['txn_tag'],
							);
							
						//Format refund request
						}else{
							$request = array(
								'type' => 'refundRequest',
								'ordernum' => $_SESSION['cms']['refund']['payment_number'],
								'refund_amount' => $_SESSION['cms']['refund']['refund_amount'],
								'txn_num' => $_SESSION['cms']['refund']['txn_num'],
								'txn_tag' => $_SESSION['cms']['refund']['txn_tag'],
							);
						}
									
						//Send request
						include("../includes/orbital/request.php");

						//Success response
						if($trxnResponse['status'] == 1){
							$success = true;
							$status = '1';
						}else{
							$success = false;
							$status = '0';
							$errors[] = $trxnResponse['message'];
						}
						
						//Update refund response
						$response = array(
							$status,
							$trxnResponse['txn_num'],
							$trxnResponse['txn_tag'],
							$trxnResponse['message'],
							$ordernum,
							$order_id
						);
						$query = $db->query("UPDATE `refunds` SET `status`=?, `txn_num`=?, `txn_tag`=?, `message`=?, `refund_number`=? WHERE `refund_id`=?", $response);
						if(!$query || $db->error()){
							trigger_error('Error updating refund response for ' .$ordernum. ': '.$db->error());
						}
					
					//Non credit card refund
					}else{
						$success = true;
						$status = '1';
						
						//Update refund response
						$response = array($status, $ordernum, $order_id);
						$query = $db->query("UPDATE `refunds` SET `status`=?, `refund_number`=? WHERE `refund_id`=?", $response);
						if(!$query || $db->error()){
							trigger_error('Error updating refund response for ' .$ordernum. ': '.$db->error());
						}
					}
										
					//Successful
					if($success){
						
						//Insert attendee refunds
						foreach($_SESSION['cms']['refund']['attendees'] as $attendee){
							if($attendee['refund_amount'] > 0){
								$params = array($order_id, $attendee['attendee_id'], $attendee['refund_amount']);
								$query = $db->query("INSERT INTO `refund_attendees`(`refund_id`, `attendee_id`, `amount`) VALUES(?,?,?)", $params);
								if(!$query || $db->error()){
									trigger_error('Error updating attendee refund response for ' .$ordernum. ': '.$db->error());
								}
							}
						}
						
						//Send receipt
						$order_data = $_SESSION['cms']['refund'];
						$order_data['record_name'] = $record_name.' No.';
						$order_data['record_number'] = $row['record_number'];
						$order_data['refund_number'] = $ordernum;
						$order_data['refund_date'] = date('Y-m-d H:i:s');
						$order_data['ordertotal'] = $_SESSION['cms']['refund']['refund_amount'];
						
						if($_SESSION['cms']['refund']['refund_type'] == 'Credit Card'){
							$order_data['ccname'] = $row['payments'][$_SESSION['cms']['refund']['payment_id']]['ccname'];
							$order_data['cctype'] = $row['payments'][$_SESSION['cms']['refund']['payment_id']]['cctype'];
							$order_data['ccnumber'] = $row['payments'][$_SESSION['cms']['refund']['payment_id']]['ccnumber'];
							$order_data['ccexpiry'] = $row['payments'][$_SESSION['cms']['refund']['payment_id']]['ccexpiry'];
							$order_data = array_merge($order_data, $trxnResponse);
						}
						
						$refund_receipt = $Registration->refund_receipt($order_data);
						$send_email = send_email($_SESSION['cms']['refund']['email'], 'Refund Confirmation', $refund_receipt);
						
						//Clear data
						unset($_SESSION['cms']['refund']);

						//Send back to record page
						if($section_id == 69){
							$CMSBuilder->set_system_alert($record_name.' refund has been successfully processed'.($send_email ? ' and email confirmation has been sent' : '').'.<br /><br /><a href="' .$sitemap[68]['page_url']. '" class="button-sm"><i class="fa fa-users"></i> Withdraw Attendees</a>', true);
						}else{
							$CMSBuilder->set_system_alert($record_name.' refund has been successfully processed'.($send_email ? ' and email confirmation has been sent' : '').'.', true);
						}
						
						header('Location: '.$sitemap[$section_id]['page_url'].'?action=edit&item_id='.ITEM_ID);
						exit();
					}
					
				}
				
			}

		}
		
	//Record not found
	}else{
		$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
		header('Location:'.$sitemap[$section_id]['page_url']);
		exit();
	}

	//Error reporting
	if(!empty($errors)){
		$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
	}
	
}

?>