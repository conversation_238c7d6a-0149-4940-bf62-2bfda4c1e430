<?php

//Display tournaments
if(isset($tournaments) && !empty($tournaments)){
	$subtotal = 0;
	$fees = 0;
	$taxes = 0;
	$total = 0;
	
	$html .= '<form name="cart-form" action="' .$_sitepages['reg_checkout']['page_url']. '" method="post">
		<table cellpadding="10" cellspacing="0" border="0" width="100%" class="cart-table noresponsive nomargin">
			<tr class="header-row">
				<th class="left">Tournament</th>
				<th class="right">Total</th>
			</tr>';
			foreach($tournaments as $item_id=>$item){
				$html .= '<tr>
					<td class="left">' .(isset($item['available']) && !$item['available'] ? '<i class="fa fa-exclamation-triangle color-red" title="Unavailable"></i> ' : '').$item['event_name']. '<br />
						<small class="dblock">' .format_date_range($item['start_date'], $item['end_date']). '</small>
						<small class="dblock">
							<a onclick="deleteCartItem('.$item_id.');" class="action">Delete</a> &nbsp; 
							<a href="' .$item['event_url']. '" class="action">View</a>
						</small>
						<input type="hidden" name="cart_items[]" value="' .$item_id. '" />
					</td>
					<td class="right" valign="top">$' .number_format($item['subtotal'], 2). '</td>
				</tr>';
				$subtotal += $item['subtotal'];
				$fees += $item['fees'];
				$taxes += $item['taxes'];
				$total += $item['total'];
			}
		$html .= '</table>';
		
		//Display totals
		$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive totals">
			<tr>
				<td align="right">Subtotal:</td>
				<td class="right" >$' .number_format($subtotal, 2). '</td>
			</tr>';
			$html .= '<tr>
				<td align="right">Taxes:</td>
				<td class="right">$' .number_format($taxes, 2). '</td>
			</tr>';
			if($fees > 0){
				$html .= '<tr>
					<td align="right">Skins:</td>
					<td class="right">$' .number_format($fees, 2). '</td>
				</tr>';	
			}
			$html .= '<tr>
				<td align="right"><h6>Total:</h6></td>
				<td class="right"><h6>$' .number_format($total, 2). '</h6></td>
			</tr>
		</table>';
	
		$html .= '<div class="form-buttons clearfix">
			<button type="submit" name="checkout" class="button solid f_right secondary red" value="true"' .(isset($unavailable) && !empty($unavailable) ? ' disabled' : ''). '>Continue</button>';
			if(isset($ajaxcart)){
				$html .= '<a class="previous f_right button" onclick="dialogClose(\'#dialog-box\')">+ Add Another Tournament</a>';
			}else{
				$html .= '<a class="previous f_right button" href="' .$_sitepages['tournaments']['page_url']. '">+ Add Another Tournament</a>';
			}
		$html .= '</div>

		<input type="hidden" name="xid" value="' .$_COOKIE['xid']. '" />
	</form>';
}

?>