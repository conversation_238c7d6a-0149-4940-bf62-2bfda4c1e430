<?php

//System files
include("../includes/config.php");
include("../../config/database.php");
include("../includes/functions.php");
include("../../core/classes/Account.class.php");
$Account = new Account('Admin');

//Logout
try{
	$Account->logout();
	unset($_SESSION['system_search']);
}catch(Exception $e){
	trigger_error($e->getMessage());
}
session_destroy();

//Redirect
header('Location: '.$path);	
exit();

?>