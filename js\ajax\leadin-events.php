<?php  

//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

if($_POST['xid'] == $_COOKIE['xid']){
	$leadin_id = $_POST['id'] ?? NULL;
	$event = $_POST['event'] ?? NULL;
	$referer = get_referer_url();
	$page_id = (isset($referer['path']) ? $SiteBuilder->get_page_id($referer['path']) : NULL);
	$ip_address = get_ip();

	if(!empty($event)){
		$params = array(
			$leadin_id,
			$page_id,
			$event,
			session_id(),
			$ip_address,
			gethostbyaddr($ip_address),
			date('Y-m-d H:i:s')
		);
		$insert = $db->query("INSERT INTO `leadin_events` (`leadin_id`, `page_id`, `event`, `session_id`, `ip_address`, `hostname`, `timestamp`) VALUES (?,?,?,?,?,?,?)", $params);
		if($insert && !$db->error()){
			echo 'success';
		}else{
			echo 'error';
		}
	}

}else{
	echo 'error';
}

?>