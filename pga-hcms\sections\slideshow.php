<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	
	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<a href="' .PAGE_URL. '?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">' .$record_name. 's  
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">
			
				<thead>
					<th width="1px" data-sorter="false"></th>
					<th width="1px" data-sorter="false"></th>
					<th>Title</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				foreach($records_arr as $row){
					echo '<tr data-table="' .$record_db. '" data-column-name="' .$record_id. '" data-name="' .$row['title']. '" data-id="' .$row[$record_id]. '">
						<td class="handle"><span class="fas fa-arrows-alt"></span></td>
						<td class="nopadding-h">'.render_gravatar($imagedir.'480/'.$row['image'], $imagedir.'1920/'.$row['image'], $row['title']).'</td>
						<td>' .$row['title'].($row['content'] ? '<br /><small>' .truncate($row['content'], 125). '</small>' : ''). '</td>
						<td>' .$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']). '</td>
						<td class="right"><a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
					</tr>';	
				}
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';
	

//Image cropping
}else if($CMSUploader->crop_queue()){
	include("includes/jcropimages.php");
	

//Display form	
}else{

	$data       = $records_arr[ITEM_ID] ?? [];
	$row        = !isset($_POST['save']) ? $data : $_POST;
	$image      = $data['image'] ?? NULL;
	$video_mp4  = $data['video_mp4'] ?? NULL;
	$video_webm = $data['video_webm'] ?? NULL;
	$video_ogg  = $data['video_ogg'] ?? NULL;

	echo '<form action="" method="post" enctype="multipart/form-data">';

		//Details
		echo '<div class="panel">
			<div class="panel-header">' .$record_name. ' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show ' .$record_name. '</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"' .(($row['showhide'] ?? 0) ? '' : ' checked'). ' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>
			<div class="panel-content">
				<div class="flex-container">
			
					<div class="form-field">
						<label>' .$record_name. ' Title' .(in_array('title', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip($record_name. ' Title', 'Put special emphasis on a word or phrase by enclosing it in {curly brackets}.'). '</label>
						<input type="text" name="title" value="' .($row['title'] ?? ''). '" class="input' .(in_array('title', $required) ? ' required' : ''). '" />
					</div>

					<div class="form-field">
						<label>Colour Overlay' .(in_array('theme', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Colour Overlay', 'This will be used as the overlay colour for the slide image.'). '</label>
						<select name="theme" class="select' .(in_array('theme', $required) ? ' required' : ''). '">
							<option value="">Default</option>';
							foreach($_themes as $theme_key => $theme_name){
								echo '<option value="' .$theme_key. '"' .(($row['theme'] ?? false) == $theme_key ? ' selected' : ''). '>' .$theme_name. '</option>';
							}
						echo '</select>
					</div>

					<div class="form-field">
						<label>Numerical Order' .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). '</label>
						<select name="ordering" class="select">
							<option value="101">Default</option>';
							for($i=1; $i<101; $i++){
								echo '<option value="' .$i. '"' .(($row['ordering'] ?? '') == $i ? ' selected' : ''). '>' .$i. '</option>';	
							}
						echo '</select>
					</div>
					
				</div>
				
				<div class="form-field auto-width">
					<label>Description</label>
					<textarea name="content" class="textarea input_lg' .(in_array('content', $required) ? ' required' : ''). '">' .($row['content'] ?? ''). '</textarea>
				</div>
				
			</div>
		</div>'; //Details

		//Links
		echo '<div class="page-content">
			<div class="tabs tab-ui">
				<ul>
					<li><a href="#link1">Button 1</a></li><li><a href="#link2">Button 2</a></li>
				</ul>

				<div id="link1">
					<div class="flex-container">

						<div class="form-field">
							<label>Button Link <small>(URL, Phone, or Email)</small>' .(in_array('url', $required_fields) ? ' <span class="required">*</span>' : '').
								$CMSBuilder->tooltip('Button Link', 'If a link is provided, a clickable button will be displayed.<br /><br /><strong>URL</strong> - User will be taken to this link.<br /><strong>Phone</strong> - User will be prompted to call the phone number provided.<br />Example: (*************.<br /><strong>Email</strong> - User will be prompted to emaill the address provided.').$CMSBuilder->tooltip('Sitemap Reference', 'Click on the icon to view the sitemap. You can use the sitemap pop up as your reference when entering links.', '<span class="fas fa-sitemap"></span>', array('sitemap-reference')). '
							</label>
							<input type="text" name="url" value="' .($row['url'] ?? ''). '" class="input' .(in_array('url', $required) ? ' required' : ''). '" />
						</div>

						<div class="form-field">
							<label>Button Text' .(in_array('url_text', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Button Text', 'Button will be displayed with this text. Defaults to &quot;Learn More&quot;.'). '</label>
							<input type="text" name="url_text" value="' .($row['url_text'] ?? ''). '" class="input' .(in_array('url_text', $required) ? ' required' : ''). '" />
						</div>

						<div class="form-field">
							<label>Open Link in' .(in_array('url_target', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<select name="url_target" class="select' .(in_array('url_target', $required) ? ' required' : ''). '">
								<option value="0"' .(!($row['url_target'] ?? 0) ? ' selected' : ''). '>Same Window</option>
								<option value="1"' .(($row['url_target'] ?? 0) ? ' selected' : ''). '>New Window</option>
							</select>
						</div>

					</div>
				</div>

				<div id="link2">
					<div class="flex-container">

						<div class="form-field">
							<label>Button Link <small>(URL, Phone, or Email)</small>' .(in_array('url2', $required_fields) ? ' <span class="required">*</span>' : '').
								$CMSBuilder->tooltip('Button Link', 'If a link is provided, a clickable button will be displayed.<br /><br /><strong>URL</strong> - User will be taken to this link.<br /><strong>Phone</strong> - User will be prompted to call the phone number provided.<br />Example: (*************.<br /><strong>Email</strong> - User will be prompted to emaill the address provided.').$CMSBuilder->tooltip('Sitemap Reference', 'Click on the icon to view the sitemap. You can use the sitemap pop up as your reference when entering links.', '<span class="fas fa-sitemap"></span>', array('sitemap-reference')). '</label>
							<input type="text" name="url2" value="' .($row['url2'] ?? ''). '" class="input' .(in_array('url2', $required) ? ' required' : ''). '" />
						</div>

						<div class="form-field">
							<label>Button Text' .(in_array('url_text2', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Button Text', 'Button will be displayed with this text. Defaults to &quot;Learn More&quot;.'). '</label>
							<input type="text" name="url_text2" value="' .($row['url_text2'] ?? ''). '" class="input' .(in_array('url_text2', $required) ? ' required' : ''). '" />
						</div>

						<div class="form-field">
							<label>Open Link in' .(in_array('url_target2', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
							<select name="url_target2" class="select' .(in_array('url_target2', $required) ? ' required' : ''). '">
								<option value="0"' .(!($row['url_target2'] ?? 0) ? ' selected' : ''). '>Same Window</option>
								<option value="1"' .(($row['url_target2'] ?? 0) ? ' selected' : ''). '>New Window</option>
							</select>
						</div>

					</div>
				</div>

			</div>
		</div>'; //Links

		//Image
		echo '<div class="panel">
			<div class="panel-header">' .$record_name. ' Media
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">
				<div class="flex-container">'.
					$CMSBuilder->img_holder($image, $imagedir.'1920/', $imagedir.'1024/', false).

					'<div class="form-field">
						<label>Upload Image <span class="required">*</span>' .$CMSBuilder->tooltip('Upload Image', 'Image must be at least '.$CMSUploader::size_label('slideshow', 'image').' and smaller than '.$_max_filesize['megabytes'].'.'). '</label>
						<input type="file" class="input' .(in_array('image', $required) ? ' required' : ''). '" name="image" value="" />
					</div>
					
					<div class="form-field">
						<label>Alt Text <small>(SEO)</small>' .$CMSBuilder->tooltip('Alt Text', 'Provide a brief description of this image.'). '</label>
						<input type="text" name="image_alt" value="' .($row['image_alt'] ?? ''). '" class="input" />
					</div>
					
				</div>
				
				<hr />
				
				<div class="flex-container">
				
					<div class="form-field">
						<label>
							Upload Video (mp4) '.
							($video_mp4 ? '&nbsp;<a href="' .$path.$videodir.$video_mp4. '" target="_blank"><i class="fas fa-external-link-alt"></i></a> ' : '').
							$CMSBuilder->tooltip('Upload Video (mp4)', 'Video should be less than 10MB in size and saved as mp4. <br/><strong>Recommended Size:</strong> 1920 x 1080').'
						</label>
						<input type="file" name="video_mp4" class="input" />
					</div>
					
					<div class="form-field">
						<label>
							Upload Video (webm) '.
							($video_webm ? '&nbsp;<a href="' .$path.$videodir.$video_webm. '" target="_blank"><i class="fas fa-external-link-alt"></i></a> ' : '').
							$CMSBuilder->tooltip('Upload Video (webm)', 'Video should be less than 10MB in size and saved as webm. <br/><strong>Recommended Size:</strong> 1920 x 1080').'
						</label>
						<input type="file" name="video_webm" class="input" />
					</div>
					
					<div class="form-field">
						<label>
							Upload Video (ogv) '.
							($video_ogg ? '&nbsp;<a href="' .$path.$videodir.$video_ogg. '" target="_blank"><i class="fas fa-external-link-alt"></i></a> ' : '').
							$CMSBuilder->tooltip('Upload Video (ogv)', 'Video should be less than 10MB in size and saved as ogv. <br/><strong>Recommended Size:</strong> 1920 x 1080').'
						</label>
						<input type="file" name="video_ogg" class="input" />
					</div>
					
				</div>';

				if($video_mp4 || $video_webm || $video_ogg){
					echo '<div class="form-field"><p>
						<input type="checkbox" class="checkbox" name="deletevideo" id="deletevideo" value="1">
						<label for="deletevideo">Delete Videos ' .$CMSBuilder->tooltip('Delete Videos', 'Checking this will delete all video files.'). '</label>
					</p></div>';
				}
	
			echo '</div>
		</div>'; //END Image

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

}

?>