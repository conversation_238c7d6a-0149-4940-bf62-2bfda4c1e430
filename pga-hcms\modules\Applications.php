<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('applications');
	$CMSBuilder->set_widget($_cmssections['applications'], 'Total Applications', $total_records);
}

if(SECTION_ID == $_cmssections['applications']){
//	Define vars
	$record_db = 'applications';
	$record_id = 'application_id';
	$record_name = 'Application';
	$records_arr = array();
	$params = array();

	if($searchterm != ""){
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
	}

	$query = $db->query("SELECT `$record_db`.*, `careers`.`title` FROM `$record_db` LEFT JOIN `careers` ON `careers`.`career_id` = `$record_db`.`career_id`" .($searchterm != "" ? " WHERE `careers`.`title` LIKE ? OR `$record_db`.`first_name` LIKE ? OR `$record_db`.`last_name` LIKE ?" : ""). " ORDER BY `$record_db`.`timestamp` DESC, `$record_db`.`$record_id` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}

	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	

	}
	
	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		$delete = $db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if($delete && !$db->error()){
			if(file_exists('../docs/resumes/'.$records_arr[ITEM_ID]['resume'])){
				unlink('../docs/resumes/'.$records_arr[ITEM_ID]['resume']);
			}
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}
		header("Location: " .PAGE_URL);
		exit();
	}

}



?>