<?php

//Table listing
if(ACTION == ''){

	include("includes/widgets/searchform.php");
	echo '<p class="f_right"><a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a></p>

	<div class="panel">
		<div class="panel-header">'.$records_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">
				<thead>
				<th width="175px">Date</th>
				<th width="auto">Message</th>
				<th width="1px" class="center">Visible</th>
				<th width="1px"></th>
				</thead>

				<tbody>';

				foreach($records_arr as $row){
				echo '<tr>
				<td>'.date("M j, Y", strtotime($row['updated_on'])).'</td>';
				// echo '<td>'.$row['first_name'].' '.$row['last_name'].'</td>';
				// echo '<td>';

				// if($row['field_name'] == 'facility_id'){
				// 	echo 'Facility updated'
				// 	.(!empty($row['previous_value']) && isset($facilities[$row['previous_value']]['name']) ? ' from '.$facilities[$row['previous_value']]['name'] : '')
				// 	.(!empty($row['current_value']) && isset($facilities[$row['current_value']]['name']) ? ' to '.$facilities[$row['current_value']]['name'] : '').'.';
				// }else if($row['field_name'] == 'class_id'){
				// 	echo 'Classification updated'
				// 	.(!empty($row['previous_value']) && isset($member_classes[$row['previous_value']]['name']) ? ' from '.$member_classes[$row['previous_value']]['name'] : '')
				// 	.(!empty($row['current_value']) && isset($member_classes[$row['current_value']]['name']) ? ' to '.$member_classes[$row['current_value']]['name'] : '').'.';
				// }else{
				// 	echo $row['current_value'] ?: 'Manual update';
				// }

				// echo '</td>';

				echo '<td>'.nl2br($row['comments']).'</td>
				<td class="center">'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
				<td><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
				</tr>';
				}

				echo '</tbody>
			</table>';

			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';


//Display form
}else{
	$data  = $records_arr[ITEM_ID] ?? [];
	$row   = !isset($_POST['save']) ? $data : $_POST;

	echo '<form action="" method="post" enctype="multipart/form-data">';

		// Entry Details
		echo '<div class="panel">
			<div class="panel-header">Entry Details
				<span class="panel-toggle fas fa-chevron-up"></span>

				<div class="panel-switch">
				<label>Show Entry</label>
				<div class="onoffswitch">
				<input type="checkbox" name="showhide" id="showhide" value="0"'.(empty($row['showhide']) ? " checked" : "").' />

				<label for="showhide">
				<span class="inner"></span>
				<span class="switch"></span>
				</label>
				</div>
				</div>
			</div>

			<div class="panel-content">';

			if(ACTION == 'add'){
				echo '<div class="form-field ui-front">
				<label>Member Name <span class="required">*</span></label>
				<input type="text" name="term" value="'.(isset($_POST['term']) ? $_POST['term'] : '').'" class="member_suggest input'.(in_array('member_name', $required) ? ' required' : '').'" />
				<input type="hidden" name="account_id" class="account_id" value="" />
				</div>';
			}else{
				echo '<div class="form-field">
				<label>Member Name</label>
				<input type="text" value="'.$row['first_name'].' '.$row['last_name'].'" class="input readonly" readonly />
				</div>

				<div class="form-field">
				<label>Member Update</label>
				<input type="text" value="';

				if($row['field_name'] == 'facility_id'){
					echo 'Facility updated'
					.(!empty($row['previous_value']) && isset($facilities[$row['previous_value']]['name']) ? ' from '.$facilities[$row['previous_value']]['name'] : '')
					.(!empty($row['current_value']) && isset($facilities[$row['current_value']]['name']) ? ' to '.$facilities[$row['current_value']]['name'] : '').'.';
				}else if($row['field_name'] == 'class_id'){
					echo 'Classification updated'
					.(!empty($row['previous_value']) && isset($member_classes[$row['previous_value']]['name']) ? ' from '.$member_classes[$row['previous_value']]['name'] : '')
					.(!empty($row['current_value']) && isset($member_classes[$row['current_value']]['name']) ? ' to '.$member_classes[$row['current_value']]['name'] : '').'.';
				}else{
					echo $row['current_value'] ?: 'Manual update';
				}

				echo '" class="input readonly" readonly />
				</div>

				<div class="form-field">
				<label>Updated By</label>
				<input type="text" value="'.$row['updated_by_name'].'" class="input readonly" readonly />
				</div>';
			}

			echo '<div class="form-field">
			<label>Updated On <span class="required">*</span></label>
			<input type="text" name="updated_on" class="input datepicker'.(in_array('updated_on', $required) ? ' required' : '').'" value="'.(isset($row['updated_on']) ? date("Y-m-d", strtotime($row['updated_on'])) : date("Y-m-d")).'" autocomplete="off" />
			</div>

			<div class="form-field">
			<label>Message <span class="required">*</span></label>
			<textarea name="comments" class="textarea'.(in_array('comments', $required) ? ' required' : '').'">'.(isset($row['comments']) ? $row['comments'] : '').'</textarea>
			</div>

			</div>
		</div>';

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "</form>";

}

?>