.dynamic-form1{
    .checkbox+label:after, .radio+label:after {
     opacity: 1; 
    }

    .form-field label {
        display: flex;
        margin-top: 10px;
    }

    .checkbox+label, .radio+label{
        align-items: center;
    }
}

.message-center-controls-container {
    /* display: -webkit-box; */
    display: -ms-flexbox;
    display: flex
;
    /* -webkit-box-pack: unset; */
    -ms-flex-pack: justify;
    justify-content: space-between;
    /* -webkit-box-align: center; */
    gap: 6%;
    -ms-flex-align: center;
    align-items: self-start;
    /* margin-bottom: 30px; */
    -ms-flex-wrap: wrap;
    /* flex-wrap: nowrap; */
    width: max-content;

    label{
        font-size: 11px;
    }

    .directory-search-form {
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 0.6;
        max-width: 393px;
    }

    .checkbox+label:after, .radio+label:after {
    opacity: 0; 
    align-self: anchor-center;
    }
    .checkbox+label:before, .radio+label:before {
        align-self: anchor-center;
        border-radius: 2px;
    }

    .checkbox:checked + label:after {
        //content: normal;
    }

    .checkbox + label{
        position: relative;
        display: inline-block;
        padding: 1px 10px 1px 26px;
        cursor: pointer;
        line-height: 20px !important;
        width: auto !important;
        margin-bottom: 5px;
        font-weight: 400;
    }
    .checkbox{
        display: none;
    }

    .checkbox:checked + label:before {
        content: "\f00c";
    }

    .checkbox + label:before{
        font-family: FontAwesome;
        display: inline-block;
        font-size: 18px;
        font-style: normal;
        line-height: 18px;
        position: absolute;
        left: 0;
        width: 18px;
        height: 18px;
        border: 1px solid #ccc;
        text-align: center;
        color: #16212F;
        background: #FFFFFF;
    }
}

.message-center-search-form {
    /* -webkit-box-flex: 1; */
    -ms-flex-positive: 1;
    flex-grow: 0.6;
    /* max-width: 450px; */
}

#message-center-search-bar{
    width: 5%;
}

#message-table{
    border-radius: 6px 6px 6px 6px;
    thead {
        border: 1px solid black !important;
        border-radius: 11px !important;
    }

    th {
        border-radius: 10px;
    }
}

.pages_list{
    display: flex;
    justify-content: space-between;
    color: black;
    a{
        color: #EF3E34;
    }
}

#site-search{padding: 0.7em 0; margin:0 0 0 -15px;
	.input{font-size:16px; font-style:normal; height:30px; width:150px; padding:0 10px; border:0; margin:0;
		&::-webkit-input-placeholder{ opacity: 1;}
		&:-moz-placeholder{ opacity: 1;}
		&::-moz-placeholder{ opacity: 1;}
		&:-ms-input-placeholder{ opacity: 1;}

		&:focus{}
		&:focus::-webkit-input-placeholder{}
		&:focus:-moz-placeholder{}
		&:focus::-moz-placeholder{}
		&:focus:-ms-input-placeholder{}
	}
	.search-btn{height:30px; width:30px; line-height:28px; border:0; background:none;  text-align:center; font-size:16px; cursor:pointer; outline:none;}
}

