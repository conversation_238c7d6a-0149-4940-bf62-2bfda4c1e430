<?php

/*-----------------------------------/
* Builder for CMS base, sections, and settings
* <AUTHOR> Army
* @date		15-06-04
* @file		CMSBuilder.class.php
*/

class CMSBuilder{

	/*-----------------------------------/
	* @var path
	* Relative path to top of the site
	*/
	public $path;

	/*-----------------------------------/
	* @var pathbits
	* Array of url segments
	*/
	public $pathbits;

	/*-----------------------------------/
	* @var db
	* Mysqli database object utilizing Database class
	*/
	private $db;

	/*-----------------------------------/
	* @var account
	* Account object utilizing Accounts class
	*/
	private $account;

	/*-----------------------------------/
	* @var sitemap
	* Non-recursive array of all pages in site map
	*/
	private $sitemap;

	/*-----------------------------------/
	* @var navigation
	* Recursive array of navigation links
	*/
	private $navigation;

	/*-----------------------------------/
	* @var pageurl
	* Url of current page
	*/
	private $pageurl;

	/*-----------------------------------/
	* @var settings
	* Array of global settings data
	*/
	private $settings;

	/*-----------------------------------/
	* @var cms_settings
	* Array of CMS settings data
	*/
	private $cms_settings;

	/*-----------------------------------/
	* @var widgets
	* Array of dashboard widget data
	*/
	private $widgets;

	/*-----------------------------------/
	* Public constructor function
	*
	* <AUTHOR> Army
	* @param	$path		Relative path to top of the site
	* @return	CMSBuilder	New CMSBuilder object
	* @throws	Exception
	*/
	public function __construct($path='/'){

		//Set database instance
		$this->db = Database::get_instance();

		//Set account instance
		if(class_exists('Account')){
			$this->account = new Account();
		}else{
			throw new Exception('Missing class file `Account`');
		}

		//Get path variables
		$pageurl = $_SERVER['REQUEST_URI'];
		if($pageurl == $path || $pageurl == $path.'index.php'){
			$pageurl = $path.'home/';
		}
		if(empty($pageurl)){
			$pathbits = array('');
		}else{
			$pathbits = explode("/",  $pageurl);
		}
		$shifts = explode("/", $path);
		for($i=0; $i<count($shifts)-2; $i++){
			array_shift($pathbits);
		}
		foreach($pathbits as $key => $bit){
			$pathbits[$key] = strip_data($bit);
			if($pathbits[$key] == "?".$_SERVER['QUERY_STRING']){
				$pageurl = str_replace($pathbits[$key], '', $pageurl);
				$pathbits[$key] = "";
			}
		}

		//Set path variables
		$this->path = $path;
		$this->pathbits = $pathbits;
		$this->pageurl = $pageurl;

		//Load sitemap and navigation
		$this->sitemap = array();
		$this->navigation = array('ungrouped'=>[], 'grouped'=>[]);
		try{
			$this->fetch_sections();
		}catch(Exception $e){
			throw new Exception($e->getMessage());
		}

		//Load global settings
		$this->settings = array();
		try{
			$this->fetch_settings();
		}catch(Exception $e){
			throw new Exception($e->getMessage());
		}

		//Load CMS settings
		$this->cms_settings = array();
		try{
			$this->fetch_cms_settings();
		}catch(Exception $e){
			throw new Exception($e->getMessage());
		}

		//Set widget array
		$this->widgets = array();

    }

	/*-----------------------------------/
	* Loads the sitemap and navigation data into this object
	*
	* <AUTHOR> Army
	* @throws	Exception
	*/
	private function fetch_sections(){

		$params = array('Enabled');
		$this->db->query("SELECT `cms_sections`.*, `group_name` FROM `cms_sections`
		LEFT JOIN `cms_section_groups` ON `cms_sections`.`group_id` = `cms_section_groups`.`group_id`
		WHERE `status` = ?
		ORDER BY IFNULL(`parent_id`, `cms_section_groups`.`ordering`), IFNULL(`parent_id`, `cms_section_groups`.`group_id`), `cms_sections`.`ordering` IS NULL, `cms_sections`.`ordering`, `section_id`", $params);
		if(!$this->db->error()){
			$result = $this->db->fetch_array();

			//Full sitemap
			$this->sitemap = $this->build_sitemap($result);

			//Ungrouped sections
			$this->navigation['ungrouped'] = $this->build_navigation($this->sitemap);

			//Grouped sections
			$this->db->query("SELECT * FROM `cms_section_groups` WHERE `showhide` = 0 ORDER BY `ordering`");
			$groups = $this->db->fetch_assoc('group_id');
			foreach($groups as $group_id=>$group){
				$group['sections'] = $this->build_navigation($this->sitemap, NULL, $group_id);
				if(!empty($group['sections'])){
					$this->navigation['grouped'][$group_id] = $group;
				}
			}

		}else{
			throw new Exception('Error retrieving site map: '.$this->db->error());
		}
	}

	/*-----------------------------------/
	* Reorders sections based on hierarchy and builds flat array of sections
	*
	* <AUTHOR> Army
	* @param	$sections_arr	Array of sections with section ID as key
	*/
	private function build_sitemap($sections_arr=array(), $parent_id=0){
		$row = array();
		foreach($sections_arr as $section){
			if($section['parent_id'] == $parent_id){
				$row[$section['section_id']] = $section;
				$row[$section['section_id']]['page_url'] = $this->path.$section['page'].'/';

				$children = $this->build_sitemap($sections_arr, $section['section_id']);
				if($children){
					foreach($children as $child){
						$row[$child['section_id']] = $child;
						$row[$child['section_id']]['page_url'] = $row[$child['parent_id']]['page_url'].$child['page'].'/';
					}
				}
			}
		}
		return $row;
	}

	/*-----------------------------------/
	* Builds nested array of sections excluding hidden sections
	*
	* <AUTHOR> Army
	* @param	$sections_arr	Array of sections with section ID as key
	*/
	private function build_navigation($sections_arr=array(), $parent_id=NULL, $group_id=NULL){
		$row = array();
		foreach($sections_arr as $section){

			//Check parent sections
			if(empty($section['parent_id'])){

				//Validate permissions and group
				if(!$this->check_permissions($section['section_id']) || (empty($group_id) && !empty($section['group_id']) || $section['group_id'] != $group_id)){
					continue;
				}
			}

			//Add sub sections
			if($section['parent_id'] == $parent_id && $section['showhide'] == 0 && $section['section_id'] > 1){
				$children = $this->build_navigation($sections_arr, $section['section_id']);
				if($children){
					$section['sub_sections'] = $children;
				}else{
					$section['sub_sections'] = array();
				}
				$row[$section['section_id']] = $section;
			}
		}
		return $row;
	}

	/*-----------------------------------/
	* Get all section groups
	*
	* <AUTHOR> Army
	* @throws	Exception
	*/
	public function fetch_section_groups(){

		$this->db->query("SELECT * FROM `cms_section_groups` ORDER BY `ordering`, `group_id`");
		if(!$this->db->error()){
			return $this->db->fetch_assoc('group_id');

		}else{
			throw new Exception('Error retrieving section groups: '.$this->db->error());
		}
	}

	/*-----------------------------------/
	* Loads global website settings into this object
	*
	* <AUTHOR> Army
	* @throws	Exception
	*/
	private function fetch_settings(){
		$this->db->query("SELECT * FROM `global_settings`");
		if(!$this->db->error()){
			$result = $this->db->fetch_array();

			//Set defaults
			$result[0]['locations'] = array();
			$result[0]['social'] = array();
			$result[0]['contact_address'] = NULL;
			$result[0]['contact_address2'] = NULL;
			$result[0]['contact_city'] = NULL;
			$result[0]['contact_province'] = NULL;
			$result[0]['contact_postal_code'] = NULL;
			$result[0]['contact_country'] = NULL;
			$result[0]['contact_phone'] = NULL;
			$result[0]['contact_fax'] = NULL;
			$result[0]['contact_toll_free'] = NULL;
			$result[0]['contact_email'] = NULL;
			$result[0]['gpslat'] = NULL;
			$result[0]['gpslong'] = NULL;
			$result[0]['zoom'] = NULL;

			//Get locations
			$this->db->query("SELECT * FROM `locations`");
			if(!$this->db->error()){
				$result2 = $this->db->fetch_array();
				foreach($result2 as $row2){
					$row2['location_numbers'] = array();
					$row2['location_hours'] = array();

					//Get global numbers
					$this->db->query("SELECT * FROM `location_numbers` WHERE `phone` IS NOT NULL && `phone` != ? && `location_id` = ?", array('', $row2['location_id']));
					if(!$this->db->error()){
						$row2['location_numbers'] = $this->db->fetch_array();
					}

					//Get global hours
					$this->db->query("SELECT * FROM `location_hours` WHERE `location_id` = ".$row2['location_id']);
					if(!$this->db->error()){
						$row2['location_hours'] = $this->db->fetch_array();
					}

					//Push to locations if visible
					if($row2['showhide'] == 0){
						$result[0]['locations'][] = $row2;
					}

					//Set defaults to head office
					if($row2['head_office']){
						$result[0]['contact_address'] = $row2['address'];
						$result[0]['contact_address2'] = $row2['address2'];
						$result[0]['contact_city'] = $row2['city'];
						$result[0]['contact_province'] = $row2['province'];
						$result[0]['contact_postal_code'] = $row2['postal_code'];
						$result[0]['contact_country'] = $row2['country'];
						$result[0]['contact_phone'] = $row2['phone'];
						$result[0]['contact_fax'] = $row2['fax'];
						$result[0]['contact_toll_free'] = $row2['toll_free'];
						$result[0]['contact_email'] = $row2['email'];
						$result[0]['gpslat'] = $row2['gpslat'];
						$result[0]['gpslong'] = $row2['gpslong'];
						$result[0]['zoom'] = $row2['zoom'];
					}

				}
			}

			//Get social links
			$this->db->query("SELECT * FROM `global_social`");
			if(!$this->db->error()){
				$result5 = $this->db->fetch_array();
				$result[0]['global_social'] = $result5;
			}

			//Save settings to object
			$this->settings = $result[0];

			//Set timezone if available
			if($this->settings['timezone'] != NULL){
				date_default_timezone_set($this->settings['timezone']);
			}

		}else{
			throw new Exception('Error retrieving global settings: '.$this->db->error());
		}
	}

	/*-----------------------------------/
	* Loads CMS settings into this object
	*
	* <AUTHOR> Army
	* @throws	Exception
	*/
	private function fetch_cms_settings(){
		$this->db->query("SELECT * FROM `cms_settings`");
		if(!$this->db->error()){
			$result = $this->db->fetch_array();
			$this->cms_settings = $result[0];
		}else{
			throw new Exception('Error retrieving CMS settings: '.$this->db->error());
		}
	}

	/*-----------------------------------/
	* Gets full site map for CMS
	*
	* <AUTHOR> Army
	* @return	Array of sections
	*/
	public function get_sitemap(){
		return $this->sitemap;
	}

	/*-----------------------------------/
	* Gets navigation structure for CMS
	*
	* <AUTHOR> Army
	* @return	Array of navigation links
	*/
	public function get_navigation(){
		return $this->navigation;
	}

	/*-----------------------------------/
	* Gets global website settings
	*
	* <AUTHOR> Army
	* @return	Array of global data
	*/
	public function global_settings(){
		return $this->settings;
	}

	/*-----------------------------------/
	* Gets CMS settings
	*
	* <AUTHOR> Army
	* @return	Array of CMS setting data
	*/
	public function cms_settings(){
		return $this->cms_settings;
	}

	/*-----------------------------------/
	* Checks to see if a user has permission to view/edit a section
	*
	* <AUTHOR> Army
	* @param	$section_id		The section ID to be checked
	* @param	$account_id		The account ID to be checked
	* @return	Array of global data
	*/
	public function check_permissions($section_id, $account_id=NULL){
		if(is_numeric($section_id)){

			//Default to current user
			if(is_null($account_id) || empty($account_id)){
				$account_id = $this->account->login_status();
			}

			//Auto access to system sections and master users
			if($section_id <= 4 || $this->account->account_has_role('Master')){
				return true;
			}

			//Check if section exists in account permissions
			$permissions = $this->account->get_account_permissions($account_id);
			if(array_key_exists($section_id, $permissions)){
				return true;
			}
		}
		return false;
	}

	/*-----------------------------------/
	* Gets all data for specified section
	*
	* <AUTHOR> Army
	* @param	$section	Url segment of section (ie. /dashboard/settings/) OR section ID
	* @param	$dir		Directory location of section file
	* @return	Array		Array of all section data
	*/
	public function get_section($section, $dir=''){
		$dir = $dir ?: $_SERVER['DOCUMENT_ROOT'].$this->path.'sections/';

		//Get section id
		if(!is_numeric($section)){
			$section_id = $this->get_section_id($section);
		}else{
			$section_id = $section;
		}

		//Retrieve page data
		$this->db->query("SELECT * FROM `cms_sections` WHERE `section_id` = ? && `status` = ?", array($section_id, 'Enabled'));
		if(!$this->db->error() && $this->db->num_rows()){
			$results = $this->db->fetch_array();
			$row = $results[0];

			//Return section data if found
			if(trim($row['filelocation']) != '' && file_exists($dir.$row['filelocation'])){

				//Set page url
				$row['page_url'] = $this->sitemap[$section_id]['page_url'];

				//Not found
				$row['error404'] = false;

				return $row;
			}
		}

		//section not found
		$error['error404'] = true;
		$error['section_id'] = '';
		$error['name'] = 'Not Found';
		$error['page'] = 'notfound';
		$error['parent_id'] = $this->get_parent_id($this->pageurl);
		$error['page_url'] = $this->pageurl;

		return $error;

	}

	/*-----------------------------------/
	* Gets all data for current section user is on
	*
	* <AUTHOR> Army
	* @return	Array	Array of section data
	*/
	public function curr_section(){
		return $this->get_section($this->pageurl);
	}

	/*-----------------------------------/
	* Gets section id based on url
	*
	* <AUTHOR> Army
	* @param	$page_url		Full page url
	* @param	$sections_arr	Array of sections to search
	* @return	Integer			Section id
	*/
	public function get_section_id($page_url, $sections_arr=NULL){
		$section_id = NULL;
		if(is_null($sections_arr)){
			$sections_arr = $this->sitemap;
		}
		foreach($sections_arr as $section){
			if($section['page_url'] === $page_url){
				$section_id = $section['section_id'];
				break;
			}else if(isset($section['sub_sections']) && is_array($section['sub_sections']) && count($section['sub_sections']) > 0){
				$section_id = $this->get_section_id($page_url, $section['sub_sections']);
				if(!is_null($section_id)){
					break;
				}
			}
		}
		return $section_id;
	}

	/*-----------------------------------/
	* Gets status for given section
	*
	* <AUTHOR> Army
	* @param	$section_id
	* @return	String	Enabled or Disabled
	*/
	public function get_section_status($section_id){
		$status = NULL;
		$this->db->query("SELECT `status` FROM `cms_sections` WHERE `section_id` = ?", array($section_id));
		if(!$this->db->error() && $this->db->num_rows()){
			$result = $this->db->fetch_array();
			$status = $result[0]['status'];
		}
		return $status;
	}

	/*-----------------------------------/
	* Gets closest parent id based on url
	*
	* <AUTHOR> Army
	* @param	$page_url	Full page url
	* @return	Integer		Parent id
	*/
	public function get_parent_id($page_url){
		$parent_id = NULL;
		$pathbits = explode('/', $page_url);

		for($i=2; $i<count($pathbits); $i++){
			$pagebits = array_slice($pathbits, 0, count($pathbits)-$i);
			$parent_id = $this->get_section_id(implode('/', $pagebits).'/');
			if(!empty($parent_id)) break;
		}

		return $parent_id;
	}

	/*-----------------------------------/
	* Gets breadcrumb info for current section user is on
	*
	* <AUTHOR> Army
	* @return	Array of breadcrumb sections
	*/
	public function get_breadcrumb(){
		$breadcrumb = array();
		$url = $this->path;

		for($i=1; $i<count($this->pathbits); $i++){
			if($this->pathbits[$i] != ''){
				$url .= $this->pathbits[$i]. '/';
				foreach($this->sitemap as $id=>$section) {
					if($section['page_url'] == $url)
						array_push($breadcrumb, array('url' => $url, 'name' => $section['name']));
				}
			}
		}

		return $breadcrumb;
	}

	/*-----------------------------------/
	* Sets dashboard widgets
	*
	* <AUTHOR> Army
	* @param	$section_id		The section ID for the widget
	* @param	$title			Title of row
	* @param	$value			Value of row
	* @param	$icon			FontAwesome icon
	*/
	public function set_widget($section_id, $title=NULL, $value=NULL, $icon=NULL, $url=NULL){
		$widget = $this->get_section($section_id);
		if(!$widget['error404'] && $this->check_permissions($widget['parent_id'] ?: $section_id)){

			//Set widget
			$this->widgets[] = array(
				'section_id' => $section_id,
				'title' => (!is_null($title) ? $title : $widget['name']),
				'value' => (!is_null($value) ? $value : 0),
				'icon' => (!is_null($icon) ? $icon : $widget['icon']),
				'url' => (!is_null($url) ? $url : $this->sitemap[$section_id]['page_url']),
			);
		}
	}

	/*-----------------------------------/
	* Retrieves dashboard widgets
	*
	* <AUTHOR> Army
	* @returns	Array of data
	*/
	public function get_widgets(){
		return $this->widgets;
	}

	/*-----------------------------------/
	* Sets system alert session
	*
	* <AUTHOR> Army
	* @param	$message	Alert message (text/html)
	* @param	$status		Boolean Success/Error
	*/
	public function set_system_alert($message, $status){
		if(!isset($_SESSION['system_alert'])){
			$_SESSION['system_alert'] = array();
		}
		array_push($_SESSION['system_alert'], array(
			'message' => $message,
			'status' => $status
		));
	}

	/*-----------------------------------/
	* Retrieves system alert session and unsets it
	*
	* <AUTHOR> Army
	* @returns	Array of alert session data or NULL
	*/
	public function system_alert(){
		if(isset($_SESSION['system_alert'])){
			$alert = $_SESSION['system_alert'];
			unset($_SESSION['system_alert']);
			return $alert;
		}
		return NULL;
	}

	/*-----------------------------------/
	* Sets section search session
	*
	* <AUTHOR> Army
	* @param	$searchterm		The search term
	* @param	$section		The relevant section id
	*/
	public function set_system_search($searchterm, $section){
		if(is_null($searchterm)){
			unset($_SESSION['system_search'][$section]);
		} else {
			$_SESSION['system_search'][$section] = $searchterm;
		}
	}

	/*-----------------------------------/
	* Retrieves system search for section
	*
	* <AUTHOR> Army
	* @returns	Array of alert session data or NULL
	*/
	public function system_search($section){
		if(!empty($_SESSION['system_search'][$section])){
			$searchterm = $_SESSION['system_search'][$section];
			return $searchterm;
		}
		return NULL;
	}

	/*-----------------------------------/
	* Generates tooltip
	*
	* <AUTHOR> Army
	* @param	$title		Tooltip title
	* @param	$content	Tooltip content (supports html tags)
	* @param	$icon		The icon to be displayed - this is what the user will see and hover over (supports html tags)
	* @param	$classes 	Custom CSS classes to add to the tooltip
	* @returns	HTML output for tooltip
	*/
	public function tooltip($title, $content, $icon = "?", $classes=array()){
		$classes = ' ' . implode(' ', $classes);
		$title = trim(htmlentities($title));
		$content = trim(htmlentities($content));
		return '<span class="tooltip' . $classes . '" title="'.($title ? '<h4>' .$title. '</h4>' : '').($content ? '<div>' .$content. '</div>' : '').'">' . $icon . '</span>';
	}

	/*-----------------------------------/
	* Generates important information alert
	*
	* <AUTHOR> Army
	* @param	$message	Important content (supports html tags)

	* @returns	HTML output for important alert
	*/
	public function important($message){
		return '<div class="panel system-alert important">
			<div class="title"><i class="fas fa-exclamation-triangle"></i>Important Notice! <span class="f_right"><a class="panel-toggle fas fa-chevron-up"></a></span></div>
			<div class="panel-content message">'.$message.'</div>
		</div>';
	}

	/*-----------------------------------/
	* Generates show/hide toggle switch for table list items
	*
	* <AUTHOR> Army
	* @param	$record_db		The name of the table being updated
	* @param	$record_id		The field name of the primary key for the table
	* @param	$item_id		The list item id to be updated
	* @param	$item_status	The current show/hide status of the item
	* @param	$item_col		The column to be updated in the table (default = showhide)
	* @returns	HTML output
	*/
	public function showhide_toggle($record_db, $record_id, $item_id, $item_status, $item_col = "showhide"){

		//Reverse boolean for showhide
		if($item_col == "showhide"){
			$item_status = !$item_status;
		}

		return '<span class="switch-sorter">' .($item_status ? "True" : "False"). '</span>
		<div class="onoffswitch">
			<input type="checkbox" name="'.$item_col.$item_id.'" id="'.$item_col.$item_id.'" value="0"' .($item_status ? " checked" : ""). ' class="ajax-showhide" data-table="'.$record_db.'" data-tableid="'.$record_id.'" data-itemid="'.$item_id.'" data-itemcol="'.$item_col.'" />
			<label for="'.$item_col.$item_id.'">
				<span class="inner"></span>
				<span class="switch"></span>
			</label>
		</div>';
	}

	/*-----------------------------------/
	* Generates status options (visible, hidden and disabled)
	*
	* <AUTHOR> Army
	* @param	$record_db		The name of the table being updated
	* @param	$record_id		The field name of the primary key for the table
	* @param	$item_id		The list item id to be updated
	* @param	$item_status	The current show/hide status of the item
	* @param	$item_col		The column to be updated in the table (default = showhide)
	* @returns	HTML output
	*/
	public function status_toggle($record_db, $record_id, $item_id="", $item_status = 0, $item_col = "showhide"){

		return '<span class="switch-sorter">' .($item_status == 0 ? "Visible" : ($item_status == 1 ? "Hidden" : "Disabled")). '</span>
		<div class="item-status'.($item_id == '' ? ' no-ajax' : '').'" data-table="'.$record_db.'" data-tableid="'.$record_id.'" data-itemid="'.$item_id.'" data-itemcol="'.$item_col.'">
			<button type="button" name="'.$item_col.'" class="button center f_left' .($item_status == 0 ? ' active' : ''). '" value="0"><i class="fas fa-eye"></i></button>
			<button type="button" name="'.$item_col.'" class="button center f_left' .($item_status == 1 ? ' active' : ''). '" value="1"><i class="fas fa-link"></i></button>'.
			($record_db != 'pages' || ($record_db == 'pages' && $item_id != 3 && ITEM_ID != 3) ? '<button type="button" name="'.$item_col.'" class="button center f_left' .($item_status == 2 ? ' active' : ''). '" value="2"><i class="fas fa-eye-slash"></i></button>' : '').
			($item_id == '' ? '<input type="hidden" name="'.$item_col.'" value="'.$item_status.'" />' : '').'
		</div>';
	}

	/*-----------------------------------/
	* Generates mini alert (ajax style)
	*
	* <AUTHOR> Army
	* @param	$message	Alert content (supports html tags)
	* @param	$status		Boolean Success/Error

	* @returns	HTML output for mini alert
	*/
	public function mini_alert($message, $status){
		return '<div class="system-alert mini '.($status ? "success" : "error").'">
			<div class="title">' .($status ? '<i class="fas fa-check"></i>Success' : '<i class="fas fa-times"></i>Error'). '</div>
			<div class="message">'.$message.'</div>
		</div>';
	}

	/*-----------------------------------/
	* Generates pager for tablesorter table
	*
	* <AUTHOR> Army
	* @param	$prevnext	True/false to display previous and next buttons
	* @param	$firstlast	True/false to display first and last buttons
	* @param	$gotopage	True/false to display page selector
	* @param	$pagesize	Number of results to display per page
	* @returns	HTML output for tooltip
	*/
	public function tablesorter_pager($pagesize=50, $prevnext=true, $firstlast=true, $gotopage=true){

		$pager = '<div class="pager" data-pagesize="' .$pagesize. '">';
		$pager .= '<span class="pagedisplay"></span>';
		$pager .= '<div class="pagebuttons clearfix">';
			$pager .= ($firstlast ? '<span class="button-sm first">&laquo;</span>' : '');
			$pager .= ($prevnext ? '<span class="button-sm prev">&lsaquo;</span>' : '');
			$pager .= ($gotopage ? '<select class="gotoPage select"></select>' : '');
			$pager .= ($prevnext ? '<span class="button-sm next">&rsaquo;</span>' : '');
			$pager .= ($firstlast ? '<span class="button-sm last">&raquo;</span>' : '');
		$pager .= '</div>';
		$pager .= '</div>';

		echo $pager;
	}

	/*-----------------------------------/
	* Generates delete button for table list items
	*
	* <AUTHOR> Army
	* @param	$record_db		The name of the table being updated
	* @param	$record_id		The field name of the primary key for the table
	* @param	$item_id		The list item id to be updated
	* @param	$item_status	The current status of the item (if not really deleting but just setting a column to indicate item is deleted; eg: status: Trashed)
	* @param	$item_col		The column to be updated in the table (default = status)
	* @param	$deletable		Check if item is deletable
	* @returns	HTML output for delete button
	*/
	public function delete_btn($record_db, $record_id, $item_id, $item_status="", $item_col="status", $deletable=1){
		$button = '';
		if($deletable) {
			$button = '<button type="button" name="delete" value="delete" class="ajax-delete button-sm" data-table="'.$record_db.'" data-tableid="'.$record_id.'" data-itemid="'.$item_id.'" data-itemstatus="'.$item_status.'" data-itemcol="'.$item_col.'"><i class="fas fa-trash-alt"></i>Delete</button>';
		}
		return $button;
	}

	/*-----------------------------------/
	* Generates a link to item in a given section
	*
	* <AUTHOR> Army
	* @param	$name		The name of the link
	* @param	$section_id	The section ID
	* @param	$item_id	The item ID
	* @param	$action		Action of the page.  Default to 'edit'
	* @param	$icon		Icon of the link.  Default to 'pencil-alt' icon
	* @returns	HTML output for item link
	*/
	public function item_link($name, $section_id, $item_id=NULL, $action='edit', $icon='<i class="fa fa-pencil-alt"></i>') {
		global $_cmssections;
		$link       = $name;
		$page_url   = false;
		$perm       = false;
		$section    = $this->get_section(($_cmssections[$section_id] ?? $section_id), $_SERVER['DOCUMENT_ROOT'].$this->path.'sections/');
		
		if (!$section['error404']) {
			$page_url = $section['page_url'];
			$perm     = $this->check_permissions($section['parent_id'] ?: $section['section_id']);
		}

		// Don't show link if item ID is defined (not null) but empty
		if ($page_url && $perm && ($item_id || is_null($item_id))) {
			$link .= '&nbsp;&nbsp;<a href="'.$page_url.($item_id ? '?action='.$action.'&item_id='.$item_id : '').'" style="--fa-display: inline;">'.$icon.'</a>';
		}

		return $link;
	}

	/*-----------------------------------/
	* Generates an image preview and optional delete checkbox
	*
	* <AUTHOR> Army
	* @param	$filename		The name of the image
	* @param	$full_dir		The directory of the larger cropped image, for lightgallery preview.
	* @param	$thumb_dir		The directory of the smaller cropped image, for display.  Defaults to $full_dir
	* @param	$deletable		Name of delete checkbox.  False if required
	* @param	$fieldname		The fieldname of the image
	* @returns	HTML output for image holder
	*/
	public function img_holder($filename, $full_dir, $thumb_dir = NULL, $deletable = true, $fieldname = 'image') {
		global $path;
		
		$html = '';
		if (check_file($filename, $full_dir)) {
			$html .= '<div class="img-holder">
				<button type="button" name="recrop" value="'.$fieldname.'" class="recrop-button" title="Re-crop Image"><i class="fas fa-crop"></i></button>
				<a href="'.$path.$full_dir.$filename.'" class="light-gallery" target="_blank">
					<img src="'.$path.($thumb_dir ?? $full_dir).$filename.'" />
				</a>';
			
			if ($deletable) {
				$html .= '<input type="checkbox" class="checkbox" name="delete'.$fieldname.'" id="delete'.$fieldname.'" value="1">
				<label for="delete'.$fieldname.'">Delete Current Image</label>';
			}

			$html .= '</div>';
		}

		return $html;
	}
}

?>