<?php
//Online withdrawal
if(PAGE_ID == $_sitepages['withdrawal']['page_id']){

	//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
		exit();
	}

	//Check for valid registration
	
	//Define vars
	$panel_id = 75;// 132;
	$confirm = false;

	$admin_email = (!empty($reg_settings['email_tournaments']) ? $reg_settings['email_tournaments'] : $global['contact_email']);

	$registration = array();
	$transactions = array();

	$total_paid = 0; //Total amount paid on full registration
	$total_refunded = 0; //Total amount refunded on full registration
	$attendee_paid = 0; //Total amount paid on specific attendee
	$attendee_refunded = 0; //Total amount refunded to specific attendee

	$refund_owing = 0;
	$balance_owing = 0;

	//Get registration
	$params = array(ITEM_ID, 'Registered', USER_LOGGED_IN, USER_LOGGED_IN);
	$query = $db->query("SELECT `reg_attendees`.*, `reg_events`.`event_type`, `reg_events`.`name` AS `event_name`, `reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`, `reg_occurrences`.`reg_deadline`, `reg_occurrences`.`payment_deadline`, `reg_occurrences`.`withdrawal_deadline`, `reg_registrations`.`registration_number`, `reg_registrations`.`paid`, `reg_registrations`.`registration_total`, `reg_registrations`.`registration_date`, `reg_registrations`.`gst_rate`, `reg_registrations`.`pst_rate` FROM `reg_attendees` ".
	"LEFT JOIN `reg_events` ON `reg_events`.`event_id` = `reg_attendees`.`event_id` ".
	"LEFT JOIN `reg_occurrences` ON `reg_occurrences`.`occurrence_id` = `reg_attendees`.`occurrence_id` ".
	"LEFT JOIN `reg_registrations` ON `reg_attendees`.`registration_id` = `reg_registrations`.`registration_id` ".
	"WHERE `reg_attendees`.`attendee_id` = ? && `reg_attendees`.`reg_status` = ? && `reg_events`.`event_type` = 2 && (`reg_attendees`.`account_id` = ? || `reg_registrations`.`account_id` = ?)", $params);
	if($query && !$db->error() && $db->num_rows()){
		$result = $db->fetch_array();

		// echo "<pre>";
		// print_r($result);
		// exit();

		$registration = $result[0];

		//Cannot withdraw online after registration deadline
		if(strtotime($registration['reg_deadline'].' '.$reg_settings['reg_close_time']) <= strtotime('now')){
			// header('Location: '.$sitemap[60]['page_url'].'?action=edit&id='.$registration['registration_id']);
			header('Location: '.$_sitepages['my-registrations']['page_url'].'?action=edit&id='.$registration['registration_id']);
			exit();
		}

		//Get registration payments
		$query = $db->query("SELECT * FROM `payments` WHERE `registration_id` = ? && `status` = 1", array($registration['registration_id']));
		if($query && !$db->error()){
			$payments = $db->fetch_array();
			foreach($payments as $payment){
				$total_paid += $payment['amount'];

				$query = $db->query("SELECT SUM(`refund_attendees`.`amount`) AS `attendee_refunded`, SUM(`refunds`.`amount`) AS `refunded` FROM `refund_attendees` LEFT JOIN `refunds` ON `refund_attendees`.`refund_id` = `refunds`.`refund_id` WHERE `refunds`.`payment_id` = ? && `refunds`.`status` = 1 && `refund_attendees`.`attendee_id` = ?", array($payment['payment_id'], ITEM_ID));
				if($query && !$db->error() && $db->num_rows()){
					$refunds = $db->fetch_array();
					$payment['attendee_refunded'] = $refunds[0]['attendee_refunded'];
					$payment['refunded'] = $refunds[0]['refunded'];
					$attendee_refunded += $refunds[0]['attendee_refunded'];
				}

				//Check if payment was refunded
				$query = $db->query("SELECT SUM(`amount`) AS `refunded`, ".
				"(SELECT SUM(`amount`) FROM `refund_attendees` WHERE `refund_attendees`.`refund_id` = `refunds`.`refund_id` && `refund_attendees`.`attendee_id` = ?) AS `attendee_refunded` ".
				"FROM `refunds` WHERE `payment_id` = ? && `status` = 1", array(ITEM_ID, $payment['payment_id']));
				if($query && !$db->error() && $db->num_rows()){
					$refunds = $db->fetch_array();
					$payment['attendee_refunded'] = $refunds[0]['attendee_refunded'];
					$payment['refunded'] = $refunds[0]['refunded'];
					$attendee_refunded = $refunds[0]['attendee_refunded'];
					$total_refunded += $refunds[0]['refunded'];
				}

				$transactions[] = $payment;

			}
		}

		//Calculate attendee totals
		$subtotal = $registration['ticket_price'];
		$fees = $registration['tournament_fee'];
		$taxes = $subtotal*(($registration['gst_rate']+$registration['pst_rate'])/100);
		$total = number_format($subtotal+$taxes+$fees, 2, '.', '');

		//If attendee was paid in full, determine refund due
		if(($total_paid-$total_refunded) >= $total){
			$attendee_paid = $total;

		//Partial payment was made
		}else{
			$attendee_paid = ($total_paid-$total_refunded);
		}

		//Determine refund owing
		$refund_owing = $attendee_paid-$attendee_refunded;

		//Charge withdrawal fee (dollar amount) if passed close time on PAYMENT deadline and NOT a partner
		$withdrawal_fee = 0;
		if(date("Y-m-d H:i:s") >= $registration['payment_deadline'].' '.$reg_settings['reg_close_time'] && empty($registration['partner_id'])){
			$withdrawal_fee = $reg_settings['cancellation_fee'];
		}

		//Charge withdrawal fee (percentage of total fees) if passed close time on WITHDRAWAL deadline and NOT a partner
		$withdrawal_percent = 0;
		if(date("Y-m-d H:i:s") >= $registration['withdrawal_deadline'].' '.$reg_settings['reg_close_time'] && empty($registration['partner_id'])){

			//Calculate percentage of total price
			$withdrawal_percent = number_format(($reg_settings['cancellation_fee_late']/100)*$total, 2, '.', '');
		}
		if($withdrawal_percent > 0){
			$withdrawal_fee = $withdrawal_percent;
		}
		//Charge late withdrawal fee (dollar amount) if passed close time on WITHDRAWAL deadline and NOT a partner
		/*if(date("Y-m-d H:i:s") >= $registration['withdrawal_deadline'].' '.$reg_settings['reg_close_time'] && empty($registration['partner_id'])){
			$withdrawal_fee = $reg_settings['cancellation_fee_late'];
		}*/

		if($withdrawal_fee > $refund_owing){
			$balance_owing = $withdrawal_fee-$refund_owing;
			$refund_owing = 0;
		}else{
			$refund_owing = $refund_owing-$withdrawal_fee;
		}

		//Format totals
		$balance_owing = number_format($balance_owing, 2, '.', '');
		$refund_owing = number_format($refund_owing, 2, '.', '');


	//Not found
	}else{
		// header('Location: '.$sitemap[60]['page_url'].'?action=edit&id=null');
		// exit('not found ');
		
		header('Location: '.$_sitepages['my-registrations']['page_url'].'?action=edit&id=null');
		exit();
	}

	//Confirm
	if(isset($_POST['continue'])){
		$confirm = true;
	}

	//Submit
	if(isset($_POST['submit'])){

		//Do withdrawal
		try{
			$Registration->update_attendee_status(ITEM_ID, 'Withdrawn');
			$reg_alert = $registration['first_name'].' '.$registration['last_name'].' has been successfully withdrawn';

			//If refund owing, only process if paid on one transaction by credit card
			$success = false;
			if($refund_owing > 0 && count($transactions) == 1 && !empty($transactions[0]['txn_num'])){

				//Insert refund data
				$params = array(
					$registration['registration_id'],
					$transactions[0]['payment_id'],
					'Credit Card',
					$refund_owing,
					'Automatic withdrawal refund',
					USER_LOGGED_IN,
					date("Y-m-d H:i:s")
				);
				$query = $query = $db->query("INSERT INTO `refunds`(`registration_id`, `payment_id`, `refund_type`, `amount`, `notes`, `processed_by`, `refund_date`) VALUES(?,?,?,?,?,?,?)", $params);
				if($query && !$db->error()){
					$order_id = $db->insert_id();
					$ordernum = 'R'.date("ymd").'-'.str_pad($order_id, 5, '0', STR_PAD_LEFT);

					$autosettle_start = date('Ymd', strtotime('-1 day')).'23'; //Yesterday at 11pm
					$autosettle_end = date('Ymd').'23'; //Today at 11pm
					$payment_datetime = date('YmdG', strtotime($transactions[0]['payment_date']));

					//Format void request (if transaction is before autosettle at 11pm)
					if($payment_datetime >= $autosettle_start && $payment_datetime < $autosettle_end){
						$request = array(
							'type' => 'reversalRequest',
							'ordernum' => $transactions[0]['payment_number'],
							'refund_amount' => $refund_owing,
							'txn_num' => $transactions[0]['txn_num'],
							'txn_tag' => $transactions[0]['txn_tag'],
						);

					//Format refund request
					}else{
						$request = array(
							'type' => 'refundRequest',
							'ordernum' => $transactions[0]['payment_number'],
							'refund_amount' => $refund_owing,
							'txn_num' => $transactions[0]['txn_num'],
							'txn_tag' => $transactions[0]['txn_tag'],
						);
					}

					//Send request
					include("includes/orbital/request.php");

					//Success response
					if($trxnResponse['status'] == 1){
						$success = true;
						$status = '1';
					}else{
						$success = false;
						$status = '0';
						$errors[] = $trxnResponse['message'];
					}

					//Update refund response
					$response = array(
						$status,
						$trxnResponse['txn_num'],
						$trxnResponse['txn_tag'],
						$trxnResponse['message'],
						$ordernum,
						$order_id
					);
					$query = $db->query("UPDATE `refunds` SET `status`=?, `txn_num`=?, `txn_tag`=?, `message`=?, `refund_number`=? WHERE `refund_id`=?", $response);
					if(!$query || $db->error()){
						trigger_error('Error updating refund response for ' .$ordernum. ': '.$db->error());
					}

				}

				//Successful
				if($success){

					//Insert attendee refund line item
					$params = array($order_id, ITEM_ID, $refund_owing);
					$query = $db->query("INSERT INTO `refund_attendees`(`refund_id`, `attendee_id`, `amount`) VALUES(?,?,?)", $params);
					if(!$query || $db->error()){
						trigger_error('Error inserting attendee refund line item for ' .$ordernum. ': '.$db->error());
					}

					$reg_alert .= ' and a refund has been issued.';

					//Format receipt
					$order_data = array(
						'first_name' => $Account->first_name,
						'last_name' => $Account->last_name,
						'email' => $Account->email,
						'phone' => $Account->phone,
						'record_name' => 'Registration No.',
						'record_number' => $registration['registration_number'],
						'refund_type' => 'Credit Card',
						'refund_number' => $ordernum,
						'refund_date' => date('Y-m-d H:i:s'),
						'ordertotal' => $refund_owing,
						'ccname' => $transactions[0]['ccname'],
						'cctype' => $transactions[0]['cctype'],
						'ccnumber' => $transactions[0]['ccnumber'],
						'ccexpiry' => $transactions[0]['ccexpiry'],
						'attendee_refund_amount' => $refund_owing,
						'attendees' => array(
							0 => array(
								'first_name' => $registration['first_name'],
								'last_name' => $registration['last_name'],
								'event_name' => $registration['event_name'],
								'refund_amount' => $refund_owing
							)
						)
					);
					$order_data = array_merge($order_data, $trxnResponse);
					$refund_receipt = $Registration->refund_receipt($order_data);
					$send_email = send_email($Account->email, 'Refund Confirmation', $refund_receipt);
					send_email($admin_email, 'Refund Confirmation', $refund_receipt);

				}

			}

			//If refund owing or balance owing and cannot process it, notify admin
			if(($refund_owing > 0 && !$success) || $balance_owing > 0){

				//Set alert
				if($refund_owing > 0){
					$reg_alert .= '. Refund will be processed separately and you will receive notification once complete.';
				}
				if($balance_owing > 0){
					$reg_alert .= '. Balance due will be invoiced separately.';
				}

				//Send admin notification
				$subject = 'Withdrawal '.($refund_owing > 0 ? 'Refund' : 'Penalty');
				$message = '<h3>' .$subject.'</h3>
				<p>The following withdrawal for <strong>' .$registration['first_name'].' '.$registration['last_name']. '</strong> could not be automatically processed and administrator attention is required:</p>
				<p><strong>Registration No. ' .$registration['registration_number']. '</strong><br />
				<strong>' .$registration['event_name']. ' on ' .format_date_range($registration['start_date'], $registration['end_date']). '.</strong></p>
				<p>
					<strong>Refund Amount: $' .number_format($attendee_paid-$attendee_refunded, 2). '</strong><br />
					<strong>Withdrawal Penalty: $' .number_format($withdrawal_fee, 2). '</strong><br />';
					if($refund_owing > 0){
						$message .= '<strong>Refund Due: $' .number_format($refund_owing, 2). '</strong><br />';
					}else{
						$message .= '<strong>Balance Due: $' .number_format($balance_owing, 2). '</strong><br />';
					}
				$message .= '</p>
				<p>Please login to your content management system to review and process this request.</p>';
				send_email($admin_email, $subject, $message);

			}

			//Set alert
			$_SESSION['reg_alert'] = $Account->alert($reg_alert, true);

		//Error
		}catch(Exception $e){
			$_SESSION['reg_alert'] = $Account->alert('Unable to process withdrawal. Please try again. '.$e->getMessage(), false);
		}

		//Send back to registration
		// header('Location: '.$sitemap[60]['page_url'].'?action=edit&id='.$registration['registration_id']);
		header('Location: '.$_sitepages['my-registrations']['page_url'].'?action=edit&id='.$registration['registration_id']);
		exit();
	}

}


?>