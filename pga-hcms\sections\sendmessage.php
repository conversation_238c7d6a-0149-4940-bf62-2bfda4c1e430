<?php  

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

echo $CMSBuilder->important("<strong>Sending to Recipients:</strong> Once a message is sent to recipients it cannot be sent again at a later time. Only opted in members will receive an email. However, opted out members will still see the message in their Message Centre.");

echo "<form action='' method='post' enctype='multipart/form-data' class='multiselects'>";

	//Details
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Message 
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content clearfix'>";
			echo "<div class='form-field'>
				<label>Select Message</label>";
				if(!empty($messages)){
					echo "<select name='message_id' class='select'>
						<option value=''>- Select -</option>";
						foreach($messages as $message) {
							echo "<option value='".$message['message_id']."' ".(isset($row['message_id']) && $row['message_id'] == $message['message_id'] ? 'selected' : '').">".$message['subject']." (".date('M d, Y', strtotime($message['date_added'])).")</option>";
						}
					echo "</select>";
				} else {
					echo "<em style='line-height:40px;'>No messages available for sending.</em>";
				}
			echo "</div>";
			echo "<div class='form-field'>
				<label>Audience</label>
				<select name='audience' class='select' onChange='showAudience(this.value)'>";
					echo "<option value='test' ".(isset($row['audience']) && $row['audience'] == 'test').">Send Test Message</option>";
					foreach($audiences as $a=>$value){
						echo "<option value='".$a."' ".(isset($row['audience']) && $row['audience'] == $a ? " selected" : "").">" .$value['label']. "</option>";
					}
				echo "</select>";
			echo "</div>";
		echo "</div>";
	echo "</div>"; // END Details

	//Test Message
	echo "<div id='test_table' class='panel ".(!isset($row['audience']) || $row['audience'] == 'test' ? "" : " hidden")."'>";
		echo "<div class='panel-header'>Send Test Message  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content'>";
			echo "<div class='form-field'>
				<label>Send to Email(s) <span class='required'>*</span> " .$CMSBuilder->tooltip('Send to Email(s)', 'Enter one or more emails to receive a test newsletter before sending to subscribers.</p><small><strong>Note:</strong> Comma separate emails if sending to more than one email. (e.g. <EMAIL>, <EMAIL>)</small>'). "</label>
				<input type='text' name='test_emails' value='" .(isset($row['test_emails']) ? $row['test_emails'] : $Account->email). "' class='input" .(in_array('test_emails', $required) ? ' required' : ''). "' />";
			echo "</div>";
		echo "</div>";	
	echo "</div>";
	
	//Audience
	foreach($audiences as $a=>$audience){
		if($a != 'all'){
			
			echo "<div id='audience-" .$a. "' class='panel audience_table ".(!isset($row['audience']) || $row['audience'] != $a ? "hidden" : "")."'>";
				echo "<div class='panel-header'>" .$audience['label']. "  
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content nopadding'>";
							
					//Member classes
					if($a == 'classes'){

						echo "<div class='panel-content clearfix'>";						
							echo "<div class='multiselects-wrapper clearfix'>";
								echo "<div class='multiselect-field inlineblock v_middle'>";
									echo "<label>Member Types</label>";
									echo "<select name='class_id' class='multiselect' size='5' data-action='add' data-move='1' data-level='1'>";
										foreach($classes as $class) {
											echo "<option value='".$class['class_id']."'>" .$class['class_name']. "</option>";
										}
									echo "</select>";
								echo "</div>";

								echo "<div class='multiselect-arrows inlineblock v_middle'>";
									echo "<a href='#' class='multiselect-arrow button disabled' data-action='add'>&raquo;</a>";
									echo "<a href='#' class='multiselect-arrow button disabled' data-action='remove'>&laquo;</a>";
								echo "</div>";

								echo "<div class='multiselect-field inlineblock v_middle'>";
									echo "<label>Selected Member Types</label>";
									echo "<select name='classes[]' class='multiselect' multiple='multiple' data-action='remove' data-move='1'>";
										if(isset($row['classes']) && !empty($row['classes'])){
											foreach($classes as $class) {
												if(in_array($class['class_id'], $row['classes'])){
													echo "<option value='".$class['class_id']."'>" .$class['class_name']. "</option>";
												}
											}
										}
									echo "</select>";
								echo "</div>";							
							echo "</div>";				
						echo "</div>";

					//Tournaments
					}else if($a == 'tournaments'){

						echo "<div class='panel-content clearfix'>";						
							echo "<div class='multiselects-wrapper clearfix'>";
								echo "<div class='multiselect-field inlineblock v_middle'>";
									echo "<label>Tournaments</label>";
									echo "<select name='event_id' class='multiselect' size='5' data-action='add' data-move='1' data-level='1' style='width:500px; height:600px;'>";
										foreach($tournaments as $event){
											echo "<option value='".$event['event_id']."'>" .$event['name']. ", " .format_date_range($event['start_date'], $event['end_date']). "</option>";
										}
									echo "</select>";
								echo "</div>";

								echo "<div class='multiselect-arrows inlineblock v_middle'>";
									echo "<a href='#' class='multiselect-arrow button disabled' data-action='add'>&raquo;</a>";
									echo "<a href='#' class='multiselect-arrow button disabled' data-action='remove'>&laquo;</a>";
								echo "</div>";

								echo "<div class='multiselect-field inlineblock v_middle'>";
									echo "<label>Selected Tournaments " .$CMSBuilder->tooltip('Selected Tournaments', 'Will only send to members who are registered for the selected tournament(s).'). "</label>";
									echo "<select name='events[]' class='multiselect' multiple='multiple' data-action='remove' data-move='1' style='width:500px; height:600px;'>";
										if(isset($row['events']) && !empty($row['events'])){
											foreach($tournaments as $event) {
												if(in_array($event['event_id'], $row['events'])){
													echo "<option value='".$event['event_id']."'>" .$event['name']. ", " .format_date_range($event['start_date'], $event['end_date']). "</option>";
												}
											}
										}
									echo "</select>";
								echo "</div>";							
							echo "</div>";				
						echo "</div>";

					}else if($a == 'members'){

						echo "<div class='panel-content clearfix'>";						
							echo "<div class='multiselects-wrapper clearfix'>";
								echo "<div class='multiselect-field inlineblock v_middle'>";
									echo "<label>Members</label>";
									echo "<select name='account_id' class='multiselect' size='5' data-action='add' data-move='1' data-level='1' style='width:500px; height:600px;'>";
										foreach($members as $member){
											echo "<option value='".$member['account_id']."'>" .$member['last_name'].", ".$member['first_name']." - ".$member['facility_name']. "</option>";
										}
									echo "</select>";
								echo "</div>";

								echo "<div class='multiselect-arrows inlineblock v_middle'>";
									echo "<a href='#' class='multiselect-arrow button disabled' data-action='add'>&raquo;</a>";
									echo "<a href='#' class='multiselect-arrow button disabled' data-action='remove'>&laquo;</a>";
								echo "</div>";

								echo "<div class='multiselect-field inlineblock v_middle'>";
									echo "<label>Selected Members</label>";
									echo "<select name='accounts[]' class='multiselect' multiple='multiple' data-action='remove' data-move='1' style='width:500px; height:600px;'>";
										if(isset($row['accounts']) && !empty($row['accounts'])) {
											foreach($members as $member){
												if(in_array($member['account_id'], $row['accounts'])){
													echo "<option value='".$member['account_id']."'>" .$member['last_name'].", ".$member['first_name']." - ".$member['facility_name']. "</option>";
												}
											}
										}
									echo "</select>";
								echo "</div>";							
							echo "</div>";				

						echo "</div>";

					}

				echo "</div>";	
			echo "</div>";
			
		}
	}

	//Sticky footer
	include("includes/widgets/formbuttons.php");

	echo '<footer id="cms-footer" class="resize">';
		echo '<button type="submit" name="send" value="send" class="button f_right"><i class="fas fa-paper-plane"></i>Send Message</button>';
	echo '</footer>';

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

echo "</form>";

?>