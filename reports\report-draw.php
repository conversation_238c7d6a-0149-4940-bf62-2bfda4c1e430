<?php

//Config
include("config.php");

//Get vars
$id = (isset($_GET['id']) ? $_GET['id'] : NULL);
$format = (isset($_GET['format']) ? $_GET['format'] : 'html');
$sort = (isset($_GET['sort']) ? $_GET['sort'] : '');
$columns = array();
$genders = array();
$skins = array();
$csv_rows = array();

//Get event
$event = $Registration->get_occurrence($id);
if(empty($event)){
	header('Location: '.$_sitepages['tournaments']['page_url']);
	exit();
}else{
		
	//Check report permissions
	if((USER_LOGGED_IN && $Account->account_has_role(1)) || (isset($event['report_draw'.$sort]) && $event['report_draw'.$sort])){
		
		//Ordering
		if($sort == 'score'){
			$orderby = "-`reg_tournament_field`.`r2_time` DESC, CAST(`reg_tournament_field`.`r2_hole` AS UNSIGNED), `reg_tournament_field`.`r2_hole` ASC, `reg_tournament_field`.`r1_score` DESC";
		}else if($sort == 'alpha'){
			$orderby = "`reg_attendees`.`last_name` ASC, `reg_attendees`.`first_name` ASC";
		}else{
			$orderby = "-`reg_tournament_field`.`r1_time` DESC, CAST(`reg_tournament_field`.`r1_hole` AS UNSIGNED), `reg_tournament_field`.`r1_hole` ASC";
		}
		
		//Get tournament field
		$records_arr = array();
		$query = $db->query("SELECT `reg_tournament_field`.*, `reg_attendees`.*, `reg_tournament_field`.`gender` AS `sort_gender`, `reg_attendees`.`attendee_id` AS `attendee_id`, `facilities`.`facility_name` FROM `reg_attendees` ".
		"LEFT JOIN `reg_tournament_field` ON `reg_attendees`.`attendee_id` = `reg_tournament_field`.`attendee_id` ".
		"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"WHERE `reg_attendees`.`occurrence_id` = ? && `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NULL ".
		"ORDER BY $orderby", 
		array($id, 'Registered'));
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				
				//Junior masters
				if(!empty($row['flight'])){
					$columns[] = 'flight';
				}
				if(!empty($row['sort_gender'])){
					$genders[$row['sort_gender']] = ($row['sort_gender'] == 'Male' ? 'Boys' : 'Girls');
				}
				
				//R1 score R2 draw
				if($sort == "score"){
					$columns[] = 'score';
					if(!empty($row['r2_time'])){
						$columns[] = 'time';
					}
					if(!empty($row['r2_hole'])){
						$columns[] = 'hole';
					}
					
					$row['time'] = $row['r2_time'];
					$row['hole'] = $row['r2_hole'];
					$row['score'] = $row['r1_score'];
					if($event['team_event']){
						if(!empty($row['r1_team_gross'])){
							$row['score'] = $row['r1_team_gross'];
						}else{
							$row['score'] = $row['r1_team_net'];
						}
					}
					
					//R1 skins
					if(!empty($row['r1_skins'])){
						$reports[] = 'skins';
						$r1_skins = explode(',', $row['r1_skins']);
						foreach($r1_skins as $hole){
							$hole = str_replace('#', '', trim($hole));
							if($hole != ""){
								$skins[1][$hole] = '#'.$hole.' '.$row['last_name'].', '.$row['first_name'];
							}
						}
					}
					
					
				//R1 draw
				}else{
					if(!empty($row['r1_time'])){
						$columns[] = 'time';
					}
					if(!empty($row['r1_hole'])){
						$columns[] = 'hole';
					}
					$row['time'] = $row['r1_time'];
					$row['hole'] = $row['r1_hole'];
				}
				
				
				$row['handicap'] = (!empty($row['handicap']) ? $row['handicap'] : 0);
				$row['partner'] = array();
				$records_arr[$row['attendee_id']] = $row;
				
			}
		}
		
		//Partners
		if($event['team_event']){
			$query = $db->query("SELECT `reg_attendees`.*, `facilities`.`facility_name` FROM `reg_attendees` ".
			"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
			"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
			"WHERE `reg_attendees`.`occurrence_id` = ? && `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NOT NULL ".
			"ORDER BY `reg_attendees`.`last_name` ASC, `reg_attendees`.`first_name` ASC", 
			array($id, 'Registered'));
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $row){
					if(array_key_exists($row['partner_id'], $records_arr)){
						$row['handicap'] = (!empty($row['handicap']) ? $row['handicap'] : 0);
						$records_arr[$row['partner_id']]['partner'] = $row;
					}
				}
			}
		}
		
		//Set field size
		$field_size = count($records_arr);
		
		//Get tournament sponsors
		$sponsors = $Registration->get_occurrence_sponsors($id);
		
		//Set header
		$header = '<table cellpadding="0" cellspacing="0" width="100%" border="0">
			<tr>
				<td width="120px"><img src="' .$logo. '" width="120px" /></td>
				<td align="center" style="font-size:12px;">
					<strong>' .$event['event_name']. '</strong><br />'.
					(trim($event['facility_name']) != '' ? $event['facility_name'] : 'TBD'). '<br />'.
					format_date_range($event['start_date'], $event['end_date']). '<br />
					Field Size: ' .$field_size. '<br />'.
					(!empty($event['purse']) && $event['purse'] > 0 ? 'Total Purse: $'.number_format($event['purse'], 2) : '').'
				</td>
				<td width="120px" align="right">';
				if(!empty($sponsors)){
					foreach($sponsors as $sponsor){
						if($sponsor['image'] != '' && file_exists('../images/logos/'.$sponsor['image'])){
							$header .= '<img src="' .$imagepath.'logos/'.$sponsor['image']. '" width="80px" style="margin-bottom:10px;" />';	
						}
					}
				}
				$header .= '</td>
			</tr>
		</table><br />';
		
		//Skins
		$subheader = '';
		if(in_array('skins', $reports)){
			$subheader .= '<hr />';
			ksort($skins[1]);
			$subheader .= '<p style="text-align:center;"><strong>' .(!empty($skins[2]) ? 'R1 ' : ''). 'Skins Winners: $' .number_format($event['skins_pot1']/count($skins[1]), 2). '/person</strong><br />(' .implode(', ', $skins[1]). ')</p>';		
			$subheader .= '<br />';
		}
		
		//Sort by gender (if applicable)
		if(empty($genders)){
			$genders['All'] = '';
		}else{
			ksort($genders);
		}
		foreach($genders as $gender=>$genderlabel){
			
			//Html
			$html = $header.($genderlabel != '' ? '<h3 style="' .$css['heading']. '">' .$genderlabel. '</h3>' : '');
			if($subheader != ''){
				$html .= $subheader;
				$subheader = '';
			}
			$html .= '<table cellpadding="5" cellspacing="1" width="100%" border="0">
				<tr>
					<th style="' .$css['th']. '">Player</th>
					<th style="' .$css['th']. '">Facility</th>'.
					(in_array('flight', $columns) ? '<th style="' .$css['th']. '" width="100px">Flight</th>' : '').
					($event['team_event'] ? '<th style="' .$css['th']. '">Partner</th>' : '').
					($event['team_event'] ? '<th style="' .$css['th']. '">Facility</th>' : '').
					(in_array('score', $columns) ? '<th style="' .$css['th']. '" width="100px">R1 Score</th>' : '').
					(in_array('time', $columns) ? '<th style="' .$css['th']. '" width="100px">' .($sort == 'score' ? 'R2 ' : ''). 'Tee Time</th>' : '').
					(in_array('hole', $columns) ? '<th style="' .$css['th']. '" width="100px">' .($sort == 'score' ? 'R2 ' : ''). 'Hole</th>' : '').'
				</tr>';	

				//CSV
				$csv_rows['headers'] = array('Player', 'Facility');
				if(in_array('flight', $columns)){
					$csv_rows['headers'][] = 'Flight';
				}
				if($gender != 'All'){
					$csv_rows['headers'][] = 'Gender';
				}
				if($event['team_event']){
					$csv_rows['headers'][] = 'Partner';
					$csv_rows['headers'][] = 'Facility';
				}
				if(in_array('score', $columns)){
					$csv_rows['headers'][] = 'R1 Score';
				}
				if(in_array('time', $columns)){
					$csv_rows['headers'][] = ($sort == 'score' ? 'R2 ' : ''). 'Tee Time';
				}
				if(in_array('hole', $columns)){
					$csv_rows['headers'][] = ($sort == 'score' ? 'R2 ' : ''). 'Hole';
				}

				//Html
				$count = 0;
				$timeblock = NULL;
				foreach($records_arr as $record){
					
					//Sort by gender
					if($gender == 'All' ||  ($gender != 'All' && $record['sort_gender'] == $gender)){
					
						//Alternate color by time
						if($sort != "alpha"){
							$recordtime = $record['time'].$record['hole'];
							if($recordtime != ""){
								if($recordtime != $timeblock){
									$timeblock = $recordtime;
									$count++;
								}
							}else{
								$count++;
							}

						//Alternate color by row
						}else{
							$count++;
						}

						$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
						$html .= '<tr>
							<td style="' .$css_td. '">' .$record['last_name']. ', '.$record['first_name']. '</td>
							<td style="' .$css_td. '">' .$record['facility_name']. '</td>'.
							(in_array('flight', $columns) ? '<td style="' .$css_td. '" align="center">' .$record['flight']. '</td>' : '').
							($event['team_event'] ? '<td style="' .$css_td. '">' .(!empty($record['partner']) ? $record['partner']['last_name'].', '.$record['partner']['first_name'] : ''). '</td>' : '').
							($event['team_event'] ? '<td style="' .$css_td. '">' .(!empty($record['partner']) ? $record['partner']['facility_name'] : ''). '</td>' : '').
							(in_array('score', $columns) ? '<td style="' .$css_td. '" align="center">' .$record['score']. '</td>' : '').
							(in_array('time', $columns) ? '<td style="' .$css_td. '" align="right">' .(!empty($record['time']) ? date('g:iA', strtotime($record['time'])) : ''). '</td>' : '').
							(in_array('hole', $columns) ? '<td style="' .$css_td. '" align="right">' .$record['hole']. '</td>' : ''). '
						</tr>';

						//CSV
						$csv_data = array(
							$record['last_name']. ', '.$record['first_name'],
							$record['facility_name']
						);
						if(in_array('flight', $columns)){
							$csv_data[] = $record['flight'];
						}
						if($gender != 'All'){
							$csv_data[] = $record['sort_gender'];
						}
						if($event['team_event']){
							$csv_data[] = (!empty($record['partner']) ? $record['partner']['last_name'].', '.$record['partner']['first_name'] : '');
							$csv_data[] = (!empty($record['partner']) ? $record['partner']['facility_name'] : '');
						}
						if(in_array('score', $columns)){
							$csv_data[] = $record['score'];
						}
						if(in_array('time', $columns)){
							$csv_data[] = (!empty($record['time']) ? date('g:iA', strtotime($record['time'])) : '');
						}
						if(in_array('hole', $columns)){
							$csv_data[] = $record['hole'];
						}
						$csv_rows['rows'][] = $csv_data;
						
					}
				}
			$html .= '</table>';
			
			$mpdf->AddPage();
			$mpdf->WriteHTML($html, 2);
		}
		
		if(empty($records_arr)){
			$html = $header.'<p>No records found.</p>';
			$mpdf->WriteHTML($html, 2);
		}
		
		//Generate csv document to download
		if($format == 'csv'){
			
			//Output csv
			header("Pragma: public");
			header("Expires: 0");
			header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
			header("Content-Type: application/force-download");
			header("Content-Type: text/csv");
			header("Content-Type: application/octet-stream");
			header("Content-Type: application/download");;
			header("Content-Disposition: attachment;filename=".$event['page']."-draw.csv");
			header("Content-Transfer-Encoding: binary ");

			$fp = fopen('php://output', 'w');
			
			if(empty($records_arr)){
				fputcsv($fp, array('No records found.'));
				
			}else{
				
				fputcsv($fp, array($event['event_name']));
				fputcsv($fp, array((trim($event['facility_name']) != '' ? $event['facility_name'] : 'TBD')));
				fputcsv($fp, array(format_date_range($event['start_date'], $event['end_date'])));
				fputcsv($fp, array('Field Size: '.$field_size));
				fputcsv($fp, array((!empty($event['purse']) && $event['purse'] > 0 ? 'Total Purse: $'.number_format($event['purse'], 2) : '')));
				fputcsv($fp, array(''));
				
				if(!empty($csv_rows)){
					fputcsv($fp, str_replace("&rsquo;", "'", $csv_rows['headers']));
					fputcsv($fp, array(''));
					foreach($csv_rows['rows'] as $row){
						fputcsv($fp, str_replace("&rsquo;", "'", $row));
					}
				}
			}
			
			fclose($fp);
			
			
		//Generate pdf and send to browser	
		}else{
			$mpdf->Output($event['event_name'].' Draw.pdf','I');
		}
		
	
	//Access denied
	}else{
		header('Location: '.$_sitepages['tournaments']['page_url'].$id.'/');
		exit();
	}
	
}	

?>