@charset "utf-8";
/*
	layout.less

	Project: PGA of Alberta 2.0
*/

/*------ non-critical deferred fonts ------*/

/* @font-face { ... } */


/*------ imports ------*/

@import (reference) "critical.less";
@import "../../../core/less/forms.less";
@import "../../../core/less/dialogs.less";
@import "../../../core/less/leadins.less";

// @path: '/';

/*------ navigation ------*/

// Mobile nav
#mobile-navigation{
	display: block;
	position: absolute;
	top: 0px;
	right: 0px;
	height: 100%;

	nav:not(.mblmenu){
		display: none;
	}

	.mblmenu{
		position: relative;
		z-index: 100;
		overflow: hidden;
		min-width: 100%;
		min-height: 100%;
		margin: 0;
		padding: 0;
	}

	ul{
		.unstyled-list();
		min-width: 300px;
	}

	a, #close-menu, .page-contact{
		padding: 20px;
	}

	a, #close-menu{
		position: relative;
		display: block;
		overflow: hidden;
		z-index: 0;
		font-family: @font-alt;
		.bold();
		// color: @color-theme2;
		color: @color-dark;
		line-height: var(--line-height-thin);
		text-align: left;
		text-transform: uppercase;
		cursor: pointer;
		.trans(color);
	}

	ul li a {
		margin-right: 20px;
		margin-left: 20px;
		border-bottom: 1px solid @color-gray-lighter;
		padding-left:0;
		padding-right:0;
	}

	// Hovered/active links
	a:hover, a:focus, li.active:not(.highlight) > a{
		color: @color-theme1;
	}

	// Highlighted Links
	li.highlight > a{
		background-color: @color-theme4;
		color: @color-light;
		.trans(background-color);
	}

	li.highlight.active > a, li.highlight > a:hover, li.highlight > a:focus{
		background-color: @color-theme1;
	}

	// Icons
	.fas{
		.position(0, 0, 0, auto, 50px, 100%);
		.flexbox(row; center; center);
	}

	// Back/close link container
	.backItemClass{
		position: relative;
		white-space: nowrap;
		display: flex;
		align-items: center;
		flex-direction: row;
		gap: 10px;
		margin: 15px 5px 20px;

		a, #close-menu{
			position: static;
			padding-left: 50px;
			border-bottom: 0;

		}

		i{
			// left: 0;
			// right: auto;
			right: 0;
			font-size: 25px;
			color: @color-theme2;
		}
	}

	// Menu container
	.levelHolderClass{
		position: absolute;
		top: 0;
		right: 0;
		padding: 0; // do not edit
		margin-left: 20px;
		min-height: 100%;
		background-color: @color-light;
		z-index: 1;

		&:after{
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 1px;
			height: 100%;
			background-color: @color-gray-lighter;
		}
	}

	// Contact info
	.menu-contact{
		border-bottom: none;
	}

	.page-contact{
		display: block !important;
		visibility: visible !important;
		text-align: left;

		li{
			margin: 0 0 10px;
			border: 0;

			&::before{
				margin-right: 10px;
				color: @color-theme2;
			}

			&.tollfree,
			&.fax,
			.label{
				display: none;
			}

			&:last-child{
				margin-bottom: 0;
			}
		}

		a{
			font-family: @font-base;
			.regular();
			color: @color-gray-dark;
			padding: 0;
			text-transform: none;

			&:hover, &:focus{
				color: @color-theme1;
			}
		}
	}

	.mobile-menu-list-container{
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 100vh;

		.mobile-menu-list{
			flex:1;

			li a{
				font-family: @font-base;
				&:hover, &:focus{
					color: @color-theme2;
				}
			}
		}
	}

	.mobile-page-contact{
		margin-top: auto;
		margin-bottom: 15px;
		display:block !important;
		.flexbox(@main: flex-end;);
		gap: 5px 15px;
		&:extend(.font-caption);
		color: @color-light;
		line-height: var(--line-height-thin);

		.label,
		.tollfree,
		.fax,
		.mailto{
			display: none;
		}

		li{
			&::before{
				.trans(color);
			}

			&:hover, &:focus{
				color: @color-gray-light;
			}
		}

		a{
			color: @color-dark;
			border-bottom: 0;
		}

		:where(#page-navbar.theme-transparent) &{
			color: @color-gray-light;

			li:hover, li:focus{
				color: @color-theme2;
			}
		}
		.link, .search{
			vertical-align: middle;
			padding: 15px 0;
			.fluid-property(font-size, 16px,18px);
			color:@color-yellow;
			.bold();
			&:hover, &:focus{
				color: @color-theme2;
			}
		}

		.mobile-button{
			color:#000;
			margin-left:20px;
			margin-right: 180px;
			padding-left:15px;

		}
	}
}


/*------ body content ------*/

// Panels
.panel{
	display: block;

	&.parallax{
		position: relative;
		.fluid-property(padding-top; 40px; 180px);
		.fluid-property(padding-bottom; 80px; 210px);
		background: var(--theme-bg);
		text-align: center;
		z-index: 0;
		overflow: hidden;

		// &:not(.contact-form-panel){
			max-height: 800px;
		// }
		min-height: 550px;

		&::before{
			content: '';
			display: block;
			.fluid-property(width, 20px, 50px);
			// .fluid-property(height, 75px, 125px);
			.fluid-property(height, 50px, 50px);
			margin-right: auto;
			margin-left: auto;
			.fluid-property(margin-bottom, 60px, 145px);
			// background-color: @color-theme3;
			opacity: 0;
		}

		.panel-image{
			.position();
			z-index: -1;
		}

		.panel-title h2{
			color: @color-light;

			// .fancy-text{
			// 	color: inherit;
			// }
		}

		.panel-text{
			&:extend(.font-h5);
			font-family: @font-base;
			// color: @color-gray-lighter;
			color:@color-light;
			// .medium();
			.regular();
			line-height: var(--line-height-normal);

			h2, h3, h4, h5, h6{
				color: inherit;
			}
		}

		.responsive-bg{
			background-attachment: fixed;
			// background: url('../../../images/svg/parallax-bg.svg') no-repeat center center;
		}

		&.theme-theme1.noimage{
			a.button:hover, a.button:focus{
				&:extend(.button.hover-theme4);
			}
		}

		&.theme-theme2.noimage{
			a:not(.button):hover, a:not(.button):focus{
				color: @color-theme3;
			}

			a.button.simple:hover, a.button.simple:focus{
				&:extend(.button.hover-theme4);
			}
		}

		&.theme-black{
			&::before{
				background-color: @color-light;
				opacity: 0.15;
			}
		}

		& + &{
			margin-top: 1px;
		}

		&:last-child{
			margin-bottom: 0;
		}

		.curve-container {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%; /* Cover the entire container height */
			z-index: 1; /* Lower z-index to be below panel content */
			pointer-events: none;
			overflow: hidden; /* Prevent any overflow */
		}

		.curve-shape {
			width: 100%;
			height: 100%; /* Fill the entire container */
			display: block;
			object-fit: cover; /* Cover the entire area */
			object-position: center center; /* Center the image */
		}

		.panel-wrapper {
			position: relative;
			z-index: 2; /* Ensure content appears above the curve overlay */
			text-align: left;
			.container{
				margin:0px;
				.fluid-property(margin-left, 0px, 30px);
			}
		}
	}

	&.side{
		.fluid-property(--padding-block, 30px, 60px);
		.fluid-property(--padding-inline, 20px, 60px);
		margin-top: var(--panel-margin-sm);

		.container{
			&:extend(.container.container-xl);
			padding: 0;
		}

		img{
			display: block;
			width: 100%;
		}

		.light-iframe{
			.fluid-property(--icon-size, 50px, 80px);
			position: relative;

			.overlay{
				background-color: @color-overlay-dark;
				z-index: 0;
				opacity: 0;
			}

			&::before, &::after{
				position: absolute;
				// .fluid-property(left, 20px, 30px);
				// .fluid-property(bottom, 20px, 30px);
				.fluid-property(left, 45%, 30px);
				.fluid-property(bottom, 45%, 30px);
				line-height: var(--icon-size);
				height: var(--icon-size);
				text-align: center;
				.trans(color);
			}

			&::before{
				.font-awesome(@unicode: f167; @family: 'Brands');
				font-size: var(--icon-size);
				color: @color-light;
				.text-shadow();
				z-index: 1;
			}

			&::after{
				.font-awesome(f04b);
				.fluid-size(22, 36);
				color: @color-theme4;
				aspect-ratio: (55.95 / 50); // Aspect ratio of the YouTube icon
				z-index: 2;
			}

			// &:hover{
			// 	.overlay{
			// 		opacity: 0.5;
			// 	}

			// 	&::before{
			// 		color: @color-theme4;
			// 	}

			// 	&::after{
			// 		color: @color-light;
			// 	}
			// }
		}

		&.btt{
			.panel-media{
				order: 1;
			}
		}

		.panel-wrapper{
			margin: 0;
			.flexbox();
			.box-shadow(@blur: 25px);
		}

		.panel-content{
			padding: var(--padding-block) var(--padding-inline);
			// background-color: @color-dark-navy-blue;
		}

		.panel-header{
			&:extend(.title-decor all);
			--line-top: calc(var(--padding-block) * -1);
			--line-left: calc(var(--padding-inline) * -1);
			.flexbox();
			// display: none;
		}

		.panel-title{
			width: 100%;
			// order: 1;

			h2{
				&:extend(.font-h2);
				margin: 0;
			}
		}

		.panel-subtitle{
			width: 100%;
			// order: 0;

			h3{
				&:extend(.font-h6);
				.fluid-property(margin-bottom, 0px, 5px);
				color: @color-theme2;
				.letter-spacing(@letter-spacing);
			}
		}

		// .panel-text{
		// 	.fluid-property(margin-top, 10px, 20px);
		// }

		.panel-media{
			width: 100%;
		}

		.panel-map{
			display: none;
		}

		&.has-map{
			.panel-image,
			.panel-video{
				display: none;
			}

			.panel-map{
				display: block;
			}
		}
	}

	&.promo, &.mini-promo, &.gallery{
		.panel-content{
			margin-bottom: 40px;
		}
	}

	&.promo{
		.panel-promos{
			&:not(:only-child){
				.fluid-property(margin-top, 20px, 30px);
			}

			.container{
				&:extend(.container.container-xl);
			}
		}
	}

	&.mini-promo{
		position: relative;

		.panel-header{
			.container{
				.panel-title{
					h2{
						text-align: center;
					}
				}
			}
		}

		.panel-promos{
			&:not(:only-child){
				.fluid-property(margin-top, 20px, 30px);
			}

			.container{
				&:extend(.container.container-lg);
			}
		}

		.curve-background {
			background: url('./../../images/svg/curve.svg') no-repeat;
			background-size: contain;
			// top:0px;
			// left:0px;
			// position: absolute;
		// 	position: absolute;
		// 	top: 0;
		// 	left: 0;
		// 	width: 100%;
		// 	height: 100px;
		// 	overflow: hidden;

		// 	svg {
		// 	  position: absolute;
		// 	  width: 100%;
		// 	  height: 100%;
		// 	  top: 0;
		// 	  left: 0;
		// 	  color:#7CC04B;
		// 	  background-color: rgb(218, 146, 23);
		// 	}
		  }

		  // Ensure content stays above the curve
		//   .container {
		// 	position: relative;
		// 	z-index: 2;
		//   }
	}

	&.cta{
		.container{
			&:extend(.container.container-lg);
		}

		.panel-title{
			h2{
				margin: 0;
				color: @color-dark;
				text-transform: uppercase;
				.fluid-property(font-size, 40px, 100px);
			}

			.fancy-text{
				color: inherit;
			}
		}

		.panel-subtitle{
			margin-top: 10px;

			.fluid-property(padding-right, 5px, 200px);
			.fluid-property(padding-left, 5px, 200px);

			h3{
				&:extend(.font-h5);
				margin: 0;
				font-family: @font-base;
				// font-family: 'Poppins', sans-serif;
				.semibold();
				color: @color-dark;
				line-height: var(--line-height-normal);
				.letter-spacing(@letter-spacing);
			}
		}

		.panel-buttons{
			.flexbox(@cross: flex-start;);
			gap: 40px;

			.button{
				&:extend(.button.light);
			}

			.button ~ .button{
				&:extend(.button.outline);
			}
		}

		&:not(:last-child){
			.fluid-property(--padding-block, 40px, 60px);
			.fluid-property(--padding-inline, 20px, 60px);

			.container{
				.fluid-property(border-radius, 0px, 3px, 1024px, 1366px);
				background: @color-light;
				padding: var(--padding-block) var(--padding-inline);
				overflow: hidden;
			}

			.panel-title{
				--line-top: calc(var(--padding-block) * -1);
				--line-left: calc(var(--padding-inline) * -1);

				&:extend(.title-decor all);
			}

			.panel-buttons{
				margin-top: 20px;
			}
		}

		&:last-child{
			margin-bottom: 0;
			.fluid-property(padding-bottom, 40px, 80px);
			// background: @color-theme2;
			text-align: center;
			background-image: url("../../images/cta.png");
			background-size:cover;
			// background-position:50% 10%;
			background-repeat: no-repeat;

			&::before{
				content: '';
				display: block;
				.fluid-property(width, 30px, 50px);
				.fluid-property(height, 75px, 125px);
				margin-right: auto;
				margin-left: auto;
				.fluid-property(margin-bottom, 40px, 60px);
				background-color: @color-theme3;
				opacity: 0;
			}

			.panel-title::before{
				content: none;
			}

			.panel-buttons{
				.fluid-property(margin-top, 40px, 60px);
				justify-content: center;
			}

			&.theme-black{
				&::before{
					background-color: @color-light;
					opacity: 0.15;
				}
			}
		}
	}

	&.gallery{
		.fluid-property(--image-width, 150px, 310px);

		&:not(.gallery-listings) .panel-gallery{
			.container{
				max-width: none;
				padding: 0;
			}

			&:not(:only-child){
				.fluid-property(margin-top, 20px, 30px);
			}
		}

		.light-gallery{
			.flexbox(row);
			gap: 1px;
			overflow: auto hidden;

			> .gal-item{
				width: var(--image-width);
				.flex(0 0 auto);
			}

			&.swiper{
				display: block;
				overflow: hidden;

				.swiper-slide{
					width: var(--image-width);
				}
			}
		}

		.swiper-scrollbar{
			position: relative;
			.fluid-property(margin-top; 20px; 60px);
			margin-inline: auto;
			left: auto;
			right: auto;
			bottom: auto;
			width: calc(100% - var(--container-padding) * 2);
			height: 10px;
			max-width: 470px;
			background: none;

			// Rail
			&::before{
				.position();
				height: 1px;
				margin: auto;
				background-color: @color-theme3;
				content: '';
			}

			.swiper-scrollbar-drag{
				border-radius: 3px;
				cursor: grab;
				background-color: @color-theme2;
				.trans(background-color);

				&:hover, &:focus{
					background-color: @color-theme1;
				}
			}
		}
	}
	 &.partners, &.staff{
		.fluid-property(--image-width, 150px, 150px);

		&:not(.gallery-listings) .panel-gallery{

			&:not(:only-child){
				.fluid-property(margin-top, 20px, 30px);
			}
		}

		.light-gallery{
			.flexbox(row);
			gap: 1px;
			overflow: auto hidden;

			> .gal-item{
				width: var(--image-width);
				.flex(0 0 auto);

			}

			&.swiper{
				display: block;
				overflow: hidden;

				.swiper-slide{
					width: var(--image-width);
				}
			}
		}
		.swiper-wrapper{
			align-items: center;
		}
		.swiper-scrollbar{
			display: none;
			position: relative;
			.fluid-property(margin-top; 20px; 60px);
			margin-inline: auto;
			left: auto;
			right: auto;
			bottom: auto;
			width: calc(100% - var(--container-padding) * 2);
			height: 10px;
			max-width: 470px;
			background: none;

			// Rail
			&::before{
				.position();
				height: 1px;
				margin: auto;
				background-color: @color-theme3;
				content: '';
			}

			.swiper-scrollbar-drag{
				border-radius: 3px;
				cursor: grab;
				background-color: @color-theme2;
				.trans(background-color);

				&:hover, &:focus{
					background-color: @color-theme1;
				}
			}
		}
	}

	&.partners, &.staff ,&.event{
		margin: 10px 0;
		padding: 50px 0;

		.container {
			z-index: 20;
		}

		.slick-track {
			.flexbox();
			margin: 0 auto;
		}

		.panel-carousel {
			&.card-items.simple {
				margin-top: 40px;
				margin-bottom: 0;
			}
		}

		.slick-navigation {
			margin-top: -78px;
			text-align: right;
			visibility: visible;
			position: relative;
		}
	}

	.event-boxes{text-align:center; margin-right:-10px;
		.event-box{
			display:-moz-inline-box;
			-moz-box-orient:vertical;
			display:inline-block;
			vertical-align:top;
			position:relative;
			width:332px;
			max-width:100%;
			padding:0;
			margin:0 10px 10px 0;
			border:1px solid @color-gray-lightest;
			box-sizing:border-box;

			.date{padding:10px 20px; background:url('@{path}images/ui/pattern-bg.jpg') left center no-repeat @color-theme1; background-size:100% auto; color:#fff;
				.day{display:block; font-size:30px; line-height:30px;
				// font-family:@font-serif;
				font-style:normal; font-weight:700;}
				.month{display:block; color:#fff; line-height:20px;}
			}
			.content{padding:20px;
				a{color:@color-dark;
					&:hover{color:@color-theme1;}
				}
				h6{line-height:26px; margin-bottom:2px;}
				small{display:block;}
			}
		}
		.event-box > .event-boxes{display:table; table-layout:fixed;}
	}
}

@media @notebook{
	.panel{
		&.cta{
			.panel-title{
				margin-bottom: 0;
			}

			&:not(:last-child){
				.panel-content{
					.flexbox(@flow: row nowrap; @main: space-between;);
					gap: 40px;
				}

				.panel-header{
					width: 65%;
				}

				.panel-buttons{
					flex-flow: column nowrap;
					align-items: stretch;
					max-width: 35%;
					margin-top: 0;
				}
			}
		}

		&.side{
			.container{
				padding: 0 var(--container-padding);
			}

			&.btt{
				.panel-media:not(.panel-right){
					order: 0;
				}
			}

			.panel-wrapper{
				.flexbox(row nowrap; center; center);
				box-shadow: none;
			}

			.panel-content{
				position: relative;
				width: 50%;
				// .box-shadow(@blur: 25px);
				z-index: 1;

				&.panel-right, &.panel-left{
					width: calc(50% + 40px);
				}

				&.panel-right{
					// margin-left: -40px;
					order: 1;
				}

				&.panel-left{
					// margin-right: -40px;
					order: 0;
				}
			}

			.panel-media{
				position: relative;
				width: 50%;
				z-index: 0;

				&.panel-right{
					order: 1;

					.light-iframe::before, .light-iframe::after{
						// .fluid-property(right, 20px, 30px);
						.fluid-property(right, 20px, 30px);
						left: auto;
					}
				}

				&.panel-left{
					order: 0;
				}
			}
		}
	}
}

// Tabs
.panel-tabs{
	display: block;

	.panel-content + &{
		// .fluid-property(margin-top, 30px, 80px);
		.fluid-property(margin-top, 10px, 20px);
	}

	.content-tabs .tabs-nav{
		border:none;
		background:@color-light;
	}
}

.content-tabs{
	.tabs-nav-wrapper{
		position: relative; // prevent scrollbars
		overflow: hidden;
	}

	.tabs-nav{
		.unstyled-list();

		li{
			display: block;

			&:first-child a{
				border-radius: 3px 3px 0 0;
			}

			&:last-child a{
				border-radius: 0 0 3px 3px;
			}

			& + li{
				a{
					border-top-color: fade(@color-theme3, 25%);
				}

				.ui-state-active a, a:hover, a:focus{
					border-top-color: fade(@color-theme3, 50%);
				}
			}
		}

		a{
			display: block;
			border: 1px solid rgba(269, 62,52, 0.25);
			background-color: rgba(269, 62,52, 0.1);
			padding: 18px 30px;
			font-family: @font-alt;
			.bold();
			line-height: var(--line-height-thin);
			vertical-align: middle;
			color:@font-color;
			outline: none;
			text-decoration: none;
			.fluid-property(font-size, 16px, 20px);
		}

		.ui-state-active a, a:hover, a:focus{
			background-color: @color-theme2;
			border-color: @color-theme2;
			color: @color-light;
		}

		// background-color: @color-theme1 !important;
		border:none !important;

		li{
			border:none;
		}
	}

	.tabs-panel{
		display: block;
		max-width: 775px;
		.fluid-property(padding-top, 30px, 50px);
		clear: both;

		& ~ .tabs-panel{
			display: none; // Prevent CLS, will be overwritten by jQueryUI
		}
	}

	.tabs-select{
		&:extend(.content-tabs .tabs-nav a);
		display: none;
		max-width: 100%;
	}
}

// Responsive tabs
// @media @max-tablet-l{
// 	.content-tabs{
// 		.tabs-nav{
// 			.flexbox(row nowrap);
// 			white-space: nowrap;
// 		}

// 		&.responsive-tabs{
// 			.tabs-nav{
// 				position: absolute;
// 				visibility: hidden;
// 				pointer-events: none;
// 				max-width: 100%; // must not overflow when responsive
// 			}

// 			.tabs-select{
// 				display: block;
// 			}
// 		}
// 	}
// }

@media @tablet-l{
	.content-tabs{
		.tabs-nav{
			.flexbox(@cross: center;);
			gap: 1px;

			li{
				display: block;

				&:first-child a{
					border-radius: 3px 0 0 3px;
				}

				&:last-child a{
					border-radius: 0 3px 3px 0;
				}

				& + li{
					a{
						border-top-color: @color-gray-lightest;
					}

					.ui-state-active a, a:hover, a:focus{
						border-top-color: @color-theme2;
					}
				}
			}

			// &::after{
			// 	content: '';
			// 	display: block;
			// 	margin-right: -20px;
			// 	margin-left: -1px;
			// 	padding-left: 20px;
			// 	height: 1px;
			// 	background-color: @color-gray-lighter;
			// 	background-clip: content-box;
			// 	.flex(1 1 auto);
			// }
		}
	}
}

// Promo boxes
.promo-boxes{
	.flexbox(row wrap; center);
	--count: 1;
	--gap: 10px;
	--gap-sum: (var(--gap) * (var(--count) - 1));
	--item-width: calc((100% - var(--gap-sum)) / var(--count));
	gap: var(--gap);
	// box-sizing: border-box;

	.promo-box{
		width: var(--item-width);
		box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
	}

	@media @tablet-l{--count: 2; }
	@media @notebook{--count: 4; }
}

.promo-box{
	max-width: 500px;

	.promo-image-wrapper{
		position: relative;
		overflow: hidden;

		img{
			display: block;
			width: 100%;
			.scale(1);
			.trans(transform);
			min-height: 320px;
			// max-width: 383px;
		}
	}

	.promo-hover{
		.position();
		.flexbox(@cross: center;);
		align-content: center;
		color: @color-light;
		z-index: 0;
		.trans(opacity);

		.promo-title, .promo-text, .promo-link, .overlay{
			display: none;
		}
	}

	.promo-content{
		padding: 20px 0;
		text-align: center;

		.promo-title{
			&:extend(.font-h4);
			margin: 0;
			color: @color-theme2;
			.trans(color);

			a{
				color: inherit;
				transition: none;
			}
		}

		.promo-text{
			.fluid-property(margin-top, 5px, 10px);
			.fluid-property(margin-bottom, 15px, 25px);
		}

		.promo-link{
			&:extend(.button);
			&:extend(.button.simple);
			&:extend(.font-caption);
			margin-top: 20px;
		}
	}

	&:hover, &:focus{
		.promo-image-wrapper img{
			.scale(1.1);
		}

		.promo-content{
			.promo-title {
				// color: @color-theme1;
				.scale(0.8);
				.trans(transform);
			}
		}

		.promo-link{
			--border: @color-theme2;
			--bg: @color-theme2;
			--text: @color-light;
		}
	}
}


// Mini promos
.mini-promo-boxes{
	&:extend(.promo-boxes);
	.fluid-property(--gap, 10px, 15px);
	--count: 2;

	.mini-promo-box{
		&:extend(.promo-boxes .promo-box);
		text-align: center;
		box-shadow: none;
	}

	@media @tablet-l{--count: 3; }
	@media @notebook{--count: 4; }
}
.mini-promo-box{
	.fluid-property(--padding, 15px, 40px);
	.fluid-property(min-height, 115px, 190px);
	.flexbox(column; space-between);

	.promo-content, .promo-link{
		height: 100%;
	}

	.promo-content{
		&:extend(.title-decor all);
		--line-top: 0;
		--line-bottom: auto;
		--line-left: 0;

		// &::before{
		// 	.fluid-property(height, 50px, 80px);
		// }

		display: block;
		padding: var(--padding);
		// background-color: @color-dark-navy-blue-light;
		background-color: transparent;
		width: 100%;
		height: 100%;
		.trans(background-color);

		&:hover, &:focus{
			// background-color: @color-theme1;
			.promo-icon{
				color: @color-light;
			}

			.promo-icon{
				transform: rotateZ(10deg);
				transition: transform 0.3s ease-in-out 0s;
				z-index: -10;
			}
		}

		:not(:hover), :not(:focus){
			.promo-icon{
				transition: transform 0.3s ease-in-out 0s;
			}
		}

		.blob-container{
			position: relative;
			.promo-fa-icon{
				position: absolute;
				left:45%;
				top:45%;
				transform: translateY(-50%);
				font-size: 30px;
				color:@color-light;
				z-index:1;
			}
		}
	}

	.promo-icon{
		.fluid-property(margin-bottom, 10px, 20px);
		.fluid-property(margin-left, 5px, 0px);
		font-size: 30px;
		line-height: 1;
		color: @color-theme3;
		.trans(color);
	}

	.promo-title{
		margin: 0;
		.fluid-size(18, 25);
		font-style: italic;
		color: @color-light;
		line-height: var(--line-height-thin);
		.letter-spacing(@letter-spacing);
	}
}


/*------ forms ------*/

:root{
	--field-height: 60px;
	--field-padding-block: 20px;
	.fluid-property(--field-padding-inline, 24px, 30px);
	--field-border: fade(@color-gray-light, 50%);
	--field-border-hover: @color-gray-light;
	--field-border-radius: 3px;
}

.input, .select, .textarea{
	font-size: 16px;
}

.textarea{
	height: 150px;
}

.select{
	.select-arrow(@color-theme2);
}

label{
	.bold();
}

.form-buttons{
	margin-top: 20px;
}


/*------ dialogs ------*/

.ui-dialog{
	--title-bg: @color-theme1;

	.ui-dialog-titlebar{
		font-family: @font-alt;
		.bold();
	}
}


/*------ leadins ------*/

.leadin-popup{
	&.theme-theme1{
		.button{
			&:extend(.button.hover-light);
		}
	}

	&.type-bar{
		.leadin-title{
			padding: 0;
		}

		.content-wrapper{
			gap: 10px 20px;
		}
	}
}


/*------ contact module ------*/

#contact-locations{
	.panel-text{
		max-width: none;
	}

	&:not(.content-tabs):not(:first-child){
		margin-top: 30px;
	}
}

.location-panel{
	--count: 1;
	--gap: 20px;
	--gap-sum: (var(--gap) * (var(--count) - 1));
	--item-width: calc((100% - var(--gap-sum)) / var(--count));

	h3{
		&:extend(.font-h4);
		color: @color-theme2;
	}

	ul{
		.flexbox();
		gap: var(--gap);
		.unstyled-list();
		line-height: var(--line-height-normal);
	}

	ul + ul{
		margin-top: var(--gap);
	}

	.label{
		display: block;
	}

	.value{
		&:extend(.font-h6);
	}

	.contact-information{
		li{
			--icon-width: 15px;
			position: relative;
			width: var(--item-width);
			padding-left: calc(var(--icon-width) + 15px);

			&::before{
				position: absolute;
				top: 0;
				left: 0;
				width: var(--icon-width);
				margin: 0;
				font-size: 14px;
				color: @color-theme2;
				text-align: center;
			}

			&.address .line1{
				display: block;
			}
		}
	}

	.contact-hours{
		li{
			width: 100%;
		}

		.open-text{
			&:extend(.font-h6);
			.bold();

			&.open{
				color: @color-theme2;
			}

			&.closing{
				color: @color-theme3;
			}

			&.closed{
				color: @color-error;
			}
		}

		&:not(:first-child){
			margin-top: 50px;
		}
	}
}

@media @tablet-p{
	.location-panel{
		--count: 2;
	}
}

@media @notebook{
	.location-panel{
		--count: 3;
	}
}

.hours-disclaimer{
	display: block;
	margin-top: 20px;
	font-family: 'Lora';
	font-style: italic;
	.bold();
	font-size: 18px;

}

.panel-map{
	.fluid-property(margin-top, 50px, 70px);

	.container{
		&:extend(.container.container-lg);
	}

	.contact-map{
		width: 100%;
		.fluid-property(height, 300px, 500px);
		border: 5px solid @color-light;
		.box-shadow(@blur: 25px;);
	}
}

// .panel.parallax.contact-form-panel{
// 	.fluid-property(padding-top; 140px; 155px);
// 	text-align: left;

// 	&::before{
// 		content: none;
// 	}

// 	.panel-title{
// 		&:extend(.title-decor all);
// 	}

// 	.panel-form{
// 		&:extend(.container);

// 		--field-border: transparent;
// 		--field-border-hover: var(--field-border);
// 		--field-border-width: 2px;
// 		--field-bg: fade(@color-dark, 50%);
// 		--field-bg-hover: @color-light;
// 		--field-placeholder: @color-gray-lighter;
// 		--field-color: @color-light;
// 		--field-color-hover: @font-color;

// 		label{
// 			&:extend(.sr-only);
// 		}

// 		.input, .textarea, .select{
// 			border-width: 0 0 0 var(--field-border-width);

// 			&:hover, &:focus{
// 				--field-placeholder: @color-gray;
// 			}
// 		}

// 		.textarea{
// 			height: 225px;
// 		}

// 		.form-buttons{
// 			margin-top: 10px;
// 		}

// 		.button{
// 			&:extend(.button.light);
// 		}

// 		&:not(:first-child){
// 			.fluid-property(margin-top, 20px, 70px);
// 		}

// 		.form-field.full-width{
// 			width: 100%;
// 		}
// 	}

// 	&.theme-theme1.noimage{
// 		#contact-form .button:hover, #contact-form .button:focus{
// 			&:extend(.button.hover-theme4);
// 		}
// 	}

// 	&.theme-black{
// 		.panel-title::before{
// 			background-color: @color-light;
// 			opacity: 0.15;
// 		}
// 	}

// 	.panel.standard.has-map + &{
// 		.fluid-property(--map-overflow, 70px, 55px);
// 		margin-top: calc(var(--map-overflow, 0px) * -1);
// 		z-index: 1;
// 	}
// }

// @media @tablet-l{
// 	.panel.parallax.contact-form-panel{
// 		.panel-form{
// 			.textarea{
// 				height: 270px;
// 			}
// 		}
// 	}
// }



/*------ gallery module ------*/

.gallery.gallery-listings{
	.panel-gallery{
		.container{
			&:extend(.container.container-xl);
		}
	}

	.light-gallery{
		align-items: flex-start;
	}

	.column{
		.flexbox(column);
		.flex(1 0 1px);
		gap: inherit;
		height: auto;
	}
}

.gal-item{
	position: relative;

	a, img{
		width: 100%;
		display: block;
	}

	.gal-link{
		.overlay{
			background-color: @color-overlay-dark;
			z-index: 0;
			opacity: 0;
		}

		&::after{
			.position(0, 0, 0, 0, 1em);
			.font-awesome(f065);
			.fluid-size(22, 40);
			text-align: center;
			line-height: 1;
			color: @color-light;
			opacity: 0;
			.text-shadow();
			.trans(opacity);
		}

		&:hover{
			.overlay{
				opacity: 0.5;
			}

			&::after{
				opacity: 1;
			}
		}
	}
}


/*------ footer ------*/

// #page-footer{
// 	display: block;
// 	text-align: center;

// 	.footer-primary{
// 		.fluid-property(padding-top, 35px, 70px, 480px, 1024px);
// 		.fluid-property(padding-bottom, 30px, 40px, 480px, 1024px);
// 		background-color: @color-theme2;
// 		color: @color-light;

// 		a, small{
// 			color: inherit;
// 		}

// 		a:hover, a:focus{
// 			color: @color-theme3;
// 		}
// 	}

// 	.footer-secondary{
// 		padding: 20px 0;
// 		background-color: @color-light;
// 		line-height: var(--line-height-normal);

// 		p{
// 			padding: 0 0 15px;

// 			&:last-child{
// 				padding-bottom: 0;
// 			}
// 		}

// 		a{
// 			color: inherit;

// 			&:hover, &:focus{
// 				color: @color-gray-dark;
// 			}
// 		}
// 	}

// 	.container{
// 		&:extend(.container.container-lg);
// 	}

// 	.page-contact{
// 		li{
// 			display: block;
// 			margin: 0 0 10px;

// 			&::before{
// 				content: none;
// 			}

// 			&:last-child{
// 				margin-bottom: 0;
// 			}
// 		}

// 		.label,
// 		.tollfree,
// 		.fax{
// 			display: none;
// 		}
// 	}
// }

// @media @tablet-l{
// 	#page-footer{
// 		.footer-secondary{
// 			p:first-child{
// 				padding: 0;
// 			}
// 		}

// 		.page-contact{
// 			li{
// 				display: inline-block;
// 				vertical-align: top;

// 				&::after{
// 					content: '|';
// 					margin: 0 10px;
// 				}

// 				&:last-child::after{
// 					content: none;
// 				}
// 			}
// 		}
// 	}
// }

// // Footer nav
// #footer-navigation{
// 	font-size: 16px;

// 	ul{
// 		gap: 20px 0;
// 		flex-wrap: wrap;
// 	}

// 	li{
// 		width: 100%;
// 	}

// 	a{
// 		display: inline-block;
// 		font-family: @font-base;
// 		.regular();
// 		text-transform: capitalize;
// 		vertical-align: top;
// 	}

// 	&::after{
// 		content: '';
// 		display: block;
// 		.fluid-property(margin-top, 30px, 50px, 480px, 1024px);
// 		.fluid-property(margin-bottom, 30px, 40px, 480px, 1024px);
// 		margin-right: auto;
// 		margin-left: auto;
// 		width: 56.25%;
// 		height: 1px;
// 		background-color: fade(@color-light, 15%);
// 	}
// }

// @media @tablet-l{
// 	#footer-navigation{
// 		ul{
// 			gap: 10px 50px;
// 			justify-content: center;
// 		}

// 		li{
// 			width: auto;
// 		}

// 		&::after{
// 			width: 100%;
// 		}
// 	}
// }

// // Social icons
// .social-icons{
// 	.fluid-property(--icon-size, 24px, 30px);
// 	.unstyled-list(@margin: 0 0 20px;);
// 	.inline-flexbox(@main: center;);
// 	gap: 10px;
// 	text-align: center;
// 	font-size: 0;

// 	li, a{
// 		display: block;
// 	}

// 	a{
// 		position: relative;
// 		width: var(--icon-size);
// 		overflow: hidden;

// 		span:nth-child(1){
// 			position: relative;
// 			font-size: var(--icon-size);
// 			line-height: 1;
// 			color: @color-light;
// 			.scale(1);
// 			z-index: 10;
// 			.trans(transform);

// 			&.fa-facebook::before{content: '\f082'; }
// 			&.fa-pinterest::before{content: '\f0d3'; }
// 			&.fa-twitter::before{content: '\f081'; }
// 			&.fa-youtube::before{content: '\f431'; }
// 		}

// 		span:nth-child(2){
// 			.position();
// 			opacity: 0;
// 			border-radius: 3px;
// 			z-index: 1;
// 			.trans(opacity);

// 			&.fa-facebook{background-color: #3B5998; }
// 			&.fa-linkedin{background-color: #1B92BD; }
// 			&.fa-pinterest{background-color: #CB2027; }
// 			&.fa-twitter{background-color: #00BDEC; }
// 			&.fa-youtube{background-color: #D20800; }
// 			&.fa-instagram{.gradient(radial; #fdf497, #d6249f 70%, #285AEB; circle at 30% 107%); }
// 			&.fa-tiktok{background-color: #000; }
// 			&.fa-houzz{background-color: #7CC04B; }
// 		}

// 		&:hover, &:focus{
// 			span:nth-child(1){
// 				.scale(0.6);
// 			}

// 			span:nth-child(2){
// 				opacity: 1;
// 			}
// 		}
// 	}
// }


/*------ new footer ------*/

// // main {
// 	// Check if the last section has the 'cta' class
// 	// section:last-of-type.cta {
// 	//   ~ #page-footer {
// 	// 	background: url('../../images/footer-cta.png');
// 	// 	background-size: cover;
// 	// 	background-repeat: no-repeat;
// 	//   }
// 	// }

// 	section:last-of-type:has(.cta) {
// 		~ #page-footer {
// 		  background: url('../../images/footer-cta.png');
// 		  background-size: cover;
// 		  background-repeat: no-repeat;
// 		}
// 	  }
// 	// Check if the last section does NOT have the 'cta' class
// 	section:last-of-type:not(.cta) {
// 	  ~ #page-footer {
// 		background: url('../../images/svg/footer.svg') no-repeat center bottom;
// 	  }
// 	}
// }

#page-footer {
    // display: block;
    position: relative;
    /* Use the footer SVG as background for the entire footer */
    // background: url('../../images/svg/footer.svg') no-repeat center bottom;
	background: url('@{path}images/svg/footer.svg') no-repeat center center;
    background-size: cover;
    min-height: 100vh; /* Full viewport height for mobile */
    color: @color-light;
	display: flex;
	gap:0px;
	flex-direction: column;
	// justify-content: space-between;

    // background: url('../../images/footer-cta.png');
	// background-size:cover;
	// background-repeat: no-repeat;
	// background-position:100% 100%;
	// min-height: 460px;

    /* Container for all footer content */
    .container {
        &:extend(.container.container-lg);
        padding-top: 40px;
        padding-bottom: 0px;
        position: relative;
        z-index: 2;
		padding-left: 20px;
		padding-right: 20px;

		.footer-primary{
			display: flex;
			flex-direction: column;
			justify-content:space-between;
			width: 100%;
			// border:1px solid blue;
		}
    }

    /* Logo styling */
    .footer-logo {
        // display: block;
		display:flex;
		flex-direction: column;
        margin: 0 auto 30px;
        max-width: 100%;
        text-align: center;
		gap:20px;
		justify-content: center;
		align-items: center;

		#page-logo {
			svg,img{
				display:static;
				.fluid-property(width, 150px, 160px);
				padding: 0 auto;
				width: 200px;
			}
			@media @max-tablet-l{
				width: auto;
			}
		}
    }

    /* Social icons row */
    .social-links {
        display: flex;
        justify-content: center;
		margin-left:50px;
        margin-bottom: 30px;

        a {
            margin: 0 5px;
            color: @color-light;

            &:hover, &:focus {
                color: @color-theme3;
            }
        }
    }

    /* Contact information */
    .page-contact {
        text-align: center;
        margin-bottom: 40px;

        li {
            display: block;
            margin: 0 0 4px;

            &::before {
                content: none;
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        // .label,
        // .tollfree,
        // .fax {
        //     display: none;
        // }

        a {
            color: @color-light;

            &:hover, &:focus {
                color: @color-theme1;
            }
        }
    }

    /* Footer navigation - vertical layout for mobile */
    #footer-navigation {
        margin-bottom: 40px;

        ul {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            padding: 0;
            list-style: none;
            margin: 0;
        }

        li {
            width: auto;
        }

        a {
            display: inline-block;
            color: @color-light;
            font-size: 18px;
            text-align: center;
			font-family: @font-base;

            &:hover, &:focus {
                color: @color-theme1;
            }
        }

		a[href*="make-donation"] {
			color: @color-yellow;

			&:hover, &:focus{
				color: @color-theme1;
			}
		}
    }

    /* Copyright section */
    .copyright-section {
        text-align: center;
        font-size: 14px;

        p {
            margin-bottom: 10px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        a {
            color: @color-light;

            &:hover, &:focus {
                color: @color-theme3;
            }
        }
    }

    /* Make A Donation link special styling */
    .donation-link {
        color: #ffcc00; /* Gold color for donation link */
        font-weight: bold;

        &:hover, &:focus {
            color: lighten(#ffcc00, 10%);
        }
    }

	.footer-secondary{
		margin-top: auto;
		border-top: 1px solid fade(@color-light, 15%);
		.container{
			padding-top: 0px;
			padding-bottom: 0px;


		}
		#page-copyright, #page-disclaimer, #page-signature{
			padding: 0px;
			small, a{
				color: @color-light;
			}
			a:hover, a:focus{
				color: @color-theme1;
			}
			@media @max-tablet-l{
				text-align: center;
				padding:3px;
			}
		}

		.footer-copyright{
			display: flex;
			flex-direction: column;
			padding: 0px;
			// font-family: `'Museo Sans', ${@font-alt}`;
			font-family:'Museo Sans';
			font-size: 13px;
			font-style: italic;

			small{
				color: @color-light;
			}

			@media @max-tablet-l{
				margin-top:40px;
			}

		}
	}
}

/* Desktop layout (above 768px) */
@media @tablet-l {
    #page-footer {
        min-height: auto; /* Auto height for desktop */
		padding-bottom: 1px;

        .container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: flex-start;
            padding-top: 20px;

			.footer-primary{
				flex-direction: row;
				justify-content:space-between;
				width: 100%;
				align-items: flex-start;
				// border:1px solid red;
			}
        }

        /* Left side content */
        .footer-left {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            flex: 1 1 auto;
            text-align: left;

            .social-links {
                margin-bottom: 0;
                justify-content: flex-start;
            }
        }


		.footer-logo {
			margin: 0 0 0 0;
			// max-width: 120px;
			display:flex;
			flex-direction: row;
			// border:1px solid orange;
			justify-content: center;
			gap:70px;
			.fluid-property(gap, 60px, 100px);

			#page-logo{
				svg,img{
					display:static;
					.fluid-property(width, 150px, 160px);
					// padding: 0 auto;
					// max-width:100%;
					// width: 100%;
				}
			}
		}

        /* Right side content */
        .footer-right {
            text-align: right;
            flex: 1 1 auto;

            .page-contact {
                text-align: right;
                margin-bottom: 0px;
				p{
					font-family: @font-alt;
					padding-bottom: 3px;
					font-size: 20px;
				}
            }
        }

        /* Navigation in desktop mode */
        #footer-navigation {
            width: 100%;
            margin: 10px 0;

            ul {
                flex-direction: row;
                justify-content:flex-end;
                gap: 30px;
            }
        }

        /* Copyright in desktop mode */
        .copyright-section {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .copyright-left {
                text-align: left;
            }

            .copyright-right {
                text-align: right;
            }
        }
    }
}

/* Social icons - maintain existing styles but adjust for the new design */
.social-icons {
    .fluid-property(--icon-size, 24px, 30px);
    .unstyled-list(@margin: 0 0 20px;);
    // .inline-flexbox(@main: center;);
	display: flex;
	flex-direction: row;
    gap: 15px;
    text-align: center;
    font-size: 0;

    li, a {
        display: block;
    }

    a {
        position: relative;
        width: var(--icon-size);
        overflow: hidden;

        span:nth-child(1) {
            position: relative;
            font-size: var(--icon-size);
            line-height: 1;
            color: @color-light;
            .scale(1);
            z-index: 10;
            .trans(transform);

            &.fa-facebook::before {content: '\f082';}
            &.fa-pinterest::before {content: '\f0d3';}
            &.fa-twitter::before {content: '\f081';}
            &.fa-youtube::before {content: '\f431';}
        }

        span:nth-child(2) {
            .position();
            opacity: 0;
            border-radius: 3px;
            z-index: 1;
            .trans(opacity);

            &.fa-facebook {background-color: #3B5998;}
            &.fa-linkedin {background-color: #1B92BD;}
            &.fa-pinterest {background-color: #CB2027;}
            &.fa-twitter {background-color: #00BDEC;}
            &.fa-youtube {background-color: #D20800;}
            &.fa-instagram {.gradient(radial; #fdf497, #d6249f 70%, #285AEB; circle at 30% 107%);}
            &.fa-tiktok {background-color: #000;}
            &.fa-houzz {background-color: #7CC04B;}
        }

        &:hover, &:focus {
            span:nth-child(1) {
                .scale(0.6);
            }

            span:nth-child(2) {
                opacity: 1;
            }
        }
    }

	&.profile-social{
		// margin-top: 20px;
		// margin-bottom: 35px;
		span:first-child, span:nth-child(2){
			color:@color-theme2 !important;
		}
	}

	&.staff-social {
		margin-top: 20px;
		margin-bottom: 35px;
		span:first-child, span:nth-child(2){
			color:@color-theme2 !important;
		}
	}
}


// Contact Panel Styles
@contact-panel-primary: #d9534f;
@contact-panel-bg: #f9f9f9;
@contact-panel-border: #ddd;
@contact-panel-text: #333;
@contact-panel-text-light: #555;
@contact-panel-text-lighter: #777;

.contact-panel-container {
    width: 100%;
    max-width: 5000px;
    margin: 0 ;

	.top-bar{
		padding: 10px 20px;
		border-radius: 8px;
		position: relative;
		display: flex;
		flex-direction: row;
		@media @max-tablet-l{
			flex-direction: column;
			margin-left: 20px;
			padding-left: 0px;
			align-items: flex-start	;
		}
		align-items: center;
		justify-content: space-between;
		.fluid-property(padding-left, 20px, 250px,1025px);
		.fluid-property(padding-right, 20px, 250px,1025px);
		margin-left: 20px;
		margin-right: 30px;
		.bold();
		.fluid-property(font-size, 25px, 49px,1025px);

		@media (max-width: 1675px) {
			.fluid-property(padding-left, 20px, 150px,1025px);
			.fluid-property(padding-right, 20px, 150px,1025px);
		}
	}
	.top-bar-content{
		text-align: left;
	}

	.top-bar-close{
		position: absolute;
		right: 20px;
		top: 50%;
		transform: translateY(-50%);

	}

	.contact-details {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		gap: 10px 55px;
		font-family: sans-serif;
		.fluid-property(font-size,20px,25px);
	  }
	  .contact-link {
		display: inline-flex;
		align-items: center;
		text-decoration: none;
		color: @color-dark;
		font-weight: bold;
	  }
	  .contact-link:hover {
		color:@color-theme1;
	  }
	  .contact-icon {
		margin-right: 8px;
		color:@color-theme2;
		font-size: 1em;
	  }

    // Top Section Styles
    .contact-top-section {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 40px;
        // background-color: @contact-panel-bg;
		background: url('../../images/svg/curve.svg');
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
        border-radius: 8px;
        overflow: hidden;
		.fluid-property(padding-left, 20px, 250px,1025px);
		.fluid-property(padding-right, 20px, 250px,1025px);

		@media (max-width: 1675px) {
			.fluid-property(padding-left, 20px, 150px,1025px);
			.fluid-property(padding-right, 20px, 150px,1025px);
		}

        .location-tabs-vertical {
			position: relative;
            flex: 0 0 30%;
            // background-color: darken(@contact-panel-bg, 5%);
            // padding: 20px 0;
            // min-height: 400px;

            h3 {
                padding: 0 20px 15px;
                margin: 0;
                border-bottom: 1px solid @contact-panel-border;
                color: @contact-panel-text;
            }

            .location-tabs-nav {
                list-style: none;
                padding: 0;
                margin: 0;
                margin-top: 10px;
				transition: transform 0.3s ease;
                .location-tab {
                    padding: 15px 20px;
                    cursor: pointer;
                    border-left: 4px solid transparent;
					line-height:var(--line-height-thin);
                    transition: all 0.3s ease;
					.fluid-property(font-size, 20px, 30px);
					color:@color-gray-light;

                    &:hover {
                        background-color: darken(@contact-panel-bg, 8%);
                    }

                    &.active {
                        .bold();
						// &:extend(.font-h4);
						.fluid-property(font-size, 30px, 45px);
						color: @color-dark;
                    }
                }
            }

			.location-scroll-arrow {
				// position: absolute;
				// left: 50%;
				position: relative;
				overflow: hidden;

				transform: translateX(-50%);
				width: 60px;
				height: 60px;
				// background-color:@color-theme2;
				background-image: url('../../images/arrow-circle-red.png'); // Default red circle
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				color: @color-light;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				z-index: 10;

				// &:hover {
				// 	color:red;
				// 	z-index:20;
				// }

				// Pseudo-element for the white circle animation
				&::before {
					content: '';
					position: absolute;
					left: 0;
					width: 100%;
					height: 100%;
					background-image: url('../../images/arrow-circle-white.png'); // White circle
					background-size: contain;
					background-repeat: no-repeat;
					background-position: center;
					z-index: -10; // Behind the icon
					transition: top 0.5s ease, bottom 0.5s ease; // Animation transition
				}

				&.disabled {
					opacity: 0.5;
					cursor: default;
					// Prevent hover animation on disabled arrows
					&:hover::before {
						// Keep pseudo-element hidden
						top: 100%; // For up arrow
						bottom: 100%; // For down arrow
					}
				}
			}

			.location-scroll-up {
				// top: 10px;
				margin-bottom: 25px;
				margin-left: 50px;
				// Animation setup
				&::before {
					top: 100%; // Start below the container
				}
				// Animate on hover (if not disabled)
				&:hover:not(.disabled)::before {
					top: 0; // Animate to top
				}


				&:hover:not(.disabled) {
					color:red;
					z-index:20;
				}

			}

			.location-scroll-down {
				// bottom: 10px;
				margin-top: 25px;
				margin-left: 50px;
				&::before {
					bottom: 100%; // Start above the container
				}
				// Animate on hover (if not disabled)
				&:hover:not(.disabled)::before {
					bottom: 0; // Animate to bottom
				}

				&:hover:not(.disabled) {
					color:red;
					z-index:20;
				}
			}

			.location-slider-wrapper {
				position: relative;
				overflow: hidden;
				// margin: 20px 0;
				padding: 40px 0; /* Add space for the arrows */
				// height: calc(100% - 80px); /* Account for arrows */
			}

			.location-tab {
				font-size: 16px;
			}
		}

        .contact-form-container {
            flex: 0 0 60%;
            padding: 30px;
			margin-right: 10px;

            h2 {
				.fluid-property(margin-top, 20px, 40px);
				.fluid-property(margin-bottom, 10px, 20px);
				color:@color-dark;
				&:extend(.font-h4);
            }

			.panel-form{
				.full-width{
					width: 100%;
				}

				.form-buttons{
					display: flex;
					flex-direction: row-reverse;
				}
			}
        }
    }

    // Bottom Section Styles
    .contact-bottom-section {
		position: relative;
        display: flex;
        flex-wrap: wrap-reverse;
        border-radius: 8px;
        overflow: hidden;
        padding: 120px 10px 20px 10px;

		.fluid-property(padding-left, 20px, 250px,1025px);
		.fluid-property(padding-right, 20px, 250px,1025px);

		@media (max-width: 1675px) {
			.fluid-property(padding-left, 20px, 150px,1025px);
			.fluid-property(padding-right, 20px, 150px,1025px);
		}

		// Add pseudo-element for the LOCATION background text
        &:before {
            content: "LOCATION";
            position: absolute;
            top: 15%;
            left: 50%;
            transform: translate(-50%, -50%);
			.fluid-property(font-size, 140px, 250px,1025px);
            font-weight: 700;
            font-family: 'Lora';
            color: rgba(239, 62, 52, 0.1);
            z-index: 0;
            white-space: nowrap;
            pointer-events: none;
            letter-spacing: 5px;

            @media @max-tablet-l {
                font-size: 100px;
            }

            @media @max-tablet-p {
                font-size: 60px;
            }
        }

        .location-details-container {
            flex: 0 0 30%;
            padding: 30px 0px 30px 0px;
			background-color: transparent;
            position: relative;
            z-index: 1;

            .location-details {
                .location-header {
                    margin-bottom: 20px;
                    padding-bottom: 15px;
                    border-bottom: 1px solid lighten(@contact-panel-border, 10%);

                    h3 {
                        margin-top: 0;
                        color: @contact-panel-text;
                    }
					p small{
						font-size:16px;
						color:@color-dark;
						background-color: rgba(241, 239, 238, 0.3);
					}

                    .location-address {
                        margin-top: 10px;
						p{
							.bold();
							line-height: var(--line-height-thin);
							font-size:20px;
							width:75%;
						}
                    }

                    .direction-btn {
                        display: inline-block;
                        margin-top: 10px;
                        padding: 5px 15px;
                        background-color: @contact-panel-primary;
                        color: white;
                        text-decoration: none;
                        border-radius: 4px;
                        font-size: 14px;

                        &:hover {
                            background-color: darken(@contact-panel-primary, 10%);
                        }
                    }
                }

                .contact-info {
					padding-right: 20px;
                    .contact-section {
                        margin-bottom: 25px;

                        h4 {
                            margin-top: 0;
                            margin-bottom: 10px;
                            color: @contact-panel-text-light;
                            font-size: 16px;
                        }

                        .contact-list, .hours-list {
                            list-style: none;
                            padding: 0;
                            margin: 0;

                            li {
                                // margin-bottom: 8px;
                                display: flex;
                                flex-wrap: wrap;

								.label{
									.bold();
								}
                                .label, .day {
                                    min-width: 100px;
                                    margin-right: 10px;
                                }
                            }
                        }

                        .open-status {
                            padding: 5px 10px;
                            margin-bottom: 15px;
                            border-radius: 4px;
                            display: inline-block;
                            font-weight: bold;

                            &.open {
                                background-color: #5cb85c;
                                color: white;
                            }

                            &.closed {
                                background-color: @contact-panel-primary;
                                color: white;
                            }

                            &.closing {
                                background-color: #f0ad4e;
                                color: white;
                            }
                        }

                        .hours-list {
                            .today {
                                // font-weight: bold;
                                color: @color-yellow;
                            }
							.day, .hours {
								font-weight: normal;
							}
                        }

                        .hours-disclaimer {
                            font-size: 12px;
                            color: @contact-panel-text-lighter;
                            margin-top: 10px;
                        }
                    }
                }
            }
        }

        .map-container {
            flex: 0 0 70%;
            min-height: 400px;

            .contact-map {
                width: 100%;
                height: 100%;
                min-height: 400px;
            }
        }
    }
}

// Responsive styles for contact panel
@media @max-tablet-l {
    .contact-panel-container {
        .contact-top-section {
            .location-tabs-vertical,
            .contact-form-container {
                flex: 0 0 100%;
            }

            .location-tabs-vertical {
                min-height: auto;
            }
        }

        .contact-bottom-section {
            .map-container,
            .location-details-container {
                flex: 0 0 100%;
            }

            .map-container {
                order: 1;
            }

            .location-details-container {
                order: 2;
            }
        }
    }
}


// Partners
.partner-grid{
	// .flexbox(row wrap);
	// gap:30px;

	.flexbox(row wrap);
	--count: 1;
	--gap: 30px;
	--gap-sum: (var(--gap) * (var(--count) - 1));
	--item-width: calc((100% - var(--gap-sum)) / var(--count));
	gap: var(--gap);
	// box-sizing: border-box;

	@media @tablet-l{--count: 2; }
	@media @notebook{--count: 4; }
	@media @max-tablet-l{
		justify-content: center;
		align-items: center;
		--count: 2;
	}

	// .partner-listing{
	// 	text-align: center;
	// 	background: url("../../images/partners/background.png") no-repeat center center/cover;


	// 	img{
	// 		display: block;
	// 		max-width: 100%;
	// 		max-height: 100%;
	// 		transition: transform 0.3s ease;
	// 		position: relative;
	// 		z-index: 1;
	// 	}

	// 	position: relative;
	// 	overflow: hidden;
	// 	transition: all 0.6s ease;

	// 	// &::before {
	// 	// 	content: '';
	// 	// 	position: absolute;
	// 	// 	top: 0;
	// 	// 	left: 0;
	// 	// 	right: 0;
	// 	// 	bottom: 0;
	// 	// 	background: url("../../images/partners/background-h.png") no-repeat center center/cover;
	// 	// 	opacity: 0;
	// 	// 	transition: opacity 0.6s ease;
	// 	// 	z-index: 0;
	// 	// }


	// 	&:before, &:after {
	// 		content: '';
	// 		position: absolute;
	// 		left: 0;
	// 		width: 100%;
	// 		height: 0px;
	// 		background-color: #e30613;
	// 		transform: scaleX(0);
	// 		// transition: transform 0.3s ease;
	// 		transition: height 0.3s ease;
	// 	}

	// 	 /* Add a pseudo-element for the background */
	// 	//  &::after {
	// 	// 	content: '';
	// 	// 	position: absolute;
	// 	// 	top: 0;
	// 	// 	left: 0;
	// 	// 	right: 0;
	// 	// 	bottom: 0;
	// 	// 	background: url("../../images/partners/background.png") no-repeat center center/cover;
	// 	// 	z-index: -1;
	// 	// 	transition: transform 0.3s ease;
	// 	// }

	// 	&:before {
	// 		top: 0;
	// 		// transform-origin: left;
	// 	}

	// 	&:after {
	// 		bottom: 0;
	// 		// transform-origin: right;
	// 	}

	// 	&:hover{
	// 		// background-size: 105%;
	// 		&:before, &:after {
	// 			transform: scaleX(1);
	// 			height: 5px;
	// 		}

	// 		img {
	// 			transform: scale(1.05);
	// 		}

	// 	background: url("../../images/partners/background-h.png") no-repeat center center/cover;
	// 	transition: background 1s ease;
	// 	background-size: 115%;

	// 		/* Scale the background pseudo-element instead */
	// 		&::after {
	// 			transform: scale(1.05);
	// 		}
	// 	}

	// 	.partner-image-wrapper{
	// 		height: 285px; // Change according to maximum image height
	// 		width: 263px;
	// 		padding: 10px 20px;
	// 		border:1px solid @color-gray-light;

	// 		@media @max-tablet-l {
	// 			height: 208px;
	// 			width: 200px;
	// 		}

	// 		@media @max-tablet-p {
	// 			height: 170px;
	// 			width: 165px;
	// 		}

	// 		.partner-image{
	// 			pointer-events: auto;
	// 			height: 100%;
	// 			.flexbox(row nowrap; center; center;);

	// 		}
	// 	}
	// }

	// partly working
	// .partner-listing {
	// 	text-align: center;
	// 	position: relative;
	// 	overflow: hidden; /* Contains the scaled background */

	// 	/* Base background with pseudo-element */
	// 	&::before {
	// 		content: '';
	// 		position: absolute;
	// 		top: 0;
	// 		left: 0;
	// 		width: 100%;
	// 		height: 100%;
	// 		background: url("../../images/partners/background.png") no-repeat center center/cover;
	// 		z-index: -2; /* Behind everything */
	// 		transition: transform 0.6s ease, opacity 0.6s ease;
	// 	}

	// 	/* Hover background with pseudo-element */
	// 	&::after {
	// 		content: '';
	// 		position: absolute;
	// 		top: 0;
	// 		left: 0;
	// 		width: 100%;
	// 		height: 100%;
	// 		background: url("../../images/partners/background-h.png") no-repeat center center/cover;
	// 		z-index: -1; /* Between base background and content */
	// 		opacity: 0;
	// 		transition: opacity 0.6s ease, transform 0.6s ease;
	// 	}

	// 	/* Border elements */
	// 	.top-border, .bottom-border {
	// 		position: absolute;
	// 		left: 0;
	// 		width: 100%;
	// 		height: 0;
	// 		background-color: #e30613;
	// 		z-index: 2; /* Above everything */
	// 		transition: height 0.3s ease;
	// 	}

	// 	.top-border {
	// 		top: 0;
	// 	}

	// 	.bottom-border {
	// 		bottom: 0;
	// 	}

	// 	img {
	// 		display: block;
	// 		max-width: 100%;
	// 		max-height: 100%;
	// 		transition: transform 0.6s ease;
	// 		position: relative;
	// 		z-index: 1;
	// 	}

	// 	&:hover {
	// 		/* Scale the base background slightly */
	// 		&::before {
	// 			transform: scale(1.05);
	// 			opacity: 0;
	// 		}

	// 		/* Show the hover background */
	// 		&::after {
	// 			opacity: 1;
	// 			transform: scale(1.0);
	// 		}

	// 		/* Show the borders */
	// 		.top-border, .bottom-border {
	// 			height: 5px;
	// 		}

	// 		/* Scale the image */
	// 		img {
	// 			transform: scale(1.05);
	// 		}
	// 	}

	// 	.partner-image-wrapper {
	// 		height: 285px; // Change according to maximum image height
	// 		width: 263px;
	// 		padding: 10px 20px;
	// 		border: 1px solid @color-gray-light;
	// 		position: relative;
	// 		z-index: 1;

	// 		@media @max-tablet-l {
	// 			height: 208px;
	// 			width: 200px;
	// 		}

	// 		@media @max-tablet-p {
	// 			height: 170px;
	// 			width: 165px;
	// 		}

	// 		.partner-image {
	// 			pointer-events: auto;
	// 			height: 100%;
	// 			.flexbox(row nowrap; center; center;);
	// 		}
	// 	}
	// }


	//
	.partner-listing {
		text-align: center;
		position: relative;
		overflow: hidden; /* Contains the scaled background */
		border-radius: 10px;
		border: 1px solid @color-gray-light;



		/* Base background - will be hidden on hover */
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: url("../../images/partners/background.png") no-repeat center center;
			background-size: 100% 100%;
			z-index: -2;
			opacity: 1;
			transition: opacity 0.6s ease;
		}

		/* Moving arcs background */
		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: url("../../images/partners/background-h.png") no-repeat;
			background-size: 110% 110%;
			// background-position: 20% -20%; /* Initial position - top right arc higher, bottom left arc lower */
			background-position: -5% 5%; /* Initial position - offset to allow diagonal movement */
			transform: scale(1) skew(0deg, 0deg); /* No skew initially */

			z-index: -1;
			opacity: 0;
			// transition: opacity 0.6s ease, background-position 0.9s ease;
			// transition: opacity 0.6s ease, background-position 0.9s ease, transform 0.9s ease;

			transform-origin: top right bottom left; /* Set transform origin to corners */
			transition: opacity 0.9s ease, background-position 2s ease, transform 2s ease;
		}

		/* Border elements */
		.top-border, .bottom-border {
			position: absolute;
			left: 0;
			width: 100%;
			height: 0;
			background-color: #e30613;
			z-index: 2; /* Above everything */
			transition: height 0.3s ease;
		}

		.top-border {
			top: 0;
		}

		.bottom-border {
			bottom: 0;
		}

		img {
			display: block;
			max-width: 100%;
			max-height: 100%;
			transition: transform 0.6s ease;
			position: relative;
			z-index: 1;
		}

		&:hover {
			/* Hide the original background */
			&::before {
				opacity: 0;
			}

			/* Show the movement background with shifted arcs */
			&::after {
				opacity: 1;
				// background-position: 0% 0%; /* End position - top arc lower, bottom arc higher */
				background-position: 5% -5%; /* Move in opposite directions */
				// transform: skew(-4deg, -4deg); /* Subtle skew to enhance the diagonal stretching effect */
				transform: scale(1.05); /* Scale slightly and skew for diagonal stretch */
			}

			/* Show the borders */
			.top-border, .bottom-border {
				height: 5px;
			}

			/* Scale the image */
			img {
				transform: scale(1.05);
			}
		}

		.partner-image-wrapper {
			height: 285px; // Change according to maximum image height
			width: 263px;
			padding: 10px 20px;
			position: relative;
			z-index: 1;

			@media @max-tablet-l {
				height: 208px;
				width: 200px;
			}

			@media @max-tablet-p {
				height: 170px;
				width: 165px;
			}

			.partner-image {
				pointer-events: auto;
				height: 100%;
				.flexbox(row nowrap; center; center;);
			}
		}
	}
	//


	// .partner-listing {
	// 	text-align: center;
	// 	background: url("../../images/partners/background.png") no-repeat center center/cover;
	// 	position: relative;
	// 	overflow: hidden; /* Add this to contain the scaled background */

	// 	/* Add this for the background zoom effect */
	// 	&::before {
	// 		content: '';
	// 		position: absolute;
	// 		top: 0;
	// 		left: 0;
	// 		right: 0;
	// 		bottom: 0;
	// 		background: url("../../images/partners/background.png") no-repeat center center/cover;
	// 		z-index: -1; /* Behind everything */
	// 		transition: transform 0.3s ease;
	// 	}

	// 	/* Border elements - keep these separate from the background element */
	// 	.top-border {
	// 		content: '';
	// 		position: absolute;
	// 		top: 0;
	// 		left: 0;
	// 		width: 100%;
	// 		height: 0;
	// 		background-color: #e30613;
	// 		transition: height 0.3s ease;
	// 		z-index: 2; /* Above everything */
	// 	}

	// 	.bottom-border {
	// 		content: '';
	// 		position: absolute;
	// 		bottom: 0;
	// 		left: 0;
	// 		width: 100%;
	// 		height: 0;
	// 		background-color: #e30613;
	// 		transition: height 0.3s ease;
	// 		z-index: 2; /* Above everything */
	// 	}

	// 	img {
	// 		display: block;
	// 		max-width: 100%;
	// 		max-height: 100%;
	// 		transition: transform 0.3s ease;
	// 	}

	// 	&:hover {
	// 		/* Scale the background */
	// 		&::before {
	// 			transform: scale(1.05);
	// 		}

	// 		/* Show the borders */
	// 		.top-border, .bottom-border {
	// 			height: 5px;
	// 		}

	// 		img {
	// 			transform: scale(1.05);
	// 		}
	// 	}
	// }
}

.partner-listings{
	margin: 0 -20px;
	.flexbox(row wrap);

	.partner-listing{
		width: 100%;
		padding: 0 20px 40px;
		.flex(0 0 auto);
	}
}

.partner-category-title{
	margin-top: 40px;
}

.partner-category-content{

}



// @media @tablet-p {
// 	.partner-listings .partner-listing{width: 50%; }
// }
// @media @tablet-l {
// 	.partner-listings .partner-listing{width: 33.33%; }
// }
// @media @notebook {
// 	.partner-listings .partner-listing{width: 25%; }
// }

// Staff
#staff-section{
	// Add pseudo-element for the OUR STAFF background text
	&:before {
		content: "OUR STAFF";
		position: absolute;
		top: 25%;
		left: 50%;
		transform: translate(-50%, -50%);
		.fluid-property(font-size, 140px, 225px,1025px);
		font-weight: 700;
		font-family: 'Lora';
		color: rgba(239, 62, 52, 0.1);
		z-index: 0;
		white-space: nowrap;
		pointer-events: none;
		letter-spacing: 5px;

		@media @max-notebook{
			top:20%;
		}

		@media @max-tablet-l {
			font-size: 100px;
			top:17%;
		}

		@media @max-tablet-p {
			font-size: 60px;
			top:13%;
		}
	}

	.staff-listing{
		.flexbox(row wrap);
		--count: 1;
		--gap: 80px;
		--gap-sum: (var(--gap) * (var(--count) - 1));
		--item-width: calc((100% - var(--gap-sum)) / var(--count));
		gap: var(--gap);
		margin-left: 0;

		@media @max-tablet-l{
			justify-content: center;
			align-items: center;
			--count: 2;
			--gap: 50px;
		}
		@media @tablet-l{--count: 2; }
		@media @notebook{--count: 4; }

		li::marker{
			content: '';
		}

	}


}

.staff-info-box{
	.flexbox(row nowrap; space-between;);
	&.other-staff-info-box{
	margin-top: 10px;
	}

	.staff-info{
		.flexbox(column nowrap);
		flex:1;
		h5 {
			margin-bottom: 2px;
		}
	}
}

#staff-bio{
	.flexbox(row nowrap;);
	gap: 25px;
	.image-box{
		flex: 0.6;
		img{
			height: 400px;
			width: 400px;
			object-fit: contain;
		}
	}
	.content-box{
		flex: 1;
	}

	@media @max-tablet-l{
		flex-direction: column;
		gap: 0px;

		.image-box{
			display: flex;
			flex: 1;
			justify-content: center;
			align-items:center;
			img{
				height: 300px;
				width: 300px;
				object-fit: contain;
			}
		}

		.content-box{
			flex: 1;
			display: flex;
			flex-direction: column;
			flex: 1;
			justify-content: center;
			align-items:center;

			.page-buttons{
				align-self: flex-start;
			}
		}
	}
}


// .location-scroll-arrow {
// 	position: relative;
// 	overflow: hidden;
// 	width: 60px;
// 	height: 60px;
// 	background-image: url('../../images/arrow-circle-red.png');
// 	background-size: contain;
// 	background-repeat: no-repeat;
// 	background-position: center;
// 	color: @color-light;
// 	border-radius: 50%;
// 	display: flex;
// 	align-items: center;
// 	justify-content: center;
// 	cursor: pointer;
// 	z-index: 10;

// 	a i{
// 		color: @color-light;

// 		&:hover{
// 			color:@color-theme2;
// 		}

// 	}
// 	// Pseudo-element for the white circle animation
// 	&::before {
// 		content: '';
// 		position: absolute;
// 		width: 100%;
// 		height: 100%;
// 		background-image: url('../../images/arrow-circle-white.png');
// 		// background-color: #00BDEC;
// 		background-size: contain;
// 		background-repeat: no-repeat;
// 		background-position: center;
// 		z-index: -10;
// 		left: -100%;
// 		transform: rotate(0deg); // Initial rotation
// 		transition: left 0.5s ease, right 0.5s ease, transform 0.5s ease;
// 	}
// }

// .location-scroll-right {
// 	&::before {
// 		left: -100%;
// 		transform: rotate(0deg); // Reset rotation
// }

// 	&:hover:not(.disabled)::before {
// 		left: 0;
// 		transform: rotate(90deg); // Rotate to horizontal on hover

// 	}

// 	&:hover:not(.disabled) {
// 		color: @color-theme2;
// 		z-index:20;
// 		a i {
// 			color:@color-theme2;
// 			z-index: inherit;
// 		}
// 	}
// }

// //////////////////////////////
// location arrow styles start

.location-scroll-arrow {
	position: relative;
	overflow: hidden;
	width: 60px;
	height: 60px;
	// background-image: url('../../images/arrow-circle-red.png');
	background-image: url('../../images/white-arrow-ra.png');

	&.location-scroll-left{
		background-image: url('../../images/white-arrow.png');

		&::before{
			right: -100%;
			left: auto;
		}
	}

	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
	color: @color-light;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 1;

	a i{
		color: @color-light;
		z-index: 1;
		&:hover{
			color:@color-theme2;
			z-index: -20;
		}

	}
	// Pseudo-element for the white circle animation
	&::before {
		content: '';
		position: absolute;
		width: 100%;
		height: 100%;
		// background-image: url('../../images/arrow-circle-white.png');
		background-image: url('../../images/red-arrow-up.png');
		// background-color: #00BDEC;
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		z-index: -10;
		left: -100%; // right for left arrow
		transform: rotate(0deg); // Initial rotation
		transition: left 0.5s ease, right 0.5s ease, transform 0.5s ease;
	}
}

.location-scroll-right {
	&::before {
		left: -100%;
		transform: rotate(0deg); // Reset rotation
	}

	&:hover:not(.disabled)::before {
		left: 0;
		transform: rotate(90deg); // Rotate to horizontal on hover

	}

	&:hover:not(.disabled) {
		color: @color-theme2;
		z-index:20;
		a i {
			color:@color-theme2;
			z-index: inherit;
		}
	}
}

.location-scroll-left {
	&::before {
		right: -100%;
		transform: rotate(0deg); // Reset rotation
	}

	&:hover:not(.disabled)::before {
		right: 0;
		transform: rotate(-90deg); // Rotate to horizontal on hover
	}

	&:hover:not(.disabled) {
		color: @color-theme2;
		z-index:20;
		a i {
			color:@color-theme2;
			z-index: inherit;
		}
	}
}

// location arrow styles end
////////////////////////////////

// .location-scroll-arrow {
// 	position: relative;
// 	overflow: hidden;
// 	width: 60px;
// 	height: 60px;
// 	background-image: url('../../images/arrow-circle-red.png');
// 	background-size: contain;
// 	background-repeat: no-repeat;
// 	background-position: center;
// 	color: @color-light;
// 	border-radius: 50%;
// 	display: flex;
// 	align-items: center;
// 	justify-content: center;
// 	cursor: pointer;
// 	z-index: 10;
// 	i{
// 		z-index: 1;
// 	}

// 	// Pseudo-element for the white circle animation
// 	&::before {
// 		content: '';
// 		position: absolute;
// 		width: 100%;
// 		height: 100%;
// 		background-image: url('../../images/arrow-circle-white.png');
// 		// background-color: #00BDEC;
// 		background-size: contain;
// 		background-repeat: no-repeat;
// 		background-position: center;
// 		z-index: -10;
// 		transition: left 0.5s ease, right 0.5s ease, transform 0.5s ease; // Added transform transition
// 		left: -100%;
// 		transform: rotate(0deg); // Initial rotation
// 	}
// }

// .location-scroll-right {
// 	&::before {
// 		left: -100%;
// 		transform: rotate(0deg); // Reset rotation
// 	}

// 	&:hover:not(.disabled)::before {

// 		left: 0;
// 		transform: rotate(90deg); // Rotate to horizontal on hover
// 	}

// 	&:hover:not(.disabled) {
// 		color: @color-theme2;
// 		z-index: 20;
// 		i{
// 			z-index: -10;
// 			opacity: 0;
// 		}
// 	}
// }

.contact-info{
	background: #80808038;
    // width: 200px;
    width: 70%;
	border-radius: 5px;
	padding:5px 10px;
	margin-bottom: 10px;

	.contact-separator{
		border: 1px solid #80808038;
		margin: 2px;
	}

	.staff-email{
		color: @color-dark;
	}
}

// Other Staff section styling
.other-staff-section {
	// background-color: #18222d; // Dark blue background as shown in image
	// padding: 40px 100px;
	.fluid-property(padding-left, 40px, 100px,1025px);
	.fluid-property(padding-right, 40px, 100px,1025px);
	.fluid-property(padding-top, 40px, 40px,1025px);
	.fluid-property(padding-bottom, 40px, 40px,1025px);

	.staff-section-title {
	  font-size: 2.5rem;
	  color: @color-dark;
	  margin-bottom: 40px;
	  text-align: left;

	  span {
		color: @color-theme2;
	  }
	}

	.staff-grid {
	  display: flex;
	  flex-wrap: wrap;
	  justify-content: space-between;
	  gap: 20px;

	  .staff-card {
		flex: 1 1 calc(25% - 20px);
		min-width: 200px;
		max-width: calc(25% - 100px);
		position: relative;
		overflow: hidden;

		a {
		  display: block;
		  width: 100%;

		  img {
			width: 100%;
			height: auto;
			display: block;
			transition: transform 0.3s ease;

			&:hover {
			  transform: scale(1.05);
			}
		  }
		}

		.staff-info {
		  background-color: transparent;
		  padding: 10px 0;
		  text-align: left;

		  h3 {
			color: @color-dark;
			font-size: 1.2rem;
			margin: 0;
			margin-bottom: 5px;
		  }

		  p {
			color: @color-dark;
			margin: 0;
			font-size: 0.9rem;
			opacity: 0.8;
			margin-bottom: 3px;
		  }
		}
	  }
	}
  }

  // Responsive styles
  @media @max-notebook {
	.other-staff-section {
	  .staff-grid {
		.staff-card {
		  flex: 1 1 calc(50% - 20px);
		  max-width: calc(50% - 20px);
		}
	  }
	}
  }

  @media @max-tablet-p {
	.other-staff-section {
	  .staff-grid {
		.flexbox(row wrap);
		--count: 1;
		--gap: 10px;
		--gap-sum: (var(--gap) * (var(--count) - 1));
		--item-width: calc((100% - var(--gap-sum)) / var(--count));
		gap: var(--gap);
		justify-content: center !important;
		.staff-card {
		//   flex: 1 1 100%;
		  max-width: 80%;
		  margin-bottom: 20px;
			a img{
				object-fit: contain;
			}
		}
	  }
	}
  }

// // Add media query for screens between 426px and tablet portrait
// @media (min-width: 321px) and (max-width: @tablet-p) {
// 	.other-staff-section {
// 		.staff-grid {
// 			.flexbox(row wrap);
// 			--count: 2;
// 			--gap: 10px;
// 			--gap-sum: (var(--gap) * (var(--count) - 1));
// 			--item-width: calc((100% - var(--gap-sum)) / var(--count));
// 			gap: var(--gap);
// 			.staff-card {
// 				max-width: var(--item-width);
// 				margin-bottom: 20px;
// 				a img{
// 					object-fit: contain;
// 				}
// 			}
// 		}
// 	}
// }

// // Add media query for screens at 425px and below
// @media (max-width: 320px) {
// 	.other-staff-section {
// 		.staff-grid {
// 			.flexbox(column nowrap);
// 			// --count: 1;
// 			// --gap: 15px;
// 			// gap: var(--gap);
// 			gap: 0px;
// 			justify-content: center !important;
// 			.staff-card {
// 				max-width: 100%;
// 				width: 100%;
// 				margin-bottom: 15px;
// 				a img{
// 					object-fit: contain;
// 				}
// 			}
// 		}
// 	}
// }

// login pages
.login-container{
	width: 100%;
	max-width: 600px;
	margin: 0 auto;

	.login-info{
		.login-info-text{
			font-family: @font-alt;
			text-align: center;
			.register{
				color: @color-theme2;
			}
		}
	}

	#login-form{
		padding: 30px;
		border:1px solid @color-gray-light;
		border-radius: 1px;
	}
	.login-bottom-info{
		.flexbox(row nowrap);
		justify-content:space-between;

		small a{
			color: @color-dark;
		}
	}

	.button-wrapper{
		margin-top:10px;
		&.reset{
			margin-top:30px;
		}
		button{
			display: block;
			margin : 0 auto !important;
		}
	}

	#reset-form{
		border:1px solid @color-gray-light;
		border-radius: 1px;
		padding:30px;
	}
}


// --- Form Grid Layout ---

// Define variables for gaps and breakpoint (optional but good practice)
// @form-grid-gap-row: 20px;
// @form-grid-gap-column: 30px;
// @form-grid-breakpoint: 768px; // Adjust breakpoint as needed (e.g., tablet portrait)


.form-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr); // Creates two equal-width columns
	gap: 20px 30px; // Vertical gap, Horizontal gap
	margin-bottom: 20px; // Add some space below the grid before the next element (like recaptcha/button)

	// Responsive stacking for smaller screens
	@media @max-tablet-l {
		grid-template-columns: 1fr; // Stack into a single column
		gap: 20px; // Only need vertical gap now
	}

	border:1px solid@color-gray-light;
	.fluid-property(padding, 20px, 25px);
	border-radius: 2px;

	&.invoice-info{
		grid-template-columns: repeat(3, 1fr);
		@media @max-tablet-l {
			grid-template-columns: 1fr;
			gap: 20px;
		}
	}

	&.no-border{
		border: none;
	}

	&.top-margin{
		margin-top:20px;
		display:block;
	}
}
.form-grid-container{
	padding: 5px;
	border: 1px solid @color-gray-light;
	border-radius: 2px;
}

.form-field {
	label {
		display: block; // Make sure label takes its own line
		margin-bottom: 5px; // Space between label and input/select
		font-weight: bold; // Optional: Make labels bold like in the target image
	}

	// Ensure inputs/selects fill their grid cell horizontally
	input[type="text"],
	input[type="email"],
	input[type="password"],
	input[type="tel"], // Add other input types if used
	select {
		width: 100%;
		padding: 10px 12px; // Adjust padding as needed
		border: 1px solid #ccc;
		box-sizing: border-box; // Crucial for width: 100% + padding/border
	}

		// Style the required asterisk
	label .req {
			color: red; // Or your theme's required color
			font-weight: bold; // Make it stand out
			margin-left: 2px;
		}
}

// #register-form { // Target the specific form or use a more general selector if applicable
     // Optional: Style the button if needed (e.g., center it or make it full width below the grid)
     #register-btn {
         // Example: Center the button below the form fields
         // display: block;
         // margin: 20px auto 0; // Add top margin, center horizontally

         // Or, if you want it aligned similar to the target image (right-aligned)
         display: block; // Or inline-block if needed alongside other elements
         margin-left: auto; // Push to the right if container allows
         margin-right: 0;
         margin-top: 20px; // Add space above the button
         padding: 12px 25px; // Adjust padding
         // Add other button styles (background, color, border-radius)
     }
// }

// General Styles (if not already present) that might be relevant
p small {
    // Style the "Required Fields" text if needed
}
.req {
   color: red; // Ensure this is globally defined or within the form scope
   font-weight: bold;
}

h4 {
    margin-top: 20px; // Add space above section headings
    margin-bottom: 15px;
}

.alert-danger{
	color:@color-theme2;
}

//
.dashboard-container{
	// .flex(row wrap);
	display: flex;
	flex-direction: row;
	gap:40px;

	@media @max-tablet-l{
		flex-direction: column;
	}

	.menu-container{
		flex:0.35;
		h4{
			margin-top:0;
		}
	}

	#account-navigation {
		ul{
			list-style-type: none;
			padding:10px 20px;
			border:1px solid @color-gray-light;
			margin-left:0px;

			li{
				padding:5px;
				border-bottom: 1px solid @color-gray-light;
				&:last-child{
					border-bottom:none;
				}
				a{
					color:@color-dark;
				}
			}
		}
	}

	.dashboard{
		flex:0.65;

		ul{
			margin-left:0px;
			list-style-type: none;
			li{
				color:@color-theme2;
			}
		}
	}
}


//  end login pages



// Blog Page Styling
.blog-page {
	background-color: #1a2433;
	color: #fff;

	#blog-leftcol.blog-sidebar {
	// @media @max-tablet-p{
	@media (max-width:600px){
		display: none;
	}
	  width: 30%;
	  background-color: #1a2433;
	  padding: 15px;
	  border-right: 1px solid rgba(255,255,255,0.1);

	  .blog-search {
		position: relative;
		margin-bottom: 20px;

		input {
		  width: 100%;
		  padding: 10px 30px 10px 10px;
		  border:1px solid @color-gray-light;
		  color: @color-dark;
		  background-color: @color-light;
		  border-radius: 2px;

		  &::placeholder {
			color:@color-gray;
		  }
		}

		.search-icon {
		  position: absolute;
		  right: 10px;
		  top: 50%;
		  transform: translateY(-50%);
		  color: #e94e1b;
		}
	  }

	  .blog-posts-list {
		height: calc(100vh - 200px);
		overflow-y: auto;
	  }

	  .blog-swiper-container {
		height: 100%;
		width: 100%;

		.swiper-slide {
		  padding: 15px;
		  border-bottom: 1px solid rgba(255,255,255,0.1);
		  cursor: pointer;
		  transition: background-color 0.3s ease;
		  border: 1px solid @color-gray-light;
		  margin-bottom: 15px;
		  background-color: @color-light;

		  &:hover, &:active {
			background-color: #f7f7f7;

			.blog-post-item {
			  background-color: #f7f7f7;
			}
		  }

		  &.active {
			background-color: #f7f7f7;
		  }

		  .blog-post-item {
			display: flex;
			flex-direction: column;
			background-color: transparent;
			transition: background-color 0.3s ease;

			.post-date {
			  font-size: 12px;
			  color: rgba(0,0,0,0.5);
			  margin-bottom: 5px;
			}

			.post-title {
			  font-weight: bold;
			  color: @color-dark;
			  margin-bottom: 5px;
			}

			.post-category {
			  font-size: 12px;
			  color: #e94e1b;
			}
		  }
		}

		.swiper-scrollbar {
		  right: 0;
		  width: 4px;
		  background-color: rgba(255,255,255,0.1);

		  .swiper-scrollbar-drag {
			background-color: #e94e1b;
		  }
		}
	  }

	  #blog-navigation {
		h6 {
		  color: #fff;
		  font-size: 14px;
		  text-transform: uppercase;
		  margin-bottom: 10px;
		}

		ul.blog-categories {
		  list-style: none;
		  padding: 0;
		  margin-bottom: 20px;

		  li {
			border-bottom: 1px solid rgba(255,255,255,0.1);
			padding: 8px 0;

			a {
			  color: #fff;
			  display: block;
			  text-decoration: none;
			  font-size: 14px;
			  padding: 5px 0;

			  &:hover,
			  &.active {
				color: #e94e1b;
			  }
			}
		  }
		}
	  }
	}

	#blog-rightcol.blog-content {
	  width: 70%;
	  padding-left: 30px;
	  color: @color-dark;
	  background-color: @color-light;

	  .blog-date {
		padding-top: 10px;
		color: @color-dark;
		display: block;
		margin-bottom: 5px;
	  }

	  .blog-title {
		padding-top: 5px;
		margin-bottom: 5px;
	  }

	  .blog-entry {
		color: @color-dark;
		margin-bottom: 30px;
		display: flex;
		flex-direction: column;

		.blog-entry-media {
		  margin-bottom: 15px;

		  .blog-thumb img {
			width: 100%;
			height: auto;
			max-height: 300px;
			object-fit: cover;
		  }
		}

		.blog-entry-content {
		  h4 a {
			color: @color-dark;
			font-size: 24px;

			&:hover {
			  color: @color-theme2;
			}
		  }


		  hr {
			border-color: rgba(255,255,255,0.1);
			margin: 15px 0;
		  }

		  .blog-description {
			color: @color-dark;
			line-height: 1.6;
			margin-bottom: 15px;
		  }

		  .blog-actions a {
			color: #e94e1b;

			&:hover {
			  text-decoration: underline;
			}
		  }
		}
	  }
	}

	// @media (max-width: 768px) {
	#blog-leftcol.blog-sidebar {
		background: #fff !important;
		// color: #000 ;
		// a, .post-title, .post-date, .post-category, h6, ul.blog-categories li a {
			// color: #000;
		// }
		// .swiper-button-next, .swiper-button-prev {
			// background: #000 !important;
			// color: #fff !important;
			// &:after { color: #fff !important; }
		// }
	}

	#blog-rightcol.blog-content {
		width: 100%;
		float: none;
		padding-left: 0;
	}

	#blog-leftcol.blog-sidebar {
		margin-bottom: 30px;
	}

	.blog-posts-list {
		height: 700px;
	}
}

// Blog Swiper Container
.blog-swiper-container {
  position: relative;
  overflow: visible;
  height: auto !important;
  margin: 0;
  padding:5px 0;

  // Hide scrollbar
  .swiper-scrollbar {
    display: none;
  }

  // Swiper wrapper
  .swiper-wrapper {
    height: auto !important;
    overflow: visible;
	padding:5px;
  }

  // Blog post slides
  .swiper-slide {
    background-color: @color-light;
    border-radius:4px;
    transition: background-color 0.3s ease;
    height: auto !important;
    opacity: 1;

    &:hover,
    &.active {
		opacity: 0.8;
    }

    &.visible {
      display: block;
    }
  }

  // Blog post item content
  .blog-post-item {
    padding: 6px;
    position: relative;
  }

  // Chevron icon in blog post items
  .post-chevron {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: @color-dark;
  }

  // Post title
  .post-title {
    font-weight: bold;
    font-size: var(--font-paragraph);
    margin-bottom: 5px;
    color: @color-light;
  }

  // Post date
  .post-date {
    font-size: var(--font-caption);
    color: @color-light;
    margin-bottom: 5px;
  }

  // Post category
  .post-category {
    font-size: var(--font-caption);
    color: @color-theme2;
  }

  // Navigation buttons
  .swiper-button-next,
  .swiper-button-prev {
    width: 60px;
    height: 60px;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 50%;
    margin: 0;
    box-shadow: none;
    transition: all 0.3s ease;
    z-index: 100;
    cursor: pointer;
    overflow: hidden;

    // Base styling
    position: relative;
    overflow: hidden;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 50%;

    // Hide default swiper button content
    &:after {
      content: none;
    }

    // Hover effect container
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      transition: transform 0.3s ease;
    }
  }

  // Position and style navigation buttons
  .swiper-button-next {
    bottom: -60px;
    top: auto;
    position: absolute;
    z-index: 100;
    transform: translateX(-50%) rotate(180deg);
    background-image: url(../../images/button-top-arrow-red.png);

    &::before {
      background-image: url(../../images/button-top-arrow.png);
      transform: translateY(100%); // Changed from 100% to -100%
    }

    &:hover::before {
      transform: translateY(-100%);
    }
  }

  .swiper-button-prev {
    top: -60px;
    bottom: auto;
    position: absolute;
    z-index: 100;
    transform: translateX(-50%) rotate(180deg);
    background-image: url(../../images/button-bottom-arrow-red.png);

    &::before {
      background-image: url(../../images/button-top-arrow.png);
      transform: translateY(-100%); // Changed from -100% to 100%
    }

    &:hover::before {
      transform: translateY(0);
    }
  }

  // Custom arrow styling
  .blog-nav-arrow {
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: @color-coral-red;
      border-radius: 50%;
      z-index: -1;
      transition: transform 0.3s ease;
      transform: scale(0);
    }
	i{
		&:before, &:after {
			content: none;
		}
	}
  }

  // Specific hover animations for up/down arrows
  .swiper-button-prev.blog-nav-arrow {
	background-image: url(../../images/button-bottom-arrow-red.png);
    &::before {
      transform: translateY(-100%) scale(0); // Start below and hidden
    }

    &:hover::before {
	background-image: url(../../images/button-bottom-arrow.png);
	transform: translateY(0) scale(1); // Move up and show
    }
  }

  .swiper-button-next.blog-nav-arrow {
	background-image: url(../../images/button-top-arrow-red.png);
    &::before {
      transform: translateY(100%) scale(0); // Start above and hidden
    }

    &:hover::before {
      transform: translateY(0) scale(1) rotate(180deg); // Move down and show
	  background-image: url(../../images/button-top-arrow.png);
    }
  }

  // Hide navigation for few posts
  &.few-posts {
    .swiper-button-next,
    .swiper-button-prev {
      display: none;
    }
  }
}

// Blog posts list container
.blog-posts-list {
  position: relative;
  padding-top: 70px;
  padding-bottom: 70px;
  margin-bottom: 20px;
  overflow: visible; // Ensure arrows outside the container are visible
}

// Category filter styles
.blog-category-filter {
  margin-bottom: 5px;
  position: relative;

  select {
    width: 100%;
    padding: 10px;
    border: 1px solid @color-gray-light;
    background-color: @color-light;
    color: @color-dark;
    border-radius: 2px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    padding-right: 30px; // Space for the chevron
  }

  &:after {
    content: '\f078'; // fa-chevron-down
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: @color-dark;
  }
}

// Mobile responsiveness
@media @max-tablet-p {
  .blog-posts-list {
    height: 300px !important;
  }

  .blog-swiper-container {
    .swiper-button-next,
    .swiper-button-prev {
      width: 25px;
      height: 25px;
    }
  }
}

/* Custom Blog Slider Styles */
.blog-custom-slider-wrapper {

  position: relative;
  margin: 30px auto;
  max-width: 90%;
  padding: 0 40px;
  overflow: visible;

  .blog-custom-slider {
    overflow: visible;
    padding: 0;

	&::before{
		content: "NEWS";
		position: absolute;
		top: 5%;
		left: 20%;
		-webkit-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);
		font-size: clamp(140px, 32.16374vw - 189.67836px, 300px);
		font-weight: 700;
		font-family: 'Lora';
		color: rgba(239, 62, 52, 0.1);
		z-index: 0;
		white-space: nowrap;
		pointer-events: none;
		letter-spacing: 5px;
	}

	&::after{
		content: "FEED";
		position: absolute;
		bottom: -45%;
		right: -10%;
		-webkit-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);
		font-size: clamp(140px, 32.16374vw - 189.67836px, 300px);
		font-weight: 700;
		font-family: 'Lora';
		color: rgba(239, 62, 52, 0.1);
		z-index: 0;
		white-space: nowrap;
		pointer-events: none;
		letter-spacing: 5px;
	}
  }

  .swiper-slide {
    height: auto;
    transition: transform 0.3s ease;
    // &:hover {
    //   transform: translateY(-5px);
    // }
  }

  /* Slide layout styles */
  .blog-slide-small-left {

    width: 35%; /* Width for the first slide */

    .blog-entry {
		max-height: 350px;
		margin-top:50px;
		// height: 380px;
      border-radius: 15px;
    }
  }

  .blog-slide-large-center {
    width: 40%; /* Width for the middle slide */

    .blog-entry {
      height: 480px; /* Tallest slide */
      border-radius: 15px;
      overflow: hidden;
	  background-size: cover;
	  background-position: center;
	  background-repeat: no-repeat
    }
  }

  .blog-slide-stacked {
    width: 25%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 300px; /* Match the height of the tallest slide */
    justify-content: center; /* Center vertically */
    align-items: center;

    .blog-entry-small-top-right,
    .blog-entry-small-bottom-right {
      max-height: 150px;
      height: 150px;
      width: 100%;
      position: relative;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }

  /* Entry styles */
  .blog-entry {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    transition: transform 0.3s ease;

    &:hover {
	   transform: scale(1.02);
    }

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.7) 100%);
      z-index: 1;
    }

    /* Category label styling */
	p{
		padding-bottom: 5px;
		.blog-category-label {
		//   position: absolute;
		//   top: 15px;
		//   left: 15px;
		background-color: rgba(255, 255, 255, 0.9);
		padding: 5px 10px;
		border-radius: 4px;
		font-size: 12px;
		font-weight: 600;
		color: #333;
		text-transform: uppercase;
		z-index: 3;
		}
	}

    /* Content styling */
    .blog-content {
      padding: 20px 15px 15px;
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;

      h4 {
        margin: 0 0 10px 0;
        font-size: 18px;
        line-height: 1.3;

        a {
          color: white;
          text-decoration: none;
          text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
          transition: color 0.2s ease;

          &:hover {
            color: @color-theme1;
          }
        }
      }

      .blog-description {
        font-size: 14px;
        line-height: 1.4;
        margin: 0 0 15px 0;
        color: rgba(255,255,255,0.9);
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
      }

      .blog-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;

        // .read-more-btn {
        //   display: inline-block;
        //   background-color: @color-theme1;
        //   color: white;
        //   padding: 6px 12px;
        //   border-radius: 4px;
        //   text-decoration: none;
        //   font-size: 12px;
        //   font-weight: 600;
        //   text-transform: uppercase;
        //   transition: background-color 0.3s ease;

        //   &:hover {
        //     background-color: darken(@color-theme1, 10%);
        //   }
        // }

        .read-time {
          font-size: 12px;
          color: rgba(255,255,255,0.8);
         text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
        }
      }
    }

    /* Remove blogPadding styles since we're using overlay */

    h4 {
      margin: 5px 0;
      font-size: 18px;
      line-height: 1.3;

      a {
        color: @color-dark;
        text-decoration: none;
        transition: color 0.2s ease;

        &:hover {
          color: @color-theme1;
        }
      }
    }

    .blog-header {
      display: flex;
      flex-wrap: wrap;
      font-size: 12px;
      color: @color-gray;
      margin-bottom: 5px;
    }

    .blog-description {
      font-size: 14px;
      line-height: 1.4;
      margin: 0 0 10px 0;
      flex-grow: 1;
      overflow: hidden;
    }

    .blog-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: auto;

    //   .read-more-btn {
    //     display: inline-block;
    //     background-color: @color-theme1;
    //     color: white;
    //     padding: 6px 12px;
    //     border-radius: 4px;
    //     text-decoration: none;
    //     font-size: 12px;
    //     font-weight: 600;
    //     text-transform: uppercase;
    //     transition: background-color 0.3s ease;

    //     &:hover {
    //       background-color: darken(@color-theme1, 10%);
    //     }
    //   }

      .read-time {
        font-size: 12px;
        color: #777;
      }
    }
  }

  /* Entry variations */
  .blog-entry-small-left,
  .blog-entry-small-top-right,
  .blog-entry-small-bottom-right {
    .blog-description {
      display: none;
    }

    .blog-content {
      padding: 15px 12px 12px;
    }

    h4 {
      font-size: 16px;
      margin: 0 0 5px;
    }

    .blog-footer {
      margin-top: 5px;
    }
  }

  /* Specific adjustments for stacked entries */
  .blog-slide-stacked {
    .blog-entry-small-top-right,
    .blog-entry-small-bottom-right {
      height: 150px;

      .blog-content {
        padding: 12px 10px 10px;
      }

      h4 {
        font-size: 14px;
        margin-bottom: 3px;
      }

      .blog-footer {
        margin-top: 0;

        // .read-more-btn {
        //   padding: 4px 8px;
        //   font-size: 10px;
        // }

        .read-time {
          font-size: 10px;
        }
      }
    }
  }

  .blog-entry-large-center {
    height: 450px;

    .blog-content {
      padding: 25px 18px 18px;
    }

    h4 {
      font-size: 22px;
      margin-bottom: 12px;
    }

    .blog-description {
      font-size: 15px;
      display: block !important;
      max-height: 120px;
      overflow: hidden;
    }

    .blog-footer {
      margin-top: 15px;

    //   .read-more-btn {
    //     padding: 8px 15px;
    //     font-size: 14px;
    //   }

      .read-time {
        font-size: 13px;
      }
    }
  }

  /* Navigation */
  .blog-swiper-button-next,
  .blog-swiper-button-prev {
    position: absolute;
    top: 50%;
    // width: 40px;
    // height: 40px;
    margin-top: -20px;
    z-index: 100; /* Increased z-index to ensure visibility */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: @color-light;
    // background-color: @color-theme1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

	//
	width: 60px;
    height: 60px;
    background-color: @color-coral-red;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    margin: 0;
    box-shadow: none;
    transition: all 0.3s ease;
    z-index: 100;
    cursor: pointer;
    overflow: hidden;

    // Hide default swiper button content
    &:after {
      content: none;
    }
	//

    /* Add arrow content */
    &:before {
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      font-size: 18px;
      line-height: 1;
    }

    &:hover {
      background-color: @color-theme2;
      transform: scale(1.1);
    }

	 // Disabled state
	&.swiper-button-disabled {
		opacity: 0.6;
		cursor: not-allowed;
		pointer-events: none;
		background-color: rgba(128, 128, 128, 0.5);
	}
  }

  .blog-swiper-button-prev {
    left: 10px;

    &:before {
      content: '\f104'; /* fa-angle-left */
    }
  }

  .blog-swiper-button-next {
    right: 10px;

    &:before {
      content: '\f105'; /* fa-angle-right */
    }
  }

  .blog-swiper-pagination {
    position: relative;
    margin-top: 20px;

    .swiper-pagination-bullet {
      width: 10px;
      height: 10px;
      background: @color-gray-light;
      opacity: 1;

      &-active {
        background: @color-theme1;
      }
    }
  }

  /* Responsive styles */
  @media (max-width: 992px) {
    .blog-slide-small-left,
    .blog-slide-large-center,
    .blog-slide-stacked {
      width: 33.333%;
    }

	.blog-entry {
		height: 320px !important;
	}

    .blog-entry-large-center {
      h4 {
        font-size: 20px;
      }
    }
  }

//   @media (max-width: 768px) {
	@media (max-width: 900px) {
    .blog-slide-small-left,
    .blog-slide-large-center,
    .blog-slide-stacked {
      width: 50%;
    }

    .blog-entry {
      height: 320px !important;
    }

    .blog-entry-large-center {
      .blog-description {
        display: none;
      }
    }
  }

  @media (max-width: 480px) {
    .blog-slide-small-left,
    .blog-slide-large-center,
    .blog-slide-stacked {
      width: 100%;
    }

    .blog-entry {
      height: 350px !important;
    }

    .blog-entry-large-center {
      .blog-description {
        display: block;
      }
    }

    .blog-swiper-button-next,
    .blog-swiper-button-prev {
      width: 50px;
      height: 50px;
      margin-top: -15px;
    }
  }
}


.wide-panel .panel-text,
.wide-panel .container {
  max-width: 100% !important;
  width: 100% !important;
}

.wide-panel .blog-page {
  &:extend(.container-lg);
  max-width: 1200px;
  margin:0 auto;
}

// Blog Mobile Dropdown Styling
.blog-mobile-dropdown {
    background-color: @color-light;
    color: @color-dark;
    display: none;
    padding: 15px 0px;

    // @media @max-tablet-p {
	@media (max-width:600px){
        display: block;
    }

    .blog-search {
        position: relative;
        margin-bottom: 15px;

        input {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 1px solid @color-gray-light;
            border-radius: 4px;
            font-size: 14px;
            color: @color-dark;
            background-color: @color-light;
            transition: border-color 0.3s, box-shadow 0.3s;

            &:focus {
                outline: none;
                border-color: @color-theme2;
                box-shadow: 0 0 0 2px rgba(0, 58, 94, 0.1);
            }

            &::placeholder {
                color: @color-gray;
            }
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 14px;
            color: @color-theme2;
            font-size: 16px;
            pointer-events: none;
        }

        .search-results-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: @color-light;
            border: 1px solid @color-gray-light;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 100;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

            .search-result-item {
                padding: 10px 15px;
                cursor: pointer;
                border-bottom: 1px solid @color-gray-lighter;
                transition: background-color 0.2s;

                &:hover {
                    background-color: @color-gray-lighter;
                }

                &:last-child {
                    border-bottom: none;
                }
            }

            .no-results {
                padding: 10px 15px;
                color: @color-gray;
                font-style: italic;
            }
        }
    }

    select {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid @color-gray-light;
        border-radius: 4px;
        font-size: 14px;
        color: @color-dark;
        background-color: @color-light;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23003A5E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 15px center;
        background-size: 16px;
        cursor: pointer;
        transition: border-color 0.3s, box-shadow 0.3s;

        &:focus {
            outline: none;
            border-color: @color-theme2;
            box-shadow: 0 0 0 2px rgba(0, 58, 94, 0.1);
        }
    }
}

/* Custom Blog Slider Styles */
.blog-custom-slider-wrapper {
    max-width: 95%;
    margin: 30px auto;
    padding: 0 75px; /* Padding for arrows on desktop */
    position: relative;

	@media @max-notebook{
		max-width: 100%;
		padding: 0 60px;
		margin: 0 auto;
	}

    /* Specific fix for 1024px width */
    @media (max-width: 1024px) and (min-width: 993px) {
        max-width: 95%;
        padding: 0 60px;
    }

    .blog-custom-slider {
        overflow: hidden;
		gap: 10px;

        /* Fix for cut-off borders at 1024px */
        @media (max-width: 1024px) {
            max-width: 95%;
            margin: 0 auto;
        }

        /* Remove any potential right border or line */
        .swiper-slide {
            box-sizing: border-box;
            border-right: none !important;
            overflow: visible;
        }

        .blog-entry {
            border: 12px solid @color-gray-light;
            border-radius: 12px;
            overflow: hidden;
            position: relative;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }

            .blog-entry-content {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 95%;
                background: rgba(255, 255, 255, 0.9);
                padding: 15px;
            }
        }
    }

    /* Slide layout styles */
    .blog-slide-small-left {
        width: 40%;

        .blog-entry {
            height: 380px;
            max-height: 350px;
            margin-top: 50px;
        }

        /* Fix for 1024px width */
        @media (max-width: 1024px) {
            width: 38%;
        }
    }

    .blog-slide-large-center {
        width: 30%;

        .blog-entry {
            height: 480px;
        }

        /* Fix for 1024px width */
        @media (max-width: 1024px) {
            width: 32%;
        }
    }

    .blog-slide-stacked {
        width: 25%;
        height: 480px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 20px;

        .blog-entry {
            height: calc((100% - 20px) / 2);
            min-height: 0;
            margin: 0;
        }

        .blog-entry-small-top-right,
        .blog-entry-small-bottom-right {
            max-height: 150px;
            height: 150px;
        }

        /* Fix for 1024px width */
        @media (max-width: 1024px) {
            width: 24%;
            padding-right: 0;
            margin-right: 0;
        }
    }

    /* Navigation buttons positioning */
    .blog-swiper-button-prev {
        left: 5px;
    }

    .blog-swiper-button-next {
        right: 5px;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        padding: 0 60px;

        .blog-slide-small-left,
        .blog-slide-large-center,
        .blog-slide-stacked {
            width: 33.333%;
        }

        .blog-entry {
            height: 350px !important;
            max-height: none !important;
            margin-top: 0 !important;
        }

        .blog-slide-stacked {
            height: auto;
            padding-right: 0;

            .blog-entry-small-top-right,
            .blog-entry-small-bottom-right {
                height: 165px;
                max-height: 165px;
            }
        }
    }

    /* Tablet and mobile styles */
    @media (max-width: 768px) {
        padding: 0 0 60px 0; /* Remove side padding, add bottom padding for arrows */

        /* Reset all custom slide layouts for mobile */
        .blog-slide-small-left,
        .blog-slide-large-center,
        .blog-slide-stacked {
            width: 100% !important;
            height: auto !important;
            display: block !important;
            margin: 0 !important;
            padding: 0 !important;
            flex-direction: row !important;
            gap: 0 !important;
        }

        /* Make all entries the same size */
        .blog-entry,
        .blog-entry-small-top-right,
        .blog-entry-small-bottom-right {
            height: 350px !important;
            max-height: none !important;
            min-height: 350px !important;
            margin: 0 0 20px 0 !important;
            aspect-ratio: 16/9 !important;
            width: 100% !important;
			max-width: 375px !important;
			margin: 0 auto !important;
        }

        /* Convert stacked slides to regular slides */
        .blog-slide-stacked {
            .blog-entry-small-top-right,
            .blog-entry-small-bottom-right {
                /* Hide the second entry in stacked slides on mobile */
                &:nth-child(2) {
                    display: none !important;
                }
            }
        }

		.blog-swiper-button-prev,
        .blog-swiper-button-next {
            top: auto;
            bottom:-10px !important;
            transform: translateY(0);
        }
    }

    // @media (max-width: 1000px) {
		@media (max-width: 985px) {
        /* Move arrows to bottom center on smaller screens */
        .blog-swiper-button-prev,
        .blog-swiper-button-next {
            top: auto;
            bottom:0;
            transform: translateY(0);
        }

        .blog-swiper-button-prev {
            left: 47%;
            margin-left: -50px;
        }

        .blog-swiper-button-next {
            right: 47%;
            margin-right: -50px;
        }

		.swiper-slide{max-height: 425px !important;}
    }

    @media (max-width: 480px) {
        max-width: 100%;
        padding: 0 0 50px 0;
    }
}

//  staff swiper

@media all and (max-width: 768px) {
	.blog-entries-container {
		flex-direction: column;
		.blog-entry-la {
			flex: 0 0 100%;
		}
	}
}

// // swiper less for blog
//  staff swiper styles
.blog-entries-container {
    position: relative;
    overflow: hidden ;
    padding: 0 50px; // Add padding for arrows
    // padding: 0 150px; // Add padding for arrows
	display: flex;
	align-items: center;
	&.staff-listing{
		height:650px;
	}
	// justify-content: center;

    &.swiper-container {
        overflow: hidden;
    }

    .swiper-wrapper {
        display: flex;
    }

    .swiper-slide {
        flex-shrink: 0;
        width: calc(100% / 3); // Exactly 3 slides per view
		// width: calc(33.33%-20px);
        position: relative;
        box-sizing: border-box;
        margin: 10px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		// .staff-card{
		// 	min-width: 330px ;
		// }
    }

    // Navigation buttons - position them properly
    .swiper-button-prev,
    .swiper-button-next {
        background-color: #fff;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .swiper-button-prev {
        left: 0;
    }

    .swiper-button-next {
        right: 0;
    }

    // Media queries for responsiveness
    @media all and (max-width: 768px) {
        .swiper-slide {
            width: 100%;
        }
    }

	.staff-card{
		img{
			// min-height: 550px;
			object-fit: contain;
			// min-width: 380px;
		}
	}

}

// Fix navigation positioning for staff panel
.swiper-container-wrapper {
    position: relative;
    overflow: hidden; // Ensure no overflow

    .staff-listing.blog-entries-container {
        margin-bottom: 0;
        overflow: hidden; // Ensure no overflow

		.swiper-slide{
			margin: 0;
			width: calc(100% / 3); // Enforce exact width
			box-sizing: border-box;
		}
    }

    .swiper-navigation {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        margin: 0;
        z-index: 10;
        pointer-events: none; // This allows clicks to pass through to elements below

		.swiper-button-next,
		.swiper-button-prev {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			font-size: 40px;
			color: @color-theme2;
			pointer-events: auto; // This makes the buttons clickable
			background-color: #fff;
			border-radius: 50%;
			width: 40px;
			height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 2px 5px rgba(0,0,0,0.2);
			cursor: pointer;
		}

		.swiper-button-next {
			right: -50px; // Move further away from the slides
			// &:after {
			// 	.font-awesome(@unicode:"f061");
			// }
		}

		.swiper-button-prev {
			left: -50px; // Move further away from the slides
			// &:after {
			// 	.font-awesome(@unicode:"f060");
			// }
		}

		.swiper-button-disabled {
			opacity: 0.5;
		}
	}
}

// // Responsive adjustments
// @media @max-tablet-l {
//     .staff-listing.blog-entries-container {
//         padding: 0 40px;

//         &.swiper-initialized + .swiper-navigation {
//             .swiper-button-next,
//             .swiper-button-prev {
//                 font-size: 30px;
//             }

//             .swiper-button-next {
//                 right: 5px;
//             }

//             .swiper-button-prev {
//                 left: 5px;
//             }
//         }
//     }
// }

// @media all and (min-width: 769px) {
//     .blog-entries-container {
//         &.swiper-initialized + .swiper-navigation {
//             .swiper-button-next,
//             .swiper-button-prev {
//                 position: absolute;
//                 top: 50%;
//                 transform: translateY(-50%);
//             }
//         }
//     }
// }


// Responsive adjustments for staff listing
@media @max-tablet-l {
    .staff-listing.blog-entries-container {
        padding: 0 40px;
        position: relative; // Ensure relative positioning

    //     & + .swiper-navigation {
    //         position: absolute;
    //         top: 0;
    //         left: 0;
    //         right: 0;
    //         bottom: 0;
    //         height: 100%;

    //         .swiper-button-next,
    //         .swiper-button-prev {
    //             font-size: 30px;
    //             position: absolute;
    //             top: 50%;
    //             transform: translateY(-50%);
    //         }

    //         .swiper-button-next {
    //             right: 5px;
    //         }

    //         .swiper-button-prev {
    //             left: 5px;
    //         }
    //     }
    }
}

// // Ensure arrows are properly positioned on desktop
// @media all and (min-width: 769px) {
//     .staff-listing.blog-entries-container {
//         &.swiper-initialized + .swiper-navigation {
//             .swiper-button-next,
//             .swiper-button-prev {
//                 position: absolute;
//                 top: 50%;
//                 transform: translateY(-50%);
//                 bottom: auto; // Override any bottom positioning
//             }

//             .swiper-button-next {
//                 right: 5px;
//                 left: auto; // Override any left positioning
//             }

//             .swiper-button-prev {
//                 left: 5px;
//                 right: auto; // Override any right positioning
//             }
//         }
//     }
// }
// //

// // Fix navigation positioning for staff panel
// .swiper-container-wrapper {
//     position: relative;
//     overflow: hidden; // Ensure no overflow

//     .staff-listing.blog-entries-container {
//         margin-bottom: 0;
//         overflow: hidden; // Ensure no overflow
//         padding: 0 60px; // Add padding for arrows

//         .swiper-slide{
//             margin: 0;
//             width: calc(100% / 3); // Enforce exact width
//             box-sizing: border-box;
//         }
//     }

//     .swiper-navigation {
//         position: absolute;
//         top: 0;
//         left: 0;
//         right: 0;
//         height: 100%;
//         margin: 0;
//         z-index: 10;
//         pointer-events: none; // This allows clicks to pass through to elements below

//         .swiper-button-next,
//         .swiper-button-prev {
//             position: absolute;
//             top: 50%;
//             transform: translateY(-50%);
//             font-size: 40px;
//             color: @color-theme2;
//             pointer-events: auto; // This makes the buttons clickable
//             background-color: #fff;
//             border-radius: 50%;
//             width: 40px;
//             height: 40px;
//             display: flex;
//             align-items: center;
//             justify-content: center;
//             box-shadow: 0 2px 5px rgba(0,0,0,0.2);
//             cursor: pointer;
//         }

//         .swiper-button-next {
//             right: 10px; // Position closer to the edge
//             &:after {
//                 .font-awesome(@unicode:"f061");
//             }
//         }

//         .swiper-button-prev {
//             left: 10px; // Position closer to the edge
//             &:after {
//                 .font-awesome(@unicode:"f060");
//             }
//         }

//         .swiper-button-disabled {
//             opacity: 0.5;
//         }
//     }
// }

// Responsive adjustments
// @media @max-tablet-l {
//     .swiper-container-wrapper {
//         .swiper-navigation {
//             .swiper-button-next,
//             .swiper-button-prev {
//                 width: 35px;
//                 height: 35px;
//                 font-size: 30px;
//             }
//         }
//     }
// }

// @media @max-tablet-p {
//     .swiper-container-wrapper {
//         .swiper-navigation {
//             .swiper-button-next,
//             .swiper-button-prev {
//                 width: 30px;
//                 height: 30px;
//                 font-size: 24px;
//             }
//         }
//     }
// }

// // Fix navigation positioning for staff panel
// .swiper-container-wrapper {
//     position: relative;
//     overflow: hidden; // Ensure no overflow

//     .staff-listing.blog-entries-container {
//         margin-bottom: 0;
//         overflow: hidden; // Ensure no overflow
//         padding: 0 60px; // Add padding for arrows

//         .swiper-slide{
//             margin: 0;
//             width: calc(100% / 3); // Enforce exact width
//             box-sizing: border-box;
//         }
//     }

//     .swiper-navigation {
//         position: absolute;
//         top: 0;
//         left: 0;
//         right: 0;
//         height: 100%;
//         margin: 0;
//         z-index: 10;
//         pointer-events: none; // This allows clicks to pass through to elements below

//         .swiper-button-next,
//         .swiper-button-prev {
//             position: absolute;
//             top: 50%;
//             transform: translateY(-50%);
//             font-size: 24px;
//             color: @color-theme2;
//             pointer-events: auto; // This makes the buttons clickable
//             background-color: #fff;
//             border-radius: 50%;
//             width: 40px;
//             height: 40px;
//             display: flex;
//             align-items: center;
//             justify-content: center;
//             box-shadow: 0 2px 5px rgba(0,0,0,0.2);
//             cursor: pointer;
//             opacity: 1; // Ensure full opacity
//             visibility: visible; // Ensure visibility
//         }

//         .swiper-button-next {
//             right: 10px; // Position closer to the edge
//             &:after {
//                 .font-awesome(@unicode:"f061");
//                 display: block; // Ensure icon is displayed
//                 font-size: 18px; // Adjust size as needed
//             }
//         }

//         .swiper-button-prev {
//             left: 10px; // Position closer to the edge
//             &:after {
//                 .font-awesome(@unicode:"f060");
//                 display: block; // Ensure icon is displayed
//                 font-size: 18px; // Adjust size as needed
//             }
//         }

//         .swiper-button-disabled {
//             opacity: 0.5;
//             cursor: not-allowed;
//         }
//     }
// }

// // Responsive adjustments
// @media @max-tablet-l {
//     .swiper-container-wrapper {
//         .swiper-navigation {
//             .swiper-button-next,
//             .swiper-button-prev {
//                 width: 35px;
//                 height: 35px;
//                 font-size: 16px;
//             }
//         }
//     }
// }

// @media @max-tablet-p {
//     .swiper-container-wrapper {
//         .swiper-navigation {
//             .swiper-button-next,
//             .swiper-button-prev {
//                 width: 30px;
//                 height: 30px;
//                 font-size: 14px;
//             }
//         }
//     }
// }
// ////////////////////////

// Fix navigation positioning for staff panel
.swiper-container-wrapper {
    position: relative;
    overflow: hidden; // Ensure no overflow

    .staff-listing.blog-entries-container {
        margin-bottom: 0;
        overflow: hidden; // Ensure no overflow
        padding: 0 60px; // Add padding for arrows

        .swiper-slide{
            margin: 0;
            width: calc(100% / 3); // Enforce exact width for desktop
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
			// border:10px solid @color-gray-light;
			// border-radius: 5px;
        }
    }

    .swiper-navigation {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        margin: 0;
        z-index: 10;
        pointer-events: none; // This allows clicks to pass through to elements below

        .swiper-button-next,
        .swiper-button-prev {
		    position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            color: @color-theme2;
            pointer-events: auto; // This makes the buttons clickable
            background-color: #fff;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            cursor: pointer;
            opacity: 1; // Ensure full opacity
            visibility: visible; // Ensure visibility
        }

        .swiper-button-next {
            right: 5px;
        }

        .swiper-button-prev {
            left: 5px;
        }

        .swiper-button-disabled {
            opacity: 0.5;
            cursor: not-allowed !important;
        }
    }
}

// Responsive adjustments
@media @max-tablet-l {
    .swiper-container-wrapper {
        .staff-listing.blog-entries-container {
            .swiper-slide {
                // width: calc(100% / 2 ) ; // 2 slides per view
				margin-left:10px;
				margin-right:10px;
				width: 100% !important;
            }

			.staff-card{
				transform:rotate(0deg) !important;
				&:hover{
					transform:rotate(0deg);
				}
			}
        }

        .swiper-navigation {
            .swiper-button-next,
            .swiper-button-prev {
                width: 45px;
                height: 45px;
                font-size: 16px;
            }
        }
    }
}

@media @max-tablet-p {
    .swiper-container-wrapper {
        .staff-listing.blog-entries-container {
            padding: 0 40px; // Reduce padding for smaller screens

            .swiper-slide {
                width: 100%; // 1 slide per view
            }
        }

        .swiper-navigation {
            .swiper-button-next,
            .swiper-button-prev {
                width: 45px;
                height: 45px;
                font-size: 14px;
            }
        }
    }
}

// Extra small devices
@media (max-width: 480px) {
    .swiper-container-wrapper {
        .staff-listing.blog-entries-container {
            padding: 0 30px; // Further reduce padding

            .swiper-slide {
                width: 100% !important; // Force 1 slide per view
                display: flex;
                justify-content: center;
                align-items: center;
            }

			.staff-card{
				transform:rotate(0deg) !important;
				&:hover{
					transform:rotate(0deg);
				}
			}
        }
    }
}

// .staff-card {
//     position: relative;
//     height: 340px;
//     width: 100%;
//     overflow: hidden;
//     border-radius: 8px;
//     box-shadow: 0 4px 8px rgba(0,0,0,0.1);
//     margin-bottom: 20px;
//     border:10px solid @color-gray-light;
// 	border-radius: 5px;

// 	img {
//         width: 100%;
//         height: 100%;
//         object-fit: cover;
//         position: absolute;
//         top: 0;
//         left: 0;
//     }

//     .staff-overlay {
//         position: absolute;
//         top: 0;
//         left: 0;
//         width: 100%;
//         height: 100%;
//         background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.6));
//         z-index: 1;
//     }

//     a {
//         display: block;
//         width: 100%;
//         height: 100%;
//     }

//     .staff-info-container {
//         position: absolute;
//         bottom: 0;
//         left: 0;
//         width: 100%;
//         padding: 20px;
//         z-index: 2;

//         .staff-position, .staff-name, .staff-email {
//             background-color: rgba(255,255,255,0.9);
//             margin-bottom: 5px;
//             padding: 5px 10px;
//             border-radius: 4px;

//             span {
//                 display: block;
//                 font-weight: 500;
//             }
//         }

//         .staff-name span {
//             font-weight: 700;
//             font-size: 1.2em;
//         }
//     }

//     &:hover .staff-overlay {
//         background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.7));
//     }
// }

// .staff-card {
// 	position: relative;
// 	height: 565px; // Adjusted to 500px;
// 	width: 100%;
// 	// min-width: 300px;
// 	overflow: hidden;
// 	border-radius: 8px;
// 	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
// 	margin-bottom: 20px;
// 	border: 10px solid #CCCCCC;
// 	border-radius: 5px;
//   }

//   .staff-card img {
// 	width: 100%;
// 	height: 100%;
// 	object-fit: cover;
// 	position: absolute;
// 	top: 0;
// 	left: 0;
//   }

//   .staff-card .staff-info-container {
// 	position: absolute;
// 	bottom: 0;
// 	left: 0;
// 	width: 100%;
// 	padding: 20px;
// 	z-index: 2;
//   }

//   .staff-card .staff-info-container .staff-position {
// 	background-color: rgba(255, 255, 255, 0.9);
// 	margin-bottom: 5px;
// 	padding: 5px 10px;
// 	border-radius: 4px;
//   }

//   .staff-card .staff-info-container .staff-name,
//   .staff-card .staff-info-container .staff-email {
// 	margin-bottom: 5px;
// 	padding: 5px 10px;
// 	border-radius: 4px;
//   }

//   .staff-card .staff-info-container .staff-position span,
//   .staff-card .staff-info-container .staff-name span,
//   .staff-card .staff-info-container .staff-email span {
// 	display: block;
// 	font-weight: 500;
//   }

//   .staff-card .staff-info-container .staff-name span,
//   .staff-card .staff-info-container .staff-email span {
// 	color: #FFFFFF;
// 	text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
//   }

//   .staff-card .staff-info-container .staff-name span {
// 	.bold();
// 	font-size: 1.2em;
//   }

.staff-card {
	position: relative;
	height: 565px; // Adjusted to 500px;
	width: 100%; // Keeps the card the width of its container (.swiper-slide)
	// min-width: 300px; // Keep commented out - can conflict with Swiper calculations
	overflow: hidden;
	border-radius: 8px;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
	margin-bottom: 20px; // Adds space below the card *inside* the slide
	border: 10px solid @color-gray-light; // Assuming #CCCCCC is a constant color or variable
	border-radius: 5px;
	// Keep transition here for consistency with inline style from PHP
	transition: transform 0.3s ease;
	// PHP adds inline `transform: rotate(...)` on the card
	// If you use the @max-tablet-p media query override from the previous LESS,
	// it will override the inline transform with !important on small screens.

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute; // Positioned relative to .staff-card
        top: 0;
        left: 0;
    }

    .staff-info-container {
        position: absolute; // Positioned relative to .staff-card
        bottom: 0;
        left: 0;
        width: 100%; // 100% of .staff-card width
        padding: 20px;
        z-index: 2;

        .staff-position {
            background-color: rgba(255, 255, 255, 0.9); // Using RGBA directly
            margin-bottom: 5px;
            padding: 5px 10px;
            border-radius: 4px;

            span {
                display: block;
                font-weight: 500;
            }
        }

        .staff-name,
        .staff-email {
            // margin-bottom: 5px;
            padding: 5px 10px;
            border-radius: 4px;

            span {
                display: block;
                font-weight: 500; // Duplicated, can remove from here if covered above
                color: #FFFFFF; // Assuming #FFFFFF is a constant color or variable
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8); // Using RGBA directly
            }
        }

        // Styles specific to the name span
        .staff-name span {
            .bold(); // Using the .bold() mixin
            font-size: 1.2em;
             // color and text-shadow are inherited from the parent rule
        }

        // Styles specific to the email span (if any needed beyond parent rule)
        // .staff-email span {
        //    // Add specific styles here if needed
        // }
		.staff-email{
			padding-top:0;
			margin-top:0;
		}
    }
}

// image upload
/* Profile photo upload styling */
.photo-upload-area {
    margin-bottom: 20px;
}

.current-photo {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.profile-thumbnail {
    max-width: 150px;
    max-height: 150px;
    border-radius: 4px;
    margin-right: 15px;
}

.photo-actions {
    display: flex;
    flex-direction: column;
}

.no-photo-message {
    color: #666;
    font-style: italic;
    margin-bottom: 10px;
}

.upload-field {
    margin-top: 10px;
}

.field-help {
    font-size: 0.85em;
    color: #666;
    margin-top: 5px;
}

//
/* Profile photo delete button */
#delete-profile-photo-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.3s;
}

#delete-profile-photo-btn:hover {
    background-color: #c82333;
}

#delete-profile-photo-btn i {
    margin-right: 5px;
}

#delete-profile-photo-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Add a loading spinner if needed */
#delete-profile-photo-btn.loading:after {
    content: "";
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

//



#directory-search-bar{
	width:30%;
}
// --- Directory Controls (Search Bar & Login Prompt) ---

// Assume these are defined globally in your LESS variables file:
// @color-light: #ffffff; // Example for white
// @color-dark: #000000;   // Example for black
// You might also want a grey for placeholder or subtle borders
// @color-gray-light: #cccccc;
// @color-gray-dark: #666666;
// @color-accent-red: #c00000; // For the search icon and login link

.directory-controls-container {
    display: flex; // Allows search and login prompt to be side-by-side
    justify-content: space-between; // Pushes them to opposite ends
    align-items: center; // Vertically aligns them
    margin-bottom: 30px; // Space below the controls
    flex-wrap: wrap;
	@media @max-tablet-l {
		flex-direction: column;
		gap:30px;
		align-items: flex-start;
		margin-bottom:10px;

		.directory-search-input{
			width:100%l
		}
	}
}

.directory-table{
	tr{
		border: none;
		border-bottom: 1px solid black;
	}
	@media @max-tablet-l {
		&.responsive{
			border:none;
			.table-header{
				// display: inline-block;
				display: none;
			}

			td{
				border: none !important;
			}

		}
	}
}

.directory-search-form {
    flex-grow: 1; // Allows search form to take available space
    max-width: 450px; // But not get too wide
    // margin-bottom: 0; // Remove bottom margin if using flex container for spacing

    .search-input-container {
        position: relative;
        display: flex;
        align-items: center;

        .directory-search-input {
            flex-grow: 1;
            width: 100%;
            height: 45px;
            // Right padding needs space for both clear and search icons
            padding: 10px 75px 10px 15px;
            border: 1px solid @color-dark; // Black border
            border-radius: 5px;
            background-color: @color-light; // White background
            color: @color-dark; // Black text
            box-sizing: border-box;

            &::placeholder {
                color: @color-gray-dark;
            }
        }

        .clear-search-btn {
            position: absolute;
            right: 40px; // To the left of search icon
            top: 50%;
            transform: translateY(-50%);
            text-decoration: none;
            font-size: 1.5em;
            color: @color-gray-dark;
            cursor: pointer;
            line-height: 1;
            padding: 0 5px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                color: @color-dark;
            }
        }

        .search-icon-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            padding: 0;
            cursor: pointer;
            color: @color-theme2;
            font-size: 1.2em;
            line-height: 1;

            &:hover {
                color: darken(@color-theme2, 10%);
            }

            i {
                display: block;
            }
        }
    }
}

.directory-login-prompt {
    // Styles for the "Members login here..." text
    // Adjust alignment and spacing as needed if it wraps below search on small screens
    // For example, if search takes full width on mobile:
    // @media (max-width: 767px) {
    //    width: 100%;
    //    text-align: center;
    //    margin-top: 15px;
    // }

    p {
        margin: 0; // Remove default paragraph margins if desired
        color: @color-dark; // Black text
        small {
            font-size: 0.9em;
        }
        a {
            color: @color-theme2;
            text-decoration: underline;
            &:hover {
                color: darken(@color-theme2, 10%);
            }
        }
    }
}

.profile-header{
	align-items: center;
	justify-content: space-evenly;
	margin-bottom: 30px;
	padding-bottom: 20px;
	gap:10px;

	&.form-grid{
		border:none;
	}

	.profile-photo-column {
		flex:0.6;
		.profile-detail-photo{
			// max-width:300px;
			width:100%;
			// height:300px;
			object-fit: cover;
			border-radius: 0px;
			border:1px solid red;
			flex:1;
		}
	}

	.profile-info-cplumn{
		flex:1;
		border:1px solid black;
		min-width:500px ;
		max-width: 100%;
	}

		.profile-info-container{
			.flexbox(row wrap);
			background-color: @color-gray-lightest;
			border-radius:5px;

			.info-box-container{
				.info-container{
					.static-box{
						.flexbox(column nowrap);
						gap: 0px;
					}
				}
			}
		}
}

// --- Directory Profile Detail Styles ---

// // Assumed Global Variables
// // @color-light: #ffffff;
// // @color-dark: #000000;
// // @color-theme2: #c00000;
// // @color-gray-light: #cccccc;
// // @color-gray-dark: #666666;


// Main container for the header section
.profile-header { // This already has 'form-grid' but we'll override display for responsive header
    display: flex;          // Enable flexbox for side-by-side layout by default
    flex-direction: row;    // Photo left, Info right
    align-items: flex-start; // Align items to the top
    gap: 20px;              // Space between photo column and info column
    margin-bottom: 30px;
    padding-bottom: 20px;

    .profile-photo-column {
        flex-basis: 200px; // Desired width of the photo column on desktop
        flex-shrink: 0;    // Prevent it from shrinking
        // Styles for the image itself are inline or can be added here
        .profile-detail-photo { // Target the image if you want to style it specifically
            width: 100%; // Make image responsive within its column
            min-height:350px;
            // border-radius: 50%; // Circular photo
            border: 1px solid @color-gray-light;

        }
        .profile-detail-no-photo { // Style the "No Photo" placeholder
			width: 400px; // Match image size
			height: 450px;
			background-color: darken(@color-light, 10%);
			display: flex;
			align-items: center;
			justify-content: center;
			color: @color-gray-dark;
			border: 1px solid @color-gray-light;
			font-style: italic;
			background: url('../../images/pga-icon.jpg') no-repeat center center;
			background-size: contain;
			border-radius: 5px;
        }
    }

    .profile-info-column {
        flex-grow: 0.2;

        h2 { // The name
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 2.5em;
            font-weight: bold;
            color: @color-theme2; // Name in red
        }

        // Your nested info structure
        .profile-info-container {
            // This div wraps the two .info-box-container divs
            // You might want to make this a flex container too if needed
            // For now, we'll let the inner .info-box-container handle their layout
			padding:10px 30px;
			display: flex;
			// justify-content: ;


            .info-box-container {
                // This contains two .info-container, and then two .static-box
                // To get "Facility | Email" and "Classification | Website" in two columns:
                display: flex;
				flex:1;
                flex-direction: row;
                justify-content: space-between; // Puts space between the two .info-container
                gap: 20px; // Space between "Facility" and "Email" columns
                margin-bottom: 15px; // Space below each pair of info rows

                .info-container { // This div seems redundant if .info-box-container is already flex
                    // If .info-container holds two .static-box side-by-side
                    // display: flex; // Not needed if .info-box-container controls this
                    // flex-direction: column; // This might be what you intend for each side

                    .static-box {
                        // Each static box now contains <p><strong>Label:</strong></p><p>Value</p>
                        // If you want label and value on same line but next static box below:
                        // No specific flex needed here unless you want label and value side-by-side *within* static-box
                        flex-basis: calc(50% - 10px); // Makes each static box take roughly half width
						// border:1px solid red;
						margin-bottom:15px;
                        p {
                            margin-top: 0;
                            margin-bottom: 0px;
							padding-bottom:0px;
							margin-top:-3px;
                            &:first-child { // The <p> with <strong>
                                font-size: 0.9em;
                                color: @color-gray-dark;
                                margin-bottom: 2px;
                            }
                            // strong {
                            //     // No specific style needed if relying on browser default
                            // }
                            a {
                                color: @color-theme2;
                                text-decoration: none;
                                &:hover {
                                    text-decoration: underline;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Container for Social Icons and Badges
        > div:last-child { // Targeting the unnamed div directly after .profile-info-container
            margin-top: 20px;
            display: flex;
            justify-content: space-between; // Social left, badges right
            align-items: center;
            flex-wrap: wrap; // Allow wrapping
            gap: 15px;

            .social-icons { // Your existing class
                list-style: none;
                padding: 0;
                margin: 0;
                display: flex;
                gap: 15px;

                li {
                    a {
                        color: @color-theme2; // Red social icons
                        font-size: 1.8em; // Make icons larger
                        text-decoration: none;
                        &:hover {
                            color: darken(@color-theme2, 10%);
                        }
                        // For Font Awesome 5/6, ensure your HTML uses appropriate classes e.g. <span class="fab fa-facebook-f"></span>
                        // The current HTML uses fas fa-facebook-square, which might be an older version icon
                        // Update your HTML spans if needed e.g. to:
                        // <span class="fab fa-facebook-f"></span>
                        // <span class="fab fa-twitter"></span>
                        // <span class="fab fa-linkedin-in"></span>
                        // <span class="fab fa-instagram"></span>
                    }
                }
            }

            // Assuming the badges are in a div sibling to ul.social-icons
            > div { // Targeting the div holding the badges
                display: flex;
                gap: 10px;
                align-items: center;
                img { // Target the badge images
                    height: 50px; // Adjust badge size
                    width: auto;
                }
            }
        }
    }

	.social-icons-container{
		margin-top: 20px;
		display: flex;
		justify-content: space-between; // Social left, badges right
		align-items: center;
		flex-wrap: wrap; // Allow wrapping
		gap: 15px;
	}

	.back-button-container{
		margin-top: 30px;
	}
}

// --- Responsive Adjustments for Mobile ---
@media @max-tablet-l {
    .profile-header {
        flex-direction: column; // Stack photo and info
        align-items: center;    // Center items when stacked

        .profile-photo-column {
            // flex-basis: auto; // Let it size naturally
        	// object-fit: contain;     // Or a percentage like 60%
            // margin-bottom: 20px;
			.profile-detail-photo{
				//  max-width: 100%;
			// min-height: 200px;
			// max-height: 100px;

			}
        }

        .profile-info-column {
            width: 100%;        // Info column takes full width
            text-align: center; // Center the text content

            h2 { // Name
                font-size: 2em;
            }

            .profile-info-container {
				flex-direction: column;
                .info-box-container {
                    flex-direction: column; // Stack the "Facility/Classification" and "Email/Website" blocks
                    align-items: flex-start;    // Center these blocks

                    .info-container {
                        // This structure might need rethinking for mobile if each static-box should be full width
                        // For now, let's make static-box take full width
                         .static-box {
                             flex-basis: 100%;
                             margin-bottom: 10px;
                             text-align: center; // Or left, depending on preference
                              p { text-align: left; } // Center paragraphs within
                              p:first-child { // Label
                                // display:block; // Already block
                              }
                         }
                    }
                }
            }

            > div:last-child { // The social + badges container
                flex-direction: column; // Stack social and badges
                align-items: center;

                .social-icons-container, // If you add this class around ul.social-icons
                .social-icons { // Target ul.social-icons directly
                    margin-bottom: 15px;
                    justify-content: center; // Center the icons themselves if they are flex
                }
                // Badges container will naturally be below and centered
            }
        }
    }
}

// --- Directory/Facility A-Z Filter Bar ---
.az-filter-bar {
    margin-bottom: 25px; // Space below the filter bar
    padding-bottom: 15px; // Space if there's a border
    border-bottom: 1px solid lighten(@color-dark, 70%); // A light grey border using dark

    .az-filter-list {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;

        li {
            margin: 0; // Remove default li margin

            .az-filter-letter {
                display: inline-block;
                padding: 8px 12px;
                min-width: 30px;
                text-align: center;
                text-decoration: none;
                font-weight: bold;
                border: 1px solid lighten(@color-dark, 60%);
                background-color: @color-light;
                color: @color-dark;
                border-radius: 3px;
                transition: background-color 0.2s ease, color 0.2s ease;

                &:hover {
                    background-color: darken(@color-light, 10%);
                    color: @color-theme2;
                }

                &.active {
                    background-color: @color-theme2;
                    color: @color-light;
                    border-color: @color-theme2;
                }
            }
        }
    }
}

// --- Adjust Search Bar ID/Class if different for facilities ---
// If you used .facility-search-form and .facility-search-input:
.facility-search-form { // Or #facility-search-bar
    // ... (keep your existing search bar styles from previous response,
    //      just ensure they target the correct class/ID for the facility search)
    // e.g.:
    // max-width: 450px;
    // margin-bottom: 20px;
    // .search-input-container { ... }
}

// --- Adjust Table Class if different for facilities ---
// If you used .facilities-table:
.facilities-table { // Or specific ID
    // ... (keep your existing table styles from previous response,
    //      ensure they target the correct class/ID for the facility table)
    // e.g.:
    // width: 100%;
    // border-collapse: collapse;
    // thead tr { border-bottom: 2px solid @color-dark; }
    // tbody tr { border-bottom: 1px solid lighten(@color-dark, 70%); }
    // th, td { padding: 12px 8px; color: @color-dark; }
}

// Visually hidden class (if not already defined globally)
.visually-hidden {
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

// // General .clearfix if not already globally defined (less needed with flexbox)
// .clearfix::after {
//     content: "";
//     clear: both;
//     display: table;
// }


// --- Pagination Styles ---
.pagination-container {
    margin-top: 30px;
    display: flex;
    justify-content: space-between; // Puts summary on left, links on right
    align-items: center;
    flex-wrap: wrap; // Allow wrapping on small screens
    padding: 10px 0;
    border-top: 1px solid lighten(@color-dark, 70%); // Light separator line

    .pagination-summary {
        font-size: 0.9em;
        color: @color-gray-dark;
        margin-right: 20px; // Space between summary and links
        margin-bottom: 10px; // If it wraps
    }

    .pagination-list {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-wrap: wrap; // Allow page numbers to wrap
        gap: 5px; // Space between page number "buttons"

        li {
            margin: 0;

            span, a {
                display: inline-block;
                padding: 8px 12px;
                min-width: 35px; // Ensures a decent tap target
                text-align: center;
                text-decoration: none;
                border: 1px solid lighten(@color-dark, 60%);
                background-color: @color-light;
                color: @color-dark;
                border-radius: 3px;
                line-height: 1.2; // For vertical centering of text
                font-size: 0.9em;
            }

            a {
                cursor: pointer;
                transition: background-color 0.2s ease, color 0.2s ease;

                &:hover {
                    background-color: darken(@color-light, 10%);
                    color: @color-theme2;
                    border-color: darken(lighten(@color-dark, 60%), 10%);
                }
            }

            &.active {
                span {
                    background-color: @color-theme2;
                    color: @color-light;
                    border-color: @color-theme2;
                    font-weight: bold;
                }
            }

            &.disabled {
                span {
                    color: @color-gray-light;
                    background-color: darken(@color-light, 5%);
                    border-color: lighten(@color-dark, 70%);
                    cursor: default;
                }
            }
        }
    }
}

// Responsive adjustments for pagination if needed
@media (max-width: 576px) {
    .pagination-container {
        flex-direction: column; // Stack summary and links
        align-items: center;

        .pagination-summary {
            margin-right: 0;
            margin-bottom: 15px;
        }
        .pagination-list {
            justify-content: center; // Center page numbers
        }
    }
}

// --- Facility Detail Page Styles (Light Theme) ---

// Your Provided Color Variables (Assumed to be defined globally)
// @color-light: #FFFFFF;
// @color-dark-navy-blue: #16212F; // Will be used for accents on a light theme
// @color-coral-red: #EF3E34;
// @color-gray-lightest: #EEEEEE; // For very light backgrounds or borders
// @color-gray-lighter: #DDDDDD;
// @color-gray-light: #CCCCCC;
// @color-gray: #999999;
// @color-gray-dark: #666666;
// @color-gray-darker: #333333;
// @color-dark: #000000;          // Primary text color
// @color-theme4: @color-lime;    // For links, or choose another accent

// // Your Provided Media Query Variables
// @tablet-l: ~"all and (min-width: 769px)";
// @max-tablet-l: ~"all and (max-width: 768px)";
// // ... (other media query vars if needed) ...


.facility-detail-page-wrapper {
    background-color: @color-light; // Main page background is white
    color: @color-dark;             // Default text color is black
    padding: 20px 0;

    .facility-detail-header-banner {
        position: relative;
        background-color: @color-gray-lightest; // Light grey fallback if no image
        background-size: cover;
        background-position: center;
        min-height: 200px;
        margin-bottom: 30px;
        // Optional: Add a subtle border or shadow if needed on white bg
        // border-bottom: 1px solid @color-gray-lighter;

        .facility-banner-hero-image {
            display: block;
            width: 100%;
            max-height: 400px;
            object-fit: cover;
        }
        .facility-banner-hero-placeholder {
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: @color-gray;
            font-style: italic;
        }
        // Title overlay might need dark text if banner is light
        // .facility-title-overlay {
        //     h1 { color: @color-dark; text-shadow: 1px 1px 2px @color-light; }
        //     .facility-location { color: @color-gray-dark; }
        // }
    }

    .facility-detail-main-layout {
        display: flex;
        flex-direction: column;
        align-items: center;

        @media @tablet-l {
            flex-direction: row;
            align-items: flex-start;
            gap: 30px;
        }

        .facility-detail-logo-wrapper {
            margin-bottom: 20px;
            width: 100%;
            max-width: 285px;
            text-align: center;
			// border:1px solid @color-gray-light;
			border-radius: 5px;
			padding:0px 10px;

            @media @tablet-l {
                flex-basis: 285px;
                flex-shrink: 0;
                margin-bottom: 0;
                text-align: left;
            }

            .facility-detail-logo-image {
                max-width: 100%;
                height: auto;
                border: 1px solid @color-gray-light; // Lighter border on white bg
                background-color: @color-light;      // Ensure bg is white if image has transparency
                padding: 5px;
            }
        }

        .facility-detail-content {
            width: 100%;
			padding: 5px 20px;
            @media @tablet-l {
                flex-grow: 1;
            }

            .facility-section-title, h4 { // For "Club Overview"
                font-size: 1.8em;
                color: @color-coral-red; // Keep red accent for titles
                margin-top: 0;
                margin-bottom: 15px;
                font-weight: bold;
            }

            .facility-description-content {
                line-height: 1.7;
                margin-bottom: 30px;
                color: @color-dark;
                p { padding-bottom: 5px; }
            }

            .facility-detail-sidebar-column {
                .facility-contact-box {
                    background-color: @color-gray-lightest;
					// max-width:450px;
                    padding: 20px;
                    border-radius: 5px;
                    color: @color-dark;

					@media @tablet-l {
						max-width: 450px;
					}

                    .facility-sidebar-title, h4 { // For "Contact Details"
                        font-size: 1.4em;
                        color: @color-dark;
                        margin-top: 0;
                        margin-bottom: 20px;
                        padding-bottom: 10px;
                        border-bottom: 1px solid @color-gray-light;
                    }
                    address {
                        font-style: normal;
                        line-height: 1.6;
                        margin-bottom: 15px;
                    }
                    p {
                        margin-bottom: 0px;
                        line-height: 1.5;
						padding-bottom:10px;
                        strong {
                            color: @color-dark;
                            margin-right: 5px;
                        }
                        a {
                            color: @color-theme4; // Link color (lime or your choice)
                            text-decoration: none;
                            &:hover {
                                text-decoration: underline;
                            }
                        }

						&.title{
							padding-bottom:0;
							font-size: 0.9em;
							color: #666666;
							margin-bottom: 2px;
						}
                    }
                }
            }
			.social-icons-container {
				margin-top: 20px;
				.social-icons {
					list-style: none;
					padding: 0;
					margin: 0;
					display: flex;
					gap: 15px;

					li a {
						color: @color-coral-red; // Red social icons
						font-size: 1.8em;
						text-decoration: none;
						&:hover {
							color: darken(@color-coral-red, 10%);
						}
					}
				}

				&.facility-social{
					margin-top: 30px;
				}
			}
        }
    }

    .facility-members-section {
        margin-top: 10px;
        padding-top: 30px;

        .facility-section-title, h4 {
            font-size: 1.8em;
            color: @color-dark;
            margin-bottom: 25px;
            text-align: center;
            @media @tablet-l {
                text-align: left;
            }
        }

        .members-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 45px;

            .member-card {
                background-color: @color-light;
                padding: 15px;
                border-radius: 4px;
                text-align: center;
                color: @color-dark;
                transition: box-shadow 0.2s ease-in-out;

                // &:hover {
                //     box-shadow: 0 4px 12px fade(@color-dark, 10%);
                // }

                a {
                    text-decoration: none;
                    color: inherit;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }

                .member-photo {
                    width: 220px;
                    height: 220px;
                    border-radius: 5px;
                    object-fit: cover;
                    margin-bottom: 10px;
                    // border: 2px solid @color-gray-light;
                }
                .member-no-photo {
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    background-color: @color-gray-lightest;
                    color: @color-gray-dark;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 10px;
                    font-size: 2em;
                    font-weight: bold;
                    border: 2px solid @color-gray-light;
                }

                .staff-info-box {
                    width: 100%;
                    .staff-info {
                        h5 { // Member name
                            margin: 10px 0 3px 0;
                            font-size: 1.1em;
                            color: @color-dark;
                            font-weight: bold;
                        }
                        .member-classification {
                            display: block;
                            font-size: 0.9em;
                            color: @color-gray-dark;
                        }
                    }
                    .location-scroll-arrow {
                        margin-top: 10px;
                        a {
                            color: @color-coral-red;
                            font-size: 1.2em;
                            // &:after{ content: '\f061'; font-family: "Font Awesome 5 Free"; font-weight: 900; margin-left: 5px;}
                        }
                    }
                }
            }
        }
    }

    .facility-detail-back-link {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid @color-gray-lighter;
        text-align: center;
        @media @tablet-l {
            text-align: left;
        }
        // .button.secondary.back-button styles come from global
    }
}

//  member resources page
// Member resources page
#member-resources {
	background: @color-light;
	color: @color-dark;
	padding: 20px;
}

#resource-search-wrapper {
	margin-bottom: 30px;
}

#search-bar {
	display: flex;
	gap: 0px;
	max-width: 350px;
	border: 1px solid @color-gray-lighter;
	border-radius: 4px;
	align-items: center;
	button.solid{
		padding:5px;
		margin:0;
	}
	a.fa{
		padding-right: 5px;
		padding-left: 0px;
	}
}

#search-bar .input {
	flex: 1;
	max-width: 400px;
	&.search-input{
		border: none;
	}
}

.resource-folder-card {
	background: @color-light;
	border: 1px solid @color-gray-lighter;
	border-radius: 8px;
	margin-bottom: 20px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.folder-header {
	padding: 20px;
	border-bottom: 1px solid @color-gray-lightest;
	display: flex;
	justify-content: space-between;
	align-items: center;
	cursor: pointer;
}

.folder-info h5 {
	margin: 0;
	color: @color-gray-darker;
	font-size: 18px;
}

.file-count {
	color: @color-gray-dark;
	margin-left: 10px;
}

.folder-actions {
	display: flex;
	gap: 10px;
	align-items: center;
}

.upload-btn {
	background: @color-success;
	color: @color-light;
	border: none;
	padding: 8px 16px;
	border-radius: 4px;
}

.folder-toggle {
	background: none;
	border: none;
	color: @color-gray-dark;
	font-size: 16px;
	cursor: pointer;
}

.folder-content {
	padding: 20px;
}

.files-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
	gap: 20px;
}

.file-card {
	border-bottom: 1px solid @color-gray-lighter;
	padding: 15px;
	display: flex;
	align-items: flex-start;
	gap: 15px;
	transition: box-shadow 0.2s;
}

.file-card:hover {
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.file-icon {
	font-size: 24px;
	color: @color-gray;
	flex-shrink: 0;
}

.file-info {
	flex: 1;
}

.file-info h6 {
	margin: 0 0 5px 0;
	font-size: 14px;
}

.file-info h6 a {
	color: @color-theme4;
	text-decoration: none;
}

.file-info h6 a:hover {
	text-decoration: underline;
}

.file-meta {
	color: @color-gray;
	font-size: 12px;
	line-height: 1.4;
}

.file-actions {
	flex-shrink: 0;
}

.delete-btn {
	background: @color-theme2;
	color: @color-light;
	border: none;
	padding: 6px 8px;
	border-radius: 3px;
	cursor: pointer;
	font-size: 12px;
}

.no-files, .no-resources {
	text-align: center;
	padding: 40px 20px;
	color: @color-gray;
}

.empty-state i {
	font-size: 48px;
	margin-bottom: 15px;
	color: @color-gray-lighter;
}

.upload-modal-content {
	padding: 20px;
}

.upload-info {
	margin-bottom: 20px;
	color: @color-gray;
}

.form-field {
	margin-bottom: 20px;
}

.form-field label {
	display: block;
	margin-bottom: 5px;
	font-weight: 500;
	color: @color-gray-darker;
}

.form-buttons {
	text-align: right;
	margin-top: 20px;
}

.input-file-container {
	position: relative;
}

.input-file {
	position: absolute;
	left: -9999px;
}

.input-file-trigger {
	display: block;
	padding: 10px 15px;
	background: @color-gray-lightest;
	border: 2px dashed @color-gray-lighter;
	border-radius: 4px;
	cursor: pointer;
	text-align: center;
	transition: all 0.2s;
}

.input-file-trigger:hover {
	background: @color-gray-lighter;
	border-color: @color-gray-light;
}

@media (max-width: 768px) {
	.files-grid {
		grid-template-columns: 1fr;
	}

	.folder-header {
		flex-direction: column;
		gap: 15px;
		align-items: stretch;
	}

	.folder-actions {
		justify-content: space-between;
	}

	.billing-profiles-list{
		tr,td{
			border:1px solid @color-gray-light;
		}
	}
}

.invoices-container {
	#search-bar.search-bar-invoices{
		border: 1px solid @color-dark;
		border-radius: 4px;
		max-width: 400px;
		.input{border: none;}
		.button.solid{
			padding-top:5px;
			position: static;
			margin-top:20px;
			margin-bottom:-10px;
		}
	}
	table{
		margin-top:40px;
	}
    .header-row {
        background-color: @color-gray-lightest;
    }
}

.invoice-details-container, .header-container, .hio-list,.cart-table {
	.header-row {
        background-color: @color-gray-lightest;
    }
	.dblock{
		display:block;
	}
}

.tournament-list{
	.header-row {
		background-color: @color-gray-lightest;
		border:1px solid @color-gray-lightest;
	}
	.dblock{
		display:block;
	}
	.red{
		border:2px solid @color-theme2;
		padding:10px;
		border-radius: 2px;

		&:hover{
			background:@color-theme2;
			color:@color-light;
		}
	}
	.tr-row{
		// border:1px solid black !important;
		border-bottom:1px solid @color-gray-light;
	}
}

.event-container{
	display:flex;
	flex-wrap:wrap;
	gap:180px;
	.event-table-container{
		flex:1;
	}
	.event-info-container{
		flex:0.25;
	}
}

.event-table{
	td{
		padding-top:5px;
		padding-top:5px;
		border-bottom:1px solid @color-gray-light;
	}
}

.amount-container{
	.amount-row{
		padding-left: 0;
		td {
			text-align: right;
		}
		td:even{
			background:@color-gray-lightest;
		}
		td:last-child {
			text-align: right;
		}
	}
}

.display-totals{
	border-bottom: 1px solid @color-gray-light;
}

.search-form-payment-refund-container{
	display: flex;
	gap:20px;
	align-items: center;
	@media (max-width: 768px) {
		flex-direction: column;
		gap:10px;
	}
	margin-bottom:5px;
	.content-tabs, .content-tabs li, .content-tabs ul{
		background:transparent;
		border:none;
		margin:0;
		padding:0;
	}

	// // .content-tabs li a{
	// #ud-id-1.ui-tabs-anchor{
	// 	border: none !important;
	// }
}

#payments{
	border-radius: 3px;
}

.billing-new{
	padding: 20px;
	padding-bottom: 10px;
	// margin-bottom: -25px;
	// &::before{
		// margin-top:1px;
	// }
}

.attendee-header{
	margin-top: 0px;
}

.terms-box{
	display:block;
	padding:20px;
	margin-bottom:30px;
	background:@color-gray-lightest;
	box-sizing:border-box;
	max-height:230px;
	overflow-y:scroll;
}

/*------ shopping cart ------*/
table.cart-table{
	tr td{
		border-top:1px solid @color-gray-light;
		 background:none;
		label{display:none;}
	}
	a.action{font-style:normal;}
	button.action{display:inline-block; border:0; padding:0; margin:0; color:@color-theme1; background:none; cursor:pointer; font-size:inherit; .trans(color);
		&:hover{color:@color-dark;}
	}
}

.withdraw-table{
	tr{
		border-bottom:1px solid @color-gray-lightest;
	}
}

table.reg-table{
	margin-bottom:10px;
	tr{
		border:1px solid @color-gray-light;
	}
	td {
		border:none;
		small{
		display:block;
		}
	}
}
div.promo-code{margin:20px 0;
	.input{background:#fff; width:190px; height:40px; padding:0 10px; margin:0;}
}


/*
 * Checkbox Fix for Payment Form
 * This fixes the issue with checkboxes not staying checked and not submitting values
 */

/* Payment page checkboxes */
.terms-conditions, #billing-profile{
	/* Make checkboxes accessible but visually hidden */
	.checkbox, .radio {
		/* Use accessible hiding that preserves functionality */
		position: absolute;
		opacity: 0; /* Don't use display:none as it breaks functionality */
		width: 1px;
		height: 1px;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border: 0;
		z-index: 1; /* Ensure it's clickable */
	}

	/* Style the custom checkbox appearance */
	.checkbox + label:before, .radio + label:before {
		font-family: FontAwesome;
		display: inline-block;
		font-size: 18px;
		font-style: normal;
		line-height: 18px;
		position: absolute;
		left: 0;
		top: 0;
		width: 18px;
		height: 18px;
		border: 1px solid #cccccc;
		text-align: center;
		color: @color-theme1;
		background: @color-light;
		content: "";
		pointer-events: none; /* Allow clicks to pass through to the hidden checkbox */
	}

	/* Style the checked state */
	.checkbox:checked + label:before {
		content: "\f00c"; /* FontAwesome checkmark */
	}

	.radio:checked + label:before {
		content: "\f111"; /* FontAwesome circle */
	}

	/* Prevent ghost checkmarks */
	.checkbox + label::after, .radio + label::after {
		content: none !important; /* Prevent any content from appearing after the label */
	}

	/* Ensure the payment form checkboxes work properly */
	#ccsave, #terms {
		/* These specific checkboxes need special handling */
		cursor: pointer;
		z-index: 2; /* Higher z-index to ensure clickability */
	}

	/* Make sure labels are clickable and stay on same line */
	label[for="ccsave"], label[for="terms"] {
		cursor: pointer;
		position: relative;
		display: inline-block;
		padding: 1px 10px 1px 26px;
		line-height: 20px !important;
		width: auto !important;
		margin-bottom: 5px;
		white-space: nowrap; /* Prevent text from wrapping */
	}

	/* Ensure small text inside labels also stays on same line */
	label[for="ccsave"] small, label[for="terms"] small {
		white-space: nowrap;
		display: inline;
	}
}

/* Login page checkbox styling */
#hio-form, #checkout-form, .login-bottom-info {
	/* Make checkbox accessible but visually hidden differently than payment checkboxes */
	.checkbox {
		opacity: 0;
		position: relative; /* Not absolute to prevent text shifting */
		margin: 0;
		width: auto;
		height: auto;
		cursor: pointer;
		z-index: 2;
	}

	/* Custom styling for login checkbox */
	.checkbox + label {
		cursor: pointer;
		display: inline-block;
		position: relative;
		padding-left: 26px;
		line-height: 20px;
		margin-bottom: 5px;
	}

	/* Style the custom checkbox appearance */
	.checkbox + label:before {
		font-family: FontAwesome;
		display: inline-block;
		font-size: 18px;
		font-style: normal;
		line-height: 18px;
		position: absolute;
		left: 0;
		top: 1px; /* Adjust top position to align with text */
		width: 18px;
		height: 18px;
		border: 1px solid #cccccc;
		text-align: center;
		color: @color-theme1;
		background: @color-light;
		content: "";
		pointer-events: none;
	}

	/* Style the checked state */
	.checkbox:checked + label:before {
		content: "\f00c"; /* FontAwesome checkmark */
	}

	/* Prevent ghost checkmarks */
	.checkbox + label::after {
		content: none !important;
	}

	/* Ensure small text inside labels stays on same line */
	label[for="reme"] small {
		white-space: nowrap;
		display: inline;
	}
}

.ccsave-container{
	margin-top: -25px;
	margin-left: 25px;
	margin-bottom:10px;
}

#hio-form{
	fieldset.hio-course{
		border-left:3px solid @color-theme2;
		padding-left: 10px;
	}

	.hio-headers{
		display:block; float:left; width:10%;
		label{
			display:block; height:70px; line-height:70px; margin:0;}
	}
	.hio-column{
		display:block; float:left; width:5%; text-align:center; box-sizing:border-box;
		label{
			display:block; height:70px; line-height:70px; margin:0;}
		div{
			padding:0 1px 1px 0; box-sizing:border-box;}
		input{

			display:block; margin:0; padding:0; text-align:center;}
	}
}


.ui-datepicker-header{
 	background:@color-theme2;
}

.hio-list{
	td{
		small{
			display:block;
		}
	}
}

#panel-67{
	.content-tabs{
	display: block !important;
	}
}

#search-bar{
	.search-icon-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            padding: 0;
            cursor: pointer;
            color: @color-theme2;
            font-size: 1.2em;
            line-height: 1;

            &:hover {
                color: darken(@color-theme2, 10%);
            }

            i {
                display: block;
            }
        }
}

#directory-search-bar.tournament-search-container{
	width:100%;
	display: flex;
	gap: 10px;
	@media (max-width: 768px) {
		flex-direction: column;
		gap: 5px;
	}
	select, .form-column{
		flex:1;
	}
	.form-column {
		position: relative;
		.button{
			position: absolute;
			right: -25px;
			top: 50%;
			transform: translateY(-50%);
		}
		.search-icon{
			position: absolute;
			right: 50px;
			top: 50%;
			transform: translateY(-50%);
		}
	}
}

#dialog-box{
	.cart-table{
		.header-row{
			background-color: @color-gray-lightest;
		}
		.dblock{
			display: block;
		}

	}
	// .totals{
	// 	tr{
	// 		display: flex;
	// 		justify-content: flex-end;
	// 		width: 100%;
	// 		border-bottom: 1px solid @color-gray-light;
	// 	}
	// 	td{
	// 		border:none;
	// 		width:110px;
	// 		text-align: right;
	// 	}
	// 	td:first-child{
	// 		width:120px;
	// 		text-align: right;
	// 	}
	// }
}

// for registration checkout
#billing-profile .form-grid .form-column{
	.input, select{
		margin-bottom:20px;
	}
}

.totals{
	tr{
		display: flex;
		justify-content: flex-end;
		width: 100%;
		border-bottom: 1px solid @color-gray-light;
	}
	td{
		border:none;
		width:110px;
		text-align: right;
	}
	td:first-child{
		width:120px;
		text-align: right;
	}
}

.partner-info{
	display:flex;
	flex-direction: column;
	.form-field-container{
		display:flex;
		flex-direction: column;
		.form-field{
			width:50%;

		}
		.clear{
			display:flex;
			justify-content: space-between;
			// gap:10px;
			.form-column{
				max-width:50%;

				&.f_left{
					input{margin-bottom: 10px;}
				}

				&.f_right{
					width: 300px!important;
				}
			}

			input{
				border:1px solid @color-gray-light;
			}
		}
	}
}
// .form-field-container{
// 	display:flex;
// }

#tournament-information{
	.report{
			padding-top:5px;
			padding-bottom:5px;
		a{
			.bold();
			color:@color-theme2;
		}
}

}
.sponsors{
	// border:1px solid green;
	// display:block;
	margin:0 0 40px -20px;
	padding:0;
	list-style:none;
	text-align:center;
	.sponsor{
		display:-moz-inline-box; -moz-box-orient:vertical; display:inline-block; vertical-align:top; width:325px; max-width:100%; margin:20px 0 0 20px;
		h4{margin-bottom:5px;}
		p{
			a{display:block;}
			small{margin-top:5px; line-height:18px;}
		}
		.img-holder{
			position:relative;
			display:table;
			margin-bottom:10px;
			width:100%;
			border:1px solid @color-gray-lightest ;box-sizing:border-box;
			.trans(border-color);
			div{
				position:relative;
				display:table-cell;
				padding:10px;
				height:180px;
				max-width:263px;
				vertical-align:middle;
				img{max-width:100%; max-height:100%;}
			}
		}
		a.img-holder:hover{border-color:@color-theme1;}
	}
	.sponsor > .sponsors{display:table; table-layout:fixed;}

}

@media (@tablet-p) {
.sponsors{
	margin-bottom:20px;
		.sponsor{width:280px;
			.img-holder div{height:120px;}
		}
	}
}
// }

.container:has(#registration_content) {
  --container-max-width: var(--container-width-lg);
//   --container-max-width: 1100px;
}

.sidebar-search input, .sidebar-year-selector select{
	border:2px solid @color-gray-light !important;
}

.reg-number{
	min-width:160px;
}

.event-info{
	// max-width:400px;
	display:flex;
	margin-top:10px;
	align-items: center;
	// justify-content: center;
	gap:25px;
	// align-self:flex-start;
	.event-name{
		max-width: 150px;
		text-align:left;
	}
	flex:0.3;
	// border:1px solid red;
}

.action-cell{
	padding-top:20px;
	flex:0.2;
	width:280px;
	// border:1px solid blue;
	// display: flex;
	// justify-content: center;
	// align-items: center;
	// flex-direction: row;
}

@media @max-tablet-l{
	.event-info{
		margin-left:20px;
		border:none;
		margin-top:0;
		padding:0;
	}
	.action-cell, .reg-number{
		border:none;
		padding-top:0;
		padding-bottom:0;
	}
}
@media @max-notebook{
	.action-cell{
		width:60px;
	}
}

.sub-navigation{
	margin-bottom: 0;
	.button{
		margin-right: 10px;
	}
	h4{
		font-size: 24px;
	}
	ul{
		border:none;
		padding:0;
		margin-left:0;
		list-style-type: none;
		li{
			// border:1px solid rgba(204,204,204) ;
			border:2px solid @color-gray-light !important;
			margin-bottom: 5px;
			padding:10px;
			font-size: 18px;
			padding-left:25px;
		}
		a:hover, a:focus{
			color:@color-theme2;
		}
		a{
			color:@color-theme1;
			text-align: center;
		}
	}
}

#account_navigation {
	width: 25%;
	box-sizing: border-box;
	padding-right: 20px;
}

#registration_content {
	// max-width: 65%;
	#nav-menu{
		@media @max-tablet-l{
			display: block;
		}
	}

	// @media @max-notebook{
	// 	max-width: 100%;
	// }

	@media @max-tablet-l{
		table, tr,td {
			border: none;
		}
		table tr{
			border: 1px solid @color-gray-light !important;
		}
	}

	.reg-number{
		padding-top:10px;
	}
}
.sidebar-search { margin-bottom: 15px; }
.sidebar-year-selector { margin-bottom: 20px; }
.sidebar-year-selector select { width: 100%; }
.search-input-wrap { position: relative; }
// .search-input-wrap .input { width: 100%; padding-right: 240px; }
.search-input-wrap .input { width: 100%;  }
.search-input-wrap .button { position: absolute; right: -20px; top: 50%; transform: translateY(-50%); }
.mobile-only { display: none; }
.content-area { padding: 0px; background: @color-light; }

/* Registration table styling */
#registration_content .registration-table { border-collapse: collapse; }
#registration_content .registration-row { transition: background-color 0.2s ease; }
#registration_content .registration-row:hover { background-color: rgba(0,0,0,0.03); }
#registration_content .reg-number { font-weight: 500; }
#registration_content .event-info { padding: 10px 0; }
#registration_content .event-name { font-size: 16px; font-weight: 600; margin-bottom: 5px; }
#registration_content .event-category { margin-bottom: 5px; }
#registration_content .category-badge { display: inline-block; padding: 3px 8px; border: 1px solid #e74c3c; border-radius: 3px; font-size: 12px; }
#registration_content .event-date { font-weight: 500; font-size: 14px; }
#registration_content .action-cell { position: relative; }
#registration_content .action-button { position: relative; overflow: hidden; }
#registration_content .edit-icon { display: inline-block; transition: opacity 0.3s ease; }
#registration_content .arrow-icon {
	position: absolute;
	top: 50%;
	left: 10%;
	opacity: 0;
	transition: opacity 0.3s ease, transform 0.3s ease;
}
#registration_content .registration-row:hover .edit-icon { opacity: 0; }
#registration_content .registration-row:hover .arrow-icon {
	opacity: 1;
	animation: slideInRight 0.3s forwards;
}

/* Download links styling */
#registration_content .download-link {
	display: block;
	position: relative;
	padding: 12px 15px;
	margin-bottom: 10px;
	border-radius: 4px;
	background-color: #f9f9f9;
	text-decoration: none;
	color: inherit;
	transition: background-color 0.2s ease;
}
#registration_content .download-link:hover {
	background-color: rgba(0,0,0,0.03);
}
#registration_content .download-link .fa-file-pdf-o {
	margin-right: 10px;
	color: #e74c3c;
}
#registration_content .download-link span {
	font-weight: 500;
}
#registration_content .download-link .arrow-icon {
	position: absolute;
	top: 50%;
	left: 10%;
	transform: translateY(-50%);
	opacity: 0;
	transition: opacity 0.3s ease, transform 0.3s ease;
}
#registration_content .download-link:hover .arrow-icon {
	opacity: 1;
	animation: slideInRight 0.3s forwards;
}

@keyframes slideInRight {
	from { transform: translate(100%, -50%); opacity: 0; }
	to { transform: translate(-50%, -50%); opacity: 1; }
}

@keyframes slideOutLeft {
	from { transform: translate(-50%, -50%); opacity: 1; }
	to { transform: translate(-100%, -50%); opacity: 0; }
}

@media (max-width: 768px) {
	.mobile-only { display: block; margin-bottom: 15px; }
	#account_navigation { display: none; }
	.registration-content { width: 100%; }
	#mobile-search-bar { margin-bottom: 15px; }
	.mobile-year-selector { margin-bottom: 15px; }
	#registration_content .event-name { font-size: 14px; }
	#registration_content .event-date { font-size: 13px; }
	#registration_content .category-badge { font-size: 11px; padding: 2px 6px; }
	#registration_content .action-cell a i{ opacity:1;}
}

@media (max-width: 890px) {
	.mobile-only { display: block; margin-bottom: 15px; }
	#account_navigation { display: none; }
	#registration_content {
		// width: 100%;
		#nav-menu{
			display:block;
		}
		.registration-content{
			width:100%;
		}
	}
	#mobile-search-bar { margin-bottom: 15px; }
}

.registration-info{
	tr{
		border-bottom:1px solid @color-gray-lightest;
	}
}

.red-border{
	border:1px solid @color-theme2;
	&:hover{
		background-color: @color-theme2;
		color:@color-light;
	}
}

a, a:visited {
  color: @font-color;
  text-decoration: none;
}