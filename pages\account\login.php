<div class="login-container">
	<div class="login-info">
		<p class="login-info-text">Please enter your username/email and password below to login to your account.
		<br>First-time users <a href="<?php echo $_sitepages['register']['page_url']; ?>" class="register">click here to create new account.</a></p>
	</div>

	<form name="login-form" id="login-form" action="" method="post">
		<div class="form-field">
			<label>Username or Email</label>
			<input id="user_login" class="input<?php echo (isset($required) && in_array('user_login', $required) ? ' error' : ''); ?>" type="text" name="user_login" value="<?php echo (isset($_POST['user_login']) ? $_POST['user_login'] : ""); ?>" />
		</div>
		<div class="form-field">
			<label>Password</label>
			<input id="user_password" class="input<?php echo (isset($required) && in_array('user_password', $required) ? ' error' : ''); ?>" type="password" name="user_password" value="" autocomplete="off" />
		</div>
		<div class="form-field login-bottom-info">
			<p>
				<input type="checkbox" class="checkbox" name="user_reme" id="reme" value="1"<?php echo (!empty($_COOKIE['auth']['reme_id']) ? ' checked' : ''); ?> /> 
				<label for="reme"><small>Keep me logged in</small></label>
				</p>
				<p><small><a href="#" data-open-hidden-modal="#forgot-password-modal">Forgot Password?</a></small></p> 
				<?php 
				// if(!($_sitepages['register']['showhide'] ?? true)){ 
				// 	echo '<small><a href="'.$_sitepages['register']['page_url'].'">'.$_sitepages['register']['name'].'</a></small>';
				// }
				?>
		</div>
		<div class="form-field button-wrapper">
			<button type="submit" name="login" class="button primary red" value="Login">
				Login
				<span class="top-border"></span>
				<span class="bottom-border"></span> <span class="left-border"></span>
				<span class="right-border"></span>
			</button>
		</div>
		<input type="hidden" name="xid" value="<?php echo $_COOKIE['xid']; ?>" />
	</form>

	<div class="hidden">
		<div id="forgot-password-modal" class="hidden-modal" title="Forgot Password" data-width="500">
			<?php include("pages/account/forgot.php"); ?>
		</div>
	</div>
</div>