<?php  

//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

if(isset($_POST['xid']) && $_POST['xid'] == $_COOKIE['xid']){
	$leadin_id = $_POST['id'] ?? NULL;
	
	if(!empty($leadin_id)){
		//get settings of leadin
		$db->query("SELECT `show_again_days` FROM `pages_leadins` WHERE `leadin_id` = ?", array($leadin_id));
		$show_again_days = $db->fetch_array()[0]['show_again_days'] ?? 0;
		$cookie_expires  = $show_again_days ? time()+60*60*24*$show_again_days : 0;

		//set cookie to do not show for this amount of time
		$_COOKIE['leadin_'.$leadin_id] = "false";
		$domain = (strpos($_SERVER['HTTP_HOST'], ':8888') === false) ? $_SERVER['HTTP_HOST'] : false;
		setcookie("leadin_".$leadin_id, "false", $cookie_expires, "/", $domain, false, true);

		echo $cookie_expires;
		exit();
	}
}

echo 'error';

?>