<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	//Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<a href="' .PAGE_URL. '?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';
	
	//Display records
	echo '<div class="panel">
		<div class="panel-header">' .$record_name. 's
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
			
				<thead>
					<th width="250px">Name</th>
					<th>Query String Label</th>
					<th>Short Code</th>
					<th data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				foreach($records_arr as $row){
					echo '<tr>
						<td>' .$row['name']. '</td>
						<td>' .$row['query_string_label']. '</td>
						<td>[' .$row['query_string_label']. ']</td>
						<td class="right"><a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-edit"></i> Edit</a></td>
					</tr>';
				}
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';


//Display form
}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">';

		//Label
		echo '<div class="panel">
			<div class="panel-header">Keyword Insert Details
				<span class="panel-toggle fa fa-chevron-up"></span>
			</div>
			<div class="panel-content">
				<div class="flex-container">
				
					<div class="form-field">
						<label>Name' .(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Name', '<p>The title/name of the keyword insert.</p><small>This only serves as a label for the keyword insert.</small>'). '</label>
						<input type="text" name="name" class="input' .(in_array('name', $required) ? ' required' : ''). '" value="' .($row['name'] ?? ''). '" />
					</div>
					
					<div class="form-field">
						<label>Query String Label' .(in_array('query_string_label', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Query String Label', 'The label of the query string (or GET variable). This label must be unique. Illegal characters found in the label will be automatically removed.<br/><br/>Example (bolded text): www.google.ca/?<strong>query</strong>=value'). '</label>
						<input type="text" name="query_string_label" class="input' .(in_array('query_string_label', $required) ? ' required' : ''). '" value="' .($row['query_string_label'] ?? ''). '" />
					</div>
				
					<div class="form-field">
						<label>Default Value' .(in_array('defaul_value', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Default Value', 'The default value that will replace the shortcode.'). '</label>
						<input type="text" name="default_value" class="input' .(in_array('default_value', $required) ? ' required' : ''). '" value="' .($row['default_value'] ?? ''). '" />
					</div>
				
				</div>
			</div>
		</div>'; //Label

		//Keyword Values
		echo '<div class="panel">
			<div class="panel-header">Keyword Insert Accepted Values
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">
				<div class="form-field full">
					<label>Enter accepted values separated with a pipe "|".' .(in_array('accepted_values', $required_fields) ? ' <span class="required">*</span>' : ''). '</label>
					<textarea name="accepted_values" class="textarea tagEditor full' .(in_array('accepted_values', $required) ? ' required' : ''). '">' .($row['accepted_values'] ?? ''). '</textarea>
				</div>
			</div>
		</div>'; //Keyword Values

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

}

?>