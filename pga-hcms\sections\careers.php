<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	//Search
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Search Postings
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";

		echo "<div class='panel-content clearfix'>";
			echo "<form id='advanced-search-form' class='clearfix' action='' method='get' enctype='multipart/form-data'>";
				echo "<div id='search-fields' class='column clearfix'>";
					echo "<div class='form-field'>
						<label>Search All</label>
						<input type='text' name='search' value='".$searchterm."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Start Date </label>
						<input type='text' name='start_date' value='".(isset($_SESSION['search_start_date'][SECTION_ID]) ? $_SESSION['search_start_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";

					echo "<div class='form-field'>
						<label>End Date </label>
						<input type='text' name='end_date' value='".(isset($_SESSION['search_end_date'][SECTION_ID]) ? $_SESSION['search_end_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";
				echo "</div>";

				echo "<div class='buttons-wrapper'>";
					echo "<div class='f_right'>";
						echo "<button type='submit' class='button'><i class='fa fa-search'></i>Search</button>";
					echo "</div>";
					echo "<button type='button' class='button reset' onclick='document.getElementById(\"clear-search-form\").submit();'><i class='fa fa-times'></i>Clear</button>";
				echo "</div>";
	
			echo "</form>";
			echo "<form id='clear-search-form' name='clear-search-form' class='hidden' action='".PAGE_URL."' method='post'>
				<input type='hidden' name='clear-search' value='Clear' />
				<input type='hidden' name='search' value='' />
				<input type='hidden' name='start_date' value='' />
				<input type='hidden' name='end_date' value='' />
				<input type='hidden' name='xssid' value='".$_COOKIE['xssid']."' />
			</form>";
		echo "</div>";
	echo "</div>";
	
	//Current jobs
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Current Postings  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";
		
			echo "<thead>";
			echo "<th width='auto'>Title</th>";
			echo "<th width='auto'>Location</th>";
			echo "<th width='auto'>Posted By</th>";
			echo "<th class='{sorter:\"monthDayYear\"}' width='100px'>Posted</th>";
			echo "<th class='{sorter:\"monthDayYear\"}' width='100px'>Closing</th>";
			echo "<th class='center' width='100px'>Approved</th>";
			echo "<th width='70px'>Visible</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_open as $row){
				
				//Display seo score
				if($cms_settings['enhanced_seo'] && (array_key_exists(3, $Account->roles) || array_key_exists(4, $Account->roles))){ //SEO and master permissions
					if($row['seo_score'] > 80){
						$seo_class = "seo-3";
					}else if($row['seo_score'] >= 50 && $row['seo_score'] <= 80){
						$seo_class = "seo-2";
					}else{
						$seo_class = "seo-1";
					}
					$seo_tooltip = "<span class='seo-tool tooltip' title='<h4>SEO Score: <strong>".number_format($row['seo_score'],1)."</strong></h4>'>&nbsp;</span>";
				}else{
					$seo_class = "";
					$seo_tooltip = "";
				}
				
				echo "<tr class='".$seo_class."'>";
					echo "<td>".$seo_tooltip.$row['title']. "</td>";
					echo "<td>".$row['facility_name']."</td>";
					echo "<td>".(!empty($row['posted_by']) ? $row['posted_by'] : $row['first_name'].' '.$row['last_name'].' <br><small>(Non-Member)</small>')."</td>";
					echo "<td>".date("M d, Y",strtotime($row['posted_date']))."</td>";
					echo "<td>".date("M d, Y",strtotime($row['closing_date']))."</td>";
					echo "<td class='center'>" .($row['approved'] ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
					echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager(50);
		
		echo "</div>";	
	echo "</div>";
	
	//Closed jobs
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Archived Postings
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";
		
			echo "<thead>";
			echo "<th width='auto'>Title</th>";
			echo "<th width='auto'>Location</th>";
			echo "<th width='auto'>Posted By</th>";
			echo "<th class='{sorter:\"monthDayYear\"}' width='100px'>Posted</th>";
			echo "<th class='{sorter:\"monthDayYear\"}' width='100px'>Closing</th>";
			echo "<th class='center' width='100px'>Approved</th>";
			echo "<th width='70px'>&nbsp;</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_closed as $row){
				
				//Display seo score
				if($cms_settings['enhanced_seo'] && (array_key_exists(3, $Account->roles) || array_key_exists(4, $Account->roles))){ //SEO and master permissions
					if($row['seo_score'] > 80){
						$seo_class = "seo-3";
					}else if($row['seo_score'] >= 50 && $row['seo_score'] <= 80){
						$seo_class = "seo-2";
					}else{
						$seo_class = "seo-1";
					}
					$seo_tooltip = "<span class='seo-tool tooltip' title='<h4>SEO Score: <strong>".number_format($row['seo_score'],1)."</strong></h4>'>&nbsp;</span>";
				}else{
					$seo_class = "";
					$seo_tooltip = "";
				}

				echo "<tr class='".$seo_class."'>";
					echo "<td>".$seo_tooltip.$row['title']. "</td>";
					echo "<td>".$row['facility_name']."</td>";
					echo "<td>".(!empty($row['posted_by']) ? $row['posted_by'] : $row['first_name'].' '.$row['last_name'].' <br><small>(Non-Member)</small>')."</td>";
					echo "<td>".date("M d, Y",strtotime($row['posted_date']))."</td>";
					echo "<td>".date("M d, Y",strtotime($row['closing_date']))."</td>";
					echo "<td class='center'>" .($row['approved'] ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
					echo "<td></td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager(50);
		
		echo "</div>";	
	echo "</div>";

} else {

	if(ACTION == 'edit') {
		$data = $records_arr[ITEM_ID];
		$file = $data['file_name'];	
		if(!isset($_POST['save'])){
			$row = $data;
		}

	} else if(ACTION == 'add' && !isset($_POST['save'])){
		$file = '';
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		// Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Job Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				<div class='panel-switch'>
					<label>Show $record_name</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " />
						<label for='showhide'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>
				<div class='panel-switch'>
					<label>Approve Job</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='approved' id='approved' value='1'" .(!isset($row['approved']) || (isset($row['approved']) && $row['approved']) ? "checked" : " "). " />
						<label for='approved'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Title <span class='required'>*</span></label>
					<input type='text' name='title' id='button-text' value='" .(isset($row['title']) ? $row['title'] : ''). "' class='input".(in_array('title', $required) ? " required" : "")."' />
				</div>";
				echo "<div class='form-field'>
					<label>Facility <span class='required'>*</span></label>
					<select name='facility_id' class='select".(in_array('facility_id', $required) ? " required" : "")."'>
						<option value=''>- Select -</option>";
						foreach($facilities as $facility){
							echo "<option value='".$facility['facility_id']."'".(isset($row['facility_id']) && $row['facility_id'] == $facility['facility_id'] ? " selected" : "").">".$facility['facility_name']."</option>";
						}
					echo "</select>
				</div>";
				echo "<div class='form-field'>
					<label>Category <span class='required'>*</span></label>
					<select name='category_id' class='select".(in_array('category_id', $required) ? " required" : "")."'>
						<option value=''>- Select -</option>";
						foreach($categories as $category){
							echo "<option value='".$category['category_id']."'".(isset($row['category_id']) && $row['category_id'] == $category['category_id'] ? " selected" : "").">".$category['category_name']."</option>";
						}
					echo "</select>
				</div>";
				echo "<div class='form-field'>
					<label>Posting Date <span class='required'>*</span>" .$CMSBuilder->tooltip('Posting Date', 'The <b>earliest</b> date applications can be receieved.'). "</label>
					<input type='text' name='posted_date' value='" .(isset($row['posted_date']) ? $row['posted_date'] : date("Y-m-d")). "' class='input datepicker".(in_array('posted_date', $required) ? " required" : "")."' readonly />
				</div>";
				echo "<div class='form-field'>
					<label>Closing Date <span class='required'>*</span>" .$CMSBuilder->tooltip('Closing Date', 'The <b>latest</b> date applications can be receieved.'). "</label>
					<input type='text' name='closing_date' value='" .(isset($row['closing_date']) ? $row['closing_date'] : ""). "' class='input datepicker".(in_array('closing_date', $required) ? " required" : "")."' readonly />
				</div>";
				echo "<div class='form-field'>
					<label>Availability <span class='required'>*</span>" .$CMSBuilder->tooltip('Availability', '<b>Public:</b><br>Everyone can apply and view the position.<br><br><b>Members Only: </b><br>Only registered PGA Members can view and apply to the position.'). "</label>
					<select name='public' class='select'>
						<option value='0'".(isset($row['public']) && $row['public'] == 0 ? " selected" : "").">Members Only</option>
						<option value='1'".(isset($row['public']) && $row['public'] == 1 ? " selected" : "").">Public</option>
					</select>
				</div>";
				echo "<div class='form-field'>
					<label>Monthly Salary Range <span class='required'>*</span></label>
					<input type='text' name='salary' value='" .(isset($row['salary']) ? $row['salary'] : ""). "' class='input".(in_array('salary', $required) ? " required" : "")."' />
				</div>";
				echo "<div class='form-field'>
					<label>Duration <span class='required'>*</span></label>
					<input type='text' name='duration' value='" .(isset($row['duration']) ? $row['duration'] : ""). "' class='input".(in_array('duration', $required) ? " required" : "")."' />
				</div>";
				
			echo "</div>";
		echo "</div>"; // END Details
	
		//Member classification
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Member Classification
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";	
				foreach($classes as $class){
					echo "<input type='checkbox' name='class_id[]' id='class".$class['class_id']."' class='checkbox' value='".$class['class_id']."'".(isset($row['class_id']) && in_array($class['class_id'], $row['class_id']) ? " checked" : "")." />
					<label for='class".$class['class_id']."'>".$class['class_name']."</label><br />";
				}
			echo "<br /></div>
		</div>";

		//Contact information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Contact Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>
				<div class='form-field'>
					<label for='first_name'>First Name<span class='required'>*</span></label>
					<input type='text' name='first_name' value='" .(isset($row['first_name']) ? $row['first_name'] : ""). "' class='input".(in_array('first_name', $required) ? " required" : "")."' />
				</div>

				<div class='form-field'>
					<label for='last_name'>Last Name<span class='required'>*</span></label>
					<input type='text' name='last_name' value='" .(isset($row['last_name']) ? $row['last_name'] : ""). "' class='input".(in_array('last_name', $required) ? " required" : "")."' />
				</div>

				<div class='form-field'>
					<label for='phone'>Phone<span class='required'>*</span></label>
					<input type='text' name='phone' value='" .(isset($row['phone']) ? $row['phone'] : ""). "' class='input".(in_array('phone', $required) ? " required" : "")."' />
				</div>

				<div class='form-field'>
					<label for='email'>Email<span class='required'>*</span></label>
					<input type='text' name='email' value='" .(isset($row['email']) ? $row['email'] : ""). "' class='input".(in_array('email', $required) ? " required" : "")."' />
				</div>
			</div>
		</div>"; //Contact information
	
		//Upload file
		echo "<div class='panel page-content" .(isset($row['type']) && $row['type'] == 1 ? " hidden" : ""). "'>";
			echo "<div class='panel-header'>PDF Document
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Upload File" .$CMSBuilder->tooltip('Upload File', 'File size must be smaller than 20MB.'). "</label>
					<input type='file' class='input" .(in_array('file', $required) ? ' required' : ''). "' name='file' value='' />
					<input type='hidden' name='old_file' value='" .(isset($file) && $file != '' && (file_exists($filedir.$file) || file_exists($tempdir.$file)) ? $file : ''). "' />
				</div>";
				if(isset($file) && $file != ''){
					echo "<p class='clear'>";
					
						//Public directory
						if(file_exists($filedir.$file)){
							echo "<a href='".$path.$filedir.$file."' target='_blank'><i class='fa fa-download'></i> Download Current File</a>";

						//Temp directory
						}else if(file_exists($tempdir.$file)){
							echo "<a href='".$root."download.php?file=".$file."&dir=temp' target='_blank'><i class='fa fa-download'></i> Download Current File</a>";
						}
					
						echo " &nbsp; <input type='checkbox' class='checkbox' name='deletefile' id='deletefile' value='1'>
						<label for='deletefile'>Delete Current File</label>";
					
					echo "</p>";
				}
			echo "</div>";
		echo "</div>"; //Upload file

		// Content
		echo "<div class='panel'>";
			echo "<div class='panel-header".(in_array('content', $required) ? " required" : "")."'>Job Description <span class='required'>*</span>
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
				echo "<div class='panel-content clearfix'>
					<textarea name='content' class='tinymceMini'>" .(isset($row['content']) ? $row['content'] : ""). "</textarea>
				</div>";
		echo "</div>"; // END Content
	
		//Applications
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Applications  
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";

				echo "<thead>";
					echo "<th width='400px'>Applicant Name</th>";
					echo "<th class='{sorter:\"monthDayYear\"}'>Application Date</th>";
					echo "<th class='{sorter:false}'>&nbsp;</th>";
				echo "</thead>";

				echo "<tbody>";
				if(!empty($row['applications'])){
				foreach($row['applications'] as $application){
					echo "<tr>";
						echo "<td>".$application['first_name'].' '.$application['last_name']."</td>";
						echo "<td>".date("F d, Y", strtotime($application['timestamp']))."</td>";
						echo "<td class='right'><a href='" .$sitemap['career']['page_url']. "?action=edit&item_id=" .$application['application_id']. "' class='button-sm'><i class='fa fa-eye'></i>View</a></td>";
					echo "</tr>";	
				}
				}
				echo "</tbody>";
				echo "</table>";
				
				//Pager
				// $CMSBuilder->tablesorter_pager(10);

			echo "</div>";	
		echo "</div>"; //Applications
	
	//SEO Content/Analysis
		include('includes/widgets/seotabs.php');

		//Sticky footer
		include("includes/widgets/formbuttons.php");

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "<input type='hidden' name='keep_tags[]' value='content' />";

	echo "</form>";

}

?>