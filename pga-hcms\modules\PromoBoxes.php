<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('promo_boxes');
	$CMSBuilder->set_widget($_cmssections['promo_boxes'], 'Total Promo Boxes', $total_records);
}

if(SECTION_ID == $_cmssections['promo_boxes']){

	//Define vars
	$record_db   = 'promo_boxes';
	$record_id   = 'promo_id';
	$record_name = 'Promo Box';
	$records_name = 'Promo Boxes';
	$default_icon = 'fas fa-link';
	
	//Validation
	$errors   = false;
	$required = [];
	$required_fields = ['title', 'icon'];

	//Image Uploader
	$imagedir     = '../images/promos/';
	$CMSUploader  = new CMSUploader('promo', $imagedir);

	//Filtering
	$where  = '';
	$params = [];
	$searchable_fields = ["$record_db.title"];

	//Selected item
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where = "WHERE $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
		
	//Build search query
	}else if($searchterm){
		foreach($searchable_fields as $key => $field){
			$searchable_fields[$key] = "$field LIKE ?";
			$params[] = '%'.$searchterm.'%';
		}

		$where .= ($where ? 'AND ' : 'WHERE ').'('.implode(' || ', $searchable_fields).')';
	}

	//Get Records
	$db->query("SELECT * FROM $record_db $where ORDER BY ordering", $params);
	$records_arr = $db->fetch_assoc($record_id);
	foreach($records_arr as $item_id => &$record){
		$record['image']      = check_file($record['image'], $imagedir);
		$record['full_image'] = $record['image'] ? $path.$imagedir.$record['image'] : false;

		$record['mini_image']      = check_file($record['mini_image'], $imagedir);
    	$record['full_mini_image'] = $record['mini_image'] ? $path.$imagedir.$record['mini_image'] : false;

		unset($record);
	}

	if($db->error()){
		$CMSBuilder->set_system_alert('Unable to retrieve data.', false);	
	}

	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){
			$row = $records_arr[ITEM_ID];
			
		//Not found	
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();	
		}
	}
	

	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()){
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);	
		}
		
		header("Location: " .PAGE_URL);
		exit();


	//Save item
	}else if(isset($_POST['save'])){
		
		//Set default values
		$_POST['showhide'] = !isset($_POST['showhide']);
		$_POST['icon'] = (($_POST['icon'] ?? '') ?: $default_icon);

		//Required fields
		foreach($required_fields as $field){
			if(($_POST[$field] ?? '') === ''){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}

		//Validate image
		if(empty($records_arr[ITEM_ID]['image']) && empty($_FILES['image']['name'])){
			$errors[] = 'Please upload an image.';
			$required[] = 'image';
		}
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large. Cannot exceed '.$_max_filesize['megabytes'].'.';
			$required[] = 'image';
		}

		//Add validation for mini_image (only check size if a file is uploaded)
		if(!empty($_FILES['mini_image']['size']) && $_FILES['mini_image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Mini Image filesize is too large. Cannot exceed '.$_max_filesize['megabytes'].'.';
			$required[] = 'mini_image';
		}

		if(!$errors){

			//Delete old images
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//For mini_image deletion
			if(isset($_POST['deletemini_image'])){
				$CMSUploader->bulk_delete(['mini_image' => $records_arr[ITEM_ID]['mini_image']]);
			}
			
			//Upload new images
			try{
				$images = $CMSUploader->bulk_upload($_POST['title'], $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$images = $CMSUploader->bulk_upload($_POST['title'], $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			//Insert to db
			$params = array(
				
				//Insert
				ITEM_ID, 
				$_POST['title'], 
				$_POST['description'], 
				$_POST['url'], 
				$_POST['url_target'], 
				$_POST['url_text'], 
				$_POST['showhide'], 
				$images['image'] ?? NULL, 
				$_POST['image_alt'], 
				$_POST['icon'],
				$images['mini_image'] ?? NULL,  // Add mini_image
				$_POST['mini_image_alt'],       // Add mini_image_alt

				//Update
				$_POST['title'], 
				$_POST['description'], 
				$_POST['url'], 
				$_POST['url_target'], 
				$_POST['url_text'], 
				$_POST['showhide'], 
				$images['image'] ?? NULL, 
				$_POST['image_alt'],
				$_POST['icon'],
				$images['mini_image'] ?? NULL,  // Add mini_image
				$_POST['mini_image_alt'],       // Add mini_image_alt
			);
			$db->query("INSERT INTO $record_db ($record_id, title, description, url, url_target, url_text, showhide, image, image_alt, icon, mini_image, mini_image_alt) VALUES (?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE title = ?, description = ?, url = ?, url_target = ?, url_text = ?, showhide = ?, image = ?, image_alt = ?, icon = ?, mini_image = ?, mini_image_alt = ?", $params);
			if(!$db->error()){
				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
					header("Location: " .PAGE_URL);
					exit();
				}
			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	//Handle images
	}else{
		include('modules/CropImages.php');
	}
}

?>