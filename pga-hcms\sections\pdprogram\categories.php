<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	include("includes/widgets/searchform.php");
	echo "<p class='f_right'><a href='" .PAGE_URL. "?action=add' class='button'><i class='fa fa-plus'></i>Add New</a></p>";
	
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Categories  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='stickyheader sortable'>";
		
			echo "<thead>";
			echo "<th width='10px' class='{sorter:false}'></th>";
			echo "<th width='0px' class='nopadding'></th>";
			echo "<th width='auto'>Name</th>";
			echo "<th width='auto'>Points</th>";
			echo "<th width='200px' class='center'>Automatic " .$CMSBuilder->tooltip('Automatic', 'Indicates whether points will be automatically awarded for this category (24 hours after completion of tournaments and events).'). "</th>";
			//echo "<th width='70px'>Visible</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				$records_arr[$row['parent_id']]['sub_items'] = (!isset($records_arr[$row['parent_id']]['sub_items']) ? array() : $records_arr[$row['parent_id']]['sub_items']);

				echo "<tr data-level='".$row['lvl']."' data-table='$record_db' data-column-name='$record_id' data-name='".$row['name']."' data-id='".$row[$record_id]."' class='".($row['parent_id'] != "" ? " push lvl".$row['lvl'] : "").(!empty($row['sub_items']) ? " has_sub" : "").($row['parent_id'] != "" && array_search($row[$record_id], array_keys($records_arr[$row['parent_id']]['sub_items'])) == (count($records_arr[$row['parent_id']]['sub_items'])-1) && empty($row['sub_items']) ? " last_child" : "")."' data-system-page='true'>";
					echo "<td class='handle'><span class='fa fa-arrows'></span></td>";
					echo "<td class='nopadding' width='0px'></td>";
					echo "<td class='show-lvl'>" .$row['name']. "</td>";
					echo "<td>" .$row['points']. " point" .($row['points'] == 1 ? "" : "s") .($row['unit'] != "" ? "/".$row['unit'] : ""). "</td>";
					echo "<td class='center'>" .($row['automatic'] ? "<i class='fa fa-check fa-lg color-theme1'></i>" : "<i class='fa fa-lg fa-close'></i>"). "</td>";
					//echo "<td>".$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide'])."</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-pencil'></i>Edit</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
		echo "</div>";	
	echo "</div>";

}else{

	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}

	}else if(ACTION == 'add' && !isset($_POST['save'])){	
		unset($row);
	}

	echo "<form action='' method='post' enctype='multipart/form-data'>";
	
		//Details
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Details
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>";
				/*echo "<div class='panel-switch'>
					<label>Show $record_name</label>
					<div class='onoffswitch'>
						<input type='checkbox' name='showhide' id='showhide' value='0'" .(isset($row['showhide']) && $row['showhide'] ? "" : " checked"). " />
						<label for='showhide'>
							<span class='inner'></span>
							<span class='switch'></span>
						</label>
					</div>
				</div>";*/
			echo "</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>Name <span class='required'>*</span></label>
					<input type='text' name='name' value='" .(isset($row['name']) ? $row['name'] : ''). "' class='input".(in_array('name', $required) ? " required" : "")."' />
				</div>";	
				if(ACTION == 'add' || (isset($row['parent_id']) && !is_null($row['parent_id']))){
					echo "<div class='form-field'>
						<label>Parent $record_name <span class='required'>*</span></label>
						<select name='parent_id' class='select" .(in_array('parent_id', $required) ? ' required' : ''). "'>
							<option value=''>- Select -</option>";
							foreach($records_arr as $parent) {
								if($parent[$record_id] != ITEM_ID && $parent['parent_id'] == ""){
									echo "<option value='".$parent[$record_id]."'".(isset($row['parent_id']) && $row['parent_id'] == $parent[$record_id] ? " selected" : "").">".$parent['name']."</option>";
								}
							}
						echo "</select>
					</div>";
				}
				echo "<div class='form-field'>
					<label>Numerical Order" .$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.'). "</label>
					<select name='ordering' class='select'>
						<option value='101'>Default</option>";
						for($i=1; $i<101; $i++){
							echo "<option value='" .$i. "' " .(isset($row['ordering']) && $row['ordering'] == $i ? "selected" : ""). ">" .$i. "</option>";	
						}
					echo "</select>
				</div>";
				echo "<div class='form-field auto-width'>
					<label>Points/Unit" .(isset($row['automatic']) && $row['automatic'] == true ? " <small>(Automatic)</small>" : ""). "</label>
					<input type='text' name='points' value='" .(isset($row['points']) ? $row['points'] : ''). "' class='input input_sm number".(in_array('points', $required) ? " required" : "")."' /> / 
					<input type='text' name='unit' value='" .(isset($row['unit']) ? $row['unit'] : ''). "' class='input input_sm".(in_array('unit', $required) ? " required" : "")."'" .(isset($row['automatic']) && $row['automatic'] == true ? " disabled" : ""). " />
				</div>";
				echo "<div class='form-field'>
					<label>Maximum Points " .$CMSBuilder->tooltip('Maximum Points', 'The maximum number of points that can be awarded for this category to a single person in a calendar year. Leave blank for no limit.'). "</label>
					<input type='text' name='max_points' value='" .(isset($row['max_points']) ? $row['max_points'] : ''). "' class='input input_sm number".(in_array('max_points', $required) ? " required" : "")."' />
				</div>";
			echo "</div>";
		echo "</div>";

		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
			if(ITEM_ID != ""){
				echo (!$row['deletable'] ? $CMSBuilder->tooltip('Delete Category', 'Due to the dynamic nature of this category, it cannot be deleted. If you wish to remove this category, set it to hidden instead.').'&nbsp;' : '');
				echo "<button type='button' name='delete' value='delete' class='button delete'" .(!$row['deletable'] ? " disabled" : ""). "><i class='fa fa-trash'></i>Delete</button>";
			}
			echo "<a href='" .PAGE_URL. "' class='cancel'>Cancel</a>";
		echo "</footer>";

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";

}

?>