<?php
/**
 * Profile Image Handler for frontend
 * Uses the core ImageUpload class for image processing
 */

// Include the core ImageUpload class
require_once($_SERVER['DOCUMENT_ROOT'] . (isset($path) ? $path : '/') . 'core/classes/ImageUpload.class.php');

// echo "DEBUGGING PATH VAR: $path";

/**
 * <PERSON>les profile image upload and processing
 * 
 * @param array $file The $_FILES['photo'] array
 * @param string $old_filename Previous filename to delete (if any)
 * @param int $account_id User's account ID for filename
 * @return array Result with status and filename or error message
 */
    function process_profile_image($file, $old_filename = null, $account_id = 0) {
        // --- PRIMARY VALIDATION: Request Method ---
        // Ensure this function ONLY processes data during a POST request.
        // This check should ideally be redundant if the calling script checks first,
        // but acts as a safeguard against incorrect usage or unexpected state persistence.
        error_log("process_profile_image: request method - ". $_SERVER['REQUEST_METHOD']);

        
        if (!isset($_SERVER['REQUEST_METHOD']) || $_SERVER['REQUEST_METHOD'] != 'POST') {
            $actual_method = $_SERVER['REQUEST_METHOD'] ?? 'Not Set';
            error_log("process_profile_image: CRITICAL - Attempted call on a non-POST request (" . $actual_method . "). Aborting.");
            // Return a standard error format
            return ['success' => false, 'message' => 'Invalid request context for file processing.', 'filename' => ''];
        }
        // --- END Request Method Check ---

        // Explicitly declare use of the global $path variable 
        global $path;
        if($_SERVER['REQUEST_METHOD'] == 'POST'){
            error_log("process_profile_image: Called from POST request.");
        } else {
            error_log("process_profile_image: Called from non-POST request.");
        }
        error_log("process_profile_image: Called from GET request.");
        error_log("process_profile_image: request method - ". $_SERVER['REQUEST_METHOD']);
        
        error_log("process_profile_image: Called with old_filename: " . $old_filename);
        error_log("process_profile_image: Called with account_id: " . $account_id);

        if($_REQUEST['REQUEST_METHOD'] == 'GET'){
            error_log("process_profile_image:stop here");

            $result['message'] = 'no file uplaod as GET request';
            return $result;
        }
        ///
        // --- FORCE CHECK REQUEST METHOD ---
        // Use $_SERVER['REQUEST_METHOD'] as $_REQUEST can contain GET/POST/COOKIE data
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            error_log("process_profile_image: CRITICAL - Called on a non-POST request (" . $_SERVER['REQUEST_METHOD'] . "). Aborting file processing.");
            // Return an error structure consistent with your function's output
            return ['success' => false, 'message' => 'Invalid request method for file processing.', 'filename' => ''];
        }
        // --- END FORCE CHECK ---

        error_log("process_profile_image: Called from POST request."); // This should now always be true if it proceeds
        error_log("process_profile_image: Called with old_filename: " . print_r($old_filename, true)); // Use print_r for null
        error_log("process_profile_image: Called with account_id: " . $account_id);
        ///

        // Define paths
        $upload_dir = 'images/users/';
        $thumbs_dir = $upload_dir . 'thumbs/';
        $full_upload_dir = $_SERVER['DOCUMENT_ROOT'] . (isset($path) ? $path : '/') . $upload_dir;
        $full_thumbs_dir = $_SERVER['DOCUMENT_ROOT'] . (isset($path) ? $path : '/') . $thumbs_dir;
        
        // Ensure directories exist
        if (!is_dir($full_upload_dir)) mkdir($full_upload_dir, 0755, true);
        if (!is_dir($full_thumbs_dir)) mkdir($full_thumbs_dir, 0755, true);

            
        // Ensure directories exist
        if (!is_dir($full_upload_dir)) @mkdir($full_upload_dir, 0777, true); // Use 0777 for Windows debug
        if (!is_dir($full_thumbs_dir)) @mkdir($full_thumbs_dir, 0777, true); // Use 0777 for Windows debug

        // --- Start Debugging ---
        // echo "<pre>";
        // echo "DEBUGGING PATHS:\n";
        // echo "DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not Set') . "\n";
        // echo "path variable: " . (isset($path) ? $path : 'Not Set') . "\n";
        // echo "upload_dir_relative: " . $upload_dir . "\n";
        // echo "thumbs_dir_relative: " . $thumbs_dir . "\n";
        // echo "--- Calculated Absolute Paths ---\n";
        // echo "Full Upload Dir: " . $full_upload_dir . "\n";
        // echo "Full Thumbs Dir: " . $full_thumbs_dir . "\n";
        // echo "--- Directory Checks ---\n";
        // echo "Upload Dir Exists? " . (is_dir($full_upload_dir) ? 'Yes' : 'No') . "\n";
        // echo "Upload Dir Writable? " . (is_writable($full_upload_dir) ? 'Yes' : 'No') . "\n";
        // echo "Thumbs Dir Exists? " . (is_dir($full_thumbs_dir) ? 'Yes' : 'No') . "\n";
        // echo "Thumbs Dir Writable? " . (is_writable($full_thumbs_dir) ? 'Yes' : 'No') . "\n";
        // echo "</pre>";
        // exit; // <-- Uncomment this line temporarily to stop execution here and see the output
        // --- End Debugging ---
    
        // Initialize result
        $result = ['success' => false, 'message' => '', 'filename' => ''];
        
        // Check for errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['message'] = 'Upload failed with error code: ' . $file['error'];
            return $result;
        }
        
        // Validate size (2MB max)
        $max_size = 2 * 1024 * 1024;
        if ($file['size'] > $max_size) {
            $result['message'] = 'File exceeds maximum size of 2MB';
            return $result;
        }
        
        // Validate type
        $allowed_types = ['image/jpeg', 'image/png'];
        $file_type = mime_content_type($file['tmp_name']);
        if (!in_array($file_type, $allowed_types)) {
            $result['message'] = 'Invalid file type. Only JPG and PNG are allowed';
            return $result;
        }
        
        // Generate unique filename
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $new_filename = 'profile_' . $account_id . '_' . time() . '.' . $extension;
        // $new_filename = 'profile_' . $account_id . '.' . $extension;
        
        // Initialize ImageUpload class
        $imageUpload = new ImageUpload();
        
        // Process main image
        if (!$imageUpload->load($file['tmp_name'])) {
            $result['message'] = 'Failed to load image';
            return $result;
        }
        
        // Resize main image to max dimensions while maintaining aspect ratio
        $imageUpload->smartFill(800, 800);
        
        // Save main image
        if (!$imageUpload->save($full_upload_dir, $new_filename)) {
            $result['message'] = 'Failed to save main image';
            return $result;
        }
        
        // Create thumbnail
        $imageUpload->load($full_upload_dir . $new_filename);
        $imageUpload->smartCrop(150, 150);
        
        if (!$imageUpload->save($full_thumbs_dir, $new_filename)) {
            $result['message'] = 'Failed to save thumbnail';
            // Delete the main image if thumbnail creation fails
            @unlink($full_upload_dir . $new_filename);
            return $result;
        }
        
        // Delete old files if they exist
        if (!empty($old_filename)) {
            @unlink($full_upload_dir . $old_filename);
            @unlink($full_thumbs_dir . $old_filename);
        }
        
        $result['success'] = true;
        $result['filename'] = $new_filename;
        return $result;
}

/**
 * Deletes a profile image and its thumbnail
 * 
 * @param string $filename The filename to delete
 * @return boolean Success or failure
 */
// function delete_profile_image($filename) {
//     global $path;

//     if (empty($filename)) return false;
    
//     $upload_dir = 'images/users/';
//     $thumbs_dir = $upload_dir . 'thumbs/';
//     $full_upload_dir = $_SERVER['DOCUMENT_ROOT'] . (isset($path) ? $path : '/') . $upload_dir;
//     $full_thumbs_dir = $_SERVER['DOCUMENT_ROOT'] . (isset($path) ? $path : '/') . $thumbs_dir;
    
//     $main_deleted = @unlink($full_upload_dir . $filename);
//     $thumb_deleted = @unlink($full_thumbs_dir . $filename);
    
//     return ($main_deleted || $thumb_deleted);
// }

/**
 * Deletes a profile image and its thumbnail
 *
 * @param string $filename The filename to delete
 * @return boolean Success or failure (best effort)
 */
function delete_profile_image($filename) {
    // ----> ADD global $path HERE! <----
    global $path;

    if (empty(trim($filename))) {
        error_log("delete_profile_image: Called with empty filename.");
        return false;
    }

    // Define paths using the global $path
    $webroot_path = isset($path) ? $path : '/';
    $upload_dir_relative = 'images/users/';
    $thumbs_dir_relative = $upload_dir_relative . 'thumbs/';
    $base_server_path = rtrim($_SERVER['DOCUMENT_ROOT'], '/\\');

    // Construct full paths
    $full_upload_path = $base_server_path . $webroot_path . $upload_dir_relative . $filename;
    $full_thumb_path = $base_server_path . $webroot_path . $thumbs_dir_relative . $filename;

    // Clean paths for the OS
    $full_upload_path = str_replace('/', DIRECTORY_SEPARATOR, $full_upload_path);
    $full_thumb_path = str_replace('/', DIRECTORY_SEPARATOR, $full_thumb_path);

    // --- Debugging Logs ---
    error_log("delete_profile_image: Attempting deletion for filename: " . $filename);
    error_log("delete_profile_image: Calculated main path: " . $full_upload_path);
    error_log("delete_profile_image: Calculated thumb path: " . $full_thumb_path);

    $main_exists = file_exists($full_upload_path);
    $thumb_exists = file_exists($full_thumb_path);

    error_log("delete_profile_image: Main file exists? " . ($main_exists ? 'Yes' : 'No'));
    error_log("delete_profile_image: Thumb file exists? " . ($thumb_exists ? 'Yes' : 'No'));

    $main_deleted = false;
    if ($main_exists) {
        $main_deleted = @unlink($full_upload_path);
        error_log("delete_profile_image: Main unlink result: " . ($main_deleted ? 'Success' : 'Failed'));
        if (!$main_deleted) {
             error_log("delete_profile_image: PHP Warning/Error likely occurred during main unlink (Check main PHP error log for details like permissions)");
        }
    }

    $thumb_deleted = false;
     if ($thumb_exists) {
        $thumb_deleted = @unlink($full_thumb_path);
        error_log("delete_profile_image: Thumb unlink result: " . ($thumb_deleted ? 'Success' : 'Failed'));
         if (!$thumb_deleted) {
             error_log("delete_profile_image: PHP Warning/Error likely occurred during thumb unlink (Check main PHP error log for details like permissions)");
         }
    }

    // empty($_FILES);
    // unset($_FILES);

    // Consider success if the files are gone (either deleted now or didn't exist)
    // Return true if BOTH don't exist after attempting deletion
    return (!file_exists($full_upload_path) && !file_exists($full_thumb_path));
}
?>