<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?>">
	<?php if($desktop_img){ ?>
	<div class="panel-image">
		<div class="responsive-bg"
			 style="<?php echo responsive_bg('images/panels/', [$desktop_img, $mobile_img]); ?>
			 	    --bg-pos-desktop: <?php echo $desktop_pos; ?>;
			 	    --bg-pos-mobile: <?php echo $mobile_pos; ?>;"></div>
		<div class="overlay overlay-<?php echo $panel['theme']; ?>"></div>

	<!-- <div class="curve-container">
		<img src="/pga/images/svg/parllax-bg.svg?v=2.2" alt="" class="curve-shape">
	</div> -->

	</div>
	<?php } ?>

	<!-- SVG Curve Overlay -->
	<!-- <div class="curve-overlay"></div>
	<div class="curve-container">
		<img src="/pga/images/svg/parllax-bg.svg?v=2.2" alt="" class="curve-shape">
	</div> -->

	<div class="panel-wrapper">
		<?php
		if($panel['title'] || $panel['include_h1']){
			echo '<header class="panel-header">
				<div class="container">
					'.($panel['include_h1'] ? '<h1>'.fancy_text($page['page_title']).'</h1>' : '').'
					'.($panel['title'] ? '<div class="panel-title"><h2>'.fancy_text($panel['title']).'</h2></div>' : '').'
				</div>
			</header>';
		}

		echo $panel['prepend_content'];

		if($panel['content']){
			echo '<div class="panel-content">
				<div class="container">
					<div class="panel-text">'.$panel['content'].'</div>
				</div>
			</div>';
		}

		echo $panel['append_content'];
		?>
	</div>
	<?php 
	if($panel['google_map']){
		include(include_path("includes/templates/contact-map.php"));
	}
	?>
</section>
