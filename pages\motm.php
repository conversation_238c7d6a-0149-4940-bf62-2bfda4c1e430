<?php

// Initialize variables to prevent undefined variable errors
$html = '';
$archive_year = $archive_year ?? NULL;
$archives = $archives ?? array();
$changelog = $changelog ?? array();

//Back link for archives
if(isset($archive_year) && in_array($archive_year, $archives)){
	$html .= '<a href="' .$_sitepages['motm']['page_url']. '">&lsaquo; Back to Recent</a>';
} else {
	$html .= '<h2>Follow the Pros</h2><p>Follow your favourite PGA of Alberta Golf Professional using our Members on the Move feature updated throughout the year.</p>';
}

//Display members on the move
if(!empty($changelog)){
	foreach($changelog as $date=>$log){

		$html .= '<h4>' .$date. '</h4>
		<p>';
		foreach($log as $entry){
			$html .= '<strong>' .date("d", strtotime($entry['updated_on'])).'</strong> &nbsp; ' .$entry['comments']. '<br />';
		}
		$html .= '</p>';

	}
}else{
	$html .= '<p>No entries to display.</p>';
}

//Display archive links
if(!empty($archives)){
	$html .= '<h4>Archives</h4>
	<p>';
	foreach($archives as $year){
		$html .= '<a href="' .$_sitepages['motm']['page_url']. 'archive-' .$year. '/">' .$year. '</a><br />';
	}
	$html .= '</p>';
}

//Set panel content
$page['page_panels'][$panel_id]['content'] = $html;

//Page panels
include("includes/pagepanels.php");

?>