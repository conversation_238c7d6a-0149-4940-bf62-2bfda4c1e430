<?php
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);
//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == 4) {
	$total_records = $db->get_record_count('award_winners');
	$CMSBuilder->set_widget($_cmssections['award-winners'], 'Total Award Winners', $total_records);
}

if(SECTION_ID == $_cmssections['award-winners']){
	
	//Define vars
	$record_db = 'award_winners';
	$record_id = 'winner_id';
	$record_name = 'Winner';
	$award_id = (isset($_GET['award_id']) && !empty($_GET['award_id']) ? $_GET['award_id'] : NULL); //Remember category

	$imagedir = "../images/users/";
	$CMSUploader = new CMSUploader('award_winners_img', $imagedir);
	
	$errors = false;
	$required = array();
	$required_fields = array('award_id' => 'Award', 'type' => 'Winner Type', 'year' => 'Year', 'first_name' => 'First Name', 'last_name' => 'Last Name'); //for validation

	//Get awards
	$awards = array();
	$query = $db->query("SELECT * FROM `awards` ORDER BY `parent_id` ASC, `name` ASC");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			if(empty($row['parent_id'])){
				$row['award_name'] = $row['name'];
				$awards[$row['award_id']] = $row;
			}else{
				if(array_key_exists($row['parent_id'], $awards)){
					$row['award_name'] = $awards[$row['parent_id']]['name']." - ".$row['name'];
					$awards[$row['parent_id']]['sub_categories'][$row['award_id']] = $row;
					$awards[$row['award_id']] = $row;
				}
				
			}
		}
	}
	
	//Get Records
	$records_arr = array();
	$params = array();
	if(!empty($award_id)){
		$params[] = $award_id;
	}
	if($searchterm != ""){
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
		$params[] = ' ';
		$params[] = '%' .$searchterm. '%';
		$params[] = '%' .$searchterm. '%';
	}
	$query = $db->query("SELECT `$record_db`.*, ".
	"`account_profiles`.`profile_id`, `account_profiles`.`first_name` AS `profile_first_name`, `account_profiles`.`last_name` AS `profile_last_name`, `account_profiles`.`photo`, ".
	"IFNULL(`$record_db`.`first_name`, `account_profiles`.`first_name`) AS `first_name`, ".
	"IFNULL(`$record_db`.`last_name`, `account_profiles`.`last_name`) AS `last_name`, ".
	"IFNULL(`$record_db`.`image`, `account_profiles`.`photo`) AS `image` ".
	"FROM `$record_db` ".
	"LEFT JOIN `accounts` ON `$record_db`.`account_id` = `accounts`.`account_id` ".
	"LEFT JOIN `account_profiles` ON `$record_db`.`account_id` = `account_profiles`.`account_id` ".
	"WHERE `$record_db`.`$record_id` IS NOT NULL ".
	(!empty($award_id) ? "&& `$record_db`.`award_id` = ? " : "").
	($searchterm != "" ? "&& CONCAT(`$record_db`.`first_name`, ? ,`$record_db`.`last_name`) LIKE ? OR CONCAT(`account_profiles`.`first_name`, ? ,`account_profiles`.`last_name`) LIKE ? OR `$record_db`.`year` LIKE ? " : "").
	"ORDER BY `year` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);	
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		
		$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
		if(!$db->error()) {

			//Delete images
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
	
			$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record. ' .$db->error(),false);	
		}

		header("Location: " .PAGE_URL);
		exit();
	
	//Save item
	} else if(isset($_POST['save'])){
		
		//Validate
		$required_missing = false;
		if(!empty($required_fields)) {
			foreach($required_fields as $field_key => $field_name) {
				if(isset($_POST[$field_key])) {
					if(trim($_POST[$field_key]) == '') {
						$required_missing = true;
						array_push($required, $field_key);
					}
				} else {
					$required_missing = true;
					array_push($required, $field_key);
				}
			}
		}
		if($required_missing) {
			$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
		}
		//Image validation
		if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > $_max_filesize['bytes']){
			$errors[] = 'Image filesize is too large.';
			$required[] = 'image';
		}
		if(!isset($_POST['showhide'])){
			$_POST['showhide'] = 1;
		}
		
		if(!$errors) {
		

			$pagename = clean_url($_POST['first_name']);
			
			//Delete image
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//Upload image
			try{
				$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			$image = $images['image'] ?? ($records_arr[ITEM_ID]['image'] ?? '');
			
			//Member searched
			if(!empty($_POST['account_id'])){
				if($_POST['first_name'] == $_POST['profile_first_name'] && $_POST['last_name'] == $_POST['profile_last_name']){
					$_POST['first_name'] = NULL;
					$_POST['last_name'] = NULL;
				}
			}

			//Insert to db
			$params = array(
				ITEM_ID, 
				$_POST['award_id'],
				$_POST['type'],
				$_POST['year'],
				(!empty($_POST['account_id']) ? $_POST['account_id'] : NULL), 
				$_POST['first_name'], 
				$_POST['last_name'], 
				trim($_POST['description']), 
				$images['image'] ?? NULL, 
				$_POST['showhide'], 
				date('Y-m-d H:i:s'),
				date('Y-m-d H:i:s'),
				$_POST['award_id'],
				$_POST['type'],
				$_POST['year'],
				(!empty($_POST['account_id']) ? $_POST['account_id'] : NULL), 
				$_POST['first_name'], 
				$_POST['last_name'], 
				trim($_POST['description']), 
				$images['image'] ?? NULL, 
				$_POST['showhide'],
				date('Y-m-d H:i:s')
			);
			$insert = $db->query("INSERT INTO `$record_db` (`$record_id`, `award_id`, `type`, `year`, `account_id`, `first_name`, `last_name`, `description`, `image`, `showhide`, `date_added`, `last_updated`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `award_id` = ?, `type` = ?, `year` = ?, `account_id` = ?, `first_name` = ?, `last_name` = ?, `description` = ?, `image` = ?, `showhide` = ?, `last_updated` = ?", $params);
			if($insert && !$db->error()){

			if(!$CMSUploader->crop_queue()){
				$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
				header("Location: " .PAGE_URL);
				exit();
			}
			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		} else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	} else{
		include('modules/CropImages.php');
	}
}

?>