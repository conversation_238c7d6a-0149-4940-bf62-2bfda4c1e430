<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?>">
	<?php
	if($panel['title'] || $panel['include_h1'] || $panel['content']) {
		echo '<div class="panel-wrapper">';

		if($panel['title'] || $panel['include_h1']) {
			echo '<header class="panel-header">
				<div class="container">
					'.($panel['include_h1'] ? '<h1>'.fancy_text($page['page_title']).'</h1>' : '').'
					'.($panel['title'] ? '<div class="panel-title"><h2>'.fancy_text($panel['title']).'</h2></div>' : '').'
				</div>
			</header>';
		}

		if($panel['content']) {
			echo '<div class="panel-content">
				<div class="container">
					<div class="panel-text">'.nl2br($panel['content']).'</div>
				</div>
			</div>';
		}

		echo '</div>';
	}

	if(!empty($panel['gallery']['photos'])){
		echo '<div class="panel-gallery">
			<div class="container">
				<div class="light-gallery animate" data-animate=".gal-item" data-delay="150">';

		foreach($panel['gallery']['photos'] as $photo_id => $photo){
			if($thumb = check_file('images/galleries/thumbs/'.$photo['image'])){
				$photo['image_alt'] = $photo['image_alt'] ?: $photo['caption'] ?: $panel['gallery']['name'];
				list($imgw, $imgh) = getimagesize($thumb);

				echo '<div class="gal-item" data-src="'.$path.'images/galleries/'.$photo['image'].'" data-exthumbimage="'.$path.$thumb.'">
					<a href="'.$path.'images/galleries/'.$photo['image'].'" target="_blank" class="gal-link" title="'.$photo['caption'].'">
						<div class="overlay"></div>
						<img class="lazy-load"
							 src="'.empty_src($imgw, $imgh).'"
							 data-src="'.$path.$thumb.'"
							 title="'.$photo['image_alt'].'"
							 alt="'.$photo['image_alt'].'"
							 width="'.$imgw.'"
							 height="'.$imgh.'" />
					</a>
				</div>';
			}
		}

			echo '</div>
		</div>';
	}
	?>
</section>