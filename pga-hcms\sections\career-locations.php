<?php

// Check for valid login
if (!defined('USER_LOGGED_IN') || !USER_LOGGED_IN) {
	exit();
}

// Table listing
if (ACTION == '') {
	
	// Action bar
	echo '<div class="actions-nav flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>

		<div class="flex-column right">
			<a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a>
		</div>
	</div>';
	
	// All records
	echo '<div class="panel">
		<div class="panel-header">'.$record_name.'s 
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">
			
				<thead>
					<th width="1px" data-sorter="false"></th>
					<th width="250px">Location Name</th>
					<th>Address</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';

				foreach ($records_arr as $row) {
					echo '<tr data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-name="'.$row['name'].'" data-id="'.$row[$record_id].'">
						<td class="handle"><span class="fas fa-arrows-alt"></span></td>
						<td>'.$row['name'].'</td>
						<td>'.
							$row['line1'].
							($row['line1'] && $row['line2'] ? '<br><small>'.$row['line2'].'</small>' : $row['line2']). // Line 2 is smalltext unless line 1 is falsey
							(!$row['line1'] && !$row['line2'] ? '<small>N/A</small>' : ''). // No address
						'</td>
						<td>'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row['showhide']).'</td>
						<td class="right"><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i> Edit</a></td>
					</tr>';
				}

				echo '</tbody>
			</table>';

			// Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';


// Display form
} else {
	$data = $records_arr[ITEM_ID] ?? [];
	$row  = !isset($_POST['save']) ? $data : $_POST;

	echo "<form action='' method='post' enctype='multipart/form-data'>";

		// General
		echo '<div class="panel">
			<div class="panel-header">General Information
				<span class="panel-toggle fas fa-chevron-up"></span>
				<div class="panel-switch">
					<label>Show '.$record_name.'</label>
					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"'.(($row['showhide'] ?? 0) ? '' : ' checked').' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>

			<div class="panel-content flex-container">
				<div class="form-field">
					<label>Location Name'.(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="name" class="input'.(in_array('name', $required) ? ' required' : '').'" value="'.($row['name'] ?? '').'" />
				</div>
				
				<div class="form-field">
					<label>Email Address'.(in_array('email', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="email" value="'.($row['email'] ?? '').'" class="input'.(in_array('email', $required) ? ' required' : '').'" />
				</div>
				
				<div class="form-field">
					<label>Numerical Order'.(in_array('ordering', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip('Numerical Order', 'Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering.').'</label>
					<select name="ordering" class="select">
						<option value="101">Default</option>';
					
					for ($i=1; $i<101; $i++) {
						echo '<option value="'.$i.'"'.(isset($row['ordering']) && $row['ordering'] == $i ? ' selected' : '').'>'.$i.'</option>';
					}

					echo '</select>
				</div>
			</div>
		</div>'; // General

		// Location
		echo '<div class="panel">
			<div class="panel-header">Address
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>

			<div class="panel-content">
				<div class="flex-container">
					<div class="form-field auto-width">
						<label>
							Street Address'.(in_array('address', $required_fields) ? ' <span class="required">*</span>' : '').'
							<small class="f_right" id="clear-location"><a>Clear Fields</a></small>
						</label>
						<input type="text" name="address" value="'.($row['address'] ?? '').'" class="input input_lg places-autocomplete" data-clear="#clear-location" />
					</div>
				</div>

				<div class="flex-container">
					<div class="form-field">
						<label>Unit No.'.(in_array('address2', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="address2" value="'.($row['address2'] ?? '').'" class="input" />
					</div>
					
					<div class="form-field">
						<label>City/Town'.(in_array('city', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="city" value="'.($row['city'] ?? '').'" class="input" />
					</div>
					
					<div class="form-field">
						<label>Province/State'.(in_array('province', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<select name="province" class="select">
							<option value="">- Select -</option>
							<optgroup label="Canada">';
							
							foreach ($provinces as $code=>$name) {
								echo '<option value="'.$code.'"'.(($row['province'] ?? '') == $code ? ' selected' : '').'>'.$name.'</option>';	
							}

							echo '</optgroup>

							<optgroup label="United States">';
							
							foreach ($states as $code=>$name) {
								echo '<option value="'.$code.'"'.(($row['province'] ?? '') == $code ? ' selected' : '').'>'.$name.'</option>';	
							}

							echo '</optgroup>
						</select>
					</div>
					
					<div class="form-field">
						<label>Country'.(in_array('country', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<select name="country" class="select">
							<option value="">- Select -</option>
							<option value="Canada"'.(($row['country'] ?? '') == 'Canada' ? ' selected' : '').'>Canada</option>
							<option value="United States"'.(($row['country'] ?? '') == 'United States' ? ' selected' : '').'>United States</option>
						</select>
					</div>
					
					<div class="form-field">
						<label>Postal/Zip Code'.(in_array('postal_code', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
						<input type="text" name="postal_code" value="'.($row['postal_code'] ?? '').'" class="input" />
					</div>
				</div>
			</div>
		</div>'; // Location

		// Sticky footer
		include("includes/widgets/formbuttons.php");

		echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

}

?>