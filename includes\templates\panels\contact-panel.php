<section id="panel-<?php echo $panel['panel_id']; ?>" class="<?php echo $panel['class']; ?>">
	<div class="panel-wrapper">
		<?php
		// Panel content from CMS
		if($panel['content'] || !empty($panel['panel_tabs']) || $panel['google_map'] || $panel['prepend_content'] || $panel['append_content']){
			echo $panel['prepend_content'];
			
			if($panel['content']){
				// Custom panel content from CMS editor
				echo '<div class="panel-cms-content">'.$panel['content'].'</div>';
			}
			
			echo $panel['append_content'];
		}
		
		if($panel['panel_type'] == 'contact-panel'){
			// Get locations data
			if(!empty($global['locations'])){
				// Main container for the contact panel
				echo '<div class="contact-panel-container">';

				echo '<div class="top-bar"><div class="top-bar-content"><span class="fancy-text">Contact</span> Information</div>
				<div class="contact-details">
  <a href="tel:************" class="contact-link">
    <i class="fas fa-phone contact-icon" aria-hidden="true"></i> 
    <span class="contact-text">************</span>
  </a>
  <a href="tel:**************" class="contact-link">
    <i class="fas fa-headset contact-icon" aria-hidden="true"></i> 
    <span class="contact-text">**************</span>
  </a>
  <a href="mailto:<EMAIL>" class="contact-link">
    <i class="fas fa-envelope contact-icon" aria-hidden="true"></i>
    <span class="contact-text"><EMAIL></span>
  </a>
</div></div>';
				
				// TOP SECTION - Form on right, vertical location tabs on left
				echo '<div class="contact-top-section">';
				
				// Left side - Vertical location tabs
				echo '<div class="location-tabs-vertical">';
				
				// Add up arrow for scrolling (red circular design)
				echo '<div class="location-scroll-arrow location-scroll-up"><i class="fas fa-arrow-up"></i></div>';
				
				// Wrapper for the scrollable content
				echo '<div class="location-slider-wrapper">';
				echo '<ul class="location-tabs-nav">';
				
				// foreach($global['locations'] as $index => $loc){
				// 	$activeClass = ($index === 0) ? ' active' : '';
				// 	$activeStyle = ($index === 0) ? ' style="font-weight: bold; font-size: 2em;"' : ' style="color: #aaa;"';
				// 	echo '<li class="location-tab'.$activeClass.'" 
				// 		data-location-id="'.$loc['location_id'].'"
				// 		data-search="'.$loc['full_address'].'" 
				// 		data-lat="'.$loc['gpslat'].'" 
				// 		data-lng="'.$loc['gpslong'].'" 
				// 		data-zoom="'.$loc['zoom'].'"'.$activeStyle.'>
				// 		'.$loc['location_name'].'
				// 	</li>';
				// }

				foreach($global['locations'] as $index => $loc){
					$activeClass = ($index === 0) ? ' active' : '';
					echo '<li class="location-tab'.$activeClass.'" 
						data-location-id="'.$loc['location_id'].'"
						data-search="'.$loc['full_address'].'" 
						data-lat="'.$loc['gpslat'].'" 
						data-lng="'.$loc['gpslong'].'" 
						data-zoom="'.$loc['zoom'].'">
						'.$loc['location_name'].'
					</li>';
				}
				
				echo '</ul>';
				echo '</div>'; // End slider wrapper
				
				// Add down arrow for scrolling (red circular design)
				echo '<div class="location-scroll-arrow location-scroll-down"><i class="fas fa-arrow-down"></i></div>';
				
				echo '</div>'; // End vertical tabs
				
				// Right side - Contact form
				echo '<div class="contact-form-container">';
				echo '<h2>Send us a Message</h2>';
				
				// Include the contact form
				// $form_html = '';
				include(include_path('includes/templates/contact-form.php'));
				echo $html; // Output the form HTML
				
				echo '</div>'; // End contact form container
				
				echo '</div>'; // End top section
				
				// BOTTOM SECTION - Location details and map
				echo '<div class="contact-bottom-section">';
				
				// Left side - Location details (moved from right to left)
				echo '<div class="location-details-container">';
				
				// Create location content for each location
				foreach($global['locations'] as $index => $loc){
					$displayStyle = ($index === 0) ? 'block' : 'none';
					
					echo '<div id="location-details-'.$loc['location_id'].'" class="location-details" style="display:'.$displayStyle.';">';
					
					// Location name and address
					echo '<div class="location-header">';
					// echo '<h3>'.$loc['location_name'].'</h3>';
					echo '<p><small>Address</small></p>';
					
					if($loc['full_address']){
						echo '<div class="location-address">';
						echo'<p>'.prettify_address($loc, true).'</p>';
						echo '<a href="http://maps.google.com/?q='.urlencode($loc['full_address']).'" target="_blank" class="button secondary">Get Directions</a>';
						echo '</div>';
					}
					echo '</div>'; // End location header
					
					// Contact information
					echo '<div class="contact-info">';
					
					// // Phone numbers
					// echo '<div class="contact-section">';
					// echo '<h4>Contact Numbers</h4>';
					// echo '<ul class="contact-list">';
					
					// if($loc['phone']){
					// 	echo '<li><span class="label">Phone:</span> <a href="tel://'.format_intl_number($loc['phone']).'">'.$loc['phone'].'</a></li>';
					// }
					
					// if($loc['toll_free']){
					// 	echo '<li><span class="label">Toll Free:</span> <a href="tel://'.format_intl_number($loc['toll_free']).'">'.$loc['toll_free'].'</a></li>';
					// }
					
					// if($loc['fax']){
					// 	echo '<li><span class="label">Fax:</span> '.$loc['fax'].'</li>';
					// }
					
					// foreach($loc['location_numbers'] as $number){
					// 	echo '<li><span class="label">'.ucwords($number['type']).':</span> <a href="tel://'.format_intl_number($number['phone']).'">'.$number['phone'].'</a></li>';
					// }
					
					// echo '</ul>';
					// echo '</div>'; // End contact section
					
					// // Email addresses
					// if($loc['email'] || !empty($loc['location_contacts'])){
					// 	echo '<div class="contact-section">';
					// 	echo '<h4>Email Addresses</h4>';
					// 	echo '<ul class="contact-list">';
						
					// 	if($loc['email']){
					// 		echo '<li><span class="label">Email:</span> <a href="mailto:'.$loc['email'].'">'.$loc['email'].'</a></li>';
					// 	}
						
					// 	foreach($loc['location_contacts'] as $contact){
					// 		if($contact['type'] == 'email'){
					// 			echo '<li><span class="label">'.($contact['label'] ?: 'Alt Email').':</span> <a href="mailto:'.$contact['value'].'">'.$contact['value'].'</a></li>';
					// 		}
					// 	}
						
					// 	echo '</ul>';
					// 	echo '</div>'; // End contact section
					// }

											
					if($loc['hours_disclaimer']){
						echo '<p class="hours-disclaimer">'.$loc['hours_disclaimer'].'</p>';
					}
					
					// Business hours
					if($loc['show_hours']){
						echo '<div class="contact-section">';
						// echo '<h4>Hours of Operation</h4>';
						
						$today = $loc['location_hours'][date('N')-1];
						$now = strtotime('now');
						$open = strtotime($today['start_time']);
						$closed = strtotime($today['end_time']);
						$closing = strtotime($today['end_time'].' - 1 hour');
						
						// // Current status
						// if($today['closed'] || $now < $open || $now >= $closed){
						// 	echo '<p class="open-status closed">'.($loc['closed_text'] ?: 'Currently Closed').'</p>';
						// } else if($now >= $closing){
						// 	echo '<p class="open-status closing">'.($loc['closing_text'] ?: 'Closing Soon').'</p>';
						// } else {
						// 	echo '<p class="open-status open">'.($loc['open_text'] ?: 'Currently Open').'</p>';
						// }
						
						echo '<ul class="hours-list">';
						foreach($loc['location_hours'] as $hours){
							$isToday = ($hours['day'] == date('l'));
							echo '<li'.($isToday ? ' class="today"' : '').'>';
							echo '<span class="day">'.$hours['day'].'</span>';
							echo '<span class="hours">'.($hours['closed'] ? 'Closed' : date('g:ia', strtotime($hours['start_time'])).' - '.date('g:ia', strtotime($hours['end_time']))).'</span>';
							echo '</li>';
						}
						echo '</ul>';

						
						echo '</div>'; // End contact section
					}
					
					echo '</div>'; // End contact info
					echo '</div>'; // End location details
				}
				
				echo '</div>'; // End location details container
				
				// Right side - Map (moved from left to right)
				echo '<div class="map-container">';
				echo '<div id="contact-map" class="contact-map" 
					data-search="'.$global['locations'][0]['full_address'].'" 
					data-lat="'.$global['locations'][0]['gpslat'].'" 
					data-lng="'.$global['locations'][0]['gpslong'].'" 
					data-zoom="'.$global['locations'][0]['zoom'].'">
				</div>';
				echo '</div>'; // End map container
				
				echo '</div>'; // End bottom section
				echo '</div>'; // End contact panel container
								
				// Include the Google Maps
				// if ($panel['google_map']) {
				// 	include(include_path("includes/templates/contact-map.php"));
				// }
			}
		}
		?>
	</div>
</section>
