<?php

//Table listing
if(ACTION == ''){

	include("includes/widgets/searchform.php");
	echo '<p class="f_right"><a href="'.PAGE_URL.'?action=add" class="button"><i class="fas fa-plus"></i>Add New</a></p>
	
	<div class="panel">
		<div class="panel-header">'.$records_name.'
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>

		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter sortable">
				<thead>
					<th width="1px" class="nopadding-r"></th>
					<th>Name</th>
					<th width="1px" class="center">Visible</th>
					<th width="1px" data-sorter="false"></th>
				</thead>
				
				<tbody>';

				foreach($records_arr as $row){
					echo '<tr data-table="'.$record_db.'" data-column-name="'.$record_id.'" data-id="'.$row[$record_id].'">
						<td class="handle nopadding-r"><i class="fas fa-arrows-alt"></i></td>
						<td>'.$row["name"].'</td>
						<td>'.$CMSBuilder->showhide_toggle($record_db, $record_id, $row[$record_id], $row["showhide"]).'</td>
						<td class="right"><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-edit"></i>Edit</a></td>
					</tr>';
				}

				echo '</tbody>
			</table>';
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo '</div>
	</div>';

//Display form
}else{
	
	if(ACTION == 'edit'){
		$data = $records_arr[ITEM_ID];
		if(!isset($_POST['save'])){
			$row = $data;
		}
		
	}else if(ACTION == 'add' && !isset($_POST['save'])){
		unset($row);
	}

	echo '<form action="" method="post" enctype="multipart/form-data">
		<div class="panel">
			<div class="panel-header">'.$record_name.' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
				
				<div class="panel-switch">
					<label>Show '.$record_name.'</label>

					<div class="onoffswitch">
						<input type="checkbox" name="showhide" id="showhide" value="0"'.(($row["showhide"] ?? 0) ? '' : ' checked'). ' />
						<label for="showhide">
							<span class="inner"></span>
							<span class="switch"></span>
						</label>
					</div>
				</div>
			</div>

			<div class="panel-content clearfix">
				<div class="form-field">
					<label>'.$record_name.' Name'.(in_array('name', $required_fields) ? ' <span class="required">*</span>' : '').'</label>
					<input type="text" name="name" value="'.($row["name"] ?? "").'" class="input'.(in_array("name", $required) ? " required" : "").'" />
				</div>

				<div class="form-field">
					<label>Numerical Order'.(in_array('ordering', $required_fields) ? ' <span class="required">*</span>' : '').$CMSBuilder->tooltip("Numerical Order", "Items will be displayed in the order they were added unless specified here. Items set to &quot;Default&quot; will appear after items with numerical ordering."). '</label>
					<select name="ordering" class="select">
						<option value="101">Default</option>';
					
					for($i = 1; $i < 101; $i++){
						echo '<option value="'.$i.'" '.(($row["ordering"] ?? '') == $i ? 'selected' : '').'>'.$i.'</option>';	
					}

					echo '</select>
				</div>
			</div>
		</div>';
		
		echo '<div class="panel page-content">
			<div class="panel-header">'.$record_name.' Description'.(in_array('content', $required_fields) ? ' <span class="required">*</span>' : '').'
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">
				<textarea name="content" class="tinymceEditor">'.($row['content'] ?? '').'</textarea>
			</div>
		</div>';

		//Sticky footer
		include('includes/widgets/formbuttons.php');
	
		echo '<input type="hidden" name="keep_tags[]" value="content" />
		<input type="hidden" name="xssid" value="'.$_COOKIE["xssid"].'" />
	</form>';
	
}

?>