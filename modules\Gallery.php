<?php

define('GALLERY_ID', $SiteBuilder->get_pagebit_id('gallery'));

if(PAGE_ID == $_sitepages['gallery']['page_id'] || PARENT_ID == $_sitepages['gallery']['page_id']) {
	
	//Define vars
	$panel_id = 3;
	$imagedir = 'images/galleries/';
	
	//Get all galleries
	$db->query("SELECT * FROM `galleries` WHERE `showhide` < ? ORDER BY `ordering`", [PAGE_ID == $_sitepages['gallery']['page_id'] ? 1 : 2]);
	$galleries = $db->fetch_assoc('gallery_id');
	foreach($galleries as $gal_id => &$gal) {

		// Get photos
		$db->query("SELECT * FROM galleries_photos WHERE showhide = 0 AND gallery_id = ? ORDER BY featured DESC, ordering ASC LIMIT 1", [$gal_id]);
		$featured_result  = $db->fetch_array();

		$gal['feat_image']      = $featured_result[0]['image'] ?? '';
		$gal['feat_image_alt']  = $featured_result[0]['caption'] ?? '';
		$gal['page_url']  		= $_sitepages['gallery']['page_url'].$gal['page'].'-'.$gal_id.'/';

		if(PAGE_ID == $_sitepages['gallery']['page_id'] && isset($page['page_panels'][$panel_id])){
			$page['page_panels'][$panel_id]['panel_promos'][$gal['gallery_id']] = [
				'promo_id'    => 'gallery-'.$gal_id,
				'title'       => $gal['name'],
				'description' => $gal['content'],
				'image'       => $gal['feat_image'],
				'image_alt'   => $gal['feat_image_alt'],
				'imagedir'    => $imagedir.'featured/',
				'url'         => $gal['page_url'],
				'url_text'    => 'View Gallery'
			];
		}

		unset($gal);
	}

	//Show galleries as promo boxes
	if(PAGE_ID == $_sitepages['gallery']['page_id'] && isset($page['page_panels'][$panel_id])){
		$page['page_panels'][$panel_id]['panel_type'] = 'promo';	
	}

	//Selected gallery page
	if(PARENT_ID == $_sitepages['gallery']['page_id'] && PAGE_ID == '' && $_sitepages['gallery']['showhide'] < 2) {
		if($gallery = $galleries[GALLERY_ID] ?? false) {
			$error404 = false;

			//Set page details
			$parent = $SiteBuilder->get_page_content(PARENT_ID);
			$page['page_title']       = $gallery['name'];
			$page['description']      = '';
			$page['meta_canonical']   = $gallery['page_url'];
			$page['meta_title']       = $gallery['meta_title'] ?: $gallery['name'].' | '.$parent['seo_title'];
			$page['meta_description'] = $gallery['meta_description'] ?: $parent['meta_description'];
			
			//Banner image
			$gallery['image'] = check_file($gallery['image'], 'images/heroes/1920/');
			$page['banner_image'] = $gallery['image'] ?: $parent['banner_image'];
			$page['banner_image_alt'] = $gallery['image_alt'] ?: $parent['banner_image_alt'];
			
			//Update breadcrumbs
			array_pop($breadcrumbs);
			$breadcrumbs[] = ['url' => $gallery['page_url'], 'name' => $gallery['name']];

			//Add dummy gallery panel for photos
			$page['page_panels']['gallery'] = [
				'panel_id'      => 'gallery',
				'panel_type'    => 'gallery',
				'theme'         => '',
				'title'         => '',
				'content'       => $gallery['content'] ? '<p>'.$gallery['content'].'</p>' : '',
				'google_map'    => false,
				'show_title'    => false,
				'class'         => 'gallery-listings',
				'gallery_id'    => GALLERY_ID,
				'gallery'       => $SiteBuilder->get_attached_gallery(GALLERY_ID),
				'gallery_limit' => ''
			];

			//Ensure url is correct
			if($gallery['page_url'] != $page['page_url']){
				header("HTTP/1.1 301 Moved Permanently");
				header('Location: ' .$gallery['page_url']);
				exit();
			}
		}
	}
}

?>