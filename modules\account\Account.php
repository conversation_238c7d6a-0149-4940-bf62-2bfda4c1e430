<?php  
// ini_set('display_errors', 1);
// 	ini_set('display_startup_errors', 1);
// 	error_reporting(E_ALL);
	// echo PAGE_ID;
//Restrict account section
$pagebits = $SiteBuilder->get_pagebits($_sitepages['account']['page']);
if((!defined('USER_LOGGED_IN') || !USER_LOGGED_IN) && ($pagebits[1] ?? "") == $_sitepages['account']['page']){
	header('Location: '.$_sitepages['login']['page_url'].'?redirect='.urlencode($page['page_url']));
	exit();
}
//Account pages
$_accountpages = array();
$query = $db->query("SELECT `page_id`, `page` FROM `pages` WHERE `parent_id` = ? && `showhide` = 0 ORDER BY `ordering`", array($_sitepages['account']['page_id']));
if($query && !$db->error()){
	$result = $db->fetch_array();
	foreach($result as $row){
		
		// // Restrict hole-in-one access (members and hio accounts)
		// if($row['page_id'] == $_sitepages['hole_in_one']['page_id']){
		// 	if(MEMBER_ACCESS || HIO_ACCESS){
		// 		$_accountpages[$row['page']] = $sitemap[$row['page_id']];				
		// 	}
			
		// //Restrict registration access (not hio accounts)
		// }else if($row['page_id'] == 60){
		// 	if(!HIO_ACCESS){
		// 		$_accountpages[$row['page']] = $sitemap[$row['page_id']];
		// 	}
			
		// //Restrict member access
		// }else{
		// 	if(!in_array($row['page_id'], $_memberpages) || (in_array($row['page_id'], $_memberpages) && MEMBER_ACCESS)){
		// 		$_accountpages[$row['page']] = $sitemap[$row['page_id']];
		// 	}
		// }
	}
}


define('ACTION', ($_GET['action'] ?? ''));
define('ITEM_ID', (isset($_GET['id']) ? $_GET['id'] : NULL));
//Account modules
include("modules/account/Register.php");
include("modules/account/Login.php");
include("modules/account/Reset.php");
include("modules/account/Profile.php");
include("modules/account/Settings.php");
include("modules/account/BillingProfiles.php");
include("modules/account/ChangePassword.php");
include("modules/account/Facility.php");
include("modules/account/Careers.php");
include("modules/account/Classifieds.php");
include("modules/account/Invoices.php");
include("modules/account/Payments.php");
include("modules/account/HoleInOne.php");
include("modules/account/MessageCentre.php");
include("modules/account/Registrations.php");
include("modules/account/Withdrawal.php");
include("modules/account/CompensationForm.php");
?>
?>