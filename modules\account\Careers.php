<?php

//My Job Postings
if(PAGE_ID == $_sitepages['my-job-postings']['page_id']){

	//Check for active login
	if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
		header('Location: ' .$_sitepages['login']['page_url'].'?redirect='.urlencode($_SERVER['REQUEST_URI']));
		exit();
	}
	
	//Set vars
	$panel_id = 54;
	$careers = array();
	$searchable_params = array('title', 'facility_name', 'category_name');
	$filedir = 'uploads/files/';
	$required = [];

	$file_fields = [
		'file'
	];

	//Pagination
	$pg = (isset($_GET['pg']) ? $_GET['pg'] : 1);
	$limit = 20;
	$totalresults = 0;

	//Deletion alert
	if(isset($_SESSION['deleted'])){
		$alert = $Account->alert('Job posting was successfully deleted.', true);
		unset($_SESSION['deleted']);
	}

	//Build query
	$params[] = USER_LOGGED_IN;
	$q = "SELECT `careers`.*, `career_categories`.`category_name`, `facilities`.`facility_name`, `facilities`.`city`, `facilities`.`province` FROM `careers` LEFT JOIN `career_categories` ON `career_categories`.`category_id` = `careers`.`category_id` LEFT JOIN `facilities` ON `facilities`.`facility_id` = `careers`.`facility_id` WHERE `careers`.`account_id` = ?";

	//Text search
	if(isset($_GET['search']) && $_GET['search']){
		$q .= " AND (";
		foreach ($searchable_params as $value){
			$q .= $value . " LIKE ? OR ";
			$params[] = '%'.$_GET['search'].'%';
		}
		$q = substr($q, 0, -4). ")"; //Remove excess " OR "

	}

	$q .= " ORDER BY `careers`.`posted_date` DESC";

	//Build filtered query
	$query = $db->query($q, $params);
	if($query && !$db->error()){
		if($db->num_rows() > 0){
			$result = $db->fetch_array();
			foreach ($result as $row) {
				$careers[$row['career_id']] = $row;
			}
			
			//Pagination
			if(ITEM_ID == ''){
				$totalresults = count($result);
				if($pg != 'all'){
					$start = (($pg-1)*$limit);
					$end = $limit;
				}else{
					$start = 0;
					$end = $totalresults;
				}
				$careers = array_slice($careers, $start, $end);
			}
			
		}
	}

	//Edit job
	if(ACTION == 'edit'){
	
		if(in_array(ITEM_ID, array_keys($careers))){
			$career_id = ITEM_ID;
			$career = $careers[$career_id];
			$file = $career['file_name'];
			
			//Get classes
			$career['class_id'] = array();
			$query = $db->query("SELECT `class_id` FROM `career_classes` WHERE `career_id` = ?", array(ITEM_ID));
			if($query && !$db->error()){
				$class_result = $db->fetch_array();
				foreach($class_result as $class_row){
					$career['class_id'][] = $class_row['class_id'];
				}
			}
			
			//Load category data
			$category_query = $db->query("SELECT * FROM `career_categories` ORDER BY `category_name`");
			if($category_query && !$db->error()){
				if($db->num_rows() > 0){
					$categories = $db->fetch_array();
				}
			}

			//Load facility data
			$facility_query = $db->query("SELECT * FROM `facilities` ORDER BY `facility_name`");
			if($facility_query && !$db->error()){
				if($db->num_rows() > 0){
					$facilities = $db->fetch_array();
				}
			}
			
			//Load classes
			$classes = array();
			$classes_query = $db->query("SELECT * FROM `membership_classes` WHERE `job_filter` = 1");
			if($classes_query && !$db->error() && $db->num_rows() > 0) {
				$classes = $db->fetch_array();
			}

			//Validation
			$required_fields = array('title', 'salary', 'duration', 'category_id', 'posted_date', 'closing_date', 'facility_id', 'first_name', 'last_name', 'email', 'phone', 'texteditor');
			if(isset($_POST['post']) && empty($errors)){
				foreach($required_fields as $field){
					if(!isset($_POST[$field]) || !trim(strip_tags(str_replace('&nbsp;', ' ', $_POST[$field])))){
						$errors[0] = 'Please fill out all the required fields.';
						$required[] = $field;
					}
					if($field == 'phone' && !detectPhone($_POST[$field])){
						$errors[] = 'Please enter a valid phone number.';
						$required[] = $field;
					}
					if($field == 'email' && !checkMail($_POST[$field])){
						$errors[] = 'Please enter a valid email.';
						$required[] = $field;
					}
				}
				if(strtotime($_POST['posted_date']) > strtotime($_POST['closing_date'])){
					$errors[] = 'Invalid closing date. Please enter a closing date after the posted date.';
					$required[] = 'closing_date';
					$required[] = 'posted_date';
				}
				if($_FILES['file']['name'] != ''){
					$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

					if($_FILES['file']['error'] == 1){
						$errors[] = 'Unable to upload file. Please try again.';
						$required[] = 'file';
					}else if($_FILES['file']['size'] > 2097152){
						$errors[] = 'Attached file is too large. File size cannot exceed 2MB.';
						$required[] = 'file';
					}else if($ext != 'pdf'){
						$errors[] = 'Invalid file type. Only PDF Documents are accepted.';
						$required[] = 'file';
					}
				}

				//Trim complex content
				$_POST['texteditor'] = (str_replace('<p>&nbsp;</p>', '', $_POST['texteditor']));
				
				//Valid submission
				if(empty($errors)){
					
					//Upload file
					$file = ($_POST['old_file'] != '' ? $_POST['old_file'] : NULL);
					if(!empty($_FILES['file']['name'])){
						$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
						$filename = clean_url($_POST['title']).'-'.date("ymdhis").'.'.$ext;

						// include("modules/classes/FileUpload.class.php");
						// $fileUpload = new FileUpload();
						// $fileUpload->load($_FILES['file']['tmp_name']);
						// $fileUpload->save($filedir, $newname);
						// if(file_exists($filedir.$newname)){
						// 	$file = $newname;
						// 	if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
						// 		unlink($filedir.$_POST['old_file']);
						// 	}
						// }
						foreach ($file_fields as $field) {
								$$field = NULL;
							
								if (!empty($_FILES[$field]['name'])) {
									// Check for errors during upload
									if ($_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
										$errors[] = "Error uploading file: " . $_FILES[$field]['name'] . " - " . $_FILES[$field]['error'];
										continue; // Skip this file and continue with the next
									}
							
									// Attempt to copy the file
									if (!@copy($_FILES[$field]['tmp_name'], $filedir . $filename)) {
										$errors[] = "Failed to upload the file: " . $_FILES[$field]['name'];
									} else {
										// Validate file exists
										$$field = check_file($filename, $filedir) ?: NULL;
							
										// Add to array for email attachment
										if ($$field) {
											$attachments[] = $filedir . $filename;
										}
									}
								}
								
						}

					}else{
						if(isset($_POST['deletefile']) && $_POST['old_file'] != ''){
							if(file_exists($filedir.$_POST['old_file'])) {
								unlink($filedir.$_POST['old_file']);
							}
							$file = NULL;
						}
					}

					if(is_null($file) || file_exists($filedir.$file)){

						// $db->new_transaction();
						
						$params = array(
							$_POST['title'], 
							clean_url($_POST['title']),
							$_POST['category_id'], 
							$_POST['facility_id'], 
							$_POST['texteditor'], 
							$_POST['salary'],
							$_POST['duration'],
							$file,
							$_POST['first_name'],
							$_POST['last_name'],
							$_POST['email'],
							formatPhoneNumber($_POST['phone']),
							date('Y-m-d', strtotime($_POST['posted_date'])),
							date('Y-m-d', strtotime($_POST['closing_date'])),
							$_POST['public'],
							(isset($_POST['showhide']) ? 1 : 0),
							date('Y-m-d H:i:s'),
							$career_id	
						);
						$query = $db->query("UPDATE `careers` SET `title` = ?, `page` = ?, `category_id` = ?, `facility_id` = ?, `content` = ?, `salary` = ?, `duration` = ?, `file_name` = ?, `first_name` = ?, `last_name` = ?, `email` = ?, `phone` = ?, `posted_date` = ?, `closing_date` = ?, `public` = ?, `showhide` = ?, `last_updated` = ? WHERE `career_id` = ?", $params);

						//Save class
						$delete = $db->query("DELETE FROM `career_classes` WHERE `career_id` = ?", array($career_id));
						$_POST['class_id'] = ($_POST['class_id'] ?? array());
						foreach($_POST['class_id'] as $class_id){
							$query = $db->query("INSERT INTO `career_classes`(`career_id`, `class_id`) VALUES(?,?) ON DUPLICATE KEY UPDATE `class_id` = ?", array($career_id, $class_id, $class_id));
						}
						
						if(!$db->error()){
							$db->commit();
							$alert = $Account->alert('Job posting has been updated. Go <a href="'.$_accountpages['job-postings']['page_url'].'">back to all</a>'.(isset($_POST['showhide']) ? '' : ', or <a href="'.$_sitepages['job-postings']['page_url'] .$career['page']. '-' .$career_id. '/'.'">view your job post</a>').'.', true);
						}else{
							$errors[] = 'Unable to update job posting. '.$db->error();
						}
					}else{
						$errors[] = 'Unable to upload file. Please try again.';
						$required[] = 'file';
					}
					
				}else{
				$alert = $Account->alert(implode('<br/>', $errors), false);
				}

			//Delete job posting
			}else if(isset($_POST['delete'])){
				
				// $db->new_transaction();
				$query = $db->query("DELETE from `careers` WHERE `career_id` = ? && `account_id` = ?", array($career_id, USER_LOGGED_IN));
				$query = $db->query("SELECT `resume` FROM `applications` WHERE `resume` IS NOT NULL && `career_id` = ?", array($career_id));
				if($db->num_rows()){
					$resumes = $db->fetch_array();
				}
				if(!$db->error()){
					$db->commit();	
					
					//Clean up files
					if($_POST['old_file'] != '' && file_exists($filedir.$_POST['old_file'])){
						unlink($filedir.$_POST['old_file']);
					}
					if(isset($resumes) && !empty($resumes)){
						foreach($resumes as $file){
							if(file_exists('docs/resumes/'.$file['resume'])){
								unlink('docs/resumes/'.$file['resume']);
							}
						}
					}
					$_SESSION['deleted'] = true;
					header('Location:'.$_sitepages['my-job-postings']['page_url']);
					exit();	
				}else{
					$errors[] = 'Unable to delete job posting. '.$db->error();
				}
			}

		//Career not found
		}else{
			$notfound = true;
			$errors[] = 'Job posting not found. Please select from the list below.';
		}
	}

} 

?>