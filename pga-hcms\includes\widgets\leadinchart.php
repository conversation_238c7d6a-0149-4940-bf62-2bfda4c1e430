<?php 


// Fetch all dataset labels
$labels   = ['Impression', 'Link Click', 'Submission'];
$datasets = array_fill_keys($labels, []);

// Get last 30 days, fill each dataset with dates
$period = new DatePeriod(new DateTime('-29 days'), new DateInterval('P1D'), new DateTime('+1 day'));
foreach ($period as $i => $date) $dates[] = $date->format('m/d');
foreach ($datasets as $label => $stats) $datasets[$label] = array_fill_keys($dates, 0);

// Fill conversions datasets
foreach($conversions as $stat) {
	$label = $stat['event'] != 'open' ? ucwords(str_replace('-', ' ', $stat['event'])) : 'Impression';
	$date = date('m/d', strtotime($stat['timestamp']));
	if(isset($datasets[$label][$date])) {
		$datasets[$label][$date]++;
	}
}

?>
<div class="panel">
	<div class="panel-header">Conversions in Last 30 Days
		<a class="panel-toggle fas fa-chevron-up"></a>
	</div>
	<div class="panel-content chart">
		<table class="chart-stats" cellspacing="0" cellpadding="15" border="0">
			<tbody>
				<tr>

				<?php
				// Display label plurals
				foreach ($datasets as $label => $dataset) {
					$label = substr($label, -1) == 's' ? $label : (substr($label, -1) == 'y' ? substr($label, 0, -1).'ies' : $label.'s');
					echo '<th class="center"><div>Total<br/> '.$label.' <b>'.array_sum($dataset).'</b></div></th>';
				}
				?>

				</tr>
			</tbody>
		</table>

		<div class="chart-container">
			<canvas id="dashboard-chart" height="200" data-source='<?php echo json_encode($datasets); ?>'></canvas>
		</div>
	</div>
</div>
<script src="<?php echo $path; ?>includes/plugins/chartjs/dist/chart.min.js"></script>