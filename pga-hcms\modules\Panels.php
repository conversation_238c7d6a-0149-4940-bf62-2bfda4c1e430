<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

if(SECTION_ID == $_cmssections['panels']){

	//Define vars
	$record_db      = 'pages_panels';
	$record_id      = 'panel_id';
	$record_name	= 'Panel';

	//Validation
	$errors   = false;
	$required = [];
	$required_fields = ['title'];

	//Links (note - if the module does not exist, this will return a 404)
	$promospage     = $CMSBuilder->get_section($_cmssections['promo_boxes']);
	$staffpage      = $CMSBuilder->get_section($_cmssections['staff']);

	//
	$partnerspage      = $CMSBuilder->get_section($_cmssections['partners']); // <-- ADD LINK SECTION

	//Image Uploading
	$imagedir       = '../images/panels/';
	$CMSUploader    = new CMSUploader(); //NOTE: When adding a panel with uploaded images, add the panel type to $panel_croptypes in /modules/Pages.php

	//Page not found
	if(!array_key_exists(PAGE_ID, $pages)){
		$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
		header('Location:' .$mainpage['page_url']);
		exit();
	}

	//No action set
	if(ACTION == ''){
		header('Location:' .$mainpage['page_url']. '?action=edit&item_id=' .PAGE_ID);
		exit();
	}

	//Selected item
	$where = '';
	$params = [PAGE_ID];
	if(ACTION == 'edit' && ITEM_ID != ''){
		$where .= "AND $record_db.$record_id = ? ";
		$params[] = ITEM_ID;
	}

	//Get panels
	$db->query("SELECT * FROM `$record_db` WHERE `page_id` = ? ".$where."ORDER BY `ordering`, `$record_id`", $params);
	$records_arr = $db->fetch_assoc($record_id);

	//Validate image exists
	foreach($records_arr as $panel_id => &$panel){
		$panel['image']        = check_file($panel['image'], $imagedir);
		$panel['image_mobile'] = check_file($panel['image_mobile'], $imagedir);

		unset($panel);
	}

	//Set variables based on panel type
	$panel_type = ($_POST['panel_type'] ?? false) ?: ($records_arr[ITEM_ID]['panel_type'] ?? 'standard');


	//Get ctas
	$db->query("SELECT * FROM `pages_cta` ORDER BY `title`");
	$ctas = $db->fetch_assoc('cta_id');

	//Get partners
	$db->query("SELECT partners.*, partner_categories.name as category FROM partners LEFT JOIN partner_categories ON partner_categories.category_id = partners.category_id ORDER BY partners.ordering, partners.name");
	$partners = $db->fetch_assoc('partner_id'); // <-- QUERY LINKS

	//Get promos
	$db->query("SELECT * FROM `promo_boxes`");
	$promoboxes = $db->fetch_assoc('promo_id');

	//Get galleries
	$db->query("SELECT * FROM `galleries` ORDER BY `name` ASC");
	$galleries = $db->fetch_assoc('gallery_id');

	//Get faq categories
	$db->query("SELECT `faq_categories`.* FROM `faq_categories` ORDER BY `ordering` ASC");
	$faq_categories = $db->fetch_assoc('category_id');

	//Get blog categories
	$db->query("SELECT `blog_categories`.* FROM `blog_categories` ORDER BY `ordering` ASC");
	$blog_categories = $db->fetch_assoc('category_id');

	//Get staff members
	$db->query("SELECT `staff`.*, `staff_categories`.`name` as category FROM `staff` LEFT JOIN `staff_categories` ON `staff_categories`.`category_id` = `staff`.`category_id` ORDER BY `ordering` ASC, `name` ASC");
	$staff_members = $db->fetch_assoc('staff_id');

	//Get reviews
	$db->query("SELECT * FROM `reviews` WHERE `content` != ? AND `content` IS NOT NULL ORDER BY `client` ASC, `rating` DESC", array(''));
	$reviews = $db->fetch_assoc('review_id');
	foreach($reviews as $review_id => $review){
		//Create an option label
		$reviews[$review_id]['label'] = ($review['client'] ?: 'Anonymous') . (!empty($review['rating']) ? ' (' . floatval($review['rating']) . '-Star Rating)' : '');
	}

	//Get forms
	$db->query("SELECT * FROM `forms` ORDER BY `form_name`");
	$forms = $db->fetch_assoc('form_id');


	//Selected item
	if(ACTION == 'edit'){
		if(array_key_exists(ITEM_ID, $records_arr)){

			$row = $records_arr[ITEM_ID];

			//Grab dynamic panel data
			$db->query("SELECT * FROM `pages_panels_tabs` WHERE `$record_id` = ? ORDER BY `ordering`", array(ITEM_ID));
			$records_arr[ITEM_ID]['page_tabs'] = $db->fetch_array();

			$db->query("SELECT promo_id FROM `pages_panels_promo` WHERE `$record_id` = ? ORDER BY `ordering`", array(ITEM_ID));
			$records_arr[ITEM_ID]['page_promos'] = array_column($db->fetch_array(), 'promo_id');

			$db->query("SELECT staff_id FROM `pages_panels_staff` WHERE `$record_id` = ? ORDER BY `ordering`", array(ITEM_ID));
			$records_arr[ITEM_ID]['page_staff'] = array_column($db->fetch_array(), 'staff_id');

			$db->query("SELECT faq_category_id FROM `pages_panels_faqs` WHERE `$record_id` = ? ORDER BY `ordering`", array(ITEM_ID));
			$records_arr[ITEM_ID]['page_faqs'] = array_column($db->fetch_array(), 'faq_category_id');

			$db->query("SELECT `pages_panels_reviews`.`review_id` FROM `pages_panels_reviews` LEFT JOIN `reviews` ON `reviews`.`review_id` = `pages_panels_reviews`.`review_id` WHERE `pages_panels_reviews`.`$record_id` = ? AND `reviews`.`content` != ? AND `reviews`.`content` IS NOT NULL ORDER BY `pages_panels_reviews`.`ordering`", array(ITEM_ID, ''));
			$records_arr[ITEM_ID]['page_reviews'] = array_column($db->fetch_array(), 'review_id');

			//

			$db->query("SELECT `partner_id` FROM `pages_panels_partners` WHERE `panel_id` = ? ORDER BY `ordering`", array(ITEM_ID));
			$records_arr[ITEM_ID]['page_partners'] = array_column($db->fetch_array(), 'partner_id'); // <-- ADD LINKS TO DATA ARRAY
			//

		//Not found
		}else{
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .$mainpage['page_url']. '?action=edit&item_id=' .PAGE_ID);
			exit();
		}
	}


	//Delete item
	if(isset($_POST['delete'])){
		$db->query("DELETE FROM `$record_db` WHERE `$record_id` = ?", array(ITEM_ID));
		if(!$db->error()){

			//Loop through all panel types that have an image
			foreach($panel_croptypes as $crop_type_key){

				//Point class to correct parameters
				$CMSUploader->set_crop_type($crop_type_key, $imagedir);
				$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
			}

			$CMSBuilder->set_system_alert('Page panel was successfully deleted.', true);

		}else{
			$CMSBuilder->set_system_alert('Unable to delete record.', false);
		}

		//Send back to main page
		header('Location: ' .$mainpage['page_url']. '?action=edit&item_id=' .PAGE_ID);
		exit();


	//Save item
	}else if(isset($_POST['save'])){

		//Set toggles
		$_POST['showhide'] = !isset($_POST['showhide']);
		$_POST['show_title'] = isset($_POST['show_title']);
		$dynamic = ITEM_ID != '' && $records_arr[ITEM_ID]['deletable'] == 0;

		if(ITEM_ID == ''){
			$row['deletable'] = 1;
		}else if($dynamic){
			$panel_type = $records_arr[ITEM_ID]['panel_type'];
		}

		//Required fields validation
		foreach($required_fields as $field) {
			if(($_POST[$field] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = $field;
			}
		}
		if (!$dynamic) {
			if($panel_type == 'cta' && empty($_POST['cta_id'])){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = 'cta_id';
			}
			if($panel_type == 'gallery' && empty($_POST['gallery_id'])){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = 'gallery_id';
			}
			if($panel_type == 'faq' && empty($_POST['page_faqs'])){
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = 'page_faqs';
			}
			if($panel_type == 'reviews' && empty($_POST['page_reviews'])){
				$errors[] = 'Please select at least one review.';
				$required[] = 'page_reviews';
			}
			if($panel_type == 'staff' && empty($_POST['page_staff'])){
				$errors[] = 'Please select at least one staff member.';
			}
			if($panel_type == 'promo' && empty($_POST['page_promos'])){
				$errors[] = 'Please select at least one promo box.';
			}
			//
			if($panel_type == 'partners' && empty($_POST['page_partners'])){
				$errors[] = 'Please select at least 1 sponsor.';
			}
			//
			if($panel_type == 'form' && empty($_POST['form_id'])){
			$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
			array_push($required, 'form_id');
			}
		}

		//Image validation
		if(in_array($panel_type, $panel_croptypes) && ($_FILES['image']['size'] ?? 0) > $_max_filesize['bytes']){
			$errors[1] = 'Image filesize is too large. Cannot exceed ' .$_max_filesize['megabytes']. '.';
			$required[] = 'image';
		}
		if(in_array($panel_type, $panel_croptypes) && ($_FILES['image_mobile']['size'] ?? 0) > $_max_filesize['bytes']){
			$errors[1] = 'Image filesize is too large. Cannot exceed ' .$_max_filesize['megabytes']. '.';
			$required[] = 'image_mobile';
		}

		//Valid video URL
		$regex = '/(?:youtube\.com\/watch\?v=|youtu\.be\/)[^\&]{11}|vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|)(\d+)(?:|\/\?)/';
		if($panel_type == 'side' && $_POST['video_url'] && !preg_match($regex, $_POST['video_url'])) {
			$errors[] = 'Please upload a valid Youtube or Vimeo URL.';
			array_push($required, 'video_url');
		}

		//Video URL requires image
		if($panel_type == 'side' && $_POST['video_url'] && empty($records_arr[ITEM_ID]['image']) && empty($_FILES['image']['name'])){
			$errors[] = 'Videos also require a poster image for mobile.  Please select an image to upload.';
			$required[] = 'image';
		}

		//Format content
		$_POST['content'] = (isset($_POST['TINYMCE_Editor']) ? str_replace("<p>&nbsp;</p>", "", $_POST['TINYMCE_Editor']) : NULL);
		$_POST['content'] = ($panel_type == 'side' || $panel_type == 'promo' || $panel_type == 'gallery' || $panel_type == 'faqs' || $panel_type == 'staff' || $panel_type == 'reviews' || $panel_type == 'parallax' ? $_POST['simple_content'] : $_POST['content']);

		//Contact panel content
		if($panel_type == 'contact-panel'){
			$_POST['content'] = $_POST['TINYMCE_Editor1'];
		}
		//


		//Save post data for tabs
		$_POST['page_tabs'] = array();
		$pages_tabs = $_POST['tab_id'] ?? [];
		foreach($pages_tabs as $key=>$tab_id){
			if (($_POST['panel_title'][$key] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = 'panel_title'.$key;
			}
			if (($_POST['panel_content'][$key] ?? '') === '') {
				$errors[0] = 'Please fill out all the <span class="required">*</span> required fields.';
				$required[] = 'panel_content'.$key;
			}

			if($_POST['panel_title'][$key] != '' || $_POST['panel_content'][$key] != ''){
				$_POST['page_tabs'][] = array(
					'tab_id' 	=> $tab_id,
					'title' 	=> $_POST['panel_title'][$key],
					'page' 		=> clean_url($_POST['panel_title'][$key]),
					'showhide' 	=> ($_POST['panel_showhide'][$key] ?? 0),
					'ordering' 	=> $_POST['panel_ordering'][$key],
					'content' 	=> str_replace("<p>&nbsp;</p>", "", $_POST['panel_content'][$key])
				);
			}
		}

		if(!$errors){

			//Upload images for panels with crop types
			$images = [];
			$crop_type_key = $panel_type;
			if(!empty($_croptypes[$crop_type_key])){

				//Point class to correct parameters
				try{
					$CMSUploader->set_crop_type($crop_type_key, $imagedir);

					//Delete old images
					if(isset($_POST['deleteimage']) || isset($_POST['deleteimage_mobile'])){
						$CMSUploader->bulk_delete([
							'image'        => isset($_POST['deleteimage']) ? $records_arr[ITEM_ID]['image'] : false,
							'image_mobile' => isset($_POST['deleteimage_mobile']) ? $records_arr[ITEM_ID]['image_mobile'] : false,
						]);
					}

					//Upload new images
					try{
						$images = $CMSUploader->bulk_upload($_POST['title'], $records_arr[ITEM_ID] ?? []);
					}catch(Exception $e){
						$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
					}

				}catch(Exception $e){
					trigger_error($e->getMessage());
				}

			}

			//Insert to db
			$params = [
				'theme'         => $_POST['theme'],
				'panel_type'    => $panel_type,

				'title'         => $_POST['title'],
				'show_title'    => $_POST['show_title'],
				'subtitle'      => $_POST['subtitle'],
				'content'       => $_POST['content'],

				'url'           => $_POST['url'] ?? NULL,
				'url_text'      => $_POST['url_text'] ?? NULL,
				'url_target'    => $_POST['url_target'] ?? 0,

				'cta_id'        => $_POST['cta_id'] ?: NULL,

				'form_id'		=> $_POST['form_id'] ?: NULL, 

				'gallery_id'    => $_POST['gallery_id'] ?: NULL,
				'gallery_limit' => $_POST['gallery_limit'] ?: NULL,

				'blog_category_id' => $_POST['blog_category_id'] ?: NULL,

				'promo_type'    => ($_POST['promo_type'] ?? 'standard'),

				'image'         => $images['image'] ?? NULL,
				'image_mobile'  => $images['image_mobile'] ?? NULL,
				'image_alt'     => $_POST['image_alt'] ?? NULL,

				'video_url'                  => $_POST['video_url'],
				'side_image_position'        => $_POST['side_image_position'],
				'side_image_position_mobile' => $_POST['side_image_position_mobile'],

				'ordering'      => $_POST['ordering'],
				'showhide'      => $_POST['showhide']
			];

			$db->new_transaction();
			$db->insert($record_db, [$record_id => ITEM_ID, 'page_id' => PAGE_ID] + $params, $params);
			if(!$db->error()){
				$item_id = ITEM_ID ?: $db->insert_id();

				//Delete tabs
				$panel_tab_ids = (isset($_POST['page_tabs']) ? array_filter(array_values(array_column($_POST['page_tabs'], 'tab_id'))) : array());
				$db->query("DELETE FROM `pages_panels_tabs` WHERE `$record_id` = ?".(!empty($panel_tab_ids) ? " AND `tab_id` NOT IN (".implode(",", $panel_tab_ids).")" : ""), array($item_id));

				//Update tabs
				foreach($_POST['page_tabs'] as $tab){
					$params = array(
						$tab['tab_id'],
						$item_id,
						$tab['title'],
						$tab['page'],
						$tab['content'],
						$tab['showhide'],
						$tab['ordering'],
						$tab['title'],
						$tab['page'],
						$tab['content'],
						$tab['showhide'],
						$tab['ordering']
					);

					$db->query("INSERT INTO `pages_panels_tabs` (`tab_id`, `$record_id`, `title`, `page`, `content`, `showhide`, `ordering`) VALUES(?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `title` = ?, `page` = ?, `content` = ?, `showhide` = ?, `ordering` = ?", $params);
				}

				//Grab promo boxes
				$promo = $_POST['page_promos'] ?? [];

				//Delete non-selected promo boxes
				$params = array_merge([$item_id], $promo);
				$filter = implode(",", array_fill_keys($promo, "?"));
				$db->query("DELETE FROM `pages_panels_promo` WHERE `$record_id` = ?".($filter ? " AND promo_id NOT IN (".$filter.")" : ""), $params);

				//Insert/Update new promo boxes
				foreach($promo as $ordering => $promo_id){
					$params = [
						$item_id,
						$promo_id,
						++$ordering,

						$item_id,
						$ordering
					];

					$db->query("INSERT INTO `pages_panels_promo` (`$record_id`, `promo_id`, `ordering`) VALUES (?,?,?) ON DUPLICATE KEY UPDATE `$record_id` = ?, `ordering` = ?", $params);
				}

				//Grab staff
				$staff = $_POST['page_staff'] ?? [];

				//Delete non-selected staff
				$params = array_merge([$item_id], $staff);
				$filter = implode(",", array_fill_keys($staff, "?"));
				$db->query("DELETE FROM `pages_panels_staff` WHERE `$record_id` = ?".($filter ? " AND staff_id NOT IN (".$filter.")" : ""), $params);

				//Insert/Update new staff
				foreach($staff as $ordering => $staff_id){
					$params = [
						$item_id,
						$staff_id,
						++$ordering,

						$item_id,
						$ordering
					];

					$db->query("INSERT INTO `pages_panels_staff` (`$record_id`, `staff_id`, `ordering`) VALUES (?,?,?) ON DUPLICATE KEY UPDATE `$record_id` = ?, `ordering` = ?", $params);
				}

				// Grab partners
				$partners = $_POST['page_partners'] ?? [];

				// Delete non-selected partners
				$params = array_merge([$item_id], $partners);
				$filter = implode(",", array_fill_keys($partners, "?"));
				$db->query("DELETE FROM `pages_panels_partners` WHERE `panel_id` = ?".($filter ? " AND partner_id NOT IN (".$filter.")" : ""), $params);

				// Insert/Update new partners
				foreach ($partners as $ordering => $partner_id) {
					$params = [
						$item_id,
						$partner_id,
						++$ordering,

						$item_id,
						$ordering
					];

					$db->query("INSERT INTO `pages_panels_partners` (`panel_id`, `partner_id`, `ordering`) VALUES (?,?,?) ON DUPLICATE KEY UPDATE `panel_id` = ?, `ordering` = ?", $params);
				}  // <-- SAVE LINKS TO PANEL DB
				//

				//Grab FAQ categories
				$faqs = (!empty($_POST['page_faqs']) ? [$_POST['page_faqs']] : []);

				//Delete non-selected FAQ categories
				$params = array_merge([$item_id], $faqs);
				$filter = implode(",", array_fill_keys($faqs, "?"));
				$db->query("DELETE FROM `pages_panels_faqs` WHERE `$record_id` = ?".($filter ? " AND faq_category_id NOT IN (".$filter.")" : ""), $params);

				//Insert/Update new FAQ categories
				foreach($faqs as $ordering => $faq_category_id){
					$params = [
						$item_id,
						$faq_category_id,
						++$ordering,

						$item_id,
						$ordering
					];

					$db->query("INSERT INTO `pages_panels_faqs` (`$record_id`, `faq_category_id`, `ordering`) VALUES (?,?,?) ON DUPLICATE KEY UPDATE `$record_id` = ?, `ordering` = ?", $params);
				}

				//Grab reviews
				$reviews = (isset($_POST['page_reviews']) ? array_unique($_POST['page_reviews']) : []);

				//Delete non-selected reviews
				$params = array_merge([$item_id], $reviews);
				$filter = implode(",", array_fill_keys($reviews, "?"));
				$db->query("DELETE FROM `pages_panels_reviews` WHERE `$record_id` = ?".($filter ? " AND review_id NOT IN (".$filter.")" : ""), $params);

				//Insert/Update new reviews
				foreach($reviews as $ordering => $review_id){
					$params = [
						$item_id,
						$review_id,
						++$ordering,

						$item_id,
						$ordering
					];

					$db->query("INSERT INTO `pages_panels_reviews` (`$record_id`, `review_id`, `ordering`) VALUES (?,?,?) ON DUPLICATE KEY UPDATE `$record_id` = ?, `ordering` = ?", $params);
				}

				//Commit changes
				if(!$db->error()){
					$db->commit();

					if(!$CMSUploader->crop_queue()){
						$CMSBuilder->set_system_alert('Page panel was successfully saved.', true);
						header('Location: '.$mainpage['page_url'].'?action=edit&item_id='.PAGE_ID);
						exit();
					}else{
						$redirect = $mainpage['page_url']. "?action=edit&item_id=" .PAGE_ID;
					}

				//Transaction error
				}else{
					$db->rollback();
					$CMSBuilder->set_system_alert('Unable to update panel.', false);
				}

			//Insert error
			}else{
				$CMSBuilder->set_system_alert('Unable to update record.', false);
			}

		//Error reporting
		}else{
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}
		}


	//Determine crop type key before handling images
	}else{
		$item_id = $_POST['item_id'] ?? false ?: ITEM_ID;
		$crop_type_key = $records_arr[$item_id]['panel_type'] ?? false;

		//Point class to correct parameters
		if(!empty($_croptypes[$crop_type_key])){
			$CMSUploader->set_crop_type($crop_type_key, $imagedir);

			if(isset($_POST['recrop'])){
				$redirect = $mainpage['page_url'].'?action=edit&item_id='.PAGE_ID;
			}else{
				$redirect = $_POST['redirect'] ?? false ?: $mainpage['page_url'];
			}

			//Handle images
			include('modules/CropImages.php');
		}
	}
}

?>