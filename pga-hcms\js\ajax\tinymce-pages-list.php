<?php

//System files
include("../../includes/config.php");
include("../../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

if(USER_LOGGED_IN) {
	$pages = get_site_pages(true, true, true);

	print json_encode(array_values($pages)); // Since $pages uses a numeric value, the page IDs, as its array keys the JSON object will be ordered by page ID. To preserve the order of the pages, only the values of the array must be encoded; array_values has to be used to preserve the order of pages.
}
?>