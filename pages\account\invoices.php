<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Display all
if(ACTION == '' || empty($invoice)){

	$search_term = trim($_GET['search'] ?? '');

	$html .= $page['page_panels'][$panel_id]['content'];

	//Search form
	$html .= '<div class="invoices-container"><form name="search-form" id="search-bar" action="" method="get" class="search-bar-invoices">
		<input type="text" name="search" class="input" value="' .(isset($_GET['search']) ? $_GET['search'] : ''). '" placeholder="Search" />
		<button type="submit" class="button solid"><i class="fa fa-search"></i></button>
		' .(isset($_GET['search']) && trim($_GET['search']) != '' ? '<a href="' .$page['page_url']. '" class="fa fa-times-circle"></a>' : ''). '
	</form>';


	//Invoices
	$html .= '<table cellpadding="10" cellspacing="0" border="0" width="100%">';
	if(!empty($invoices)){
		$html .= '<tr>
			<th align="left">Invoice No.</th>
			<th align="left">Invoice Date</th>
			<th align="left">Payment</th>
			<th align="left">Total</th>
			<th width="120px">Action</th>
		</tr>';
		foreach($invoices as $invoice){
			$html .= '<tr>
				<td><a href="' .$page['page_url']. '?action=edit&id=' .$invoice['invoice_id']. '">' .$invoice['invoice_number']. '</a></td>
				<td>' .date('M j, Y', strtotime($invoice['invoice_date'])). '</td>
				<td>';
				if($invoice['paid'] == '1'){
					$html .= 'Processed';
				}else{
					if(!empty($invoice['due_date']) && $invoice['due_date'] <= date("Y-m-d")){
						$html .= '<span class="color-red">Overdue</span>';
					}else{
						$html .= 'Pending';
					}
				}
				$html .= '</td>
				<td>$' .number_format($invoice['invoice_total'], 2). '</td>
				<td><a href="' .$page['page_url']. '?action=edit&id=' .$invoice['invoice_id']. '" class="button simple"><i class="fa fa-pencil"></i></a></td>
			</tr>';
		}
	}else{
		$html .= '<tr>
			<td class="nobg" colspan="5">No invoices found' .(isset($_GET['search']) && trim($_GET['search']) != '' ? ' matching `<strong>' .$_GET['search']. '</strong>`' : ''). '.</td>
		</tr>';
	}

	//Pager
	if($totalresults > 0){
		$searchterm = (isset($_GET['search']) ? $_GET['search'] : '');
		$html .= '<tr>
			<td class="pager" colspan="5">
				<small>';
					$html .= 'Displaying '.($pg == 'all' ? '1 - '.$totalresults : (1+($limit*($pg-1))).' - '.(count($invoices)+($limit*($pg-1)))).' (of '.$totalresults.' Total)<br />';
					if($totalresults > $limit && $pg != 'all'){
						$tagend = round($totalresults % $limit, 0);
						$splits = round(($totalresults - $tagend)/$limit, 0);
						$num_pages = ($tagend == 0 ? $splits : $splits+1);
						$pos = $pg;
						$startpos = ($pos*$limit)-$limit;

						$html .= ($pos > 1 ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos-1).'">&lsaquo; Prev</a> ' : '');
						for($i=1; $i<=$num_pages; $i++){
							$html .= ($i != $pos ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.$i.'">'.$i.'</a> ' : '<span><u>'.$i.'</u></span> ');
						}
						$html .= ($pos < $num_pages ? '<a href="'.$page['page_url'].'?search='.urlencode($searchterm).'&pg='.($pos+1).'">Next &rsaquo;</a> ' : '');
					}
				$html .= '</small>
			</td>
		</tr>';
	}

	$html .= '</table>';
	$html .= '<div><a href="'.htmlspecialchars($path.'account/', ENT_QUOTES, 'UTF-8').'" class="button primary red back-button"> BACK TO MY ACCOUNT <span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a></div>';
	$html .='</div>';


//Selected invoice
}else if(ACTION == 'edit'){

	//Panel title
	$page['page_panels'][$panel_id]['title'] = 'Invoice';
	$page['page_panels'][$panel_id]['show_title'] = true;

	//Invoice
	$html .= '<div class="invoice-details-container">';
	$html .= '<h4>Invoice Information</h4>';
	$html .= '<div class="static-info-box form-grid invoice-info">
	<div>
		<label>Invoice No:</label>
		<span>'.$invoice['invoice_number'].'</span>
		<br><br>
		<label>Due Date:</label>
		<span>' .date("M j, Y", strtotime($invoice['due_date'])). '</span>
	</div>
	<div>
		<label>Invoice Date:</label>
		<span>' .(!empty($invoice['due_date']) ? date("M j, Y", strtotime($invoice['invoice_date'])) : 'None'). '</span>
		<br><br>
		<label>Billed To:</label>
		<span>'.$invoice['bill_to'].'</span>
	</div>';


	if(!empty($invoice['hio_id'])){
		$html .= '<div><label>Hole In One:</label> <span><a href="' .$_sitepages['hole_in_one']['page_url']. '?action=edit&id=' .$invoice['hio_id']. '">' .$invoice['event_name']. '</a></span><br><br>
		<label>Download Invoice:</label>
		<span><a href="' .$page['page_url']. '?action=edit&id=' .ITEM_ID. '&download=true"><i class="fa fa-file-pdf-o"></i>&nbsp; Download Invoice</a></span></div>';
	} else {
		$html .= '<div><label>Billed To:</label>
		<span><a href="' .$page['page_url']. '?action=edit&id=' .ITEM_ID. '&download=true"></i> Download Invoice</a></span></div>';
	}

	$html .= '</div>';

	$html .= '<h4>Contact Information</h4>';
	$html .= '<div class="static-info-box form-grid invoice-info">
	<div>
		<label>Contact Name:</label>
		<span>' .$invoice['first_name'].' '.$invoice['last_name'].'</span>
	</div>
	<div>
		<label>Email Address:</label>
		<span><a href="mailto:' .$invoice['email']. '">' .$invoice['email'].'</a></span>
	</div>
	<div>
		<label>Phone Number:</label>
		<span><a href="tel://' .$invoice['phone']. '">' .$invoice['phone'].'</a></span>
	</div>';
	$html .= '</div>';

	// $html .= '<table cellpadding="0" cellspacing="0" border="0" width="100%" class="nobgs noresponsive noborder">
	// 	<tr>
	// 		<td width="200px">Invoice No:</td>
	// 		<td>' .$invoice['invoice_number']. '</td>
	// 	</tr>
	// 	<tr>
	// 		<td>Invoice Date:</td>
	// 		<td>' .date("M j, Y", strtotime($invoice['invoice_date'])). '</td>
	// 	</tr>
	// 	<tr>
	// 		<td>Due Date:</td>
	// 		<td>' .(!empty($invoice['due_date']) ? date("M j, Y", strtotime($invoice['due_date'])) : 'None'). '</td>
	// 	</tr>
	// 	<tr>
	// 		<td>Billed To:</td>
	// 		<td>' .$invoice['bill_to'].' </td>
	// 	</tr>';
	// 	if(!empty($invoice['hio_id'])){
	// 		$html .= '<tr>
	// 			<td valign="top">Hole In One:</td>
	// 			<td><a href="' .$_sitepages['hole_in_one']['page_url']. '?action=edit&id=' .$invoice['hio_id']. '">' .$invoice['event_name']. '</a></td>
	// 		</tr>';
	// 	}
	// $html .= '</table>';

	// //Download
	// $html .= '<p><a href="' .$page['page_url']. '?action=edit&id=' .ITEM_ID. '&download=true"><i class="fa fa-file-pdf-o"></i>&nbsp; Download Invoice</a></p>';

	// //Contact
	// $html .= '<h4>Contact Information</h4>
	// 	<table cellpadding="0" cellspacing="0" border="0" width="100%" class="nobgs noresponsive noborder">
	// 	<tr>
	// 		<td width="200px">Contact Name:</td>
	// 		<td>' .$invoice['first_name'].' '.$invoice['last_name']. '</td>
	// 	</tr>
	// 	<tr>
	// 		<td>Email Address:</td>
	// 		<td><a href="mailto:' .$invoice['email']. '">' .$invoice['email'].'</a></td>
	// 	</tr>
	// 	<tr>
	// 		<td>Phone Number:</td>
	// 		<td><a href="tel://' .$invoice['phone']. '">' .$invoice['phone'].'</a></td>
	// 	</tr>
	// </table>';

	//Comments
	if(trim($invoice['comments']) != ''){
		$html .= '<p>' .nl2br($invoice['comments']). '</p>';
	}

	//Display totals
	$html .= '<table cellpadding="10" cellspacing="0" border="0" width="25%" class="noresponsive">
		<tr class="display-totals">
			<td align="right">Amount:</td>
			<td class="right" width="120px">$' .number_format($invoice['invoice_total']-$invoice['taxes'], 2). '</td>
		</tr>';
		$html .= '<tr class="display-totals">
			<td align="right">Taxes:</td>
			<td class="right">$' .number_format($invoice['taxes'], 2). '</td>
		</tr>';
		$html .= '<tr class="display-totals">
			<td align="right"><h6>Total:</h6></td>
			<td class="right"><h6>$' .number_format($invoice['invoice_total'], 2). '</h6></td>
		</tr>
	</table>';

	//Search form + payment + refund tabs
	$html .= '<div class="search-form-payment-refund-container">';
	$html .= '<div class="invoices-container"><form name="search-form" id="search-bar" action="" method="get" class="search-bar-invoice">
			<input type="text" name="search" class="input" value="' .(isset($_GET['search_payments']) ? $_GET['search_payments'] : ''). '" placeholder="Search" />
			<button type="submit" class="button solid"><i class="fa fa-search"></i></button>
			' .(isset($_GET['search_payments']) && trim($_GET['search_payments']) != '' ? '<a href="' .$page['page_url']. '" class="fa fa-times-circle"></a>' : ''). '</form>
		</div>';

		$html .= '<div class="content-tabs form-tabs clear clearfix" data-tabs-limit="5">
			<div class="tabs-nav-wrapper clearfix">
				<ul class="tabs-nav clearfix">
					<li><a href="#payments">Payments</a></li>
					' .(!empty($invoice['refunds']) ? '<li><a href="#refunds">Refunds</a></li>' : ''). '
				</ul>
			</div>
		</div>
	</div>';
	////

	//Transactions
	$html .= '<div class="content-tabs form-tabs clear clearfix" data-tabs-limit="5">';
		// $html .= '<nav class="tabs-nav-wrapper clearfix">
		// 	<ul class="tabs-nav clearfix">
		// 		<li><a href="#payments">Payments</a></li>
		// 		' .(!empty($invoice['refunds']) ? '<li><a href="#refunds">Refunds</a></li>' : ''). '
		// 	</ul>
		// </nav>';

		//Payments
		$html .= '<div id="payments" class="tabs-panel">
			<table cellpadding="10" cellspacing="0" border="0" width="100%" class="nomargin">';

			$paid = 0;
			if(!empty($invoice['payments'])){
				$html .= '<tr class="header-row">
					<th align="left">No.</th>
					<th align="left">Date</th>
					<th align="left">Type</th>
					<th align="right">Status</th>
					<th align="right" width="120px">Amount</th>
				</tr>';
				foreach($invoice['payments'] as $payment){
					if($payment['status'] == '1'){
						$paid += $payment['amount'];
					}
					$html .= '<tr>
						<td>' .$payment['payment_number']. '</td>
						<td>' .date('M j, Y g:iA', strtotime($payment['payment_date'])). '</td>
						<td>';
						if($payment['payment_type'] == 'Credit Card' && !empty($payment['ccnumber'])){
							$html .= $payment['cctype']." **** **** **** ".$payment['ccnumber']. " &nbsp; " .substr($payment['ccexpiry'], 0, 2)."/".substr($payment['ccexpiry'], -2, 2);
						}else{
							$html .= $payment['payment_type'];
						}
						$html .= '</td>
						<td align="right">' .($payment['status'] == '1' ? 'Processed' : 'Failed'). '</td>
						<td align="right">$' .number_format($payment['amount'], 2). '</td>
					</tr>';
				}
			}else{
				$html .= '<tr>
					<td class="nobg">No transactions to display.</td>
				</tr>';
			}

			$html .= '</table>
			<table cellpadding="10" cellspacing="0" border="0" width="40%" class="noresponsive noborder nobgs nomargin f_right">
				<tr>
					<td align="right"><h6>Total Paid:</h6></td>
					<td class="right" width="120px"><h6>$' .number_format($paid, 2). '</h6></td>
				</tr>';
				if($paid < $invoice['invoice_total']){
					$overdue = (!empty($invoice['due_date']) && $invoice['due_date'] <= date("Y-m-d") ? true : false);
					$html .= '<tr>
						<td align="right"><h5 class="' .($overdue ? 'color-red' : ''). '">' .($overdue ? 'Overdue' : 'Balance Due'). ':</h5></td>
						<td class="right" width="130px"><h5 class="' .($overdue ? 'color-red' : ''). '">$' .number_format($invoice['invoice_total']-$paid, 2). '</h5></td>
					</tr>';
					$html .= '<tr>
						<td colspan="2" align="right"><a href="' .$_sitepages['payments']['page_url']. '?id=i' .$invoice['invoice_id']. '" class="button solid nomargin red primary f_right">Pay Now<span class="top-border"></span><span class="bottom-border"></span><span class="left-border"></span><span class="right-border"></span></a></td>
					</tr>';
				}
			$html .= '</table>
		</div>';

		//Refunds
		if(!empty($invoice['refunds'])){
			$html .= '<div id="refunds" class="tabs-panel">
				<table cellpadding="10" cellspacing="0" border="0" width="100%" class="nomargin">';

				$refunded = 0;
				$html .= '<tr>
					<th align="left">No.</th>
					<th align="left">Date</th>
					<th align="left">Type</th>
					<th align="right">Status</th>
					<th align="right" width="120px">Amount</th>
				</tr>';
				foreach($invoice['refunds'] as $refund){
					if($refund['status'] == '1'){
						$refunded += $refund['amount'];
					}
					$html .= '<tr>
						<td>' .$refund['refund_number']. '</td>
						<td>' .date('M j, Y g:iA', strtotime($refund['refund_date'])). '</td>
						<td>';
						if($refund['refund_type'] == 'Credit Card' && !empty($refund['ccnumber'])){
							$html .= $refund['cctype']." **** **** **** ".$refund['ccnumber']. " &nbsp; " .substr($refund['ccexpiry'], 0, 2)."/".substr($refund['ccexpiry'], -2, 2);
						}else{
							$html .= $refund['refund_type'];
						}
						$html .= '</td>
						<td align="right">' .($refund['status'] == '1' ? 'Processed' : 'Failed'). '</td>
						<td align="right">$' .number_format($refund['amount'], 2). '</td>
					</tr>';
				}
				$html .= '</table>
				<table cellpadding="10" cellspacing="0" border="0" width="100%" class="noresponsive noborder nobgs nomargin">
					<tr>
						<td align="right"><h6>Total Refunded:</h6></td>
						<td class="right" width="120px"><h6>$' .number_format($refunded, 2). '</h6></td>
					</tr>
				</table>
			</div>';
		}

	$html .= '</div>';
	$html .= '</div>';

}

//Set panel content
// $page['page_panels'][$panel_id]['content'] = $html;

?>