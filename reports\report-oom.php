<?php

//Config
include("config.php");

//Get vars
$id = (isset($_GET['id']) ? $_GET['id'] : NULL);
$type = (isset($_GET['report']) ? $_GET['report'] : 'events'); //Default to events played
$year = (isset($_GET['year']) ? $_GET['year'] : date('Y')); //Default this year
$start_date = $year.'-01-01';
$end_date = $year.'-12-31';
if($year == date('Y')){
	$end_date = $year.'-'.date('m-d'); //only go up to today if viewing this year
}

$reports = array();
$reports['events'] = array('title' => 'Events Played');
$reports['money'] = array('title' => 'Overall Money Earned');
$reports['score'] = array('title' => 'Individual Scoring Average');
$reports['points'] = array('title' => 'Overall Points');
$reports['overunder'] = array('title' => 'Individual Average Time Par');

//Categories
$category_names = array();
$categories = (isset($_GET['categories']) ? $_GET['categories'] : array());
if(!empty($categories)){
	$query = $db->query("SELECT `name`, `category_id` FROM `reg_categories`");
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			if(in_array($row['category_id'], $categories)){
				$category_names[] = $row['name'];
			}
		}
	}
}

//Set data array
$records = array();

//If request is valid report
if(array_key_exists($type, $reports)){
	
	//Individual points report
	if(($type == 'points' || $type == 'money') && !empty($id)){
		
		$params = array('Registered', $start_date, $end_date, $id);
		$where = "";
		if(!empty($categories)){
			$where .= "&& (";
			$count = 0;
			foreach($categories as $category){
				$count++;
				$params[] = $category;
				$where .= "`reg_event_categories`.`category_id` = ?".($count < count($categories) ? " || " : "");
			}
			$where .= ") ";
		}
		
		$query = $db->query("SELECT ".
			"`reg_tournament_field`.*, ".
			"`reg_attendees`.`first_name`, `reg_attendees`.`last_name`, `reg_attendees`.`account_id`, `reg_attendees`.`partner_id`, ".
			"`account_profiles`.`profile_id`, ".
			"`facilities`.`facility_name`, ".
			"`membership_types`.`membership_name`, ".
			"`reg_event_categories`.`category_id`, ".
			"`reg_events`.`name` AS `event_name`, ".
			"`reg_occurrences`.`start_date`, `reg_occurrences`.`end_date`, ".
			"IFNULL(`reg_tournament_field`.`points`, 0) AS `points`, ".
			"IFNULL(`reg_tournament_field`.`prize`, 0) AS `prize` ".
		"FROM `reg_attendees` ".
		"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
		"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
		"LEFT JOIN `membership_types` ON `account_profiles`.`membership_id` = `membership_types`.`membership_id` ".
		"LEFT JOIN `reg_event_categories` ON `reg_attendees`.`event_id` = `reg_event_categories`.`event_id` ".
		"LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ".
		"LEFT JOIN `reg_occurrences` ON `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` ".
		"LEFT JOIN `reg_tournament_field` ON `reg_attendees`.`attendee_id` = `reg_tournament_field`.`attendee_id` ".
		"WHERE `reg_attendees`.`reg_status` = ? && `reg_attendees`.`account_id` IS NOT NULL " .($type == 'points' ? "&& `reg_attendees`.`partner_id` IS NULL " : ""). "&& `reg_events`.`role_id` != 8 && (`reg_occurrences`.`start_date` >= ? && `reg_occurrences`.`end_date` <= ?) && `account_profiles`.`profile_id` = ? ".$where.
		"ORDER BY `reg_tournament_field`.`points` DESC" .($type == 'points' ? " LIMIT 5" : ""), $params);
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				
				//If attendee was a partner, get team prize from partner entry
				if($type == 'money' && !empty($row['partner_id'])){
					$params = array($row['partner_id']);
					$query = $db->query("SELECT IFNULL(`team_prize`, 0) AS `team_prize` FROM `reg_tournament_field` WHERE `attendee_id` = ?", $params);
					if($query && !$db->error()){
						$partner = $db->fetch_array();
						$row['team_prize'] = $partner[0]['team_prize'];
					}
				}
				
				//Team prize amount should be divided between team members, except for Pro-Ams
				if($row['category_id'] != 8){
					$row['team_prize'] = ($row['team_prize']/2);
				}else{
					//Partner does not get team money for Pro-Ams
					if(!empty($row['partner_id'])){
						$row['team_prize'] = 0;
					}
				}

				//Total prize money
				$row['total_prize'] = number_format($row['prize']+$row['team_prize'], 2, '.', '');
				
				//Push to records
				$records[] = $row;	
				
			}
		}
		
		//Sort by prize total for money report
		if($type == 'money'){
			foreach($records as $key=>$row){
				$money[$key] = $row['total_prize'];
			}
			array_multisort($money, SORT_DESC, $records);
		}
		
		//Set html
		$html = '<table cellpadding="0" cellspacing="0" width="100%" border="0">
			<tr>
				<td width="120px"><img src="' .$logo. '" width="120px" /></td>
				<td align="center">
					<h2>' .$reports[$type]['title'].' '.$year. (!empty($records) ? ' - '.$records[0]['first_name'].' '.$records[0]['last_name'] : ''). '</h2>
					' .(!empty($category_names) ? '<h4>' .implode('<br />', $category_names). '</h4>' : ''). '
				</td>
				<td width="120px">&nbsp;</td>
			</tr>
		</table><br />';

		$html .= '<table cellpadding="5" cellspacing="1" width="100%" border="0"><tr>
				<th style="' .$css['th']. '">Event</th>
				<th style="' .$css['th']. '">Event Date</th>
				' .($type == 'points' ? '<th style="' .$css['th']. '" width="100px">Points</th>' : ''). '
				<th style="' .$css['th']. '" width="100px">Individual Money</th>
				' .($type == 'money' ? '<th style="' .$css['th']. '" width="100px">Team Money</th>' : ''). '
				' .($type == 'money' ? '<th style="' .$css['th']. '" width="100px">Overall Money</th>' : ''). '
			</tr>';
			$count = 0;
			foreach($records as $record){
								
				//Only display records with a total
				if(($type == 'points' && ($record['points'] > 0 || $record['prize'] > 0)) || ($type == 'money' && $record['total_prize'] > 0)){
					$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
					$html .= '<tr>
						<td style="' .$css_td. '">' .$record['event_name']. '</td>
						<td style="' .$css_td. '">' .format_date_range($record['start_date'], $record['end_date']). '</td>
						' .($type == 'points' ? '<td style="' .$css_td. '" align="center">' .number_format($record['points'], 2). '</td>' : ''). '
						<td style="' .$css_td. '" align="right">$' .number_format($record['prize'], 2). '</td>
						' .($type == 'money' ? '<td style="' .$css_td. '" align="right">$' .number_format($record['team_prize'], 2). '</td>' : ''). '
						' .($type == 'money' ? '<td style="' .$css_td. '" align="right">$' .number_format($record['total_prize'], 2). '</td>' : ''). '
					</tr>';
					$count++;
				}
			}
		$html .= '</table>';
		if($count == 0){
			$html .= '<p>No records found.</p>';
		}
		
		//Generate pdf and send to browser
		$mpdf->WriteHTML($html, 2);
		$mpdf->Output($reports[$type]['title'].' '.$year.' - '.$records[0]['first_name'].' '.$records[0]['last_name']. '.pdf','I');
		
		
	//Overall reports
	}else{
	
		//Loop through attendees and partners
		for($i=0; $i<=1; $i++){
		
			//Get all tournament attendees
			//Attendee is registered
			//Attendee is account holder
			//Attendee is not a partner
			//Event date in selected year
			$params = array('Registered', $start_date, $end_date);
			if(!empty($id)){
				$params[] = $id;
			}
			if(!empty($tournaments)){
				$params[] = $tournaments;
			}
			$where = "";
			if(!empty($categories)){
				$where .= "&& (";
				$count = 0;
				foreach($categories as $category){
					$count++;
					$params[] = $category;
					$where .= "`reg_event_categories`.`category_id` = ?".($count < count($categories) ? " || " : "");
				}
				$where .= ") ";
			}
			$query = $db->query("SELECT ".
				"`reg_tournament_field`.*, ".
				"`reg_attendees`.`first_name`, `reg_attendees`.`last_name`, `reg_attendees`.`account_id`, `reg_attendees`.`partner_id`, ".
				"`account_profiles`.`profile_id`, ".
				"`facilities`.`facility_name`, ".
				"`membership_types`.`membership_name`, ".
				"`reg_event_categories`.`category_id`, ".
				"(IFNULL(`reg_tournament_field`.`r1_score`, 0)+IFNULL(`reg_tournament_field`.`r2_score`, 0)) AS `final_score`, ".
				"(IFNULL(`reg_tournament_field`.`r1_team_gross`, 0)+IFNULL(`reg_tournament_field`.`r2_team_gross`, 0)) AS `team_gross`, ".
				"(IFNULL(`reg_tournament_field`.`r1_team_net`, 0)+IFNULL(`reg_tournament_field`.`r2_team_net`, 0)) AS `team_net`, ".
				"IFNULL(`reg_occurrences`.`rounds`, 1) AS `rounds`, ".
				"IFNULL(`reg_tournament_field`.`points`, 0) AS `points`, ".
				"IFNULL(`reg_tournament_field`.`prize`, 0) AS `prize`, ".
				"IFNULL(`reg_tournament_field`.`team_prize`, 0) AS `team_prize`, ".
				"IFNULL(`reg_tournament_field`.`r1_score`, 0) AS `r1_score`, ".
				"IFNULL(`reg_tournament_field`.`r2_score`, 0) AS `r2_score`, ".
				"IFNULL(`reg_tournament_field`.`r1_overunder`, 0) AS `r1_overunder`, ".
				"IFNULL(`reg_tournament_field`.`r2_overunder`, 0) AS `r2_overunder` ".
			"FROM `reg_attendees` ".
			"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
			"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
			"LEFT JOIN `membership_types` ON `account_profiles`.`membership_id` = `membership_types`.`membership_id` ".
			"LEFT JOIN `reg_event_categories` ON `reg_attendees`.`event_id` = `reg_event_categories`.`event_id` ".
			"LEFT JOIN `reg_events` ON `reg_attendees`.`event_id` = `reg_events`.`event_id` ".
			"LEFT JOIN `reg_occurrences` ON `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` ".
			"LEFT JOIN `reg_tournament_field` ON `reg_attendees`." .($i==0 ? "`attendee_id`" : "`partner_id`"). " = `reg_tournament_field`.`attendee_id` ".
			"WHERE `reg_attendees`.`reg_status` = ? && `reg_attendees`.`account_id` IS NOT NULL && `reg_attendees`.`partner_id` " .($i==0 ? "IS NULL" : "IS NOT NULL"). " && `reg_events`.`role_id` != 8 && (`reg_occurrences`.`start_date` >= ? && `reg_occurrences`.`end_date` <= ?) ".
			(!empty($id) ? "&& `account_profiles`.`profile_id` = ? " : "").$where.
			"ORDER BY `reg_tournament_field`.`points` DESC, `reg_attendees`.`last_name` ASC, `reg_attendees`.`first_name` ASC", $params);
			if($query && !$db->error()){
				$result = $db->fetch_array();
				foreach($result as $row){

					//Team prize amount should be divided between team members, except for Pro-Ams
					if($row['category_id'] != 8){
						$row['team_prize'] = ($row['team_prize']/2);
					}
					
					//Change overunder to decimals
					$row['r1_overunder'] = str_replace(':', '.', $row['r1_overunder']);
					$row['r2_overunder'] = str_replace(':', '.', $row['r2_overunder']);
					
					//Push to records array
					if(array_key_exists($row['account_id'], $records)){
						
						//Only add partner events to count if events or money report
						if(empty($row['partner_id']) || (!empty($row['partner_id']) && ($type == 'events' || $type == 'money'))){
							
							//Only add events and rounds with an actual score/avg for scoring/overunder report
							if($type == 'score' || $type == 'overunder'){
								if(!empty($row['r1_'.$type]) || !empty($row['r2_'.$type])){
									$records[$row['account_id']]['events']++;
								}
								if(!empty($row['r1_'.$type])){
									$records[$row['account_id']]['rounds'] += 1;
								}
								if(!empty($row['r2_'.$type])){
									$records[$row['account_id']]['rounds'] += 1;
								}
								
							}else{
								$records[$row['account_id']]['events']++;
								$records[$row['account_id']]['rounds'] += $row['rounds'];
							}
							
						}
						if(empty($row['partner_id'])){
							$records[$row['account_id']]['score'] += ($row['r1_score']+$row['r2_score']);
							$records[$row['account_id']]['score_avg'] = ($records[$row['account_id']]['score']/$records[$row['account_id']]['rounds']);
							$records[$row['account_id']]['overunder'] += ($row['r1_overunder']+$row['r2_overunder']);
							$records[$row['account_id']]['overunder_avg'] = ($records[$row['account_id']]['overunder']/$records[$row['account_id']]['rounds']);
							$records[$row['account_id']]['prize'] += $row['prize'];
							if($records[$row['account_id']]['events'] <= 5){ //Only add top 5
								$records[$row['account_id']]['points'] += $row['points'];
							}
						}
						$records[$row['account_id']]['team_prize'] += $row['team_prize'];
						$records[$row['account_id']]['total_prize'] = ($records[$row['account_id']]['prize']+$records[$row['account_id']]['team_prize']);
											

					}else{
						$records[$row['account_id']] = array(
							'profile_id' => $row['profile_id'],
							'first_name' => $row['first_name'],
							'last_name' => $row['last_name'],
							'facility_name' => $row['facility_name'],
							'membership_name' => $row['membership_name'],
							'events' => 0,
							'rounds' => 0,
							'score' => (empty($row['partner_id']) ? $row['r1_score']+$row['r2_score'] : 0),
							'score_avg' => (empty($row['partner_id']) ? $row['r1_score']+$row['r2_score'] : 0),
							'overunder' => (empty($row['partner_id']) ? $row['r1_overunder']+$row['r2_overunder'] : 0),
							'overunder_avg' => (empty($row['partner_id']) ? $row['r1_overunder']+$row['r2_overunder'] : 0),
							'points' => (empty($row['partner_id']) ? $row['points'] : 0),
							'prize' => (empty($row['partner_id']) ? $row['prize'] : 0),
							'team_prize' => $row['team_prize'],
							'total_prize' => $row['prize']+$row['team_prize']
						);
						
						//Only add partner events to count if events or money report
						if(empty($row['partner_id']) || (!empty($row['partner_id']) && ($type == 'events' || $type == 'money'))){
							
							//Only add events and rounds with an actual score for scoring report
							if($type == 'score' || $type == 'overunder'){
								if(!empty($row['r1_'.$type]) || !empty($row['r2_'.$type])){
									$records[$row['account_id']]['events']++;
								}
								if(!empty($row['r1_'.$type])){
									$records[$row['account_id']]['rounds'] += 1;
								}
								if(!empty($row['r2_'.$type])){
									$records[$row['account_id']]['rounds'] += 1;
								}
								
							}else{
								$records[$row['account_id']]['events']++;
								$records[$row['account_id']]['rounds'] += $row['rounds'];
							}
							
						}
						
						//Update averages based on rounds
						$records[$row['account_id']]['score_avg'] = ($records[$row['account_id']]['score']/$records[$row['account_id']]['rounds']);
						$records[$row['account_id']]['overunder_avg'] = ($records[$row['account_id']]['overunder']/$records[$row['account_id']]['rounds']);
						
					}
				}
			}
			
		}

		//Sort by column
		foreach($records as $key=>$row){
			$lname[$key] = $row['last_name'];
			$fname[$key] = $row['first_name'];
			$events[$key] = $row['events'];
			$score[$key] = $row['score_avg'];
			$overunder[$key] = $row['overunder_avg'];
			$points[$key] = $row['points'];
			$money[$key] = $row['total_prize'];
		}
		if($type == 'score'){
			array_multisort($score, SORT_ASC, $lname, SORT_ASC, $fname, SORT_ASC, $records);
		}else if($type == 'points'){
			array_multisort($points, SORT_DESC, $lname, SORT_ASC, $fname, SORT_ASC, $records);
		}else if($type == 'money'){
			array_multisort($money, SORT_DESC, $lname, SORT_ASC, $fname, SORT_ASC, $records);
		}else if($type == 'overunder'){
			array_multisort($overunder, SORT_ASC, $lname, SORT_ASC, $fname, SORT_ASC, $records);
		}else{
			array_multisort($events, SORT_DESC, $lname, SORT_ASC, $fname, SORT_ASC, $records);
		}

		//Set html
		$html = '<table cellpadding="0" cellspacing="0" width="100%" border="0">
			<tr>
				<td width="120px"><img src="' .$logo. '" width="120px" /></td>
				<td align="center">
					<h2>' .$reports[$type]['title'].' '.$year. '</h2>
					' .(!empty($category_names) ? '<h4>' .implode('<br />', $category_names). '</h4>' : ''). '
				</td>
				<td width="120px">&nbsp;</td>
			</tr>
		</table><br />';

		$html .= '<table cellpadding="5" cellspacing="1" width="100%" border="0"><tr>
				' .($type == 'points' || $type == 'money' || $type == 'score' ? '<th style="' .$css['th']. '" width="50px">Rank</th>' : ''). '
				<th style="' .$css['th']. '">Professional</th>
				<th style="' .$css['th']. '">Facility</th>
				' .($type == 'points' ? '<th style="' .$css['th']. '">Membership</th>' : ''). '
				<th style="' .$css['th']. '" width="100px">No. Events</th>
				' .($type == 'score' || $type == 'overunder' ? '<th style="' .$css['th']. '" width="100px">No. Rounds</th>' : ''). '
				' .($type == 'score' ? '<th style="' .$css['th']. '" width="100px">Scoring Average</th>' : ''). '
				' .($type == 'overunder' ? '<th style="' .$css['th']. '" width="100px">Avg +/- Time</th>' : ''). '
				' .($type == 'points' ? '<th style="' .$css['th']. '" width="100px">Overall Points</th>' : ''). '
				' .($type == 'money' ? '<th style="' .$css['th']. '" width="100px">Individual Money</th>' : ''). '
				' .($type == 'money' ? '<th style="' .$css['th']. '" width="100px">Team Money</th>' : ''). '
				' .($type == 'money' ? '<th style="' .$css['th']. '" width="100px">Overall Money</th>' : ''). '
			</tr>';
			$count = 0;
			$rank = 0;
			$prev = '';
			foreach($records as $record){
				$count ++;
				
				//Only display rows with a total > 0
				if(($type == 'events') || ($type == 'score' && $record['score_avg'] > 0) || ($type == 'overunder' && $record['events'] > 0) || ($type == 'points' && $record['points'] > 0) || ($type == 'money' && $record['total_prize'] > 0)){
					
					//Determine rank
					if($type == 'points'){
						if($record['points'] != $prev){
							$rank = $count;
							$prev = $record['points'];
						}
					}else if($type == 'money'){
						if($record['total_prize'] != $prev){
							$rank = $count;
							$prev = $record['total_prize'];
						}
					}else if($type == 'score'){
						if($record['score_avg'] != $prev){
							$rank = $count;
							$prev = $record['score_avg'];
						}
					}
					
					$css_td = ($count%2 ? $css['td'] : $css['td-alt']);
					$html .= '<tr>
						' .($type == 'points' || $type == 'money' || $type == 'score' ? '<td style="' .$css_td. '" align="center">' .$rank. '</td>' : ''). '
						<td style="' .$css_td. '">';
						if($type == 'points' || $type == 'money'){
							$html .= '<a href="' .$siteurl.$path. 'reports/report-oom.php?year='.$year.'&report='.$type.'&id='.$record['profile_id'].(!empty($categories) ? '&categories[]='.implode('&categories[]=', $categories) : ''). '">'.$record['last_name']. ', '.$record['first_name'].'</a>';
						}else{
							$html .= $record['last_name'].', '.$record['first_name'];
						}
						$html .= '</td>
						<td style="' .$css_td. '">' .$record['facility_name']. '</td>
						' .($type == 'points' ? '<td style="' .$css_td. '">' .$record['membership_name']. '</td>' : ''). '
						<td style="' .$css_td. '" align="center">' .$record['events']. '</td>
						' .($type == 'score' || $type == 'overunder' ? '<td style="' .$css_td. '" align="center">' .$record['rounds']. '</td>' : ''). '
						' .($type == 'score' ? '<td style="' .$css_td. '" align="center">' .number_format($record['score_avg'], 2). '</td>' : ''). '
						' .($type == 'overunder' ? '<td style="' .$css_td. '" align="center">' .str_replace('.', ':', (number_format($record['overunder_avg'], 2) > 0 ? '+' : '').number_format($record['overunder_avg'], 2)). '</td>' : ''). '
						' .($type == 'points' ? '<td style="' .$css_td. '" align="center">' .number_format($record['points'], 2). '</td>' : ''). '
						' .($type == 'money' ? '<td style="' .$css_td. '" align="right">$' .number_format($record['prize'], 2). '</td>' : ''). '
						' .($type == 'money' ? '<td style="' .$css_td. '" align="right">$' .number_format($record['team_prize'], 2). '</td>' : ''). '
						' .($type == 'money' ? '<td style="' .$css_td. '" align="right">$' .number_format($record['total_prize'], 2). '</td>' : ''). '
					</tr>';
				}
			}
		$html .= '</table>';
		if($count == 0){
			$html .= '<p>No records found.</p>';
		}
		
		//Generate pdf and send to browser
		$mpdf->WriteHTML($html, 2);
		$mpdf->Output($reports[$type]['title'].' '.$year.'.pdf','I');
		
	}

//Invalid request
}else{
	die('Report not found.');
}

?>