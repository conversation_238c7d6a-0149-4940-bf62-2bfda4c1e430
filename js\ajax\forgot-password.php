<?php  

//System files
include("../../config/config.php");
include("../../config/database.php");
include("../../includes/functions.php");
include("../../includes/utils.php");

error_reporting(0);
ini_set('display_errors', 'off');

//Email validation
if(!checkmail($_POST['email'])) {
	echo 'Email address is invalid.';
	exit();
}

//Cookie validation
if($_POST['xid'] != $_COOKIE['xid']) {
	echo 'Invalid session. Please make sure cookies are enabled on your browser then try again.';
	exit();
}

//All valid
try{
	$Account->forgot_password($_POST['email']);
	echo 'success';
}catch(Exception $e){
	echo $e->getMessage();
}

?>