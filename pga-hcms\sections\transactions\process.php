<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Process payment
if(SECTION_ID == $_cmssections['transactions-payments']){
	
	if(isset($_SESSION['cms']['payment'])){
		foreach($_SESSION['cms']['payment'] as $key=>$data){
			$row[$key] = $data;
		}
	}
	
	//Enter payment information
	if(!$confirm){
		
		echo "<form action='' method='post' enctype='multipart/form-data'>";
	
		//Order Information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
					<tr>
						<td width='150px'>$record_name No:</td>
						<td><a href='" .$sitemap[$section_id]['page_url']. "?action=edit&item_id=" .ITEM_ID. "'>".$row['record_number']."</a></td>
					</tr>
					<tr>
						<td>$record_name Date:</td>
						<td>".date('M j, Y g:iA', strtotime($row['record_date']))."</td>
					</tr>
					<tr>
						<td>Balance Due:</td>
						<td>$".number_format($row['balance'], 2)."</td>
					</tr>";
					if(!empty($row['account_id'])){
						echo "<tr>
							<td>Account No:</td>
							<td><a href='" .$sitemap[$_cmssections['manage_users']]['page_url']. "?action=edit&item_id=" .$row['account_id']. "'>" .$row['account_id']."</a></td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Order Information

		//Payment information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Payment Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>First Name <span class='required'>*</span></label>
					<input type='text' name='first_name' value='" .(isset($row['first_name']) ? $row['first_name'] : ''). "' class='input" .(in_array('first_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Last Name <span class='required'>*</span></label>
					<input type='text' name='last_name' value='" .(isset($row['last_name']) ? $row['last_name'] : ''). "' class='input" .(in_array('last_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Email Address <span class='required'>*</span></label>
					<input type='text' name='email' value='" .(isset($row['email']) ? $row['email'] : ''). "' class='input" .(in_array('email', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Phone Number <span class='required'>*</span></label>
					<input type='text' name='phone' value='" .(isset($row['phone']) ? $row['phone'] : ''). "' class='input" .(in_array('phone', $required) ? ' required' : ''). "' />
				</div>";
				echo "<br class='clear' />";

				$payment_types = $db->get_enum_vals('payments', 'payment_type');
				echo "<div class='form-field'>
					<label>Payment Type <span class='required'>*</span></label>
					<select type='text' name='payment_type' class='select" .(in_array('payment_type', $required) ? ' required' : ''). "' onchange='paymentToggle(this.value);'>
						<option value=''>- Select -</option>";
						foreach($payment_types as $type){
							echo "<option value='" .$type. "'" .(isset($row['payment_type']) && $row['payment_type'] == $type ? " selected" : ""). ">" .$type. "</option>";
						}
					echo "</select>
				</div>";

				echo "<div class='form-field'>
					<label>Payment Amount <span class='required'>*</span></label>
					<input type='text' name='payment_amount' id='payment_amount' value='" .(isset($row['payment_amount']) ? number_format($row['payment_amount'], 2, '.', '') : (isset($row['balance']) ? number_format($row['balance'], 2, '.', '') : '')). "' class='input number" .(in_array('payment_amount', $required) ? ' required' : ''). "' />
				</div>";
				
				echo "<div class='ccfields form-field' style='" .(isset($row['payment_type']) && $row['payment_type'] == 'Credit Card' ? "" : "display:none;"). "'>
					<label>Service Fee " .(isset($admin_fee_percentage) && $admin_fee_percentage > 0 ? '<small>(Suggested '.$admin_fee_percentage.'%)</small>' : ''). "</label>
					<input type='text' name='admin_fee' id='admin_fee' value='" .(isset($row['admin_fee']) ? number_format($row['admin_fee'], 2, '.', '') : ''). "' class='input number" .(in_array('admin_fee', $required) ? ' required' : ''). "' />
				</div>";

			echo "</div>
		</div>";

		//Billing information
		echo "<div class='ccfields clearfix' style='" .(isset($row['payment_type']) && $row['payment_type'] == 'Credit Card' ? "" : "display:none;"). "'>";

			//Billing profiles
			if(!empty($billing_profiles)){
				echo "<div class='panel'>";
					echo "<div class='panel-header'>Billing Profiles
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";
					echo "<div class='panel-content nopadding clearfix'>";			
						echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
							foreach($billing_profiles as $billing){
								$card_errors = array();

								//Check card expiry
								$expiry_date = '20'.substr($billing['ccexpiry'], -2, 2).substr($billing['ccexpiry'], 0, 2).'01';
								if($expiry_date <= date("Ymd")){
									$card_errors[] = 'Credit card is expired. Please choose another.';
								}

								//Check card type
								$valid_card = false;
								$accepted_cards = array();
								foreach($payment_options as $payopt){
									$accepted_cards[] = $payopt['name'];
									if($billing['cctype'] == $payopt['type']){
										$valid_card = true;
									}
								}
								if(!$valid_card){
									$card_errors[] = 'Invalid card type. We currently only accept '.implode(' &amp; ', $accepted_cards).'.';
								}

								$billing_label = $billing['cctype']. ' **** **** **** '.$billing['ccnumber']. ' &nbsp; '.substr($billing['ccexpiry'], 0, 2).'/'.substr($billing['ccexpiry'], -2, 2);
								if(empty($card_errors)){
									echo "<tr>
										<td><input type='radio' class='radio billing-toggle' id='billing-" .$billing['billing_id']. "' name='billing_id' value='" .$billing['billing_id']. "' onclick='billingToggle(false);'" .(isset($row['billing_id']) && $row['billing_id'] == $billing['billing_id'] ? " checked" : ""). " />
										<label for='billing-" .$billing['billing_id']. "'> " .$billing_label. "</label></td>
									</tr>";
								}else{
									echo "<tr>
										<td><input type='radio' class='radio billing-toggle' id='billing-" .$billing['billing_id']. "' disabled />
										<label for='billing-" .$billing['billing_id']. "'> " .$billing_label. "</label>
										<small class='required'><i class='fa fa-exclamation-triangle'></i> " .(implode(' ', $card_errors)). "</small></td>
									</tr>";
								}
							}

							echo "<tr>
								<td><input type='radio' class='radio billing-toggle' name='billing_id' id='billing-new' value='' onclick='billingToggle(true);'" .(isset($row['billing_id']) && $row['billing_id'] == '' ? " checked" : ""). " />
								<label for='billing-new' class='billing-new'>&nbsp;&nbsp;New Credit Card</label></td>
							</tr>";

						echo "</table>";
					echo "</div>
				</div>";
			}

			//Billing information
			echo "<div id='billfields' class='panel' style='" .((isset($row['billing_id']) && $row['billing_id'] == '') || empty($billing_profiles) ? "" : " display:none;"). "'>";
				echo "<div class='panel-header'>Billing Information
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content clearfix'>";					
					echo "<div class='form-field'>
						<label>Street Address <span class='required'>*</span></label>
						<input type='text' name='bill_address1' value='" .(isset($row['bill_address1']) ? $row['bill_address1'] : ''). "' class='input" .(in_array('bill_address1', $required) ? ' required' : ''). "' />
					</div>";
					echo "<div class='form-field'>
						<label>Unit No.</label>
						<input type='text' name='bill_address2' value='" .(isset($row['bill_address2']) ? $row['bill_address2'] : ''). "' class='input" .(in_array('bill_address2', $required) ? ' required' : ''). "' />
					</div>";

					echo "<br class='clear' />";

					echo "<div class='form-field'>
						<label>City/Town <span class='required'>*</span></label>
						<input type='text' name='bill_city' value='" .(isset($row['bill_city']) ? $row['bill_city'] : ''). "' class='input" .(in_array('bill_city', $required) ? ' required' : ''). "' />
					</div>";
		
					//Canada
					echo "<div class='form-field addr' id='addr-CA' style='" .(isset($row['bill_country']) && $row['bill_country'] != 'CA' && $row['bill_country'] != '' ? "display:none;" : ""). "'>
						<label>Province <span class='required'>*</span></label>
						<select name='bill_province' class='select" .(in_array('bill_province', $required) ? ' required' : ''). "'>";
							echo "<option value=''>- Select -</option>";
							for($p=1; $p<=count($provinces); $p++){
								echo "<option value='" .$provinces[$p][1]. "'" .((isset($row['bill_province']) && $row['bill_province'] == $provinces[$p][1]) ? " selected" : ""). ">" .$provinces[$p][0]. "</option>";	
							}
						echo "</select>
					</div>";

					//United States
					echo "<div class='form-field addr' id='addr-US' style='" .(isset($row['bill_country']) && $row['bill_country'] == 'US' ? "" : "display:none;"). "'>
						<label>State <span class='required'>*</span></label>
						<select name='bill_state' class='select" .(in_array('bill_province', $required) ? ' required' : ''). "'>";
							echo "<option value=''>- Select -</option>";
							for($p=1; $p<=count($states); $p++){
								echo "<option value='" .$states[$p][1]. "'" .((isset($row['bill_province']) && $row['bill_province'] == $states[$p][1]) ? " selected" : ""). ">" .$states[$p][0]. "</option>";	
							}
						echo "</select>
					</div>";

					//Other
					echo "<div class='form-field addr' id='addr-ALT' style='" .(!isset($_POST['bill_country']) || (isset($row['bill_country']) && $row['bill_country'] != 'CA' && $row['bill_country'] != 'US' && $row['bill_country'] != '') ? "" : "display:none;"). "'>
						<label>Region <span class='required'>*</span></label>
						<input type='text' name='bill_region' value='" .(isset($row['bill_province']) ? $row['bill_province'] : ''). "' class='input" .(in_array('bill_province', $required) ? ' required' : ''). "' />
					</div>";
		
					echo "<div class='form-field'>
						<label>Postal/Zip Code <span class='required'>*</span></label>
						<input type='text' name='bill_postalcode' value='" .(isset($row['bill_postalcode']) ? $row['bill_postalcode'] : ''). "' class='input" .(in_array('bill_postalcode', $required) ? ' required' : ''). "' />
					</div>";
		
					echo "<div class='form-field'>
					<label>Country <span class='required'>*</span></label>
						<select name='bill_country' class='select country" .(in_array('bill_country', $required) ? ' required' : ''). "' id='addr'>
							<option value=''>- Select -</option>";
							foreach($countries as $code=>$country){
								echo "<option value='" .$code. "'" .((isset($row['bill_country']) && $row['bill_country'] == $code) ? " selected" : ""). ">" .$country. "</option>";
							}
						echo "</select>
					</div>";

					echo "<hr class='clear' />";

					echo "<div class='form-field'>
						<label>Cardholder Name <span class='required'>*</span></label>
						<input type='text' name='ccname' value='" .(isset($row['ccname']) ? $row['ccname'] : ''). "' class='input" .(in_array('ccname', $required) ? ' required' : ''). "' />
					</div>";
					echo "<div class='form-field'>
						<label>Card Number <span class='required'>*</span></label>
						<input type='text' name='ccnumber' value='" .(isset($_POST['ccnumber']) && $row['billing_id'] == '' ? $_POST['ccnumber'] : ''). "' class='input number" .(in_array('ccnumber', $required) ? ' required' : ''). "' maxlength='16' />
					</div>";	
					echo "<div class='form-field'>
						<label>Expiry Date <span class='required'>*</span></label>
						<select name='exp_month' class='select select_sm" .(in_array('exp_month', $required) ? ' required' : ''). "'>
							<option value=''>- Month -</option>";
							foreach($months as $key=>$value){
								$key = str_pad($key, 2, '0', STR_PAD_LEFT);
								echo '<option value="' .$key. '"' .(isset($row['exp_month']) && $row['exp_month'] == $key && $row['billing_id'] == '' ? ' selected' : ''). '>' .substr($value, 0, 3). ' (' .$key. ')</option>';	
							}
						echo "</select>
						<select name='exp_year' class='select select_sm" .(in_array('exp_year', $required) ? ' required' : ''). "'>
							<option value=''>- Year -</option>";
							for($y=date('Y'); $y<=(date('Y')+20); $y++){
								echo '<option value="' .substr($y, -2). '"' .(isset($row['exp_year']) && $row['exp_year'] == substr($y, -2) && $row['payment']['billing_id'] == '' ? ' selected' : ''). '>' .$y. '</option>';	
							}
						echo "</select>
					</div>";
					echo "<div class='form-field'>
						<label>CVV Code <span class='required'>*</span></label>
						<input type='text' name='cvv' value='" .(isset($_POST['cvv']) ? $_POST['cvv'] : ''). "' class='input input_sm number" .(in_array('cvv', $required) ? ' required' : ''). "' maxlength='4' />
					</div>";

					echo "<p class='clear'>
						<input type='checkbox' name='ccsave' id='ccsave' class='checkbox' value='1'" .(isset($row['ccsave']) && $row['ccsave'] == true ? ' checked' : ''). " />
						<label for='ccsave'><small>Save this credit card to billing profiles</small></label>
					</p>";

				echo "</div>";
			echo "</div>";

		echo "</div>"; //End ccfields

		//Notes
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Notes ".$CMSBuilder->tooltip('Notes', 'Notes are for internal use only. They will not appear on the front-end of the website.')."
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<textarea name='notes' class='textarea'>".(isset($row['notes']) ? $row['notes'] : "")."</textarea>
				</div>";
			echo "</div>";
		echo "</div>"; //Notes

		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			echo "<button type='submit' class='button f_right' name='continue' value='true'><i class='fa fa-arrow-right'></i>Continue</button>";
			echo "<a href='" .$sitemap[$section_id]['page_url']. "?action=edit&item_id=".ITEM_ID."' class='cancel'>Cancel</a>";
		echo "</footer>";
		// Sticky footer
		// include('includes/widgets/formbuttons.php');

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "</form>";
	
		
	//Confirmation	
	}else{
		
		echo "<form action='' method='post' enctype='multipart/form-data'>";
		
		//Review and submit
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Review &amp; Submit
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
					<tr>
						<td width='150px'>$record_name No:</td>
						<td>".$row['record_number']."</td>
					</tr>
					<tr>
						<td>Name:</td>
						<td>".$_SESSION['cms']['payment']['first_name']." ".$_SESSION['cms']['payment']['last_name']. "</td>
					</tr>
					<tr>
						<td>Email Address:</td>
						<td>".$_SESSION['cms']['payment']['email']."</td>
					</tr>
					<tr>
						<td>Phone Number:</td>
						<td>".$_SESSION['cms']['payment']['phone']."</td>
					</tr>
					<tr>
						<td>Total Amount:</td>
						<td>$".number_format($_SESSION['cms']['payment']['payment_amount']+$_SESSION['cms']['payment']['admin_fee'], 2)."</td>
					</tr>
					<tr>
						<td>Service Fee:</td>
						<td>$".number_format($_SESSION['cms']['payment']['admin_fee'], 2)."</td>
					</tr>
					<tr>
						<td>Payment Type:</td>
						<td>";
						if($_SESSION['cms']['payment']['payment_type'] == 'Credit Card'){
							echo $_SESSION['cms']['payment']['cctype']. ' **** **** **** ' .substr($ccnumber, -4, 4). " &nbsp; ".
							$_SESSION['cms']['payment']['exp_month'].'/'.$_SESSION['cms']['payment']['exp_year'];
						}else{
							echo $_SESSION['cms']['payment']['payment_type'];
						}
						echo "</td>
					</tr>";
					if($_SESSION['cms']['payment']['payment_type'] == 'Credit Card' && trim($_SESSION['cms']['payment']['bill_address1']) != ''){
						echo "<tr>
							<td valign='top'>Billing Address:</td>
							<td>".(trim($_SESSION['cms']['payment']['bill_address2']) != '' ? $_SESSION['cms']['payment']['bill_address2'].' - ' : '').$_SESSION['cms']['payment']['bill_address1']."<br/>".$_SESSION['cms']['payment']['bill_city'].", ".$_SESSION['cms']['payment']['bill_province'].", ".$_SESSION['cms']['payment']['bill_country']."<br/>".$_SESSION['cms']['payment']['bill_postalcode']."</td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>"; 
		
		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			echo "<button type='submit' class='button f_right' name='process' value='true'><i class='fa fa-check'></i>Submit Payment</button>";
			echo "<a onclick='document.editform.submit();' class='cancel'>Go Back</a>";
		echo "</footer>";
		//Sticky footer
		// include('includes/widgets/formbuttons.php');
		
		echo "<input type='hidden' name='ccnumber' value='" .$_POST['ccnumber'] ."' />";
		echo "<input type='hidden' name='cvv' value='" .$_POST['cvv'] ."' />";
		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "</form>";
		
		echo "<form id='editform' name='editform' action='' method='post' enctype='multipart/form-data'>";
			echo "<input type='hidden' name='ccnumber' value='" .$_POST['ccnumber'] ."' />";
			echo "<input type='hidden' name='cvv' value='" .$_POST['cvv'] ."' />";
			echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
		echo "</form>";
		
	}


//Process refund
}else if(SECTION_ID == $_cmssections['transactions-refunds']){

	if(isset($_SESSION['cms']['refund'])){
		foreach($_SESSION['cms']['refund'] as $key=>$data){
			$row[$key] = $data;
		}
	}	
	
	echo "<form action='' method='post' enctype='multipart/form-data'>";
	
	//Enter refund information
	if(!$confirm){	
	
		echo $CMSBuilder->important('Only payments eligible for a refund will be available. Refund amount can be partial or full, but cannot exceed the payment amount.');	

		//Order Information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
					<tr>
						<td width='150px'>$record_name No:</td>
						<td><a href='" .$sitemap[$section_id]['page_url']. "?action=edit&item_id=" .ITEM_ID. "'>".$row['record_number']."</a></td>
					</tr>
					<tr>
						<td>$record_name Date:</td>
						<td>".date('M j, Y g:iA', strtotime($row['record_date']))."</td>
					</tr>";
					if(!empty($row['account_id'])){
						echo "<tr>
							<td>Account No:</td>
							<td><a href='" .$sitemap[$_cmssections['manage_users']]['page_url']. "?action=edit&item_id=" .$row['account_id']. "'>" .$row['account_id']."</a></td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Order Information	

		//Refund Information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Refund Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<label>First Name <span class='required'>*</span></label>
					<input type='text' name='first_name' value='" .(isset($row['first_name']) ? $row['first_name'] : ''). "' class='input" .(in_array('first_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Last Name <span class='required'>*</span></label>
					<input type='text' name='last_name' value='" .(isset($row['last_name']) ? $row['last_name'] : ''). "' class='input" .(in_array('last_name', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Email Address <span class='required'>*</span></label>
					<input type='text' name='email' value='" .(isset($row['email']) ? $row['email'] : ''). "' class='input" .(in_array('email', $required) ? ' required' : ''). "' />
				</div>";
				echo "<div class='form-field'>
					<label>Phone Number <span class='required'>*</span></label>
					<input type='text' name='phone' value='" .(isset($row['phone']) ? $row['phone'] : ''). "' class='input" .(in_array('phone', $required) ? ' required' : ''). "' />
				</div>";
				echo "<br class='clear' />";
		
				echo "<div class='form-field'>
					<label>Payment No. <span class='required'>*</span></label>
					<select name='payment_id' class='select" .(in_array('payment_id', $required) ? ' required' : ''). "' onchange='populateRefund(this.value);'>";
						echo "<option value=''>- Select -</option>";
						foreach($row['payments'] as $payment){
							if($payment['status'] == '1' && $payment['refund_amount'] < $payment['amount']){
								echo "<option value='" .$payment['payment_id']. "'" .((isset($row['payment_id']) && $row['payment_id'] == $payment['payment_id']) ? " selected" : ""). ">" .$payment['payment_number']. " ($" .number_format(($payment['amount']+$payment['admin_fee'])-$payment['refund_amount'], 2). ")</option>";
							}
						}
					echo "</select>
				</div>";

				$payment_types = $db->get_enum_vals('payments', 'payment_type');
				echo "<div class='form-field'>
					<label>Refund Type <span class='required'>*</span>" .$CMSBuilder->tooltip('Refund Type', 'Credit Card - Only select this option if the item was paid with a credit card online. This will process a refund transaction through the payment gateway. The credit card used will receive the refund if the transaction is processed successfully.'). "</label>
					<select type='text' name='refund_type' id='type' class='select" .(in_array('refund_type', $required) ? ' required' : ''). "'>
						<option value=''>- Select -</option>";
						foreach($payment_types as $type){
							echo "<option value='" .$type. "'" .(isset($row['refund_type']) && $row['refund_type'] == $type ? " selected" : ""). ">" .$type. "</option>";
						}
					echo "</select>
				</div>";

				//If no attendees or no attendee balance exists, do standard refund
				if(!isset($row['attendees']) || empty($row['attendees']) || $row['attendee_available_amount'] <= 0){
					echo "<div class='form-field'>
						<label>Refund Amount <span class='required'>*</span></label>
						<input type='text' name='refund_amount' id='amount' value='" .(isset($row['refund_amount']) ? number_format($row['refund_amount'], 2, '.', '') : ''). "' class='input number" .(in_array('refund_amount', $required) ? ' required' : ''). "' />
					</div>";
				}else{
					echo "<input type='hidden' name='payment_amount' id='amount' value='' disabled />";
				}

			echo "</div>"; 
		echo "</div>"; //Refund Information
				
		//Attendees
		if(!empty($row['attendees'])){
						
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Refund Attendees
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content nopadding clearfix'>";
					echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
						<thead>
							<tr>
								<th width='150px'>Name</th>
								<th width='150px'>Status</th>
								<th>" .(substr($row['registration_number'], -1) == 2 ? "Tournament" : "Event"). "</th>
								<th class='right' width='80px'>Price</th>
								<th class='right' width='80px'>Taxes</th>
								<th class='right' width='80px'>Fees</th>
								<th class='right' width='80px'>Refunds</th>
								<th class='right' width='70px'>Total</th>
								<th class='right' width='150px'>Refund Amount</th>
							</tr>
						</thead>
						<tbody>";
							foreach($row['attendees'] as $attendee){
								$refund_amount += $attendee['refund_amount'];
								echo "<tr>
									<td><a href='" .$sitemap[$_cmssections['registration-attendees']]['page_url']. "?action=edit&item_id=" .$attendee['attendee_id']. "'>" .$attendee['first_name']." ".$attendee['last_name']. "</a></td>
									<td>" .$attendee['reg_status']. "</td>
									<td>" .$attendee['event_name']. "</td>
									<td class='right'>$" .number_format($attendee['ticket_price']+$attendee['total_addons'], 2). "</td>
									<td class='right'>$" .number_format($attendee['taxes'], 2). "</td>
									<td class='right'>$" .number_format($attendee['admin_fee']+$attendee['tournament_fee'], 2). "</td>
									<td class='right'>-$" .number_format($attendee['total_refunded'], 2). "</td>
									<td class='right'><strong>$" .number_format($attendee['balance'], 2). "</strong></td>
									<td class='right'>";
										if($attendee['balance'] > 0){
											echo "$<input type='text' name='attendee_refunds[" .$attendee['attendee_id']. "]' class='input input_sm number nomargin sum" .(in_array('attendee_refunds_'.$attendee['attendee_id'], $required) ? ' required' : ''). "' value='" .(isset($attendee['refund_amount']) ? number_format($attendee['refund_amount'], 2, '.', '') : ''). "' />";
										}else{
											echo "$<input type='text' name='disabled' class='input input_sm number nomargin' value='' disabled />";
										}
									echo "</td>
								</tr>";
							}
			
							//Attendee amounts must be settled first
							if($row['attendee_available_amount'] > 0){
								echo "<tr>
									<td colspan='8' class='right'><strong>Total Refund Amount:</strong> " .$CMSBuilder->tooltip('Total Refund Amount', 'Total refund amount will be automatically calculated based on the refund amounts above. Cannot exceed the payment amount.'). "</td>
									<td class='right'>$<input type='text' name='refund_amount' id='refund_amount' value='" .(isset($row['refund_amount']) ? number_format($row['refund_amount'], 2, '.', '') : ''). "' class='input input_sm number nomargin" .(in_array('refund_amount', $required) ? ' required' : ''). "' readonly /></td>
								</tr>";
							}
						echo "</tbody>
					</table>
				</div>
			</div>";
		}
			
		//Notes
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Notes ".$CMSBuilder->tooltip('Notes', 'Notes are for internal use only. They will not appear on the front-end of the website.')."
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<div class='form-field'>
					<textarea name='notes' class='textarea'>".(isset($row['notes']) ? $row['notes'] : "")."</textarea>
				</div>";
			echo "</div>";
		echo "</div>"; //Notes
		
		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			echo "<button type='submit' class='button f_right' name='continue' value='true'><i class='fa fa-arrow-right'></i>Continue</button>";
			echo "<a href='" .$sitemap[$section_id]['page_url']. "?action=edit&item_id=".ITEM_ID."' class='cancel'>Cancel</a>";
		echo "</footer>";
		//Sticky footer
		// include('includes/widgets/formbuttons.php');
		
		echo "<input type='hidden' name='txn_num' id='txn_num' value='".(isset($row['txn_num']) ? $row['txn_num'] : "")."' />";
		echo "<input type='hidden' name='txn_tag' id='txn_tag' value='".(isset($row['txn_tag']) ? $row['txn_tag'] : "")."' />";
	
		
	//Confirmation
	}else{
				
		//Review and submit
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Review &amp; Submit
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
					<tr>
						<td width='150px'>$record_name No:</td>
						<td>".$row['record_number']."</td>
					</tr>
					<tr>
						<td>Payment No:</td>
						<td>".$refund_payment['payment_number']."</td>
					</tr>
					<tr>
						<td>Name:</td>
						<td>".$row['first_name']." ".$row['last_name']. "</td>
					</tr>
					<tr>
						<td>Email Address:</td>
						<td>".$row['email']."</td>
					</tr>
					<tr>
						<td>Phone Number:</td>
						<td>".$row['phone']."</td>
					</tr>
					<tr>
						<td>Refund Amount:</td>
						<td>$".number_format($_SESSION['cms']['refund']['refund_amount'], 2)."</td>
					</tr>
					<tr>
						<td>Refund Type:</td>
						<td>";
						if($_SESSION['cms']['refund']['refund_type'] == 'Credit Card'){
							echo $row['payments'][$_SESSION['cms']['refund']['payment_id']]['cctype']. ' **** **** **** ' .$row['payments'][$_SESSION['cms']['refund']['payment_id']]['ccnumber']. " &nbsp; ".
							substr($row['payments'][$_SESSION['cms']['refund']['payment_id']]['ccexpiry'], 0, 2).'/'.substr($row['payments'][$_SESSION['cms']['refund']['payment_id']]['ccexpiry'], -2, 2);
						}else{
							echo $_SESSION['cms']['refund']['refund_type'];
						}			
						echo "</td>
					</tr>";
				echo "</table>";
			echo "</div>";
		echo "</div>";
				
		//Attendees
		if(!empty($_SESSION['cms']['refund']['attendees']) && $_SESSION['cms']['refund']['attendee_refund_amount'] > 0){
			
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Refund Attendees
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content nopadding clearfix'>";
					echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
						<thead>
							<tr>
								<th width='150px'>Name</th>
								<th width='150px'>Status</th>
								<th>" .(substr($row['registration_number'], -1) == 2 ? "Tournament" : "Event"). "</th>
								<th class='right' width='150px'>Refund Amount</th>
							</tr>
						</thead>
						<tbody>";
						foreach($row['attendees'] as $attendee){
							if($attendee['refund_amount'] > 0){
								echo "<tr>
									<td>" .$attendee['first_name']." ".$attendee['last_name']. "</td>
									<td>" .$attendee['reg_status']. "</td>
									<td>" .$attendee['event_name']. "</td>
									<td class='right'>$" .number_format($attendee['refund_amount'], 2). "</td>
								</tr>";
							}
						}
						echo "</tbody>
					</table>
				</div>
			</div>";
		}
		
		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			echo "<button type='submit' class='button f_right' name='process' value='true'><i class='fa fa-check'></i>Submit Refund</button>";
			echo "<a href='" .PAGE_URL. "?action=process&item_id=".ITEM_ID."&record=".RECORD."' class='cancel'>Go Back</a>";
		echo "</footer>";
		
	}

	echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";
	echo "</form>";
	
}

?>
<script type="text/javascript">
	function paymentToggle(val){
		if(val == 'Credit Card'){
			$('.ccfields').slideDown();
		}else{
			$('.ccfields').slideUp();
		}
	}
	function billingToggle(bool){
		if(bool == true){
			$('#billfields').slideDown();
		}else{
			$('#billfields').slideUp();
		}
	}
	function populateRefund(id){
		<?php $array = array_values($row['payments']); ?>
		var payments = JSON.parse('<?php echo json_encode($array); ?>');
		var total_payments = <?php echo count($array); ?>;
				
		for(var i=0; i<total_payments; i++){
			if(payments[i].payment_id == id){
			 	$('#amount').val((payments[i].amount-payments[i].refund_amount).toFixed(2));  
				$('#type').val(payments[i].payment_type);
				$('#txn_num').val(payments[i].txn_num);
				$('#txn_tag').val(payments[i].txn_tag);
				break;
			}	
		}
	}
	$(function(){
		$('input#payment_amount').on('input', function(){
			var payment_amount = $(this).val();
			var admin_fee = 0;
			var admin_fee_percentage = <?php echo (isset($admin_fee_percentage) ? $admin_fee_percentage : 0); ?>;
			if(admin_fee_percentage > 0){
			   admin_fee = (payment_amount*(admin_fee_percentage/100));
			}
			$('input#admin_fee').val(admin_fee.toFixed(2));
		});
		$('input.sum').on('input', function(){
			var refund = 0;
			$('input.sum').each(function(){
				if($(this).val() != ''){
					refund = refund+parseFloat($(this).val()); 
				}
			});
			$('#refund_amount').val(refund.toFixed(2));
		});
	});
</script>