<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	//Conversion chart
	include("includes/widgets/inquirieschart.php");

	//Search
	echo '<form action="" method="get" enctype="multipart/form-data">
		<div class="panel">
			<div class="panel-header">Search
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content">
				<div class="form-field">
					<label>Filter by Form</label>
					<select name="form_id" class="select">
						<option value="">All</option>';
						foreach($forms as $form){
							echo '<option value="'.$form['form_id'].'"'.(($_SESSION['search_form_id'][SECTION_ID] ?? '') == $form['form_id'] ? ' selected' : '').'>'.$form['form_name'].'</option>';
						}
					echo '</select>
				</div>
				<div class="form-field">
					<label>Start Date</label>
					<input type="text" name="start_date" value="'.($_SESSION['search_start_date'][SECTION_ID] ?? '').'" class="input" />
				</div>
				<div class="form-field">
					<label>End Date</label>
					<input type="text" name="end_date" value="'.($_SESSION['search_end_date'][SECTION_ID] ?? '').'" class="input" />
				</div>
			</div>
			<div class="pager">
				<div class="flex-container">
					<div class="flex-column left"><button type="button" class="button delete" onclick="document.getElementById(\'clear-search-form\').submit();"><i class="fa fa-times"></i> Clear</a></div>
					<div class="flex-column right">
						<button type="submit" name="export" class="button"><i class="fas fa-download"></i> Export</button>
						<button type="submit" name="advanced_search" class="button"><i class="fas fa-search"></i> Search</button>
					</div>
				</div>
			</div>
		</div>
	</form>

	<form id="clear-search-form" name="clear-search-form" class="hidden" action="'.PAGE_URL.'" method="post">
		<input type="hidden" name="clear-search" value="Clear" />
		<input type="hidden" name="xssid" value="'.$_COOKIE['xssid'].'" />
	</form>';

	//Display records
	echo '<div class="panel">
		<div class="panel-header">Form Submissions
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
				<thead>
					<tr>
						<th>Subject</th>
						<th>Date Submitted</th>
						<th data-sorter="false"></th>
					</tr>
				</thead>
				<tbody>';

					foreach($records_arr as $row){
						echo '<tr>
							<td>'.$row['subject'].'</td>
							<td>'.date('F d, Y', strtotime($row['timestamp'])).'</td>
							<td class="right"><a href="'.PAGE_URL.'?action=edit&item_id='.$row[$record_id].'" class="button-sm"><i class="fas fa-eye"></i>View</a></td>
						</tr>';
					}

				echo '</tbody>
			</table>';

			$CMSBuilder->tablesorter_pager();
		
		echo '</div>
	</div>';

}else{
	
	echo "<form action='' method='post' enctype='multipart/form-data'>";

		//Details
		echo '<div class="panel">
			<div class="panel-header">'.$record_name.' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">
				<table cellpadding="0" cellspacing="0" border="0" width="100%">
					<tr>
						<td width="200px">Subject:</td>
						<td>'.$row['subject'].'</td>
					</tr>
					<tr>
						<td width="200px">Date Submitted:</td>
						<td>'.date('F d, Y', strtotime($row['timestamp'])).'</td>
					</tr>';

					//Display standalone fields
					if(($row['form_fields'] ?? false) && isset($row['form_fields'][0])){
						foreach(($row['form_fields'][0]['form_fields'] ?? []) as $field){
						
							if($field['type'] == 'file'){
								
								 $field['value'] = '<a href="'.$root.'uploads/files/'.$field['value'].'" target="_blank">'.$field['value'].'</a>';
							}
							echo '<tr>
								<td width="200px">'.$field['label'].':</td>
								<td>'.$field['value'].'</td>
							</tr>';
						}
					}
				echo '</table>
			</div>
		</div>';

		//Fieldsets
		if($row['form_fields'] ?? false){
			foreach($row['form_fields'] as $fieldset_id => $fieldset){
				if($fieldset_id){
					echo '<div class="panel">
						<div class="panel-header">'.$fieldset['legend'].'
							<span class="panel-toggle fas fa-chevron-up"></span>
						</div>
						<div class="panel-content nopadding">
							<table cellpadding="0" cellspacing="0" border="0" width="100%">';
								foreach($fieldset['form_fields'] as $field){
									
									if($field['type'] == 'file'){
										// var_dump($field['value']);exit;
									 $field['value'] = '<a href="'.$root.'uploads/files/'.$field['value'].'" target="_blank">'.$field['value'].'</a>';
									}
									echo '<tr>
										<td width="200px">'.$field['label'].':</td>
										<td>'.$field['value'].'</td>
									</tr>';
								}
							echo '</table>
						</div>
					</div>';
				}
			}
		}

		//Sticky footer
		echo '<footer id="cms-footer">
			<div class="flex-container">
				<div class="flex-column left">';
					if(ITEM_ID != ""){
						echo '<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn" data-confirm="Are you sure you want to permanently delete this entry? This action is NOT undoable."><i class="fas fa-trash-alt"></i>Delete</button>';
					}
					echo '<a href="' .PAGE_URL. '" class="cancel">Back</a>
				</div>
			</div>
		</footer>';

		echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

}

?>