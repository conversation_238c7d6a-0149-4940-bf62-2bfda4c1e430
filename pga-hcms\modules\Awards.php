<?php 

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$total_records = $db->get_record_count('awards');
	$CMSBuilder->set_widget($_cmssections['award-categories'], 'Total Awards', $total_records);
}

if(SECTION_ID == $_cmssections['award-categories']){
	
	//Define vars
	$record_db = 'awards';
	$record_id = 'award_id';
	$record_name = 'Category';

	
	$imagedir = "../images/heroes/";
	

	$CMSUploader = new CMSUploader('banner', $imagedir);
	$errors = false;
	$required = array();
	$required_fields = array('name' => $record_name.' Name'); // for validation
	
	$seo_page_id = $_sitepages['careers'];
	$awards_page = get_page_url($seo_page_id);

	//Get Records
	$records_arr = array();
	$params = array();
	
	$query = $db->query("SELECT * FROM `$record_db` ORDER BY `ordering`");
	if($query && !$db->error()) {
		$result = $db->fetch_array();
		foreach($result as $row) {
			$records_arr[$row[$record_id]] = $row;
			$records_arr[$row[$record_id]]['sub_items'] = array();
		}

		$lvl = 1; //tracking depth of array
		$records_arr = build_hierarchy($records_arr, $record_id);
		foreach($records_arr as $record_key => &$record){
			if($record['parent_id'] && array_key_exists($record['parent_id'], $records_arr)){
				$records_arr[$record['parent_id']]['sub_items'][$record_key] = &$record;
				$records_arr[$record['parent_id']]['sub_items'][$record_key]['parent_name'] = $records_arr[$record['parent_id']]['name'];
			}
		}
	}

	//Search
	if(ACTION == '') {
		$_GET['search'] = $CMSBuilder->system_search(SECTION_ID);
		if(isset($_GET['search']) && $_GET['search'] != '') {
			$search_result = array();
			foreach($records_arr as $key => $search_record) {
				if(stripos($search_record['name'],$_GET['search']) !== false){
					$search_result[$key] = $search_record;
				}
			}
			$records_arr = $search_result;
		}
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			$row = $records_arr[ITEM_ID];	
		}
	}

	//Delete item
	if(isset($_POST['delete'])){
		if(ITEM_ID == 1){
			$CMSBuilder->set_system_alert('You cannot delete the default category `'.$_POST['name'].'`.', false);
		}else{
			$db->query("DELETE FROM $record_db WHERE $record_id = ?", [ITEM_ID]);
			if(!$db->error()){
				$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
				sitemap_XML();
				$CMSBuilder->set_system_alert($record_name.' was successfully deleted.', true);
			}else{
				$CMSBuilder->set_system_alert('Unable to delete record. '.$db->error(), false);	
			}
			header("Location: " .PAGE_URL);
			exit();
		}

	//Save item
	}else if(isset($_POST['save'])){

		$required_missing = false;
		if(!empty($required_fields)) {
			foreach($required_fields as $field_key => $field_name) {
				if(isset($_POST[$field_key])) {
					if(trim($_POST[$field_key]) == '') {
						$required_missing = true;
						array_push($required, $field_key);
					}
				} else {
					$required_missing = true;
					array_push($required, $field_key);
				}
			}
		}
		if($required_missing) {
			$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
		}
		if(!isset($_POST['showhide'])){
			$_POST['showhide'] = 1;
		}

		$pagename = clean_url($_POST['name']);

		if(!isset($_POST['meta_title'])){
			$_POST['meta_title'] = (ITEM_ID != '' ? $records_arr[ITEM_ID]['meta_title'] : NULL);
		}
		if(!isset($_POST['meta_description'])){
			$_POST['meta_description'] = (ITEM_ID != '' ? $records_arr[ITEM_ID]['meta_description'] : NULL);
		}
		if(!isset($_POST['focus_keyword'])){
			$_POST['focus_keyword'] = (ITEM_ID != '' ? $records_arr[ITEM_ID]['focus_keyword'] : NULL);
		}

		if(!$errors) {

			//Delete image
			if(isset($_POST['deleteimage'])){
				$CMSUploader->bulk_delete(['image' => $records_arr[ITEM_ID]['image']]);
			}

			//Upload new image
			try{
				$images = $CMSUploader->bulk_upload($pagename, $records_arr[ITEM_ID] ?? []);
			}catch(Exception $e){
				$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
			}

			$image = $images['image'] ?? ($records_arr[ITEM_ID]['image'] ?? '');

			$params = array(
				ITEM_ID, 
				$_POST['name'],
				$pagename,
				($_POST['parent_id'] != '' ? $_POST['parent_id'] : NULL),
				$_POST['description'],
				$images['image'] ?? NULL,
				$_POST['image_alt'],
				$_POST['showhide'],
				$_POST['ordering'],
				$_POST['meta_title'],
				$_POST['meta_description'],
				$_POST['focus_keyword'], 
				date('Y-m-d H:i:s'),
				date('Y-m-d H:i:s'),

				$_POST['name'],
				$pagename,
				($_POST['parent_id'] != '' ? $_POST['parent_id'] : NULL),
				$_POST['description'],
				$images['image'] ?? NULL,
				$_POST['image_alt'],
				$_POST['showhide'],
				$_POST['ordering'],
				$_POST['meta_title'],
				$_POST['meta_description'],
				$_POST['focus_keyword'], 
				date('Y-m-d H:i:s')
			);

			$insert = $db->query("INSERT INTO `$record_db` (`$record_id`, `name`, `page`, `parent_id`, `description`, `image`, `image_alt`, `showhide`, `ordering`, `meta_title`, `meta_description`, `focus_keyword`, `date_added`, `last_updated`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `name`=?, `page`=?, `parent_id`=?, `description`=?, `image`=?, `image_alt`=?, `showhide`=?, `ordering`=?, `meta_title`=?, `meta_description`=?, `focus_keyword`=?, `last_updated`=?", $params);

			if($insert && !$db->error()){
				$item_id = (ITEM_ID != "" ? ITEM_ID : $db->insert_id());

				if($cms_settings['enhanced_seo']){
					$page_url = $siteurl.$root.$awards_page.$pagename."-".$item_id."/";
					try {
						$save_score = $Analyzer->save_score($_POST['focus_keyword'], $page_url, $pagename, $_POST['title'], ($records_arr[ITEM_ID]['seo_score'] ?? NULL), $item_id, $record_db, $record_id);
						if(!empty($save_score) && (MASTER_USER || SEO_USER)){
							$seo_message = "<br/><small>".$save_score."</small>";
						}
					} catch(Exception $e) {
						unset($e);
					}
				}

				if(!$CMSUploader->crop_queue()){
					$CMSBuilder->set_system_alert($record_name.' was successfully saved.'.($seo_message ?? ''), true);
					header("Location: " .PAGE_URL);
					exit();
				}

			} else {
				$CMSBuilder->set_system_alert('Unable to update record. '.$db->error(), false);
			}

		} else {
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}	
		}

	}else{
		include('modules/CropImages.php');
	}
}

?>
