@charset "utf-8";
/* 
	typography.less
	
	Project: PGA of Alberta
	
*/
/*------ imports ------*/
@import url(https://fonts.googleapis.com/css?family=Lato:400,400i,700,700i,900i|Lora:400,400i,700,700i);
/* 
	definitions.less
	
	Project: PGA of Alberta
	
*/
/* 
	mixins.less
	
	Project: PGA of Alberta
	
*/
.full {
  width: 100%;
}
.fluid-max {
  max-width: 1580px;
  padding: 0 30px;
  display: block;
  margin: 0 auto;
  position: relative;
  box-sizing: border-box;
}
.fluid-min {
  max-width: 1420px;
  padding: 0 30px;
  display: block;
  margin: auto;
  position: relative;
  box-sizing: border-box;
}
.f_right {
  float: right;
  display: block;
}
.f_left {
  float: left;
  display: block;
}
.clear {
  display: block;
  clear: both;
}
.right {
  text-align: right;
}
.left {
  text-align: left;
}
.center {
  text-align: center;
}
.dblock {
  display: block !important;
}
.dinline-block {
  display: inline-block !important;
}
.dinline {
  display: inline !important;
}
.relative {
  position: relative;
}
.nomargin {
  margin: 0 !important;
}
.nopadding {
  padding: 0 !important;
}
.noborder {
  border: 0 !important;
}
.nobg {
  background: none !important;
}
.hidden {
  display: none !important;
}
.circular {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #cccccc;
}
.drop-shadow:before {
  content: '';
  display: block;
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  right: 0;
  height: 26px;
  background: url('../images/ui/drop-shadow.png') center top no-repeat;
}
.overlay {
  position: absolute;
  z-index: 0;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.overlay.theme {
  background: rgba(65, 133, 28, 0.5);
}
.overlay.black {
  background: rgba(0, 0, 0, 0.5);
}
.overlay.solid {
  background: #000000;
}
.responsive-bg {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.parallax {
  background-attachment: fixed;
}
.touch .parallax {
  background-attachment: scroll;
}
@media all and (max-width:1440px) {
  .drop-shadow:before {
    background-size: 100% 26px;
  }
}
@media all and (max-width:1024px) {
  .fluid-max,
  .fluid-min {
    padding: 0 20px;
  }
}
/*------ global elements------*/
body,
th,
td {
  font: italic 21px 'Lato', Arial, Helvetica, sans-serif;
  color: #666666;
  line-height: 30px;
  letter-spacing: -0.025em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
img {
  border: 0;
  -ms-interpolation-mode: bicubic;
  display: inline-block;
  max-width: 100%;
}
p {
  padding: 0 0 20px;
  margin: 0;
}
ul,
ol {
  padding: 0 0 20px;
  margin: 0 0 0 40px;
}
hr {
  border: 0;
  border-top: 1px solid #eeeeee;
  height: 0px;
  background: #fff;
  padding: 0;
  margin: 0 0 20px 0;
  clear: both;
}
quote,
blockquote {
  display: block;
  position: relative;
  padding: 40px 60px;
  margin: 20px 0 40px 0;
  font-size: 42px;
  font-weight: 700;
  line-height: 48px;
  max-width: 800px;
  color: #41851c;
}
quote p,
blockquote p,
quote ol,
blockquote ol,
quote ul,
blockquote ul {
  padding: 0;
}
quote:before,
blockquote:before {
  content: '“';
  display: block;
  position: absolute;
  top: 25px;
  left: -25px;
  opacity: 0.1;
  filter: alpha(opacity=10);
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=10);
  font: italic 350px/250px "Lato", Arial, sans-serif;
  font-weight: 900;
}
table {
  border-collapse: collapse;
  margin: 10px 0 30px;
  border: 1px solid #eeeeee;
}
table th {
  background-color: #41851c;
  color: #fff;
  font-weight: 700;
  font-style: normal;
  font-family: 'Lora', Times, serif;
}
table tr:nth-child(2n+1) td {
  background: #eeeeee;
}
table label {
  display: none;
  margin: 0;
}
table.nobgs td {
  background: none !important;
}
table td.pager {
  background: none !important;
  border-top: 1px solid #eeeeee;
}
table td.pager label {
  display: none;
}
/*------ typography ------*/
/* global links */
a {
  text-decoration: none;
  cursor: pointer;
  -webkit-transition: color 0.3s ease 0s;
  -moz-transition: color 0.3s ease 0s;
  -ms-transition: color 0.3s ease 0s;
  -o-transition: color 0.3s ease 0s;
  transition: color 0.3s ease 0s;
  outline: none;
}
a,
a:link,
a:visited {
  color: #41851c;
}
a:hover,
a:active {
  color: #333333;
}
/* headings reset */
h1,
h2,
h3,
h4,
h5,
h6 {
  padding: 0;
  font-weight: 700;
  font-style: normal;
  font-family: 'Lora', Times, serif;
}
h1 em,
h2 em,
h3 em,
h4 em,
h5 em,
h6 em {
  font-weight: normal;
}
h1,
.cta h2,
#slideshow h2 {
  font-size: 90px;
  line-height: 90px;
  margin: 0;
  color: #fff;
}
h2 {
  font-size: 72px;
  line-height: 72px;
  margin: 0 0 20px;
  color: #41851c;
}
h3 {
  font-size: 42px;
  line-height: 42px;
  margin: 0 0 15px;
  color: #41851c;
}
h4 {
  font-size: 30px;
  line-height: 30px;
  margin: 0 0 10px;
  font-family: 'Lato', Arial, Helvetica, sans-serif;
  font-style: italic;
  font-weight: 900;
  color: #333333;
}
h5 {
  font-size: 24px;
  line-height: 30px;
  margin: 0 0 5px;
  color: #41851c;
}
h6 {
  font-size: 21px;
  line-height: 30px;
  margin: 0;
  font-family: 'Lato', Arial, Helvetica, sans-serif;
  font-style: italic;
  font-weight: 900;
  color: #333333;
}
p + h2,
ul + h2,
ol + h2 {
  margin-top: 20px;
}
p + h3,
ul + h3,
ol + h3 {
  margin-top: 15px;
}
p + h4,
ul + h4,
ol + h4 {
  margin-top: 10px;
}
p + h5,
ul + h5,
ol + h5 {
  margin-top: 5px;
}
table + h2 {
  margin-top: 0;
  padding-top: 20px;
}
table + h3 {
  margin-top: 0;
  padding-top: 15px;
}
table + h4 {
  margin-top: 0;
  padding-top: 10px;
}
table + h5 {
  margin-top: 0;
  padding-top: 5px;
}
small {
  display: inline-block;
  font-size: 70%;
  line-height: 160%;
  color: #999999;
}
.font-large {
  font-size: 30px;
  line-height: 42px;
}
.font-medium {
  font-size: 24px;
}
::selection {
  background: #4b9a20;
  color: #fff;
}
::-moz-selection {
  background: #4b9a20;
  color: #fff;
}
.color-white {
  color: #fff !important;
}
.color-dark {
  color: #333333 !important;
}
.color-light {
  color: #999999 !important;
}
.color-theme1 {
  color: #41851c !important;
}
.color-theme2 {
  color: #ef3e34 !important;
}
.color-red {
  color: #ef3e34 !important;
}
.underline {
  position: relative;
  display: inline-block;
  padding-bottom: 24px;
  margin-bottom: 40px;
  max-width: 90%;
}
.underline:before,
.underline:after {
  content: '';
  position: absolute;
  height: 1px;
  background: rgba(65, 133, 28, 0.5);
}
.underline:before {
  bottom: 5px;
  left: 0;
  right: -10%;
}
.underline:after {
  bottom: 0;
  left: 0;
  right: 20%;
}
.underline.center {
  display: block;
  max-width: none;
}
.underline.center:before {
  left: 20%;
  right: 20%;
  width: 60%;
  max-width: none;
}
.underline.center:after {
  left: 30%;
  right: 30%;
  width: 40%;
  max-width: none;
}
.underline.color-white:before,
.underline.color-white:after {
  background: rgba(255, 255, 255, 0.5);
}
@media all and (max-width:1440px) {
  h1,
  .cta h2,
  #slideshow h2 {
    font-size: 72px;
    line-height: 72px;
  }
  h2 {
    font-size: 60px;
    line-height: 60px;
  }
}
@media all and (max-width:1024px) {
  body,
  th,
  td {
    font-size: 18px;
    line-height: 26px;
  }
  h1,
  .cta h2,
  #slideshow h2 {
    font-size: 60px;
    line-height: 60px;
  }
  h2 {
    font-size: 42px;
    line-height: 42px;
  }
  h3 {
    font-size: 36px;
    line-height: 36px;
  }
  h4 {
    font-size: 24px;
    line-height: 26px;
  }
  h5 {
    font-size: 21px;
    line-height: 26px;
  }
  h6 {
    font-size: 18px;
    line-height: 26px;
  }
  quote,
  blockquote {
    font-size: 30px;
    line-height: 40px;
    margin: 0 0 20px;
    padding: 40px;
  }
  quote:before,
  blockquote:before {
    font-size: 250px;
    line-height: 200px;
    left: -20px;
    top: 20px;
  }
  .font-large {
    font-size: 24px;
    line-height: 32px;
  }
  .font-medium {
    font-size: 21px;
  }
  .underline {
    display: block;
    max-width: none;
    margin-bottom: 30px;
  }
  .underline:before {
    left: 0;
    right: 10%;
  }
  .underline:after {
    left: 0;
    right: 30%;
  }
  .underline.center:before {
    left: 5%;
    right: 5%;
    width: 90%;
  }
  .underline.center:after {
    left: 15%;
    right: 15%;
    width: 70%;
  }
}
@media all and (max-width:768px) {
  table {
    width: 100% !important;
    border: 1px solid #eeeeee;
  }
  table th,
  table td {
    display: block;
    width: auto !important;
    text-align: left;
  }
  table tr.header-row {
    display: none;
  }
  table label {
    display: inline-block;
    font-weight: bold;
  }
  table.noresponsive tr.header-row {
    display: table-row;
  }
  table.noresponsive th,
  table.noresponsive td {
    display: table-cell;
  }
}
@media all and (max-width:480px) {
  h1,
  .cta h2,
  #slideshow h2 {
    font-size: 42px;
    line-height: 42px;
  }
  h2 {
    font-size: 36px;
    line-height: 36px;
  }
  h3 {
    font-size: 30px;
    line-height: 30px;
  }
  .underline:before {
    left: 0;
    right: 0;
  }
  .underline:after {
    left: 0;
    right: 25%;
  }
  .underline.center:before {
    left: 0;
    right: 0;
    width: 100%;
  }
  .underline.center:after {
    left: 15%;
    right: 15%;
    width: 70%;
  }
  quote,
  blockquote {
    font-size: 24px;
    line-height: 30px;
    padding: 40px 30px;
  }
  quote:before,
  blockquote:before {
    font-size: 200px;
    line-height: 180px;
    left: -15px;
    top: 15px;
  }
  .font-large {
    font-size: 21px;
    line-height: 28px;
  }
  .font-medium {
    font-size: 18px;
  }
}
/*------ email templates ------*/
body.email-template {
  background: #eeeeee;
  font-size: 16px;
  line-height: 24px;
  min-width: 650px;
}
body.email-template .button {
  padding: 20px 30px;
  display: inline-block;
  color: #fff;
  border: 1px solid #41851c;
  font-size: 18px !important;
  font-family: 'Lato', Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: italic;
  cursor: pointer;
  outline: none;
  background-color: #41851c;
  text-decoration: none;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -ms-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
body.email-template .button:hover {
  color: #41851c;
  background: #fff;
  border-color: 1px solid #fff;
}
body.email-template small {
  font-size: 85%;
  line-height: 140%;
}
body.email-template table {
  border: 1px solid #eeeeee;
}
body.email-template table tr.header-row,
body.email-template table th {
  display: table-cell;
  font-size: 16px;
  line-height: 24px;
}
body.email-template table td {
  display: table-cell;
  font-size: 16px;
  line-height: 24px;
}
body.email-template table label {
  display: none;
}
