<?php 

//News widget
if((PAGE_ID == $_sitepages['home']['page_id'] || PAGE_ID == $_sitepages['account']['page_id']) && !empty($newsfeed)){
			
	echo '<section id="featured-news" class="panel promo-boxes fluid-max clearfix' .(empty($page['page_panels']) ? ' last-child' : ''). '">';
		echo '<div class="jcarousel-wrapper">
			<div class="jcarousel">
				<ul>';
				foreach($newsfeed as $article){
					$article['page_url'] = $_sitepages['newsfeed']['page_url'].'recent/'.$article['page'].'-'.$article['newsletter_id'].'/';

					$imageurl = '';
					if($article['image'] != '' && file_exists('images/news/thumbs/'.$article['image'])){
						$imageurl = $path.'images/news/thumbs/'.$article['image'];
					}
					$video_id = youtube_id_from_url($article['video']);
					if(!empty($video_id) && $imageurl == ''){
						$imageurl = 'https://img.youtube.com/vi/' .$video_id. '/maxresdefault.jpg';
					}
					if($imageurl == ''){
						$imageurl = $path.'images/news/thumbs/default.jpg';
					}

					echo '<li class="promo-box">
						<a href="' .$article['page_url']. '" class="promo-image">
							<img src="' .$imageurl. '" alt="' .$article['title']. '" width="490" height="290" class="promo carousel-img" />
							<div class="overlay black">
								<div><span>Read Article &rsaquo;</span></div>
							</div>
						</a>
						<div class="promo-content">
							<div class="promo-title">
								<small>
									<time datetime="' .date('Y-m-d H:i', strtotime($article['post_date'])). '">' .date('F j, Y', strtotime($article['post_date'])). '</time> &nbsp; 
									<strong><a href="' .$_sitepages['newsfeed']['page_url']. '?category=' .$article['category_id']. '">' .$article['category_name']. '</a></strong>
								</small>
								<h5>' .($article['priority'] == '1' && isset($article['flagged']) ? '<a href="' .$article['page_url']. '" class="color-red"><i class="fa fa-exclamation-triangle"></i> ' : '<a href="' .$article['page_url']. '">').$article['title']. '</a></h5>
							</div>
						</div>
					</li>';
				}	
				echo '</ul>
			</div>
			<a href="#" class="jcarousel-control-prev"><i class="fa fa-angle-left"></i></a>
			<a href="#" class="jcarousel-control-next"><i class="fa fa-angle-right"></i></a>
		</div>';
		
	echo '</section>';
		
} 

?>