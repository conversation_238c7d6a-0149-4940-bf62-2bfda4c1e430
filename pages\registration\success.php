<?php

//Notice
if(isset($_SESSION['reg']['checkout']['success'])){
	$html .= $Account->important($_SESSION['reg']['checkout']['success']);
	unset($_SESSION['reg']['checkout']['success']);
}

//Buttons
$html .= '<p>
	<a href="' .$_sitepages['my_registrations']['page_url']. '" class="button solid inline"><i class="fa fa-user"></i> ' .$_sitepages['my_registrations']['name']. '</a>
	<a href="' .$_sitepages['home']['page_url']. '" class="button solid inline"><i class="fa fa-home"></i> Go to Home</a>
</p>';

//Receipt
if(isset($_SESSION['purchase_receipt'])){
	//$html .= $_SESSION['purchase_receipt'];
	unset($_SESSION['purchase_receipt']);
}

//Set panel content
$page['page_panels'][64]['content'] .= $html;

?>