<?php

//Sanitize data
if(isset($_POST['keep_tags'])){
	$_POST['keep_tags'] = [];
}
sanitize_form_data();

//Include Sendgrid
// if(!empty($global['sendgrid_key']) && !empty($global['sendgrid_key'])){
// 	include($_SERVER['DOCUMENT_ROOT'].$root."core/plugins/sendgrid/config.php");	
// }

//Emogrifier
include($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Emogrifier.class.php");

//Instantiate Account
//include($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Emogrifier.class.php"); //only include if not already in utils.php
include($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Account.class.php");
$Account = new Account();

//Instatiate SiteBuilder
include($_SERVER['DOCUMENT_ROOT'].$root."core/classes/SiteBuilder.class.php");
$SiteBuilder = new SiteBuilder($path);
$pathbits = $SiteBuilder->pathbits;
$sitemap = $SiteBuilder->get_sitemap();
$global = $SiteBuilder->global_settings();
$page = $SiteBuilder->curr_page_content();
$navigation = $SiteBuilder->get_navigation();
$breadcrumbs = $SiteBuilder->get_breadcrumb();
$browser = !empty($_SERVER['HTTP_USER_AGENT']) ? @get_browser(null, true) : [];
$error404 = $page['error404'];

//Initialize registration
include($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Registration.class.php");
$Registration = new Registration();
$reg_settings = $Registration->get_reg_settings();

//Initialize shopping cart
include($_SERVER['DOCUMENT_ROOT'].$root."core/classes/ShoppingCart.class.php");
$ShoppingCart = new ShoppingCart();
$reg_cart = $ShoppingCart->get_cart();

// Initialize Blog
include_once($_SERVER['DOCUMENT_ROOT'].$root."core/classes/Blog.class.php");
$Blog = new Blog();
$blog_settings = $Blog->blog_settings(); // <-- INIT BLOG CLASS

//Include Sendgrid
if(isset($global['sendgrid_key']) && !empty($global['sendgrid_key'])){
	include($_SERVER['DOCUMENT_ROOT'].$root."includes/plugins/sendgrid/config.php");
}

//Session hijacking prevention
if(!isset($_COOKIE['xid']) || $_COOKIE['xid'] == ""){
	$randomstr = gen_random_string();
	$_COOKIE['xid'] = $randomstr;
	$domain = (strpos($_SERVER['HTTP_HOST'], ':8888') === false) ? $_SERVER['HTTP_HOST'] : false;
	setcookie("xid", $randomstr, 0, "/", $domain, $ssl, true);
}
$_COOKIE['xid'] = strip_data($_COOKIE['xid']);

//Google map
$hasmap = array_filter(array_column($global['locations'], 'google_map'));

//Page constants
define('PAGE_ID', $page['page_id']);
define('PARENT_ID',  $page['parent_id']);
define('GOOGLE_MAP', $hasmap && ($page['google_map'] == -1 ? $global['google_map'] : $page['google_map']));
define('LANDING', $page['type'] == 2);
define('USER_LOGGED_IN', $Account->login_status());

define('MEMBER_ACCESS', (USER_LOGGED_IN && (array_key_exists(1, $Account->roles) || array_key_exists(2, $Account->roles)) ? true : false));

define('HIO_ACCESS', (USER_LOGGED_IN && array_key_exists(9, $Account->roles) ? true : false));

//Dynamic pages
array_walk($_sitepages, function(&$id, $name) use ($sitemap){
	$id = ($sitemap[$id] ?? false);
});

?>