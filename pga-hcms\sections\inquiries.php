<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == ''){

	//Conversion chart
	include("includes/widgets/inquirieschart.php");

	//Action bar
	echo '<div class="flex-container">
		<div class="flex-column left">';
			include("includes/widgets/searchform.php");
		echo '</div>
		<div class="flex-column right">
			<form action="" method="post" enctype="multipart/form-data" target="_self">'.
				($unread > 0 ? '<button type="submit" name="read" value="read" class="button"><i class="fas fa-check-double"></i>Mark All as Read</button>' : '').
				'<button type="submit" name="export" value="export" class="button"><i class="fas fa-download"></i>Export</button>
				<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
			</form>
		</div>
	</div>';
	
	//Display records
	echo '<div class="panel">
		<div class="panel-header">Inquiries 
			<span class="panel-toggle fas fa-chevron-up"></span>
		</div>
		<div class="panel-content nopadding">
			<table cellpadding="0" cellspacing="0" border="0" class="tablesorter">
			
				<thead>
					<th>Type</th>
					<th>Name</th>
					<th>Email</th>
					<th>Subject</th>
					<th>Request Date</th>
					<th data-sorter="false">&nbsp;</th>
				</thead>
				
				<tbody>';
				foreach($records_arr as $row){
					echo '<tr'.(!$row['date_reviewed'] ? ' class="bold"' : '').'>
						<td>' .($row['inquiry'] ?: '<small>N/A</small>'). '</td>
						<td>' .($row['name'] ?: '<small>N/A</small>'). '</td>
						<td>' .($row['email'] ?: '<small>N/A</small>'). '</td>
						<td>' .($row['subject'] ?: '<small>N/A</small>'). '</td>
						<td>' .date("F d, Y",strtotime($row['timestamp'])). '</td>
						<td class="right"><a href="' .PAGE_URL. '?action=edit&item_id=' .$row[$record_id]. '" class="button-sm"><i class="fas fa-eye"></i>View</a></td>
					</tr>';	
				}
				echo '</tbody>
			</table>';

			//Pager
			$CMSBuilder->tablesorter_pager();

		echo '</div>
	</div>';
	

//Display selected
}else if(ACTION == 'edit'){
	
	$data = $records_arr[ITEM_ID];

	echo '<form action="" method="post" enctype="multipart/form-data">';

		//Inquiry details
		echo '<div class="panel">
			<div class="panel-header">' .$record_name. ' Details
				<span class="panel-toggle fas fa-chevron-up"></span>
			</div>
			<div class="panel-content nopadding">
				<table cellpadding="0" cellspacing="0" border="0" width="100%">
					<tr>
						<td width="150px">' .$record_name. ' Type:</td>
						<td>' .$data['inquiry']. '</td>
					</tr>
					<tr>
						<td>Sent On:</td>
						<td>' .date("F d, Y",strtotime($data['timestamp'])). '</td>
					</tr>';

					$display = $data;
					unset($display['inquiry_id']);
					unset($display['inquiry']);
					unset($display['message']);
					unset($display['ip_address']);
					unset($display['timestamp']);
	
					foreach($display as $field => $value){
						if($value){
							echo '<tr>
								<td>' .ucwords(str_replace(['-', '_'], ' ', $field)). ':</td>
								<td>' .$value. '</td>
							</tr>';
						}
					}
					
				echo '</table>
			</div>
		</div>';

		//Message
		if($data['message']){
			echo '<div class="panel">
				<div class="panel-header">Message
					<span class="panel-toggle fas fa-chevron-up"></span>
				</div>
				<div class="panel-content">
					<p>' .nl2br($data['message']). '</p>
				</div>
			</div>';
		}

		//Sticky footer
		echo '<footer id="cms-footer">
			<div class="flex-container">
				<div class="flex-column left">';
					if(ITEM_ID != ""){
						echo '<button type="button" name="delete" value="delete" class="button delete confirm-submit-btn" data-confirm="Are you sure you want to permanently delete this entry? This action is NOT undoable."><i class="fas fa-trash-alt"></i>Delete</button>';
					}
					echo '<a href="' .PAGE_URL. '" class="cancel">Back</a>
				</div>
			</div>
		</footer>';

		echo '<input type="hidden" name="xssid" value="' .$_COOKIE['xssid']. '" />
	</form>';

}

?>