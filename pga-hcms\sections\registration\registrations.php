<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Table listing
if(ACTION == '') {
	
	//Search
	echo "<div class='panel'>";
		echo "<div class='panel-header'>Search ".$record_name."s
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";

		echo "<div class='panel-content clearfix'>";
			echo "<form id='advanced-search-form' class='clearfix' action='' method='get' enctype='multipart/form-data'>";
				echo "<div id='search-fields' class='column clearfix'>";
					echo "<div class='form-field'>
						<label>Search All</label>
						<input type='text' name='search' value='".$searchterm."' class='input' />
					</div>";

					echo "<div class='form-field'>
						<label>Start Date </label>
						<input type='text' name='start_date' value='".(isset($_SESSION['search_start_date'][SECTION_ID]) ? $_SESSION['search_start_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";

					echo "<div class='form-field'>
						<label>End Date </label>
						<input type='text' name='end_date' value='".(isset($_SESSION['search_end_date'][SECTION_ID]) ? $_SESSION['search_end_date'][SECTION_ID] : "")."' class='input datepicker' autocomplete='off' />
					</div>";
				echo "</div>";

				echo "<div class='buttons-wrapper'>";
					echo "<div class='f_right'>";
						echo "<button type='button' class='button' onclick='exportForm(this.form);'><i class='fa fa-download'></i>Export</button> &nbsp;";
						echo "<button type='submit' class='button'><i class='fa fa-search'></i>Search</button>";
					echo "</div>";
					echo "<button type='button' class='button reset' onclick='document.getElementById(\"clear-search-form\").submit();'><i class='fa fa-times'></i>Clear</button>";
				echo "</div>";
	
			echo "</form>";
			echo "<form id='clear-search-form' name='clear-search-form' class='hidden' action='".PAGE_URL."' method='post'>
				<input type='hidden' name='clear-search' value='Clear' />
				<input type='hidden' name='search' value='' />
				<input type='hidden' name='start_date' value='' />
				<input type='hidden' name='end_date' value='' />
				<input type='hidden' name='xssid' value='".$_COOKIE['xssid']."' />
			</form>";
		echo "</div>";
	echo "</div>";

	//Export Form
	echo "<script>
		function exportForm(this_form) {
			this_form.target=\"_blank\"; 
			this_form.action=\"".$path."exports/export-registrations.php\"; 
			this_form.submit(); 
			this_form.target=\"\"; 
			this_form.action=\"\";
		}
	</script>";
	
	//Records 
	echo "<div class='panel'>";
		echo "<div class='panel-header'>".$record_name."s  
			<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
		</div>";
		echo "<div class='panel-content nopadding'>";
			echo "<table cellpadding='0' cellspacing='0' border='0' class='tablesorter'>";
		
			echo "<thead>";
			echo "<th>No.</th>";
			echo "<th class='{sorter:\"monthDayYear\"}'>Date</th>";
			echo "<th>Name</th>";
			echo "<th>Email</th>";
			echo "<th>Phone</th>";
			echo "<th>Total</th>";
			echo "<th class='center'>Registered</th>";
			echo "<th class='center'>Paid</th>";
			echo "<th class='{sorter:false}'>&nbsp;</th>";
			echo "</thead>";
			
			echo "<tbody>";
			foreach($records_arr as $row){
				echo "<tr>";
					echo "<td>" .$row['registration_number']. "</td>";
					echo "<td>" .date('M j, Y', strtotime($row['registration_date'])). "</td>";
					echo "<td>" .$row['first_name']." ".$row['last_name']. "</td>";
					echo "<td>" .$row['email']. "</td>";
					echo "<td>" .$row['phone']. "</td>";
					echo "<td>$" .number_format($row['registration_total']+$row['reg_admin_fee'],2). "</td>";
					echo "<td>" .($row['status'] == '1' ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
					echo "<td>" .($row['paid'] == '1' ? "<span class='show'>Yes</span>" : "<span class='hide'>No</span>"). "</td>";
					echo "<td class='right'><a href='" .PAGE_URL. "?action=edit&item_id=" .$row[$record_id]. "' class='button-sm'><i class='fa fa-eye'></i>View</a></td>";
				echo "</tr>";	
			}
			echo "</tbody>";
			echo "</table>";
			
			//Pager
			$CMSBuilder->tablesorter_pager();
		
		echo "</div>";	
	echo "</div>";

} else {

	$data = $records_arr[ITEM_ID];
	if(!isset($_POST['save'])){
		$row = $data;
	}
	
	echo "<form class='print_el' action='' method='post' enctype='multipart/form-data'>";
	
		//Order Information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>$record_name Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>
					<tr>
						<td width='150px'>Registration No:</td>
						<td>".$row['registration_number']."</td>
					</tr>
					<tr>
						<td>Registration Date:</td>
						<td>".date('M j, Y g:iA', strtotime($row['registration_date']))."</td>
					</tr>
					<tr>
						<td>Registration Status:</td>
						<td>".($row['status'] == '1' ? 'Complete' : 'Incomplete')."</td>
					</tr>
					<tr>
						<td>Payment Status:</td>
						<td>".($row['paid'] == '1' ? 'Processed' : ($row['status'] == '1' ? 'Pending' : 'Failed'))."</td>
					</tr>";
					if(!empty($row['registered_by'])){
						$registered_by = $Account->get_account_profile($row['registered_by']);
						echo "<tr>
							<td>Registered By:</td>
							<td>" .$registered_by['first_name']." ".$registered_by['last_name']."</td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Order Information
	
		//Contact Information
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Contact Information
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding clearfix'>";
				echo "<table cellpadding='0' cellspacing='0' border='0' width='100%'>";
					if(!empty($row['account_id'])){
						echo "<tr>
							<td>Account No:</td>
							<td><a href='" .$sitemap[$_cmssections['manage_users']]['page_url']. "?action=edit&item_id=" .$row['account_id']. "'>" .$row['account_id']."</a></td>
						</tr>";
					}
					echo "<tr>
						<td width='150px'>Name:</td>
						<td>".$row['first_name']." ".$row['last_name']."</td>
					</tr>
					<tr>
						<td>Email:</td>
						<td>".$row['email']."</td>
					</tr>
					<tr>
						<td>Phone:</td>
						<td>".$row['phone']."</td>
					</tr>";
					if(trim($row['company']) != ''){
						echo "<tr>
							<td>Company:</td>
							<td>".$row['company']."</td>
						</tr>";
					}
					if(trim($row['facility']) != ''){
						echo "<tr>
							<td>Facility:</td>
							<td>".$row['facility']."</td>
						</tr>";
					}
					if(trim($row['address1']) != ''){
						echo "<tr>
							<td valign='top'>Mailing Address:</td>
							<td>".(trim($row['address2']) != '' ? $row['address2'].' - ' : '').$row['address1']."<br/>".$row['city'].", ".$row['province'].", ".$row['country']."<br/>".$row['postal_code']."</td>
						</tr>";
					}
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Contact Information

		//Events
		$event_type = NULL;
		if(!empty($row['events'])){
			foreach($row['events'] as $event){
				$event_type = $event['event_type'];
				echo "<div class='panel'>";
					echo "<div class='panel-header'>".$event['name']." (".format_date_range($event['start_date'], $event['end_date']).")  
						<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
					</div>";

					echo "<div class='panel-content nopadding clearfix'>";
						echo "<table width='100%' cellpadding='0' cellspacing='0'>";
							echo "<thead>";
								echo "<tr>";
									echo "<th width='350px' class='{sorter:false}'>Attendee</th>";
									echo "<th>Status</th>";
									echo "<th class='{sorter:false}'>Extras</th>";
									echo "<th width='150px' class='{sorter:false}'></th>";
									echo "<th width='150px' class='{sorter:false} right'>Price</th>";
								echo "</tr>";
							echo "</thead>";

							echo "<tbody>";
								if(!empty($event['attendees'])){
									foreach($event['attendees'] as $attendee){
										$addons_sum = 0;
										echo "<tr>";
											echo "<td class='v_top'>";
												echo "<a href='" .$sitemap[$_cmssections['registration-attendees']] ['page_url']. '?action=edit&item_id=' .$attendee['attendee_id']. "'>" .$attendee['first_name']." ".$attendee['last_name']. "</a>";
												echo (trim($attendee['email']) != '' ? "<small class='clear'>Email: ".$attendee['email']."</small>" : "");
												echo (trim($attendee['phone']) != '' ? "<small class='clear'>Phone: ".$attendee['phone']."</small>" : "");
												echo (trim($attendee['company']) != '' ? "<small class='clear'>Company: ".$attendee['company']."</small>" : "");
												echo (trim($attendee['facility']) != '' ? "<small class='clear'>Facility: ".$attendee['facility']."</small>" : "");
												echo (trim($attendee['position']) != '' ? "<small class='clear'>Position: ".$attendee['position']."</small>" : "");
												echo (trim($attendee['comments']) != '' ? "<small class='clear'>Comments: ".$attendee['comments']."</small>" : "");
											echo "</td>";
											echo "<td class='v_top'>" .$attendee['reg_status']. "</td>";
											echo "<td class='v_top'>";
												if(!empty($attendee['options'])){
													foreach($attendee['options'] as $att_addon){
														echo $att_addon['name']."<br /><small>".$att_addon['value'];
														if($att_addon['value'] != ""){
															echo " - $".number_format($att_addon['price_adjustment'], 2);
															$addons_sum += $att_addon['price_adjustment'];
														}
														echo "</small><br/>";
													}
												}else{
													echo "None";
												}
												echo "<input type='hidden' name='addons_".$attendee['attendee_id']."' class='addons_sum' value='" .$addons_sum. "' />";
											echo "</td>";
										
											if($row['status'] && $row['total_paid'] == 0){
												
												echo "<td class='v_top right' style='padding-top:22px;'><strong>".$attendee['ticket_type'].":</strong></td>";
												echo "<td class='right v_top'>$<input type='text' name='ticket_price[".$attendee['attendee_id']."]' class='input input_sm number nomargin sum" .(in_array('ticket_price_'.$attendee['attendee_id'], $required) ? ' required' : ''). "' value='".number_format($attendee['ticket_price'], 2, '.', ''). "' /></td>";
												
											}else{
												
												echo "<td class='v_top right'><strong>".$attendee['ticket_type'].":</strong></td>";
												echo "<td class='right v_top'>$".number_format($attendee['ticket_price'], 2). "</td>";
											}
											
										echo "</tr>";
									}
								}
							echo "</tbody>";
						echo "</table>";
					echo "</div>";
				echo "</div>";
			}
		} //Events
		 
		//Totals
		echo "<div id='registration-totals' class='panel'>";
			echo "<div class='panel-header'>Totals 
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table width='100%' cellpadding='0' cellspacing='0'>";
					echo "<tbody>";
						echo "<tr>
							<td class='left'>" .($row['promocode'] != '' ? '<strong>Discount Code:</strong> '.$row['promocode'] : '&nbsp;'). "</td>
							<td class='right'><strong>Discount:</strong></td>";
							
							if($row['status'] && $row['total_paid'] == 0){
								echo "<td class='right'>$<input type='text' name='discount' id='discount' class='input input_sm number nomargin" .(in_array('discount', $required) ? ' required' : ''). "' value='".number_format($row['discount'], 2, '.', '')."' /></td>";
							}else{
								echo "<td class='right'>$".number_format($row['discount'], 2)."</td>";
							}
							
						echo "</tr>";
						echo "<tr>
							<td class='right' colspan='2'><strong>Subtotal:</strong></td>
							<td class='right' id='subtotal'>$".number_format($row['registration_total']-$row['taxes']-$row['fees'],2)."</td>
						</tr>";
						echo "<tr>
							<td class='right' colspan='2'><strong>Taxes:</strong></td>
							<td class='right' id='taxes'>$".number_format($row['taxes'],2)."</td>
						</tr>";
						if($event_type == 2){
							echo "<tr>
								<td class='right' colspan='2'><strong>Skins:</strong></td>";
								if($row['status'] && $row['total_paid'] == 0){
									echo "<td class='right'>$<input type='text' name='fees' id='fees' class='input input_sm number nomargin' value='" .$row['fees'] ."' /></td>";
								}else{
									echo "<td class='right'>$".number_format($row['fees'], 2)."</td>";
								}
							echo "</tr>";
						}
						echo "<tr>
							<td class='right' colspan='2'><strong>Service Fee:</strong></td>
							<td class='right' id='taxes'>$".number_format($row['admin_fee'],2)."</td>
						</tr>";
						echo "<tr id='ordertotal'>
							<td class='right' colspan='2'><strong>Total:</strong></td>
							<td class='right' width='150px'><strong id='total'>$".number_format($row['registration_total']+$row['admin_fee'],2)."</strong></td>
						</tr>";
					echo "</tbody>";
				echo "</table>";
			echo "</div>";
		echo "</div>"; //Totals
	
		//Payments
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Payments
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content nopadding'>";
				echo "<table width='100%' cellpadding='0' cellspacing='0'>";
					echo "<thead>";
						echo "<tr>";
							echo "<th width='150px'>No.</th>";
							echo "<th width='150px'>Date</th>";					
							echo "<th width='250px'>Type</th>";
							echo "<th>Response</th>";
							echo "<th class='right' width='100px'>Status</th>";
							echo "<th class='right' width='150px'>Amount</th>";
						echo "</tr>";
					echo "</thead>";

					echo "<tbody>";
					if(!empty($row['payments'])){
						foreach($row['payments'] as $payment){	
							echo "<tr>";
								echo "<td><a href='" .$sitemap[$_cmssections['transactions-payments']]['page_url']. "?action=edit&item_id=" .$payment['payment_id']. "'>".$payment['payment_number']."</a></td>";
								echo "<td>".date('M j, Y g:iA', strtotime($payment['payment_date']))."</td>";
								echo "<td>";
								if($payment['payment_type'] == 'Credit Card' && !empty($payment['ccnumber'])){
									echo $payment['cctype']." **** **** **** ".$payment['ccnumber']. " &nbsp; " .substr($payment['ccexpiry'], 0, 2)."/".substr($payment['ccexpiry'], -2, 2); 
								}else{
									echo $payment['payment_type'];
								}
							echo "</td>";
								echo "<td>".$payment['message']."</td>";
								echo "<td class='right'>" .($payment['status'] == '1' ? "Processed" : "Failed"). "</td>";
								echo "<td class='right'>$".number_format($payment['amount']+$payment['admin_fee'], 2)."</td>";
							echo "</tr>";
						}
						echo "<tr>
							<td colspan='5' class='right'><strong>Total Paid:</strong></td>
							<td class='right'>$".number_format($row['total_paid']+$row['admin_fee'],2)."</td>
						</tr>";
					}else{
						echo "<tr><td colspan='4'><em>No payments have been received for this registration.</em></td></td>";
					}
					echo "</tbody>";
				echo "</table>";
				
				if($row['status'] == '1'){
					if(($row['total_paid']-$row['total_refunded']) < $row['registration_total']){
						echo "<div class='pager left'><a href='" .$sitemap[$_cmssections['transactions-payments']]['page_url']. "?action=process&item_id=".ITEM_ID."&record=registration' class='button'><i class='fa fa-credit-card'></i>Pay Now</a></div>";
					}else{
						echo "<div class='pager left'><a class='button disabled'><i class='fa fa-credit-card'></i>Pay Now</a> ".$CMSBuilder->tooltip('Pay Now', 'This registration is paid in full.')."</div>";
					}
				}

			echo "</div>"; 
		echo "</div>"; //Payments

		//Refunds
		if($row['total_paid'] > 0 && $row['registration_total'] > 0){
			echo "<div class='panel'>";
				echo "<div class='panel-header'>Refunds
					<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
				</div>";
				echo "<div class='panel-content nopadding'>";
					echo "<table width='100%' cellpadding='0' cellspacing='0'>";
						echo "<thead>";
							echo "<tr>";
								echo "<th width='150px'>No.</th>";
								echo "<th width='150px'>Date</th>";					
								echo "<th width='250px'>Type</th>";
								echo "<th>Response</th>";
								echo "<th class='right' width='100px'>Status</th>";
								echo "<th class='right' width='150px'>Amount</th>";
							echo "</tr>";
						echo "</thead>";
	
						echo "<tbody>";
							if(!empty($row['refunds'])){
								foreach($row['refunds'] as $refund){	
									echo "<tr>";
										echo "<td><a href='" .$sitemap[$_cmssections['transactions-refunds']]['page_url']. "?action=edit&item_id=" .$refund['refund_id']. "'>".$refund['refund_number']."</a></td>";
										echo "<td>".date('M j, Y g:iA', strtotime($refund['refund_date']))."</td>";
										echo "<td>";
										if($refund['refund_type'] == 'Credit Card' && !empty($refund['ccnumber'])){
											echo $refund['cctype']." **** **** **** ".$refund['ccnumber']. " &nbsp; " .substr($refund['ccexpiry'], 0, 2)."/".substr($refund['ccexpiry'], -2, 2); 
										}else{
											echo $refund['refund_type'];
										}
										echo "</td>";
										echo "<td>".$refund['message']."</td>";
										echo "<td class='right'>" .($refund['status'] == '1' ? "Processed" : "Failed"). "</td>";
										echo "<td class='right'>$".number_format($refund['amount'], 2)."</td>";
									echo "</tr>";
								}
								echo "<tr>
									<td colspan='5' class='right'><strong>Total Refunded:</strong></td>
									<td class='right'>$".number_format($row['total_refunded'],2)."</td>
								</tr>";
							}else{
								echo "<tr><td colspan='4'><em>No refunds have been applied to this registration.</em></td></td>";
							}
						echo "</tbody>";
					echo "</table>";
			
					if($row['total_paid'] > $row['total_refunded']){
						echo "<div class='pager left'><a href='" .$sitemap[$_cmssections['transactions-refunds']]['page_url']. "?action=process&item_id=".ITEM_ID."&record=registration' class='button'><i class='fa fa-undo'></i>Refund</a></div>";
					}else{
						echo "<div class='pager left'><a class='button disabled'><i class='fa fa-undo'></i>Refund</a> ".$CMSBuilder->tooltip('Refund Registration', 'This transaction is not eligible for a refund.')."</div>";
					}
			
				echo "</div>"; 
			echo "</div>"; // Refunds

		}
	
		//Notes
		echo "<div class='panel'>";
			echo "<div class='panel-header'>Notes ".$CMSBuilder->tooltip('Notes', 'Notes are for internal use only. They will not appear on the front-end of the website.')."
				<span class='f_right'><a class='panel-toggle fa fa-chevron-up'></a></span>
			</div>";
			echo "<div class='panel-content clearfix'>";
				echo "<textarea name='notes' class='textarea'>".(isset($row['notes']) ? $row['notes'] : "")."</textarea>";
			echo "</div>";
		echo "</div>"; //Notes

		//Sticky footer
		echo "<footer id='cms-footer' class='resize'>";
			echo "<button type='submit' class='button f_right' name='save' value='save'><i class='fa fa-check'></i>Save Changes</button>";
			echo "<button type='submit' class='button f_right' name='download' value='download'><i class='fa fa-file-pdf-o'></i>Download</button>";
			echo "<a href='".PAGE_URL."' class='cancel'>Cancel</a>";
		echo "</footer>";

		echo "<input type='hidden' name='xssid' value='" .$_COOKIE['xssid'] ."' />";

	echo "</form>";
	
?>

<script type="text/javascript">
	function numberWithCommas(n){
		var parts = n.toString().split(".");
		return parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",") + (parts[1] ? "." + parts[1] : "");
	}
	
	$('input.sum, input#discount, input#fees').on('input', function(){
		var tax_rate_percent = <?php echo (($row['gst_rate']+$row['pst_rate'])/100); ?>;
		var discount = parseFloat($('input#discount').val() != '' ? $('input#discount').val() : 0);
		var fees =  parseFloat($('input#fees').length && $('input#fees').val() != '' ? $('input#fees').val() : 0);
		
		var subtotal = 0;
		$('input.sum').each(function(){
			var this_sum = parseFloat($(this).val() != '' ? $(this).val() : 0);
			subtotal += this_sum;
		});
		$('input.addons_sum').each(function(){
			var this_sum = parseFloat($(this).val() != '' ? $(this).val() : 0);
			subtotal += this_sum;
		});
		if(discount > subtotal){
		   discount = subtotal;
		   subtotal = 0;
		}else{
			subtotal = subtotal-discount;
		}
		
		var taxes = subtotal*tax_rate_percent;
		var total = subtotal+taxes+fees;
						 
		subtotal = subtotal.toFixed(2);
		taxes = taxes.toFixed(2);
		total = total.toFixed(2);

		$('#subtotal').html('$'+numberWithCommas(subtotal));		
		$('#taxes').html('$'+numberWithCommas(taxes));		
		$('#total').html('$'+numberWithCommas(total));			 
						 
	});
</script>

<?php } ?>