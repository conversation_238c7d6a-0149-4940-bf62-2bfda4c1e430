<?php

//Check for valid login
if(!defined('USER_LOGGED_IN') || !USER_LOGGED_IN){
	exit();
}

//Dashboard widget
if(SECTION_ID == $_cmssections['dashboard']){
	$get_total = $db->query("SELECT `event_id` FROM `reg_events` WHERE `event_type` = 2 && `status` = ?", array('Active'));
	if($get_total && !$db->error()) {
		$CMSBuilder->set_widget($_cmssections['registration-tournaments'], 'Total Tournaments', $db->num_rows());
	}
	$get_total = $db->query("SELECT `event_id` FROM `reg_events` WHERE `event_type` = 1 && `status` = ?", array('Active'));
	if($get_total && !$db->error()) {
		$CMSBuilder->set_widget($_cmssections['registration-events'], 'Total '.EVENT_CODE.'s', $db->num_rows());
	}
}

if(SECTION_ID == $_cmssections['registration-events'] || SECTION_ID == $_cmssections['registration-tournaments']){

	// print_r($_POST);
	// print_r($_FILES);

	// if(SECTION_ID == $_cmssections['registration-events']){
	// 	exit('Events');
	// }else if(SECTION_ID == $_cmssections['registration-tournaments']){
	// 	exit('Tournaments');
	// }

	//Define vars
	$record_db = 'reg_events';
	$record_id = 'event_id';
	$record_name = EVENT_CODE;

	define('EVENT_TYPE', (SECTION_ID == $_cmssections['registration-events'] ? 1 : 2));

	$imagedir = "../images/heroes/";
	$cropimages = array();
	$errors = false;
	$required = array();

	//Banner crop sizes
	$imagesizes[] = array('dir' => '1920/', 'width'=>$_cropsizes['Pages'][1920]['width'], 'height'=>$_cropsizes['Pages'][1920]['height'], 'label'=>'Banner Image (Desktop)');
	$imagesizes[] = array('dir' => '1366/', 'width'=>$_cropsizes['Pages'][1366]['width'], 'height'=>$_cropsizes['Pages'][1366]['height'], 'label'=>'Banner Image (Notebook)');
	$imagesizes[] = array('dir' => '1024/', 'width'=>$_cropsizes['Pages'][1024]['width'], 'height'=>$_cropsizes['Pages'][1024]['height'], 'label'=>'Banner Image (Tablet - Landscape)');
	$imagesizes[] = array('dir' => '768/', 'width'=>$_cropsizes['Pages'][768]['width'], 'height'=>$_cropsizes['Pages'][768]['height'], 'label'=>'Banner Image (Tablet - Portrait)');
	$imagesizes[] = array('dir' => '480/', 'width'=>$_cropsizes['Pages'][480]['width'], 'height'=>$_cropsizes['Pages'][480]['height'], 'label'=>'Banner Image (Mobile)');
	$total_imagesizes = count($imagesizes);

	// Initialize CMSUploader for banner images
	$CMSUploader = new CMSUploader('banner', $imagedir);

	if(STEP == '' || STEP == 1){

		//Get categories
		$default_category = "";
		$all_categories = array();
		$get_all_categories = $db->query("SELECT * FROM `reg_categories` WHERE `reg_system_id` = 1 AND `status` != ? AND `parent_id` = ? ORDER BY `name`", array('Trashed', EVENT_TYPE));
		if($get_all_categories && !$db->error() && $db->num_rows() > 0) {
			$result = $db->fetch_array();
			foreach($result as $category) {
				$all_categories[$category['category_id']] = $category;
				$all_categories[$category['category_id']]['sub_items'] = array();
			}
		}

		//Facilities
		$facilities = array();
		$query = $db->query("SELECT `facility_id`, `facility_name` FROM `facilities` ORDER BY `facility_name` ASC");
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				$facilities[$row['facility_id']] = $row;
			}
		}

		//Get CTAs
		$ctas = array();
		$query = $db->query("SELECT * FROM `pages_cta` ORDER BY `title`");
		if($query && !$db->error() && $db->num_rows() > 0) {
			$ctas = $db->fetch_array();
		}

		//Get galleries
		$galleries = array();
		$query = $db->query("SELECT * FROM `galleries` ORDER BY `name` ASC");
		if($query && !$db->error()){
			$galleries = $db->fetch_array();
		}

	}
	if(STEP == 1){

		//Member categories
		$member_categories = array();
		$query = $db->query("SELECT `category_id`, `name` FROM `membership_categories` ORDER BY `name` ASC");
		if($query && !$db->error()){
			$member_categories = $db->fetch_array();
		}

		//Member classes
		$member_classes = array();
		$query = $db->query("SELECT `class_id`, `class_name` FROM `membership_classes` ORDER BY `class_name` ASC");
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				$member_classes[$row['class_id']] = $row;
			}
		}

		//Sponsors
		$sponsor_types = $db->get_enum_vals('reg_occurrence_sponsors', 'type');
		$sponsors = array();
		$query = $db->query("SELECT `partner_id` AS `sponsor_id`, `name` FROM `partners` ORDER BY `name` ASC");
		if($query && !$db->error()){
			$result = $db->fetch_array();
			foreach($result as $row){
				$sponsors[$row['sponsor_id']] = $row;
			}
		}

		//PD categories
		$pd_categories = array();
		if(EVENT_TYPE != 2){
			$query = $db->query("SELECT `name`, `category_id` FROM `pd_categories` WHERE `parent_id` = 10 && `category_id` <= 3 ORDER BY `ordering`");
			if($query && !$db->error()){
				$pd_categories = $db->fetch_array();
			}
		}

	}
	if(STEP == 2){

		//Get GL Accounts
		$glaccounts = array();
		$query = $db->query("SELECT * FROM `gl_accounts` ORDER BY `gl_name`");
		if($query && !$db->error()){
			$glaccounts = $db->fetch_array();
		}
	}
	if(STEP == 3){

		//Get all waivers
		$all_waivers = array();
		$get_all_waivers = $db->query("SELECT * FROM `reg_waivers` WHERE `event_type` IS NULL ORDER BY `title`");
		if($get_all_waivers && !$db->error() && $db->num_rows() > 0) {
			$result = $db->fetch_array();
			foreach($result as $waiver) {
				$all_waivers[$waiver['waiver_id']] = $waiver;
			}
		}

		//Get all forms
		$all_forms = array();
		$get_all_forms = $db->query("SELECT * FROM `forms` WHERE `login` = 1 ORDER BY `form_name`");
		if($get_all_forms && !$db->error() && $db->num_rows() > 0) {
			$result = $db->fetch_array();
			foreach($result as $form) {
				$all_forms[$form['form_id']] = $form;
			}
		}
	}

	//Get Records
	$records_arr = array();
	$archived_records = array();
	$current_records = array();
	$params = array('Registered', 'Trashed', EVENT_TYPE);
	$where = "WHERE `$record_db`.`status` != ? AND `$record_db`.`event_type` = ?";

	if(ACTION == ""){
		if($searchterm != ""){
			$where .= " AND `$record_db`.`name` LIKE ?";
			$params[] = '%' .$searchterm. '%';
		}
		if(isset($_GET['category_id'])){
			$_SESSION['search_category_id'][SECTION_ID] = $_GET['category_id'];
		}
		if(isset($_SESSION['search_category_id'][SECTION_ID]) && $_SESSION['search_category_id'][SECTION_ID] != ''){
			$where .= " AND `reg_event_categories`.`category_id` = ?";
			$params[] = $_SESSION['search_category_id'][SECTION_ID];
		}
	}

	$query = $db->query("SELECT `$record_db`.*, `reg_locations`.*, `reg_locations`.`name` AS `location_name`, `reg_occurrences`.*, `$record_db`.`name` AS `name`, `reg_event_categories`.`category_id`, GROUP_CONCAT(DISTINCT `reg_event_categories`.`category_id`) AS `assigned_categories`, (SELECT COUNT(*) FROM `reg_attendees` WHERE `reg_attendees`.`occurrence_id` = `reg_occurrences`.`occurrence_id` AND `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NULL) AS `attendance` FROM `$record_db` ".
	"LEFT JOIN `reg_event_categories` ON `reg_event_categories`.`$record_id` = `$record_db`.`$record_id` ".
	"LEFT JOIN `reg_occurrences` ON `$record_db`.`$record_id` = `reg_occurrences`.`$record_id` ".
	"LEFT JOIN `reg_locations` ON `reg_occurrences`.`occurrence_id` = `reg_locations`.`occurrence_id`".
	$where. " GROUP BY `$record_db`.`$record_id` ORDER BY `$record_db`.`name` ASC, `$record_db`.`last_updated` DESC", $params);
	if($query && !$db->error()){
		$result = $db->fetch_array();
		foreach($result as $row){
			if($row['end_date'] < date("Y")."-01-01"){
				array_push($archived_records, $row);
			}else{
				array_push($current_records, $row);
			}
			$records_arr[$row[$record_id]] = $row;
		}
	}else{
		$CMSBuilder->set_system_alert('Unable to retrieve data. '.$db->error(), false);
	}

	//Not found
	if(ACTION == 'edit'){
		if(!array_key_exists(ITEM_ID, $records_arr)){
			$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
			header('Location:' .PAGE_URL);
			exit();
		}else{
			if(STEP == 1){

				//Get eligibility
				$records_arr[ITEM_ID]['eligibility'] = array();
				$query = $db->query("SELECT `category_id` FROM `reg_event_eligibility` WHERE $record_id = ?", array(ITEM_ID));
				if($query && !$db->error() && $db->num_rows() > 0) {
					$result = $db->fetch_array();
					foreach($result as $eligibility) {
						$records_arr[ITEM_ID]['eligibility'][] = $eligibility['category_id'];
					}
					$records_arr[ITEM_ID]['eligibility'] = implode(', ', $records_arr[ITEM_ID]['eligibility']);
				}

				//Get membership classes
				$records_arr[ITEM_ID]['membership_classes'] = array();
				$query = $db->query("SELECT `class_id` FROM `reg_event_membership_classes` WHERE $record_id = ?", array(ITEM_ID));
				if($query && !$db->error() && $db->num_rows() > 0) {
					$result = $db->fetch_array();
					foreach($result as $memberclass) {
						$records_arr[ITEM_ID]['membership_classes'][] = $memberclass['class_id'];
					}
				}

				//Get sponsors
				$records_arr[ITEM_ID]['assigned_sponsors'] = array();
				$query = $db->query("SELECT `sponsor_id`, `type` FROM `reg_occurrence_sponsors` WHERE `occurrence_id` = ? ORDER BY `uid`", array($records_arr[ITEM_ID]['occurrence_id']));
				if($query && !$db->error() && $db->num_rows() > 0){
					$result = $db->fetch_array();
					foreach($result as $assigned_sponsors){
						$records_arr[ITEM_ID]['assigned_sponsors'][$assigned_sponsors['type'] ?: $sponsor_types[0]][] = $assigned_sponsors['sponsor_id'];
					}
				}

			}else if(STEP == 2){

				//Get tickets
				$records_arr[ITEM_ID]['pricing'] = array();
				$query = $db->query("SELECT * FROM `reg_event_pricing` WHERE $record_id = ?", array(ITEM_ID));
				if($query && !$db->error() && $db->num_rows() > 0) {
					$result = $db->fetch_array();
					foreach($result as $pricing) {
						$pricing['discount_id'] = '';
						$pricing['discount_price'] = '';
						$pricing['discount_ticket_qty'] = '';
						$records_arr[ITEM_ID]['pricing'][$pricing['pricing_id']] = $pricing;
					}
				}

			}else if(STEP == 3){

				//Get event waivers
				$records_arr[ITEM_ID]['assigned_waivers'] = array();
				$query = $db->query("SELECT `waiver_id` FROM `reg_event_waivers` WHERE `$record_id` = ? ORDER BY `uid`", array(ITEM_ID));
				if($query && !$db->error() && $db->num_rows() > 0) {
					$result = $db->fetch_array();
					foreach($result as $waiver) {
						$records_arr[ITEM_ID]['assigned_waivers'][] = $waiver['waiver_id'];
					}
				}

				//Get event forms
				$records_arr[ITEM_ID]['assigned_forms'] = array();
				$query = $db->query("SELECT `form_id` FROM `reg_event_forms` WHERE `$record_id` = ? ORDER BY `uid`", array(ITEM_ID));
				if($query && !$db->error() && $db->num_rows() > 0) {
					$result = $db->fetch_array();
					foreach($result as $form) {
						$records_arr[ITEM_ID]['assigned_forms'][] = $form['form_id'];
					}
				}

			}else if(STEP == 4){


				//Get attendee fields
				if(EVENT_TYPE != 2){

					$records_arr[ITEM_ID]['attendee_fields'] = array();
					$query = $db->query("SELECT * FROM `reg_event_attendee_information` WHERE `event_id` = ?", array(ITEM_ID));
					if($query && !$db->error() && $db->num_rows() > 0) {
						$result = $db->fetch_array();
						$records_arr[ITEM_ID]['attendee_fields'] = $result[0];
					}

				//Get tournament entries
				}else if(EVENT_TYPE == 2){

					//Entries
					$records_arr[ITEM_ID]['attendees'] = array();
					$records_arr[ITEM_ID]['attendee_names'] = array();
					$query = $db->query("SELECT `reg_tournament_field`.*, `reg_tournament_field`.`attendee_id` AS `draw_id`, (IFNULL(`reg_tournament_field`.`r1_score`, 0)+IFNULL(`reg_tournament_field`.`r2_score`, 0)) AS `final_score`, (IFNULL(`reg_tournament_field`.`r1_team_gross`, 0)+IFNULL(`reg_tournament_field`.`r2_team_gross`, 0)) AS `team_gross`, (IFNULL(`reg_tournament_field`.`r1_team_net`, 0)+IFNULL(`reg_tournament_field`.`r2_team_net`, 0)) AS `team_net`, `reg_attendees`.*, `reg_attendees`.`attendee_id` AS `attendee_id`, `facilities`.`facility_name` FROM `reg_attendees` ".
					"LEFT JOIN `reg_tournament_field` ON `reg_attendees`.`attendee_id` = `reg_tournament_field`.`attendee_id` ".
					"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
					"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
					"WHERE `reg_attendees`.`occurrence_id` = ? && `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NULL ".
					"ORDER BY `reg_attendees`.`last_name` ASC, `reg_attendees`.`first_name` ASC",
					array($records_arr[ITEM_ID]['occurrence_id'], 'Registered'));
					if($query && !$db->error()){
						$result = $db->fetch_array();
						foreach($result as $row){
							$row['partner'] = array();
							$row['handicap'] = (!empty($row['handicap']) ? $row['handicap'] : 0);
							$row['facility_name'] = ($row['facility_name'] != "" ? $row['facility_name'] : "No Facility");
							$row['team_final_score'] = $row['team_gross'].'/'.$row['team_net'];
							$records_arr[ITEM_ID]['attendees'][$row['attendee_id']] = $row;
							$records_arr[ITEM_ID]['attendee_names'][$row['last_name'].', '.$row['first_name']] = $row['first_name'].' '.$row['last_name'];
						}

					}

					//Partners
					if($records_arr[ITEM_ID]['team_event']){
						$query = $db->query("SELECT `reg_attendees`.*, `facilities`.`facility_name` FROM `reg_attendees` ".
						"LEFT JOIN `account_profiles` ON `reg_attendees`.`account_id` = `account_profiles`.`account_id` ".
						"LEFT JOIN `facilities` ON `account_profiles`.`facility_id` = `facilities`.`facility_id` ".
						"WHERE `reg_attendees`.`occurrence_id` = ? && `reg_attendees`.`reg_status` = ? && `reg_attendees`.`partner_id` IS NOT NULL ".
						"ORDER BY `reg_attendees`.`last_name` ASC, `reg_attendees`.`first_name` ASC",
						array($records_arr[ITEM_ID]['occurrence_id'], 'Registered'));
						if($query && !$db->error()){
							$result = $db->fetch_array();
							foreach($result as $row){
								if(array_key_exists($row['partner_id'], $records_arr[ITEM_ID]['attendees'])){
									$row['handicap'] = (!empty($row['handicap']) ? $row['handicap'] : 0);
									$row['facility_name'] = ($row['facility_name'] != "" ? $row['facility_name'] : "No Facility");
									$records_arr[ITEM_ID]['attendees'][$row['partner_id']]['partner'] = $row;
									$records_arr[ITEM_ID]['attendee_names'][$row['last_name'].', '.$row['first_name']] = $row['first_name'].' '.$row['last_name'];
								}
							}
						}
					}

				}

			}else if(STEP == 5){

				//Get addons
				$records_arr[ITEM_ID]['addons'] = array();
				$query = $db->query("SELECT * FROM `reg_occurrence_addons` WHERE `occurrence_id` = ? ORDER BY `addon_id`", array($records_arr[ITEM_ID]['occurrence_id']));
				if($query && !$db->error() && $db->num_rows() > 0){
					$result = $db->fetch_array();
					foreach($result as $addon) {
						$addon['options'] = array();

						//Get options
						$query2 = $db->query("SELECT * FROM `reg_occurrence_options` WHERE `addon_id` = ? ORDER BY `option_id` ASC", array($addon['addon_id']));
						if($query2 && !$db->error() && $db->num_rows() > 0){
							$result2 = $db->fetch_array();
							foreach($result2 as $option) {
								$addon['options'][$option['option_id']] = $option;
							}
						}

						$records_arr[ITEM_ID]['addons'][$addon['addon_id']] = $addon;
					}
				}

			}else{
				$CMSBuilder->set_system_alert('Requested item was not found. Please select from the list below.', false);
				header('Location:' .PAGE_URL);
				exit();
			}

			$row = $records_arr[ITEM_ID];
		}
	}

	//Steps
	$form_steps = array(
		1 => array('section_name' => 'General', 'step_name' => 1),
		2 => array('section_name' => 'Pricing', 'step_name' => 2),
		3 => array('section_name' => 'Waivers', 'step_name' => 3),
		4 => array('section_name' => (EVENT_TYPE == 2 ? 'Field' : 'Fields'), 'step_name' => 4),
		5 => array('section_name' => (EVENT_TYPE == 2 ? 'Reports' : 'Extras'), 'step_name' => 5)
	);

	//Restricted Steps
	$restricted_steps = array();
	if(ACTION == 'add') {
		$restricted_steps = array(2,3,4,5);
	}

	//Redirect to First Step
	if(in_array(STEP, $restricted_steps)){
		$CMSBuilder->set_system_alert('You cannot skip a required step. Please complete the form below to move on to the next step.', false);
		header('Location:' .PAGE_URL.'?action='.ACTION.'&step=1');
		exit();
	}

	//Delete item
	if(isset($_POST['delete'])){

		$delete = $db->query("UPDATE `$record_db` SET `status` = ? WHERE `$record_id` = ?", array('Trashed', ITEM_ID));
		if($delete && !$db->error()) {
			$CMSUploader->bulk_delete($records_arr[ITEM_ID]);
			$CMSBuilder->set_system_alert(EVENT_CODE.' was successfully deleted.', true);
			sitemap_XML();
		} else {
			$CMSBuilder->set_system_alert('Unable to delete '.EVENT_CODE.'. ' .$db->error(),false);
		}
		header("Location: " .PAGE_URL);
		exit();

	//Save item
	}else if(isset($_POST['save'])){

		//Event Info
		if(STEP == 1) {

			$required_fields = array('name' => 'Name', 'category_id' => 'Category', 'description' => 'Description'); // for validation
			if(EVENT_TYPE == 2){
				$required_fields['facility_id'] = 'Facility';
				$required_fields['max_capacity'] = 'Field';
			}else{
				$required_fields['location_name'] = 'Location Name';
			}
			$required_fields['start_date'] = 'Start Date';
			$required_fields['end_date'] = 'End Date';
			$required_fields['reg_open'] = 'Registration Opens';
			$required_fields['reg_deadline'] = 'Registration Deadline';
			$required_fields['payment_deadline'] = 'Payment Deadline';

			//Validate
			$required_missing = false;
			if(!empty($required_fields)) {
				foreach($required_fields as $field_key => $field_name) {
					if(isset($_POST[$field_key])) {
						if(trim($_POST[$field_key]) == '') {
							$required_missing = true;
							array_push($required, $field_key);
						}
					} else {
						$required_missing = true;
						array_push($required, $field_key);
					}
				}
			}
			if($required_missing) {
				$errors[] = 'Please fill out all the <span class="required">*</span> required fields.';
			}

			//Date validation
			if(strtotime($_POST['start_date'])>strtotime($_POST['end_date'])){
				$errors[] = 'Start date must be before the end date.';
				array_push($required, 'start_date');
			}
			if(strtotime($_POST['reg_open'])>strtotime($_POST['start_date'])){
				$errors[] = 'Registration open date must be before the start date.';
				array_push($required, 'reg_opens');
			}
			if(strtotime($_POST['reg_deadline'])>strtotime($_POST['start_date'])){
				$errors[] = 'Registration deadline must be before the start date.';
				array_push($required, 'reg_deadline');
			}
			if(EVENT_TYPE != 2){
				$_POST['withdrawal_deadline'] = $_POST['payment_deadline'];
			}
			if(strtotime($_POST['withdrawal_deadline'])<strtotime($_POST['payment_deadline'])){
				$errors[] = 'Withdrawal deadline cannot be before the payment deadline.';
				array_push($required, 'withdrawal_deadline');
			}
			if(!empty($_FILES['image']['size']) && $_FILES['image']['size'] > 20480000){
				$errors[] = 'Image filesize is too large.';
			}

			//Create safe page name
			$pagename = clean_url($_POST['name']);

			if(!$errors){

				// Handle image upload and deletion using CMSUploader
				$image = $_POST['old_image'] ?? null;
				$crop_required = false;

				try {
					// Delete existing image if requested
					if (isset($_POST['deleteimage']) && !empty($image)) {
						$CMSUploader->bulk_delete(['image' => $image]);
						$image = null;
					}

					// Upload new image if provided
					if (!empty($_FILES['image']['name']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
						$uploaded_images = $CMSUploader->bulk_upload(
							$pagename,
							['image' => $image] // Pass current image for cleanup
						);

						if (isset($uploaded_images['image'])) {
							$image = $uploaded_images['image'];

							// Check if cropping is needed IMMEDIATELY after upload
							if ($CMSUploader->crop_queue()) {
								$crop_required = true;
							}
						}
					}

					// Check if we need to show crop page
					if (!empty($image) && $CMSUploader->crop_queue()) {
						// Set a redirect URL to come back after cropping
						$redirect_url = PAGE_URL . "?action=edit&item_id=" . ($item_id ?? ITEM_ID) . "&step=" . STEP;
						$crop_required = true;
					}

				} catch (Exception $e) {
					$errors[] = 'Image upload failed: ' . $e->getMessage();
					$CMSBuilder->set_system_alert('Image upload failed. Please try again.', false);
				}

				//Insert to db
				$db->new_transaction();

				$params = array(
					ITEM_ID,
					EVENT_TYPE,
					(isset($_POST['team_event']) ? $_POST['team_event'] : 0),
					$_POST['name'],
					$pagename,
					(isset($_POST['social_sharing']) ? $_POST['social_sharing'] : 0),
					(isset($_POST['attendee_sharing']) ? $_POST['attendee_sharing'] : 0),
					(isset($_POST['waiting_list']) ? $_POST['waiting_list'] : 0),
					(isset($_POST['gender']) && $_POST['gender'] != '' ? $_POST['gender'] : NULL),
					(isset($_POST['age']) && $_POST['age'] != '' ? $_POST['age'] : NULL),
					$role_id,
					$image,
					$_POST['image_alt'],
					($_POST['gallery_id'] != '' ? $_POST['gallery_id'] : NULL),
					(isset($_POST['pd_category_id']) && $_POST['pd_category_id'] != '' ? $_POST['pd_category_id'] : NULL),
					$_POST['description'],
					1,
					date('Y-m-d H:i:s'),
					date('Y-m-d H:i:s'),

					(isset($_POST['team_event']) ? $_POST['team_event'] : 0),
					$_POST['name'],
					$pagename,
					(isset($_POST['social_sharing']) ? $_POST['social_sharing'] : 0),
					(isset($_POST['attendee_sharing']) ? $_POST['attendee_sharing'] : 0),
					(isset($_POST['waiting_list']) ? $_POST['waiting_list'] : 0),
					(isset($_POST['gender']) && $_POST['gender'] != '' ? $_POST['gender'] : NULL),
					(isset($_POST['age']) && $_POST['age'] != '' ? $_POST['age'] : NULL),
					$role_id,
					$image,
					$_POST['image_alt'],
					($_POST['gallery_id'] != '' ? $_POST['gallery_id'] : NULL),
					(isset($_POST['pd_category_id']) && $_POST['pd_category_id'] != '' ? $_POST['pd_category_id'] : NULL),
					$_POST['description'],
					date('Y-m-d H:i:s')
				);
				$insert = $db->query("INSERT INTO `reg_events` (`$record_id`, `event_type`, `team_event`, `name`, `page`, `social_sharing`, `attendee_sharing`, `waiting_list`, `gender`, `age`, `role_id`, `banner_image`, `banner_image_alt`, `gallery_id`, `pd_category_id`, `description`, `showhide`, `date_added`, `last_updated`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `team_event`=?, `name`=?, `page`=?, `social_sharing`=?, `attendee_sharing`=?, `waiting_list`=?, `gender`=?, `age`=?, `role_id`=?, `banner_image`=?, `banner_image_alt`=?, `gallery_id`=?, `pd_category_id`=?, `description`=?, `last_updated`=?", $params);
				if($insert && !$db->error()) {
					$item_id = (ITEM_ID != "" ? ITEM_ID : $db->insert_id());

					//Categories
					$categories = array($_POST['category_id']);
					$delete = $db->query("DELETE FROM `reg_event_categories` WHERE `$record_id` = ?".(!empty($categories) ? " AND `category_id` NOT IN (".implode(",", $categories).")" : ""), array($item_id));
					if(!empty($categories)) {
						foreach($categories as $category_id) {
							$categoryquery = $db->query("INSERT INTO `reg_event_categories`(`$record_id`, `category_id`) VALUES (?,?) ON DUPLICATE KEY UPDATE category_id = ?", array($item_id, $category_id, $category_id));
						}
					}

					//Default pricing and form fields
					if(ITEM_ID == ''){

						if(EVENT_TYPE == 2){
							$params = array($item_id, 'Entry Fee', 0);
							$pricingquery = $db->query("INSERT INTO `reg_event_pricing` (`event_id`, `price_type`, `price`) VALUES (?,?,?)", $params);
						}else{
							$params = array($item_id, 'General', 0);
							$pricingquery = $db->query("INSERT INTO `reg_event_pricing` (`event_id`, `price_type`, `price`) VALUES (?,?,?)", $params);
							$params = array($item_id);
							$fieldsquery = $db->query("INSERT INTO `reg_event_attendee_information` (`event_id`) VALUES (?)", $params);
						}
					}

					//Occurrence
					$occurrence_id = (isset($records_arr[ITEM_ID]['occurrence_id']) ? $records_arr[ITEM_ID]['occurrence_id'] : NULL);
					$params = array(
						$occurrence_id,
						$item_id,
						(isset($_POST['facility_id']) ? $_POST['facility_id'] : NULL),
						($_POST['max_capacity'] != '' ? $_POST['max_capacity'] : NULL),
						($_POST['max_attendees'] != '' ? $_POST['max_attendees'] : NULL),
						$_POST['start_date'],
						$_POST['end_date'],
						$_POST['reg_open'],
						$_POST['reg_deadline'],
						$_POST['payment_deadline'],
						$_POST['withdrawal_deadline'],
						(isset($_POST['email_notifications']) ? $_POST['email_notifications'] : 0),
						(isset($_POST['payment_notifications']) ? $_POST['payment_notifications'] : 0),
						(isset($_POST['purse']) && is_numeric($_POST['purse']) ? number_format($_POST['purse'], 2, '.', '') : NULL),
						(isset($_POST['purse_3_year_avg']) ? number_format($_POST['purse_3_year_avg'], 2, '.', '') : NULL),
						(isset($_POST['skins_pot1']) ? number_format($_POST['skins_pot1'], 2, '.', '') : NULL),
						(isset($_POST['skins_pot2']) ? number_format($_POST['skins_pot2'], 2, '.', '') : NULL),
						(isset($_POST['draw_type']) ? $_POST['draw_type'] : NULL),
						(isset($_POST['scoring_type']) ? $_POST['scoring_type'] : NULL),
						(isset($_POST['par']) && $_POST['par'] != '' ? $_POST['par'] : NULL),
						(isset($_POST['rounds']) && $_POST['rounds'] != '' ? $_POST['rounds'] : NULL),
						($_POST['cta_id'] != '' ? $_POST['cta_id'] : NULL),
						date("Y-m-d H:i:s"),
						date("Y-m-d H:i:s"),

						(isset($_POST['facility_id']) ? $_POST['facility_id'] : NULL),
						($_POST['max_capacity'] != '' ? $_POST['max_capacity'] : NULL),
						($_POST['max_attendees'] != '' ? $_POST['max_attendees'] : NULL),
						$_POST['start_date'],
						$_POST['end_date'],
						$_POST['reg_open'],
						$_POST['reg_deadline'],
						$_POST['payment_deadline'],
						$_POST['withdrawal_deadline'],
						(isset($_POST['email_notifications']) ? $_POST['email_notifications'] : 0),
						(isset($_POST['payment_notifications']) ? $_POST['payment_notifications'] : 0),
						(isset($_POST['purse']) && is_numeric($_POST['purse']) ? number_format($_POST['purse'], 2, '.', '') : NULL),
						(isset($_POST['purse_3_year_avg']) ? number_format($_POST['purse_3_year_avg'], 2, '.', '') : NULL),
						(isset($_POST['skins_pot1']) ? number_format($_POST['skins_pot1'], 2, '.', '') : NULL),
						(isset($_POST['skins_pot2']) ? number_format($_POST['skins_pot2'], 2, '.', '') : NULL),
						(isset($_POST['draw_type']) ? $_POST['draw_type'] : NULL),
						(isset($_POST['scoring_type']) ? $_POST['scoring_type'] : NULL),
						(isset($_POST['par']) && $_POST['par'] != '' ? $_POST['par'] : NULL),
						(isset($_POST['rounds']) && $_POST['rounds'] != '' ? $_POST['rounds'] : NULL),
						($_POST['cta_id'] != '' ? $_POST['cta_id'] : NULL),
						date("Y-m-d H:i:s")
					);
					$insert = $db->query("INSERT INTO `reg_occurrences`(`occurrence_id`, `$record_id`, `facility_id`, `max_capacity`, `max_attendees`, `start_date`, `end_date`, `reg_open`, `reg_deadline`, `payment_deadline`, `withdrawal_deadline`, `email_notifications`, `payment_notifications`, `purse`, `purse_3_year_avg`, `skins_pot1`, `skins_pot2`, `draw_type`, `scoring_type`, `par`, `rounds`, `cta_id`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `facility_id`=?, `max_capacity`=?, `max_attendees`=?, `start_date`=?, `end_date`=?, `reg_open`=?, `reg_deadline`=?, `payment_deadline`=?, `withdrawal_deadline`=?, `email_notifications`=?, `payment_notifications`=?, `purse`=?, `purse_3_year_avg`=?, `skins_pot1`=?, `skins_pot2`=?, `draw_type`=?, `scoring_type`=?, `par`=?, `rounds`=?, `cta_id`=?, `last_updated`=?", $params);
					$occurrence_id = (!is_null($occurrence_id) ? $occurrence_id : $db->insert_id());

					//Event Location
					if(EVENT_TYPE != 2){
						$location_id = (isset($records_arr[ITEM_ID]['location_id']) ? $records_arr[ITEM_ID]['location_id'] : NULL);
						$params = array(
							$location_id,
							$reg_settings['reg_system_id'],
							$occurrence_id,
							$_POST['location_name'],
							clean_url($_POST['location_name']),
							$_POST['address'],
							$_POST['address2'],
							$_POST['city'],
							$_POST['province'],
							$_POST['postal_code'],
							$_POST['country'],
							(isset($_POST['google_map']) ? 1 : 0),
							$_POST['gpslat'],
							$_POST['gpslong'],
							$_POST['zoom'],
							date('Y-m-d H:i:s'),
							date('Y-m-d H:i:s'),

							$_POST['location_name'],
							clean_url($_POST['location_name']),
							$_POST['address'],
							$_POST['address2'],
							$_POST['city'],
							$_POST['province'],
							$_POST['postal_code'],
							$_POST['country'],
							(isset($_POST['google_map']) ? 1 : 0),
							$_POST['gpslat'],
							$_POST['gpslong'],
							$_POST['zoom'],
							date('Y-m-d H:i:s')
						);
						$query = $db->query("INSERT INTO `reg_locations`(`location_id`, `reg_system_id`, `occurrence_id`, `name`, `page`, `address`, `address2`, `city`, `province`, `postal_code`, `country`, `google_map`, `gpslat`, `gpslong`, `zoom`, `date_added`, `last_updated`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `name`=?, `page`=?, `address`=?, `address2`=?, `city`=?, `province`=?, `postal_code`=?, `country`=?, `google_map`=?, `gpslat`=?, `gpslong`=?, `zoom`=?, `last_updated`=?", $params);
					}

					//Tournament Eligibility
					if(EVENT_TYPE == 2){

						//Categories
						$eligibility = (!empty($_POST['eligibility']) && $_POST['eligibility'] != 'Non-Members' ? explode(', ', $_POST['eligibility']) : NULL);
						$delete = $db->query("DELETE FROM `reg_event_eligibility` WHERE `$record_id` = ?".(!empty($eligibility) ? " AND `category_id` NOT IN (".implode(",", $eligibility).")" : ""), array($item_id));
						if(!empty($eligibility)) {
							foreach($eligibility as $category_id) {
								$categoryquery = $db->query("INSERT INTO `reg_event_eligibility`(`$record_id`, `category_id`) VALUES (?,?) ON DUPLICATE KEY UPDATE `category_id` = ?", array($item_id, $category_id, $category_id));
							}
						}

						//Classification
						$membership_classes = ($_POST['membership_classes'] ?? []);
						$delete = $db->query("DELETE FROM `reg_event_membership_classes` WHERE `$record_id` = ?".(!empty($membership_classes) ? " AND `class_id` NOT IN (".implode(",", $membership_classes).")" : ""), array($item_id));
						if(!empty($membership_classes)) {
							foreach($membership_classes as $class_id) {
								$classquery = $db->query("INSERT INTO `reg_event_membership_classes`(`$record_id`, `class_id`) VALUES (?,?) ON DUPLICATE KEY UPDATE `class_id` = ?", array($item_id, $class_id, $class_id));
							}
						}
					}

					//Sponsors
					$assigned_sponsors = (isset($_POST['assigned_sponsors']) ? $_POST['assigned_sponsors'] : array());
					$delete = $db->query("DELETE FROM `reg_occurrence_sponsors` WHERE `occurrence_id` = ?", array($occurrence_id));
					if(!empty($assigned_sponsors)) {
						foreach($sponsor_types as $stype){
							foreach($assigned_sponsors[$stype] as $sponsor_id) {
								$sponsorsquery = $db->query("INSERT INTO `reg_occurrence_sponsors`(`occurrence_id`, `sponsor_id`, `type`) VALUES (?,?,?) ON DUPLICATE KEY UPDATE `sponsor_id` = ?", array($occurrence_id, $sponsor_id, $stype, $sponsor_id));
							}
						}
					}

				}

				if(!$db->error() && !$errors) {
					$db->commit();

					//If tournament field was increased, auto-register from waitlist
					if(EVENT_TYPE == 2){
						if($_POST['max_capacity'] > $records_arr[ITEM_ID]['max_capacity']){
							try{
								$autoreg = $Registration->wait_list_autoregister($occurrence_id);
								if($autoreg > 0){
									$CMSBuilder->set_system_alert('Field size has been increased and ' .$autoreg. ' attendees were auto-registered from the waiting list.', true);
								}
							}catch(Exception $e){
								$CMSBuilder->set_system_alert('Unable to auto-register attendees to meet increased field size. '.$e->getMessage(), false);
							}

						}
					}

					// //
					// // Check for crop requirement BEFORE other redirects
					// if ($crop_required) {
					// 	// Store redirect URL for after cropping
					// 	$_SESSION['crop_redirect'] = PAGE_URL."?action=edit&item_id=".$item_id."&step=2";

					// 	// Redirect to crop page
					// 	header("Location: " . PAGE_URL . "?action=edit&item_id=" . $item_id . "&step=1");
					// 	exit();
					// }
					// //

					$redirect = PAGE_URL."?action=edit&item_id=".$item_id."&step=2";
					if(count($cropimages) == 0){
						$CMSBuilder->set_system_alert($record_name.' was successfully saved.', true);
						header("Location: ".$redirect);
						exit();
					}

				}else{
					$errors[] = 'Unable to save '.$record_name.'. '.$db->error();
				}
			}


		//Pricing
		}else if(STEP == 2){

			$_POST['pricing'] = array();
			if(isset($_POST['price_id']) && !empty($_POST['price_id'])){
				$db->new_transaction();

				//Event pricing settings
				$params = array(
					(isset($_POST['gl_id']) && !empty($_POST['gl_id']) ? $_POST['gl_id'] : NULL),
					(isset($_POST['gl_id_fees']) && !empty($_POST['gl_id_fees']) ? $_POST['gl_id_fees'] : NULL),
					(isset($_POST['admin_fee']) && trim($_POST['admin_fee']) != '' ? number_format($_POST['admin_fee'], 2, '.', '') : NULL),
					(isset($_POST['admin_fee_type']) ? $_POST['admin_fee_type'] : 'Percent'),
					(isset($_POST['tournament_fee']) && trim($_POST['tournament_fee']) != '' ? number_format($_POST['tournament_fee'], 2, '.', '') : NULL),
					ITEM_ID
				);
				$query = $db->query("UPDATE `$record_db` SET `gl_id` = ?, `gl_id_fees` = ?, `admin_fee` = ?, `admin_fee_type` = ?, `tournament_fee` = ? WHERE `$record_id` = ?", $params);

				foreach($_POST['price_id'] as $pcount => $price_id) {

					$_POST['pricing'][$price_id] = array('price_type'=>$_POST['price_types'][$pcount], 'price'=>$_POST['prices'][$pcount]);

					if(isset($_POST['price_types'][$pcount]) && $_POST['price_types'][$pcount] != '' && isset($_POST['prices'][$pcount]) && $_POST['prices'][$pcount] != ''){

						$params = array($price_id, ITEM_ID, $_POST['price_types'][$pcount], $_POST['prices'][$pcount], $_POST['price_types'][$pcount], $_POST['prices'][$pcount]);
						$pricingquery = $db->query("INSERT INTO `reg_event_pricing` (`pricing_id`, `event_id`, `price_type`, `price`) VALUES (?,?,?,?) ON DUPLICATE KEY UPDATE `price_type`=?, `price`=?", $params);
					}else{
						if($price_id != '') {
							$delete = $db->query("DELETE FROM `reg_event_pricing` WHERE `pricing_id`=?", array($price_id));
						}
					}
				}

				if(!$db->error()) {
					$db->commit();

					$CMSBuilder->set_system_alert('Pricing was successfully saved.', true);
					header("Location: " .PAGE_URL."?action=edit&item_id=".ITEM_ID."&step=3");
					exit();
				}else{
					$errors[] = 'Unable to save pricing: '.$db->error();
				}
			}else{
				$CMSBuilder->set_system_alert('Please enter at least one price type.', false);
			}

		//Waivers & Forms
		}else if(STEP == 3){

			$db->new_transaction();

			//Save waivers
			$assigned_waivers = (isset($_POST['assigned_waivers']) && !empty($_POST['assigned_waivers']) ? $_POST['assigned_waivers'] : array());
			$delete_waivers = $db->query("DELETE FROM `reg_event_waivers` WHERE `".$record_id."` = ?", array(ITEM_ID));
			if(!empty($assigned_waivers)){
				foreach($assigned_waivers as $waiver_id){
					$waiversquery = $db->query("INSERT INTO `reg_event_waivers` (`$record_id`, `waiver_id`) VALUES (?,?) ON DUPLICATE KEY UPDATE `waiver_id` = ?", array(ITEM_ID, $waiver_id, $waiver_id));
				}
			}

			//Save forms
			$assigned_forms = (isset($_POST['assigned_forms']) && !empty($_POST['assigned_forms']) ? $_POST['assigned_forms'] : array());
			$delete_forms = $db->query("DELETE FROM `reg_event_forms` WHERE `".$record_id."` = ?", array(ITEM_ID));
			if(!empty($assigned_forms)){
				foreach($assigned_forms as $form_id){
					$formsquery = $db->query("INSERT INTO `reg_event_forms` (`$record_id`, `form_id`) VALUES (?,?) ON DUPLICATE KEY UPDATE `form_id` = ?", array(ITEM_ID, $form_id, $form_id));
				}
			}

			if(!$db->error()){
				$db->commit();
				$CMSBuilder->set_system_alert($record_name.' waivers were successfully saved.', true);
				header("Location: " .PAGE_URL."?action=edit&item_id=".ITEM_ID."&step=4");
				exit();

			}else{
				$errors[] = 'Unable to save waivers: '.$db->error();
			}

		//Attendee Info
		}else if(STEP == 4){

			//Event form fields
			if(EVENT_TYPE != 2){

				//$_POST['attendee_fields']['first_name'] = ($_POST['f_name_show_hide'] > 0 ? $_POST['f_name_req'] : ($_POST['f_name_req']*-1));
				//$_POST['attendee_fields']['last_name'] = ($_POST['l_name_show_hide'] > 0 ? $_POST['l_name_req'] : ($_POST['l_name_req']*-1));
				$_POST['attendee_fields']['first_name'] = 1;
				$_POST['attendee_fields']['last_name'] = 1;
				$_POST['attendee_fields']['email'] = ($_POST['email_show_hide'] > 0 ? $_POST['email_req'] : ($_POST['email_req']*-1));
				$_POST['attendee_fields']['phone'] = ($_POST['phone_show_hide'] > 0 ? $_POST['phone_req'] : ($_POST['phone_req']*-1));
				$_POST['attendee_fields']['company'] = ($_POST['company_show_hide'] > 0 ? $_POST['company_req'] : ($_POST['company_req']*-1));
				$_POST['attendee_fields']['facility'] = ($_POST['facility_show_hide'] > 0 ? $_POST['facility_req'] : ($_POST['facility_req']*-1));
				$_POST['attendee_fields']['position'] = ($_POST['position_show_hide'] > 0 ? $_POST['position_req'] : ($_POST['position_req']*-1));
				$_POST['attendee_fields']['comments'] = ($_POST['comments_show_hide'] > 0 ? $_POST['comments_req'] : ($_POST['comments_req']*-1));

				$params = array(
					ITEM_ID,
					$_POST['attendee_fields']['first_name'],
					$_POST['attendee_fields']['last_name'],
					$_POST['attendee_fields']['email'],
					$_POST['attendee_fields']['phone'],
					$_POST['attendee_fields']['company'],
					$_POST['attendee_fields']['facility'],
					$_POST['attendee_fields']['position'],
					$_POST['attendee_fields']['comments'],

					$_POST['attendee_fields']['first_name'],
					$_POST['attendee_fields']['last_name'],
					$_POST['attendee_fields']['email'],
					$_POST['attendee_fields']['phone'],
					$_POST['attendee_fields']['company'],
					$_POST['attendee_fields']['facility'],
					$_POST['attendee_fields']['position'],
					$_POST['attendee_fields']['comments']
				);
				$fieldsquery = $db->query("INSERT INTO `reg_event_attendee_information` (`event_id`, `first_name`, `last_name`, `email`, `phone`, `company`, `facility`, `position`, `comments`) VALUES (?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `first_name`=?, `last_name`=?, `email`=?, `phone`=?, `company`=?, `facility`=?, `position`=?, `comments`=?", $params);

				if($fieldsquery && !$db->error()) {
					$CMSBuilder->set_system_alert($record_name.' fields were successfully saved.', true);
					header("Location: " .PAGE_URL."?action=edit&item_id=".ITEM_ID."&step=5");
					exit();
				} else {
					$errors[] = 'Unable to save fields: '.$db->error();
				}

			//Tournament entries
			}else if(EVENT_TYPE == 2){

				if(isset($_POST['entries']) && !empty($_POST['entries'])){

					$db->new_transaction();

					foreach($_POST['entries'] as $attendee_id=>$data){

						//Set post data
						foreach($data as $col=>$value){
							if($value == ""){
								$data[$col] = NULL;
							}
							$row['attendees'][$attendee_id][$col] = $value;
						}

						//Final result
						$row['attendees'][$attendee_id]['final_score'] = (($data['r1_score'] != "" ? $data['r1_score'] : 0)+($data['r2_score'] != "" ? $data['r2_score'] : 0));
						$row['attendees'][$attendee_id]['team_gross'] = (($data['r1_team_gross'] != "" ? $data['r1_team_gross'] : 0)+($data['r2_team_gross'] != "" ? $data['r2_team_gross'] : 0));
						$row['attendees'][$attendee_id]['team_net'] = (($data['r1_team_net'] != "" ? $data['r1_team_net'] : 0)+($data['r2_team_net'] != "" ? $data['r2_team_net'] : 0));
						$row['attendees'][$attendee_id]['team_final_score'] = $row['attendees'][$attendee_id]['team_gross'].'/'.$row['attendees'][$attendee_id]['team_net'];

						//Target score
						if(isset($data['target_score'])){
							$data['target_score'] = 'Class A';
						}
						if(isset($data['target_score_cfm'])){
							$data['target_score'] = 'CFM';
						}

						//Save field data
						$params = array(
							$attendee_id,
							(isset($data['r1_hole']) ? $data['r1_hole'] : NULL),
							(isset($data['r1_time']) ? $data['r1_time'] : NULL),
							(isset($data['r1_score']) ? $data['r1_score'] : NULL),
							(isset($data['r1_team_gross']) ? $data['r1_team_gross'] : NULL),
							(isset($data['r1_team_net']) ? $data['r1_team_net'] : NULL),
							(isset($data['r1_overunder']) ? $data['r1_overunder'] : NULL),
							(isset($data['r1_skins']) ? $data['r1_skins'] : NULL),
							(isset($data['r2_hole']) ? $data['r2_hole'] : NULL),
							(isset($data['r2_time']) ? $data['r2_time'] : NULL),
							(isset($data['r2_score']) ? $data['r2_score'] : NULL),
							(isset($data['r2_team_gross']) ? $data['r2_team_gross'] : NULL),
							(isset($data['r2_team_net']) ? $data['r2_team_net'] : NULL),
							(isset($data['r2_overunder']) ? $data['r2_overunder'] : NULL),
							(isset($data['r2_skins']) ? $data['r2_skins'] : NULL),
							(isset($data['points']) ? $data['points'] : NULL),
							(isset($data['prize']) ? $data['prize'] : NULL),
							(isset($data['gc_prize']) ? $data['gc_prize'] : NULL),
							(isset($data['team_prize']) ? $data['team_prize'] : NULL),
							(isset($data['team_gc_prize']) ? $data['team_gc_prize'] : NULL),
							(isset($data['flight']) ? $data['flight'] : NULL),
							(isset($data['champion']) ? $data['champion'] : NULL),
							(isset($data['playoff']) ? $data['playoff'] : NULL),
							(isset($data['target_score']) ? $data['target_score'] : NULL),
							(isset($data['gender']) ? $data['gender'] : NULL),
							USER_LOGGED_IN,
							date('Y-m-d H:i:s'),
							(isset($data['r1_hole']) ? $data['r1_hole'] : NULL),
							(isset($data['r1_time']) ? $data['r1_time'] : NULL),
							(isset($data['r1_score']) ? $data['r1_score'] : NULL),
							(isset($data['r1_team_gross']) ? $data['r1_team_gross'] : NULL),
							(isset($data['r1_team_net']) ? $data['r1_team_net'] : NULL),
							(isset($data['r1_overunder']) ? $data['r1_overunder'] : NULL),
							(isset($data['r1_skins']) ? $data['r1_skins'] : NULL),
							(isset($data['r2_hole']) ? $data['r2_hole'] : NULL),
							(isset($data['r2_time']) ? $data['r2_time'] : NULL),
							(isset($data['r2_score']) ? $data['r2_score'] : NULL),
							(isset($data['r2_team_gross']) ? $data['r2_team_gross'] : NULL),
							(isset($data['r2_team_net']) ? $data['r2_team_net'] : NULL),
							(isset($data['r2_overunder']) ? $data['r2_overunder'] : NULL),
							(isset($data['r2_skins']) ? $data['r2_skins'] : NULL),
							(isset($data['points']) ? $data['points'] : NULL),
							(isset($data['prize']) ? $data['prize'] : NULL),
							(isset($data['gc_prize']) ? $data['gc_prize'] : NULL),
							(isset($data['team_prize']) ? $data['team_prize'] : NULL),
							(isset($data['team_gc_prize']) ? $data['team_gc_prize'] : NULL),
							(isset($data['flight']) ? $data['flight'] : NULL),
							(isset($data['champion']) ? $data['champion'] : NULL),
							(isset($data['playoff']) ? $data['playoff'] : NULL),
							(isset($data['target_score']) ? $data['target_score'] : NULL),
							(isset($data['gender']) ? $data['gender'] : NULL),
							USER_LOGGED_IN,
							date('Y-m-d H:i:s')
						);
						$query = $db->query("INSERT INTO `reg_tournament_field`(`attendee_id`, `r1_hole`, `r1_time`, `r1_score`, `r1_team_gross`, `r1_team_net`, `r1_overunder`, `r1_skins`, `r2_hole`, `r2_time`, `r2_score`, `r2_team_gross`, `r2_team_net`, `r2_overunder`, `r2_skins`, `points`, `prize`, `gc_prize`, `team_prize`, `team_gc_prize`, `flight`, `champion`, `playoff`, `target_score`, `gender`, `updated_by`, `last_updated`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `r1_hole`=?, `r1_time`=?, `r1_score`=?, `r1_team_gross`=?, `r1_team_net`=?, `r1_overunder`=?, `r1_skins`=?, `r2_hole`=?, `r2_time`=?, `r2_score`=?, `r2_team_gross`=?, `r2_team_net`=?, `r2_overunder`=?, `r2_skins`=?, `points`=?, `prize`=?, `gc_prize`=?, `team_prize`=?, `team_gc_prize`=?, `flight`=?, `champion`=?, `playoff`=?, `target_score`=?, `gender`=?, `updated_by`=?, `last_updated`=?", $params);

						//Save handicaps
						if(isset($_POST['handicap_'.$attendee_id])){
							$handicap = (!empty($_POST['handicap_'.$attendee_id]) ? $_POST['handicap_'.$attendee_id] : NULL);
							$update = $db->query("UPDATE `reg_attendees` SET `handicap` = ? WHERE `attendee_id` = ?", array($handicap, $attendee_id));
						}
						if(!empty($row['attendees'][$attendee_id]['partner'])){
							$partner_attendee_id = $row['attendees'][$attendee_id]['partner']['attendee_id'];
							if(isset($_POST['handicap_'.$partner_attendee_id])){
								$handicap = (!empty($_POST['handicap_'.$partner_attendee_id]) ? $_POST['handicap_'.$partner_attendee_id] : NULL);
								$update = $db->query("UPDATE `reg_attendees` SET `handicap` = ? WHERE `attendee_id` = ?", array($handicap, $partner_attendee_id));
							}
						}

					}

					if(!$db->error()){
						$db->commit();

						$CMSBuilder->set_system_alert(EVENT_CODE.' field was successfully saved.', true);
						header("Location: " .PAGE_URL."?action=edit&item_id=".ITEM_ID."&step=5");
						exit();

					}else{
						$CMSBuilder->set_system_alert('Unable to update field: '.$db->error(), false);
					}

				}else{
					$CMSBuilder->set_system_alert(EVENT_CODE.' was successfully saved.', true);
					header("Location: " .PAGE_URL."?action=edit&item_id=".ITEM_ID."&step=5");
					exit();
				}

			}

		//Final step
		}else if(STEP == 5){

			//Event extras
			if(EVENT_TYPE != 2){

				$db->new_transaction();

				$_POST['addons'] = array();
				if(isset($_POST['addon_id']) && !empty($_POST['addon_id'])){
					foreach($_POST['addon_id'] as $acount => $addon_id){
						$has_options = false;

						//Store addons in array in case there are errors
						$_POST['addons'][$acount] = array(
							'addon_id' => $_POST['addon_id'][$acount],
							'name' => $_POST['addon_name'][$acount],
							'required' => $_POST['addon_required'][$acount],
							'options' => array()
						);

						//Store options
						if(isset($_POST['option_id'][$acount]) && !empty($_POST['option_id'][$acount])){
							foreach($_POST['option_id'][$acount] as $ocount => $option_id) {
								$_POST['addons'][$acount]['options'][$ocount] = array(
									'option_id' => $_POST['option_id'][$acount][$ocount],
									'name' => $_POST['option_name'][$acount][$ocount],
									'price_adjustment' => str_replace("$", "", $_POST['option_price'][$acount][$ocount])
								);
								if(trim($_POST['option_name'][$acount][$ocount]) != ''){
									$has_options = true;
								}
							}
						}

						//Addon has a name and has options, save
						if(trim($_POST['addon_name'][$acount]) != '' && $has_options){

							//Save addon
							$params = array(
								$_POST['addon_id'][$acount],
								$records_arr[ITEM_ID]['occurrence_id'],
								$_POST['addon_name'][$acount],
								$_POST['addon_required'][$acount],
								$_POST['addon_name'][$acount],
								$_POST['addon_required'][$acount]
							);
							$addonsqquery = $db->query("INSERT INTO `reg_occurrence_addons` (`addon_id`, `occurrence_id`, `name`, `required`) VALUES (?,?,?,?) ON DUPLICATE KEY UPDATE `name`=?, `required`=?", $params);
							$new_addon_id = ($addon_id != '' ? $addon_id : $db->insert_id());

							//Save options
							if(isset($_POST['option_id'][$acount]) && !empty($_POST['option_id'][$acount])){
								foreach($_POST['option_id'][$acount] as $ocount => $option_id){

									//Option has name, save
									if(trim($_POST['option_name'][$acount][$ocount]) != ''){
										$params = array(
											$_POST['option_id'][$acount][$ocount],
											$new_addon_id,
											$_POST['option_name'][$acount][$ocount],
											str_replace("$", "", $_POST['option_price'][$acount][$ocount]),
											$_POST['option_name'][$acount][$ocount],
											str_replace("$", "", $_POST['option_price'][$acount][$ocount])
										);
										$optionsquery = $db->query("INSERT INTO `reg_occurrence_options` (`option_id`, `addon_id`, `name`, `price_adjustment`) VALUES (?,?,?,?) ON DUPLICATE KEY UPDATE `name`=?, `price_adjustment`=?", $params);

									//Option has no name, delete
									}else{
										if($option_id != ''){
											$delete = $db->query("DELETE FROM `reg_occurrence_options` WHERE `option_id` = ?", array($option_id));
										}
									}
								}
							}

						//Addon has no name and options, delete
						}else{
							if($addon_id != ''){
								$delete = $db->query("DELETE `reg_occurrence_addons`, `reg_occurrence_options` FROM `reg_occurrence_addons` LEFT JOIN `reg_occurrence_options` ON `reg_occurrence_addons`.`addon_id` = `reg_occurrence_options`.`addon_id` WHERE `reg_occurrence_addons`.`addon_id` = ?", array($addon_id));
							}
						}
					}
				}

				if(!$db->error()){
					$db->commit();
					$CMSBuilder->set_system_alert(EVENT_CODE.' was successfully saved.', true);
					header('Location: '.PAGE_URL);
					exit();
				}else{
					$errors[] = 'Unable to save extras. '.$db->error();
				}


			//Tournament reports
			}else if(EVENT_TYPE == 2){

				$occurrence_id = (isset($records_arr[ITEM_ID]['occurrence_id']) ? $records_arr[ITEM_ID]['occurrence_id'] : NULL);
				$params = array(
					(isset($_POST['report_draw']) ? 1 : 0),
					(isset($_POST['report_drawalpha']) ? 1 : 0),
					(isset($_POST['report_drawscore']) ? 1 : 0),
					(isset($_POST['report_results']) ? 1 : 0),
					(isset($_POST['report_money']) ? 1 : 0),
					date("Y-m-d H:i:s"),
					$occurrence_id
				);
				$query = $db->query("UPDATE `reg_occurrences` SET `report_draw`=?, `report_drawalpha`=?, `report_drawscore`=?, `report_results`=?, `report_money`=?, `last_updated`=? WHERE `occurrence_id`=?", $params);
				if($query && !$db->error()){
					$CMSBuilder->set_system_alert(EVENT_CODE.' was successfully saved.', true);
					header('Location: '.PAGE_URL);
					exit();
				}else{
					$errors[] = 'Unable to save reports. '.$db->error();
				}
			}

		}

		if($errors && is_array($errors)){
			$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			foreach($_POST AS $key=>$data){
				$row[$key] = $data;
			}
		}else{
			// sitemapXML();
			sitemap_XML();
		}

	//Duplicate
	}else if(isset($_POST['duplicate'])){
		if(EVENT_TYPE != 2){ //IMPORTANT: Do not allow duplication of Tournaments
			//Get addons
			$records_arr[ITEM_ID]['addons'] = array();
			$query = $db->query("SELECT * FROM `reg_occurrence_addons` WHERE `occurrence_id` = ? ORDER BY `addon_id`", array($records_arr[ITEM_ID]['occurrence_id']));
			if($query && !$db->error() && $db->num_rows() > 0){
				$result = $db->fetch_array();
				foreach($result as $addon) {
					$addon['options'] = array();

					//Get options
					$query2 = $db->query("SELECT * FROM `reg_occurrence_options` WHERE `addon_id` = ? ORDER BY `option_id` ASC", array($addon['addon_id']));
					if($query2 && !$db->error() && $db->num_rows() > 0){
						$result2 = $db->fetch_array();
						foreach($result2 as $option) {
							$addon['options'][$option['option_id']] = $option;
						}
					}

					$records_arr[ITEM_ID]['addons'][$addon['addon_id']] = $addon;
				}
			}

			//Define initial variables
			$original_event = $duplicate_event = $records_arr[ITEM_ID];
			$uploads_arr = array();

			//Count number of duplicates to rename duplicate (avoid same name/page/slug's)
			$unique_url = $original_event['page'];
			$unique_url_base = preg_replace('/(-copy(\-[0-9]*)?)$/', '', $unique_url); //get slug without "-copy-N"
			$name_base = preg_replace('/( \- Copy( \([0-9]+\))?){0,1}$/i', '', $original_event['name']); //get name without "- Copy (N)"

			$duplicate_count = 0;
			foreach($records_arr as $check_event){
				if(
					$check_event['page'] == $unique_url
					|| preg_match('/^('.$unique_url_base.')(\-copy(\-[0-9]*)?){0,1}$/', $check_event['page'])
				){
					$duplicate_count++;
				}
			}

			//Re-format properties
			if($duplicate_count > 0){
				$duplicate_event['name'] = $name_base.' - Copy'.($duplicate_count > 1 ? ' ('.$duplicate_count.')' : '');
			}
			$duplicate_event['occurrence_id'] = NULL; //To be overwritten below
			$duplicate_event['page'] = clean_url($duplicate_event['name']);
			$duplicate_event['showhide'] = 1;
			$duplicate_event['date_added'] = date('Y-m-d H:i:s');
			$duplicate_event['last_updated'] = date('Y-m-d H:i:s');

			//Queue image(s) to upload
			$duplicate_event['banner_image'] = NULL;
			if($original_event['banner_image'] != ''){
				$ext = pathinfo($imagedir.$imagesizes[0]['dir'].$original_event['banner_image'], PATHINFO_EXTENSION);
				$duplicate_event['banner_image'] = $duplicate_event['page'].'-'.date("ymdhis").'.'.$ext;

				$dirs = array($imagedir);
				foreach($imagesizes as $size){
					$dirs[] = $imagedir.$size['dir'];
				}
				$uploads_arr[] = array(
					'old_image' => $original_event['banner_image'],
					'new_image' => $duplicate_event['banner_image'],
					'dirs' => $dirs
				);
			}

			//Start new transaction
			$db->new_transaction();

			//Copy event
			$insert_columns = $db->get_db_columns('reg_events', ['event_id']);
			$params = array();
			foreach($insert_columns as $column){
				$params[] = ($duplicate_event[$column] ?? NULL);
			}
			$values = implode(',', array_fill_keys($insert_columns, '?'));
			$db->query("INSERT INTO `reg_events` (`".implode("` , `", $insert_columns)."`) VALUES (".$values.")", $params);
			$duplicate_event['event_id'] = $db->insert_id();

			//Copy event categories
			$insert_columns = $db->get_db_columns('reg_event_categories', ['uid', 'event_id']);
			$params = array($duplicate_event['event_id'], $original_event['event_id']);
			$db->query(
				"INSERT INTO `reg_event_categories`"
				." (`event_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `reg_event_categories`"
				." WHERE `event_id` = ?"
			, $params);

			//Copy event pricing
			$insert_columns = $db->get_db_columns('reg_event_pricing', ['pricing_id', 'event_id']);
			$params = array($duplicate_event['event_id'], $original_event['event_id']);
			$db->query(
				"INSERT INTO `reg_event_pricing`"
				." (`event_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `reg_event_pricing`"
				." WHERE `event_id` = ?"
			, $params);

			//Copy attendee fields
			$insert_columns = $db->get_db_columns('reg_event_attendee_information', ['information_id', 'event_id']);
			$params = array($duplicate_event['event_id'], $original_event['event_id']);
			$db->query(
				"INSERT INTO `reg_event_attendee_information`"
				." (`event_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `reg_event_attendee_information`"
				." WHERE `event_id` = ?"
			, $params);

			//Copy occurrence
			$insert_columns = $db->get_db_columns('reg_occurrences', ['occurrence_id', 'event_id']);
			$params = array($duplicate_event['event_id'], $original_event['event_id']);
			$db->query(
				"INSERT INTO `reg_occurrences`"
				." (`event_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `reg_occurrences`"
				." WHERE `event_id` = ?"
			, $params);
			$duplicate_event['occurrence_id'] = $db->insert_id(); //IMPORTANT: Each event only has one occurrence; so this expects only 1 occurrence to be duplicated

			//Copy location (IMPORTANT: Each event only has one occurrence; so the code below assumes there is only one occurrence)
			$insert_columns = $db->get_db_columns('reg_locations', ['location_id', 'occurrence_id']);
			$params = array($duplicate_event['occurrence_id'], $original_event['occurrence_id']);
			$db->query(
				"INSERT INTO `reg_locations`"
				." (`occurrence_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `reg_locations`"
				." WHERE `occurrence_id` = ?"
			, $params);

			//Copy sponsors (IMPORTANT: Each event only has one occurrence; so the code below assumes there is only one occurrence)
			$insert_columns = $db->get_db_columns('reg_occurrence_sponsors', ['uid', 'occurrence_id']);
			$params = array($duplicate_event['occurrence_id'], $original_event['occurrence_id']);
			$db->query(
				"INSERT INTO `reg_occurrence_sponsors`"
				." (`occurrence_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `reg_occurrence_sponsors`"
				." WHERE `occurrence_id` = ?"
			, $params);

			//Copy waivers
			$insert_columns = $db->get_db_columns('reg_event_waivers', ['uid', 'event_id']);
			$params = array($duplicate_event['event_id'], $original_event['event_id']);
			$db->query(
				"INSERT INTO `reg_event_waivers`"
				." (`event_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `reg_event_waivers`"
				." WHERE `event_id` = ?"
			, $params);

			//Copy forms
			$insert_columns = $db->get_db_columns('reg_event_forms', ['uid', 'event_id']);
			$params = array($duplicate_event['event_id'], $original_event['event_id']);
			$db->query(
				"INSERT INTO `reg_event_forms`"
				." (`event_id`, `".implode("` , `", $insert_columns)."`)"
				." SELECT ?, `".implode("` , `", $insert_columns)."`"
				." FROM `reg_event_forms`"
				." WHERE `event_id` = ?"
			, $params);

			//Copy addons (IMPORTANT: Each event only has one occurrence; so the code below assumes there is only one occurrence)
			foreach($original_event['addons'] as $this_addon){
				$params = array(
					$duplicate_event['occurrence_id'],
					$this_addon['name'],
					$this_addon['required']
				);
				$db->query("INSERT INTO `reg_occurrence_addons` (`occurrence_id`, `name`, `required`) VALUES (?,?,?)", $params);
				$new_addon_id = $db->insert_id();

				//Copy addon options
				foreach($this_addon['options'] as $this_option){
					$params = array(
						$new_addon_id,
						$this_option['name'],
						$this_option['price_adjustment']
					);
					$db->query("INSERT INTO `reg_occurrence_options` (`addon_id`, `name`, `price_adjustment`) VALUES (?,?,?)", $params);
				}
			}

			//No errors
			if(!$db->error()){
				$db->commit();

				//Copy images
				foreach($uploads_arr as $upload){
					foreach($upload['dirs'] as $dir){
						if(file_exists($dir.$upload['old_image'])){
							copy($dir.$upload['old_image'], $dir.$upload['new_image']);
						}
					}
				}

				$CMSBuilder->set_system_alert('Event was successfully duplicated.', true);
				header('Location:'.PAGE_URL.'?action=edit&item_id='.$duplicate_event['event_id'].'&step=1');
				exit();

			}else{
				$db->rollback();
				$CMSBuilder->set_system_alert('Unable to duplicate event. '.$db->error(), false);
			}
		}
	//Import file
	}else if(isset($_POST['import'])){

		//Tournaments only
		if(EVENT_TYPE == 2 && STEP == 4){

			$filedir = "../docs/temp/";
			$filetypes = array('xml');
			$import_errors = array();

			//Validate
			if(!empty($_FILES['import_file']['size']) && $_FILES['import_file']['size'] > 20480000){
				$errors[] = 'File size is too large.';
			}
			if(!empty($_FILES['import_file']['name'])){
				$ext = strtolower(pathinfo($_FILES['import_file']['name'], PATHINFO_EXTENSION));
				if(!in_array($ext, $filetypes)){
					$errors[] = 'File type `' .$ext. '` is restricted. Allowed file types include '.implode(', ', $filetypes).'.';
				}
			}
			if(!$errors){

				//Upload to temp
				$filename = 'import-'.date('ymdhis').'.xml';
				// $fileUpload = new FileUpload();
				// $fileUpload->load($_FILES['import_file']['tmp_name']);
				// $fileUpload->save($filedir, $filename);

				// Attempt to copy the temporary file to its final destination
				if (!@copy($_FILES['import_file']['tmp_name'], $filedir . $filename)) {
					// If the copy fails, add an error to be displayed to the user
					$errors[] = "Failed to upload the file. Please check permissions or contact an administrator.";

				} else {
					if(file_exists($filedir.$filename)){
						//Read file
						$fp = fopen($filedir.$filename, "r+");
						$xmlstring = fread($fp, filesize($filedir.$filename));
						fclose($fp);

						//Parse file
						$xml = simplexml_load_string($xmlstring);
						$json = json_encode($xml);
						$array = json_decode($json, true);

						//Delete from temp
						unlink($filedir.$filename);

						//Default arrays
						$field_data = array();
						$results_data = array();

						//Field data (Alpha Listing XML)
						if(isset($array['Group']['Details'])){
							if(!empty($array['Group']['Details'])){
								foreach($array['Group']['Details'] as $details){
									if(isset($details['Section']['Field'])){
										$data_arr = array();
										foreach($details['Section']['Field'] as $field){
											if(!is_array($field['Value'])){
												$data_arr[$field['@attributes']['Name']] = str_replace("'", "&rsquo;", $field['Value']);
											}
										}
									}
									$field_data[$data_arr['Player']] = $data_arr;

									//Entry not found
									if(!array_key_exists($data_arr['Player'], $row['attendee_names'])){
										$import_errors[] = '<strong class="required"><i class="fa fa-exclamation-triangle"></i> No player found matching `' .$data_arr['Player']. '`</strong>';
									}
								}

							}else{
								$errors[] = 'No field data found.';
							}

						//Results data (Results XML)
						}else if(isset($array['Group']['Group']) || isset($array['Group'][0])){

							//Format group node
							$Groups = array();
							if(isset($array['Group']['Group']['Details'])){
								$Groups[0] = $array['Group'];
							}else if(isset($array['Group'][0])){
								$Groups = $array['Group'];
							}
							foreach($Groups as $Group){

								//Determine score type for each group
								$score_type = 'Individual Gross';
								if(isset($Group['GroupHeader']['Section'][0]['Field'])){
									foreach($Group['GroupHeader']['Section'][0]['Field'] as $HeaderField){
										if($HeaderField['@attributes']['Name'] == 'Title3'){
											if($HeaderField['Value'] == 'Team Gross' || $HeaderField['Value'] == 'Team Net'){
												$score_type = $HeaderField['Value']; //Set team scoring type, otherwise use default Individual Gross
											}
										}
									}
								}

								//Get row data
								if(isset($Group['Group']['Details'])){
									foreach($Group['Group']['Details'] as $Details){
										$data_arr = array();

										//Format section node
										$Sections = array();
										if(isset($Details['Section']['Field'])){
											$Sections[0] = $Details['Section'];
										}else if(isset($Details['Section'][0])){
											$Sections = $Details['Section'];
										}
										foreach($Sections as $Section){

											//Format field node
											$Fields = array();
											if(isset($Section['Field']['Value'])){
												$Fields[0] = $Section['Field'];
											}else if(isset($Section['Field'][0])){
												$Fields = $Section['Field'];
											}
											foreach($Fields as $Field){

												//Format data fields
												if(!is_array($Field['Value'])){
													if($Field['@attributes']['Name'] == 'Column51'){
														$Field['@attributes']['Name'] = $score_type; //Set scoring type
													}
													if($Field['@attributes']['Name'] == 'Player'){
														$Field['@attributes']['Name'] = 'TeamOrPlayer'; //Use player name instead of team number
														$Field['Value'] = substr($Field['Value'], 0, strpos($Field['Value'], ','));
													}
													$data_arr[$Field['@attributes']['Name']] = str_replace("'", "&rsquo;", $Field['Value']);
												}

											}

										}

										//Push to results array
										if(!array_key_exists($data_arr['TeamOrPlayer'], $results_data)){
											$results_data[$data_arr['TeamOrPlayer']] = $data_arr;

											//Entry not found
											if(!in_array($data_arr['TeamOrPlayer'], $row['attendee_names'])){
												$import_errors[] = '<strong class="required"><i class="fa fa-exclamation-triangle"></i> No player found matching `' .$data_arr['TeamOrPlayer']. '`</strong>';
											}

										}else{
											$results_data[$data_arr['TeamOrPlayer']] = $results_data[$data_arr['TeamOrPlayer']]+$data_arr; //Merge all scoring types
										}

									}
								}

							}

						//Bad markup
						}else{
							$errors[] = 'Unable to load data. Unexpected XML markup.';
						}

					//Upload error
					}else{
						$errors[] = 'Unable to upload file. Please try again.';
					}
				}

			}

			//Error report
			if(!empty($errors)){
				$CMSBuilder->set_system_alert(implode('<br />', $errors), false);
			}


		}


	} else {
		//Handle images
		// if($CMSUploader->crop_queue()){
		// print_r($_POST);
		// print_r($_FILES);
			// include('modules/registration/CropImagesEvents.php');
			include('modules/CropImages.php');
		// }
	}
}

?>